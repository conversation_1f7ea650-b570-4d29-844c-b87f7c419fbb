import { getApiClient } from './api';
import { API_ENDPOINTS } from './api';

// Chat types
export interface ChatMessage {
  _id: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'system';
  sender: {
    _id: string;
    username: string;
    avatar?: string;
  };
  timestamp: string;
  isRead: boolean;
  metadata?: any;
}

export interface ChatConversation {
  _id: string;
  trade: {
    _id: string;
    type: 'buy' | 'sell';
    status: string;
    cryptocurrency: {
      type: string;
      amount: number;
    };
    fiat: {
      currency: string;
      amount: number;
    };
  };
  participants: Array<{
    _id: string;
    username: string;
    avatar?: string;
    isOnline: boolean;
    lastSeen: string;
  }>;
  lastMessage?: ChatMessage;
  unreadCount: number;
  status: 'active' | 'archived' | 'locked';
  createdAt: string;
  updatedAt: string;
}

// Chat Service Class
class ChatService {
  /**
   * Get user's chat conversations
   */
  async getConversations(): Promise<ChatConversation[]> {
    try {
      const response = await getApiClient().get<ChatConversation[]>(
        API_ENDPOINTS.CHAT.LIST
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        // Return mock data if API fails
        return this.getMockConversations();
      }
    } catch (error) {
      console.error('Get conversations error:', error);
      // Return mock data on error
      return this.getMockConversations();
    }
  }

  /**
   * Get messages for a specific conversation
   */
  async getMessages(conversationId: string, limit: number = 50, offset: number = 0): Promise<ChatMessage[]> {
    try {
      const response = await getApiClient().get<ChatMessage[]>(
        API_ENDPOINTS.CHAT.MESSAGES(conversationId),
        { limit, offset }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        // Return mock data if API fails
        return this.getMockMessages();
      }
    } catch (error) {
      console.error('Get messages error:', error);
      // Return mock data on error
      return this.getMockMessages();
    }
  }

  /**
   * Send a message
   */
  async sendMessage(conversationId: string, content: string, type: 'text' | 'image' | 'file' = 'text'): Promise<ChatMessage> {
    try {
      const response = await getApiClient().post<ChatMessage>(
        API_ENDPOINTS.CHAT.SEND_MESSAGE(conversationId),
        { content, type }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to send message');
      }
    } catch (error) {
      console.error('Send message error:', error);
      throw error;
    }
  }

  /**
   * Mark messages as read
   */
  async markAsRead(conversationId: string): Promise<void> {
    try {
      const response = await getApiClient().post(
        API_ENDPOINTS.CHAT.MARK_READ(conversationId)
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to mark as read');
      }
    } catch (error) {
      console.error('Mark as read error:', error);
      // Don't throw error for this operation
    }
  }

  /**
   * Get mock conversations for offline/error scenarios
   */
  private getMockConversations(): ChatConversation[] {
    return [
      {
        _id: 'mock-conv-1',
        trade: {
          _id: 'mock-trade-1',
          type: 'buy',
          status: 'in_progress',
          cryptocurrency: {
            type: 'BTC',
            amount: 0.001
          },
          fiat: {
            currency: 'KES',
            amount: 5000
          }
        },
        participants: [
          {
            _id: 'user-1',
            username: 'Alice',
            isOnline: true,
            lastSeen: new Date().toISOString()
          },
          {
            _id: 'user-2',
            username: 'Bob',
            isOnline: false,
            lastSeen: new Date(Date.now() - 1000 * 60 * 30).toISOString()
          }
        ],
        lastMessage: {
          _id: 'msg-1',
          content: 'Payment sent, please confirm',
          type: 'text',
          sender: {
            _id: 'user-1',
            username: 'Alice'
          },
          timestamp: new Date().toISOString(),
          isRead: false
        },
        unreadCount: 1,
        status: 'active',
        createdAt: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  }

  /**
   * Get mock messages for offline/error scenarios
   */
  private getMockMessages(): ChatMessage[] {
    return [
      {
        _id: 'msg-1',
        content: 'Hi, I\'m interested in your offer',
        type: 'text',
        sender: {
          _id: 'user-1',
          username: 'Alice'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        isRead: true
      },
      {
        _id: 'msg-2',
        content: 'Great! Let\'s proceed with the trade',
        type: 'text',
        sender: {
          _id: 'user-2',
          username: 'Bob'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        isRead: true
      }
    ];
  }
}

// Create and export service instance
export const chatService = new ChatService();

// Export default
export default chatService;
