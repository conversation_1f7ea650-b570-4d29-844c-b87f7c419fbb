/**
 * Comprehensive Input Validation Service
 * Enterprise-grade input validation for financial platform
 */

const Joi = require('joi');
const validator = require('validator');
const { ethers } = require('ethers');
const logger = require('../utils/logger');

class InputValidationService {
  constructor() {
    this.validationSchemas = new Map();
    this.customValidators = new Map();
    this.initializeCustomValidators();
    this.initializeSchemas();
  }

  /**
   * Initialize validation schemas
   */
  initializeSchemas() {
    // Wallet authentication schema
    this.validationSchemas.set('walletAuth', Joi.object({
      walletAddress: Joi.string().custom(this.customValidators.get('ethereumAddress')).required(),
      signature: Joi.string().custom(this.customValidators.get('ethereumSignature')).required(),
      message: Joi.string().min(10).max(500).required(),
      timestamp: Joi.number().integer().min(Date.now() - 300000).max(Date.now() + 60000).required()
    }));

    // User profile schema
    this.validationSchemas.set('userProfile', Joi.object({
      firstName: Joi.string().min(2).max(50).pattern(/^[a-zA-Z\s]+$/).required(),
      lastName: Joi.string().min(2).max(50).pattern(/^[a-zA-Z\s]+$/).required(),
      email: Joi.string().email().max(255).required(),
      phone: Joi.string().custom(this.customValidators.get('phoneNumber')).required(),
      country: Joi.string().length(2).uppercase().required(),
      city: Joi.string().min(2).max(100).pattern(/^[a-zA-Z\s]+$/).required(),
      dateOfBirth: Joi.date().max('now').min('1900-01-01').required()
    }));

    // Trading offer schema
    this.validationSchemas.set('tradingOffer', Joi.object({
      type: Joi.string().valid('buy', 'sell').required(),
      cryptocurrency: Joi.object({
        symbol: Joi.string().valid('BTC', 'ETH', 'USDT', 'USDC', 'DAI').required(),
        network: Joi.string().valid('bitcoin', 'ethereum', 'polygon').required(),
        contractAddress: Joi.string().custom(this.customValidators.get('ethereumAddress')).when('symbol', {
          is: Joi.string().valid('USDT', 'USDC', 'DAI'),
          then: Joi.required(),
          otherwise: Joi.forbidden()
        })
      }).required(),
      amount: Joi.object({
        min: Joi.number().positive().precision(8).required(),
        max: Joi.number().positive().precision(8).min(Joi.ref('min')).required()
      }).required(),
      pricing: Joi.object({
        type: Joi.string().valid('fixed', 'market_percentage').required(),
        value: Joi.number().when('type', {
          is: 'fixed',
          then: Joi.number().positive().precision(2).required(),
          otherwise: Joi.number().min(-50).max(50).precision(2).required()
        })
      }).required(),
      paymentMethods: Joi.array().items(
        Joi.object({
          method: Joi.string().valid('mpesa', 'airtel_money', 'bank_transfer').required(),
          details: Joi.object().required()
        })
      ).min(1).max(3).required(),
      terms: Joi.string().min(10).max(1000).required(),
      location: Joi.object({
        country: Joi.string().length(2).uppercase().required(),
        city: Joi.string().min(2).max(100).required()
      }).required()
    }));

    // Trade execution schema
    this.validationSchemas.set('tradeExecution', Joi.object({
      offerId: Joi.string().custom(this.customValidators.get('mongoObjectId')).required(),
      amount: Joi.number().positive().precision(8).required(),
      paymentMethod: Joi.string().valid('mpesa', 'airtel_money', 'bank_transfer').required(),
      paymentDetails: Joi.object().required()
    }));

    // KYC document schema
    this.validationSchemas.set('kycDocument', Joi.object({
      documentType: Joi.string().valid('national_id', 'passport', 'driving_license').required(),
      documentNumber: Joi.string().min(5).max(50).alphanum().required(),
      expiryDate: Joi.date().min('now').required(),
      issuingCountry: Joi.string().length(2).uppercase().required(),
      frontImage: Joi.string().custom(this.customValidators.get('base64Image')).required(),
      backImage: Joi.string().custom(this.customValidators.get('base64Image')).when('documentType', {
        is: 'passport',
        then: Joi.forbidden(),
        otherwise: Joi.required()
      }),
      selfieImage: Joi.string().custom(this.customValidators.get('base64Image')).required()
    }));

    // Transaction schema
    this.validationSchemas.set('transaction', Joi.object({
      type: Joi.string().valid('send', 'receive', 'trade_escrow', 'trade_release').required(),
      cryptocurrency: Joi.object({
        symbol: Joi.string().valid('BTC', 'ETH', 'USDT', 'USDC', 'DAI').required(),
        network: Joi.string().valid('bitcoin', 'ethereum', 'polygon').required(),
        contractAddress: Joi.string().custom(this.customValidators.get('ethereumAddress')).optional()
      }).required(),
      amount: Joi.number().positive().precision(8).required(),
      recipientAddress: Joi.string().custom(this.customValidators.get('cryptoAddress')).required(),
      gasPrice: Joi.number().positive().optional(),
      gasLimit: Joi.number().positive().optional()
    }));

    // Chat message schema
    this.validationSchemas.set('chatMessage', Joi.object({
      tradeId: Joi.string().custom(this.customValidators.get('mongoObjectId')).required(),
      message: Joi.string().min(1).max(1000).required(),
      type: Joi.string().valid('text', 'image', 'file', 'payment_proof').default('text'),
      attachments: Joi.array().items(
        Joi.object({
          type: Joi.string().valid('image', 'document').required(),
          data: Joi.string().custom(this.customValidators.get('base64File')).required(),
          filename: Joi.string().max(255).required(),
          mimeType: Joi.string().valid('image/jpeg', 'image/png', 'application/pdf').required()
        })
      ).max(3).optional()
    }));
  }

  /**
   * Initialize custom validators
   */
  initializeCustomValidators() {
    // Ethereum address validator
    this.customValidators.set('ethereumAddress', (value, helpers) => {
      if (!ethers.isAddress(value)) {
        return helpers.error('any.invalid', { message: 'Invalid Ethereum address' });
      }
      return value.toLowerCase();
    });

    // Ethereum signature validator
    this.customValidators.set('ethereumSignature', (value, helpers) => {
      if (!/^0x[a-fA-F0-9]{130}$/.test(value)) {
        return helpers.error('any.invalid', { message: 'Invalid Ethereum signature format' });
      }
      return value;
    });

    // Bitcoin address validator
    this.customValidators.set('bitcoinAddress', (value, helpers) => {
      // Basic Bitcoin address validation (simplified)
      if (!/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/.test(value)) {
        return helpers.error('any.invalid', { message: 'Invalid Bitcoin address' });
      }
      return value;
    });

    // Generic crypto address validator
    this.customValidators.set('cryptoAddress', (value, helpers) => {
      // Try Ethereum first, then Bitcoin
      if (ethers.isAddress(value)) {
        return value.toLowerCase();
      }
      if (/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/.test(value)) {
        return value;
      }
      return helpers.error('any.invalid', { message: 'Invalid cryptocurrency address' });
    });

    // MongoDB ObjectId validator
    this.customValidators.set('mongoObjectId', (value, helpers) => {
      if (!/^[0-9a-fA-F]{24}$/.test(value)) {
        return helpers.error('any.invalid', { message: 'Invalid ObjectId format' });
      }
      return value;
    });

    // Phone number validator
    this.customValidators.set('phoneNumber', (value, helpers) => {
      // International phone number format
      if (!validator.isMobilePhone(value, 'any', { strictMode: false })) {
        return helpers.error('any.invalid', { message: 'Invalid phone number format' });
      }
      return value;
    });

    // Base64 image validator
    this.customValidators.set('base64Image', (value, helpers) => {
      // Check if it's a valid base64 image
      const base64Regex = /^data:image\/(jpeg|jpg|png);base64,([A-Za-z0-9+/=]+)$/;
      if (!base64Regex.test(value)) {
        return helpers.error('any.invalid', { message: 'Invalid base64 image format' });
      }
      
      // Check file size (max 5MB)
      const base64Data = value.split(',')[1];
      const sizeInBytes = (base64Data.length * 3) / 4;
      if (sizeInBytes > 5 * 1024 * 1024) {
        return helpers.error('any.invalid', { message: 'Image size exceeds 5MB limit' });
      }
      
      return value;
    });

    // Base64 file validator
    this.customValidators.set('base64File', (value, helpers) => {
      // Check if it's a valid base64 file
      const base64Regex = /^data:(image\/(jpeg|jpg|png)|application\/pdf);base64,([A-Za-z0-9+/=]+)$/;
      if (!base64Regex.test(value)) {
        return helpers.error('any.invalid', { message: 'Invalid base64 file format' });
      }
      
      // Check file size (max 10MB)
      const base64Data = value.split(',')[1];
      const sizeInBytes = (base64Data.length * 3) / 4;
      if (sizeInBytes > 10 * 1024 * 1024) {
        return helpers.error('any.invalid', { message: 'File size exceeds 10MB limit' });
      }
      
      return value;
    });

    // Amount validator with precision check
    this.customValidators.set('cryptoAmount', (value, helpers) => {
      const amount = parseFloat(value);
      if (isNaN(amount) || amount <= 0) {
        return helpers.error('any.invalid', { message: 'Invalid amount' });
      }
      
      // Check decimal places (max 8 for crypto)
      const decimalPlaces = (value.toString().split('.')[1] || '').length;
      if (decimalPlaces > 8) {
        return helpers.error('any.invalid', { message: 'Amount has too many decimal places' });
      }
      
      return amount;
    });
  }

  /**
   * Validate input against schema
   */
  validate(schemaName, data, options = {}) {
    const schema = this.validationSchemas.get(schemaName);
    if (!schema) {
      throw new Error(`Validation schema '${schemaName}' not found`);
    }

    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      ...options
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      logger.warn('Validation failed', {
        schema: schemaName,
        errors: validationErrors,
        data: this.sanitizeLogData(data)
      });

      throw new ValidationError('Input validation failed', validationErrors);
    }

    return value;
  }

  /**
   * Create validation middleware
   */
  createValidationMiddleware(schemaName, dataSource = 'body') {
    return (req, res, next) => {
      try {
        const data = req[dataSource];
        const validatedData = this.validate(schemaName, data);
        req[dataSource] = validatedData;
        next();
      } catch (error) {
        if (error instanceof ValidationError) {
          return res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: error.errors
          });
        }
        
        logger.error('Validation middleware error:', error);
        return res.status(500).json({
          success: false,
          error: 'Internal validation error'
        });
      }
    };
  }

  /**
   * Sanitize sensitive data for logging
   */
  sanitizeLogData(data) {
    const sensitiveFields = ['password', 'signature', 'privateKey', 'mnemonic', 'pin'];
    const sanitized = { ...data };
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }

  /**
   * Add custom validation schema
   */
  addSchema(name, schema) {
    this.validationSchemas.set(name, schema);
  }

  /**
   * Add custom validator
   */
  addCustomValidator(name, validator) {
    this.customValidators.set(name, validator);
  }

  /**
   * Get all available schemas
   */
  getAvailableSchemas() {
    return Array.from(this.validationSchemas.keys());
  }
}

/**
 * Custom validation error class
 */
class ValidationError extends Error {
  constructor(message, errors) {
    super(message);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

module.exports = {
  InputValidationService,
  ValidationError,
  inputValidationService: new InputValidationService()
};
