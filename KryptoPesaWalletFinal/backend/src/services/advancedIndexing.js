/**
 * Advanced Database Indexing Service for 50K+ Daily Users
 * Optimizes database performance through strategic indexing
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');

class AdvancedIndexingService {
  constructor() {
    this.indexingStrategies = new Map();
    this.performanceMetrics = {
      indexCreationTime: 0,
      queryOptimizationCount: 0,
      indexUsageStats: new Map()
    };
  }

  /**
   * Initialize all performance-critical indexes
   */
  async initializePerformanceIndexes() {
    try {
      logger.info('Creating performance-critical indexes for 50K+ users...');

      // User collection indexes
      await this.createUserIndexes();
      
      // Trade collection indexes
      await this.createTradeIndexes();
      
      // Offer collection indexes
      await this.createOfferIndexes();
      
      // Message collection indexes
      await this.createMessageIndexes();
      
      // Transaction collection indexes
      await this.createTransactionIndexes();
      
      // Notification collection indexes
      await this.createNotificationIndexes();

      logger.info('All performance indexes created successfully');
    } catch (error) {
      logger.error('Error creating performance indexes:', error);
      throw error;
    }
  }

  /**
   * User collection performance indexes
   */
  async createUserIndexes() {
    const User = mongoose.model('User');
    
    // Compound index for wallet authentication (most frequent query)
    await User.collection.createIndex(
      { walletAddress: 1, isActive: 1 },
      { 
        name: 'wallet_auth_performance',
        background: true,
        partialFilterExpression: { isActive: true }
      }
    );

    // Index for user search and filtering
    await User.collection.createIndex(
      { 'profile.firstName': 'text', 'profile.lastName': 'text', email: 'text' },
      { 
        name: 'user_search_text',
        background: true,
        weights: {
          'profile.firstName': 10,
          'profile.lastName': 10,
          email: 5
        }
      }
    );

    // Verification status index
    await User.collection.createIndex(
      { 'verification.status': 1, createdAt: -1 },
      { name: 'verification_status_performance', background: true }
    );

    // Reputation and trading activity index
    await User.collection.createIndex(
      { 'reputation.score': -1, 'stats.totalTrades': -1, isActive: 1 },
      { 
        name: 'reputation_trading_performance',
        background: true,
        partialFilterExpression: { isActive: true }
      }
    );

    logger.info('User collection indexes created');
  }

  /**
   * Trade collection performance indexes
   */
  async createTradeIndexes() {
    const Trade = mongoose.model('Trade');
    
    // Most critical: Active trades by user
    await Trade.collection.createIndex(
      { seller: 1, status: 1, createdAt: -1 },
      { 
        name: 'seller_active_trades',
        background: true,
        partialFilterExpression: { 
          status: { $in: ['pending', 'payment_sent', 'escrow_pending'] }
        }
      }
    );

    await Trade.collection.createIndex(
      { buyer: 1, status: 1, createdAt: -1 },
      { 
        name: 'buyer_active_trades',
        background: true,
        partialFilterExpression: { 
          status: { $in: ['pending', 'payment_sent', 'escrow_pending'] }
        }
      }
    );

    // Trade history and analytics
    await Trade.collection.createIndex(
      { 'cryptocurrency.symbol': 1, status: 1, createdAt: -1 },
      { name: 'crypto_trade_history', background: true }
    );

    // Dispute resolution index
    await Trade.collection.createIndex(
      { status: 1, 'dispute.createdAt': -1 },
      { 
        name: 'dispute_resolution',
        background: true,
        partialFilterExpression: { status: 'disputed' }
      }
    );

    // Escrow management index
    await Trade.collection.createIndex(
      { 'escrow.contractAddress': 1, 'escrow.status': 1 },
      { name: 'escrow_management', background: true }
    );

    logger.info('Trade collection indexes created');
  }

  /**
   * Offer collection performance indexes
   */
  async createOfferIndexes() {
    const Offer = mongoose.model('Offer');
    
    // Marketplace browsing (most frequent)
    await Offer.collection.createIndex(
      { 
        isActive: 1, 
        'cryptocurrency.symbol': 1, 
        type: 1, 
        'pricing.pricePerUnit': 1 
      },
      { 
        name: 'marketplace_browsing',
        background: true,
        partialFilterExpression: { isActive: true }
      }
    );

    // Geographic filtering
    await Offer.collection.createIndex(
      { 
        isActive: 1,
        'location.country': 1,
        'location.city': 1,
        'cryptocurrency.symbol': 1
      },
      { 
        name: 'geographic_offers',
        background: true,
        partialFilterExpression: { isActive: true }
      }
    );

    // Payment method filtering
    await Offer.collection.createIndex(
      { 
        isActive: 1,
        'paymentMethods.method': 1,
        'cryptocurrency.symbol': 1,
        type: 1
      },
      { 
        name: 'payment_method_offers',
        background: true,
        partialFilterExpression: { isActive: true }
      }
    );

    // User's offers management
    await Offer.collection.createIndex(
      { creator: 1, isActive: 1, createdAt: -1 },
      { name: 'user_offers_management', background: true }
    );

    logger.info('Offer collection indexes created');
  }

  /**
   * Message collection performance indexes
   */
  async createMessageIndexes() {
    const Message = mongoose.model('Message');
    
    // Chat room messages (most frequent)
    await Message.collection.createIndex(
      { tradeId: 1, createdAt: -1 },
      { name: 'trade_chat_messages', background: true }
    );

    // User message history
    await Message.collection.createIndex(
      { sender: 1, createdAt: -1 },
      { name: 'user_message_history', background: true }
    );

    // Unread messages
    await Message.collection.createIndex(
      { recipient: 1, isRead: 1, createdAt: -1 },
      { 
        name: 'unread_messages',
        background: true,
        partialFilterExpression: { isRead: false }
      }
    );

    logger.info('Message collection indexes created');
  }

  /**
   * Transaction collection performance indexes
   */
  async createTransactionIndexes() {
    const Transaction = mongoose.model('Transaction');
    
    // User transaction history
    await Transaction.collection.createIndex(
      { userId: 1, createdAt: -1 },
      { name: 'user_transaction_history', background: true }
    );

    // Blockchain transaction tracking
    await Transaction.collection.createIndex(
      { 
        'blockchain.network': 1, 
        'blockchain.txHash': 1,
        status: 1
      },
      { name: 'blockchain_tracking', background: true }
    );

    // Pending transactions monitoring
    await Transaction.collection.createIndex(
      { status: 1, createdAt: -1 },
      { 
        name: 'pending_transactions',
        background: true,
        partialFilterExpression: { status: 'pending' }
      }
    );

    logger.info('Transaction collection indexes created');
  }

  /**
   * Notification collection performance indexes
   */
  async createNotificationIndexes() {
    const Notification = mongoose.model('Notification');
    
    // User notifications
    await Notification.collection.createIndex(
      { userId: 1, isRead: 1, createdAt: -1 },
      { name: 'user_notifications', background: true }
    );

    // Notification cleanup (TTL index)
    await Notification.collection.createIndex(
      { createdAt: 1 },
      { 
        name: 'notification_ttl',
        background: true,
        expireAfterSeconds: 30 * 24 * 60 * 60 // 30 days
      }
    );

    logger.info('Notification collection indexes created');
  }

  /**
   * Monitor index usage and performance
   */
  async monitorIndexPerformance() {
    try {
      const collections = ['users', 'trades', 'offers', 'messages', 'transactions', 'notifications'];
      
      for (const collectionName of collections) {
        const stats = await mongoose.connection.db.collection(collectionName).indexStats();
        
        for (const indexStat of stats) {
          this.performanceMetrics.indexUsageStats.set(
            `${collectionName}.${indexStat.name}`,
            {
              usage: indexStat.accesses,
              lastAccessed: indexStat.lastAccessed,
              efficiency: this.calculateIndexEfficiency(indexStat)
            }
          );
        }
      }
      
      logger.info('Index performance monitoring completed');
    } catch (error) {
      logger.error('Error monitoring index performance:', error);
    }
  }

  /**
   * Calculate index efficiency score
   */
  calculateIndexEfficiency(indexStat) {
    const accessCount = indexStat.accesses?.ops || 0;
    const since = indexStat.accesses?.since || new Date();
    const hoursSince = (Date.now() - since.getTime()) / (1000 * 60 * 60);
    
    return hoursSince > 0 ? accessCount / hoursSince : 0;
  }

  /**
   * Get index performance report
   */
  getIndexPerformanceReport() {
    const report = {
      totalIndexes: this.performanceMetrics.indexUsageStats.size,
      highUsageIndexes: [],
      lowUsageIndexes: [],
      recommendations: []
    };

    for (const [indexName, stats] of this.performanceMetrics.indexUsageStats) {
      if (stats.efficiency > 100) {
        report.highUsageIndexes.push({ name: indexName, ...stats });
      } else if (stats.efficiency < 1) {
        report.lowUsageIndexes.push({ name: indexName, ...stats });
      }
    }

    // Generate recommendations
    if (report.lowUsageIndexes.length > 0) {
      report.recommendations.push('Consider removing unused indexes to improve write performance');
    }

    return report;
  }
}

module.exports = new AdvancedIndexingService();
