import React, { createContext, useContext, useState, useEffect } from 'react';

// Notification types
export type NotificationType = 
  | 'trade_request' 
  | 'payment_received' 
  | 'trade_completed' 
  | 'security_alert' 
  | 'kyc_update' 
  | 'system_update';

// Notification interface
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  relatedId?: string;
  icon: string;
  iconColor: string;
}

// Notification context interface
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => void;
  refreshNotifications: () => Promise<void>;
}

// Mock notifications data
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'trade_request',
    title: 'New Trade Request',
    message: '<PERSON> wants to buy 0.005 BTC from you',
    timestamp: '2024-01-15T14:30:00Z',
    isRead: false,
    relatedId: 'trade_123',
    icon: 'swap-horizontal',
    iconColor: 'primary',
  },
  {
    id: '2',
    type: 'payment_received',
    title: 'Payment Received',
    message: 'Payment of $250 has been confirmed for trade #456',
    timestamp: '2024-01-15T13:15:00Z',
    isRead: false,
    relatedId: 'trade_456',
    icon: 'checkmark-circle',
    iconColor: 'success',
  },
  {
    id: '3',
    type: 'kyc_update',
    title: 'KYC Verification Approved',
    message: 'Your identity verification has been approved. You can now trade up to $10,000.',
    timestamp: '2024-01-15T12:45:00Z',
    isRead: false,
    relatedId: null,
    icon: 'shield-checkmark',
    iconColor: 'success',
  },
  {
    id: '4',
    type: 'trade_completed',
    title: 'Trade Completed',
    message: 'Your trade with Alice Johnson has been completed successfully',
    timestamp: '2024-01-15T12:00:00Z',
    isRead: true,
    relatedId: 'trade_789',
    icon: 'trophy',
    iconColor: 'warning',
  },
  {
    id: '5',
    type: 'security_alert',
    title: 'New Login Detected',
    message: 'A new login was detected from Chrome on Windows',
    timestamp: '2024-01-14T20:30:00Z',
    isRead: true,
    relatedId: null,
    icon: 'shield-outline',
    iconColor: 'error',
  },
  {
    id: '6',
    type: 'system_update',
    title: 'System Update',
    message: 'New features available! Check out the latest improvements',
    timestamp: '2024-01-14T18:00:00Z',
    isRead: true,
    relatedId: null,
    icon: 'information-circle',
    iconColor: 'textSecondary',
  },
];

// Create the context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Notification Provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [isLoading, setIsLoading] = useState(false);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.isRead).length;

  // Mark a single notification as read
  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, isRead: true } : n
      )
    );
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, isRead: true }))
    );
  };

  // Clear all notifications
  const clearAll = () => {
    setNotifications([]);
  };

  // Add a new notification
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      isRead: false,
    };

    setNotifications(prev => [newNotification, ...prev]);
  };

  // Refresh notifications from backend
  const refreshNotifications = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would fetch from backend
      // const freshNotifications = await getNotificationsQuery();
      // setNotifications(freshNotifications);
      
      // For now, just simulate a refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Failed to refresh notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Simulate receiving new notifications
  useEffect(() => {
    // In a real app, this would be a WebSocket connection or push notification handler
    const interval = setInterval(() => {
      // Randomly add a new notification (for demo purposes)
      if (Math.random() > 0.95) { // 5% chance every interval
        const randomNotifications = [
          {
            type: 'trade_request' as NotificationType,
            title: 'New Trade Request',
            message: 'Someone wants to trade with you',
            icon: 'swap-horizontal',
            iconColor: 'primary',
          },
          {
            type: 'payment_received' as NotificationType,
            title: 'Payment Received',
            message: 'Payment confirmed for your trade',
            icon: 'checkmark-circle',
            iconColor: 'success',
          },
        ];
        
        const randomNotification = randomNotifications[Math.floor(Math.random() * randomNotifications.length)];
        addNotification(randomNotification);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    clearAll,
    addNotification,
    refreshNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
