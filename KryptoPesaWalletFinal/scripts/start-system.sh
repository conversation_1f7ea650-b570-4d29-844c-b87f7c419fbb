#!/bin/bash

# KryptoPesa System Startup Script
# Starts all components: Backend, Admin Dashboard, and prepares for mobile testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Function to kill process on port
kill_port() {
    local port=$1
    if port_in_use $port; then
        print_warning "Killing existing process on port $port"
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18 or later."
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        print_error "Node.js version 18 or later is required. Current version: $(node -v)"
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed."
        exit 1
    fi
    
    # Check if MongoDB is available (Docker or local)
    if ! command_exists mongod && ! command_exists docker; then
        print_error "Neither MongoDB nor Docker is available. Please install one of them."
        exit 1
    fi
    
    # Check if Redis is available (Docker or local)
    if ! command_exists redis-server && ! command_exists docker; then
        print_warning "Neither Redis nor Docker is available. Some features may not work."
    fi
    
    print_success "Prerequisites check passed!"
}

# Start MongoDB
start_mongodb() {
    print_status "Starting MongoDB..."
    
    if command_exists docker; then
        # Check if MongoDB container is already running
        if docker ps | grep -q "kryptopesa-mongodb"; then
            print_success "MongoDB container is already running"
            return 0
        fi
        
        # Start MongoDB with Docker
        docker run -d \
            --name kryptopesa-mongodb \
            -p 27017:27017 \
            -e MONGO_INITDB_ROOT_USERNAME=admin \
            -e MONGO_INITDB_ROOT_PASSWORD=password123 \
            -e MONGO_INITDB_DATABASE=kryptopesa \
            -v kryptopesa_mongodb_data:/data/db \
            mongo:7.0 \
            --auth
        
        wait_for_service "mongodb://localhost:27017" "MongoDB"
    else
        # Start local MongoDB
        if ! pgrep mongod > /dev/null; then
            mongod --dbpath ./data/db --logpath ./data/mongodb.log --fork
            wait_for_service "mongodb://localhost:27017" "MongoDB"
        else
            print_success "MongoDB is already running"
        fi
    fi
}

# Start Redis
start_redis() {
    print_status "Starting Redis..."
    
    if command_exists docker; then
        # Check if Redis container is already running
        if docker ps | grep -q "kryptopesa-redis"; then
            print_success "Redis container is already running"
            return 0
        fi
        
        # Start Redis with Docker
        docker run -d \
            --name kryptopesa-redis \
            -p 6379:6379 \
            -v kryptopesa_redis_data:/data \
            redis:7.2-alpine \
            redis-server --appendonly yes --requirepass "redis123"
        
        wait_for_service "http://localhost:6379" "Redis" || true
    else
        # Start local Redis
        if ! pgrep redis-server > /dev/null; then
            redis-server --daemonize yes --logfile ./data/redis.log
            sleep 2
        else
            print_success "Redis is already running"
        fi
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Backend dependencies
    if [ ! -d "backend/node_modules" ]; then
        print_status "Installing backend dependencies..."
        cd backend
        npm install
        cd ..
    fi
    
    # Admin dashboard dependencies
    if [ ! -d "admin-dashboard/node_modules" ]; then
        print_status "Installing admin dashboard dependencies..."
        cd admin-dashboard
        npm install
        cd ..
    fi
    
    # Mobile app dependencies (React Native)
    if [ ! -d "mobile/node_modules" ]; then
        print_status "Installing mobile app dependencies..."
        cd mobile
        npm install
        cd ..
    fi
    
    print_success "Dependencies installed!"
}

# Start backend server
start_backend() {
    print_status "Starting backend server..."
    
    # Kill existing backend process
    kill_port 3000
    
    cd backend
    
    # Set environment variables
    export NODE_ENV=development
    export PORT=3000
    export MONGODB_URI=***********************************************************************
    export REDIS_URL=redis://:redis123@localhost:6379
    export JWT_SECRET=your-super-secret-jwt-key-change-in-production
    export ENCRYPTION_KEY=your-32-character-encryption-key
    
    # Start backend in background
    npm start > ../logs/backend.log 2>&1 &
    echo $! > ../logs/backend.pid
    
    cd ..
    
    # Wait for backend to be ready
    wait_for_service "http://localhost:3000/api/health" "Backend API"
}

# Start admin dashboard
start_admin_dashboard() {
    print_status "Starting admin dashboard..."
    
    # Kill existing admin dashboard process
    kill_port 3001
    
    cd admin-dashboard
    
    # Set environment variables
    export REACT_APP_API_URL=http://localhost:3000
    export REACT_APP_WS_URL=ws://localhost:3000
    export PORT=3001
    
    # Build and start admin dashboard
    if [ ! -d "build" ]; then
        print_status "Building admin dashboard..."
        npm run build
    fi
    
    # Start admin dashboard in background
    npx serve -s build -l 3001 > ../logs/admin-dashboard.log 2>&1 &
    echo $! > ../logs/admin-dashboard.pid
    
    cd ..
    
    # Wait for admin dashboard to be ready
    wait_for_service "http://localhost:3001" "Admin Dashboard"
}

# Prepare mobile app
prepare_mobile_app() {
    print_status "Preparing mobile app for device installation..."
    
    cd mobile
    
    # Set environment variables
    export REACT_NATIVE_API_URL=http://********:3000  # Android emulator
    export REACT_NATIVE_WS_URL=ws://********:3000
    
    # Check if Android SDK is available
    if [ -z "$ANDROID_HOME" ]; then
        print_warning "ANDROID_HOME not set. Please ensure Android SDK is installed."
        print_warning "You can install it via Android Studio or command line tools."
    fi
    
    # Check if device is connected
    if command_exists adb; then
        local devices=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
        if [ "$devices" -gt 0 ]; then
            print_success "Android device(s) detected: $devices"
            adb devices
        else
            print_warning "No Android devices detected. Please connect your Pixel 7 via USB."
            print_warning "Make sure USB debugging is enabled in Developer Options."
        fi
    else
        print_warning "ADB not found. Please install Android SDK platform tools."
    fi
    
    cd ..
}

# Create logs directory
create_logs_dir() {
    if [ ! -d "logs" ]; then
        mkdir -p logs
    fi
}

# Main execution
main() {
    print_status "🚀 Starting KryptoPesa System..."
    echo "=================================================="
    
    # Change to project directory
    cd "$(dirname "$0")/.."
    
    # Create logs directory
    create_logs_dir
    
    # Check prerequisites
    check_prerequisites
    
    # Start services
    start_mongodb
    start_redis
    
    # Install dependencies
    install_dependencies
    
    # Start backend
    start_backend
    
    # Start admin dashboard
    start_admin_dashboard
    
    # Prepare mobile app
    prepare_mobile_app
    
    echo "=================================================="
    print_success "🎉 KryptoPesa System Started Successfully!"
    echo ""
    print_status "📊 Admin Dashboard: http://localhost:3001"
    print_status "🔧 Backend API: http://localhost:3000"
    print_status "📱 Mobile App: Ready for device installation"
    echo ""
    print_status "📋 Next Steps:"
    echo "  1. Open Admin Dashboard: http://localhost:3001"
    echo "  2. Connect your Pixel 7 via USB"
    echo "  3. Enable USB debugging on your phone"
    echo "  4. Run: cd mobile && npm run android"
    echo ""
    print_status "📝 Logs are available in the 'logs' directory"
    print_status "🛑 To stop the system, run: ./scripts/stop-system.sh"
}

# Run main function
main "$@"
