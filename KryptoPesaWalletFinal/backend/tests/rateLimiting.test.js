const request = require('supertest');
const { app } = require('../src/server');
const rateLimitingService = require('../src/services/rateLimitingService');
const { getRedisClient } = require('../src/config/redis');
const User = require('../src/models/User');
const jwt = require('jsonwebtoken');

describe('Rate Limiting System', () => {
  let redisClient;
  let adminToken;
  let userToken;
  let testUser;
  let adminUser;

  beforeAll(async () => {
    // Initialize Redis client
    redisClient = getRedisClient();
    
    // Initialize rate limiting service
    await rateLimitingService.initialize();

    // Create test users
    testUser = new User({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'hashedpassword',
      role: 'user'
    });
    await testUser.save();

    adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: 'hashedpassword',
      role: 'admin'
    });
    await adminUser.save();

    // Generate tokens
    userToken = jwt.sign(
      { userId: testUser._id, username: testUser.username },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { userId: adminUser._id, username: adminUser.username },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // Cleanup test data
    await User.deleteMany({ email: { $in: ['<EMAIL>', '<EMAIL>'] } });
    
    // Clear rate limiting data
    if (redisClient && redisClient.isReady) {
      const keys = await redisClient.keys('rl:*');
      if (keys.length > 0) {
        await redisClient.del(...keys);
      }
    }
    
    rateLimitingService.stopMonitoring();
  });

  beforeEach(async () => {
    // Clear rate limiting data before each test
    if (redisClient && redisClient.isReady) {
      const keys = await redisClient.keys('rl:*');
      if (keys.length > 0) {
        await redisClient.del(...keys);
      }
    }
    rateLimitingService.resetMetrics();
  });

  describe('Production Rate Limiting', () => {
    test('should apply different rate limits to different endpoints', async () => {
      // Test login rate limiting (very strict)
      const loginRequests = [];
      for (let i = 0; i < 5; i++) {
        loginRequests.push(
          request(app)
            .post('/api/auth/login')
            .send({ email: '<EMAIL>', password: 'wrongpassword' })
        );
      }
      
      const loginResponses = await Promise.all(loginRequests);
      const rateLimitedLogin = loginResponses.filter(res => res.status === 429);
      expect(rateLimitedLogin.length).toBeGreaterThan(0);
    });

    test('should allow requests within rate limits', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.headers['x-ratelimit-limit']).toBeDefined();
      expect(response.headers['x-ratelimit-remaining']).toBeDefined();
    });

    test('should block requests exceeding rate limits', async () => {
      // Make multiple requests to exceed limit
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app)
            .post('/api/auth/login')
            .send({ email: '<EMAIL>', password: 'wrongpassword' })
        );
      }

      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
      expect(rateLimitedResponses[0].body.error).toBe('Rate limit exceeded');
      expect(rateLimitedResponses[0].body.retryAfter).toBeDefined();
    });

    test('should include proper rate limit headers', async () => {
      const response = await request(app)
        .get('/api/health');

      expect(response.headers['x-ratelimit-limit']).toBeDefined();
      expect(response.headers['x-ratelimit-remaining']).toBeDefined();
      expect(response.headers['x-ratelimit-reset']).toBeDefined();
      expect(response.headers['x-ratelimit-type']).toBeDefined();
    });
  });

  describe('Rate Limiting Service', () => {
    test('should initialize successfully', async () => {
      const service = require('../src/services/rateLimitingService');
      const initialized = await service.initialize();
      expect(initialized).toBe(true);
    });

    test('should track request metrics', () => {
      const ip = '***********';
      const userAgent = 'test-agent';
      const endpoint = '/api/test';

      rateLimitingService.trackRequest(ip, userAgent, endpoint, false);
      rateLimitingService.trackRequest(ip, userAgent, endpoint, true);

      const metrics = rateLimitingService.getMetrics();
      expect(metrics.totalRequests).toBe(2);
      expect(metrics.blockedRequests).toBe(1);
      expect(metrics.rateLimitViolations).toBe(1);
    });

    test('should perform health checks', async () => {
      const healthStatus = await rateLimitingService.performHealthCheck();
      expect(healthStatus.status).toBeDefined();
      expect(healthStatus.timestamp).toBeDefined();
    });

    test('should manage IP whitelist', async () => {
      const testIP = '***********00';
      
      // Whitelist IP
      const whitelisted = await rateLimitingService.whitelistIP(testIP, 60);
      expect(whitelisted).toBe(true);

      // Check if whitelisted
      const isWhitelisted = await rateLimitingService.isWhitelisted(testIP);
      expect(isWhitelisted).toBe(true);
    });

    test('should manage IP blacklist', async () => {
      const testIP = '*************';
      
      // Blacklist IP
      const blacklisted = await rateLimitingService.blacklistIP(testIP, 60);
      expect(blacklisted).toBe(true);

      // Check if blacklisted
      const isBlacklisted = await rateLimitingService.isBlacklisted(testIP);
      expect(isBlacklisted).toBe(true);
    });

    test('should get IP status', async () => {
      const testIP = '************';
      const status = await rateLimitingService.getIPStatus(testIP);
      
      expect(status.ip).toBe(testIP);
      expect(status.activeRateLimits).toBeDefined();
      expect(status.violations).toBeDefined();
      expect(status.suspicious).toBeDefined();
    });
  });

  describe('Admin Rate Limiting API', () => {
    test('should require admin authentication', async () => {
      await request(app)
        .get('/api/admin/rate-limiting/metrics')
        .expect(401);

      await request(app)
        .get('/api/admin/rate-limiting/metrics')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });

    test('should get rate limiting metrics for admin', async () => {
      const response = await request(app)
        .get('/api/admin/rate-limiting/metrics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.metrics).toBeDefined();
      expect(response.body.data.health).toBeDefined();
    });

    test('should allow admin to whitelist IP', async () => {
      const testIP = '***********50';
      
      const response = await request(app)
        .post('/api/admin/rate-limiting/whitelist')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          ip: testIP,
          duration: 3600,
          reason: 'Test whitelist'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.ip).toBe(testIP);
    });

    test('should allow admin to blacklist IP', async () => {
      const testIP = '*************';
      
      const response = await request(app)
        .post('/api/admin/rate-limiting/blacklist')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          ip: testIP,
          duration: 3600,
          reason: 'Test blacklist'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.ip).toBe(testIP);
    });

    test('should get IP status for admin', async () => {
      const testIP = '************';
      
      const response = await request(app)
        .get(`/api/admin/rate-limiting/ip/${testIP}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.ip).toBe(testIP);
      expect(response.body.data.status).toBeDefined();
    });

    test('should reset metrics for admin', async () => {
      const response = await request(app)
        .post('/api/admin/rate-limiting/reset-metrics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('reset');
    });

    test('should validate IP format', async () => {
      const invalidIP = 'invalid-ip';
      
      await request(app)
        .post('/api/admin/rate-limiting/whitelist')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          ip: invalidIP,
          duration: 3600
        })
        .expect(400);
    });
  });

  describe('Smart Rate Limiting', () => {
    test('should apply appropriate limits based on endpoint', async () => {
      // Test that different endpoints get different rate limits
      const healthResponse = await request(app).get('/api/health');
      const authResponse = await request(app).post('/api/auth/login').send({});

      expect(healthResponse.headers['x-ratelimit-type']).toBe('health');
      expect(authResponse.headers['x-ratelimit-type']).toBe('login');
    });

    test('should handle Redis connection failures gracefully', async () => {
      // Temporarily disable Redis
      const originalRedisClient = rateLimitingService.redisClient;
      rateLimitingService.redisClient = null;

      const response = await request(app)
        .get('/api/health')
        .expect(200);

      // Should still work without Redis
      expect(response.status).toBe(200);

      // Restore Redis client
      rateLimitingService.redisClient = originalRedisClient;
    });
  });

  describe('Rate Limiting Edge Cases', () => {
    test('should handle concurrent requests correctly', async () => {
      const concurrentRequests = Array(20).fill().map(() =>
        request(app)
          .post('/api/auth/login')
          .send({ email: '<EMAIL>', password: 'wrongpassword' })
      );

      const responses = await Promise.all(concurrentRequests);
      const successfulResponses = responses.filter(res => res.status !== 429);
      const rateLimitedResponses = responses.filter(res => res.status === 429);

      // Should have some successful and some rate limited
      expect(successfulResponses.length).toBeGreaterThan(0);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    test('should reset rate limits after window expires', async () => {
      // This test would need to wait for the rate limit window to expire
      // For testing purposes, we'll just verify the logic exists
      const metrics = rateLimitingService.getMetrics();
      expect(metrics.timeSinceReset).toBeDefined();
    });

    test('should handle malformed requests gracefully', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send('invalid json')
        .expect(400);

      // Should still apply rate limiting even for malformed requests
      expect(response.headers['x-ratelimit-limit']).toBeDefined();
    });
  });
});
