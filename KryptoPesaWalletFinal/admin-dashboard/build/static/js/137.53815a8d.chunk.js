"use strict";(self.webpackChunkkryptopesa_admin_dashboard=self.webpackChunkkryptopesa_admin_dashboard||[]).push([[137],{5137:(e,r,n)=>{n.r(r),n.d(r,{default:()=>R});var i=n(5043),s=n(6446),t=n(5865),l=n(3336),a=n(9650),d=n(1806),o=n(4882),c=n(8076),u=n(39),h=n(3460),A=n(3845),x=n(1906),j=n(35),p=n(6600),m=n(5316),v=n(3193),g=n(9190),_=n(648),f=n(2143),D=n(5795),b=n(9347),y=n(579);const R=()=>{const[e,r]=(0,i.useState)(null),[n,R]=(0,i.useState)(!1),[w,C]=(0,i.useState)(""),[S,k]=(0,i.useState)(""),I=e=>{switch(e){case"open":return"error";case"under_review":return"warning";case"resolved":return"success";default:return"default"}},T=e=>{switch(e){case"urgent":return"error";case"high":return"warning";case"medium":return"info";default:return"default"}};return(0,y.jsxs)(s.A,{children:[(0,y.jsx)(t.A,{variant:"h4",gutterBottom:!0,children:"Dispute Management"}),(0,y.jsx)(l.A,{children:(0,y.jsx)(a.A,{children:(0,y.jsxs)(d.A,{children:[(0,y.jsx)(o.A,{children:(0,y.jsxs)(c.A,{children:[(0,y.jsx)(u.A,{children:"Dispute ID"}),(0,y.jsx)(u.A,{children:"Trade ID"}),(0,y.jsx)(u.A,{children:"Initiator"}),(0,y.jsx)(u.A,{children:"Respondent"}),(0,y.jsx)(u.A,{children:"Category"}),(0,y.jsx)(u.A,{children:"Amount"}),(0,y.jsx)(u.A,{children:"Status"}),(0,y.jsx)(u.A,{children:"Priority"}),(0,y.jsx)(u.A,{children:"Created"}),(0,y.jsx)(u.A,{children:"Actions"})]})}),(0,y.jsx)(h.A,{children:[{id:"DSP-001",tradeId:"TRD-001",initiator:"john_doe",respondent:"jane_smith",category:"payment_not_received",status:"open",priority:"high",createdAt:"2023-07-15T10:30:00Z",amount:"500 USDT"},{id:"DSP-002",tradeId:"TRD-002",initiator:"alice_trader",respondent:"bob_crypto",category:"wrong_amount",status:"under_review",priority:"medium",createdAt:"2023-07-14T15:45:00Z",amount:"1000 USDT"}].map(e=>(0,y.jsxs)(c.A,{children:[(0,y.jsx)(u.A,{children:e.id}),(0,y.jsx)(u.A,{children:e.tradeId}),(0,y.jsx)(u.A,{children:e.initiator}),(0,y.jsx)(u.A,{children:e.respondent}),(0,y.jsx)(u.A,{children:e.category.replace("_"," ")}),(0,y.jsx)(u.A,{children:e.amount}),(0,y.jsx)(u.A,{children:(0,y.jsx)(A.A,{label:e.status,color:I(e.status),size:"small"})}),(0,y.jsx)(u.A,{children:(0,y.jsx)(A.A,{label:e.priority,color:T(e.priority),size:"small"})}),(0,y.jsx)(u.A,{children:new Date(e.createdAt).toLocaleDateString()}),(0,y.jsx)(u.A,{children:(0,y.jsx)(x.A,{variant:"contained",size:"small",onClick:()=>(e=>{r(e),R(!0)})(e),disabled:"resolved"===e.status,children:"Resolve"})})]},e.id))})]})})}),(0,y.jsxs)(j.A,{open:n,onClose:()=>R(!1),maxWidth:"sm",fullWidth:!0,children:[(0,y.jsxs)(p.A,{children:["Resolve Dispute ",null===e||void 0===e?void 0:e.id]}),(0,y.jsx)(m.A,{children:(0,y.jsxs)(s.A,{sx:{mt:2},children:[(0,y.jsxs)(v.A,{fullWidth:!0,margin:"normal",children:[(0,y.jsx)(g.A,{children:"Resolution Decision"}),(0,y.jsxs)(_.A,{value:w,onChange:e=>C(e.target.value),label:"Resolution Decision",children:[(0,y.jsx)(f.A,{value:"favor_initiator",children:"Favor Initiator"}),(0,y.jsx)(f.A,{value:"favor_respondent",children:"Favor Respondent"}),(0,y.jsx)(f.A,{value:"partial_refund",children:"Partial Refund"}),(0,y.jsx)(f.A,{value:"no_action",children:"No Action"})]})]}),(0,y.jsx)(D.A,{fullWidth:!0,multiline:!0,rows:4,label:"Reasoning",value:S,onChange:e=>k(e.target.value),margin:"normal",placeholder:"Explain the reasoning for this resolution..."})]})}),(0,y.jsxs)(b.A,{children:[(0,y.jsx)(x.A,{onClick:()=>R(!1),children:"Cancel"}),(0,y.jsx)(x.A,{onClick:()=>{console.log("Resolving dispute:",{disputeId:e.id,resolution:w,reasoning:S}),R(!1),r(null),C(""),k("")},variant:"contained",disabled:!w||!S,children:"Submit Resolution"})]})]})]})}}}]);
//# sourceMappingURL=137.53815a8d.chunk.js.map