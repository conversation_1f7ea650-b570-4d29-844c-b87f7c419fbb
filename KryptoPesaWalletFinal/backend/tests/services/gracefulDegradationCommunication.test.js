/**
 * Graceful Degradation Communication Tests
 * Tests for system status communication and degradation notifications
 */

const { gracefulDegradationService } = require('../../src/services/gracefulDegradation');
const { realTimeScalabilityService } = require('../../src/services/realTimeScalabilityService');
const { notificationService } = require('../../src/services/notificationService');

// Mock dependencies
jest.mock('../../src/services/realTimeScalabilityService');
jest.mock('../../src/services/notificationService');
jest.mock('../../src/utils/logger');

describe('Graceful Degradation Communication', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset service state
    gracefulDegradationService.degradationLevel = 'none';
    gracefulDegradationService.previousDegradationLevel = 'none';
    gracefulDegradationService.degradationHistory = [];
    gracefulDegradationService.activeAlerts.clear();
    gracefulDegradationService.lastNotificationTime.clear();
  });

  describe('Degradation Level Changes', () => {
    test('should handle degradation level change from none to partial', async () => {
      // Mock real-time service
      realTimeScalabilityService.broadcast = jest.fn().mockResolvedValue();
      realTimeScalabilityService.publishEvent = jest.fn().mockResolvedValue();

      // Trigger degradation level change
      await gracefulDegradationService.handleDegradationLevelChange('none', 'partial');

      // Verify degradation event was recorded
      expect(gracefulDegradationService.degradationHistory).toHaveLength(1);
      const event = gracefulDegradationService.degradationHistory[0];
      expect(event.previousLevel).toBe('none');
      expect(event.newLevel).toBe('partial');
      expect(event.affectedFeatures).toBeDefined();

      // Verify broadcast was called
      expect(realTimeScalabilityService.broadcast).toHaveBeenCalledWith(
        'system_status_update',
        expect.objectContaining({
          type: 'system_status_update',
          degradationLevel: 'partial',
          previousLevel: 'none'
        })
      );

      // Verify admin notification was sent
      expect(realTimeScalabilityService.publishEvent).toHaveBeenCalledWith(
        'admin',
        'admin_alert',
        expect.objectContaining({
          type: 'system_degradation_alert',
          title: 'System Degradation Alert - PARTIAL'
        })
      );
    });

    test('should handle degradation level change from partial to severe', async () => {
      realTimeScalabilityService.broadcast = jest.fn().mockResolvedValue();
      realTimeScalabilityService.publishEvent = jest.fn().mockResolvedValue();

      await gracefulDegradationService.handleDegradationLevelChange('partial', 'severe');

      const event = gracefulDegradationService.degradationHistory[0];
      expect(event.previousLevel).toBe('partial');
      expect(event.newLevel).toBe('severe');

      // Verify high priority admin alert for severe degradation
      expect(realTimeScalabilityService.publishEvent).toHaveBeenCalledWith(
        'admin',
        'admin_alert',
        expect.objectContaining({
          priority: 'high',
          title: 'System Degradation Alert - SEVERE'
        })
      );
    });

    test('should handle recovery from degradation to healthy', async () => {
      realTimeScalabilityService.broadcast = jest.fn().mockResolvedValue();
      realTimeScalabilityService.publishEvent = jest.fn().mockResolvedValue();

      await gracefulDegradationService.handleDegradationLevelChange('severe', 'none');

      const event = gracefulDegradationService.degradationHistory[0];
      expect(event.previousLevel).toBe('severe');
      expect(event.newLevel).toBe('none');

      // Verify broadcast was sent for recovery
      expect(realTimeScalabilityService.broadcast).toHaveBeenCalledWith(
        'system_status_update',
        expect.objectContaining({
          degradationLevel: 'none',
          previousLevel: 'severe'
        })
      );

      // Admin notification should not be sent for recovery (severity decreased)
      expect(realTimeScalabilityService.publishEvent).not.toHaveBeenCalled();
    });
  });

  describe('Affected Features Detection', () => {
    test('should correctly identify disabled features for partial degradation', () => {
      const affectedFeatures = gracefulDegradationService.getAffectedFeatures('none', 'partial');
      
      expect(affectedFeatures.disabled).toContain('analytics');
      expect(affectedFeatures.enabled).toHaveLength(0);
      expect(affectedFeatures.limited).toContain('realTimeUpdates');
    });

    test('should correctly identify disabled features for severe degradation', () => {
      const affectedFeatures = gracefulDegradationService.getAffectedFeatures('none', 'severe');
      
      expect(affectedFeatures.disabled).toEqual(
        expect.arrayContaining(['realTimeUpdates', 'notifications', 'analytics', 'reporting'])
      );
    });

    test('should correctly identify enabled features during recovery', () => {
      const affectedFeatures = gracefulDegradationService.getAffectedFeatures('severe', 'none');
      
      expect(affectedFeatures.enabled).toEqual(
        expect.arrayContaining(['realTimeUpdates', 'notifications', 'analytics', 'reporting'])
      );
      expect(affectedFeatures.disabled).toHaveLength(0);
    });
  });

  describe('User Notifications', () => {
    test('should send user notifications for degradation with throttling', async () => {
      realTimeScalabilityService.broadcast = jest.fn().mockResolvedValue();

      const degradationEvent = {
        newLevel: 'partial',
        previousLevel: 'none',
        affectedFeatures: { disabled: ['analytics'], enabled: [], limited: [] },
        timestamp: new Date()
      };

      await gracefulDegradationService.notifyUsers(degradationEvent);

      // Verify user notification was sent
      expect(realTimeScalabilityService.broadcast).toHaveBeenCalledWith(
        'system_notification',
        expect.objectContaining({
          type: 'system_status_notification',
          title: 'Service Status Update',
          priority: 'normal'
        })
      );

      // Verify throttling timestamp was set
      expect(gracefulDegradationService.lastNotificationTime.has('users')).toBe(true);
    });

    test('should respect throttling for user notifications', async () => {
      realTimeScalabilityService.broadcast = jest.fn().mockResolvedValue();

      // Set recent notification time
      gracefulDegradationService.lastNotificationTime.set('users', Date.now());

      const degradationEvent = {
        newLevel: 'partial',
        previousLevel: 'none',
        affectedFeatures: { disabled: [], enabled: [], limited: [] },
        timestamp: new Date()
      };

      await gracefulDegradationService.notifyUsers(degradationEvent);

      // Verify notification was throttled
      expect(realTimeScalabilityService.broadcast).not.toHaveBeenCalled();
    });

    test('should not send user notifications for recovery', async () => {
      realTimeScalabilityService.broadcast = jest.fn().mockResolvedValue();

      const degradationEvent = {
        newLevel: 'none',
        previousLevel: 'partial',
        affectedFeatures: { disabled: [], enabled: ['analytics'], limited: [] },
        timestamp: new Date()
      };

      await gracefulDegradationService.notifyUsers(degradationEvent);

      // Verify no notification was sent for recovery
      expect(realTimeScalabilityService.broadcast).not.toHaveBeenCalled();
    });
  });

  describe('Active Alerts Management', () => {
    test('should create active alert for degradation', async () => {
      const degradationEvent = {
        newLevel: 'partial',
        timestamp: new Date()
      };

      await gracefulDegradationService.updateActiveAlerts(degradationEvent);

      expect(gracefulDegradationService.activeAlerts.size).toBe(1);
      const alert = gracefulDegradationService.activeAlerts.get('degradation_partial');
      expect(alert).toMatchObject({
        id: 'degradation_partial',
        type: 'system_degradation',
        level: 'partial',
        acknowledged: false
      });
    });

    test('should clear alerts when system recovers', async () => {
      // First create an alert
      await gracefulDegradationService.updateActiveAlerts({
        newLevel: 'partial',
        timestamp: new Date()
      });

      expect(gracefulDegradationService.activeAlerts.size).toBe(1);

      // Then recover
      await gracefulDegradationService.updateActiveAlerts({
        newLevel: 'none',
        timestamp: new Date()
      });

      expect(gracefulDegradationService.activeAlerts.size).toBe(0);
    });

    test('should acknowledge alert', () => {
      // Create an alert
      gracefulDegradationService.activeAlerts.set('test_alert', {
        id: 'test_alert',
        acknowledged: false
      });

      const result = gracefulDegradationService.acknowledgeAlert('test_alert', 'admin123');

      expect(result).toBe(true);
      const alert = gracefulDegradationService.activeAlerts.get('test_alert');
      expect(alert.acknowledged).toBe(true);
      expect(alert.acknowledgedBy).toBe('admin123');
      expect(alert.acknowledgedAt).toBeInstanceOf(Date);
    });
  });

  describe('Communication Configuration', () => {
    test('should update communication configuration', () => {
      const newConfig = {
        broadcastStatusChanges: false,
        notifyAdmins: true,
        notifyUsers: false,
        alertThresholds: {
          partial: 60000,
          severe: 30000
        }
      };

      gracefulDegradationService.updateCommunicationConfig(newConfig);

      const config = gracefulDegradationService.getCommunicationConfig();
      expect(config.broadcastStatusChanges).toBe(false);
      expect(config.alertThresholds.partial).toBe(60000);
    });

    test('should respect disabled communication settings', async () => {
      realTimeScalabilityService.broadcast = jest.fn().mockResolvedValue();
      realTimeScalabilityService.publishEvent = jest.fn().mockResolvedValue();

      // Disable all communications
      gracefulDegradationService.updateCommunicationConfig({
        broadcastStatusChanges: false,
        notifyAdmins: false,
        notifyUsers: false
      });

      await gracefulDegradationService.handleDegradationLevelChange('none', 'severe');

      // Verify no communications were sent
      expect(realTimeScalabilityService.broadcast).not.toHaveBeenCalled();
      expect(realTimeScalabilityService.publishEvent).not.toHaveBeenCalled();
    });
  });

  describe('Statistics and Metrics', () => {
    test('should calculate degradation statistics', () => {
      // Add some test events
      const now = new Date();
      const events = [
        {
          timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
          newLevel: 'partial'
        },
        {
          timestamp: new Date(now.getTime() - 1 * 60 * 60 * 1000), // 1 hour ago
          newLevel: 'severe'
        },
        {
          timestamp: new Date(now.getTime() - 30 * 60 * 1000), // 30 minutes ago
          newLevel: 'none'
        }
      ];

      gracefulDegradationService.degradationHistory = events;

      const stats = gracefulDegradationService.getDegradationStatistics();

      expect(stats.totalEvents).toBe(3);
      expect(stats.recentEvents).toBe(3);
      expect(stats.degradationFrequency).toBe(2); // 2 degradation events (not recovery)
    });

    test('should calculate uptime percentage', () => {
      // Mock degradation history with known downtime
      const now = Date.now();
      gracefulDegradationService.degradationHistory = [
        {
          timestamp: new Date(now - 2 * 60 * 60 * 1000), // 2 hours ago - start degradation
          newLevel: 'partial'
        },
        {
          timestamp: new Date(now - 1 * 60 * 60 * 1000), // 1 hour ago - recover
          newLevel: 'none'
        }
      ];

      const uptime = gracefulDegradationService.calculateUptimePercentage();

      // Should be approximately 95.83% (1 hour downtime out of 24 hours)
      expect(uptime).toBeGreaterThan(95);
      expect(uptime).toBeLessThan(96);
    });
  });

  describe('Force Degradation', () => {
    test('should force degradation level and trigger communications', async () => {
      realTimeScalabilityService.broadcast = jest.fn().mockResolvedValue();
      
      await gracefulDegradationService.forceDegradationLevel('severe');

      expect(gracefulDegradationService.degradationLevel).toBe('severe');
      expect(gracefulDegradationService.degradationHistory).toHaveLength(1);
      
      // Verify communication was triggered
      expect(realTimeScalabilityService.broadcast).toHaveBeenCalled();
    });
  });

  describe('Message Generation', () => {
    test('should generate appropriate admin notification messages', () => {
      const degradationEvent = {
        newLevel: 'severe',
        affectedFeatures: {
          disabled: ['analytics', 'reporting'],
          enabled: [],
          limited: []
        },
        serviceStatus: {
          redis: 'unhealthy',
          blockchain: 'healthy',
          database: 'healthy',
          external: 'degraded'
        }
      };

      const message = gracefulDegradationService.getAdminNotificationMessage(degradationEvent);

      expect(message).toContain('SEVERE');
      expect(message).toContain('analytics, reporting');
      expect(message).toContain('redis, external');
    });

    test('should generate user-friendly notification messages', () => {
      const degradationEvent = {
        newLevel: 'partial',
        affectedFeatures: {
          disabled: ['realTimeUpdates'],
          enabled: [],
          limited: []
        }
      };

      const message = gracefulDegradationService.getUserNotificationMessage(degradationEvent);

      expect(message).toContain('temporarily limited');
      expect(message).toContain('real-time updates');
    });
  });

  describe('Error Handling', () => {
    test('should handle communication service failures gracefully', async () => {
      // Mock service failure
      realTimeScalabilityService.broadcast = jest.fn().mockRejectedValue(new Error('Service unavailable'));
      realTimeScalabilityService.publishEvent = jest.fn().mockRejectedValue(new Error('Service unavailable'));

      // Should not throw error
      await expect(
        gracefulDegradationService.handleDegradationLevelChange('none', 'severe')
      ).resolves.not.toThrow();

      // Event should still be recorded
      expect(gracefulDegradationService.degradationHistory).toHaveLength(1);
    });

    test('should handle invalid degradation levels', async () => {
      await expect(
        gracefulDegradationService.forceDegradationLevel('invalid')
      ).resolves.not.toThrow();

      // Should maintain previous valid level
      expect(['none', 'partial', 'severe']).toContain(gracefulDegradationService.degradationLevel);
    });
  });
});
