const request = require('supertest');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const app = require('../../server');
const User = require('../../models/User');
const authService = require('../../services/authService');
const { getRedisClient } = require('../../config/redis');

describe('Authentication & Authorization Hardening Tests', () => {
  let testUser;
  let adminUser;
  let accessToken;
  let refreshToken;
  let redisClient;

  beforeAll(async () => {
    redisClient = getRedisClient();
  });

  beforeEach(async () => {
    // Create test user
    testUser = await global.createTestUser({
      email: '<EMAIL>',
      password: 'SuperSecurePassword123!@#',
      role: 'user'
    });

    // Create admin user
    adminUser = await global.createTestUser({
      username: 'adminuser',
      email: '<EMAIL>',
      password: 'AdminSecurePassword123!@#',
      phone: '+254700000002',
      role: 'admin'
    });

    // Generate tokens for test user
    const tokens = await authService.generateTokens(testUser._id);
    accessToken = tokens.accessToken;
    refreshToken = tokens.refreshToken;
  });

  afterEach(async () => {
    // Clean up Redis sessions
    if (redisClient && redisClient.isReady) {
      const keys = await redisClient.keys('*');
      if (keys.length > 0) {
        await redisClient.del(keys);
      }
    }
  });

  describe('Enhanced JWT Security', () => {
    test('should generate secure access and refresh tokens', async () => {
      const tokens = await authService.generateTokens(testUser._id);
      
      expect(tokens.accessToken).toBeDefined();
      expect(tokens.refreshToken).toBeDefined();
      expect(tokens.sessionId).toBeDefined();
      expect(tokens.expiresIn).toBe('15m');

      // Verify token structure
      const decoded = jwt.decode(tokens.accessToken);
      expect(decoded.userId).toBe(testUser._id.toString());
      expect(decoded.sessionId).toBeDefined();
      expect(decoded.type).toBe('access');
      expect(decoded.iss).toBe('kryptopesa.com');
      expect(decoded.aud).toBe('kryptopesa-users');
    });

    test('should validate access tokens properly', async () => {
      const decoded = await authService.verifyAccessToken(accessToken);
      
      expect(decoded.userId).toBe(testUser._id.toString());
      expect(decoded.type).toBe('access');
    });

    test('should reject invalid tokens', async () => {
      const invalidToken = 'invalid.token.here';
      
      await expect(authService.verifyAccessToken(invalidToken))
        .rejects.toThrow('Invalid token');
    });

    test('should reject expired tokens', async () => {
      const expiredToken = jwt.sign(
        { userId: testUser._id, type: 'access' },
        process.env.JWT_SECRET,
        { expiresIn: '-1h' }
      );

      await expect(authService.verifyAccessToken(expiredToken))
        .rejects.toThrow('Token expired');
    });

    test('should refresh access tokens using refresh token', async () => {
      const newTokens = await authService.refreshAccessToken(refreshToken);
      
      expect(newTokens.accessToken).toBeDefined();
      expect(newTokens.expiresIn).toBe('15m');
      expect(newTokens.accessToken).not.toBe(accessToken);
    });

    test('should reject invalid refresh tokens', async () => {
      const invalidRefreshToken = 'invalid.refresh.token';
      
      await expect(authService.refreshAccessToken(invalidRefreshToken))
        .rejects.toThrow('Invalid refresh token');
    });
  });

  describe('Session Management', () => {
    test('should store and validate sessions in Redis', async () => {
      if (!redisClient || !redisClient.isReady) {
        return; // Skip if Redis not available
      }

      const tokens = await authService.generateTokens(testUser._id);
      const decoded = jwt.decode(tokens.accessToken);
      
      const sessionKey = `session:${testUser._id}:${decoded.sessionId}`;
      const sessionData = await redisClient.get(sessionKey);
      
      expect(sessionData).toBeDefined();
      const session = JSON.parse(sessionData);
      expect(session.userId).toBe(testUser._id.toString());
    });

    test('should revoke refresh tokens on logout', async () => {
      const tokens = await authService.generateTokens(testUser._id);
      const decoded = jwt.decode(tokens.accessToken);
      
      await authService.revokeRefreshToken(testUser._id, decoded.sessionId);
      
      await expect(authService.refreshAccessToken(tokens.refreshToken))
        .rejects.toThrow('Invalid refresh token');
    });

    test('should revoke all user sessions', async () => {
      const tokens1 = await authService.generateTokens(testUser._id);
      const tokens2 = await authService.generateTokens(testUser._id);
      
      await authService.revokeAllUserSessions(testUser._id);
      
      await expect(authService.refreshAccessToken(tokens1.refreshToken))
        .rejects.toThrow('Invalid refresh token');
      await expect(authService.refreshAccessToken(tokens2.refreshToken))
        .rejects.toThrow('Invalid refresh token');
    });

    test('should get active user sessions', async () => {
      await authService.generateTokens(testUser._id);
      await authService.generateTokens(testUser._id);
      
      const sessions = await authService.getUserSessions(testUser._id);
      expect(sessions).toHaveLength(2);
      expect(sessions[0].userId).toBe(testUser._id.toString());
    });
  });

  describe('Password Security', () => {
    test('should enforce strong password policy', () => {
      const weakPasswords = [
        'password',
        '12345678',
        'Password1',
        'short',
        'NoNumbers!',
        'nonumbers123!',
        'NOLOWERCASE123!',
        'NoSpecialChars123'
      ];

      weakPasswords.forEach(password => {
        const validation = authService.validatePassword(password);
        expect(validation.isValid).toBe(false);
        expect(validation.errors.length).toBeGreaterThan(0);
      });
    });

    test('should accept strong passwords', () => {
      const strongPasswords = [
        'SuperSecurePassword123!@#',
        'MyVeryStr0ng!Password',
        'C0mpl3x!P@ssw0rd#2024'
      ];

      strongPasswords.forEach(password => {
        const validation = authService.validatePassword(password);
        expect(validation.isValid).toBe(true);
        expect(validation.errors).toHaveLength(0);
      });
    });

    test('should hash passwords securely', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await authService.hashPassword(password);
      
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(50);
    });
  });

  describe('Two-Factor Authentication', () => {
    test('should generate 2FA secret and QR code', async () => {
      const twoFactorData = await authService.generate2FASecret(testUser._id, testUser.email);
      
      expect(twoFactorData.secret).toBeDefined();
      expect(twoFactorData.qrCode).toBeDefined();
      expect(twoFactorData.manualEntryKey).toBeDefined();
      expect(twoFactorData.qrCode).toMatch(/^data:image\/png;base64,/);
    });

    test('should verify 2FA tokens correctly', () => {
      const secret = 'JBSWY3DPEHPK3PXP';
      const token = speakeasy.totp({
        secret,
        encoding: 'base32'
      });

      const isValid = authService.verify2FAToken(secret, token);
      expect(isValid).toBe(true);
    });

    test('should reject invalid 2FA tokens', () => {
      const secret = 'JBSWY3DPEHPK3PXP';
      const invalidToken = '000000';

      const isValid = authService.verify2FAToken(secret, invalidToken);
      expect(isValid).toBe(false);
    });
  });

  describe('API Authentication Endpoints', () => {
    test('should login with enhanced token response', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: testUser.email,
          password: 'SuperSecurePassword123!@#'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.accessToken).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
      expect(response.body.data.expiresIn).toBe('15m');
      expect(response.body.data.requiresTwoFactor).toBe(false);
    });

    test('should refresh tokens via API', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({
          refreshToken
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.accessToken).toBeDefined();
      expect(response.body.data.expiresIn).toBe('15m');
    });

    test('should logout and invalidate session', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify refresh token is invalidated
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken });

      expect(refreshResponse.status).toBe(401);
    });

    test('should setup 2FA', async () => {
      const response = await request(app)
        .post('/api/auth/2fa/setup')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.secret).toBeDefined();
      expect(response.body.data.qrCode).toBeDefined();
    });

    test('should verify and enable 2FA', async () => {
      const secret = 'JBSWY3DPEHPK3PXP';
      const token = speakeasy.totp({
        secret,
        encoding: 'base32'
      });

      const response = await request(app)
        .post('/api/auth/2fa/verify')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          secret,
          token
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify 2FA is enabled in database
      const updatedUser = await User.findById(testUser._id);
      expect(updatedUser.security.twoFactorEnabled).toBe(true);
    });
  });

  describe('Role-Based Access Control', () => {
    test('should allow admin access to admin routes', async () => {
      const adminTokens = await authService.generateTokens(adminUser._id);
      
      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${adminTokens.accessToken}`);

      expect(response.status).toBe(200);
    });

    test('should deny user access to admin routes', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(403);
      expect(response.body.code).toBe('INSUFFICIENT_PRIVILEGES');
    });

    test('should require authentication for protected routes', async () => {
      const response = await request(app)
        .get('/api/auth/me');

      expect(response.status).toBe(401);
      expect(response.body.code).toBe('MISSING_TOKEN');
    });
  });

  describe('Account Security', () => {
    test('should lock account after failed login attempts', async () => {
      // Simulate 5 failed login attempts
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({
            identifier: testUser.email,
            password: 'wrongpassword'
          });
      }

      // Verify account is locked
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: testUser.email,
          password: 'SuperSecurePassword123!@#'
        });

      expect(response.status).toBe(423);
      expect(response.body.message).toContain('locked');
    });

    test('should prevent access for inactive accounts', async () => {
      // Deactivate user account
      testUser.status = 'suspended';
      await testUser.save();

      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(403);
      expect(response.body.code).toBe('ACCOUNT_INACTIVE');
    });
  });
});
