/**
 * Service Layer Performance Optimization
 * Comprehensive performance enhancements for all service operations including
 * distributed caching, query optimization, connection pooling, and price lookup optimization
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');
const { AppError } = require('../middleware/errorHandler');
const priceService = require('./priceService');

class ServiceLayerPerformanceService {
  constructor() {
    this.redisClient = null;
    this.performanceMetrics = {
      totalOperations: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      slowOperations: 0,
      errorRate: 0,
      priceApiCalls: 0,
      priceApiErrors: 0,
      queryOptimizations: 0,
      lastReset: new Date()
    };
    
    this.distributedCache = new Map();
    this.queryOptimizations = new Map();
    this.connectionPools = new Map();
    this.priceCache = new Map();
    this.bulkOperationQueue = new Map();
    
    // Performance thresholds
    this.thresholds = {
      slowQuery: 500, // ms
      cacheExpiry: 300, // seconds
      priceExpiry: 60, // seconds for price data
      bulkBatchSize: 100,
      maxCacheSize: 10000
    };
    
    this.initialize();
  }

  /**
   * Initialize performance optimization service
   */
  async initialize() {
    try {
      this.redisClient = getRedisClient();
      
      if (this.redisClient) {
        logger.info('Service layer performance optimization initialized with Redis');
        
        // Start background processes
        this.startCacheCleanup();
        this.startBulkOperationProcessor();
        this.startMetricsCollection();
      } else {
        logger.warn('Redis not available, using memory-only caching');
      }
      
      // Initialize query optimizations
      this.setupQueryOptimizations();
      
      return true;
    } catch (error) {
      logger.error('Failed to initialize service layer performance:', error);
      return false;
    }
  }

  /**
   * Distributed caching with Redis fallback to memory
   */
  async setCache(key, value, ttl = this.thresholds.cacheExpiry) {
    try {
      const serializedValue = JSON.stringify({
        data: value,
        timestamp: Date.now(),
        ttl: ttl * 1000
      });

      // Try Redis first
      if (this.redisClient && this.redisClient.isReady) {
        await this.redisClient.setex(key, ttl, serializedValue);
      }

      // Always store in memory as fallback
      this.distributedCache.set(key, {
        data: value,
        timestamp: Date.now(),
        ttl: ttl * 1000
      });

      // Cleanup memory cache if too large
      if (this.distributedCache.size > this.thresholds.maxCacheSize) {
        this.cleanupMemoryCache();
      }

      return true;
    } catch (error) {
      logger.error('Error setting cache:', error);
      return false;
    }
  }

  /**
   * Get from distributed cache
   */
  async getCache(key) {
    try {
      // Try Redis first
      if (this.redisClient && this.redisClient.isReady) {
        const cached = await this.redisClient.get(key);
        if (cached) {
          const parsed = JSON.parse(cached);
          if (Date.now() - parsed.timestamp < parsed.ttl) {
            this.performanceMetrics.cacheHits++;
            return parsed.data;
          }
        }
      }

      // Fallback to memory cache
      const memoryCached = this.distributedCache.get(key);
      if (memoryCached && Date.now() - memoryCached.timestamp < memoryCached.ttl) {
        this.performanceMetrics.cacheHits++;
        return memoryCached.data;
      }

      this.performanceMetrics.cacheMisses++;
      return null;
    } catch (error) {
      logger.error('Error getting cache:', error);
      this.performanceMetrics.cacheMisses++;
      return null;
    }
  }

  /**
   * Optimized price lookup with aggressive caching
   */
  async getOptimizedPrices(cryptos, currencies = ['USD']) {
    const cacheKey = `prices:${JSON.stringify(cryptos)}:${JSON.stringify(currencies)}`;
    
    try {
      // Check cache first
      const cached = await this.getCache(cacheKey);
      if (cached) {
        return cached;
      }

      // Fetch from price service
      const startTime = Date.now();
      const prices = await priceService.getPrices(cryptos, currencies);
      const responseTime = Date.now() - startTime;

      this.performanceMetrics.priceApiCalls++;
      this.updateResponseTime(responseTime);

      // Cache with shorter TTL for price data
      await this.setCache(cacheKey, prices, this.thresholds.priceExpiry);

      return prices;
    } catch (error) {
      this.performanceMetrics.priceApiErrors++;
      logger.error('Error in optimized price lookup:', error);
      throw error;
    }
  }

  /**
   * Bulk operation optimization
   */
  async addToBulkQueue(operation, data) {
    const queueKey = `bulk:${operation}`;
    
    if (!this.bulkOperationQueue.has(queueKey)) {
      this.bulkOperationQueue.set(queueKey, []);
    }
    
    this.bulkOperationQueue.get(queueKey).push({
      data,
      timestamp: Date.now()
    });

    // Process if batch size reached
    if (this.bulkOperationQueue.get(queueKey).length >= this.thresholds.bulkBatchSize) {
      await this.processBulkQueue(queueKey);
    }
  }

  /**
   * Process bulk operations
   */
  async processBulkQueue(queueKey) {
    const queue = this.bulkOperationQueue.get(queueKey);
    if (!queue || queue.length === 0) return;

    try {
      const operation = queueKey.replace('bulk:', '');
      const batchData = queue.splice(0, this.thresholds.bulkBatchSize);

      switch (operation) {
        case 'balance_update':
          await this.processBulkBalanceUpdates(batchData);
          break;
        case 'trade_status':
          await this.processBulkTradeStatusUpdates(batchData);
          break;
        case 'user_activity':
          await this.processBulkUserActivityUpdates(batchData);
          break;
        default:
          logger.warn(`Unknown bulk operation: ${operation}`);
      }

      logger.info(`Processed bulk operation: ${operation}, batch size: ${batchData.length}`);
    } catch (error) {
      logger.error(`Error processing bulk queue ${queueKey}:`, error);
    }
  }

  /**
   * Optimized aggregation queries
   */
  async getOptimizedAggregation(collection, pipeline, cacheKey, ttl = this.thresholds.cacheExpiry) {
    try {
      // Check cache first
      const cached = await this.getCache(cacheKey);
      if (cached) {
        return cached;
      }

      const startTime = Date.now();
      
      // Execute optimized aggregation
      const result = await mongoose.model(collection).aggregate([
        // Add index hints for better performance
        { $hint: this.getOptimalIndex(collection, pipeline) },
        ...pipeline
      ]);

      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      this.performanceMetrics.queryOptimizations++;

      // Cache result
      await this.setCache(cacheKey, result, ttl);

      return result;
    } catch (error) {
      logger.error('Error in optimized aggregation:', error);
      throw error;
    }
  }

  /**
   * Connection pool optimization
   */
  getOptimizedConnection(database = 'mongodb') {
    if (!this.connectionPools.has(database)) {
      this.connectionPools.set(database, this.createOptimizedPool(database));
    }
    return this.connectionPools.get(database);
  }

  /**
   * Create optimized connection pool
   */
  createOptimizedPool(database) {
    const poolConfig = {
      maxPoolSize: process.env.NODE_ENV === 'production' ? 100 : 20,
      minPoolSize: process.env.NODE_ENV === 'production' ? 10 : 5,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferCommands: false,
      retryWrites: true,
      readPreference: 'secondaryPreferred'
    };

    logger.info(`Created optimized connection pool for ${database}:`, poolConfig);
    return poolConfig;
  }

  /**
   * Query optimization setup
   */
  setupQueryOptimizations() {
    // Common query patterns and their optimal indexes
    this.queryOptimizations.set('Trade', {
      'status_user': { status: 1, buyer: 1, seller: 1 },
      'created_date': { createdAt: -1 },
      'amount_range': { 'cryptocurrency.amount': 1 },
      'location': { 'offer.location': 1 }
    });

    this.queryOptimizations.set('Offer', {
      'active_type': { status: 1, type: 1 },
      'crypto_fiat': { cryptocurrency: 1, fiatCurrency: 1 },
      'price_range': { price: 1 },
      'location': { location: 1 }
    });

    this.queryOptimizations.set('User', {
      'reputation': { 'reputation.score': -1 },
      'location': { 'profile.location.country': 1 },
      'verification': { 'verification.status': 1 }
    });

    logger.info('Query optimizations configured');
  }

  /**
   * Get optimal index for query
   */
  getOptimalIndex(collection, pipeline) {
    const optimizations = this.queryOptimizations.get(collection);
    if (!optimizations) return {};

    // Analyze pipeline to determine best index
    const matchStage = pipeline.find(stage => stage.$match);
    if (!matchStage) return {};

    const matchFields = Object.keys(matchStage.$match);
    
    // Find best matching index
    for (const [indexName, indexSpec] of Object.entries(optimizations)) {
      const indexFields = Object.keys(indexSpec);
      if (matchFields.some(field => indexFields.includes(field))) {
        return indexSpec;
      }
    }

    return {};
  }

  /**
   * Update response time metrics
   */
  updateResponseTime(responseTime) {
    this.performanceMetrics.totalOperations++;
    
    // Update average response time
    const totalTime = this.performanceMetrics.averageResponseTime * (this.performanceMetrics.totalOperations - 1) + responseTime;
    this.performanceMetrics.averageResponseTime = totalTime / this.performanceMetrics.totalOperations;

    // Track slow operations
    if (responseTime > this.thresholds.slowQuery) {
      this.performanceMetrics.slowOperations++;
      logger.warn(`Slow operation detected: ${responseTime}ms`);
    }
  }

  /**
   * Cleanup memory cache
   */
  cleanupMemoryCache() {
    const now = Date.now();
    const keysToDelete = [];

    for (const [key, value] of this.distributedCache.entries()) {
      if (now - value.timestamp > value.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.distributedCache.delete(key));
    
    // If still too large, remove oldest entries
    if (this.distributedCache.size > this.thresholds.maxCacheSize) {
      const entries = Array.from(this.distributedCache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, Math.floor(this.thresholds.maxCacheSize * 0.2));
      toRemove.forEach(([key]) => this.distributedCache.delete(key));
    }

    logger.debug(`Cleaned up memory cache, removed ${keysToDelete.length} expired entries`);
  }

  /**
   * Start cache cleanup process
   */
  startCacheCleanup() {
    setInterval(() => {
      this.cleanupMemoryCache();
    }, 60000); // Every minute

    logger.info('Cache cleanup process started');
  }

  /**
   * Start bulk operation processor
   */
  startBulkOperationProcessor() {
    setInterval(async () => {
      for (const queueKey of this.bulkOperationQueue.keys()) {
        await this.processBulkQueue(queueKey);
      }
    }, 5000); // Every 5 seconds

    logger.info('Bulk operation processor started');
  }

  /**
   * Start metrics collection
   */
  startMetricsCollection() {
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, 30000); // Every 30 seconds

    logger.info('Metrics collection started');
  }

  /**
   * Process bulk balance updates
   */
  async processBulkBalanceUpdates(batchData) {
    try {
      const bulkOps = batchData.map(item => ({
        updateOne: {
          filter: { user: item.data.userId },
          update: {
            $set: {
              [`balances.${item.data.currency}`]: item.data.balance,
              lastUpdated: new Date()
            }
          }
        }
      }));

      await mongoose.model('Wallet').bulkWrite(bulkOps);
      logger.info(`Processed ${bulkOps.length} bulk balance updates`);
    } catch (error) {
      logger.error('Error processing bulk balance updates:', error);
    }
  }

  /**
   * Process bulk trade status updates
   */
  async processBulkTradeStatusUpdates(batchData) {
    try {
      const bulkOps = batchData.map(item => ({
        updateOne: {
          filter: { _id: item.data.tradeId },
          update: {
            $set: {
              status: item.data.status,
              updatedAt: new Date()
            }
          }
        }
      }));

      await mongoose.model('Trade').bulkWrite(bulkOps);
      logger.info(`Processed ${bulkOps.length} bulk trade status updates`);
    } catch (error) {
      logger.error('Error processing bulk trade status updates:', error);
    }
  }

  /**
   * Process bulk user activity updates
   */
  async processBulkUserActivityUpdates(batchData) {
    try {
      const bulkOps = batchData.map(item => ({
        updateOne: {
          filter: { _id: item.data.userId },
          update: {
            $set: {
              lastActive: new Date(),
              isOnline: item.data.isOnline
            }
          }
        }
      }));

      await mongoose.model('User').bulkWrite(bulkOps);
      logger.info(`Processed ${bulkOps.length} bulk user activity updates`);
    } catch (error) {
      logger.error('Error processing bulk user activity updates:', error);
    }
  }

  /**
   * Collect performance metrics
   */
  collectPerformanceMetrics() {
    const metrics = {
      ...this.performanceMetrics,
      cacheHitRate: this.performanceMetrics.totalOperations > 0
        ? (this.performanceMetrics.cacheHits / this.performanceMetrics.totalOperations * 100).toFixed(2)
        : 0,
      errorRate: this.performanceMetrics.totalOperations > 0
        ? (this.performanceMetrics.priceApiErrors / this.performanceMetrics.totalOperations * 100).toFixed(2)
        : 0,
      slowOperationRate: this.performanceMetrics.totalOperations > 0
        ? (this.performanceMetrics.slowOperations / this.performanceMetrics.totalOperations * 100).toFixed(2)
        : 0,
      memoryCacheSize: this.distributedCache.size,
      bulkQueueSizes: Object.fromEntries(
        Array.from(this.bulkOperationQueue.entries()).map(([key, queue]) => [key, queue.length])
      )
    };

    // Log metrics if there are performance issues
    if (metrics.slowOperationRate > 5 || metrics.errorRate > 1) {
      logger.warn('Performance issues detected:', metrics);
    }

    return metrics;
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    return this.collectPerformanceMetrics();
  }

  /**
   * Reset performance metrics
   */
  resetMetrics() {
    this.performanceMetrics = {
      totalOperations: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      slowOperations: 0,
      errorRate: 0,
      priceApiCalls: 0,
      priceApiErrors: 0,
      queryOptimizations: 0,
      lastReset: new Date()
    };

    logger.info('Performance metrics reset');
  }

  /**
   * Optimize database indexes
   */
  async optimizeIndexes() {
    try {
      const collections = ['Trade', 'Offer', 'User', 'Wallet', 'Message'];

      for (const collectionName of collections) {
        const model = mongoose.model(collectionName);
        const indexes = this.queryOptimizations.get(collectionName);

        if (indexes) {
          for (const [indexName, indexSpec] of Object.entries(indexes)) {
            try {
              await model.collection.createIndex(indexSpec, {
                name: `${collectionName.toLowerCase()}_${indexName}`,
                background: true
              });
              logger.info(`Created index ${indexName} for ${collectionName}`);
            } catch (error) {
              if (error.code !== 85) { // Index already exists
                logger.error(`Error creating index ${indexName} for ${collectionName}:`, error);
              }
            }
          }
        }
      }

      logger.info('Database index optimization completed');
    } catch (error) {
      logger.error('Error optimizing indexes:', error);
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    try {
      // Process remaining bulk operations
      for (const queueKey of this.bulkOperationQueue.keys()) {
        await this.processBulkQueue(queueKey);
      }

      // Clear caches
      this.distributedCache.clear();
      this.bulkOperationQueue.clear();

      logger.info('Service layer performance optimization shutdown completed');
    } catch (error) {
      logger.error('Error during shutdown:', error);
    }
  }
}

// Create singleton instance
const serviceLayerPerformance = new ServiceLayerPerformanceService();

module.exports = {
  ServiceLayerPerformanceService,
  serviceLayerPerformance
};
