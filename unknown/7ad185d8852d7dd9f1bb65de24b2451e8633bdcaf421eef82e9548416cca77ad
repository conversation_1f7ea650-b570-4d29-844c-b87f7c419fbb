const logger = require('./utils/logger');

/**
 * Error Boundary Component for Admin Dashboard
 * Catches and handles React errors gracefully
 */

import React from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  AlertTitle,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip
} from '@mui/material';
import {
  ErrorOutline,
  Refresh,
  BugReport,
  ExpandMore,
  Home
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import performanceMonitor from '../utils/performanceMonitor';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    this.setState({
      error,
      errorInfo
    });

    // Record error in performance monitor
    performanceMonitor.recordError(error, {
      context: 'error_boundary',
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
      retryCount: this.state.retryCount
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      // logger.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // Send error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo);
    }
  }

  reportError = async (error, errorInfo) => {
    try {
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        errorId: this.state.errorId,
        retryCount: this.state.retryCount,
        userId: localStorage.getItem('admin_user_id'),
        sessionId: sessionStorage.getItem('session_id')
      };

      // Send to error reporting service
      await fetch('/api/admin/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify(errorReport)
      });
    } catch (reportingError) {
      // logger.error('Failed to report error:', reportingError);
    }
  };

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
    window.location.href = '/dashboard';
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          errorId={this.state.errorId}
          retryCount={this.state.retryCount}
          onRetry={this.handleRetry}
          onReload={this.handleReload}
          onGoHome={this.handleGoHome}
        />
      );
    }

    return this.props.children;
  }
}

const ErrorFallback = ({
  error,
  errorInfo,
  errorId,
  retryCount,
  onRetry,
  onReload,
  onGoHome
}) => {
  const navigate = useNavigate();

  const getErrorSeverity = (error) => {
    const criticalPatterns = [
      /network error/i,
      /unauthorized/i,
      /forbidden/i,
      /internal server error/i
    ];

    const isCritical = criticalPatterns.some(pattern =>
      pattern.test(error?.message || '')
    );

    return isCritical ? 'error' : 'warning';
  };

  const getErrorCategory = (error) => {
    const message = error?.message || '';
    
    if (message.includes('ChunkLoadError') || message.includes('Loading chunk')) {
      return 'chunk_load';
    }
    if (message.includes('Network Error') || message.includes('fetch')) {
      return 'network';
    }
    if (message.includes('Cannot read property') || message.includes('undefined')) {
      return 'runtime';
    }
    if (message.includes('Permission') || message.includes('Unauthorized')) {
      return 'permission';
    }
    
    return 'unknown';
  };

  const getErrorSuggestion = (category) => {
    const suggestions = {
      chunk_load: 'This appears to be a loading issue. Try refreshing the page.',
      network: 'Check your internet connection and try again.',
      runtime: 'This is a code execution error. Please report this issue.',
      permission: 'You may not have permission for this action. Try logging in again.',
      unknown: 'An unexpected error occurred. Please try refreshing the page.'
    };

    return suggestions[category] || suggestions.unknown;
  };

  const severity = getErrorSeverity(error);
  const category = getErrorCategory(error);
  const suggestion = getErrorSuggestion(category);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        p: 3
      }}
    >
      <Paper
        elevation={3}
        sx={{
          maxWidth: 600,
          width: '100%',
          p: 4,
          textAlign: 'center'
        }}
      >
        <ErrorOutline
          sx={{
            fontSize: 64,
            color: severity === 'error' ? 'error.main' : 'warning.main',
            mb: 2
          }}
        />

        <Typography variant="h4" gutterBottom>
          Oops! Something went wrong
        </Typography>

        <Typography variant="body1" color="text.secondary" paragraph>
          We're sorry, but an unexpected error occurred in the admin dashboard.
        </Typography>

        <Alert severity={severity} sx={{ mb: 3, textAlign: 'left' }}>
          <AlertTitle>Error Details</AlertTitle>
          {suggestion}
        </Alert>

        <Box sx={{ display: 'flex', gap: 1, mb: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Chip
            label={`Category: ${category}`}
            size="small"
            variant="outlined"
          />
          <Chip
            label={`Error ID: ${errorId}`}
            size="small"
            variant="outlined"
          />
          {retryCount > 0 && (
            <Chip
              label={`Retry: ${retryCount}`}
              size="small"
              variant="outlined"
              color="warning"
            />
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mb: 3, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={onRetry}
            disabled={retryCount >= 3}
          >
            Try Again
          </Button>

          <Button
            variant="outlined"
            startIcon={<Home />}
            onClick={onGoHome}
          >
            Go to Dashboard
          </Button>

          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={onReload}
          >
            Reload Page
          </Button>
        </Box>

        {process.env.NODE_ENV === 'development' && error && (
          <Accordion sx={{ textAlign: 'left' }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle2">
                <BugReport sx={{ mr: 1, verticalAlign: 'middle' }} />
                Developer Information
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="subtitle2" gutterBottom>
                Error Message:
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: 'monospace',
                  bgcolor: 'grey.100',
                  p: 1,
                  borderRadius: 1,
                  mb: 2,
                  wordBreak: 'break-all'
                }}
              >
                {error.message}
              </Typography>

              {error.stack && (
                <>
                  <Typography variant="subtitle2" gutterBottom>
                    Stack Trace:
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontFamily: 'monospace',
                      bgcolor: 'grey.100',
                      p: 1,
                      borderRadius: 1,
                      mb: 2,
                      whiteSpace: 'pre-wrap',
                      fontSize: '0.75rem',
                      maxHeight: 200,
                      overflow: 'auto'
                    }}
                  >
                    {error.stack}
                  </Typography>
                </>
              )}

              {errorInfo?.componentStack && (
                <>
                  <Typography variant="subtitle2" gutterBottom>
                    Component Stack:
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontFamily: 'monospace',
                      bgcolor: 'grey.100',
                      p: 1,
                      borderRadius: 1,
                      whiteSpace: 'pre-wrap',
                      fontSize: '0.75rem',
                      maxHeight: 200,
                      overflow: 'auto'
                    }}
                  >
                    {errorInfo.componentStack}
                  </Typography>
                </>
              )}
            </AccordionDetails>
          </Accordion>
        )}

        <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
          If this problem persists, please contact the system administrator.
        </Typography>
      </Paper>
    </Box>
  );
};

export default ErrorBoundary;
