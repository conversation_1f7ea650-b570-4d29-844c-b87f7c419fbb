{"version": 3, "file": "static/js/191.2e755496.chunk.js", "mappings": "ggBAIA,SAAeA,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,8BACD,S,iCCoCJ,MAieA,EAjemBC,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACvB,MAAOC,EAAQC,KAAaC,EAAAA,EAAAA,UAAS,KAC9BC,GAASC,KAAcF,EAAAA,EAAAA,WAAS,IAChCG,GAAOC,KAAYJ,EAAAA,EAAAA,UAAS,OAC5BK,GAAMC,KAAWN,EAAAA,EAAAA,UAAS,IAC1BO,GAAaC,KAAkBR,EAAAA,EAAAA,UAAS,KACxCS,GAAaC,KAAkBV,EAAAA,EAAAA,UAAS,IACxCW,GAAYC,KAAiBZ,EAAAA,EAAAA,UAAS,KACtCa,GAAcC,KAAmBd,EAAAA,EAAAA,UAAS,KAC1Ce,GAAYC,KAAiBhB,EAAAA,EAAAA,UAAS,KACtCiB,GAAcC,KAAmBlB,EAAAA,EAAAA,UAAS,KAC1CmB,GAAeC,KAAoBpB,EAAAA,EAAAA,UAAS,OAC5CqB,GAAiBC,KAAsBtB,EAAAA,EAAAA,WAAS,GAGjDuB,GAAcC,UAClB,IACEtB,IAAW,GACX,MAAMuB,EAAS,CACbpB,KAAMA,GAAO,EACbqB,MAAOnB,GACPoB,OAAQhB,SAAciB,EACtBC,OAAQhB,SAAgBe,EACxBE,KAAMf,SAAca,EACpBG,eAAgBd,SAAgBW,EAChCI,OAAQ,YACRC,UAAW,QAGPC,QAAiBC,EAAAA,EAAMC,IAAI,gBAAiB,CAAEX,WAEhDS,EAASG,KAAKC,UAChBvC,GAAUmC,EAASG,KAAKA,KAAKvC,QAC7BY,GAAewB,EAASG,KAAKA,KAAKE,WAAW9B,aAEjD,CAAE,MAAON,IACPqC,QAAQrC,MAAM,yBAA0BA,IACxCC,GAAS,yBACX,CAAC,QACCF,IAAW,EACb,IAGFuC,EAAAA,EAAAA,WAAU,KACRlB,MACC,CAAClB,GAAME,GAAaI,GAAYE,GAAcE,GAAYE,KAE7D,MAcMyB,GAAoBlB,MAAOmB,EAAOC,KACtC,WACyBT,EAAAA,EAAMU,IAAI,iBAADC,OAAkBH,EAAMI,QAAO,WAAW,CACxElB,OAAQe,EACRI,OAAO,SAADF,OAAWF,EAAM,cAGZP,KAAKC,SAChBf,IAEJ,CAAE,MAAOpB,IACPqC,QAAQrC,MAAM,iCAAkCA,GAClD,GAGI8C,GAAkBpB,IACtB,OAAQA,GACN,IAAK,SAAU,MAAO,UACtB,IAAK,SAAU,MAAO,UACtB,IAAK,WAAY,MAAO,QAExB,QAAS,MAAO,YAIdqB,GAAgBpB,GACJ,QAATA,EAAiB,UAAY,QAOhCqB,GAAeA,CAACC,EAAQC,IACtB,GAANP,OAAUQ,WAAWF,GAAQG,QAAQ,GAAE,KAAAT,OAAIO,GAGvCG,GAAcC,GACX,IAAIC,KAAKD,GAAYE,mBAAmB,QAAS,CACtDC,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,YAINC,GAAkBA,CAACC,EAAgBC,KACvC,IAAKA,EAAa,MAAO,MACzB,MAAMC,GAAWF,EAAiBC,GAAeA,EAAe,IAChE,MAAM,GAANrB,OAAUsB,EAAS,EAAI,IAAM,IAAEtB,OAAGsB,EAAOb,QAAQ,GAAE,MAGrD,OACEc,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,EACF3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,sBAKtCF,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAGC,GAAI,EAAEP,SAAA,EAChC3F,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAEX,UAC9B3F,EAAAA,EAAAA,KAACuG,EAAAA,EAAI,CAAAZ,UACHF,EAAAA,EAAAA,MAACe,EAAAA,EAAW,CAAAb,SAAA,EACV3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACa,MAAM,gBAAgBX,cAAY,EAAAH,SAAC,kBAG/C3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAIF,SACrB9D,GAAY6E,2BAKrB1G,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAEX,UAC9B3F,EAAAA,EAAAA,KAACuG,EAAAA,EAAI,CAAAZ,UACHF,EAAAA,EAAAA,MAACe,EAAAA,EAAW,CAAAb,SAAA,EACV3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACa,MAAM,gBAAgBX,cAAY,EAAAH,SAAC,mBAG/C3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKY,MAAM,eAAcd,SAC1CzE,EAAOyF,OAAOC,GAAkB,WAAbA,EAAE3D,QAAqB4D,iBAKnD7G,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAEX,UAC9B3F,EAAAA,EAAAA,KAACuG,EAAAA,EAAI,CAAAZ,UACHF,EAAAA,EAAAA,MAACe,EAAAA,EAAW,CAAAb,SAAA,EACV3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACa,MAAM,gBAAgBX,cAAY,EAAAH,SAAC,gBAG/C3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKY,MAAM,eAAcd,SAC1CzE,EAAOyF,OAAOC,GAAgB,QAAXA,EAAE1D,MAAgB2D,iBAK9C7G,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAEX,UAC9B3F,EAAAA,EAAAA,KAACuG,EAAAA,EAAI,CAAAZ,UACHF,EAAAA,EAAAA,MAACe,EAAAA,EAAW,CAAAb,SAAA,EACV3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACa,MAAM,gBAAgBX,cAAY,EAAAH,SAAC,iBAG/C3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKY,MAAM,aAAYd,SACxCzE,EAAOyF,OAAOC,GAAgB,SAAXA,EAAE1D,MAAiB2D,oBAQjD7G,EAAAA,EAAAA,KAAC8G,EAAAA,EAAK,CAACC,GAAI,CAAEC,EAAG,EAAGd,GAAI,GAAIP,UACzBF,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAGgB,WAAW,SAAQtB,SAAA,EAC7C3F,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,UACvB3F,EAAAA,EAAAA,KAACkH,EAAAA,EAAS,CACRC,WAAS,EACTC,YAAY,mBACZC,MAAOtF,GACPuF,SAAWC,GAAMvF,GAAcuF,EAAEC,OAAOH,OACxCI,WAAY,CACVC,gBACE1H,EAAAA,EAAAA,KAAC2H,EAAAA,EAAc,CAACC,SAAS,QAAOjC,UAC9B3F,EAAAA,EAAAA,KAAC6H,EAAAA,EAAU,YAMrB7H,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,UACvBF,EAAAA,EAAAA,MAACqC,EAAAA,EAAW,CAACX,WAAS,EAAAxB,SAAA,EACpB3F,EAAAA,EAAAA,KAAC+H,EAAAA,EAAU,CAAApC,SAAC,YACZF,EAAAA,EAAAA,MAACuC,EAAAA,EAAM,CACLX,MAAOpF,GACPqF,SAAWC,GAAMrF,GAAgBqF,EAAEC,OAAOH,OAC1CY,MAAM,SAAQtC,SAAA,EAEd3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,GAAE1B,SAAC,SACnB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,SAAQ1B,SAAC,YACzB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,SAAQ1B,SAAC,YACzB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,WAAU1B,SAAC,cAC3B3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,UAAS1B,SAAC,qBAIhC3F,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,UACvBF,EAAAA,EAAAA,MAACqC,EAAAA,EAAW,CAACX,WAAS,EAAAxB,SAAA,EACpB3F,EAAAA,EAAAA,KAAC+H,EAAAA,EAAU,CAAApC,SAAC,UACZF,EAAAA,EAAAA,MAACuC,EAAAA,EAAM,CACLX,MAAOlF,GACPmF,SAAWC,GAAMnF,GAAcmF,EAAEC,OAAOH,OACxCY,MAAM,OAAMtC,SAAA,EAEZ3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,GAAE1B,SAAC,SACnB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,MAAK1B,SAAC,SACtB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,OAAM1B,SAAC,kBAI7B3F,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,UACvBF,EAAAA,EAAAA,MAACqC,EAAAA,EAAW,CAACX,WAAS,EAAAxB,SAAA,EACpB3F,EAAAA,EAAAA,KAAC+H,EAAAA,EAAU,CAAApC,SAAC,YACZF,EAAAA,EAAAA,MAACuC,EAAAA,EAAM,CACLX,MAAOhF,GACPiF,SAAWC,GAAMjF,GAAgBiF,EAAEC,OAAOH,OAC1CY,MAAM,SAAQtC,SAAA,EAEd3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,GAAE1B,SAAC,SACnB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,OAAM1B,SAAC,UACvB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,OAAM1B,SAAC,UACvB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,MAAK1B,SAAC,SACtB3F,EAAAA,EAAAA,KAACkI,EAAAA,EAAQ,CAACb,MAAM,MAAK1B,SAAC,iBAI5B3F,EAAAA,EAAAA,KAAC+F,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,UACvB3F,EAAAA,EAAAA,KAACmI,EAAAA,EAAM,CACLtC,QAAQ,WACRuC,QAASA,KACPpG,GAAc,IACdE,GAAgB,IAChBE,GAAc,IACdE,GAAgB,KAChBqD,SACH,0BAQPF,EAAAA,EAAAA,MAAC4C,EAAAA,EAAc,CAACC,UAAWxB,EAAAA,EAAMnB,SAAA,EAC/BF,EAAAA,EAAAA,MAAC8C,EAAAA,EAAK,CAAA5C,SAAA,EACJ3F,EAAAA,EAAAA,KAACwI,EAAAA,EAAS,CAAA7C,UACRF,EAAAA,EAAAA,MAACgD,EAAAA,EAAQ,CAAA9C,SAAA,EACP3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,SAAC,cACX3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,SAAC,aACX3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,SAAC,UACX3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,SAAC,kBACX3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,SAAC,WACX3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,SAAC,YACX3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,SAAC,aACX3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,SAAC,kBAGf3F,EAAAA,EAAAA,KAAC2I,EAAAA,EAAS,CAAAhD,SACPzE,EAAO0H,IAAK7E,IAAK,IAAA8E,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAlLPzG,EAkLO,OAChBuC,EAAAA,EAAAA,MAACgD,EAAAA,EAAQ,CAAiBmB,OAAK,EAAAjE,SAAA,EAC7B3F,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,UACR3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,QAAQgE,WAAW,YAAWlE,SAC/C5B,EAAMI,aAGXnE,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,UACRF,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACoE,QAAQ,OAAO7C,WAAW,SAAS8C,IAAK,EAAEpE,SAAA,EAC7C3F,EAAAA,EAAAA,KAACgK,EAAAA,EAAM,CAACjD,GAAI,CAAEkD,MAAO,GAAIC,OAAQ,IAAKvE,SACtB,QADsBkD,EACnC9E,EAAMoG,eAAO,IAAAtB,GAAU,QAAVC,EAAbD,EAAeuB,gBAAQ,IAAAtB,OAAV,EAAbA,EAAyBuB,OAAO,GAAGC,iBAEtC7E,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,EACF3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SACX,QADWoD,EACxBhF,EAAMoG,eAAO,IAAApB,OAAA,EAAbA,EAAeqB,YAElB3E,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAACC,QAAQ,UAAUY,MAAM,gBAAed,SAAA,CAAC,SAC/B,QAAbqD,EAAAjF,EAAMoG,eAAO,IAAAnB,GAAY,QAAZC,EAAbD,EAAeuB,kBAAU,IAAAtB,OAAZ,EAAbA,EAA2BuB,QAAS,cAKlDxK,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,UACR3F,EAAAA,EAAAA,KAACyK,EAAAA,EAAI,CACHC,MA1MGxH,EA0Mea,EAAMb,KAzMxB,QAATA,GAAiBlD,EAAAA,EAAAA,KAAC2K,EAAAA,EAAc,KAAM3K,EAAAA,EAAAA,KAAC4K,EAAAA,EAAgB,KA0M9C3C,MAAOlE,EAAMb,KAAKoH,cAClB7D,MAAOnC,GAAaP,EAAMb,MAC1B2H,KAAK,aAGT7K,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,UACRF,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,EACFF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SAAA,CACxBpB,GAAiC,QAArB2E,EAACnF,EAAMZ,sBAAc,IAAA+F,OAAA,EAApBA,EAAsB4B,UAA+B,QAAtB3B,EAAEpF,EAAMZ,sBAAc,IAAAgG,OAAA,EAApBA,EAAsB4B,QAAQ,KAC5ExG,GAAiC,QAArB6E,EAACrF,EAAMZ,sBAAc,IAAAiG,OAAA,EAApBA,EAAsB4B,UAA+B,QAAtB3B,EAAEtF,EAAMZ,sBAAc,IAAAkG,OAAA,EAApBA,EAAsB0B,YAEvEtF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAACC,QAAQ,UAAUY,MAAM,gBAAed,SAAA,CAAC,cACtCpB,GAAiC,QAArB+E,EAACvF,EAAMZ,sBAAc,IAAAmG,OAAA,EAApBA,EAAsB2B,gBAAqC,QAAtB1B,EAAExF,EAAMZ,sBAAc,IAAAoG,OAAA,EAApBA,EAAsBwB,iBAI5F/K,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,UACRF,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,EACF3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SACxBpB,GAAuB,QAAXiF,EAACzF,EAAMmH,YAAI,IAAA1B,OAAA,EAAVA,EAAYlE,eAA0B,QAAZmE,EAAE1F,EAAMmH,YAAI,IAAAzB,OAAA,EAAVA,EAAYhF,aAExDgB,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAACC,QAAQ,UAAUY,MAAM,gBAAed,SAAA,CAAC,WACzCN,GAA0B,QAAXqE,EAAC3F,EAAMmH,YAAI,IAAAxB,OAAA,EAAVA,EAAYpE,eAA0B,QAAZqE,EAAE5F,EAAMmH,YAAI,IAAAvB,OAAA,EAAVA,EAAYpE,sBAIvEvF,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,UACR3F,EAAAA,EAAAA,KAACyK,EAAAA,EAAI,CACHxC,MAAOlE,EAAMd,OACbwD,MAAOpC,GAAeN,EAAMd,QAC5B4H,KAAK,aAGT7K,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,UACR3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SACxBf,GAAWb,EAAMoH,gBAGtBnL,EAAAA,EAAAA,KAAC0I,EAAAA,EAAS,CAAA/C,UACRF,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACoE,QAAQ,OAAOC,IAAK,EAAEpE,SAAA,EACzB3F,EAAAA,EAAAA,KAACoL,EAAAA,EAAO,CAACC,MAAM,eAAc1F,UAC3B3F,EAAAA,EAAAA,KAACsL,EAAAA,EAAU,CACTT,KAAK,QACLzC,QAASA,IAxRNrE,KACvBvB,GAAiBuB,GACjBrB,IAAmB,IAsRgB6I,CAAgBxH,GAAO4B,UAEtC3F,EAAAA,EAAAA,KAACwL,EAAAA,EAAQ,QAIK,WAAjBzH,EAAMd,SACLjD,EAAAA,EAAAA,KAACoL,EAAAA,EAAO,CAACC,MAAM,cAAa1F,UAC1B3F,EAAAA,EAAAA,KAACsL,EAAAA,EAAU,CACTT,KAAK,QACLpE,MAAM,UACN2B,QAASA,IAAMtE,GAAkBC,EAAO,UAAU4B,UAElD3F,EAAAA,EAAAA,KAACyL,EAAS,QAKE,WAAjB1H,EAAMd,SACLjD,EAAAA,EAAAA,KAACoL,EAAAA,EAAO,CAACC,MAAM,iBAAgB1F,UAC7B3F,EAAAA,EAAAA,KAACsL,EAAAA,EAAU,CACTT,KAAK,QACLpE,MAAM,UACN2B,QAASA,IAAMtE,GAAkBC,EAAO,UAAU4B,UAElD3F,EAAAA,EAAAA,KAAC0L,EAAAA,EAAY,SAKnB1L,EAAAA,EAAAA,KAACoL,EAAAA,EAAO,CAACC,MAAM,mBAAkB1F,UAC/B3F,EAAAA,EAAAA,KAACsL,EAAAA,EAAU,CACTT,KAAK,QACLpE,MAAM,QACN2B,QAASA,IAAMtE,GAAkBC,EAAO,YAAY4B,UAEpD3F,EAAAA,EAAAA,KAAC2L,EAAAA,EAAS,eAvGL5H,EAAM6H,aAiH3B5L,EAAAA,EAAAA,KAAC6L,EAAAA,EAAe,CACdC,mBAAoB,CAAC,GAAI,GAAI,GAAI,KACjCxD,UAAU,MACVyD,MAAOlK,GACPF,YAAaA,GACbF,KAAMA,GACNuK,aArViBC,CAACC,EAAOC,KAC/BzK,GAAQyK,IAqVFC,oBAlVyBF,IAC/BtK,GAAeyK,SAASH,EAAM1E,OAAOH,MAAO,KAC5C3F,GAAQ,UAqVN+D,EAAAA,EAAAA,MAAC6G,EAAAA,EAAM,CACLC,KAAM9J,GACN+J,QAASA,IAAM9J,IAAmB,GAClC+J,SAAS,KACTtF,WAAS,EAAAxB,SAAA,EAETF,EAAAA,EAAAA,MAACiH,EAAAA,EAAW,CAAA/G,SAAA,CAAC,mBACmB,OAAbpD,SAAa,IAAbA,QAAa,EAAbA,GAAe4B,YAElCnE,EAAAA,EAAAA,KAAC2M,EAAAA,EAAa,CAAAhH,SACXpD,KACCvC,EAAAA,EAAAA,KAAC0F,EAAAA,EAAG,CAAAC,UACFF,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAEN,SAAA,EACzBF,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,SAAA,EACvB3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,uBAGtCF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EAAC3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,UAAc,IAAEpD,GAAcW,SAClDuC,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EAAC3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,YAAgB,IAAEpD,GAAcU,WACpDwC,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EAAC3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,aAAiB,IAAEf,GAAWrC,GAAc4I,eAChE1F,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EAAC3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,aAAiB,IAAEf,GAAWrC,GAAcqK,kBAElEnH,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,SAAA,EACvB3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,sBAGtCF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,SAAa,IAAEpB,GAAyC,QAA7BpE,EAACoC,GAAcY,sBAAc,IAAAhD,OAAA,EAA5BA,EAA8B2K,UAAuC,QAA9B1K,EAAEmC,GAAcY,sBAAc,IAAA/C,OAAA,EAA5BA,EAA8B2K,YAE7GtF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,SAAa,IAAEpB,GAAyC,QAA7BlE,EAACkC,GAAcY,sBAAc,IAAA9C,OAAA,EAA5BA,EAA8B2K,UAAuC,QAA9B1K,EAAEiC,GAAcY,sBAAc,IAAA7C,OAAA,EAA5BA,EAA8ByK,YAE7GtF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,eAAmB,IAAEpB,GAAyC,QAA7BhE,EAACgC,GAAcY,sBAAc,IAAA5C,OAAA,EAA5BA,EAA8B0K,gBAA6C,QAA9BzK,EAAE+B,GAAcY,sBAAc,IAAA3C,OAAA,EAA5BA,EAA8BuK,YAEzHtF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,WAAe,IAAEpB,GAA+B,QAAnB9D,EAAC8B,GAAc2I,YAAI,IAAAzK,OAAA,EAAlBA,EAAoB6E,eAAkC,QAApB5E,EAAE6B,GAAc2I,YAAI,IAAAxK,OAAA,EAAlBA,EAAoB+D,iBAGlGgB,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAGT,SAAA,EAChB3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,yBAGtCF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,cAAkB,IAAuB,QAAtBhF,EAAC4B,GAAc4H,eAAO,IAAAxJ,OAAA,EAArBA,EAAuByJ,aAErD3E,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,gBAAoB,KAAuB,QAArB/E,EAAA2B,GAAc4H,eAAO,IAAAvJ,GAAY,QAAZC,EAArBD,EAAuB2J,kBAAU,IAAA1J,OAAZ,EAArBA,EAAmC2J,QAAS,EAAE,WAE9E/E,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT3F,EAAAA,EAAAA,KAAA,UAAA2F,SAAQ,sBAA0B,KAAuB,QAArB7E,EAAAyB,GAAc4H,eAAO,IAAArJ,GAAY,QAAZC,EAArBD,EAAuByJ,kBAAU,IAAAxJ,OAAZ,EAArBA,EAAmC8L,kBAAmB,SAG9FpH,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAGT,SAAA,EAChB3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,oBAGT,QADhB3E,EACZuB,GAAcuK,sBAAc,IAAA9L,OAAA,EAA5BA,EAA8B4H,IAAI,CAACmE,EAAQC,KAC1ChN,EAAAA,EAAAA,KAACyK,EAAAA,EAAI,CAEHxC,MAAO8E,EAAOA,OAAOE,QAAQ,IAAK,KAClCC,MAAO,CAAEC,YAAa,EAAGC,aAAc,IAFlCJ,QAMS,QAAnB/L,EAAAsB,GAAc8K,aAAK,IAAApM,OAAA,EAAnBA,EAAqBqM,gBACpB7H,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAGT,SAAA,EAChB3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,kBAGtC3F,EAAAA,EAAAA,KAAC4F,EAAAA,EAAU,CAAAD,SAAEpD,GAAc8K,MAAMC,0BAO7CtN,EAAAA,EAAAA,KAACuN,EAAAA,EAAa,CAAA5H,UACZ3F,EAAAA,EAAAA,KAACmI,EAAAA,EAAM,CAACC,QAASA,IAAM1F,IAAmB,GAAOiD,SAAC,kB,+HCzf5D,SAAe5F,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,kHACD,U,wBCTG,SAASuN,EAAsBC,GACpC,OAAOC,EAAAA,EAAAA,IAAqB,YAAaD,EAC3C,EACsBE,EAAAA,EAAAA,GAAuB,YAAa,CAAC,OAAQ,eAAgB,WAAY,UAAW,SAAU,MAAO,a,cCD3H,MAAMC,EAAY,CAAC,MAAO,WAAY,YAAa,YAAa,QAAS,YAAa,WAAY,QAAS,MAAO,SAAU,WAwBtHC,GAAaC,EAAAA,EAAAA,IAAO,MAAO,CAC/BC,KAAM,YACNN,KAAM,OACNO,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJC,GACEF,EACJ,MAAO,CAACC,EAAOE,KAAMF,EAAOC,EAAWtI,SAAUsI,EAAWE,cAAgBH,EAAOG,gBAPpEP,CAShBQ,IAAA,IAAC,MACFC,GACDD,EAAA,MAAM,CACL1G,SAAU,WACVkC,QAAS,OACT7C,WAAY,SACZuH,eAAgB,SAChBC,WAAY,EACZxE,MAAO,GACPC,OAAQ,GACRL,WAAY0E,EAAMG,WAAW7E,WAC7B8E,SAAUJ,EAAMG,WAAWE,QAAQ,IACnCC,WAAY,EACZC,aAAc,MACdC,SAAU,SACVC,WAAY,OACZC,SAAU,CAAC,CACThB,MAAO,CACLpI,QAAS,WAEXqH,MAAO,CACL4B,cAAeP,EAAMW,MAAQX,GAAOY,MAAML,eAE3C,CACDb,MAAO,CACLpI,QAAS,UAEXqH,MAAO,CACL4B,aAAc,IAEf,CACDb,MAAO,CACLI,cAAc,GAEhBnB,OAAOkC,EAAAA,EAAAA,GAAS,CACd3I,OAAQ8H,EAAMW,MAAQX,GAAOc,QAAQC,WAAWC,SAC/ChB,EAAMW,KAAO,CACdM,gBAAiBjB,EAAMW,KAAKG,QAAQrF,OAAOyF,YACzCL,EAAAA,EAAAA,GAAS,CACXI,gBAAiBjB,EAAMc,QAAQK,KAAK,MACnCnB,EAAMoB,YAAY,OAAQ,CAC3BH,gBAAiBjB,EAAMc,QAAQK,KAAK,cAIpCE,GAAY9B,EAAAA,EAAAA,IAAO,MAAO,CAC9BC,KAAM,YACNN,KAAM,MACNO,kBAAmBA,CAACC,EAAOC,IAAWA,EAAO2B,KAH7B/B,CAIf,CACD7D,MAAO,OACPC,OAAQ,OACR4F,UAAW,SAEXC,UAAW,QAEXtJ,MAAO,cAEPuJ,WAAY,MAERC,GAAiBnC,EAAAA,EAAAA,IAAOoC,EAAQ,CACpCnC,KAAM,YACNN,KAAM,WACNO,kBAAmBA,CAACC,EAAOC,IAAWA,EAAOiC,UAHxBrC,CAIpB,CACD7D,MAAO,MACPC,OAAQ,QAwCV,MAiJA,EAjJ4BkG,EAAAA,WAAiB,SAAgBC,EAASC,GACpE,MAAMrC,GAAQsC,EAAAA,EAAAA,GAAgB,CAC5BtC,MAAOoC,EACPtC,KAAM,eAEF,IACFyC,EACA7K,SAAU8K,EAAY,UACtBC,EAAS,UACTpI,EAAY,MAAK,MACjBqI,EAAQ,CAAC,EAAC,UACVC,EAAY,CAAC,EAAC,SACdC,EAAQ,MACRC,EAAK,IACLC,EAAG,OACHC,EAAM,QACNnL,EAAU,YACRoI,EACJgD,GAAQC,EAAAA,EAAAA,GAA8BjD,EAAOL,GAC/C,IAAIjI,EAAW,KAGf,MAAMwL,EA5DR,SAAkBC,GAKf,IALgB,YACjBC,EAAW,eACXC,EAAc,IACdP,EAAG,OACHC,GACDI,EACC,MAAOD,EAAQI,GAAanB,EAAAA,UAAe,GA8B3C,OA7BAA,EAAAA,UAAgB,KACd,IAAKW,IAAQC,EACX,OAEFO,GAAU,GACV,IAAIC,GAAS,EACb,MAAMC,EAAQ,IAAIC,MAmBlB,OAlBAD,EAAME,OAAS,KACRH,GAGLD,EAAU,WAEZE,EAAMG,QAAU,KACTJ,GAGLD,EAAU,UAEZE,EAAMJ,YAAcA,EACpBI,EAAMH,eAAiBA,EACvBG,EAAMV,IAAMA,EACRC,IACFS,EAAMI,OAASb,GAEV,KACLQ,GAAS,IAEV,CAACH,EAAaC,EAAgBP,EAAKC,IAC/BG,CACT,CAuBiBW,EAAU1C,EAAAA,EAAAA,GAAS,CAAC,EAAGyB,EAAU,CAC9CE,MACAC,YAEIe,EAAShB,GAAOC,EAChBgB,EAAmBD,GAAqB,UAAXZ,EAC7BhD,GAAaiB,EAAAA,EAAAA,GAAS,CAAC,EAAGnB,EAAO,CACrCI,cAAe2D,EACf1J,YACAzC,YAEIoM,EAjKkB9D,KACxB,MAAM,QACJ8D,EAAO,QACPpM,EAAO,aACPwI,GACEF,EACEwC,EAAQ,CACZvC,KAAM,CAAC,OAAQvI,EAASwI,GAAgB,gBACxCwB,IAAK,CAAC,OACNM,SAAU,CAAC,aAEb,OAAO+B,EAAAA,EAAAA,GAAevB,EAAOnD,EAAuByE,IAsJpCE,CAAkBhE,IAC3BiE,EAASC,IAAgBC,EAAAA,EAAAA,GAAQ,MAAO,CAC7C5B,UAAWuB,EAAQpC,IACnB0C,YAAa3C,EACb4C,uBAAwB,CACtB7B,QACAC,UAAW,CACTf,KAAKT,EAAAA,EAAAA,GAAS,CAAC,EAAGyB,EAAUD,EAAUf,OAG1C4C,gBAAiB,CACfjC,MACAO,MACAC,SACAF,SAEF3C,eAgBF,OAbExI,EADEqM,GACsBhS,EAAAA,EAAAA,KAAKoS,GAAShD,EAAAA,EAAAA,GAAS,CAAC,EAAGiD,IAGxC5B,GAAiC,IAAjBA,EAChBA,EACFsB,GAAUvB,EACRA,EAAI,IAESxQ,EAAAA,EAAAA,KAAKiQ,EAAgB,CAC3C9B,WAAYA,EACZuC,UAAWuB,EAAQ9B,YAGHnQ,EAAAA,EAAAA,KAAK6N,GAAYuB,EAAAA,EAAAA,GAAS,CAC5CsD,GAAIpK,EACJ6F,WAAYA,EACZuC,WAAWiC,EAAAA,EAAAA,GAAKV,EAAQ7D,KAAMsC,GAC9BJ,IAAKA,GACJW,EAAO,CACRtL,SAAUA,IAEd,E,yDCpNA,SAAe5F,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,4EACD,a,kGCFJ,MAAM2N,EAAY,CAAC,YAAa,cAAe,aAAc,yBAA0B,oBAAqB,0BAC1GgF,EAAa,CAAC,YAAa,QAAS,aACpCC,EAAa,CAAC,aAmBD,SAASP,EAOxBvE,EAAM+E,GACJ,MAAM,UACFpC,EACA6B,YAAaQ,EAAkB,WAC/B5E,EAAU,uBACVqE,EAAsB,kBACtBQ,EAAiB,uBACjBC,GACEH,EACJI,GAAqBhC,EAAAA,EAAAA,GAA8B4B,EAAYlF,IAE7DtF,UAAW6K,EAAa,MACxBxC,EAAQ,CACN,CAAC5C,QAAO/K,GACT,UACD4N,EAAY,CACV,CAAC7C,QAAO/K,IAERwP,EACJvB,GAAQC,EAAAA,EAAAA,GAA8BsB,EAAwBI,GAC1DL,EAAc5B,EAAM5C,IAASgF,EAI7BK,GAA0BC,EAAAA,EAAAA,GAAsBzC,EAAU7C,GAAOI,GACjEmF,GAAkBC,EAAAA,EAAAA,IAAenE,EAAAA,EAAAA,GAAS,CAC5CsB,aACCwC,EAAoB,CACrBV,uBAAiC,SAATzE,EAAkBkD,OAAQjO,EAClDwQ,kBAAmBJ,MAGnBnF,OACE3F,UAAWmL,GACZ,YACDC,GACEJ,EACJK,GAAczC,EAAAA,EAAAA,GAA8BoC,EAAgBrF,MAAO4E,GAC/DvC,GAAMsD,EAAAA,EAAAA,GAAWF,EAAwC,MAA3BN,OAAkC,EAASA,EAAwB9C,IAAKwC,EAAWxC,KACjHuD,EAAiBb,EAAoBA,EAAkBW,GAAe,CAAC,EACvEG,GAAkB1E,EAAAA,EAAAA,GAAS,CAAC,EAAGjB,EAAY0F,GAC3CE,EAAyB,SAAThG,EAAkB0F,GAAiBN,EAAgBM,EACnExF,GAAQ+F,EAAAA,EAAAA,GAAiBzB,GAAanD,EAAAA,EAAAA,GAAS,CAAC,EAAY,SAATrB,IAAoBoF,IAAkBxC,EAAM5C,IAASkF,EAAiC,SAATlF,IAAoB4C,EAAM5C,IAASkF,EAAwBU,EAAaI,GAAiB,CAC7NrB,GAAIqB,GACH,CACDzD,QACEwD,GAIJ,OAHAG,OAAOC,KAAKL,GAAgBM,QAAQC,WAC3BnG,EAAMmG,KAER,CAAC7B,EAAatE,EACvB,C,yDC/EA,SAAelO,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,2EACD,e", "sources": ["../node_modules/@mui/icons-material/esm/Pause.js", "pages/OffersPage.js", "../node_modules/@mui/material/internal/svg-icons/Person.js", "../node_modules/@mui/material/Avatar/avatarClasses.js", "../node_modules/@mui/material/Avatar/Avatar.js", "../node_modules/@mui/icons-material/esm/TrendingUp.js", "../node_modules/@mui/material/utils/useSlot.js", "../node_modules/@mui/icons-material/esm/TrendingDown.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 19h4V5H6zm8-14v14h4V5z\"\n}), 'Pause');", "import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  Chip,\n  IconButton,\n  Tooltip,\n  TextField,\n  InputAdornment,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Avatar,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Visibility as ViewIcon,\n  Block as BlockIcon,\n  CheckCircle as ActivateIcon,\n  Pause as PauseIcon,\n  TrendingUp as TrendingUpIcon,\n  TrendingDown as TrendingDownIcon,\n} from '@mui/icons-material';\nimport axios from 'axios';\n\nconst OffersPage = () => {\n  const [offers, setOffers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  const [totalOffers, setTotalOffers] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [typeFilter, setTypeFilter] = useState('');\n  const [cryptoFilter, setCryptoFilter] = useState('');\n  const [selectedOffer, setSelectedOffer] = useState(null);\n  const [offerDetailOpen, setOfferDetailOpen] = useState(false);\n\n  // Fetch offers data\n  const fetchOffers = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: page + 1,\n        limit: rowsPerPage,\n        search: searchTerm || undefined,\n        status: statusFilter || undefined,\n        type: typeFilter || undefined,\n        cryptocurrency: cryptoFilter || undefined,\n        sortBy: 'createdAt',\n        sortOrder: 'desc'\n      };\n\n      const response = await axios.get('/admin/offers', { params });\n\n      if (response.data.success) {\n        setOffers(response.data.data.offers);\n        setTotalOffers(response.data.data.pagination.totalOffers);\n      }\n    } catch (error) {\n      console.error('Error fetching offers:', error);\n      setError('Failed to fetch offers');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchOffers();\n  }, [page, rowsPerPage, searchTerm, statusFilter, typeFilter, cryptoFilter]);\n\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  const handleChangeRowsPerPage = (event) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  const handleViewOffer = (offer) => {\n    setSelectedOffer(offer);\n    setOfferDetailOpen(true);\n  };\n\n  const handleOfferAction = async (offer, action) => {\n    try {\n      const response = await axios.put(`/admin/offers/${offer.offerId}/status`, {\n        status: action,\n        reason: `Admin ${action} action`\n      });\n\n      if (response.data.success) {\n        fetchOffers(); // Refresh the list\n      }\n    } catch (error) {\n      console.error('Error performing offer action:', error);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active': return 'success';\n      case 'paused': return 'warning';\n      case 'inactive': return 'error';\n      case 'expired': return 'default';\n      default: return 'default';\n    }\n  };\n\n  const getTypeColor = (type) => {\n    return type === 'buy' ? 'success' : 'error';\n  };\n\n  const getTypeIcon = (type) => {\n    return type === 'buy' ? <TrendingUpIcon /> : <TrendingDownIcon />;\n  };\n\n  const formatAmount = (amount, currency) => {\n    return `${parseFloat(amount).toFixed(2)} ${currency}`;\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const calculateMargin = (effectivePrice, marketPrice) => {\n    if (!marketPrice) return 'N/A';\n    const margin = ((effectivePrice - marketPrice) / marketPrice) * 100;\n    return `${margin > 0 ? '+' : ''}${margin.toFixed(2)}%`;\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Offer Management\n      </Typography>\n\n      {/* Summary Cards */}\n      <Grid container spacing={3} mb={3}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                Total Offers\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalOffers.toLocaleString()}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                Active Offers\n              </Typography>\n              <Typography variant=\"h4\" color=\"success.main\">\n                {offers.filter(o => o.status === 'active').length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                Buy Offers\n              </Typography>\n              <Typography variant=\"h4\" color=\"success.main\">\n                {offers.filter(o => o.type === 'buy').length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                Sell Offers\n              </Typography>\n              <Typography variant=\"h4\" color=\"error.main\">\n                {offers.filter(o => o.type === 'sell').length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Filters */}\n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={12} md={3}>\n            <TextField\n              fullWidth\n              placeholder=\"Search offers...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={2}>\n            <FormControl fullWidth>\n              <InputLabel>Status</InputLabel>\n              <Select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                label=\"Status\"\n              >\n                <MenuItem value=\"\">All</MenuItem>\n                <MenuItem value=\"active\">Active</MenuItem>\n                <MenuItem value=\"paused\">Paused</MenuItem>\n                <MenuItem value=\"inactive\">Inactive</MenuItem>\n                <MenuItem value=\"expired\">Expired</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={2}>\n            <FormControl fullWidth>\n              <InputLabel>Type</InputLabel>\n              <Select\n                value={typeFilter}\n                onChange={(e) => setTypeFilter(e.target.value)}\n                label=\"Type\"\n              >\n                <MenuItem value=\"\">All</MenuItem>\n                <MenuItem value=\"buy\">Buy</MenuItem>\n                <MenuItem value=\"sell\">Sell</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={2}>\n            <FormControl fullWidth>\n              <InputLabel>Crypto</InputLabel>\n              <Select\n                value={cryptoFilter}\n                onChange={(e) => setCryptoFilter(e.target.value)}\n                label=\"Crypto\"\n              >\n                <MenuItem value=\"\">All</MenuItem>\n                <MenuItem value=\"USDT\">USDT</MenuItem>\n                <MenuItem value=\"USDC\">USDC</MenuItem>\n                <MenuItem value=\"BTC\">BTC</MenuItem>\n                <MenuItem value=\"ETH\">ETH</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={2}>\n            <Button\n              variant=\"outlined\"\n              onClick={() => {\n                setSearchTerm('');\n                setStatusFilter('');\n                setTypeFilter('');\n                setCryptoFilter('');\n              }}\n            >\n              Clear Filters\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Offers Table */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Offer ID</TableCell>\n              <TableCell>Creator</TableCell>\n              <TableCell>Type</TableCell>\n              <TableCell>Amount Range</TableCell>\n              <TableCell>Price</TableCell>\n              <TableCell>Status</TableCell>\n              <TableCell>Created</TableCell>\n              <TableCell>Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {offers.map((offer) => (\n              <TableRow key={offer._id} hover>\n                <TableCell>\n                  <Typography variant=\"body2\" fontFamily=\"monospace\">\n                    {offer.offerId}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                    <Avatar sx={{ width: 32, height: 32 }}>\n                      {offer.creator?.username?.charAt(0).toUpperCase()}\n                    </Avatar>\n                    <Box>\n                      <Typography variant=\"body2\">\n                        {offer.creator?.username}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"textSecondary\">\n                        Rep: {offer.creator?.reputation?.score || 0}\n                      </Typography>\n                    </Box>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    icon={getTypeIcon(offer.type)}\n                    label={offer.type.toUpperCase()}\n                    color={getTypeColor(offer.type)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Box>\n                    <Typography variant=\"body2\">\n                      {formatAmount(offer.cryptocurrency?.minAmount, offer.cryptocurrency?.symbol)} -\n                      {formatAmount(offer.cryptocurrency?.maxAmount, offer.cryptocurrency?.symbol)}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"textSecondary\">\n                      Available: {formatAmount(offer.cryptocurrency?.availableAmount, offer.cryptocurrency?.symbol)}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Box>\n                    <Typography variant=\"body2\">\n                      {formatAmount(offer.fiat?.effectivePrice, offer.fiat?.currency)}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"textSecondary\">\n                      Margin: {calculateMargin(offer.fiat?.effectivePrice, offer.fiat?.marketPrice)}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={offer.status}\n                    color={getStatusColor(offer.status)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\">\n                    {formatDate(offer.createdAt)}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Box display=\"flex\" gap={1}>\n                    <Tooltip title=\"View Details\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleViewOffer(offer)}\n                      >\n                        <ViewIcon />\n                      </IconButton>\n                    </Tooltip>\n\n                    {offer.status === 'active' && (\n                      <Tooltip title=\"Pause Offer\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"warning\"\n                          onClick={() => handleOfferAction(offer, 'paused')}\n                        >\n                          <PauseIcon />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n\n                    {offer.status === 'paused' && (\n                      <Tooltip title=\"Activate Offer\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"success\"\n                          onClick={() => handleOfferAction(offer, 'active')}\n                        >\n                          <ActivateIcon />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n\n                    <Tooltip title=\"Deactivate Offer\">\n                      <IconButton\n                        size=\"small\"\n                        color=\"error\"\n                        onClick={() => handleOfferAction(offer, 'inactive')}\n                      >\n                        <BlockIcon />\n                      </IconButton>\n                    </Tooltip>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n\n        <TablePagination\n          rowsPerPageOptions={[10, 25, 50, 100]}\n          component=\"div\"\n          count={totalOffers}\n          rowsPerPage={rowsPerPage}\n          page={page}\n          onPageChange={handleChangePage}\n          onRowsPerPageChange={handleChangeRowsPerPage}\n        />\n      </TableContainer>\n\n      {/* Offer Detail Dialog */}\n      <Dialog\n        open={offerDetailOpen}\n        onClose={() => setOfferDetailOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          Offer Details - {selectedOffer?.offerId}\n        </DialogTitle>\n        <DialogContent>\n          {selectedOffer && (\n            <Box>\n              <Grid container spacing={2}>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Offer Information\n                  </Typography>\n                  <Typography><strong>Type:</strong> {selectedOffer.type}</Typography>\n                  <Typography><strong>Status:</strong> {selectedOffer.status}</Typography>\n                  <Typography><strong>Created:</strong> {formatDate(selectedOffer.createdAt)}</Typography>\n                  <Typography><strong>Expires:</strong> {formatDate(selectedOffer.expiresAt)}</Typography>\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Amount & Pricing\n                  </Typography>\n                  <Typography>\n                    <strong>Min:</strong> {formatAmount(selectedOffer.cryptocurrency?.minAmount, selectedOffer.cryptocurrency?.symbol)}\n                  </Typography>\n                  <Typography>\n                    <strong>Max:</strong> {formatAmount(selectedOffer.cryptocurrency?.maxAmount, selectedOffer.cryptocurrency?.symbol)}\n                  </Typography>\n                  <Typography>\n                    <strong>Available:</strong> {formatAmount(selectedOffer.cryptocurrency?.availableAmount, selectedOffer.cryptocurrency?.symbol)}\n                  </Typography>\n                  <Typography>\n                    <strong>Price:</strong> {formatAmount(selectedOffer.fiat?.effectivePrice, selectedOffer.fiat?.currency)}\n                  </Typography>\n                </Grid>\n                <Grid item xs={12}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Creator Information\n                  </Typography>\n                  <Typography>\n                    <strong>Username:</strong> {selectedOffer.creator?.username}\n                  </Typography>\n                  <Typography>\n                    <strong>Reputation:</strong> {selectedOffer.creator?.reputation?.score || 0}/100\n                  </Typography>\n                  <Typography>\n                    <strong>Completed Trades:</strong> {selectedOffer.creator?.reputation?.completedTrades || 0}\n                  </Typography>\n                </Grid>\n                <Grid item xs={12}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Payment Methods\n                  </Typography>\n                  {selectedOffer.paymentMethods?.map((method, index) => (\n                    <Chip\n                      key={index}\n                      label={method.method.replace('_', ' ')}\n                      style={{ marginRight: 8, marginBottom: 4 }}\n                    />\n                  ))}\n                </Grid>\n                {selectedOffer.terms?.instructions && (\n                  <Grid item xs={12}>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Instructions\n                    </Typography>\n                    <Typography>{selectedOffer.terms.instructions}</Typography>\n                  </Grid>\n                )}\n              </Grid>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setOfferDetailOpen(false)}>\n            Close\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default OffersPage;\n", "'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from '../../utils/createSvgIcon';\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n}), 'Person');", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAvatarUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatar', slot);\n}\nconst avatarClasses = generateUtilityClasses('MuiAvatar', ['root', 'colorDefault', 'circular', 'rounded', 'square', 'img', 'fallback']);\nexport default avatarClasses;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"alt\", \"children\", \"className\", \"component\", \"slots\", \"slotProps\", \"imgProps\", \"sizes\", \"src\", \"srcSet\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Person from '../internal/svg-icons/Person';\nimport { getAvatarUtilityClass } from './avatarClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    colorDefault\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, colorDefault && 'colorDefault'],\n    img: ['img'],\n    fallback: ['fallback']\n  };\n  return composeClasses(slots, getAvatarUtilityClass, classes);\n};\nconst AvatarRoot = styled('div', {\n  name: 'MuiAvatar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], ownerState.colorDefault && styles.colorDefault];\n  }\n})(({\n  theme\n}) => ({\n  position: 'relative',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  flexShrink: 0,\n  width: 40,\n  height: 40,\n  fontFamily: theme.typography.fontFamily,\n  fontSize: theme.typography.pxToRem(20),\n  lineHeight: 1,\n  borderRadius: '50%',\n  overflow: 'hidden',\n  userSelect: 'none',\n  variants: [{\n    props: {\n      variant: 'rounded'\n    },\n    style: {\n      borderRadius: (theme.vars || theme).shape.borderRadius\n    }\n  }, {\n    props: {\n      variant: 'square'\n    },\n    style: {\n      borderRadius: 0\n    }\n  }, {\n    props: {\n      colorDefault: true\n    },\n    style: _extends({\n      color: (theme.vars || theme).palette.background.default\n    }, theme.vars ? {\n      backgroundColor: theme.vars.palette.Avatar.defaultBg\n    } : _extends({\n      backgroundColor: theme.palette.grey[400]\n    }, theme.applyStyles('dark', {\n      backgroundColor: theme.palette.grey[600]\n    })))\n  }]\n}));\nconst AvatarImg = styled('img', {\n  name: 'MuiAvatar',\n  slot: 'Img',\n  overridesResolver: (props, styles) => styles.img\n})({\n  width: '100%',\n  height: '100%',\n  textAlign: 'center',\n  // Handle non-square image. The property isn't supported by IE11.\n  objectFit: 'cover',\n  // Hide alt text.\n  color: 'transparent',\n  // Hide the image broken icon, only works on Chrome.\n  textIndent: 10000\n});\nconst AvatarFallback = styled(Person, {\n  name: 'MuiAvatar',\n  slot: 'Fallback',\n  overridesResolver: (props, styles) => styles.fallback\n})({\n  width: '75%',\n  height: '75%'\n});\nfunction useLoaded({\n  crossOrigin,\n  referrerPolicy,\n  src,\n  srcSet\n}) {\n  const [loaded, setLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (!src && !srcSet) {\n      return undefined;\n    }\n    setLoaded(false);\n    let active = true;\n    const image = new Image();\n    image.onload = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('loaded');\n    };\n    image.onerror = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('error');\n    };\n    image.crossOrigin = crossOrigin;\n    image.referrerPolicy = referrerPolicy;\n    image.src = src;\n    if (srcSet) {\n      image.srcset = srcSet;\n    }\n    return () => {\n      active = false;\n    };\n  }, [crossOrigin, referrerPolicy, src, srcSet]);\n  return loaded;\n}\nconst Avatar = /*#__PURE__*/React.forwardRef(function Avatar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatar'\n  });\n  const {\n      alt,\n      children: childrenProp,\n      className,\n      component = 'div',\n      slots = {},\n      slotProps = {},\n      imgProps,\n      sizes,\n      src,\n      srcSet,\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let children = null;\n\n  // Use a hook instead of onError on the img element to support server-side rendering.\n  const loaded = useLoaded(_extends({}, imgProps, {\n    src,\n    srcSet\n  }));\n  const hasImg = src || srcSet;\n  const hasImgNotFailing = hasImg && loaded !== 'error';\n  const ownerState = _extends({}, props, {\n    colorDefault: !hasImgNotFailing,\n    component,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const [ImgSlot, imgSlotProps] = useSlot('img', {\n    className: classes.img,\n    elementType: AvatarImg,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        img: _extends({}, imgProps, slotProps.img)\n      }\n    },\n    additionalProps: {\n      alt,\n      src,\n      srcSet,\n      sizes\n    },\n    ownerState\n  });\n  if (hasImgNotFailing) {\n    children = /*#__PURE__*/_jsx(ImgSlot, _extends({}, imgSlotProps));\n    // We only render valid children, non valid children are rendered with a fallback\n    // We consider that invalid children are all falsy values, except 0, which is valid.\n  } else if (!!childrenProp || childrenProp === 0) {\n    children = childrenProp;\n  } else if (hasImg && alt) {\n    children = alt[0];\n  } else {\n    children = /*#__PURE__*/_jsx(AvatarFallback, {\n      ownerState: ownerState,\n      className: classes.fallback\n    });\n  }\n  return /*#__PURE__*/_jsx(AvatarRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Avatar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used in combination with `src` or `srcSet` to\n   * provide an alt attribute for the rendered `img` element.\n   */\n  alt: PropTypes.string,\n  /**\n   * Used to render icon or text elements inside the Avatar if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attributes) applied to the `img` element if the component is used to display an image.\n   * It can be used to listen for the loading error event.\n   * @deprecated Use `slotProps.img` instead. This prop will be removed in v7. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/).\n   */\n  imgProps: PropTypes.object,\n  /**\n   * The `sizes` attribute for the `img` element.\n   */\n  sizes: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    img: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    img: PropTypes.elementType\n  }),\n  /**\n   * The `src` attribute for the `img` element.\n   */\n  src: PropTypes.string,\n  /**\n   * The `srcSet` attribute for the `img` element.\n   * Use this attribute for responsive image display.\n   */\n  srcSet: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The shape of the avatar.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default Avatar;", "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z\"\n}), 'TrendingUp');", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"elementType\", \"ownerState\", \"externalForwardedProps\", \"getSlotOwnerState\", \"internalForwardedProps\"],\n  _excluded2 = [\"component\", \"slots\", \"slotProps\"],\n  _excluded3 = [\"component\"];\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n      className,\n      elementType: initialElementType,\n      ownerState,\n      externalForwardedProps,\n      getSlotOwnerState,\n      internalForwardedProps\n    } = parameters,\n    useSlotPropsParams = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const {\n      component: rootComponent,\n      slots = {\n        [name]: undefined\n      },\n      slotProps = {\n        [name]: undefined\n      }\n    } = externalForwardedProps,\n    other = _objectWithoutPropertiesLoose(externalForwardedProps, _excluded2);\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const _mergeSlotProps = mergeSlotProps(_extends({\n      className\n    }, useSlotPropsParams, {\n      externalForwardedProps: name === 'root' ? other : undefined,\n      externalSlotProps: resolvedComponentsProps\n    })),\n    {\n      props: {\n        component: slotComponent\n      },\n      internalRef\n    } = _mergeSlotProps,\n    mergedProps = _objectWithoutPropertiesLoose(_mergeSlotProps.props, _excluded3);\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, parameters.ref);\n  const slotOwnerState = getSlotOwnerState ? getSlotOwnerState(mergedProps) : {};\n  const finalOwnerState = _extends({}, ownerState, slotOwnerState);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, _extends({}, name === 'root' && !rootComponent && !slots[name] && internalForwardedProps, name !== 'root' && !slots[name] && internalForwardedProps, mergedProps, LeafComponent && {\n    as: LeafComponent\n  }, {\n    ref\n  }), finalOwnerState);\n  Object.keys(slotOwnerState).forEach(propName => {\n    delete props[propName];\n  });\n  return [elementType, props];\n}", "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m16 18 2.29-2.29-4.88-4.88-4 4L2 7.41 3.41 6l6 6 4-4 6.3 6.29L22 12v6z\"\n}), 'TrendingDown');"], "names": ["createSvgIcon", "_jsx", "d", "OffersPage", "_selectedOffer$crypto", "_selectedOffer$crypto2", "_selectedOffer$crypto3", "_selectedOffer$crypto4", "_selectedOffer$crypto5", "_selectedOffer$crypto6", "_selectedOffer$fiat", "_selectedOffer$fiat2", "_selectedOffer$creato", "_selectedOffer$creato2", "_selectedOffer$creato3", "_selectedOffer$creato4", "_selectedOffer$creato5", "_selectedOffer$paymen", "_selectedOffer$terms", "offers", "setOffers", "useState", "loading", "setLoading", "error", "setError", "page", "setPage", "rowsPerPage", "setRowsPerPage", "totalOffers", "setTotalOffers", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "typeFilter", "setTypeFilter", "cryptoFilter", "setCryptoFilter", "<PERSON><PERSON><PERSON>", "setSelectedOffer", "offerDetailOpen", "setOfferDetailOpen", "fetchOffers", "async", "params", "limit", "search", "undefined", "status", "type", "cryptocurrency", "sortBy", "sortOrder", "response", "axios", "get", "data", "success", "pagination", "console", "useEffect", "handleOfferAction", "offer", "action", "put", "concat", "offerId", "reason", "getStatusColor", "getTypeColor", "formatAmount", "amount", "currency", "parseFloat", "toFixed", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "<PERSON><PERSON><PERSON><PERSON>", "effectivePrice", "marketPrice", "margin", "_jsxs", "Box", "children", "Typography", "variant", "gutterBottom", "Grid", "container", "spacing", "mb", "item", "xs", "sm", "md", "Card", "<PERSON><PERSON><PERSON><PERSON>", "color", "toLocaleString", "filter", "o", "length", "Paper", "sx", "p", "alignItems", "TextField", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "InputAdornment", "position", "SearchIcon", "FormControl", "InputLabel", "Select", "label", "MenuItem", "<PERSON><PERSON>", "onClick", "TableContainer", "component", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "map", "_offer$creator", "_offer$creator$userna", "_offer$creator2", "_offer$creator3", "_offer$creator3$reput", "_offer$cryptocurrency", "_offer$cryptocurrency2", "_offer$cryptocurrency3", "_offer$cryptocurrency4", "_offer$cryptocurrency5", "_offer$cryptocurrency6", "_offer$fiat", "_offer$fiat2", "_offer$fiat3", "_offer$fiat4", "hover", "fontFamily", "display", "gap", "Avatar", "width", "height", "creator", "username", "char<PERSON>t", "toUpperCase", "reputation", "score", "Chip", "icon", "TrendingUpIcon", "TrendingDownIcon", "size", "minAmount", "symbol", "maxAmount", "availableAmount", "fiat", "createdAt", "<PERSON><PERSON><PERSON>", "title", "IconButton", "handleViewOffer", "ViewIcon", "PauseIcon", "ActivateIcon", "BlockIcon", "_id", "TablePagination", "rowsPerPageOptions", "count", "onPageChange", "handleChangePage", "event", "newPage", "onRowsPerPageChange", "parseInt", "Dialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expiresAt", "completedTrades", "paymentMethods", "method", "index", "replace", "style", "marginRight", "marginBottom", "terms", "instructions", "DialogActions", "getAvatarUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "_excluded", "AvatarRoot", "styled", "name", "overridesResolver", "props", "styles", "ownerState", "root", "colorDefault", "_ref", "theme", "justifyContent", "flexShrink", "typography", "fontSize", "pxToRem", "lineHeight", "borderRadius", "overflow", "userSelect", "variants", "vars", "shape", "_extends", "palette", "background", "default", "backgroundColor", "defaultBg", "grey", "applyStyles", "AvatarImg", "img", "textAlign", "objectFit", "textIndent", "AvatarFallback", "Person", "fallback", "React", "inProps", "ref", "useDefaultProps", "alt", "childrenProp", "className", "slots", "slotProps", "imgProps", "sizes", "src", "srcSet", "other", "_objectWithoutPropertiesLoose", "loaded", "_ref2", "crossOrigin", "referrerPolicy", "setLoaded", "active", "image", "Image", "onload", "onerror", "srcset", "useLoaded", "hasImg", "hasImgNotFailing", "classes", "composeClasses", "useUtilityClasses", "ImgSlot", "imgSlotProps", "useSlot", "elementType", "externalForwardedProps", "additionalProps", "as", "clsx", "_excluded2", "_excluded3", "parameters", "initialElementType", "getSlotOwnerState", "internalForwardedProps", "useSlotPropsParams", "rootComponent", "resolvedComponentsProps", "resolveComponentProps", "_mergeSlotProps", "mergeSlotProps", "externalSlotProps", "slotComponent", "internalRef", "mergedProps", "useForkRef", "slotOwnerState", "finalOwnerState", "LeafComponent", "appendOwnerState", "Object", "keys", "for<PERSON>ach", "propName"], "sourceRoot": ""}