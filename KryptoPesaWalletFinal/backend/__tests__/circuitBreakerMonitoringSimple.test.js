/**
 * Circuit Breaker Monitoring Service Tests
 * Simple tests for circuit breaker monitoring functionality
 */

const { CircuitBreakerMonitoringService } = require('../src/services/circuitBreakerMonitoring');
const { CircuitBreaker } = require('../src/utils/circuitBreaker');

describe('Circuit Breaker Monitoring Service', () => {
  let monitoringService;
  let testCircuitBreaker;

  beforeEach(async () => {
    // Create test circuit breaker
    testCircuitBreaker = new CircuitBreaker({
      name: 'TestService',
      failureThreshold: 3,
      timeout: 5000,
      monitoringPeriod: 1000
    });

    // Create fresh monitoring service instance for testing
    monitoringService = new CircuitBreakerMonitoringService();

    // Initialize monitoring service
    await monitoringService.initialize();
  });

  afterEach(async () => {
    if (monitoringService) {
      await monitoringService.shutdown();
    }
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      const freshService = new CircuitBreakerMonitoringService();
      const result = await freshService.initialize();
      
      expect(result).toBe(true);
      expect(freshService.isInitialized).toBe(true);
      
      await freshService.shutdown();
    });

    test('should not initialize twice', async () => {
      const result = await monitoringService.initialize();
      expect(result).toBe(true);
    });
  });

  describe('Metrics Collection', () => {
    test('should collect circuit breaker metrics', () => {
      const metrics = monitoringService.getCircuitBreakerMetrics();

      expect(metrics).toHaveProperty('circuitBreakers');
      expect(metrics).toHaveProperty('summary');
      expect(metrics).toHaveProperty('timestamp');
    });

    test('should calculate health score correctly', () => {
      // Test healthy circuit breaker
      testCircuitBreaker.state = 'CLOSED';
      testCircuitBreaker.metrics.totalRequests = 100;
      testCircuitBreaker.metrics.totalFailures = 5; // 5% failure rate
      
      monitoringService.calculateHealthScore('testService', testCircuitBreaker.getState());
      
      const healthScore = monitoringService.metrics.healthScores.get('testService');
      expect(healthScore).toBeGreaterThan(80);
    });

    test('should calculate health score for degraded circuit breaker', () => {
      // Test degraded circuit breaker
      testCircuitBreaker.state = 'OPEN';
      testCircuitBreaker.metrics.totalRequests = 100;
      testCircuitBreaker.metrics.totalFailures = 60; // 60% failure rate
      
      monitoringService.calculateHealthScore('testService', testCircuitBreaker.getState());
      
      const healthScore = monitoringService.metrics.healthScores.get('testService');
      expect(healthScore).toBeLessThan(50);
    });

    test('should generate circuit breaker summary', () => {
      const testMetrics = {
        testService1: {
          currentState: 'CLOSED',
          healthScore: 90,
          totalTrips: 2,
          totalResets: 2,
          uptime: 95
        },
        testService2: {
          currentState: 'OPEN',
          healthScore: 30,
          totalTrips: 5,
          totalResets: 3,
          uptime: 70
        }
      };

      const summary = monitoringService.getCircuitBreakerSummary(testMetrics);

      expect(summary.totalCircuitBreakers).toBe(2);
      expect(summary.healthyCount).toBe(1);
      expect(summary.unhealthyCount).toBe(1);
      expect(summary.openCount).toBe(1);
      expect(summary.closedCount).toBe(1);
      expect(summary.totalTrips).toBe(7);
      expect(summary.totalResets).toBe(5);
      expect(summary.averageHealthScore).toBe(60);
      expect(summary.averageUptime).toBe(82.5);
    });
  });

  describe('Manual Controls', () => {
    test('should force circuit breaker to open state', async () => {
      // Add test circuit breaker to the collection for testing
      const { circuitBreakers } = require('../src/utils/circuitBreaker');
      circuitBreakers.testService = testCircuitBreaker;

      const result = await monitoringService.forceCircuitBreakerState('testService', 'open');

      expect(result.success).toBe(true);
      expect(result.newState).toBe('OPEN');
      expect(testCircuitBreaker.getState().state).toBe('OPEN');

      // Clean up
      delete circuitBreakers.testService;
    });

    test('should force circuit breaker to closed state', async () => {
      // Add test circuit breaker to the collection for testing
      const { circuitBreakers } = require('../src/utils/circuitBreaker');
      circuitBreakers.testService = testCircuitBreaker;

      testCircuitBreaker.forceOpen();
      
      const result = await monitoringService.forceCircuitBreakerState('testService', 'closed');

      expect(result.success).toBe(true);
      expect(result.newState).toBe('CLOSED');
      expect(testCircuitBreaker.getState().state).toBe('CLOSED');

      // Clean up
      delete circuitBreakers.testService;
    });

    test('should throw error for invalid circuit breaker name', async () => {
      await expect(
        monitoringService.forceCircuitBreakerState('nonexistent', 'open')
      ).rejects.toThrow('not found');
    });

    test('should throw error for invalid state', async () => {
      // Add test circuit breaker to the collection for testing
      const { circuitBreakers } = require('../src/utils/circuitBreaker');
      circuitBreakers.testService = testCircuitBreaker;

      await expect(
        monitoringService.forceCircuitBreakerState('testService', 'invalid')
      ).rejects.toThrow('Invalid state');

      // Clean up
      delete circuitBreakers.testService;
    });
  });

  describe('Metrics Reset', () => {
    test('should reset specific circuit breaker metrics', () => {
      // Add some metrics first
      monitoringService.metrics.totalTrips.set('testService', 5);
      monitoringService.metrics.totalResets.set('testService', 3);

      const result = monitoringService.resetCircuitBreakerMetrics('testService');

      expect(result.success).toBe(true);
      expect(monitoringService.metrics.totalTrips.get('testService')).toBe(0);
      expect(monitoringService.metrics.totalResets.get('testService')).toBe(0);
    });

    test('should reset all circuit breaker metrics', () => {
      // Add some metrics first
      monitoringService.metrics.totalTrips.set('testService', 5);
      
      const result = monitoringService.resetCircuitBreakerMetrics();

      expect(result.success).toBe(true);
      expect(result.circuitBreaker).toBe('all');
      expect(monitoringService.metrics.totalTrips.get('testService')).toBe(0);
    });

    test('should throw error for nonexistent circuit breaker', () => {
      expect(() => {
        monitoringService.resetCircuitBreakerMetrics('nonexistent');
      }).toThrow('not found');
    });
  });

  describe('Cleanup and Shutdown', () => {
    test('should clean up old metrics data', () => {
      // Add old failure patterns
      const oldPatterns = [
        { timestamp: Date.now() - (25 * 60 * 60 * 1000), type: 'trip' }, // 25 hours ago
        { timestamp: Date.now() - (1 * 60 * 60 * 1000), type: 'trip' }   // 1 hour ago
      ];
      monitoringService.metrics.failurePatterns.set('testService', oldPatterns);

      monitoringService.cleanupOldMetrics();

      const patterns = monitoringService.metrics.failurePatterns.get('testService');
      expect(patterns).toHaveLength(1); // Only recent pattern should remain
    });

    test('should shutdown gracefully', async () => {
      await monitoringService.shutdown();

      expect(monitoringService.isInitialized).toBe(false);
      expect(monitoringService.monitoringInterval).toBe(null);
    });
  });

  describe('State Transition Tracking', () => {
    test('should record state changes', () => {
      monitoringService.recordStateChange('testService', 'OPEN', 'failure threshold reached');

      const history = monitoringService.stateHistory.get('testService');
      expect(history).toBeDefined();
      expect(history.length).toBeGreaterThan(0);
      expect(history[history.length - 1].state).toBe('OPEN');
      expect(history[history.length - 1].reason).toBe('failure threshold reached');
    });

    test('should get last known state', () => {
      monitoringService.recordStateChange('testService', 'CLOSED', 'initialization');
      monitoringService.recordStateChange('testService', 'OPEN', 'failure');

      const lastState = monitoringService.getLastKnownState('testService');
      expect(lastState).toBeDefined();
      expect(lastState.state).toBe('OPEN');
      expect(lastState.reason).toBe('failure');
    });

    test('should calculate uptime correctly', () => {
      // Add some state history
      const history = [
        { timestamp: Date.now() - 10000, state: 'CLOSED' },
        { timestamp: Date.now() - 8000, state: 'OPEN' },
        { timestamp: Date.now() - 5000, state: 'CLOSED' },
        { timestamp: Date.now(), state: 'CLOSED' }
      ];
      monitoringService.stateHistory.set('testService', history);

      const uptime = monitoringService.calculateUptime('testService');
      expect(uptime).toBeGreaterThan(0);
      expect(uptime).toBeLessThanOrEqual(100);
    });
  });

  describe('Health Score Calculation', () => {
    test('should calculate health score based on state and metrics', () => {
      const state = {
        state: 'CLOSED',
        metrics: {
          totalRequests: 100,
          totalFailures: 10,
          totalSuccesses: 90
        }
      };

      monitoringService.calculateHealthScore('testService', state);
      const healthScore = monitoringService.metrics.healthScores.get('testService');

      expect(healthScore).toBeGreaterThan(0);
      expect(healthScore).toBeLessThanOrEqual(100);
    });

    test('should penalize open circuit breakers', () => {
      const openState = {
        state: 'OPEN',
        metrics: {
          totalRequests: 100,
          totalFailures: 50,
          totalSuccesses: 50
        }
      };

      monitoringService.calculateHealthScore('testService', openState);
      const healthScore = monitoringService.metrics.healthScores.get('testService');

      expect(healthScore).toBeLessThan(50); // Should be heavily penalized
    });
  });
});
