const { createAdapter } = require('@socket.io/redis-adapter');
const { createClient } = require('redis');
const logger = require('../utils/logger');

class SocketRedisAdapter {
  constructor() {
    this.pubClient = null;
    this.subClient = null;
    this.adapter = null;
    this.isInitialized = false;
  }

  /**
   * Initialize Redis adapter for Socket.IO
   */
  async initialize() {
    try {
      // Create Redis clients for pub/sub
      const redisConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        db: process.env.REDIS_DB || 0,
        retryDelayOnFailover: 100,
        enableOfflineQueue: false,
        maxRetriesPerRequest: 3,
        connectTimeout: 10000,
        lazyConnect: true
      };

      // Create pub client
      this.pubClient = createClient(redisConfig);
      this.pubClient.on('error', (err) => {
        logger.error('Redis pub client error:', err);
      });
      this.pubClient.on('connect', () => {
        logger.info('Redis pub client connected');
      });

      // Create sub client
      this.subClient = this.pubClient.duplicate();
      this.subClient.on('error', (err) => {
        logger.error('Redis sub client error:', err);
      });
      this.subClient.on('connect', () => {
        logger.info('Redis sub client connected');
      });

      // Connect both clients
      await Promise.all([
        this.pubClient.connect(),
        this.subClient.connect()
      ]);

      // Create the adapter
      this.adapter = createAdapter(this.pubClient, this.subClient, {
        key: 'kryptopesa:socket.io',
        requestsTimeout: 5000
      });

      this.isInitialized = true;
      logger.info('Socket.IO Redis adapter initialized successfully');
      
      return this.adapter;
    } catch (error) {
      logger.error('Failed to initialize Socket.IO Redis adapter:', error);
      this.isInitialized = false;
      throw error;
    }
  }

  /**
   * Get the adapter instance
   */
  getAdapter() {
    if (!this.isInitialized) {
      throw new Error('Redis adapter not initialized');
    }
    return this.adapter;
  }

  /**
   * Publish message to specific room across all server instances
   */
  async publishToRoom(room, event, data) {
    try {
      if (!this.pubClient) {
        throw new Error('Redis pub client not available');
      }

      const message = JSON.stringify({
        type: 'room_message',
        room,
        event,
        data,
        timestamp: new Date().toISOString(),
        serverId: process.env.SERVER_ID || 'unknown'
      });

      await this.pubClient.publish(`kryptopesa:room:${room}`, message);
      logger.debug(`Published message to room ${room}: ${event}`);
    } catch (error) {
      logger.error(`Failed to publish to room ${room}:`, error);
    }
  }

  /**
   * Publish message to specific user across all server instances
   */
  async publishToUser(userId, event, data) {
    try {
      if (!this.pubClient) {
        throw new Error('Redis pub client not available');
      }

      const message = JSON.stringify({
        type: 'user_message',
        userId,
        event,
        data,
        timestamp: new Date().toISOString(),
        serverId: process.env.SERVER_ID || 'unknown'
      });

      await this.pubClient.publish(`kryptopesa:user:${userId}`, message);
      logger.debug(`Published message to user ${userId}: ${event}`);
    } catch (error) {
      logger.error(`Failed to publish to user ${userId}:`, error);
    }
  }

  /**
   * Subscribe to room messages
   */
  async subscribeToRoomMessages(io) {
    try {
      if (!this.subClient) {
        throw new Error('Redis sub client not available');
      }

      // Subscribe to room message pattern
      await this.subClient.pSubscribe('kryptopesa:room:*', (message, channel) => {
        try {
          const data = JSON.parse(message);
          const room = channel.replace('kryptopesa:room:', '');
          
          // Don't process messages from the same server instance
          if (data.serverId === (process.env.SERVER_ID || 'unknown')) {
            return;
          }

          // Emit to local room
          io.to(room).emit(data.event, data.data);
          logger.debug(`Relayed room message from ${data.serverId} to room ${room}: ${data.event}`);
        } catch (error) {
          logger.error('Failed to process room message:', error);
        }
      });

      logger.info('Subscribed to room messages');
    } catch (error) {
      logger.error('Failed to subscribe to room messages:', error);
    }
  }

  /**
   * Subscribe to user messages
   */
  async subscribeToUserMessages(io) {
    try {
      if (!this.subClient) {
        throw new Error('Redis sub client not available');
      }

      // Subscribe to user message pattern
      await this.subClient.pSubscribe('kryptopesa:user:*', (message, channel) => {
        try {
          const data = JSON.parse(message);
          const userId = channel.replace('kryptopesa:user:', '');
          
          // Don't process messages from the same server instance
          if (data.serverId === (process.env.SERVER_ID || 'unknown')) {
            return;
          }

          // Emit to specific user
          io.to(`user:${userId}`).emit(data.event, data.data);
          logger.debug(`Relayed user message from ${data.serverId} to user ${userId}: ${data.event}`);
        } catch (error) {
          logger.error('Failed to process user message:', error);
        }
      });

      logger.info('Subscribed to user messages');
    } catch (error) {
      logger.error('Failed to subscribe to user messages:', error);
    }
  }

  /**
   * Get connection statistics across all server instances
   */
  async getGlobalStats() {
    try {
      if (!this.pubClient) {
        return null;
      }

      // Request stats from all server instances
      const statsRequest = {
        type: 'stats_request',
        requestId: `stats_${Date.now()}`,
        timestamp: new Date().toISOString(),
        serverId: process.env.SERVER_ID || 'unknown'
      };

      await this.pubClient.publish('kryptopesa:stats:request', JSON.stringify(statsRequest));
      
      // In a real implementation, you would collect responses and aggregate them
      // For now, return local stats
      return {
        serverId: process.env.SERVER_ID || 'unknown',
        timestamp: new Date().toISOString(),
        localConnections: 0, // This would be populated by the Socket.IO server
        rooms: 0,
        adapters: this.isInitialized ? 1 : 0
      };
    } catch (error) {
      logger.error('Failed to get global stats:', error);
      return null;
    }
  }

  /**
   * Broadcast system message to all connected users
   */
  async broadcastSystemMessage(event, data) {
    try {
      if (!this.pubClient) {
        throw new Error('Redis pub client not available');
      }

      const message = JSON.stringify({
        type: 'system_broadcast',
        event,
        data,
        timestamp: new Date().toISOString(),
        serverId: process.env.SERVER_ID || 'unknown'
      });

      await this.pubClient.publish('kryptopesa:system:broadcast', message);
      logger.info(`Broadcasted system message: ${event}`);
    } catch (error) {
      logger.error('Failed to broadcast system message:', error);
    }
  }

  /**
   * Subscribe to system broadcasts
   */
  async subscribeToSystemBroadcasts(io) {
    try {
      if (!this.subClient) {
        throw new Error('Redis sub client not available');
      }

      await this.subClient.subscribe('kryptopesa:system:broadcast', (message) => {
        try {
          const data = JSON.parse(message);
          
          // Don't process messages from the same server instance
          if (data.serverId === (process.env.SERVER_ID || 'unknown')) {
            return;
          }

          // Broadcast to all connected sockets
          io.emit(data.event, data.data);
          logger.info(`Relayed system broadcast from ${data.serverId}: ${data.event}`);
        } catch (error) {
          logger.error('Failed to process system broadcast:', error);
        }
      });

      logger.info('Subscribed to system broadcasts');
    } catch (error) {
      logger.error('Failed to subscribe to system broadcasts:', error);
    }
  }

  /**
   * Cleanup and disconnect
   */
  async cleanup() {
    try {
      if (this.pubClient) {
        await this.pubClient.quit();
      }
      if (this.subClient) {
        await this.subClient.quit();
      }
      
      this.isInitialized = false;
      logger.info('Socket.IO Redis adapter cleaned up');
    } catch (error) {
      logger.error('Failed to cleanup Redis adapter:', error);
    }
  }

  /**
   * Health check for Redis connections
   */
  async healthCheck() {
    try {
      if (!this.pubClient || !this.subClient) {
        return { healthy: false, error: 'Redis clients not initialized' };
      }

      // Test both connections
      await Promise.all([
        this.pubClient.ping(),
        this.subClient.ping()
      ]);

      return {
        healthy: true,
        pubClient: 'connected',
        subClient: 'connected',
        adapter: this.isInitialized ? 'initialized' : 'not_initialized'
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        pubClient: this.pubClient ? 'error' : 'not_initialized',
        subClient: this.subClient ? 'error' : 'not_initialized'
      };
    }
  }
}

// Create singleton instance
const socketRedisAdapter = new SocketRedisAdapter();

module.exports = socketRedisAdapter;
