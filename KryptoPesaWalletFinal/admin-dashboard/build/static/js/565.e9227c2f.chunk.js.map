{"version": 3, "file": "static/js/565.e9227c2f.chunk.js", "mappings": "+KAIA,SAAeA,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,4EACD,a,6QCHJ,MAAMC,E,QAAMC,EAAMC,OAAO,CACvBC,QACI,iCAEJC,QAAS,IACTC,QAAS,CACP,eAAgB,sBAKpBL,EAAIM,aAAaC,QAAQC,IACtBC,IACC,MAAMC,EAAQC,aAAaC,QAAQ,eAInC,OAHIF,IACFD,EAAOJ,QAAQQ,cAAa,UAAAC,OAAaJ,IAEpCD,GAERM,GACQC,QAAQC,OAAOF,IAK1Bf,EAAIM,aAAaY,SAASV,IACvBU,GACQA,EAERH,IAAW,IAADI,EAMT,OAL+B,OAAb,QAAdA,EAAAJ,EAAMG,gBAAQ,IAAAC,OAAA,EAAdA,EAAgBC,UAElBT,aAAaU,WAAW,eACxBC,OAAOC,SAASC,KAAO,UAElBR,QAAQC,OAAOF,KAKnB,MAAMU,EASA,CACTC,SAAUA,IAAM1B,EAAI2B,IAAI,0BACxBC,mBAAoBA,IAAM5B,EAAI2B,IAAI,qBAClCE,sBAAuBA,IAAM7B,EAAI2B,IAAI,yBA4KlC,MAAMG,EAAiB,IA9GvB,MACLC,WAAAA,GACEC,KAAKC,OAAS,KACdD,KAAKE,UAAY,IAAIC,IACrBH,KAAKI,kBAAoB,EACzBJ,KAAKK,qBAAuB,CAC9B,CAEAC,OAAAA,GACE,MAAM5B,EAAQC,aAAaC,QAAQ,eACnC,IAAKF,EAEH,YADA6B,QAAQxB,MAAM,qDAQhB,IACEiB,KAAKC,OAAS,IAAIO,UAAU,GAAD1B,OAJzB,2BAIkC,WAAAA,OAAUJ,IAE9CsB,KAAKC,OAAOQ,OAAS,KACnBF,QAAQG,IAAI,6BACZV,KAAKI,kBAAoB,EACzBJ,KAAKW,KAAK,cAGZX,KAAKC,OAAOW,UAAaC,IACvB,IACE,MAAMC,EAAOC,KAAKC,MAAMH,EAAMC,MAC9Bd,KAAKW,KAAKG,EAAKG,KAAMH,EAAKI,QAC5B,CAAE,MAAOnC,GACPwB,QAAQxB,MAAM,mCAAoCA,EACpD,GAGFiB,KAAKC,OAAOkB,QAAU,KACpBZ,QAAQG,IAAI,gCACZV,KAAKW,KAAK,gBACVX,KAAKoB,oBAGPpB,KAAKC,OAAOoB,QAAWtC,IACrBwB,QAAQxB,MAAM,yBAA0BA,GACxCiB,KAAKW,KAAK,QAAS5B,GAEvB,CAAE,MAAOA,GACPwB,QAAQxB,MAAM,uCAAwCA,EACxD,CACF,CAEAuC,UAAAA,GACMtB,KAAKC,SACPD,KAAKC,OAAOsB,QACZvB,KAAKC,OAAS,KAElB,CAEAmB,gBAAAA,GACE,GAAIpB,KAAKI,kBAAoBJ,KAAKK,qBAAsB,CACtDL,KAAKI,oBACL,MAAMoB,EAA8C,IAAtCC,KAAKC,IAAI,EAAG1B,KAAKI,mBAE/BuB,WAAW,KACTpB,QAAQG,IAAI,sCAAD5B,OAAuCkB,KAAKI,kBAAiB,KAAAtB,OAAIkB,KAAKK,qBAAoB,MACrGL,KAAKM,WACJkB,EACL,CACF,CAEAI,EAAAA,CAAGf,EAAOgB,GACH7B,KAAKE,UAAU4B,IAAIjB,IACtBb,KAAKE,UAAU6B,IAAIlB,EAAO,IAE5Bb,KAAKE,UAAUP,IAAIkB,GAAOmB,KAAKH,EACjC,CAEAI,GAAAA,CAAIpB,EAAOgB,GACT,GAAI7B,KAAKE,UAAU4B,IAAIjB,GAAQ,CAC7B,MAAMqB,EAAYlC,KAAKE,UAAUP,IAAIkB,GAC/BsB,EAAQD,EAAUE,QAAQP,GAC5BM,GAAS,GACXD,EAAUG,OAAOF,EAAO,EAE5B,CACF,CAEAxB,IAAAA,CAAKE,EAAOC,GACNd,KAAKE,UAAU4B,IAAIjB,IACrBb,KAAKE,UAAUP,IAAIkB,GAAOyB,QAAQT,IAChC,IACEA,EAASf,EACX,CAAE,MAAO/B,GACPwB,QAAQxB,MAAM,qCAAsCA,EACtD,GAGN,CAEAwD,IAAAA,CAAKzB,GACCd,KAAKC,QAAUD,KAAKC,OAAOuC,aAAehC,UAAUiC,KACtDzC,KAAKC,OAAOsC,KAAKxB,KAAK2B,UAAU5B,IAEhCP,QAAQoC,KAAK,4CAEjB,G,aCpMF,MAAMC,EAAWC,EAAAA,KAAWC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,KAAEC,EAAI,MAAEC,EAAQ,UAAS,OAAEC,GAAQL,EAAA,OAC5EhF,EAAAA,EAAAA,KAACsF,EAAAA,EAAI,CAAAC,UACHvF,EAAAA,EAAAA,KAACwF,EAAAA,EAAW,CAAAD,UACVE,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACC,QAAQ,OAAOC,WAAW,SAASC,eAAe,gBAAeN,SAAA,EACpEE,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAH,SAAA,EACFvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACV,MAAM,gBAAgBW,cAAY,EAACC,QAAQ,QAAOT,SAC3DN,KAEHjF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,KAAKC,UAAU,MAAKV,SACrCL,SAESgB,IAAXb,IACCI,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACC,QAAQ,OAAOC,WAAW,SAASO,GAAI,EAAEZ,SAAA,EAC5CE,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACC,QAAQ,OAAOC,WAAW,SAAQL,SAAA,CACpCF,EAAS,IAAKrF,EAAAA,EAAAA,KAACoG,EAAAA,EAAU,CAAChB,MAAM,UAAUiB,SAAS,UACnDhB,EAAS,IAAKrF,EAAAA,EAAAA,KAACsG,EAAAA,EAAY,CAAClB,MAAM,QAAQiB,SAAS,WACpDZ,EAAAA,EAAAA,MAACK,EAAAA,EAAU,CACTE,QAAQ,QACRO,GAAI,CACFC,GAAI,GACJpB,MAAOC,EAAS,EAAI,eAAiBA,EAAS,EAAI,aAAe,kBACjEE,SAAA,CAEDF,EAAS,EAAI,IAAM,GAAIA,EAAO,WAGnCrF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,QAAQZ,MAAM,gBAAgBmB,GAAI,CAAEC,GAAI,GAAIjB,SAAC,wBAMvEvF,EAAAA,EAAAA,KAAC0F,EAAAA,EAAG,CACFa,GAAI,CACFE,gBAAgB,GAADzF,OAAKoE,EAAK,UACzBsB,aAAc,MACdC,EAAG,EACHhB,QAAS,OACTC,WAAY,SACZC,eAAgB,UAChBN,SAEDJ,aA8UX,EAvUsByB,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC1B,MAAOC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,OAC5BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChChH,EAAOmH,IAAYH,EAAAA,EAAAA,UAAS,MAG7BI,GAAaC,EAAAA,EAAAA,aAAYC,UAC7B,IACEJ,GAAW,GACX,MAAM/G,QAAiBO,EAAmBC,WAEtCR,EAAS4B,KAAKwF,SAChBR,EAAS5G,EAAS4B,KAAKA,KAE3B,CAAE,MAAO/B,GACHwH,EAGJL,EAAS,uCACX,CAAC,QACCD,GAAW,EACb,GACC,KAEHO,EAAAA,EAAAA,WAAU,KACRL,IAGA,MAAMM,EAAqB3F,IACzBgF,EAASY,IAASC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbD,GACA5F,KAIPhB,EAAe8B,GAAG,yBAA0B6E,GAC5C3G,EAAe8B,GAAG,eAAgBuE,GAClCrG,EAAe8B,GAAG,cAAeuE,GAGjC,MAAMS,EAAWC,YAAYV,EAAY,KAEzC,MAAO,KACLW,cAAcF,GACd9G,EAAemC,IAAI,yBAA0BwE,GAC7C3G,EAAemC,IAAI,eAAgBkE,GACnCrG,EAAemC,IAAI,cAAekE,KAEnC,CAACA,IAGJ,MAAMY,GAAeX,EAAAA,EAAAA,aAAaY,GAC5BA,GAAO,IACH,GAANlI,QAAWkI,EAAM,KAASC,QAAQ,GAAE,KAElCD,GAAO,IACH,GAANlI,QAAWkI,EAAM,KAAMC,QAAQ,GAAE,MAEzB,OAAHD,QAAG,IAAHA,OAAG,EAAHA,EAAKE,mBAAoB,IAC/B,IAEGC,GAAiBf,EAAAA,EAAAA,aAAagB,GAC5B,IAANtI,OAAWiI,EAAaK,IACvB,CAACL,IAGEM,GAAiBC,EAAAA,EAAAA,SAAQ,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACnC,OAAKzC,EAEE,CACL0C,WAAYxB,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZ0B,EAAL1B,EAAO2C,kBAAU,IAAAjB,GAAO,QAAPC,EAAjBD,EAAmBkB,aAAK,IAAAjB,OAAnB,EAALA,EAA0BkB,OACnDC,aAAc5B,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZ4B,EAAL5B,EAAO2C,kBAAU,IAAAf,GAAQ,QAARC,EAAjBD,EAAmBmB,cAAM,IAAAlB,OAApB,EAALA,EAA2BmB,QACtDC,gBAAiB/B,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZ8B,EAAL9B,EAAO2C,kBAAU,IAAAb,GAAU,QAAVC,EAAjBD,EAAmBoB,gBAAQ,IAAAnB,OAAtB,EAALA,EAA6BoB,SAC3DC,YAAa9B,EAAoB,OAALtB,QAAK,IAALA,GAAiB,QAAZgC,EAALhC,EAAO2C,kBAAU,IAAAX,GAAQ,QAARC,EAAjBD,EAAmBe,cAAM,IAAAd,OAApB,EAALA,EAA2BoB,QACvDC,YAAapC,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZkC,EAALlC,EAAO2C,kBAAU,IAAAT,GAAQ,QAARC,EAAjBD,EAAmBqB,cAAM,IAAApB,OAApB,EAALA,EAA2BU,OACrDW,gBAAiBtC,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZoC,EAALpC,EAAO2C,kBAAU,IAAAP,GAAQ,QAARC,EAAjBD,EAAmBW,cAAM,IAAAV,OAApB,EAALA,EAA2BoB,WACzDC,eAAgBxC,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZsC,EAALtC,EAAO2C,kBAAU,IAAAL,GAAO,QAAPC,EAAjBD,EAAmBM,aAAK,IAAAL,OAAnB,EAALA,EAA0BoB,WACvDC,aAAc1C,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZwC,EAALxC,EAAO2C,kBAAU,IAAAH,GAAQ,QAARC,EAAjBD,EAAmBe,cAAM,IAAAd,OAApB,EAALA,EAA2BO,SAVrC,MAYlB,CAAChD,EAAOkB,EAAcI,IAEzB,OAAInB,GAEAlI,EAAAA,EAAAA,KAAC0F,EAAAA,EAAG,CAACC,QAAQ,OAAOE,eAAe,SAASD,WAAW,SAASgG,UAAU,QAAOrG,UAC/EvF,EAAAA,EAAAA,KAAC6L,EAAAA,EAAgB,MAKnB5K,GAEAwE,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAH,SAAA,EACFvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,KAAKD,cAAY,EAAAR,SAAC,wBAGtCvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACV,MAAM,QAAOG,SAAEtE,QAM/BwE,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAH,SAAA,EACFvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,KAAKD,cAAY,EAAAR,SAAC,wBAItCE,EAAAA,EAAAA,MAACqG,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAEzG,SAAA,EAEzBvF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAE7G,UAC9BvF,EAAAA,EAAAA,KAAC8E,EAAQ,CACPG,MAAM,cACNC,MAAqB,OAAdqE,QAAc,IAAdA,OAAc,EAAdA,EAAgBkB,WACvBpF,OAAa,OAAL0C,QAAK,IAALA,GAAiB,QAAZlB,EAALkB,EAAO2C,kBAAU,IAAA7D,GAAO,QAAPC,EAAjBD,EAAmB8D,aAAK,IAAA7D,OAAnB,EAALA,EAA0BzB,OAClCF,MAAMnF,EAAAA,EAAAA,KAACqM,EAAAA,EAAM,IACbjH,MAAM,eAIVpF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAE7G,UAC9BvF,EAAAA,EAAAA,KAAC8E,EAAQ,CACPG,MAAM,gBACNC,MAAqB,OAAdqE,QAAc,IAAdA,OAAc,EAAdA,EAAgBsB,aACvBxF,OAAa,OAAL0C,QAAK,IAALA,GAAiB,QAAZhB,EAALgB,EAAO2C,kBAAU,IAAA3D,GAAQ,QAARC,EAAjBD,EAAmB+D,cAAM,IAAA9D,OAApB,EAALA,EAA2B3B,OACnCF,MAAMnF,EAAAA,EAAAA,KAACsM,EAAAA,EAAS,IAChBlH,MAAM,eAIVpF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAE7G,UAC9BvF,EAAAA,EAAAA,KAAC8E,EAAQ,CACPG,MAAM,mBACNC,MAAqB,OAAdqE,QAAc,IAAdA,OAAc,EAAdA,EAAgByB,gBACvB7F,MAAMnF,EAAAA,EAAAA,KAACuM,EAAAA,EAAK,IACZnH,MAAM,eAIVpF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAE7G,UAC9BvF,EAAAA,EAAAA,KAAC8E,EAAQ,CACPG,MAAM,eACNC,MAAqB,OAAdqE,QAAc,IAAdA,OAAc,EAAdA,EAAgB4B,YACvB9F,OAAa,OAAL0C,QAAK,IAALA,GAAiB,QAAZd,EAALc,EAAO2C,kBAAU,IAAAzD,GAAQ,QAARC,EAAjBD,EAAmB6D,cAAM,IAAA5D,OAApB,EAALA,EAA2BsF,aACnCrH,MAAMnF,EAAAA,EAAAA,KAACoG,EAAAA,EAAU,IACjBhB,MAAM,YAIVpF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAE7G,UAC9BvF,EAAAA,EAAAA,KAAC8E,EAAQ,CACPG,MAAM,eACNC,MAAO+D,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZZ,EAALY,EAAO2C,kBAAU,IAAAvD,GAAQ,QAARC,EAAjBD,EAAmBmE,cAAM,IAAAlE,OAApB,EAALA,EAA2BwD,OAC/CzF,MAAMnF,EAAAA,EAAAA,KAACyM,EAAAA,EAAU,IACjBrH,MAAM,iBAIVpF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAE7G,UAC9BvF,EAAAA,EAAAA,KAAC8E,EAAQ,CACPG,MAAM,mBACNC,MAAO+D,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZV,EAALU,EAAO2C,kBAAU,IAAArD,GAAQ,QAARC,EAAjBD,EAAmByD,cAAM,IAAAxD,OAApB,EAALA,EAA2BkE,WAC/CrG,MAAMnF,EAAAA,EAAAA,KAACsM,EAAAA,EAAS,IAChBlH,MAAM,eAIVpF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAE7G,UAC9BvF,EAAAA,EAAAA,KAAC8E,EAAQ,CACPG,MAAM,mBACNC,MAAO+D,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZR,EAALQ,EAAO2C,kBAAU,IAAAnD,GAAO,QAAPC,EAAjBD,EAAmBoD,aAAK,IAAAnD,OAAnB,EAALA,EAA0BkE,WAC9CvG,MAAMnF,EAAAA,EAAAA,KAACqM,EAAAA,EAAM,IACbjH,MAAM,eAIVpF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAE7G,UAC9BvF,EAAAA,EAAAA,KAAC8E,EAAQ,CACPG,MAAM,gBACNC,MAAO+D,EAAkB,OAALlB,QAAK,IAALA,GAAiB,QAAZN,EAALM,EAAO2C,kBAAU,IAAAjD,GAAQ,QAARC,EAAjBD,EAAmB6D,cAAM,IAAA5D,OAApB,EAALA,EAA2BqD,QAC/C5F,MAAMnF,EAAAA,EAAAA,KAACyM,EAAAA,EAAU,IACjBrH,MAAM,iBAKVpF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAE7G,UACvBE,EAAAA,EAAAA,MAACiH,EAAAA,EAAK,CAACnG,GAAI,CAAEI,EAAG,GAAIpB,SAAA,EAClBvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,KAAKD,cAAY,EAAAR,SAAC,qBAGtCvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACV,MAAM,gBAAeG,SAAC,yDAOtCvF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAE7G,UACvBE,EAAAA,EAAAA,MAACiH,EAAAA,EAAK,CAACnG,GAAI,CAAEI,EAAG,GAAIpB,SAAA,EAClBvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,KAAKD,cAAY,EAAAR,SAAC,mBAGtCvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACV,MAAM,gBAAeG,SAAC,yDAOtCvF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAG3G,UAChBE,EAAAA,EAAAA,MAACiH,EAAAA,EAAK,CAACnG,GAAI,CAAEI,EAAG,GAAIpB,SAAA,EAClBvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,KAAKD,cAAY,EAAAR,SAAC,0BAGtCvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACV,MAAM,gBAAeG,SAAC,yDAOtCvF,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIS,GAAI,EAAEpH,UACvBE,EAAAA,EAAAA,MAACiH,EAAAA,EAAK,CAACnG,GAAI,CAAEI,EAAG,GAAIpB,SAAA,EAClBvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,KAAKD,cAAY,EAAAR,SAAC,mBAGtCvF,EAAAA,EAAAA,KAAC4M,EAAAA,EAAc,CAAArH,UACbE,EAAAA,EAAAA,MAACoH,EAAAA,EAAK,CAACC,KAAK,QAAOvH,SAAA,EACjBvF,EAAAA,EAAAA,KAAC+M,EAAAA,EAAS,CAAAxH,UACRE,EAAAA,EAAAA,MAACuH,EAAAA,EAAQ,CAAAzH,SAAA,EACPvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,SAAC,cACXvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,SAAC,YACXvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,SAAC,YACXvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,SAAC,eAGfvF,EAAAA,EAAAA,KAACkN,EAAAA,EAAS,CAAA3H,SACF,OAALwC,QAAK,IAALA,GAAqB,QAAhBJ,EAALI,EAAOoF,sBAAc,IAAAxF,GAAQ,QAARC,EAArBD,EAAuBmD,cAAM,IAAAlD,OAAxB,EAALA,EAA+BwF,MAAM,EAAG,GAAGC,IAAKC,IAAK,IAAAC,EAAAC,EAAAC,EAAA,OACpDhI,EAAAA,EAAAA,MAACuH,EAAAA,EAAQ,CAAAzH,SAAA,EACPvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,UACRE,EAAAA,EAAAA,MAACK,EAAAA,EAAU,CAACE,QAAQ,QAAQ0H,WAAW,YAAWnI,SAAA,CAClC,QADkCgI,EAC/CD,EAAMK,eAAO,IAAAJ,OAAA,EAAbA,EAAeK,UAAU,EAAG,GAAG,YAGpC5N,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,UACRE,EAAAA,EAAAA,MAACK,EAAAA,EAAU,CAACE,QAAQ,QAAOT,SAAA,CACJ,QADIiI,EACxBF,EAAMO,sBAAc,IAAAL,OAAA,EAApBA,EAAsBlE,OAAO,IAAsB,QAArBmE,EAACH,EAAMO,sBAAc,IAAAJ,OAAA,EAApBA,EAAsBK,aAG1D9N,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,UACRvF,EAAAA,EAAAA,KAAC+N,EAAAA,EAAI,CACHC,MAAOV,EAAMhM,OACbwL,KAAK,QACL1H,MACmB,cAAjBkI,EAAMhM,OAAyB,UACd,aAAjBgM,EAAMhM,OAAwB,UACb,cAAjBgM,EAAMhM,OAAyB,QAAU,eAI/CtB,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,UACRvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,QAAOT,SACxB,IAAI0I,KAAKX,EAAMY,WAAWC,2BAxBlBb,EAAMc,oBAmCjCpO,EAAAA,EAAAA,KAAC8L,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIS,GAAI,EAAEpH,UACvBE,EAAAA,EAAAA,MAACiH,EAAAA,EAAK,CAACnG,GAAI,CAAEI,EAAG,GAAIpB,SAAA,EAClBvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,KAAKD,cAAY,EAAAR,SAAC,qBAGtCvF,EAAAA,EAAAA,KAAC4M,EAAAA,EAAc,CAAArH,UACbE,EAAAA,EAAAA,MAACoH,EAAAA,EAAK,CAACC,KAAK,QAAOvH,SAAA,EACjBvF,EAAAA,EAAAA,KAAC+M,EAAAA,EAAS,CAAAxH,UACRE,EAAAA,EAAAA,MAACuH,EAAAA,EAAQ,CAAAzH,SAAA,EACPvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,SAAC,gBACXvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,SAAC,WACXvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,SAAC,YACXvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,SAAC,eAGfvF,EAAAA,EAAAA,KAACkN,EAAAA,EAAS,CAAA3H,SACF,OAALwC,QAAK,IAALA,GAAqB,QAAhBF,EAALE,EAAOoF,sBAAc,IAAAtF,GAAU,QAAVC,EAArBD,EAAuBoD,gBAAQ,IAAAnD,OAA1B,EAALA,EAAiCsF,MAAM,EAAG,GAAGC,IAAKgB,IAAO,IAAAC,EAAAC,EAAAC,EAAA,OACxD/I,EAAAA,EAAAA,MAACuH,EAAAA,EAAQ,CAAAzH,SAAA,EACPvF,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,UACRE,EAAAA,EAAAA,MAACK,EAAAA,EAAU,CAACE,QAAQ,QAAQ0H,WAAW,YAAWnI,SAAA,CAC9B,QAD8B+I,EAC/CD,EAAQI,iBAAS,IAAAH,OAAA,EAAjBA,EAAmBV,UAAU,EAAG,GAAG,YAGxC5N,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,UACRE,EAAAA,EAAAA,MAACK,EAAAA,EAAU,CAACE,QAAQ,QAAQ0H,WAAW,YAAWnI,SAAA,CAClC,QADkCgJ,EAC/CF,EAAQf,aAAK,IAAAiB,GAAS,QAATC,EAAbD,EAAeZ,eAAO,IAAAa,OAAT,EAAbA,EAAwBZ,UAAU,EAAG,GAAG,YAG7C5N,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,UACRvF,EAAAA,EAAAA,KAAC+N,EAAAA,EAAI,CACHC,MAAOK,EAAQ/M,OACfwL,KAAK,QACL1H,MACqB,aAAnBiJ,EAAQ/M,OAAwB,UACb,YAAnB+M,EAAQ/M,OAAuB,UAAY,eAIjDtB,EAAAA,EAAAA,KAACiN,EAAAA,EAAS,CAAA1H,UACRvF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAU,CAACE,QAAQ,QAAOT,SACxB,IAAI0I,KAAKI,EAAQH,WAAWC,2BAvBpBE,EAAQD,yB,yDCzW3C,SAAerO,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,2EACD,e", "sources": ["../node_modules/@mui/icons-material/esm/TrendingUp.js", "services/api.js", "pages/DashboardPage.js", "../node_modules/@mui/icons-material/esm/TrendingDown.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z\"\n}), 'TrendingUp');", "import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' \n    ? 'https://api.kryptopesa.com/api'\n    : 'http://localhost:3000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('admin_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for error handling\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('admin_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// API endpoints\nexport const adminAPI = {\n  // Authentication\n  auth: {\n    login: (credentials) => api.post('/auth/login', credentials),\n    logout: () => api.post('/auth/logout'),\n    getCurrentUser: () => api.get('/auth/me'),\n  },\n\n  // Dashboard\n  dashboard: {\n    getStats: () => api.get('/admin/dashboard/stats'),\n    getRealtimeMetrics: () => api.get('/metrics/realtime'),\n    getApplicationMetrics: () => api.get('/metrics/application'),\n  },\n\n  // User Management\n  users: {\n    getUsers: (params) => api.get('/admin/users', { params }),\n    getUserById: (id) => api.get(`/admin/users/${id}`),\n    updateUser: (id, data) => api.put(`/admin/users/${id}`, data),\n    suspendUser: (id, reason) => api.post(`/admin/users/${id}/suspend`, { reason }),\n    unsuspendUser: (id) => api.post(`/admin/users/${id}/unsuspend`),\n    banUser: (id, reason) => api.post(`/admin/users/${id}/ban`, { reason }),\n    unbanUser: (id) => api.post(`/admin/users/${id}/unban`),\n    verifyUser: (id) => api.post(`/admin/users/${id}/verify`),\n    unverifyUser: (id) => api.post(`/admin/users/${id}/unverify`),\n  },\n\n  // Trade Management\n  trades: {\n    getTrades: (params) => api.get('/admin/trades', { params }),\n    getTradeById: (id) => api.get(`/admin/trades/${id}`),\n    updateTrade: (id, data) => api.put(`/admin/trades/${id}`, data),\n    cancelTrade: (id, reason) => api.post(`/admin/trades/${id}/cancel`, { reason }),\n    completeTrade: (id) => api.post(`/admin/trades/${id}/complete`),\n  },\n\n  // Offer Management\n  offers: {\n    getOffers: (params) => api.get('/admin/offers', { params }),\n    getOfferById: (id) => api.get(`/admin/offers/${id}`),\n    updateOffer: (id, data) => api.put(`/admin/offers/${id}`, data),\n    deactivateOffer: (id, reason) => api.post(`/admin/offers/${id}/deactivate`, { reason }),\n    activateOffer: (id) => api.post(`/admin/offers/${id}/activate`),\n  },\n\n  // Dispute Management\n  disputes: {\n    getDisputes: (params) => api.get('/admin/disputes', { params }),\n    getDisputeById: (id) => api.get(`/admin/disputes/${id}`),\n    updateDispute: (id, data) => api.put(`/admin/disputes/${id}`, data),\n    assignDispute: (id, adminId) => api.post(`/admin/disputes/${id}/assign`, { adminId }),\n    resolveDispute: (id, resolution) => api.post(`/admin/disputes/${id}/resolve`, resolution),\n    escalateDispute: (id, reason) => api.post(`/admin/disputes/${id}/escalate`, { reason }),\n  },\n\n  // System Management\n  system: {\n    getSystemHealth: () => api.get('/health/detailed'),\n    getMetrics: () => api.get('/metrics'),\n    getPrometheusMetrics: () => api.get('/metrics/prometheus'),\n    resetMetrics: () => api.post('/metrics/reset'),\n  },\n\n  // Analytics\n  analytics: {\n    getUserAnalytics: (params) => api.get('/admin/analytics/users', { params }),\n    getTradeAnalytics: (params) => api.get('/admin/analytics/trades', { params }),\n    getRevenueAnalytics: (params) => api.get('/admin/analytics/revenue', { params }),\n    getPerformanceAnalytics: (params) => api.get('/admin/analytics/performance', { params }),\n  },\n};\n\n// WebSocket connection for real-time updates\nexport class AdminWebSocket {\n  constructor() {\n    this.socket = null;\n    this.listeners = new Map();\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n  }\n\n  connect() {\n    const token = localStorage.getItem('admin_token');\n    if (!token) {\n      console.error('No admin token available for WebSocket connection');\n      return;\n    }\n\n    const wsUrl = process.env.NODE_ENV === 'production'\n      ? 'wss://api.kryptopesa.com'\n      : 'ws://localhost:3000';\n\n    try {\n      this.socket = new WebSocket(`${wsUrl}?token=${token}`);\n\n      this.socket.onopen = () => {\n        console.log('Admin WebSocket connected');\n        this.reconnectAttempts = 0;\n        this.emit('connected');\n      };\n\n      this.socket.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          this.emit(data.type, data.payload);\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n\n      this.socket.onclose = () => {\n        console.log('Admin WebSocket disconnected');\n        this.emit('disconnected');\n        this.attemptReconnect();\n      };\n\n      this.socket.onerror = (error) => {\n        console.error('Admin WebSocket error:', error);\n        this.emit('error', error);\n      };\n    } catch (error) {\n      console.error('Error creating WebSocket connection:', error);\n    }\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n  }\n\n  attemptReconnect() {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff\n      \n      setTimeout(() => {\n        console.log(`Attempting to reconnect WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n        this.connect();\n      }, delay);\n    }\n  }\n\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, []);\n    }\n    this.listeners.get(event).push(callback);\n  }\n\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      const callbacks = this.listeners.get(event);\n      const index = callbacks.indexOf(callback);\n      if (index > -1) {\n        callbacks.splice(index, 1);\n      }\n    }\n  }\n\n  emit(event, data) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error('Error in WebSocket event callback:', error);\n        }\n      });\n    }\n  }\n\n  send(data) {\n    if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n      this.socket.send(JSON.stringify(data));\n    } else {\n      console.warn('WebSocket not connected, cannot send data');\n    }\n  }\n}\n\n// Create singleton WebSocket instance\nexport const adminWebSocket = new AdminWebSocket();\n\n// Utility functions\nexport const formatError = (error) => {\n  if (error.response?.data?.message) {\n    return error.response.data.message;\n  }\n  if (error.message) {\n    return error.message;\n  }\n  return 'An unexpected error occurred';\n};\n\nexport const handleApiError = (error, defaultMessage = 'Operation failed') => {\n  const message = formatError(error);\n  console.error('API Error:', message);\n  return message;\n};\n\nexport default api;\n", "import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport {\n  Grid,\n  Paper,\n  Typography,\n  Box,\n  Card,\n  CardContent,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  CircularProgress,\n} from '@mui/material';\nimport {\n  People,\n  SwapHoriz,\n  Gavel,\n  TrendingUp,\n  TrendingDown,\n  LocalOffer,\n} from '@mui/icons-material';\nimport { adminAPI, adminWebSocket } from '../services/api';\n\nconst StatCard = React.memo(({ title, value, icon, color = 'primary', growth }) => (\n  <Card>\n    <CardContent>\n      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n        <Box>\n          <Typography color=\"textSecondary\" gutterBottom variant=\"body2\">\n            {title}\n          </Typography>\n          <Typography variant=\"h4\" component=\"div\">\n            {value}\n          </Typography>\n          {growth !== undefined && (\n            <Box display=\"flex\" alignItems=\"center\" mt={1}>\n              <Box display=\"flex\" alignItems=\"center\">\n                {growth > 0 && <TrendingUp color=\"success\" fontSize=\"small\" />}\n                {growth < 0 && <TrendingDown color=\"error\" fontSize=\"small\" />}\n                <Typography\n                  variant=\"body2\"\n                  sx={{\n                    ml: 0.5,\n                    color: growth > 0 ? 'success.main' : growth < 0 ? 'error.main' : 'text.secondary'\n                  }}\n                >\n                  {growth > 0 ? '+' : ''}{growth}%\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\" color=\"textSecondary\" sx={{ ml: 1 }}>\n                vs last week\n              </Typography>\n            </Box>\n          )}\n        </Box>\n        <Box\n          sx={{\n            backgroundColor: `${color}.light`,\n            borderRadius: '50%',\n            p: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n          }}\n        >\n          {icon}\n        </Box>\n      </Box>\n    </CardContent>\n  </Card>\n));\n\nconst DashboardPage = () => {\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Fetch dashboard statistics with useCallback to prevent unnecessary re-renders\n  const fetchStats = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await adminAPI.dashboard.getStats();\n\n      if (response.data.success) {\n        setStats(response.data.data);\n      }\n    } catch (error) {\n      if (process.env.NODE_ENV === 'development') {\n        console.error('Error fetching dashboard stats:', error);\n      }\n      setError('Failed to fetch dashboard statistics');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchStats();\n\n    // Set up WebSocket for real-time updates\n    const handleStatsUpdate = (data) => {\n      setStats(prevStats => ({\n        ...prevStats,\n        ...data\n      }));\n    };\n\n    adminWebSocket.on('dashboard_stats_update', handleStatsUpdate);\n    adminWebSocket.on('trade_update', fetchStats);\n    adminWebSocket.on('user_update', fetchStats);\n\n    // Refresh stats every 30 seconds as fallback\n    const interval = setInterval(fetchStats, 30000);\n\n    return () => {\n      clearInterval(interval);\n      adminWebSocket.off('dashboard_stats_update', handleStatsUpdate);\n      adminWebSocket.off('trade_update', fetchStats);\n      adminWebSocket.off('user_update', fetchStats);\n    };\n  }, [fetchStats]);\n\n  // Memoized formatting functions to prevent recalculation on every render\n  const formatNumber = useCallback((num) => {\n    if (num >= 1000000) {\n      return `${(num / 1000000).toFixed(1)}M`;\n    }\n    if (num >= 1000) {\n      return `${(num / 1000).toFixed(1)}K`;\n    }\n    return num?.toLocaleString() || '0';\n  }, []);\n\n  const formatCurrency = useCallback((amount) => {\n    return `$${formatNumber(amount)}`;\n  }, [formatNumber]);\n\n  // Memoized stats calculations to prevent unnecessary recalculations\n  const formattedStats = useMemo(() => {\n    if (!stats) return null;\n\n    return {\n      totalUsers: formatNumber(stats?.statistics?.users?.total),\n      activeTrades: formatNumber(stats?.statistics?.trades?.active),\n      pendingDisputes: formatNumber(stats?.statistics?.disputes?.pending),\n      totalVolume: formatCurrency(stats?.statistics?.trades?.volume),\n      totalOffers: formatNumber(stats?.statistics?.offers?.total),\n      completedTrades: formatNumber(stats?.statistics?.trades?.completed),\n      weeklyNewUsers: formatNumber(stats?.statistics?.users?.weeklyNew),\n      activeOffers: formatNumber(stats?.statistics?.offers?.active),\n    };\n  }, [stats, formatNumber, formatCurrency]);\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box>\n        <Typography variant=\"h4\" gutterBottom>\n          Dashboard Overview\n        </Typography>\n        <Typography color=\"error\">{error}</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Dashboard Overview\n      </Typography>\n      \n      <Grid container spacing={3}>\n        {/* Stats Cards */}\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Total Users\"\n            value={formattedStats?.totalUsers}\n            growth={stats?.statistics?.users?.growth}\n            icon={<People />}\n            color=\"primary\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Active Trades\"\n            value={formattedStats?.activeTrades}\n            growth={stats?.statistics?.trades?.growth}\n            icon={<SwapHoriz />}\n            color=\"success\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Pending Disputes\"\n            value={formattedStats?.pendingDisputes}\n            icon={<Gavel />}\n            color=\"warning\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Total Volume\"\n            value={formattedStats?.totalVolume}\n            growth={stats?.statistics?.trades?.volumeGrowth}\n            icon={<TrendingUp />}\n            color=\"info\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Total Offers\"\n            value={formatNumber(stats?.statistics?.offers?.total)}\n            icon={<LocalOffer />}\n            color=\"secondary\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Completed Trades\"\n            value={formatNumber(stats?.statistics?.trades?.completed)}\n            icon={<SwapHoriz />}\n            color=\"success\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"New Users (Week)\"\n            value={formatNumber(stats?.statistics?.users?.weeklyNew)}\n            icon={<People />}\n            color=\"primary\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Active Offers\"\n            value={formatNumber(stats?.statistics?.offers?.active)}\n            icon={<LocalOffer />}\n            color=\"secondary\"\n          />\n        </Grid>\n\n        {/* Recent Activity */}\n        <Grid item xs={12} md={8}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Recent Activity\n            </Typography>\n            <Typography color=\"textSecondary\">\n              Recent activity data will be displayed here...\n            </Typography>\n          </Paper>\n        </Grid>\n\n        {/* Quick Actions */}\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Quick Actions\n            </Typography>\n            <Typography color=\"textSecondary\">\n              Quick action buttons will be displayed here...\n            </Typography>\n          </Paper>\n        </Grid>\n\n        {/* Charts */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Trading Volume Chart\n            </Typography>\n            <Typography color=\"textSecondary\">\n              Trading volume chart will be displayed here...\n            </Typography>\n          </Paper>\n        </Grid>\n\n        {/* Recent Activity */}\n        <Grid item xs={12} lg={6}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Recent Trades\n            </Typography>\n            <TableContainer>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Trade ID</TableCell>\n                    <TableCell>Amount</TableCell>\n                    <TableCell>Status</TableCell>\n                    <TableCell>Time</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {stats?.recentActivity?.trades?.slice(0, 5).map((trade) => (\n                    <TableRow key={trade._id}>\n                      <TableCell>\n                        <Typography variant=\"body2\" fontFamily=\"monospace\">\n                          {trade.tradeId?.substring(0, 8)}...\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {trade.cryptocurrency?.amount} {trade.cryptocurrency?.symbol}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={trade.status}\n                          size=\"small\"\n                          color={\n                            trade.status === 'completed' ? 'success' :\n                            trade.status === 'disputed' ? 'warning' :\n                            trade.status === 'cancelled' ? 'error' : 'default'\n                          }\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {new Date(trade.createdAt).toLocaleDateString()}\n                        </Typography>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n\n        <Grid item xs={12} lg={6}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Recent Disputes\n            </Typography>\n            <TableContainer>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Dispute ID</TableCell>\n                    <TableCell>Trade</TableCell>\n                    <TableCell>Status</TableCell>\n                    <TableCell>Time</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {stats?.recentActivity?.disputes?.slice(0, 5).map((dispute) => (\n                    <TableRow key={dispute._id}>\n                      <TableCell>\n                        <Typography variant=\"body2\" fontFamily=\"monospace\">\n                          {dispute.disputeId?.substring(0, 8)}...\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" fontFamily=\"monospace\">\n                          {dispute.trade?.tradeId?.substring(0, 8)}...\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={dispute.status}\n                          size=\"small\"\n                          color={\n                            dispute.status === 'resolved' ? 'success' :\n                            dispute.status === 'pending' ? 'warning' : 'default'\n                          }\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {new Date(dispute.createdAt).toLocaleDateString()}\n                        </Typography>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default DashboardPage;\n", "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m16 18 2.29-2.29-4.88-4.88-4 4L2 7.41 3.41 6l6 6 4-4 6.3 6.29L22 12v6z\"\n}), 'TrendingDown');"], "names": ["createSvgIcon", "_jsx", "d", "api", "axios", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "concat", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "adminAPI", "getStats", "get", "getRealtimeMetrics", "getApplicationMetrics", "adminWebSocket", "constructor", "this", "socket", "listeners", "Map", "reconnectAttempts", "maxReconnectAttempts", "connect", "console", "WebSocket", "onopen", "log", "emit", "onmessage", "event", "data", "JSON", "parse", "type", "payload", "onclose", "attemptReconnect", "onerror", "disconnect", "close", "delay", "Math", "pow", "setTimeout", "on", "callback", "has", "set", "push", "off", "callbacks", "index", "indexOf", "splice", "for<PERSON>ach", "send", "readyState", "OPEN", "stringify", "warn", "StatCard", "React", "_ref", "title", "value", "icon", "color", "growth", "Card", "children", "<PERSON><PERSON><PERSON><PERSON>", "_jsxs", "Box", "display", "alignItems", "justifyContent", "Typography", "gutterBottom", "variant", "component", "undefined", "mt", "TrendingUp", "fontSize", "TrendingDown", "sx", "ml", "backgroundColor", "borderRadius", "p", "DashboardPage", "_stats$statistics9", "_stats$statistics9$us", "_stats$statistics0", "_stats$statistics0$tr", "_stats$statistics1", "_stats$statistics1$tr", "_stats$statistics10", "_stats$statistics10$o", "_stats$statistics11", "_stats$statistics11$t", "_stats$statistics12", "_stats$statistics12$u", "_stats$statistics13", "_stats$statistics13$o", "_stats$recentActivity", "_stats$recentActivity2", "_stats$recentActivity3", "_stats$recentActivity4", "stats", "setStats", "useState", "loading", "setLoading", "setError", "fetchStats", "useCallback", "async", "success", "process", "useEffect", "handleStatsUpdate", "prevStats", "_objectSpread", "interval", "setInterval", "clearInterval", "formatNumber", "num", "toFixed", "toLocaleString", "formatCurrency", "amount", "formattedStats", "useMemo", "_stats$statistics", "_stats$statistics$use", "_stats$statistics2", "_stats$statistics2$tr", "_stats$statistics3", "_stats$statistics3$di", "_stats$statistics4", "_stats$statistics4$tr", "_stats$statistics5", "_stats$statistics5$of", "_stats$statistics6", "_stats$statistics6$tr", "_stats$statistics7", "_stats$statistics7$us", "_stats$statistics8", "_stats$statistics8$of", "totalUsers", "statistics", "users", "total", "activeTrades", "trades", "active", "pendingDisputes", "disputes", "pending", "totalVolume", "volume", "totalOffers", "offers", "completedTrades", "completed", "weeklyNewUsers", "weeklyNew", "activeOffers", "minHeight", "CircularProgress", "Grid", "container", "spacing", "item", "xs", "sm", "md", "People", "SwapHoriz", "Gavel", "volumeGrowth", "LocalOffer", "Paper", "lg", "TableContainer", "Table", "size", "TableHead", "TableRow", "TableCell", "TableBody", "recentActivity", "slice", "map", "trade", "_trade$tradeId", "_trade$cryptocurrency", "_trade$cryptocurrency2", "fontFamily", "tradeId", "substring", "cryptocurrency", "symbol", "Chip", "label", "Date", "createdAt", "toLocaleDateString", "_id", "dispute", "_dispute$disputeId", "_dispute$trade", "_dispute$trade$tradeI", "disputeId"], "sourceRoot": ""}