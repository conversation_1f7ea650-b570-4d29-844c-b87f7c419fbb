const { redis } = require('../config/database');
const crypto = require('crypto');

/**
 * Advanced Caching Service for High-Performance API
 * Implements multi-layer caching with TTL, invalidation, and warming strategies
 */

class CacheService {
  constructor() {
    this.redis = redis;
    this.localCache = new Map();
    this.maxLocalCacheSize = 1000;
    this.defaultTTL = 300; // 5 minutes
    
    // Cache hit/miss statistics
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };
    
    // Start cache cleanup interval
    this.startCleanupInterval();
  }

  /**
   * Generate cache key with namespace and hashing
   */
  generateKey(namespace, identifier, params = {}) {
    const baseKey = `${namespace}:${identifier}`;
    
    if (Object.keys(params).length === 0) {
      return baseKey;
    }
    
    // Create deterministic hash of parameters
    const paramString = JSON.stringify(params, Object.keys(params).sort());
    const hash = crypto.createHash('md5').update(paramString).digest('hex');
    
    return `${baseKey}:${hash}`;
  }

  /**
   * Get value from cache (checks local cache first, then Redis)
   */
  async get(key) {
    try {
      // Check local cache first
      if (this.localCache.has(key)) {
        const cached = this.localCache.get(key);
        if (cached.expiry > Date.now()) {
          this.stats.hits++;
          return cached.value;
        } else {
          this.localCache.delete(key);
        }
      }

      // Check Redis cache
      if (this.redis) {
        const cached = await this.redis.get(key);
        if (cached) {
          const value = JSON.parse(cached);
          
          // Promote to local cache
          this.setLocal(key, value, 60); // 1 minute local cache
          
          this.stats.hits++;
          return value;
        }
      }

      this.stats.misses++;
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Set value in cache with TTL
   */
  async set(key, value, ttl = this.defaultTTL) {
    try {
      // Set in Redis
      if (this.redis) {
        await this.redis.setex(key, ttl, JSON.stringify(value));
      }

      // Set in local cache with shorter TTL
      const localTTL = Math.min(ttl, 300); // Max 5 minutes local cache
      this.setLocal(key, value, localTTL);

      this.stats.sets++;
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Set value in local cache only
   */
  setLocal(key, value, ttl) {
    // Cleanup local cache if too large
    if (this.localCache.size >= this.maxLocalCacheSize) {
      const firstKey = this.localCache.keys().next().value;
      this.localCache.delete(firstKey);
    }

    this.localCache.set(key, {
      value,
      expiry: Date.now() + (ttl * 1000)
    });
  }

  /**
   * Delete from cache
   */
  async delete(key) {
    try {
      // Delete from local cache
      this.localCache.delete(key);

      // Delete from Redis
      if (this.redis) {
        await this.redis.del(key);
      }

      this.stats.deletes++;
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Delete multiple keys by pattern
   */
  async deletePattern(pattern) {
    try {
      if (this.redis) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
          this.stats.deletes += keys.length;
        }
      }

      // Clear matching keys from local cache
      for (const key of this.localCache.keys()) {
        if (this.matchesPattern(key, pattern)) {
          this.localCache.delete(key);
        }
      }

      return true;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return false;
    }
  }

  /**
   * Get or set pattern (cache-aside pattern)
   */
  async getOrSet(key, fetchFunction, ttl = this.defaultTTL) {
    try {
      // Try to get from cache first
      let value = await this.get(key);
      
      if (value !== null) {
        return value;
      }

      // Fetch from source
      value = await fetchFunction();
      
      if (value !== null && value !== undefined) {
        await this.set(key, value, ttl);
      }

      return value;
    } catch (error) {
      console.error('Cache getOrSet error:', error);
      // Return result from fetch function even if caching fails
      return await fetchFunction();
    }
  }

  /**
   * Increment counter in cache
   */
  async increment(key, amount = 1, ttl = this.defaultTTL) {
    try {
      if (this.redis) {
        const result = await this.redis.incrby(key, amount);
        await this.redis.expire(key, ttl);
        return result;
      }
      return null;
    } catch (error) {
      console.error('Cache increment error:', error);
      return null;
    }
  }

  /**
   * Set with expiry at specific time
   */
  async setExpireAt(key, value, expireAt) {
    try {
      if (this.redis) {
        await this.redis.set(key, JSON.stringify(value));
        await this.redis.expireat(key, Math.floor(expireAt.getTime() / 1000));
      }
      return true;
    } catch (error) {
      console.error('Cache setExpireAt error:', error);
      return false;
    }
  }

  /**
   * Cache warming for frequently accessed data
   */
  async warmCache(warmingConfig) {
    console.log('Starting cache warming...');
    
    for (const config of warmingConfig) {
      try {
        const { key, fetchFunction, ttl } = config;
        const value = await fetchFunction();
        
        if (value !== null && value !== undefined) {
          await this.set(key, value, ttl);
          console.log(`Warmed cache for key: ${key}`);
        }
      } catch (error) {
        console.error(`Failed to warm cache for key ${config.key}:`, error);
      }
    }
    
    console.log('Cache warming completed');
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      localCacheSize: this.localCache.size,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Clear all caches
   */
  async clear() {
    try {
      this.localCache.clear();
      
      if (this.redis) {
        await this.redis.flushdb();
      }
      
      // Reset stats
      this.stats = { hits: 0, misses: 0, sets: 0, deletes: 0 };
      
      return true;
    } catch (error) {
      console.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * Check if key matches pattern (simple wildcard support)
   */
  matchesPattern(key, pattern) {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return regex.test(key);
  }

  /**
   * Start cleanup interval for local cache
   */
  startCleanupInterval() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, cached] of this.localCache.entries()) {
        if (cached.expiry <= now) {
          this.localCache.delete(key);
        }
      }
    }, 60000); // Cleanup every minute
  }

  /**
   * Batch operations for better performance
   */
  async mget(keys) {
    try {
      if (this.redis) {
        const values = await this.redis.mget(...keys);
        return values.map(value => value ? JSON.parse(value) : null);
      }
      return keys.map(() => null);
    } catch (error) {
      console.error('Cache mget error:', error);
      return keys.map(() => null);
    }
  }

  async mset(keyValuePairs, ttl = this.defaultTTL) {
    try {
      if (this.redis) {
        const pipeline = this.redis.pipeline();
        
        for (const [key, value] of keyValuePairs) {
          pipeline.setex(key, ttl, JSON.stringify(value));
        }
        
        await pipeline.exec();
        this.stats.sets += keyValuePairs.length;
      }
      return true;
    } catch (error) {
      console.error('Cache mset error:', error);
      return false;
    }
  }
}

// Cache namespaces for different data types
const CACHE_NAMESPACES = {
  USER: 'user',
  TRADING: 'trading',
  WALLET: 'wallet',
  OFFERS: 'offers',
  TRADES: 'trades',
  RATES: 'rates',
  STATS: 'stats',
  SESSION: 'session'
};

// Cache TTL configurations
const CACHE_TTL = {
  SHORT: 60,        // 1 minute
  MEDIUM: 300,      // 5 minutes
  LONG: 1800,       // 30 minutes
  VERY_LONG: 3600,  // 1 hour
  DAILY: 86400      // 24 hours
};

// Create singleton instance
const cacheService = new CacheService();

module.exports = {
  cache: cacheService,
  CACHE_NAMESPACES,
  CACHE_TTL
};
