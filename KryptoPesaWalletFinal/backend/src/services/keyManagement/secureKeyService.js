const AWS = require('aws-sdk');
const { ethers } = require('ethers');
const crypto = require('crypto');
const logger = require('../../utils/logger');
const SecureKey = require('../../models/SecureKey');
const securityConfig = require('../../config/security');
const { circuitBreakers } = require('../../utils/circuitBreaker');

class SecureKeyService {
  constructor() {
    this.kms = new AWS.KMS({
      region: process.env.AWS_REGION || 'us-east-1'
    });
    this.encryptedKeys = new Map();
    this.decryptedKeys = new Map();
    this.keyRotationInterval = 24 * 60 * 60 * 1000; // 24 hours
    this.securityConfig = securityConfig;
    this.rotationMonitoringInterval = null;
    this.keyAccessMetrics = new Map();
    this.fallbackMode = false;
  }

  /**
   * Initialize secure key management
   */
  async initialize() {
    try {
      // Test AWS connectivity
      await this.testAWSConnectivity();

      // Load encrypted keys from secure storage
      await this.loadEncryptedKeys();

      // Set up key rotation schedule and monitoring
      this.setupKeyRotation();
      this.setupRotationMonitoring();

      // Initialize key access metrics
      this.initializeMetrics();

      logger.info('✅ Secure key service initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize secure key service:', error);

      // Enable fallback mode if AWS is unavailable
      if (error.code === 'NetworkingError' || error.code === 'UnknownEndpoint') {
        logger.warn('🔄 AWS unavailable, enabling fallback mode');
        this.fallbackMode = true;
        await this.loadEncryptedKeysFromDatabase();
        return true;
      }

      throw error;
    }
  }

  /**
   * Load encrypted keys from storage
   */
  async loadEncryptedKeys() {
    try {
      const networks = ['ethereum', 'polygon', 'bitcoin'];

      for (const network of networks) {
        const encryptedKey = await this.getStoredEncryptedKey(network);
        if (encryptedKey) {
          this.encryptedKeys.set(network, encryptedKey);
          logger.debug(`✅ Loaded encrypted key for ${network}`);
        }
      }

      logger.info(`✅ Loaded encrypted keys for ${this.encryptedKeys.size} networks`);
    } catch (error) {
      logger.error('❌ Failed to load encrypted keys:', error);
      throw error;
    }
  }

  /**
   * Get wallet instance with secure key management
   */
  async getSecureWallet(network = 'polygon') {
    try {
      let privateKey = this.decryptedKeys.get(network);

      if (!privateKey) {
        privateKey = await this.decryptPrivateKey(network);
        this.decryptedKeys.set(network, privateKey);

        // Record access for monitoring
        await this.recordKeyAccess(network, 'wallet_access');

        // Auto-clear from memory after 1 hour
        setTimeout(() => {
          this.decryptedKeys.delete(network);
          logger.debug(`🧹 Cleared ${network} private key from memory`);
        }, 60 * 60 * 1000);
      }

      const provider = this.getProvider(network);
      const wallet = new ethers.Wallet(privateKey, provider);

      logger.debug(`✅ Secure wallet created for ${network}: ${wallet.address}`);
      return wallet;
    } catch (error) {
      logger.error(`❌ Failed to get secure wallet for ${network}:`, error);

      // Record failed access attempt
      await this.sendSecurityAlert('wallet_access_failed', {
        network,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      throw error;
    }
  }

  /**
   * Decrypt private key using AWS KMS
   */
  async decryptPrivateKey(network) {
    try {
      const encryptedKey = this.encryptedKeys.get(network);
      if (!encryptedKey) {
        throw new Error(`No encrypted key found for network: ${network}`);
      }

      const params = {
        CiphertextBlob: Buffer.from(encryptedKey, 'base64'),
        EncryptionContext: {
          network: network,
          service: 'kryptopesa-escrow'
        }
      };

      const result = await this.kms.decrypt(params).promise();
      return result.Plaintext.toString('utf8');
    } catch (error) {
      logger.error(`Failed to decrypt private key for ${network}:`, error);
      throw error;
    }
  }

  /**
   * Encrypt and store private key using AWS KMS
   */
  async encryptPrivateKey(network, privateKey) {
    try {
      const params = {
        KeyId: process.env.AWS_KMS_KEY_ID,
        Plaintext: privateKey,
        EncryptionContext: {
          network: network,
          service: 'kryptopesa-escrow'
        }
      };

      const result = await this.kms.encrypt(params).promise();
      const encryptedKey = result.CiphertextBlob.toString('base64');
      
      this.encryptedKeys.set(network, encryptedKey);
      
      // Store in secure database or AWS Secrets Manager
      await this.storeEncryptedKey(network, encryptedKey);
      
      logger.info(`Private key encrypted and stored for network: ${network}`);
    } catch (error) {
      logger.error(`Failed to encrypt private key for ${network}:`, error);
      throw error;
    }
  }

  /**
   * Load encrypted keys from secure storage
   */
  async loadEncryptedKeys() {
    try {
      // In production, load from AWS Secrets Manager or secure database
      const networks = ['polygon', 'ethereum'];
      
      for (const network of networks) {
        const encryptedKey = await this.getStoredEncryptedKey(network);
        if (encryptedKey) {
          this.encryptedKeys.set(network, encryptedKey);
        }
      }
    } catch (error) {
      logger.error('Failed to load encrypted keys:', error);
      throw error;
    }
  }

  /**
   * Store encrypted key in secure storage
   */
  async storeEncryptedKey(network, encryptedKey) {
    try {
      // Primary: AWS Secrets Manager
      if (process.env.USE_AWS_SECRETS_MANAGER === 'true' && !this.fallbackMode) {
        await this.storeInAWSSecretsManager(network, encryptedKey);
      }

      // Always store in database as fallback
      await this.storeInSecureDatabase(network, encryptedKey);

      logger.info(`✅ Encrypted key stored for network: ${network}`);
    } catch (error) {
      logger.error(`❌ Failed to store encrypted key for ${network}:`, error);
      throw error;
    }
  }

  /**
   * Store key in AWS Secrets Manager
   */
  async storeInAWSSecretsManager(network, encryptedKey) {
    const secretsManager = new AWS.SecretsManager({
      region: process.env.AWS_REGION
    });

    const params = {
      SecretId: `kryptopesa-${network}-private-key`,
      SecretString: encryptedKey,
      Description: `KryptoPesa ${network} private key - ${new Date().toISOString()}`
    };

    try {
      await secretsManager.updateSecret(params).promise();
    } catch (error) {
      if (error.code === 'ResourceNotFoundException') {
        // Create new secret if it doesn't exist
        await secretsManager.createSecret({
          ...params,
          Name: params.SecretId
        }).promise();
      } else {
        throw error;
      }
    }
  }

  /**
   * Store key in secure database (fallback)
   */
  async storeInSecureDatabase(network, encryptedKey) {
    const keyId = `blockchain_${network}_private_key`;

    // Encrypt the already encrypted key with our database encryption
    const doubleEncrypted = this.securityConfig.encrypt(encryptedKey);

    const secureKey = await SecureKey.findOneAndUpdate(
      { keyId },
      {
        keyId,
        keyType: 'blockchain_private_key',
        network,
        encryptedData: doubleEncrypted,
        metadata: {
          description: `${network} blockchain private key`,
          purpose: 'escrow_operations',
          environment: process.env.NODE_ENV || 'production',
          keyStrength: 256
        },
        rotation: {
          lastRotated: new Date(),
          rotationInterval: 30 * 24 * 60 * 60 * 1000 // 30 days
        },
        access: {
          allowedServices: ['escrow', 'wallet', 'transaction']
        }
      },
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true
      }
    );

    await secureKey.addAuditEntry('created', {
      storage_method: 'database_fallback',
      network,
      encrypted_with: 'double_encryption'
    });
  }

  /**
   * Get stored encrypted key
   */
  async getStoredEncryptedKey(network) {
    try {
      // Try AWS Secrets Manager first if available
      if (process.env.USE_AWS_SECRETS_MANAGER === 'true' && !this.fallbackMode) {
        try {
          const encryptedKey = await this.getFromAWSSecretsManager(network);
          if (encryptedKey) {
            await this.recordKeyAccess(network, 'aws_secrets_manager');
            return encryptedKey;
          }
        } catch (error) {
          logger.warn(`AWS Secrets Manager failed for ${network}, trying database fallback:`, error.message);
        }
      }

      // Fallback to database storage
      const encryptedKey = await this.getFromSecureDatabase(network);
      if (encryptedKey) {
        await this.recordKeyAccess(network, 'database_fallback');
        return encryptedKey;
      }

      return null;
    } catch (error) {
      logger.error(`Failed to retrieve encrypted key for ${network}:`, error);
      throw error;
    }
  }

  /**
   * Get key from AWS Secrets Manager
   */
  async getFromAWSSecretsManager(network) {
    const secretsManager = new AWS.SecretsManager({
      region: process.env.AWS_REGION
    });

    try {
      const result = await secretsManager.getSecretValue({
        SecretId: `kryptopesa-${network}-private-key`
      }).promise();

      return result.SecretString;
    } catch (error) {
      if (error.code === 'ResourceNotFoundException') {
        return null;
      }
      throw error;
    }
  }

  /**
   * Get key from secure database
   */
  async getFromSecureDatabase(network) {
    const keyId = `blockchain_${network}_private_key`;

    const secureKey = await SecureKey.findOne({
      keyId,
      'security.isActive': true,
      'security.isCompromised': false
    });

    if (!secureKey) {
      return null;
    }

    // Decrypt the double-encrypted key
    const decrypted = this.securityConfig.decrypt(secureKey.encryptedData);

    // Update access tracking
    await secureKey.markAccessed('key_management_service', 'internal');

    return decrypted;
  }

  /**
   * Set up automatic key rotation
   */
  setupKeyRotation() {
    setInterval(async () => {
      try {
        await this.rotateKeys();
      } catch (error) {
        logger.error('❌ Key rotation failed:', error);
        await this.handleRotationFailure(error);
      }
    }, this.keyRotationInterval);
  }

  /**
   * Set up rotation monitoring and alerting
   */
  setupRotationMonitoring() {
    // Check for overdue rotations every hour
    this.rotationMonitoringInterval = setInterval(async () => {
      try {
        await this.checkOverdueRotations();
        await this.generateSecurityReport();
      } catch (error) {
        logger.error('❌ Rotation monitoring failed:', error);
      }
    }, 60 * 60 * 1000); // 1 hour

    logger.info('🔄 Key rotation monitoring enabled');
  }

  /**
   * Rotate encryption keys
   */
  async rotateKeys() {
    logger.info('🔄 Starting comprehensive key rotation process');

    try {
      // Clear decrypted keys from memory
      this.decryptedKeys.clear();

      // Find keys that need rotation
      const keysNeedingRotation = await SecureKey.findKeysNeedingRotation();

      if (keysNeedingRotation.length === 0) {
        logger.info('✅ No keys require rotation at this time');
        return;
      }

      logger.info(`🔄 Rotating ${keysNeedingRotation.length} keys`);

      for (const secureKey of keysNeedingRotation) {
        await this.rotateIndividualKey(secureKey);
      }

      // Generate rotation report
      await this.generateRotationReport(keysNeedingRotation.length);

      logger.info('✅ Key rotation process completed successfully');
    } catch (error) {
      logger.error('❌ Key rotation process failed:', error);
      throw error;
    }
  }

  /**
   * Rotate individual key
   */
  async rotateIndividualKey(secureKey) {
    try {
      logger.info(`🔄 Rotating key: ${secureKey.keyId}`);

      if (secureKey.keyType === 'blockchain_private_key') {
        // For blockchain keys, we don't rotate the actual private key
        // Instead, we re-encrypt it with a new encryption key
        const currentKey = await this.getFromSecureDatabase(secureKey.network);
        if (currentKey) {
          // Re-encrypt with fresh encryption
          const newEncrypted = this.securityConfig.encrypt(currentKey);
          await secureKey.rotateKey(newEncrypted, 'system');
        }
      }

      logger.info(`✅ Successfully rotated key: ${secureKey.keyId}`);
    } catch (error) {
      logger.error(`❌ Failed to rotate key ${secureKey.keyId}:`, error);

      // Mark key as needing attention
      await secureKey.addAuditEntry('rotation_failed', {
        error: error.message,
        retry_required: true
      });

      throw error;
    }
  }

  /**
   * Get provider for network
   */
  getProvider(network) {
    const providers = {
      polygon: new ethers.providers.JsonRpcProvider(process.env.POLYGON_RPC_URL),
      ethereum: new ethers.providers.JsonRpcProvider(process.env.ETHEREUM_RPC_URL)
    };
    
    return providers[network];
  }

  /**
   * Check for overdue key rotations
   */
  async checkOverdueRotations() {
    try {
      const overdueKeys = await SecureKey.findKeysNeedingRotation();

      if (overdueKeys.length > 0) {
        logger.warn(`⚠️  ${overdueKeys.length} keys are overdue for rotation`);

        // Send alert for critical keys
        for (const key of overdueKeys) {
          if (key.keyType === 'blockchain_private_key') {
            await this.sendSecurityAlert('key_rotation_overdue', {
              keyId: key.keyId,
              network: key.network,
              daysPastDue: Math.floor((Date.now() - key.rotation.nextRotation) / (24 * 60 * 60 * 1000))
            });
          }
        }
      }
    } catch (error) {
      logger.error('❌ Failed to check overdue rotations:', error);
    }
  }

  /**
   * Generate security report
   */
  async generateSecurityReport() {
    try {
      const summary = await SecureKey.getSecuritySummary();
      const compromisedKeys = await SecureKey.findCompromisedKeys();

      const report = {
        timestamp: new Date().toISOString(),
        summary,
        compromisedKeys: compromisedKeys.length,
        metrics: {
          totalKeyAccesses: Array.from(this.keyAccessMetrics.values()).reduce((sum, count) => sum + count, 0),
          fallbackModeActive: this.fallbackMode
        }
      };

      logger.info('📊 Security Report:', JSON.stringify(report, null, 2));

      // Store report for audit purposes
      if (compromisedKeys.length > 0) {
        await this.sendSecurityAlert('compromised_keys_detected', {
          count: compromisedKeys.length,
          keys: compromisedKeys.map(k => ({ keyId: k.keyId, compromisedAt: k.security.compromisedAt }))
        });
      }

      return report;
    } catch (error) {
      logger.error('❌ Failed to generate security report:', error);
    }
  }

  /**
   * Record key access for metrics
   */
  async recordKeyAccess(network, method) {
    const key = `${network}_${method}`;
    const currentCount = this.keyAccessMetrics.get(key) || 0;
    this.keyAccessMetrics.set(key, currentCount + 1);
  }

  /**
   * Initialize metrics tracking
   */
  initializeMetrics() {
    this.keyAccessMetrics.clear();
    logger.info('📊 Key access metrics initialized');
  }

  /**
   * Test AWS connectivity
   */
  async testAWSConnectivity() {
    try {
      await this.kms.describeKey({
        KeyId: process.env.AWS_KMS_KEY_ID || 'alias/aws/s3'
      }).promise();

      logger.info('✅ AWS KMS connectivity verified');
      return true;
    } catch (error) {
      logger.warn('⚠️  AWS KMS connectivity failed:', error.message);
      throw error;
    }
  }

  /**
   * Load encrypted keys from database fallback
   */
  async loadEncryptedKeysFromDatabase() {
    try {
      const keys = await SecureKey.find({
        keyType: 'blockchain_private_key',
        'security.isActive': true,
        'security.isCompromised': false
      });

      for (const key of keys) {
        this.encryptedKeys.set(key.network, key.encryptedData);
      }

      logger.info(`✅ Loaded ${keys.length} keys from database fallback`);
    } catch (error) {
      logger.error('❌ Failed to load keys from database:', error);
      throw error;
    }
  }

  /**
   * Handle rotation failure
   */
  async handleRotationFailure(error) {
    await this.sendSecurityAlert('key_rotation_failed', {
      error: error.message,
      timestamp: new Date().toISOString(),
      action_required: 'immediate'
    });
  }

  /**
   * Generate rotation report
   */
  async generateRotationReport(rotatedCount) {
    const report = {
      timestamp: new Date().toISOString(),
      rotatedKeys: rotatedCount,
      status: 'completed',
      nextRotationCheck: new Date(Date.now() + this.keyRotationInterval).toISOString()
    };

    logger.info('📋 Rotation Report:', JSON.stringify(report, null, 2));
  }

  /**
   * Send security alert
   */
  async sendSecurityAlert(type, details) {
    const alert = {
      type,
      severity: type.includes('compromised') || type.includes('failed') ? 'critical' : 'warning',
      timestamp: new Date().toISOString(),
      service: 'key_management',
      details
    };

    logger.warn(`🚨 SECURITY ALERT [${alert.severity.toUpperCase()}]:`, JSON.stringify(alert, null, 2));

    // In production, this would integrate with alerting systems
    // like PagerDuty, Slack, or email notifications
  }

  /**
   * Secure cleanup on shutdown
   */
  cleanup() {
    // Clear all decrypted keys from memory
    this.decryptedKeys.clear();
    this.encryptedKeys.clear();
    this.keyAccessMetrics.clear();

    // Clear monitoring intervals
    if (this.rotationMonitoringInterval) {
      clearInterval(this.rotationMonitoringInterval);
    }

    logger.info('🧹 Secure key service cleaned up');
  }
}

module.exports = new SecureKeyService();
