import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration
const isDevelopment = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
const API_BASE_URL = isDevelopment
  ? 'http://***************:3000/api'  // Development - Use computer's IP for mobile access
  : 'https://api.kryptopesa.com/api';  // Production

// Storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  WALLET_ADDRESS: 'wallet_address',
};

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: any[];
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// HTTP Client class
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    const walletAddress = await AsyncStorage.getItem(STORAGE_KEYS.WALLET_ADDRESS);
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    if (walletAddress) {
      headers['X-Wallet-Address'] = walletAddress;
    }

    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      
      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          // Token expired, try to refresh
          await this.handleTokenRefresh();
        }
        
        throw new Error(data.message || `HTTP ${response.status}`);
      }
      
      return data;
    } else {
      throw new Error(`Unexpected response type: ${contentType}`);
    }
  }

  private async handleTokenRefresh(): Promise<void> {
    try {
      const refreshToken = await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, data.data.accessToken);
        await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, data.data.refreshToken);
      } else {
        // Refresh failed, clear tokens
        await AsyncStorage.multiRemove([
          STORAGE_KEYS.ACCESS_TOKEN,
          STORAGE_KEYS.REFRESH_TOKEN,
          STORAGE_KEYS.WALLET_ADDRESS,
        ]);
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }

  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    try {
      const url = new URL(`${this.baseURL}${endpoint}`);

      if (params) {
        Object.keys(params).forEach(key => {
          if (params[key] !== undefined && params[key] !== null) {
            url.searchParams.append(key, params[key].toString());
          }
        });
      }

      const headers = await this.getAuthHeaders();
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error(`API GET ${endpoint} failed:`, error);
      // Return a failed response that services can handle
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Network request failed',
        data: null as T,
      };
    }
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error(`API POST ${endpoint} failed:`, error);
      // Return a failed response that services can handle
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Network request failed',
        data: null as T,
      };
    }
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'PUT',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error(`API PUT ${endpoint} failed:`, error);
      // Return a failed response that services can handle
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Network request failed',
        data: null as T,
      };
    }
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'DELETE',
        headers,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error(`API DELETE ${endpoint} failed:`, error);
      // Return a failed response that services can handle
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Network request failed',
        data: null as T,
      };
    }
  }

  // File upload method
  async uploadFile<T>(endpoint: string, file: any, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const token = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    const walletAddress = await AsyncStorage.getItem(STORAGE_KEYS.WALLET_ADDRESS);
    
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }

    const headers: Record<string, string> = {};
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    if (walletAddress) {
      headers['X-Wallet-Address'] = walletAddress;
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
    });

    return this.handleResponse<T>(response);
  }
}

// Create API client instance
console.log('Initializing API client with URL:', API_BASE_URL);
export const apiClient = new ApiClient(API_BASE_URL);

// Auth token management
export const authTokens = {
  async setTokens(accessToken: string, refreshToken: string, walletAddress?: string): Promise<void> {
    await AsyncStorage.multiSet([
      [STORAGE_KEYS.ACCESS_TOKEN, accessToken],
      [STORAGE_KEYS.REFRESH_TOKEN, refreshToken],
    ]);
    
    if (walletAddress) {
      await AsyncStorage.setItem(STORAGE_KEYS.WALLET_ADDRESS, walletAddress);
    }
  },

  async getAccessToken(): Promise<string | null> {
    return AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
  },

  async getWalletAddress(): Promise<string | null> {
    return AsyncStorage.getItem(STORAGE_KEYS.WALLET_ADDRESS);
  },

  async clearTokens(): Promise<void> {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.ACCESS_TOKEN,
      STORAGE_KEYS.REFRESH_TOKEN,
      STORAGE_KEYS.WALLET_ADDRESS,
    ]);
  },

  async isAuthenticated(): Promise<boolean> {
    const token = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    return !!token;
  },
};

// API endpoints configuration
export const API_ENDPOINTS = {
  // Wallet Authentication
  WALLET_AUTH: {
    AUTHENTICATE: '/wallet-auth/authenticate',
    VERIFY: '/wallet-auth/verify',
    REFRESH: '/wallet-auth/refresh',
  },
  
  // User Management
  USERS: {
    PROFILE: '/users/profile',
    UPDATE_PROFILE: '/users/profile',
    SEARCH: '/users/search',
    DASHBOARD: '/users/dashboard',
  },
  
  // Wallet Operations
  WALLET: {
    GET: '/wallet',
    CREATE: '/wallet/create',
    IMPORT: '/wallet/import',
    TRANSACTIONS: '/wallet/transactions',
    BALANCE: '/wallet/balance',
    SECURITY: '/wallet/security',
  },
  
  // Trading & Offers
  OFFERS: {
    LIST: '/offers',
    CREATE: '/offers',
    GET: (id: string) => `/offers/${id}`,
    UPDATE: (id: string) => `/offers/${id}`,
    DELETE: (id: string) => `/offers/${id}`,
  },
  
  // Trades
  TRADES: {
    LIST: '/trades',
    CREATE: '/trades',
    GET: (id: string) => `/trades/${id}`,
    UPDATE: (id: string) => `/trades/${id}`,
    ACTIVE: '/trades/active',
    HISTORY: '/trades/history',
    STATS: '/trades/stats',
  },

  // Chat
  CHAT: {
    CONVERSATIONS: '/chat/conversations',
    LIST: '/chat',
    GET: (id: string) => `/chat/${id}`,
    MESSAGES: (conversationId: string) => `/chat/${conversationId}/messages`,
    SEND_MESSAGE: (conversationId: string) => `/chat/${conversationId}/messages`,
    MARK_READ: (conversationId: string) => `/chat/${conversationId}/read`,
    UPLOAD_FILE: (id: string) => `/chat/${id}/upload`,
  },
  
  // Notifications
  NOTIFICATIONS: {
    LIST: '/notifications',
    MARK_READ: (id: string) => `/notifications/${id}/read`,
    MARK_ALL_READ: '/notifications/mark-all-read',
    PREFERENCES: '/notifications/preferences',
  },
  
  // KYC
  KYC: {
    STATUS: '/kyc/status',
    SUBMIT: '/kyc/submit',
    UPLOAD_DOCUMENT: '/kyc/upload',
  },

  // System
  SYSTEM: {
    HEALTH: '/system/health',
    STATUS: '/system/status',
  },
};

// East African Market Configuration
export const EAST_AFRICAN_CONFIG = {
  CURRENCIES: {
    KES: { name: 'Kenyan Shilling', symbol: 'KSh', country: 'KE' },
    TZS: { name: 'Tanzanian Shilling', symbol: 'TSh', country: 'TZ' },
    UGX: { name: 'Ugandan Shilling', symbol: 'USh', country: 'UG' },
    RWF: { name: 'Rwandan Franc', symbol: 'RWF', country: 'RW' },
    USD: { name: 'US Dollar', symbol: '$', country: 'US' },
  },

  COUNTRIES: {
    KE: { name: 'Kenya', flag: '🇰🇪', timezone: 'Africa/Nairobi' },
    TZ: { name: 'Tanzania', flag: '🇹🇿', timezone: 'Africa/Dar_es_Salaam' },
    UG: { name: 'Uganda', flag: '🇺🇬', timezone: 'Africa/Kampala' },
    RW: { name: 'Rwanda', flag: '🇷🇼', timezone: 'Africa/Kigali' },
  },

  PAYMENT_METHODS: {
    mpesa: {
      name: 'M-Pesa',
      icon: '📱',
      countries: ['KE', 'TZ'],
      validation: /^\+254\d{9}$|^\+255\d{9}$/
    },
    airtel_money: {
      name: 'Airtel Money',
      icon: '💰',
      countries: ['KE', 'TZ', 'UG', 'RW'],
      validation: /^\+254\d{9}$|^\+255\d{9}$|^\+256\d{9}$|^\+250\d{9}$/
    },
    bank_transfer: {
      name: 'Bank Transfer',
      icon: '🏦',
      countries: ['KE', 'TZ', 'UG', 'RW'],
      validation: null
    },
    cash: {
      name: 'Cash',
      icon: '💵',
      countries: ['KE', 'TZ', 'UG', 'RW'],
      validation: null
    },
  },

  CRYPTOCURRENCIES: {
    USDT: { name: 'Tether USD', symbol: 'USDT', network: 'Polygon' },
    USDC: { name: 'USD Coin', symbol: 'USDC', network: 'Polygon' },
    DAI: { name: 'Dai Stablecoin', symbol: 'DAI', network: 'Polygon' },
    BTC: { name: 'Bitcoin', symbol: 'BTC', network: 'Bitcoin' },
    ETH: { name: 'Ethereum', symbol: 'ETH', network: 'Ethereum' },
    MATIC: { name: 'Polygon', symbol: 'MATIC', network: 'Polygon' },
  },
};
