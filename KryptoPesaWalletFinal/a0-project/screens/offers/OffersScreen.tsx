import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList,
  Modal,
  Switch,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import {
  tradingService,
  userService,
  EAST_AFRICAN_CONFIG,
  Offer,
  OfferFilters,
  EastAfricanCurrency,
  CryptoCurrency,
  PaymentMethod,
  EastAfricanCountry,
  eastAfricanHelpers
} from '../../services';

// East African focused filter options
const FILTER_OPTIONS = {
  cryptocurrencies: Object.keys(EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES) as CryptoCurrency[],
  fiatCurrencies: Object.keys(EAST_AFRICAN_CONFIG.CURRENCIES) as EastAfricanCurrency[],
  countries: Object.keys(EAST_AFRICAN_CONFIG.COUNTRIES) as EastAfricanCountry[],
  paymentMethods: Object.keys(EAST_AFRICAN_CONFIG.PAYMENT_METHODS) as PaymentMethod[],
};

// Default filters for East African market
const DEFAULT_FILTERS: OfferFilters = {
  cryptocurrency: 'USDT',
  fiatCurrency: 'KES',
  country: 'KE',
  sortBy: 'price',
  sortOrder: 'asc',
  limit: 20,
  offset: 0,
};

export default function OffersScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();

  // Get initial type from route params if available
  const initialType = route.params?.type || 'buy';

  // State management
  const [offerType, setOfferType] = useState<'buy' | 'sell'>(initialType);
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [userProfile, setUserProfile] = useState(null);

  // Filter states with East African defaults
  const [filters, setFilters] = useState<OfferFilters>({
    ...DEFAULT_FILTERS,
    type: initialType,
  });

  // Load user profile for default currency/country
  const loadUserProfile = async () => {
    try {
      const profile = await userService.getProfile();
      setUserProfile(profile);

      // Update filters with user preferences
      setFilters(prev => ({
        ...prev,
        fiatCurrency: profile.preferences.currency,
        country: profile.profile.location.country,
      }));
    } catch (err) {
      console.error('Failed to load user profile:', err);
    }
  };

  // Load offers from API
  const loadOffers = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      const response = await tradingService.getOffers({
        ...filters,
        type: offerType,
      });

      setOffers(response.data || []);
    } catch (err) {
      console.error('Failed to load offers:', err);
      setError(err instanceof Error ? err.message : 'Failed to load offers');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadUserProfile();
  }, []);

  // Load offers when filters change
  useEffect(() => {
    if (userProfile) {
      loadOffers();
    }
  }, [offerType, filters, userProfile]);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadOffers(false);
    setRefreshing(false);
  };

  // Filter update handlers
  const updateFilters = (newFilters: Partial<OfferFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters({
      ...DEFAULT_FILTERS,
      type: offerType,
      fiatCurrency: userProfile?.preferences.currency || 'KES',
      country: userProfile?.profile.location.country || 'KE',
    });
  };

  const togglePaymentMethod = (method: PaymentMethod) => {
    const currentMethods = filters.paymentMethod ? [filters.paymentMethod] : [];
    if (currentMethods.includes(method)) {
      updateFilters({ paymentMethod: undefined });
    } else {
      updateFilters({ paymentMethod: method });
    }
  };

  // Check if we should show the create offer screen
  useEffect(() => {
    if (route.params?.action === 'create') {
      navigation.navigate('CreateOffer');
    }
  }, [route.params]);
  
  const renderOfferItem = ({ item }: { item: Offer }) => (
    <TouchableOpacity
      style={styles.offerCard}
      onPress={() => navigation.navigate('OfferDetail', { offerId: item._id })}
    >
      <View style={styles.offerHeader}>
        <View style={styles.offerTypeContainer}>
          <Text style={[
            styles.offerTypeText,
            item.type === 'buy' ? styles.buyText : styles.sellText
          ]}>
            {item.type === 'buy' ? 'Buy' : 'Sell'}
          </Text>
          <View style={styles.countryFlag}>
            <Text style={styles.flagText}>
              {EAST_AFRICAN_CONFIG.COUNTRIES[item.location.country]?.flag || '🌍'}
            </Text>
          </View>
        </View>
        <Text style={styles.offerPrice}>
          {eastAfricanHelpers.formatCurrencyAmount(item.fiat.currentPrice, item.fiat.currency)}
        </Text>
      </View>

      <View style={styles.offerDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Currency:</Text>
          <View style={styles.cryptoContainer}>
            <View style={styles.cryptoIconContainer}>
              <Text style={styles.cryptoIconText}>
                {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[item.cryptocurrency.type]?.symbol || item.cryptocurrency.type}
              </Text>
            </View>
            <Text style={styles.detailValue}>
              {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[item.cryptocurrency.type]?.name || item.cryptocurrency.type}
            </Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Limits:</Text>
          <Text style={styles.detailValue}>
            {item.cryptocurrency.minAmount} - {item.cryptocurrency.maxAmount} {item.cryptocurrency.type}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment:</Text>
          <View style={styles.paymentMethodsContainer}>
            {item.paymentMethods.slice(0, 2).map((method, index) => (
              <View key={index} style={styles.paymentMethodBadge}>
                <Text style={styles.paymentMethodText}>
                  {eastAfricanHelpers.getPaymentMethodName(method)}
                </Text>
              </View>
            ))}
            {item.paymentMethods.length > 2 && (
              <Text style={styles.morePaymentMethods}>
                +{item.paymentMethods.length - 2} more
              </Text>
            )}
          </View>
        </View>

        {item.location.city && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Location:</Text>
            <Text style={styles.detailValue}>
              {item.location.city}, {eastAfricanHelpers.getCountryName(item.location.country)}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.userSection}>
        <View style={styles.userInfo}>
          <Ionicons name="person-circle" size={24} color={theme.colors.textSecondary} />
          <Text style={styles.userName}>{item.trader.username}</Text>
          {item.trader.isOnline && (
            <View style={styles.onlineIndicator} />
          )}
        </View>
        <View style={styles.userStats}>
          <Text style={styles.tradeCount}>{item.trader.totalTrades} trades</Text>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color={theme.colors.warning} />
            <Text style={styles.ratingText}>{item.trader.averageRating.toFixed(1)}</Text>
          </View>
          <Text style={styles.completionRate}>
            {(item.trader.completionRate * 100).toFixed(0)}%
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.actionButton,
          item.type === 'buy' ? styles.buyButton : styles.sellButton
        ]}
        onPress={() => navigation.navigate('Trade', { offerId: item._id })}
      >
        <Text style={styles.actionButtonText}>
          {item.type === 'buy' ? 'Sell to Buyer' : 'Buy from Seller'}
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>KryptoPesa Offers</Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => navigation.navigate('CreateOffer')}
        >
          <Ionicons name="add-circle" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.tabContainer}>
        <TouchableOpacity 
          style={[styles.tab, offerType === 'buy' && styles.activeTab]}
          onPress={() => setOfferType('buy')}
        >
          <Text style={[styles.tabText, offerType === 'buy' && styles.activeTabText]}>
            Buy Crypto
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, offerType === 'sell' && styles.activeTab]}
          onPress={() => setOfferType('sell')}
        >
          <Text style={[styles.tabText, offerType === 'sell' && styles.activeTabText]}>
            Sell Crypto
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.quickFiltersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[styles.quickFilter, filters.cryptocurrency === 'USDT' && styles.activeQuickFilter]}
            onPress={() => updateFilters({ cryptocurrency: filters.cryptocurrency === 'USDT' ? undefined : 'USDT' })}
          >
            <Text style={[styles.quickFilterText, filters.cryptocurrency === 'USDT' && styles.activeQuickFilterText]}>
              USDT
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickFilter, filters.fiatCurrency === 'KES' && styles.activeQuickFilter]}
            onPress={() => updateFilters({ fiatCurrency: filters.fiatCurrency === 'KES' ? undefined : 'KES' })}
          >
            <Text style={[styles.quickFilterText, filters.fiatCurrency === 'KES' && styles.activeQuickFilterText]}>
              🇰🇪 KES
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickFilter, filters.paymentMethod === 'mpesa' && styles.activeQuickFilter]}
            onPress={() => updateFilters({ paymentMethod: filters.paymentMethod === 'mpesa' ? undefined : 'mpesa' })}
          >
            <Text style={[styles.quickFilterText, filters.paymentMethod === 'mpesa' && styles.activeQuickFilterText]}>
              📱 M-Pesa
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setFilterModalVisible(true)}
          >
            <Ionicons name="options" size={20} color={theme.colors.primary} />
            <Text style={styles.filterButtonText}>More</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
      
      {/* Active Filters Display */}
      {(filters.cryptocurrency || filters.fiatCurrency !== userProfile?.preferences.currency ||
        filters.paymentMethod || filters.country !== userProfile?.profile.location.country) && (
        <View style={styles.activeFiltersContainer}>
          <Text style={styles.activeFiltersTitle}>Active Filters:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersScrollView}>
            {filters.cryptocurrency && (
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>{filters.cryptocurrency}</Text>
                <TouchableOpacity onPress={() => updateFilters({ cryptocurrency: undefined })}>
                  <Ionicons name="close-circle" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            )}

            {filters.fiatCurrency && filters.fiatCurrency !== userProfile?.preferences.currency && (
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>
                  {EAST_AFRICAN_CONFIG.COUNTRIES[filters.country || 'KE']?.flag} {filters.fiatCurrency}
                </Text>
                <TouchableOpacity onPress={() => updateFilters({ fiatCurrency: userProfile?.preferences.currency })}>
                  <Ionicons name="close-circle" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            )}

            {filters.paymentMethod && (
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>
                  {eastAfricanHelpers.getPaymentMethodName(filters.paymentMethod)}
                </Text>
                <TouchableOpacity onPress={() => updateFilters({ paymentMethod: undefined })}>
                  <Ionicons name="close-circle" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            )}

            <TouchableOpacity style={styles.clearFiltersButton} onPress={clearFilters}>
              <Text style={styles.clearFiltersText}>Clear All</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      )}

      {/* Offers List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading offers...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load offers</Text>
          <Text style={styles.errorSubText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadOffers()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : offers.length > 0 ? (
        <FlatList
          data={offers}
          renderItem={renderOfferItem}
          keyExtractor={item => item._id}
          contentContainerStyle={styles.offersList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyStateContainer}>
          <Ionicons name="storefront-outline" size={64} color={theme.colors.border} />
          <Text style={styles.emptyStateText}>No offers available</Text>
          <Text style={styles.emptyStateSubText}>
            Be the first to create an offer in this market
          </Text>
          <TouchableOpacity
            style={styles.createOfferButton}
            onPress={() => navigation.navigate('CreateOffer')}
          >
            <Text style={styles.createOfferButtonText}>Create First Offer</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {/* Filter Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={filterModalVisible}
        onRequestClose={() => setFilterModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Offers</Text>
              <TouchableOpacity onPress={() => setFilterModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalScrollView}>
              <Text style={styles.filterSectionTitle}>Cryptocurrency</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsScrollView}>
                {FILTER_OPTIONS.cryptocurrencies.map(crypto => (
                  <TouchableOpacity
                    key={crypto}
                    style={[
                      styles.optionButton,
                      filters.cryptocurrency === crypto && styles.selectedOption
                    ]}
                    onPress={() => updateFilters({
                      cryptocurrency: filters.cryptocurrency === crypto ? undefined : crypto
                    })}
                  >
                    <Text style={[
                      styles.optionText,
                      filters.cryptocurrency === crypto && styles.selectedOptionText
                    ]}>
                      {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[crypto]?.name || crypto}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <Text style={styles.filterSectionTitle}>East African Currency</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsScrollView}>
                {FILTER_OPTIONS.fiatCurrencies.map(currency => (
                  <TouchableOpacity
                    key={currency}
                    style={[
                      styles.optionButton,
                      filters.fiatCurrency === currency && styles.selectedOption
                    ]}
                    onPress={() => updateFilters({
                      fiatCurrency: filters.fiatCurrency === currency ? undefined : currency
                    })}
                  >
                    <Text style={[
                      styles.optionText,
                      filters.fiatCurrency === currency && styles.selectedOptionText
                    ]}>
                      {EAST_AFRICAN_CONFIG.CURRENCIES[currency]?.symbol} {currency}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <Text style={styles.filterSectionTitle}>Country</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsScrollView}>
                {FILTER_OPTIONS.countries.map(country => (
                  <TouchableOpacity
                    key={country}
                    style={[
                      styles.optionButton,
                      filters.country === country && styles.selectedOption
                    ]}
                    onPress={() => updateFilters({
                      country: filters.country === country ? undefined : country
                    })}
                  >
                    <Text style={[
                      styles.optionText,
                      filters.country === country && styles.selectedOptionText
                    ]}>
                      {EAST_AFRICAN_CONFIG.COUNTRIES[country]?.flag} {EAST_AFRICAN_CONFIG.COUNTRIES[country]?.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              
              <Text style={styles.filterSectionTitle}>Amount Range</Text>
              <View style={styles.amountRangeContainer}>
                <View style={styles.amountInputContainer}>
                  <Text style={styles.amountLabel}>Min</Text>
                  <TextInput
                    style={styles.amountInput}
                    placeholder="0.001"
                    value={minAmount}
                    onChangeText={setMinAmount}
                    keyboardType="numeric"
                  />
                </View>
                <View style={styles.amountSeparator} />
                <View style={styles.amountInputContainer}>
                  <Text style={styles.amountLabel}>Max</Text>
                  <TextInput
                    style={styles.amountInput}
                    placeholder="No limit"
                    value={maxAmount}
                    onChangeText={setMaxAmount}
                    keyboardType="numeric"
                  />
                </View>
              </View>
              
              <Text style={styles.filterSectionTitle}>Payment Methods</Text>
              <View style={styles.paymentMethodsContainer}>
                {paymentMethodOptions.map(method => (
                  <TouchableOpacity
                    key={method}
                    style={styles.paymentMethodRow}
                    onPress={() => togglePaymentMethod(method)}
                  >
                    <Text style={styles.paymentMethodText}>{method}</Text>
                    <Switch
                      value={selectedPaymentMethods.includes(method)}
                      onValueChange={() => togglePaymentMethod(method)}
                      trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
                      thumbColor={selectedPaymentMethods.includes(method) ? theme.colors.primary : theme.colors.surface}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={styles.clearButton}
                onPress={clearFilters}
              >
                <Text style={styles.clearButtonText}>Clear All</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.applyButton}
                onPress={() => setFilterModalVisible(false)}
              >
                <Text style={styles.applyButtonText}>Apply Filters</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  createButton: {
    padding: 4,
  },
  quickFiltersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.colors.surface,
  },
  quickFilter: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginRight: 8,
    backgroundColor: theme.colors.background,
  },
  activeQuickFilter: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  quickFilterText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  activeQuickFilterText: {
    color: '#FFFFFF',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    marginRight: 8,
  },
  filterButtonText: {
    fontSize: 12,
    color: theme.colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: theme.colors.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  activeTabText: {
    color: theme.colors.primary,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.inputText,
  },
  filterButton: {
    marginLeft: 12,
    padding: 12,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  activeFiltersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  activeFiltersTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  filtersScrollView: {
    flexDirection: 'row',
  },
  filterTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  filterTagText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: 4,
  },
  clearFiltersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.errorLight,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  clearFiltersText: {
    fontSize: 14,
    color: theme.colors.error,
    fontWeight: '500',
  },
  offersList: {
    padding: 16,
  },
  offerCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  offerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  offerTypeContainer: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  offerTypeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  buyText: {
    color: theme.colors.success,
  },
  sellText: {
    color: theme.colors.error,
  },
  countryFlag: {
    marginLeft: 8,
  },
  flagText: {
    fontSize: 16,
  },
  cryptoIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  cryptoIconText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  paymentMethodsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  paymentMethodBadge: {
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 4,
    marginBottom: 2,
  },
  paymentMethodText: {
    fontSize: 10,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  morePaymentMethods: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  onlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981',
    marginLeft: 6,
  },
  completionRate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 12,
    textAlign: 'center',
  },
  errorSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  offerPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  offerDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    maxWidth: '60%',
  },
  cryptoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cryptoIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 4,
  },
  userSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginLeft: 8,
  },
  userStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tradeCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
    marginLeft: 2,
  },
  actionButton: {
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  buyButton: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  sellButton: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  createOfferButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  createOfferButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalScrollView: {
    padding: 16,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
    marginTop: 8,
  },
  optionsScrollView: {
    marginBottom: 16,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginRight: 8,
  },
  selectedOption: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  optionText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  selectedOptionText: {
    color: 'white',
  },
  amountRangeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  amountInputContainer: {
    flex: 1,
  },
  amountLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  amountInput: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
  },
  amountSeparator: {
    width: 16,
  },
  paymentMethodsContainer: {
    marginBottom: 16,
  },
  paymentMethodRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  paymentMethodText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  clearButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    marginRight: 8,
  },
  clearButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  applyButton: {
    flex: 2,
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
});