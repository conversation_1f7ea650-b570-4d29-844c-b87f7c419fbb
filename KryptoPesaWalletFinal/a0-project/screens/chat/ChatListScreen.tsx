import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import {
  chatService,
  userService,
  websocketService,
  EAST_AFRICAN_CONFIG,
  ChatConversation,
  eastAfricanHelpers
} from '../../services';

// Mock chat conversations data
const mockChatList = [
  {
    id: '1',
    traderId: '2',
    traderName: 'Alice Trader',
    traderAvatar: null,
    tradeId: 'trade_1',
    tradeContext: 'BTC/USD - 0.0025 BTC',
    lastMessage: {
      text: "I've just sent the payment. Please check your account.",
      timestamp: '2023-07-05T17:45:00Z',
      senderId: '1', // current user
    },
    unreadCount: 0,
    isOnline: true,
    tradeStatus: 'payment_sent',
  },
  {
    id: '2',
    traderId: '3',
    traderName: 'Bob Crypto',
    traderAvatar: null,
    tradeId: 'trade_2',
    tradeContext: 'ETH/USD - 0.5 ETH',
    lastMessage: {
      text: 'Thanks for the quick payment! Releasing crypto now.',
      timestamp: '2023-07-05T16:30:00Z',
      senderId: '3', // other trader
    },
    unreadCount: 2,
    isOnline: false,
    tradeStatus: 'pending_payment',
  },
  {
    id: '3',
    traderId: '4',
    traderName: 'Charlie Exchange',
    traderAvatar: null,
    tradeId: 'trade_3',
    tradeContext: 'USDT/USD - 500 USDT',
    lastMessage: {
      text: 'Perfect! Let me know when you receive the funds.',
      timestamp: '2023-07-05T15:20:00Z',
      senderId: '4', // other trader
    },
    unreadCount: 1,
    isOnline: true,
    tradeStatus: 'escrow_pending',
  },
  {
    id: '4',
    traderId: '5',
    traderName: 'Diana Swift',
    traderAvatar: null,
    tradeId: 'trade_4',
    tradeContext: 'BTC/USD - 0.1 BTC',
    lastMessage: {
      text: 'Trade completed successfully! Thanks for the smooth transaction.',
      timestamp: '2023-07-04T14:15:00Z',
      senderId: '5', // other trader
    },
    unreadCount: 0,
    isOnline: false,
    tradeStatus: 'completed',
  },
  {
    id: '5',
    traderId: '6',
    traderName: 'Erik Hodler',
    traderAvatar: null,
    tradeId: 'trade_5',
    tradeContext: 'ETH/USD - 2.0 ETH',
    lastMessage: {
      text: 'Hi, I\'m interested in your ETH offer. Is it still available?',
      timestamp: '2023-07-04T10:30:00Z',
      senderId: '6', // other trader
    },
    unreadCount: 3,
    isOnline: true,
    tradeStatus: 'pending',
  },
];

const getTradeStatusColor = (status, theme) => {
  switch (status) {
    case 'pending':
      return theme.colors.warning;
    case 'payment_sent':
      return theme.colors.primary;
    case 'pending_payment':
      return theme.colors.warning;
    case 'escrow_pending':
      return theme.colors.purple;
    case 'completed':
      return theme.colors.success;
    case 'disputed':
      return theme.colors.error;
    default:
      return theme.colors.textSecondary;
  }
};

const formatTimestamp = (timestamp) => {
  const now = new Date();
  const messageTime = new Date(timestamp);
  const diffInHours = (now - messageTime) / (1000 * 60 * 60);
  
  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now - messageTime) / (1000 * 60));
    return `${diffInMinutes}m`;
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}h`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d`;
  }
};

export default function ChatListScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();

  // State management
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState(null);

  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'active', 'completed'

  // Load chat conversations
  const loadConversations = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      const [chatList, profile] = await Promise.all([
        chatService.getConversations(),
        userService.getProfile(),
      ]);

      // Filter out invalid conversations
      const validConversations = (chatList || []).filter(conv =>
        conv &&
        conv._id &&
        conv.trade &&
        conv.trade._id &&
        conv.participants &&
        Array.isArray(conv.participants)
      );

      setConversations(validConversations);
      setUserProfile(profile);

      // Join chat rooms for real-time updates
      chatList.forEach(conversation => {
        if (conversation && conversation._id) {
          websocketService.joinRoom('chat', conversation._id);
        }
      });
    } catch (err) {
      console.error('Failed to load conversations:', err);
      setError(err instanceof Error ? err.message : 'Failed to load chats');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadConversations();

    // Set up WebSocket listeners for real-time updates
    const unsubscribeChatMessage = websocketService.onChatMessage((data) => {
      setConversations(prev =>
        prev.map(conv =>
          conv._id === data.chatId
            ? {
                ...conv,
                lastMessage: data.message,
                unreadCount: conv.unreadCount + (data.message.sender._id !== userProfile?._id ? 1 : 0)
              }
            : conv
        )
      );
    });

    const unsubscribeTradeUpdate = websocketService.onTradeUpdate((data) => {
      setConversations(prev =>
        prev.map(conv =>
          conv.trade._id === data.tradeId
            ? { ...conv, trade: { ...conv.trade, status: data.status } }
            : conv
        )
      );
    });

    return () => {
      unsubscribeChatMessage();
      unsubscribeTradeUpdate();
      // Leave all chat rooms
      conversations.forEach(conversation => {
        websocketService.leaveRoom('chat', conversation._id);
      });
    };
  }, []);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadConversations(false);
    setRefreshing(false);
  };

  // Check if current user is buyer or seller
  const isCurrentUserBuyer = (conversation: ChatConversation) => {
    return userProfile && conversation?.trade?.buyer?._id === userProfile._id;
  };

  const getOtherTrader = (conversation: ChatConversation) => {
    if (!userProfile || !conversation?.trade) return null;
    return isCurrentUserBuyer(conversation) ? conversation.trade.seller : conversation.trade.buyer;
  };

  const filteredChats = conversations.filter(conversation => {
    const otherTrader = getOtherTrader(conversation);

    // Skip if essential data is missing
    if (!otherTrader || !conversation?.trade) {
      return false;
    }

    const tradeContext = `${conversation.trade.cryptocurrency?.type || 'N/A'}/${conversation.trade.fiat?.currency || 'N/A'} - ${conversation.trade.cryptocurrency?.amount || 0} ${conversation.trade.cryptocurrency?.type || 'N/A'}`;

    // Search filter
    if (searchQuery &&
        !otherTrader.username?.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !tradeContext.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Status filter
    switch (filter) {
      case 'unread':
        return conversation.unreadCount > 0;
      case 'active':
        return ['pending', 'funded', 'payment_sent', 'payment_confirmed'].includes(conversation.trade.status);
      case 'completed':
        return conversation.trade.status === 'completed';
      default:
        return true;
    }
  });

  const renderChatItem = ({ item }: { item: ChatConversation }) => {
    const otherTrader = getOtherTrader(item);
    const isBuyer = isCurrentUserBuyer(item);

    // Skip rendering if essential data is missing
    if (!otherTrader || !item?._id || !item?.trade?._id) {
      return null;
    }

    return (
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => navigation.navigate('IndividualChat', {
          traderId: otherTrader._id,
          tradeId: item.trade._id,
          traderName: otherTrader.username,
          conversationId: item._id
        })}
      >
        <View style={styles.avatarContainer}>
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarText}>
              {otherTrader.username.charAt(0).toUpperCase()}
            </Text>
          </View>
          {otherTrader.isOnline && <View style={styles.onlineIndicator} />}
        </View>

        <View style={styles.chatContent}>
          <View style={styles.chatHeader}>
            <View style={styles.traderNameContainer}>
              <Text style={styles.traderName}>@{otherTrader.username}</Text>
              <Text style={styles.tradeRole}>
                {isBuyer ? 'Selling to you' : 'Buying from you'}
              </Text>
            </View>
            <View style={styles.rightHeader}>
              <Text style={styles.timestamp}>
                {item.lastMessage ? formatTimestamp(item.lastMessage.timestamp) : ''}
              </Text>
              {item.unreadCount > 0 && (
                <View style={styles.unreadBadge}>
                  <Text style={styles.unreadCount}>{item.unreadCount}</Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.tradeContextContainer}>
            <View style={[styles.statusDot, { backgroundColor: getTradeStatusColor(item.trade.status, theme) }]} />
            <View style={styles.cryptoIconContainer}>
              <Text style={styles.cryptoIconText}>
                {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[item.trade.cryptocurrency.type]?.symbol || item.trade.cryptocurrency.type}
              </Text>
            </View>
            <Text style={styles.tradeContext}>
              {item.trade.cryptocurrency.amount} {item.trade.cryptocurrency.type} • {eastAfricanHelpers.formatCurrencyAmount(item.trade.fiat.amount, item.trade.fiat.currency)}
            </Text>
            <Text style={styles.paymentMethodIcon}>
              {EAST_AFRICAN_CONFIG.PAYMENT_METHODS[item.trade.paymentMethod]?.icon}
            </Text>
          </View>

          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage ? (
              <>
                {item.lastMessage.sender._id === userProfile?._id ? 'You: ' : ''}
                {item.lastMessage.content}
              </>
            ) : (
              'No messages yet'
            )}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="chatbubbles-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyStateTitle}>No Conversations</Text>
      <Text style={styles.emptyStateText}>
        Start trading to begin conversations with other traders.
      </Text>
      <TouchableOpacity 
        style={styles.browseOffersButton}
        onPress={() => navigation.navigate('Offers')}
      >
        <Text style={styles.browseOffersButtonText}>Browse Offers</Text>
      </TouchableOpacity>
    </View>
  );

  const totalUnreadCount = conversations.reduce((sum, chat) => sum + chat.unreadCount, 0);
  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>KryptoPesa Chats</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading your conversations...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>KryptoPesa Chats</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load chats</Text>
          <Text style={styles.errorSubText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadConversations()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>KryptoPesa Chats</Text>
        <View style={styles.headerRight}>
          {totalUnreadCount > 0 && (
            <View style={styles.totalUnreadBadge}>
              <Text style={styles.totalUnreadText}>{totalUnreadCount}</Text>
            </View>
          )}
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('Offers')}
          >
            <Ionicons name="add-circle-outline" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search conversations..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={theme.colors.textSecondary}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterTab, filter === 'all' && styles.activeFilterTab]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterTabText, filter === 'all' && styles.activeFilterTabText]}>
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterTab, filter === 'unread' && styles.activeFilterTab]}
          onPress={() => setFilter('unread')}
        >
          <Text style={[styles.filterTabText, filter === 'unread' && styles.activeFilterTabText]}>
            Unread ({conversations.filter(c => c.unreadCount > 0).length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterTab, filter === 'active' && styles.activeFilterTab]}
          onPress={() => setFilter('active')}
        >
          <Text style={[styles.filterTabText, filter === 'active' && styles.activeFilterTabText]}>
            Active ({conversations.filter(c => ['pending', 'funded', 'payment_sent', 'payment_confirmed'].includes(c.trade.status)).length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterTab, filter === 'completed' && styles.activeFilterTab]}
          onPress={() => setFilter('completed')}
        >
          <Text style={[styles.filterTabText, filter === 'completed' && styles.activeFilterTabText]}>
            Completed ({conversations.filter(c => c.trade.status === 'completed').length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Chat List */}
      <FlatList
        data={filteredChats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  totalUnreadBadge: {
    backgroundColor: theme.colors.error,
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  totalUnreadText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  headerButton: {
    padding: 4,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
  },
  filterContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  filterTab: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  activeFilterTab: {
    backgroundColor: theme.colors.primaryLight,
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  activeFilterTabText: {
    color: theme.colors.primaryDark,
    fontWeight: '600',
  },
  listContainer: {
    flexGrow: 1,
  },
  chatItem: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.success,
    borderWidth: 2,
    borderColor: theme.colors.surface,
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  traderName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  rightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timestamp: {
    fontSize: 12,
    color: theme.colors.textTertiary,
  },
  unreadBadge: {
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  tradeContextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  tradeContext: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  lastMessage: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  browseOffersButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseOffersButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  // New styles for East African features
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 12,
    textAlign: 'center',
  },
  errorSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  traderNameContainer: {
    flex: 1,
  },
  tradeRole: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  cryptoIconContainer: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 6,
  },
  cryptoIconText: {
    color: '#FFFFFF',
    fontSize: 8,
    fontWeight: 'bold',
  },
  paymentMethodIcon: {
    fontSize: 12,
    marginLeft: 6,
  },
});
