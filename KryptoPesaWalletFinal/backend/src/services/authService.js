const crypto = require('crypto');
const { ethers } = require('ethers');
const WalletUser = require('../models/WalletUser');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

/**
 * Wallet-based authentication service for non-custodial KryptoPesa
 * Replaces traditional JWT/password authentication with cryptographic wallet signatures
 */
class AuthService {
  constructor() {
    this.redisClient = getRedisClient();
    this.challengeExpiry = 5 * 60; // 5 minutes for challenge expiry
  }

  /**
   * Generate authentication challenge for wallet
   */
  async generateChallenge(walletAddress, action = 'login') {
    try {
      const timestamp = Date.now();
      const nonce = crypto.randomBytes(16).toString('hex');
      
      const challenge = {
        walletAddress: walletAddress.toLowerCase(),
        action,
        timestamp,
        nonce,
        message: `KryptoPesa Authentication\nTimestamp: ${timestamp}\nAction: ${action}\nWallet: ${walletAddress}\nNonce: ${nonce}`
      };

      // Store challenge in Redis with expiry
      const challengeKey = `auth_challenge:${walletAddress.toLowerCase()}:${nonce}`;
      await this.redisClient.setex(challengeKey, this.challengeExpiry, JSON.stringify(challenge));

      return {
        challenge: challenge.message,
        nonce,
        expiresAt: timestamp + (this.challengeExpiry * 1000)
      };
    } catch (error) {
      logger.error('Error generating challenge:', error);
      throw new AppError('Failed to generate authentication challenge', 500);
    }
  }

  /**
   * Verify wallet signature and authenticate user
   */
  async verifySignature(walletAddress, signature, nonce) {
    try {
      // Retrieve challenge from Redis
      const challengeKey = `auth_challenge:${walletAddress.toLowerCase()}:${nonce}`;
      const challengeData = await this.redisClient.get(challengeKey);
      
      if (!challengeData) {
        throw new AppError('Challenge expired or not found', 401);
      }

      const challenge = JSON.parse(challengeData);
      
      // Verify the signature
      const recoveredAddress = ethers.utils.verifyMessage(challenge.message, signature);
      
      if (recoveredAddress.toLowerCase() !== walletAddress.toLowerCase()) {
        throw new AppError('Invalid signature', 401);
      }

      // Clean up used challenge
      await this.redisClient.del(challengeKey);

      // Find or create wallet user
      let user = await WalletUser.findByWalletAddress(walletAddress);
      
      if (!user && challenge.action === 'register') {
        // Create new wallet user
        user = new WalletUser({
          walletAddress: walletAddress.toLowerCase(),
          publicKey: recoveredAddress, // Store the recovered address as verification
          profile: {
            displayName: `User_${walletAddress.slice(-6)}`, // Default display name
          },
          createdAt: new Date(),
          lastLoginAt: new Date()
        });
        await user.save();
        logger.info(`New wallet user created: ${walletAddress}`);
      } else if (user) {
        // Update last login
        user.lastLoginAt = new Date();
        await user.save();
      }

      return {
        user,
        authenticated: true,
        walletAddress: walletAddress.toLowerCase()
      };
    } catch (error) {
      logger.error('Signature verification failed:', error);
      throw new AppError('Authentication failed', 401);
    }
  }

  /**
   * Validate wallet address format
   */
  isValidWalletAddress(address) {
    try {
      return ethers.utils.isAddress(address);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get user by wallet address
   */
  async getUserByWallet(walletAddress) {
    try {
      return await WalletUser.findByWalletAddress(walletAddress);
    } catch (error) {
      logger.error('Error getting user by wallet:', error);
      return null;
    }
  }

  /**
   * Legacy methods for compatibility - simplified for wallet-only auth
   */
  async validatePassword() {
    // No password validation needed for wallet auth
    return { isValid: true, errors: [] };
  }

  async verifyAccessToken() {
    // No JWT tokens in wallet auth
    throw new AppError('JWT tokens not supported in wallet-only authentication', 400);
  }
}

module.exports = new AuthService();
