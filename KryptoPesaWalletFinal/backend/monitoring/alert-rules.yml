# Comprehensive Alert Rules for KryptoPesa Production Monitoring
# Enterprise-grade alerting for financial trading platform

groups:
  # Critical System Alerts
  - name: critical_system_alerts
    rules:
      # High CPU Usage
      - alert: HighCPUUsage
        expr: system_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for more than 5 minutes"
          runbook_url: "https://docs.kryptopesa.com/runbooks/high-cpu"

      # Critical CPU Usage
      - alert: CriticalCPUUsage
        expr: system_cpu_usage_percent > 90
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "Critical CPU usage detected"
          description: "CPU usage is {{ $value }}% for more than 2 minutes"
          runbook_url: "https://docs.kryptopesa.com/runbooks/critical-cpu"

      # High Memory Usage
      - alert: HighMemoryUsage
        expr: (system_memory_usage_bytes / system_memory_total_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% for more than 5 minutes"

      # Critical Memory Usage
      - alert: CriticalMemoryUsage
        expr: (system_memory_usage_bytes / system_memory_total_bytes) * 100 > 95
        for: 1m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "Critical memory usage detected"
          description: "Memory usage is {{ $value }}% - immediate action required"

      # Disk Space Warning
      - alert: DiskSpaceWarning
        expr: system_disk_usage_percent > 80
        for: 10m
        labels:
          severity: warning
          category: storage
        annotations:
          summary: "Disk space running low"
          description: "Disk usage is {{ $value }}% on {{ $labels.device }}"

      # Critical Disk Space
      - alert: CriticalDiskSpace
        expr: system_disk_usage_percent > 90
        for: 5m
        labels:
          severity: critical
          category: storage
        annotations:
          summary: "Critical disk space shortage"
          description: "Disk usage is {{ $value }}% on {{ $labels.device }} - immediate cleanup required"

  # Application Performance Alerts
  - name: application_performance_alerts
    rules:
      # High Response Time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.route }}"

      # Critical Response Time
      - alert: CriticalResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: critical
          category: performance
        annotations:
          summary: "Critical API response time"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.route }}"

      # High Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          category: errors
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.route }}"

      # Critical Error Rate
      - alert: CriticalErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.10
        for: 1m
        labels:
          severity: critical
          category: errors
        annotations:
          summary: "Critical error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.route }}"

      # Service Down
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: availability
        annotations:
          summary: "Service is down"
          description: "{{ $labels.instance }} has been down for more than 1 minute"

  # Database Alerts
  - name: database_alerts
    rules:
      # High Database Connections
      - alert: HighDatabaseConnections
        expr: database_connections_active > 40
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "High database connection count"
          description: "Active database connections: {{ $value }}/50"

      # Critical Database Connections
      - alert: CriticalDatabaseConnections
        expr: database_connections_active > 45
        for: 2m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "Critical database connection count"
          description: "Active database connections: {{ $value }}/50 - connection pool exhaustion imminent"

      # Slow Database Queries
      - alert: SlowDatabaseQueries
        expr: histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Slow database queries detected"
          description: "95th percentile query time is {{ $value }}s for {{ $labels.operation }}"

      # Database Connection Failure
      - alert: DatabaseConnectionFailure
        expr: increase(database_connection_errors_total[5m]) > 5
        for: 1m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "Database connection failures"
          description: "{{ $value }} database connection failures in the last 5 minutes"

  # Security Alerts
  - name: security_alerts
    rules:
      # High Failed Login Rate
      - alert: HighFailedLoginRate
        expr: rate(failed_logins_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High failed login rate"
          description: "{{ $value }} failed login attempts per second"

      # Critical Failed Login Rate
      - alert: CriticalFailedLoginRate
        expr: rate(failed_logins_total[5m]) > 50
        for: 1m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Critical failed login rate - possible attack"
          description: "{{ $value }} failed login attempts per second - potential brute force attack"

      # Security Events
      - alert: SecurityEventDetected
        expr: increase(security_events_total{severity="high"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "High severity security event detected"
          description: "{{ $value }} high severity security events in the last 5 minutes"

      # Suspicious Activity
      - alert: SuspiciousActivity
        expr: rate(security_events_total{event_type="suspicious_activity"}[10m]) > 5
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Suspicious activity detected"
          description: "{{ $value }} suspicious activities per second"

  # Business Logic Alerts
  - name: business_alerts
    rules:
      # Low Trading Volume
      - alert: LowTradingVolume
        expr: rate(trading_volume_total[1h]) < 100
        for: 30m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "Low trading volume"
          description: "Trading volume is {{ $value }} per hour - below normal threshold"

      # High Escrow Transaction Failures
      - alert: HighEscrowFailures
        expr: rate(escrow_transactions_total{status="failed"}[10m]) > 0.1
        for: 5m
        labels:
          severity: critical
          category: business
        annotations:
          summary: "High escrow transaction failure rate"
          description: "{{ $value }} escrow transactions failing per second"

      # Wallet Transaction Anomaly
      - alert: WalletTransactionAnomaly
        expr: rate(wallet_transactions_total{status="failed"}[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "High wallet transaction failure rate"
          description: "{{ $value }} wallet transactions failing per second"

      # No Active Users
      - alert: NoActiveUsers
        expr: active_users_total == 0
        for: 10m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "No active users detected"
          description: "No active users for the last 10 minutes"

  # Cache Performance Alerts
  - name: cache_alerts
    rules:
      # Low Cache Hit Rate
      - alert: LowCacheHitRate
        expr: (rate(cache_hits_total[10m]) / (rate(cache_hits_total[10m]) + rate(cache_misses_total[10m]))) * 100 < 70
        for: 10m
        labels:
          severity: warning
          category: cache
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value }}% - performance may be degraded"

      # Critical Cache Hit Rate
      - alert: CriticalCacheHitRate
        expr: (rate(cache_hits_total[10m]) / (rate(cache_hits_total[10m]) + rate(cache_misses_total[10m]))) * 100 < 50
        for: 5m
        labels:
          severity: critical
          category: cache
        annotations:
          summary: "Critical cache hit rate"
          description: "Cache hit rate is {{ $value }}% - immediate investigation required"

  # External Dependencies
  - name: external_dependency_alerts
    rules:
      # Blockchain API Failure
      - alert: BlockchainAPIFailure
        expr: increase(external_api_errors_total{service="blockchain"}[5m]) > 3
        for: 2m
        labels:
          severity: critical
          category: external
        annotations:
          summary: "Blockchain API failures"
          description: "{{ $value }} blockchain API failures in the last 5 minutes"

      # Payment Gateway Issues
      - alert: PaymentGatewayIssues
        expr: increase(external_api_errors_total{service="payment"}[5m]) > 2
        for: 3m
        labels:
          severity: warning
          category: external
        annotations:
          summary: "Payment gateway issues"
          description: "{{ $value }} payment gateway errors in the last 5 minutes"

  # Infrastructure Alerts
  - name: infrastructure_alerts
    rules:
      # Load Balancer Health
      - alert: LoadBalancerUnhealthy
        expr: nginx_up == 0
        for: 1m
        labels:
          severity: critical
          category: infrastructure
        annotations:
          summary: "Load balancer is down"
          description: "Nginx load balancer is not responding"

      # Container Restart Loop
      - alert: ContainerRestartLoop
        expr: increase(container_restarts_total[10m]) > 3
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "Container restart loop detected"
          description: "Container {{ $labels.container }} has restarted {{ $value }} times in 10 minutes"

      # High Network Latency
      - alert: HighNetworkLatency
        expr: network_latency_seconds > 0.5
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "High network latency"
          description: "Network latency is {{ $value }}s"

  # Compliance and Audit Alerts
  - name: compliance_alerts
    rules:
      # Audit Log Failures
      - alert: AuditLogFailures
        expr: increase(audit_log_errors_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: compliance
        annotations:
          summary: "Audit log failures detected"
          description: "{{ $value }} audit log failures - compliance risk"

      # Data Retention Violation
      - alert: DataRetentionViolation
        expr: data_retention_violations_total > 0
        for: 0m
        labels:
          severity: critical
          category: compliance
        annotations:
          summary: "Data retention policy violation"
          description: "{{ $value }} data retention violations detected"

      # Backup Failures
      - alert: BackupFailure
        expr: increase(backup_failures_total[24h]) > 0
        for: 0m
        labels:
          severity: critical
          category: backup
        annotations:
          summary: "Backup failure detected"
          description: "{{ $value }} backup failures in the last 24 hours"

      # Missing Backups
      - alert: MissingBackups
        expr: time() - backup_last_success_timestamp > 86400
        for: 1h
        labels:
          severity: critical
          category: backup
        annotations:
          summary: "Missing recent backups"
          description: "No successful backup in the last 24 hours"
