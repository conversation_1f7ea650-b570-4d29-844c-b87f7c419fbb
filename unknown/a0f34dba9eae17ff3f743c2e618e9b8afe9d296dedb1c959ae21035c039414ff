#!/bin/bash

# KryptoPesa Mobile App Deployment Script for Pixel 7
# Builds and installs the React Native app directly to your device

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites for mobile development
check_mobile_prerequisites() {
    print_status "Checking mobile development prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed."
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed."
        exit 1
    fi
    
    # Check if we have React Native CLI
    if ! command_exists npx; then
        print_error "npx is not available."
        exit 1
    fi
    
    # Check Android SDK
    if [ -z "$ANDROID_HOME" ]; then
        print_warning "ANDROID_HOME environment variable is not set."
        print_warning "Trying to detect Android SDK..."
        
        # Common Android SDK locations
        local possible_paths=(
            "$HOME/Library/Android/sdk"
            "$HOME/Android/Sdk"
            "/usr/local/android-sdk"
            "/opt/android-sdk"
        )
        
        for path in "${possible_paths[@]}"; do
            if [ -d "$path" ]; then
                export ANDROID_HOME="$path"
                export PATH="$PATH:$ANDROID_HOME/emulator:$ANDROID_HOME/tools:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools"
                print_success "Found Android SDK at: $ANDROID_HOME"
                break
            fi
        done
        
        if [ -z "$ANDROID_HOME" ]; then
            print_error "Android SDK not found. Please install Android Studio or Android SDK."
            print_error "Download from: https://developer.android.com/studio"
            exit 1
        fi
    fi
    
    # Check ADB
    if ! command_exists adb; then
        print_error "ADB (Android Debug Bridge) not found."
        print_error "Please ensure Android SDK platform-tools are installed and in PATH."
        exit 1
    fi
    
    # Check Java
    if ! command_exists java; then
        print_error "Java is not installed. Please install JDK 11 or later."
        exit 1
    fi
    
    print_success "Mobile development prerequisites check passed!"
}

# Check device connection
check_device_connection() {
    print_status "Checking device connection..."
    
    # List connected devices
    local devices=$(adb devices | grep -v "List of devices" | grep "device$")
    local device_count=$(echo "$devices" | grep -c "device$" || true)
    
    if [ "$device_count" -eq 0 ]; then
        print_error "No Android devices detected!"
        print_error ""
        print_error "Please ensure:"
        print_error "1. Your Pixel 7 is connected via USB"
        print_error "2. USB debugging is enabled in Developer Options"
        print_error "3. You've authorized the computer for debugging"
        print_error ""
        print_error "To enable USB debugging:"
        print_error "1. Go to Settings > About phone"
        print_error "2. Tap 'Build number' 7 times to enable Developer Options"
        print_error "3. Go to Settings > System > Developer Options"
        print_error "4. Enable 'USB debugging'"
        print_error "5. Connect your phone and authorize this computer"
        exit 1
    elif [ "$device_count" -eq 1 ]; then
        local device_id=$(echo "$devices" | awk '{print $1}')
        print_success "Device detected: $device_id"
        
        # Get device info
        local device_model=$(adb -s "$device_id" shell getprop ro.product.model 2>/dev/null || echo "Unknown")
        local android_version=$(adb -s "$device_id" shell getprop ro.build.version.release 2>/dev/null || echo "Unknown")
        
        print_status "Device Model: $device_model"
        print_status "Android Version: $android_version"
        
        # Verify it's a Pixel 7
        if [[ "$device_model" == *"Pixel 7"* ]]; then
            print_success "Confirmed: Pixel 7 detected!"
        else
            print_warning "Device model doesn't appear to be Pixel 7, but continuing anyway..."
        fi
        
        export TARGET_DEVICE="$device_id"
    else
        print_warning "Multiple devices detected. Using the first one:"
        echo "$devices"
        export TARGET_DEVICE=$(echo "$devices" | head -1 | awk '{print $1}')
        print_status "Selected device: $TARGET_DEVICE"
    fi
}

# Install mobile dependencies
install_mobile_dependencies() {
    print_status "Installing mobile app dependencies..."
    
    cd mobile
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing npm dependencies..."
        npm install
    else
        print_status "Dependencies already installed, checking for updates..."
        npm install
    fi
    
    # Install pods for iOS (if on macOS, though we're targeting Android)
    if [[ "$OSTYPE" == "darwin"* ]] && [ -d "ios" ]; then
        print_status "Installing iOS pods (for future iOS builds)..."
        cd ios
        pod install --repo-update || print_warning "Pod install failed, but continuing with Android build..."
        cd ..
    fi
    
    cd ..
    print_success "Mobile dependencies installed!"
}

# Configure environment for device
configure_environment() {
    print_status "Configuring environment for device deployment..."
    
    cd mobile
    
    # Get the local IP address for API connection
    local local_ip
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        local_ip=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | head -1 | awk '{print $2}')
    else
        # Linux
        local_ip=$(hostname -I | awk '{print $1}')
    fi
    
    if [ -z "$local_ip" ]; then
        local_ip="*************"  # Fallback IP
        print_warning "Could not detect local IP, using fallback: $local_ip"
        print_warning "You may need to update this manually in the app configuration"
    else
        print_success "Detected local IP: $local_ip"
    fi
    
    # Create or update .env file for mobile
    cat > .env << EOF
# KryptoPesa Mobile App Environment Configuration
# Generated for device deployment

# API Configuration
REACT_NATIVE_API_URL=http://${local_ip}:3000
REACT_NATIVE_WS_URL=ws://${local_ip}:3000

# App Configuration
REACT_NATIVE_APP_NAME=KryptoPesa
REACT_NATIVE_APP_VERSION=1.0.0
REACT_NATIVE_ENVIRONMENT=development

# Feature Flags
REACT_NATIVE_ENABLE_BIOMETRICS=true
REACT_NATIVE_ENABLE_PUSH_NOTIFICATIONS=true
REACT_NATIVE_ENABLE_OFFLINE_MODE=true

# Debug Settings
REACT_NATIVE_DEBUG_MODE=true
REACT_NATIVE_LOG_LEVEL=info
EOF
    
    print_success "Environment configured for IP: $local_ip"
    cd ..
}

# Build and deploy the app
build_and_deploy() {
    print_status "Building and deploying KryptoPesa mobile app..."
    
    cd mobile
    
    # Clean previous builds
    print_status "Cleaning previous builds..."
    npx react-native clean || true
    rm -rf android/app/build || true
    
    # Start Metro bundler in background
    print_status "Starting Metro bundler..."
    npx react-native start --reset-cache > ../logs/metro.log 2>&1 &
    local metro_pid=$!
    echo $metro_pid > ../logs/metro.pid
    
    # Wait for Metro to start
    print_status "Waiting for Metro bundler to start..."
    sleep 10
    
    # Build and install the app
    print_status "Building and installing app on device..."
    print_status "This may take several minutes on first build..."
    
    # Run the Android build and install
    if npx react-native run-android --device="$TARGET_DEVICE" --verbose; then
        print_success "🎉 App successfully installed on your Pixel 7!"
        print_success ""
        print_success "📱 KryptoPesa is now running on your device!"
        print_success ""
        print_status "📋 What you can do now:"
        print_status "  • Test user registration and login"
        print_status "  • Create and browse crypto offers"
        print_status "  • Test the P2P trading workflow"
        print_status "  • Try offline functionality"
        print_status "  • Test real-time notifications"
        print_status ""
        print_status "🔧 Development tools:"
        print_status "  • Shake device to open developer menu"
        print_status "  • Enable 'Live Reload' for instant updates"
        print_status "  • Check logs: adb logcat | grep ReactNativeJS"
    else
        print_error "Failed to build and install the app!"
        print_error "Check the logs for more details:"
        print_error "  • Metro logs: logs/metro.log"
        print_error "  • Android logs: adb logcat"
        
        # Kill Metro bundler
        if [ -f "../logs/metro.pid" ]; then
            kill $(cat ../logs/metro.pid) 2>/dev/null || true
            rm ../logs/metro.pid
        fi
        
        cd ..
        exit 1
    fi
    
    cd ..
}

# Show post-deployment instructions
show_instructions() {
    print_success "🚀 Deployment Complete!"
    echo "=================================================="
    print_status "📱 KryptoPesa Mobile App is now installed on your Pixel 7"
    echo ""
    print_status "🔗 System URLs:"
    print_status "  • Admin Dashboard: http://localhost:3001"
    print_status "  • Backend API: http://localhost:3000"
    print_status "  • Mobile App: Installed on device"
    echo ""
    print_status "🧪 Testing Checklist:"
    print_status "  ✓ Open the app on your Pixel 7"
    print_status "  ✓ Test user registration"
    print_status "  ✓ Create a crypto offer"
    print_status "  ✓ Browse available offers"
    print_status "  ✓ Initiate a trade"
    print_status "  ✓ Test chat functionality"
    print_status "  ✓ Check admin dashboard for activity"
    echo ""
    print_status "🛠️ Development Commands:"
    print_status "  • Reload app: adb shell input keyevent 82, then tap 'Reload'"
    print_status "  • View logs: adb logcat | grep ReactNativeJS"
    print_status "  • Restart Metro: cd mobile && npx react-native start"
    print_status "  • Reinstall: cd mobile && npx react-native run-android"
    echo ""
    print_status "🆘 Troubleshooting:"
    print_status "  • If app crashes: Check adb logcat for errors"
    print_status "  • If API not reachable: Verify backend is running on correct IP"
    print_status "  • If build fails: Try 'cd mobile && npx react-native clean'"
    echo "=================================================="
}

# Main execution
main() {
    print_status "📱 Deploying KryptoPesa to Pixel 7..."
    echo "=================================================="
    
    # Change to project directory
    cd "$(dirname "$0")/.."
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    # Check prerequisites
    check_mobile_prerequisites
    
    # Check device connection
    check_device_connection
    
    # Install dependencies
    install_mobile_dependencies
    
    # Configure environment
    configure_environment
    
    # Build and deploy
    build_and_deploy
    
    # Show instructions
    show_instructions
}

# Handle script interruption
trap 'print_error "Deployment interrupted!"; exit 1' INT TERM

# Run main function
main "$@"
