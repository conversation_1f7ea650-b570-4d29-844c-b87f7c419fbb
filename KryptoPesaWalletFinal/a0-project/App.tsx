import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StyleSheet } from 'react-native';
import { SafeAreaProvider } from "react-native-safe-area-context";

// Screens
import HomeScreen from "./screens/HomeScreen";
import LoginScreen from "./screens/auth/LoginScreen";
import RegisterScreen from "./screens/auth/RegisterScreen";
import ForgotPasswordScreen from "./screens/auth/ForgotPasswordScreen";
import DashboardScreen from "./screens/dashboard/DashboardScreen";
import OffersScreen from "./screens/offers/OffersScreen";
import CreateOfferScreen from "./screens/offers/CreateOfferScreen";
import OfferDetailScreen from "./screens/offers/OfferDetailScreen";
import TradeScreen from "./screens/trades/TradeScreen";
import ActiveTradesScreen from "./screens/trades/ActiveTradesScreen";
import WalletScreen from "./screens/wallet/WalletScreen";
import TransactionDetailScreen from "./screens/wallet/TransactionDetailScreen";
import ProfileScreen from "./screens/profile/ProfileScreen";
import TraderProfileScreen from "./screens/profile/TraderProfileScreen";
import KYCVerificationScreen from "./screens/profile/KYCVerificationScreen";
import ChatListScreen from "./screens/chat/ChatListScreen";
import IndividualChatScreen from "./screens/chat/IndividualChatScreen";
import NotificationCenterScreen from "./screens/notifications/NotificationCenterScreen";
import SettingsScreen from "./screens/settings/SettingsScreen";
import HelpSupportScreen from "./screens/help/HelpSupportScreen";

// Auth Context
import { AuthProvider, useAuth } from "./context/AuthContext";

// Theme Context
import { ThemeProvider, useTheme } from "./context/ThemeContext";

// KYC Context
import { KYCProvider } from "./context/KYCContext";

// Notification Context
import { NotificationProvider } from "./context/NotificationContext";

const Stack = createNativeStackNavigator();

function AuthStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    </Stack.Navigator>
  );
}

function AppStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Dashboard" component={DashboardScreen} />
      <Stack.Screen name="Offers" component={OffersScreen} />
      <Stack.Screen name="CreateOffer" component={CreateOfferScreen} />
      <Stack.Screen name="OfferDetail" component={OfferDetailScreen} />
      <Stack.Screen name="Trade" component={TradeScreen} />
      <Stack.Screen name="ActiveTrades" component={ActiveTradesScreen} />
      <Stack.Screen name="Wallet" component={WalletScreen} />
      <Stack.Screen name="TransactionDetail" component={TransactionDetailScreen} />
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen name="TraderProfile" component={TraderProfileScreen} />
      <Stack.Screen name="KYCVerification" component={KYCVerificationScreen} />
      <Stack.Screen name="Chat" component={ChatListScreen} />
      <Stack.Screen name="IndividualChat" component={IndividualChatScreen} />
      <Stack.Screen name="NotificationCenter" component={NotificationCenterScreen} />
      <Stack.Screen name="Settings" component={SettingsScreen} />
      <Stack.Screen name="HelpSupport" component={HelpSupportScreen} />
    </Stack.Navigator>
  );
}

function RootNavigator() {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <HomeScreen />;
  }
  
  return isAuthenticated ? <AppStack /> : <AuthStack />;
}

export default function App() {
  return (
    <SafeAreaProvider style={styles.container}>
      <ThemeProvider>
        <AuthProvider>
          <KYCProvider>
            <NotificationProvider>
              <NavigationContainer>
                <RootNavigator />
              </NavigationContainer>
            </NotificationProvider>
          </KYCProvider>
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    userSelect: "none"
  }
});