import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { walletAuthService, authTokens, initializeServices, cleanupServices } from '../services';
// import * as LocalAuthentication from 'expo-local-authentication';

interface WalletData {
  addresses: {
    ethereum: string;
    bitcoin: string;
    polygon: string;
  };
  isBackedUp: boolean;
  createdAt: string;
}

interface UserProfile {
  id: string;
  name?: string;
  country?: string;
  city?: string;
  profileComplete: boolean;
  kycLevel: number;
  reputation: number;
  totalTrades: number;
}

interface WalletAuthContextType {
  // Wallet state
  isWalletSetup: boolean;
  isWalletUnlocked: boolean;
  walletData: WalletData | null;
  userProfile: UserProfile | null;
  
  // Loading states
  isLoading: boolean;
  isInitializing: boolean;
  
  // Wallet actions
  createWallet: (mnemonic: string[], pin: string) => Promise<void>;
  importWallet: (mnemonic: string[], pin: string) => Promise<void>;
  unlockWallet: (pin: string) => Promise<boolean>;
  lockWallet: () => void;
  
  // Profile actions
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>;
  
  // Security actions
  verifyPin: (pin: string) => Promise<boolean>;
  changePIN: (oldPin: string, newPin: string) => Promise<boolean>;
  
  // Biometric actions
  isBiometricsEnabled: boolean;
  enableBiometrics: () => Promise<boolean>;
  disableBiometrics: () => Promise<void>;
  authenticateWithBiometrics: () => Promise<boolean>;
}

const WalletAuthContext = createContext<WalletAuthContextType | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  WALLET_EXISTS: 'wallet_exists',
  WALLET_DATA: 'wallet_data',
  USER_PROFILE: 'user_profile',
  PIN_HASH: 'pin_hash',
  BIOMETRICS_ENABLED: 'biometrics_enabled',
  ENCRYPTED_MNEMONIC: 'encrypted_mnemonic',
};

interface WalletAuthProviderProps {
  children: ReactNode;
}

export function WalletAuthProvider({ children }: WalletAuthProviderProps) {
  const [isWalletSetup, setIsWalletSetup] = useState(false);
  const [isWalletUnlocked, setIsWalletUnlocked] = useState(false);
  const [walletData, setWalletData] = useState<WalletData | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [isBiometricsEnabled, setIsBiometricsEnabled] = useState(false);

  useEffect(() => {
    initializeWallet();
  }, []);

  const initializeWallet = async () => {
    try {
      setIsInitializing(true);
      
      // Check if wallet exists
      const walletExists = await AsyncStorage.getItem(STORAGE_KEYS.WALLET_EXISTS);
      setIsWalletSetup(walletExists === 'true');
      
      if (walletExists === 'true') {
        // Load wallet data and user profile
        const [storedWalletData, storedProfile, biometricsEnabled] = await Promise.all([
          AsyncStorage.getItem(STORAGE_KEYS.WALLET_DATA),
          AsyncStorage.getItem(STORAGE_KEYS.USER_PROFILE),
          AsyncStorage.getItem(STORAGE_KEYS.BIOMETRICS_ENABLED),
        ]);
        
        if (storedWalletData) {
          setWalletData(JSON.parse(storedWalletData));
        }
        
        if (storedProfile) {
          setUserProfile(JSON.parse(storedProfile));
        }
        
        setIsBiometricsEnabled(biometricsEnabled === 'true');
      }
    } catch (error) {
      console.error('Failed to initialize wallet:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const generateWalletAddresses = (mnemonic: string[]): WalletData['addresses'] => {
    // In a real app, you'd use proper crypto libraries to derive addresses
    const seed = mnemonic.join('');
    const hash = seed.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    return {
      ethereum: `0x${Math.abs(hash).toString(16).padStart(40, '0')}`,
      bitcoin: `1${Math.abs(hash * 2).toString(36).toUpperCase().slice(0, 33)}`,
      polygon: `0x${Math.abs(hash * 3).toString(16).padStart(40, '0')}`,
    };
  };

  const hashPin = (pin: string): string => {
    // Simple hash for demo - use proper crypto in production
    return pin.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0).toString();
  };

  const createWallet = async (mnemonic: string[], pin: string) => {
    try {
      setIsLoading(true);
      
      const addresses = generateWalletAddresses(mnemonic);
      const newWalletData: WalletData = {
        addresses,
        isBackedUp: true,
        createdAt: new Date().toISOString(),
      };
      
      const newProfile: UserProfile = {
        id: `user_${Date.now()}`,
        profileComplete: false,
        kycLevel: 0,
        reputation: 0,
        totalTrades: 0,
      };
      
      // Store wallet data
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.WALLET_EXISTS, 'true'),
        AsyncStorage.setItem(STORAGE_KEYS.WALLET_DATA, JSON.stringify(newWalletData)),
        AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(newProfile)),
        AsyncStorage.setItem(STORAGE_KEYS.PIN_HASH, hashPin(pin)),
        // In production, encrypt the mnemonic properly
        AsyncStorage.setItem(STORAGE_KEYS.ENCRYPTED_MNEMONIC, JSON.stringify(mnemonic)),
      ]);
      
      setIsWalletSetup(true);
      setWalletData(newWalletData);
      setUserProfile(newProfile);
      setIsWalletUnlocked(true);
    } catch (error) {
      console.error('Failed to create wallet:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const importWallet = async (mnemonic: string[], pin: string) => {
    try {
      setIsLoading(true);
      
      const addresses = generateWalletAddresses(mnemonic);
      const importedWalletData: WalletData = {
        addresses,
        isBackedUp: true,
        createdAt: new Date().toISOString(),
      };
      
      const newProfile: UserProfile = {
        id: `user_${Date.now()}`,
        profileComplete: false,
        kycLevel: 0,
        reputation: 0,
        totalTrades: 0,
      };
      
      // Store wallet data
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.WALLET_EXISTS, 'true'),
        AsyncStorage.setItem(STORAGE_KEYS.WALLET_DATA, JSON.stringify(importedWalletData)),
        AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(newProfile)),
        AsyncStorage.setItem(STORAGE_KEYS.PIN_HASH, hashPin(pin)),
        AsyncStorage.setItem(STORAGE_KEYS.ENCRYPTED_MNEMONIC, JSON.stringify(mnemonic)),
      ]);
      
      setIsWalletSetup(true);
      setWalletData(importedWalletData);
      setUserProfile(newProfile);
      setIsWalletUnlocked(true);
    } catch (error) {
      console.error('Failed to import wallet:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const unlockWallet = async (pin: string): Promise<boolean> => {
    try {
      const storedPinHash = await AsyncStorage.getItem(STORAGE_KEYS.PIN_HASH);
      const enteredPinHash = hashPin(pin);
      
      if (storedPinHash === enteredPinHash) {
        setIsWalletUnlocked(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to unlock wallet:', error);
      return false;
    }
  };

  const lockWallet = () => {
    setIsWalletUnlocked(false);
  };

  const updateProfile = async (profileUpdates: Partial<UserProfile>) => {
    try {
      if (!userProfile) return;
      
      const updatedProfile = { ...userProfile, ...profileUpdates };
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(updatedProfile));
      setUserProfile(updatedProfile);
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  };

  const verifyPin = async (pin: string): Promise<boolean> => {
    try {
      const storedPinHash = await AsyncStorage.getItem(STORAGE_KEYS.PIN_HASH);
      return storedPinHash === hashPin(pin);
    } catch (error) {
      console.error('Failed to verify PIN:', error);
      return false;
    }
  };

  const changePIN = async (oldPin: string, newPin: string): Promise<boolean> => {
    try {
      const isOldPinValid = await verifyPin(oldPin);
      if (!isOldPinValid) return false;
      
      await AsyncStorage.setItem(STORAGE_KEYS.PIN_HASH, hashPin(newPin));
      return true;
    } catch (error) {
      console.error('Failed to change PIN:', error);
      return false;
    }
  };

  const enableBiometrics = async (): Promise<boolean> => {
    try {
      // Mock implementation for now
      await AsyncStorage.setItem(STORAGE_KEYS.BIOMETRICS_ENABLED, 'true');
      setIsBiometricsEnabled(true);
      return true;
    } catch (error) {
      console.error('Failed to enable biometrics:', error);
      return false;
    }
  };

  const disableBiometrics = async () => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.BIOMETRICS_ENABLED, 'false');
      setIsBiometricsEnabled(false);
    } catch (error) {
      console.error('Failed to disable biometrics:', error);
    }
  };

  const authenticateWithBiometrics = async (): Promise<boolean> => {
    try {
      if (!isBiometricsEnabled) return false;

      // Mock implementation for now
      setIsWalletUnlocked(true);
      return true;
    } catch (error) {
      console.error('Biometric authentication failed:', error);
      return false;
    }
  };

  const value: WalletAuthContextType = {
    // State
    isWalletSetup,
    isWalletUnlocked,
    walletData,
    userProfile,
    isLoading,
    isInitializing,
    isBiometricsEnabled,
    
    // Actions
    createWallet,
    importWallet,
    unlockWallet,
    lockWallet,
    updateProfile,
    verifyPin,
    changePIN,
    enableBiometrics,
    disableBiometrics,
    authenticateWithBiometrics,
  };

  return (
    <WalletAuthContext.Provider value={value}>
      {children}
    </WalletAuthContext.Provider>
  );
}

export function useWalletAuth() {
  const context = useContext(WalletAuthContext);
  if (context === undefined) {
    throw new Error('useWalletAuth must be used within a WalletAuthProvider');
  }
  return context;
}
