import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Vibration,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../context/ThemeContext';
import { useWalletAuth } from '../../context/WalletAuthContext';
import { Ionicons } from '@expo/vector-icons';
// import * as LocalAuthentication from 'expo-local-authentication';

export default function UnlockWalletScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { unlockWallet } = useWalletAuth();
  const [pin, setPin] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [lockoutTime, setLockoutTime] = useState(0);
  const [biometricsAvailable, setBiometricsAvailable] = useState(false);

  useEffect(() => {
    checkBiometrics();
    // Auto-prompt biometrics if available
    setTimeout(() => {
      if (biometricsAvailable) {
        handleBiometricAuth();
      }
    }, 500);
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isLocked && lockoutTime > 0) {
      interval = setInterval(() => {
        setLockoutTime(prev => {
          if (prev <= 1) {
            setIsLocked(false);
            setAttempts(0);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isLocked, lockoutTime]);

  const checkBiometrics = async () => {
    // Mock implementation for now
    setBiometricsAvailable(true);
  };

  const handleBiometricAuth = async () => {
    try {
      // Mock implementation for now
      handleSuccessfulUnlock();
    } catch (error) {
      console.log('Biometric auth error:', error);
    }
  };

  const handleNumberPress = (number: string) => {
    if (isLocked) return;
    
    if (pin.length < 6) {
      const newPin = pin + number;
      setPin(newPin);
      
      if (newPin.length === 6) {
        validatePin(newPin);
      }
    }
  };

  const handleBackspace = () => {
    if (isLocked) return;
    setPin(pin.slice(0, -1));
  };

  const validatePin = async (enteredPin: string) => {
    try {
      const isValid = await unlockWallet(enteredPin);

      if (isValid) {
        handleSuccessfulUnlock();
      } else {
        handleFailedAttempt();
      }
    } catch (error) {
      console.error('PIN validation error:', error);
      handleFailedAttempt();
    }
  };

  const handleSuccessfulUnlock = () => {
    setPin('');
    setAttempts(0);
    // Navigation is handled automatically by App.tsx when isWalletUnlocked becomes true
  };

  const handleFailedAttempt = () => {
    Vibration.vibrate(500);
    setPin('');
    const newAttempts = attempts + 1;
    setAttempts(newAttempts);

    if (newAttempts >= 5) {
      // Lock for 5 minutes after 5 failed attempts
      setIsLocked(true);
      setLockoutTime(300); // 5 minutes
      Alert.alert(
        'Wallet Locked',
        'Too many failed attempts. Your wallet is locked for 5 minutes.',
        [{ text: 'OK' }]
      );
    } else {
      Alert.alert(
        'Incorrect PIN',
        `Incorrect PIN. ${5 - newAttempts} attempts remaining.`,
        [{ text: 'OK' }]
      );
    }
  };

  const handleForgotPin = () => {
    Alert.alert(
      'Reset Wallet',
      'To reset your PIN, you\'ll need to restore your wallet using your recovery phrase. This will erase the current wallet data.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Restore Wallet', 
          style: 'destructive',
          onPress: () => navigation.navigate('ImportWallet')
        }
      ]
    );
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const styles = createStyles(theme);

  const renderPinDots = () => {
    return (
      <View style={styles.pinDotsContainer}>
        {[...Array(6)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              index < pin.length && styles.filledPinDot
            ]}
          />
        ))}
      </View>
    );
  };

  const renderNumberPad = () => {
    const numbers = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['', '0', 'backspace']
    ];

    return (
      <View style={styles.numberPad}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numberRow}>
            {row.map((item, itemIndex) => {
              if (item === '') {
                return <View key={itemIndex} style={styles.numberButton} />;
              }
              
              if (item === 'backspace') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[styles.numberButton, isLocked && styles.disabledButton]}
                    onPress={handleBackspace}
                    disabled={isLocked}
                  >
                    <Ionicons name="backspace" size={24} color={theme.colors.text} />
                  </TouchableOpacity>
                );
              }

              return (
                <TouchableOpacity
                  key={itemIndex}
                  style={[styles.numberButton, isLocked && styles.disabledButton]}
                  onPress={() => handleNumberPress(item)}
                  disabled={isLocked}
                >
                  <Text style={styles.numberText}>{item}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <View style={styles.logoWrapper}>
            <Ionicons name="wallet" size={60} color={theme.colors.primary} />
          </View>
          <Text style={styles.appName}>KryptoPesa</Text>
          <Text style={styles.welcomeBack}>Welcome back!</Text>
        </View>

        <View style={styles.unlockContainer}>
          <Text style={styles.unlockTitle}>
            {isLocked ? 'Wallet Locked' : 'Enter your PIN'}
          </Text>
          
          {isLocked ? (
            <Text style={styles.lockoutText}>
              Try again in {formatTime(lockoutTime)}
            </Text>
          ) : (
            <Text style={styles.unlockSubtitle}>
              Enter your 6-digit PIN to unlock your wallet
            </Text>
          )}

          {attempts > 0 && !isLocked && (
            <Text style={styles.attemptsText}>
              {5 - attempts} attempts remaining
            </Text>
          )}
        </View>

        {renderPinDots()}
        {renderNumberPad()}

        <View style={styles.actionsContainer}>
          {biometricsAvailable && !isLocked && (
            <TouchableOpacity
              style={styles.biometricButton}
              onPress={handleBiometricAuth}
            >
              <Ionicons name="finger-print" size={24} color={theme.colors.primary} />
              <Text style={styles.biometricText}>Use Biometrics</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.forgotButton}
            onPress={handleForgotPin}
          >
            <Text style={styles.forgotText}>Forgot PIN? Restore wallet</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 60,
  },
  logoWrapper: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: theme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  welcomeBack: {
    fontSize: 18,
    color: theme.colors.textSecondary,
  },
  unlockContainer: {
    alignItems: 'center',
    marginVertical: 32,
  },
  unlockTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  unlockSubtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  lockoutText: {
    fontSize: 18,
    color: theme.colors.error,
    fontWeight: '600',
  },
  attemptsText: {
    fontSize: 14,
    color: theme.colors.warning,
    marginTop: 8,
  },
  pinDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 48,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: theme.colors.border,
    marginHorizontal: 8,
  },
  filledPinDot: {
    backgroundColor: theme.colors.primary,
  },
  numberPad: {
    alignItems: 'center',
  },
  numberRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  numberButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.cardBackground,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  disabledButton: {
    backgroundColor: theme.colors.disabled,
    opacity: 0.5,
  },
  numberText: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.colors.text,
  },
  actionsContainer: {
    alignItems: 'center',
    marginTop: 32,
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 16,
  },
  biometricText: {
    fontSize: 16,
    color: theme.colors.primary,
    marginLeft: 8,
    fontWeight: '500',
  },
  forgotButton: {
    padding: 12,
  },
  forgotText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textDecorationLine: 'underline',
  },
});
