/**
 * Migration Script: Traditional Auth to Wallet-Based Auth
 * 
 * This script migrates the existing User collection to the new WalletUser model
 * WARNING: This is a destructive migration. Backup your database first!
 */

const mongoose = require('mongoose');
const { ethers } = require('ethers');

// Import both old and new models
const User = require('../src/models/User');
const WalletUser = require('../src/models/WalletUser');

async function migrateToWalletAuth() {
  try {
    console.log('🚀 Starting migration to wallet-based authentication...');
    
    // Step 1: Get all existing users
    const existingUsers = await User.find({}).lean();
    console.log(`📊 Found ${existingUsers.length} existing users to migrate`);
    
    if (existingUsers.length === 0) {
      console.log('✅ No users to migrate. Creating fresh wallet-based system.');
      return;
    }
    
    // Step 2: Create migration plan
    console.log('📋 Creating migration plan...');
    const migrationPlan = [];
    
    for (const user of existingUsers) {
      // Generate a wallet address for existing users
      // In production, you might want to ask users to connect their wallets
      const wallet = ethers.Wallet.createRandom();
      
      const walletUserData = {
        walletAddress: wallet.address,
        publicKey: wallet.publicKey,
        profile: {
          displayName: user.profile?.firstName && user.profile?.lastName 
            ? `${user.profile.firstName} ${user.profile.lastName}`
            : user.username,
          avatar: user.profile?.avatar || null,
          bio: user.profile?.bio || null,
          location: {
            country: user.profile?.location?.country || null,
            city: user.profile?.location?.city || null
          }
        },
        reputation: {
          score: user.reputation?.score || 0,
          totalTrades: user.reputation?.totalTrades || 0,
          completedTrades: user.reputation?.completedTrades || 0,
          cancelledTrades: user.reputation?.cancelledTrades || 0,
          disputedTrades: user.reputation?.disputedTrades || 0,
          averageResponseTime: user.reputation?.averageResponseTime || null,
          lastTradeAt: user.reputation?.lastTradeAt || null
        },
        preferences: {
          language: user.preferences?.language || 'en',
          currency: user.preferences?.currency || 'KES',
          notifications: {
            push: user.preferences?.notifications?.push !== false,
            tradeUpdates: user.preferences?.notifications?.tradeUpdates !== false,
            priceAlerts: user.preferences?.notifications?.priceAlerts || false
          },
          trading: {
            autoReply: user.preferences?.trading?.autoReply || false,
            showOnlineStatus: user.preferences?.trading?.showOnlineStatus !== false,
            allowDirectMessages: user.preferences?.trading?.allowDirectMessages !== false
          }
        },
        status: user.status || 'active',
        isLocked: user.isLocked || false,
        lockReason: user.lockReason || null,
        lockExpiresAt: user.lockExpiresAt || null,
        lastActive: user.lastActive || new Date(),
        verification: {
          level: user.verification?.identity?.verified ? 1 : 0,
          status: user.verification?.identity?.verified ? 'approved' : 'not_started',
          documents: [],
          tradingLimits: {
            daily: 1000,
            monthly: 10000
          }
        },
        // Store original user ID for reference during migration
        metadata: {
          originalUserId: user._id.toString(),
          originalUsername: user.username,
          originalEmail: user.email,
          originalPhone: user.phone,
          migrationDate: new Date(),
          // Store the generated wallet info for admin reference
          generatedWallet: {
            address: wallet.address,
            // DON'T store private key in production!
            // This is just for migration reference
            mnemonic: wallet.mnemonic?.phrase
          }
        }
      };
      
      migrationPlan.push({
        originalUser: user,
        walletUserData,
        walletInfo: {
          address: wallet.address,
          mnemonic: wallet.mnemonic?.phrase
        }
      });
    }
    
    // Step 3: Create backup collection
    console.log('💾 Creating backup of original users...');
    const backupCollectionName = `users_backup_${Date.now()}`;
    await mongoose.connection.db.collection(backupCollectionName).insertMany(existingUsers);
    console.log(`✅ Backup created: ${backupCollectionName}`);
    
    // Step 4: Create new wallet users
    console.log('👥 Creating new wallet users...');
    const createdUsers = [];
    
    for (const plan of migrationPlan) {
      try {
        const walletUser = new WalletUser(plan.walletUserData);
        const savedUser = await walletUser.save();
        createdUsers.push({
          walletUser: savedUser,
          walletInfo: plan.walletInfo
        });
        console.log(`✅ Created wallet user: ${savedUser.walletAddress}`);
      } catch (error) {
        console.error(`❌ Failed to create wallet user for ${plan.originalUser.username}:`, error);
      }
    }
    
    // Step 5: Update related collections (trades, offers, etc.)
    console.log('🔄 Updating related collections...');
    
    // Create mapping from old user IDs to new wallet addresses
    const userIdToWalletMap = new Map();
    migrationPlan.forEach((plan, index) => {
      if (createdUsers[index]) {
        userIdToWalletMap.set(
          plan.originalUser._id.toString(),
          createdUsers[index].walletUser._id
        );
      }
    });
    
    // Update Trade collection
    const Trade = require('../src/models/Trade');
    for (const [oldUserId, newUserId] of userIdToWalletMap) {
      await Trade.updateMany(
        { $or: [{ seller: oldUserId }, { buyer: oldUserId }] },
        { 
          $set: {
            seller: { $cond: [{ $eq: ['$seller', oldUserId] }, newUserId, '$seller'] },
            buyer: { $cond: [{ $eq: ['$buyer', oldUserId] }, newUserId, '$buyer'] }
          }
        }
      );
    }
    
    // Update Offer collection
    const Offer = require('../src/models/Offer');
    for (const [oldUserId, newUserId] of userIdToWalletMap) {
      await Offer.updateMany(
        { user: oldUserId },
        { $set: { user: newUserId } }
      );
    }
    
    // Update Chat collection
    const Chat = require('../src/models/Chat');
    for (const [oldUserId, newUserId] of userIdToWalletMap) {
      await Chat.updateMany(
        { participants: oldUserId },
        { $set: { 'participants.$': newUserId } }
      );
    }
    
    // Step 6: Generate migration report
    console.log('📊 Generating migration report...');
    const report = {
      migrationDate: new Date(),
      totalUsersProcessed: existingUsers.length,
      successfulMigrations: createdUsers.length,
      failedMigrations: existingUsers.length - createdUsers.length,
      backupCollection: backupCollectionName,
      walletInfo: createdUsers.map(user => ({
        walletAddress: user.walletUser.walletAddress,
        displayName: user.walletUser.displayName,
        originalUsername: user.walletUser.metadata.originalUsername,
        // Include mnemonic for admin to help users recover
        mnemonic: user.walletInfo.mnemonic
      }))
    };
    
    // Save report
    const reportPath = `./migration_report_${Date.now()}.json`;
    require('fs').writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('✅ Migration completed successfully!');
    console.log(`📊 Migration report saved to: ${reportPath}`);
    console.log(`💾 Original users backed up to: ${backupCollectionName}`);
    console.log(`👥 Successfully migrated: ${createdUsers.length}/${existingUsers.length} users`);
    
    // Step 7: Optional - Remove old User collection (commented out for safety)
    // console.log('🗑️  Removing old User collection...');
    // await mongoose.connection.db.collection('users').drop();
    // console.log('✅ Old User collection removed');
    
    console.log('\n🎉 Migration to wallet-based authentication completed!');
    console.log('\n⚠️  IMPORTANT NOTES:');
    console.log('1. Users will need to be informed about the new wallet-based system');
    console.log('2. Provide users with their generated wallet addresses and mnemonics');
    console.log('3. Users should set up their PINs when they first access the app');
    console.log('4. Consider implementing a user notification system for this change');
    console.log('5. The old User collection is preserved as backup');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  const mongoose = require('mongoose');
  
  // Connect to database
  mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa', {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
  
  mongoose.connection.once('open', async () => {
    console.log('📡 Connected to MongoDB');
    
    try {
      await migrateToWalletAuth();
      console.log('🎉 Migration script completed successfully');
    } catch (error) {
      console.error('💥 Migration script failed:', error);
    } finally {
      await mongoose.connection.close();
      console.log('📡 Database connection closed');
      process.exit(0);
    }
  });
  
  mongoose.connection.on('error', (error) => {
    console.error('💥 Database connection error:', error);
    process.exit(1);
  });
}

module.exports = { migrateToWalletAuth };
