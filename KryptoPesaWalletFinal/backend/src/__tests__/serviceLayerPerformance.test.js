/**
 * Service Layer Performance Tests
 * Comprehensive tests for service layer performance optimizations
 */

const { ServiceLayerPerformanceService } = require('../services/serviceLayerPerformance');

describe('Service Layer Performance Optimization', () => {
  let performanceService;

  beforeEach(() => {
    performanceService = new ServiceLayerPerformanceService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should initialize performance service successfully', () => {
      expect(performanceService).toBeDefined();
      expect(performanceService.distributedCache).toBeDefined();
      expect(performanceService.performanceMetrics).toBeDefined();
    });

    it('should have performance metrics tracking', () => {
      const metrics = performanceService.getPerformanceMetrics();
      expect(metrics).toBeDefined();
      expect(metrics.cacheHits).toBe(0);
      expect(metrics.cacheMisses).toBe(0);
      expect(metrics.queryCount).toBe(0);
    });

    it('should support cache operations', async () => {
      const key = 'test:key';
      const value = { data: 'test' };

      await performanceService.setCache(key, value, 300);
      expect(performanceService.distributedCache.has(key)).toBe(true);

      const result = await performanceService.getCache(key);
      expect(result).toEqual(value);
    });
  });
});