import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import {
  userService,
  tradingService,
  EAST_AFRICAN_CONFIG,
  UserProfile,
  eastAfricanHelpers
} from '../../services';

// Mock trader data
const mockTrader = {
  id: '2',
  name: 'Alice Trader',
  username: '@alicetrader',
  avatar: null,
  rating: 4.8,
  totalTrades: 42,
  completionRate: 98.5,
  avgResponseTime: '5 minutes',
  memberSince: '2022-03-15',
  lastSeen: '2 hours ago',
  verified: true,
  badges: ['Verified', 'Fast Trader', 'Reliable'],
  bio: 'Experienced crypto trader with focus on BTC and ETH. Quick responses and reliable transactions.',
  preferredPaymentMethods: ['Bank Transfer', 'PayPal', 'Zelle'],
  tradingStats: {
    totalVolume: '15.5 BTC',
    avgTradeSize: '0.37 BTC',
    disputes: 0,
    positiveReviews: 41,
    neutralReviews: 1,
    negativeReviews: 0,
  },
};

const mockRecentTrades = [
  {
    id: '1',
    type: 'sell',
    amount: '0.025 BTC',
    price: '50,200 USD',
    date: '2023-07-05',
    status: 'completed',
  },
  {
    id: '2',
    type: 'buy',
    amount: '0.15 BTC',
    price: '49,800 USD',
    date: '2023-07-03',
    status: 'completed',
  },
  {
    id: '3',
    type: 'sell',
    amount: '0.08 BTC',
    price: '50,500 USD',
    date: '2023-07-01',
    status: 'completed',
  },
];

const mockReviews = [
  {
    id: '1',
    reviewer: 'John D.',
    rating: 5,
    comment: 'Excellent trader! Fast payment and great communication.',
    date: '2023-07-04',
    tradeAmount: '0.025 BTC',
  },
  {
    id: '2',
    reviewer: 'Sarah M.',
    rating: 5,
    comment: 'Very reliable and professional. Highly recommended!',
    date: '2023-07-02',
    tradeAmount: '0.15 BTC',
  },
  {
    id: '3',
    reviewer: 'Mike R.',
    rating: 4,
    comment: 'Good trader, smooth transaction.',
    date: '2023-06-30',
    tradeAmount: '0.08 BTC',
  },
];

export default function TraderProfileScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { traderId } = route.params || {};

  // State management
  const [traderProfile, setTraderProfile] = useState<UserProfile | null>(null);
  const [traderStats, setTraderStats] = useState(null);
  const [recentTrades, setRecentTrades] = useState([]);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [activeTab, setActiveTab] = useState('overview');

  // Load trader data
  const loadTraderData = async (showLoading = true) => {
    if (!traderId) {
      setError('Trader ID not provided');
      setLoading(false);
      return;
    }

    try {
      if (showLoading) setLoading(true);
      setError(null);

      const [profile, stats, trades, traderReviews] = await Promise.all([
        userService.getTraderProfile(traderId),
        tradingService.getTraderStats(traderId),
        tradingService.getTraderRecentTrades(traderId),
        tradingService.getTraderReviews(traderId),
      ]);

      setTraderProfile(profile);
      setTraderStats(stats);
      setRecentTrades(trades);
      setReviews(traderReviews);
    } catch (err) {
      console.error('Failed to load trader data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load trader profile');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadTraderData();
  }, [traderId]);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadTraderData(false);
    setRefreshing(false);
  };

  const renderStarRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Ionicons key={i} name="star" size={16} color={theme.colors.warning} />
      );
    }
    
    if (hasHalfStar) {
      stars.push(
        <Ionicons key="half" name="star-half" size={16} color={theme.colors.warning} />
      );
    }
    
    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <Ionicons key={`empty-${i}`} name="star-outline" size={16} color={theme.colors.textSecondary} />
      );
    }
    
    return stars;
  };

  const renderReviewItem = ({ item }) => (
    <View style={styles.reviewItem}>
      <View style={styles.reviewHeader}>
        <View style={styles.reviewerInfo}>
          <Text style={styles.reviewerName}>{item.reviewer}</Text>
          <View style={styles.reviewRating}>
            {renderStarRating(item.rating)}
          </View>
        </View>
        <Text style={styles.reviewDate}>{item.date}</Text>
      </View>
      <Text style={styles.reviewComment}>{item.comment}</Text>
      <Text style={styles.reviewTradeAmount}>Trade: {item.tradeAmount}</Text>
    </View>
  );

  const renderTradeItem = ({ item }) => (
    <View style={styles.tradeItem}>
      <View style={styles.tradeInfo}>
        <Text style={[styles.tradeType, { color: item.type === 'buy' ? theme.colors.success : theme.colors.error }]}>
          {item.type.toUpperCase()}
        </Text>
        <Text style={styles.tradeAmount}>{item.amount}</Text>
        <Text style={styles.tradePrice}>{item.price}</Text>
      </View>
      <View style={styles.tradeDetails}>
        <Text style={styles.tradeDate}>{item.date}</Text>
        <Text style={[styles.tradeStatus, { color: theme.colors.success }]}>
          {item.status.toUpperCase()}
        </Text>
      </View>
    </View>
  );

  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      {/* Trading Stats */}
      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>Trading Statistics</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{mockTrader.tradingStats.totalVolume}</Text>
            <Text style={styles.statLabel}>Total Volume</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{mockTrader.tradingStats.avgTradeSize}</Text>
            <Text style={styles.statLabel}>Avg Trade Size</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{mockTrader.tradingStats.disputes}</Text>
            <Text style={styles.statLabel}>Disputes</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{mockTrader.completionRate}%</Text>
            <Text style={styles.statLabel}>Completion Rate</Text>
          </View>
        </View>
      </View>

      {/* Payment Methods */}
      <View style={styles.paymentMethodsContainer}>
        <Text style={styles.sectionTitle}>Preferred Payment Methods</Text>
        <View style={styles.paymentMethods}>
          {mockTrader.preferredPaymentMethods.map((method, index) => (
            <View key={index} style={styles.paymentMethodChip}>
              <Text style={styles.paymentMethodText}>{method}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Bio */}
      {mockTrader.bio && (
        <View style={styles.bioContainer}>
          <Text style={styles.sectionTitle}>About</Text>
          <Text style={styles.bioText}>{mockTrader.bio}</Text>
        </View>
      )}
    </View>
  );

  const renderReviewsTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.reviewsHeader}>
        <Text style={styles.sectionTitle}>Reviews ({mockReviews.length})</Text>
        <View style={styles.reviewsSummary}>
          <Text style={styles.reviewsPositive}>{mockTrader.tradingStats.positiveReviews} Positive</Text>
          <Text style={styles.reviewsNeutral}>{mockTrader.tradingStats.neutralReviews} Neutral</Text>
          <Text style={styles.reviewsNegative}>{mockTrader.tradingStats.negativeReviews} Negative</Text>
        </View>
      </View>
      <FlatList
        data={mockReviews}
        renderItem={renderReviewItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );

  const renderTradesTab = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Recent Trades</Text>
      <FlatList
        data={mockRecentTrades}
        renderItem={renderTradeItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );

  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Trader Profile</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading trader profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !traderProfile) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Trader Profile</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load trader</Text>
          <Text style={styles.errorSubText}>{error || 'Trader not found'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadTraderData()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>KryptoPesa Trader</Text>
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-vertical" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            {mockTrader.avatar ? (
              <Image source={{ uri: mockTrader.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Ionicons name="person" size={40} color={theme.colors.textTertiary} />
              </View>
            )}
            {mockTrader.verified && (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark-circle" size={20} color={theme.colors.success} />
              </View>
            )}
          </View>
          
          <View style={styles.profileInfo}>
            <Text style={styles.traderName}>{mockTrader.name}</Text>
            <Text style={styles.traderUsername}>{mockTrader.username}</Text>
            
            <View style={styles.ratingContainer}>
              <View style={styles.stars}>
                {renderStarRating(mockTrader.rating)}
              </View>
              <Text style={styles.ratingText}>{mockTrader.rating}</Text>
              <Text style={styles.tradesCount}>({mockTrader.totalTrades} trades)</Text>
            </View>
            
            <View style={styles.badges}>
              {mockTrader.badges.map((badge, index) => (
                <View key={index} style={styles.badge}>
                  <Text style={styles.badgeText}>{badge}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Quick Stats */}
        <View style={styles.quickStats}>
          <View style={styles.quickStatItem}>
            <Text style={styles.quickStatValue}>{mockTrader.avgResponseTime}</Text>
            <Text style={styles.quickStatLabel}>Avg Response</Text>
          </View>
          <View style={styles.quickStatItem}>
            <Text style={styles.quickStatValue}>{mockTrader.lastSeen}</Text>
            <Text style={styles.quickStatLabel}>Last Seen</Text>
          </View>
          <View style={styles.quickStatItem}>
            <Text style={styles.quickStatValue}>{mockTrader.memberSince}</Text>
            <Text style={styles.quickStatLabel}>Member Since</Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={styles.chatButton}
            onPress={() => navigation.navigate('IndividualChat', {
              traderId: mockTrader.id,
              traderName: mockTrader.name
            })}
          >
            <Ionicons name="chatbubble-outline" size={20} color="white" />
            <Text style={styles.chatButtonText}>Message</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.reportButton}>
            <Ionicons name="flag-outline" size={20} color={theme.colors.error} />
            <Text style={styles.reportButtonText}>Report</Text>
          </TouchableOpacity>
        </View>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <View style={styles.tabs}>
            <TouchableOpacity 
              style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
              onPress={() => setActiveTab('overview')}
            >
              <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText]}>
                Overview
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.tab, activeTab === 'reviews' && styles.activeTab]}
              onPress={() => setActiveTab('reviews')}
            >
              <Text style={[styles.tabText, activeTab === 'reviews' && styles.activeTabText]}>
                Reviews
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.tab, activeTab === 'trades' && styles.activeTab]}
              onPress={() => setActiveTab('trades')}
            >
              <Text style={[styles.tabText, activeTab === 'trades' && styles.activeTabText]}>
                Trades
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Tab Content */}
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'reviews' && renderReviewsTab()}
        {activeTab === 'trades' && renderTradesTab()}
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  moreButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  profileHeader: {
    backgroundColor: theme.colors.surface,
    padding: 20,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 2,
  },
  profileInfo: {
    alignItems: 'center',
  },
  traderName: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  traderUsername: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stars: {
    flexDirection: 'row',
    marginRight: 8,
  },
  ratingText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginRight: 4,
  },
  tradesCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  badges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  badge: {
    backgroundColor: theme.colors.primaryLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginHorizontal: 4,
    marginVertical: 2,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.primaryDark,
  },
  quickStats: {
    backgroundColor: theme.colors.surface,
    flexDirection: 'row',
    paddingVertical: 16,
    marginTop: 8,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: theme.colors.border,
  },
  quickStatItem: {
    flex: 1,
    alignItems: 'center',
  },
  quickStatValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  quickStatLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    gap: 12,
  },
  chatButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  chatButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  reportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.error,
    gap: 8,
  },
  reportButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.error,
  },
  tabsContainer: {
    backgroundColor: theme.colors.surface,
    marginTop: 8,
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: theme.colors.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  activeTabText: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  tabContent: {
    backgroundColor: theme.colors.surface,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: theme.colors.background,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  paymentMethodsContainer: {
    marginBottom: 24,
  },
  paymentMethods: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  paymentMethodChip: {
    backgroundColor: theme.colors.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  paymentMethodText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  bioContainer: {
    marginBottom: 24,
  },
  bioText: {
    fontSize: 16,
    lineHeight: 24,
    color: theme.colors.text,
  },
  reviewsHeader: {
    marginBottom: 16,
  },
  reviewsSummary: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 8,
  },
  reviewsPositive: {
    fontSize: 14,
    color: theme.colors.success,
    fontWeight: '500',
  },
  reviewsNeutral: {
    fontSize: 14,
    color: theme.colors.warning,
    fontWeight: '500',
  },
  reviewsNegative: {
    fontSize: 14,
    color: theme.colors.error,
    fontWeight: '500',
  },
  reviewItem: {
    backgroundColor: theme.colors.background,
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  reviewerName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  reviewRating: {
    flexDirection: 'row',
  },
  reviewDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  reviewComment: {
    fontSize: 14,
    lineHeight: 20,
    color: theme.colors.text,
    marginBottom: 8,
  },
  reviewTradeAmount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  tradeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  tradeInfo: {
    flex: 1,
  },
  tradeType: {
    fontSize: 12,
    fontWeight: '700',
    marginBottom: 4,
  },
  tradeAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  tradePrice: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  tradeDetails: {
    alignItems: 'flex-end',
  },
  tradeDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  tradeStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
});
