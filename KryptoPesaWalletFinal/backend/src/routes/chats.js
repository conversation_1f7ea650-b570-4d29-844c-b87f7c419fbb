const express = require('express');
const router = express.Router();
const { body, query, validationResult } = require('express-validator');
const { authenticateWallet } = require('../middleware/walletAuth');
const User = require('../models/User');
const logger = require('../utils/logger');

/**
 * GET /api/chats
 * Get user's chat list
 */
router.get('/', [
  authenticateWallet
], async (req, res, next) => {
  try {
    const userId = req.user._id;

    // Mock chat data for now - in production this would come from database
    const mockChats = [
      {
        id: 'chat_1',
        tradeId: 'trade_123',
        participants: [
          {
            walletAddress: req.user.walletAddress,
            displayName: req.user.username || 'You',
            isOnline: true
          },
          {
            walletAddress: '******************************************',
            displayName: 'Alice Trader',
            isOnline: false
          }
        ],
        lastMessage: {
          id: 'msg_1',
          senderId: '******************************************',
          content: 'Payment sent! Please confirm receipt.',
          timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
          type: 'text',
          isRead: false
        },
        unreadCount: 1,
        tradeStatus: 'payment_sent',
        updatedAt: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2 hours ago
      },
      {
        id: 'chat_2',
        tradeId: 'trade_456',
        participants: [
          {
            walletAddress: req.user.walletAddress,
            displayName: req.user.username || 'You',
            isOnline: true
          },
          {
            walletAddress: '0x8ba1f109551bD432803012645Hac136c22C177ec',
            displayName: 'Bob Crypto',
            isOnline: true
          }
        ],
        lastMessage: {
          id: 'msg_2',
          senderId: req.user.walletAddress,
          content: 'Thanks for the quick trade!',
          timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
          type: 'text',
          isRead: true
        },
        unreadCount: 0,
        tradeStatus: 'completed',
        updatedAt: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1 day ago
      }
    ];

    res.json({
      success: true,
      data: {
        chats: mockChats
      }
    });

  } catch (error) {
    logger.error('Failed to get chat list:', error);
    next(error);
  }
});

/**
 * GET /api/chats/:tradeId/messages
 * Get messages for a specific chat
 */
router.get('/:tradeId/messages', [
  authenticateWallet,
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 })
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { tradeId } = req.params;
    const { limit = 50, offset = 0 } = req.query;
    const userId = req.user._id;

    // Mock messages for now - in production this would come from database
    const mockMessages = [
      {
        id: 'msg_1',
        senderId: '******************************************',
        senderName: 'Alice Trader',
        content: 'Hi! I\'m interested in your BTC offer.',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        type: 'text',
        isRead: true
      },
      {
        id: 'msg_2',
        senderId: req.user.walletAddress,
        senderName: req.user.username || 'You',
        content: 'Great! Let\'s proceed with the trade.',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2 + 1000 * 60 * 5).toISOString(),
        type: 'text',
        isRead: true
      },
      {
        id: 'msg_3',
        senderId: '******************************************',
        senderName: 'Alice Trader',
        content: 'Payment sent! Please confirm receipt.',
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        type: 'text',
        isRead: false
      }
    ];

    // Apply pagination
    const paginatedMessages = mockMessages.slice(offset, offset + limit);

    res.json({
      success: true,
      data: {
        messages: paginatedMessages,
        pagination: {
          total: mockMessages.length,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: offset + limit < mockMessages.length
        }
      }
    });

  } catch (error) {
    logger.error('Failed to get chat messages:', error);
    next(error);
  }
});

/**
 * POST /api/chats/:tradeId/messages
 * Send a message in a chat
 */
router.post('/:tradeId/messages', [
  authenticateWallet,
  body('content').isLength({ min: 1, max: 1000 }).withMessage('Message content is required and must be less than 1000 characters'),
  body('type').optional().isIn(['text', 'image', 'file']).withMessage('Invalid message type')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { tradeId } = req.params;
    const { content, type = 'text' } = req.body;
    const userId = req.user._id;

    // In production, this would save the message to the database
    const newMessage = {
      id: `msg_${Date.now()}`,
      senderId: req.user.walletAddress,
      senderName: req.user.username || 'You',
      content,
      timestamp: new Date().toISOString(),
      type,
      isRead: false
    };

    logger.info(`Message sent in trade ${tradeId} by user ${userId}: ${content}`);

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        message: newMessage
      }
    });

  } catch (error) {
    logger.error('Failed to send message:', error);
    next(error);
  }
});

/**
 * PUT /api/chats/:tradeId/read
 * Mark all messages in a chat as read
 */
router.put('/:tradeId/read', [
  authenticateWallet
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const userId = req.user._id;

    // In production, this would update message read status in the database
    logger.info(`Marking messages as read in trade ${tradeId} for user ${userId}`);

    res.json({
      success: true,
      message: 'Messages marked as read'
    });

  } catch (error) {
    logger.error('Failed to mark messages as read:', error);
    next(error);
  }
});

/**
 * GET /api/chats/:tradeId/participants
 * Get chat participants
 */
router.get('/:tradeId/participants', [
  authenticateWallet
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const userId = req.user._id;

    // Mock participants for now
    const mockParticipants = [
      {
        walletAddress: req.user.walletAddress,
        displayName: req.user.username || 'You',
        isOnline: true,
        lastSeen: new Date().toISOString()
      },
      {
        walletAddress: '******************************************',
        displayName: 'Alice Trader',
        isOnline: false,
        lastSeen: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30 minutes ago
      }
    ];

    res.json({
      success: true,
      data: {
        participants: mockParticipants
      }
    });

  } catch (error) {
    logger.error('Failed to get chat participants:', error);
    next(error);
  }
});

module.exports = router;
