#!/usr/bin/env node

/**
 * Comprehensive Dependency Security Audit for KryptoPesa
 * Checks for vulnerabilities, outdated packages, and security best practices
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

class DependencySecurityAudit {
  constructor() {
    this.auditResults = {
      timestamp: new Date().toISOString(),
      projects: [],
      summary: {
        totalVulnerabilities: 0,
        criticalVulnerabilities: 0,
        highVulnerabilities: 0,
        moderateVulnerabilities: 0,
        lowVulnerabilities: 0,
        outdatedPackages: 0,
        unusedDependencies: 0
      },
      recommendations: []
    };
  }

  async runComprehensiveAudit() {
    console.log('🔒 Starting Comprehensive Dependency Security Audit');
    console.log('=' * 60);

    const projects = [
      { name: 'Backend', path: 'backend', type: 'node' },
      { name: 'Admin Dashboard', path: 'admin-dashboard', type: 'react' },
      { name: 'Mobile App', path: 'kryptopesa_mobile', type: 'flutter' },
      { name: 'Smart Contracts', path: 'smart-contracts', type: 'node' },
      { name: 'Root Project', path: '.', type: 'node' }
    ];

    for (const project of projects) {
      await this.auditProject(project);
    }

    await this.generateSecurityReport();
    return this.auditResults;
  }

  async auditProject(project) {
    console.log(`\n🔍 Auditing ${project.name} (${project.path})`);
    console.log('-'.repeat(40));

    const projectResult = {
      name: project.name,
      path: project.path,
      type: project.type,
      vulnerabilities: [],
      outdatedPackages: [],
      unusedDependencies: [],
      securityIssues: [],
      recommendations: []
    };

    try {
      // Check if package.json exists
      const packageJsonPath = path.join(project.path, 'package.json');
      await fs.access(packageJsonPath);

      // Run npm audit
      if (project.type === 'node' || project.type === 'react') {
        await this.runNpmAudit(project, projectResult);
        await this.checkOutdatedPackages(project, projectResult);
        await this.analyzePackageJson(project, projectResult);
        await this.checkSecurityBestPractices(project, projectResult);
      }

      // Flutter-specific checks
      if (project.type === 'flutter') {
        await this.auditFlutterProject(project, projectResult);
      }

    } catch (error) {
      if (error.code === 'ENOENT') {
        console.log(`  ⚠️  No package.json found in ${project.path}`);
        projectResult.recommendations.push('No package.json found - skipping audit');
      } else {
        console.error(`  ❌ Error auditing ${project.name}:`, error.message);
        projectResult.securityIssues.push({
          type: 'audit_error',
          message: error.message
        });
      }
    }

    this.auditResults.projects.push(projectResult);
  }

  async runNpmAudit(project, projectResult) {
    try {
      console.log(`  🔍 Running npm audit for ${project.name}...`);
      
      const auditOutput = execSync('npm audit --json', {
        cwd: project.path,
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      });

      const auditData = JSON.parse(auditOutput);
      
      if (auditData.vulnerabilities) {
        for (const [packageName, vulnData] of Object.entries(auditData.vulnerabilities)) {
          const vulnerability = {
            package: packageName,
            severity: vulnData.severity,
            title: vulnData.title,
            url: vulnData.url,
            range: vulnData.range,
            fixAvailable: vulnData.fixAvailable
          };
          
          projectResult.vulnerabilities.push(vulnerability);
          this.auditResults.summary.totalVulnerabilities++;
          
          switch (vulnData.severity) {
            case 'critical':
              this.auditResults.summary.criticalVulnerabilities++;
              break;
            case 'high':
              this.auditResults.summary.highVulnerabilities++;
              break;
            case 'moderate':
              this.auditResults.summary.moderateVulnerabilities++;
              break;
            case 'low':
              this.auditResults.summary.lowVulnerabilities++;
              break;
          }
        }
      }

      console.log(`    Found ${projectResult.vulnerabilities.length} vulnerabilities`);

    } catch (error) {
      // npm audit returns non-zero exit code when vulnerabilities are found
      if (error.stdout) {
        try {
          const auditData = JSON.parse(error.stdout);
          // Process audit data similar to above
          console.log(`    Found vulnerabilities in ${project.name}`);
        } catch (parseError) {
          console.log(`    ⚠️  Could not parse npm audit output for ${project.name}`);
        }
      }
    }
  }

  async checkOutdatedPackages(project, projectResult) {
    try {
      console.log(`  📅 Checking outdated packages for ${project.name}...`);
      
      const outdatedOutput = execSync('npm outdated --json', {
        cwd: project.path,
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      });

      const outdatedData = JSON.parse(outdatedOutput);
      
      for (const [packageName, packageData] of Object.entries(outdatedData)) {
        const outdatedPackage = {
          package: packageName,
          current: packageData.current,
          wanted: packageData.wanted,
          latest: packageData.latest,
          type: packageData.type
        };
        
        projectResult.outdatedPackages.push(outdatedPackage);
        this.auditResults.summary.outdatedPackages++;
      }

      console.log(`    Found ${projectResult.outdatedPackages.length} outdated packages`);

    } catch (error) {
      // npm outdated returns non-zero exit code when outdated packages are found
      if (error.stdout) {
        try {
          const outdatedData = JSON.parse(error.stdout);
          // Process outdated data
          console.log(`    Found outdated packages in ${project.name}`);
        } catch (parseError) {
          console.log(`    ⚠️  Could not parse npm outdated output for ${project.name}`);
        }
      }
    }
  }

  async analyzePackageJson(project, projectResult) {
    try {
      const packageJsonPath = path.join(project.path, 'package.json');
      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));

      // Check for security-related configurations
      const securityChecks = [
        {
          check: 'scripts.audit',
          message: 'No audit script defined',
          recommendation: 'Add "audit": "npm audit" to scripts'
        },
        {
          check: 'scripts.audit-fix',
          message: 'No audit-fix script defined',
          recommendation: 'Add "audit-fix": "npm audit fix" to scripts'
        }
      ];

      for (const securityCheck of securityChecks) {
        const keys = securityCheck.check.split('.');
        let current = packageJson;
        
        for (const key of keys) {
          if (current && current[key]) {
            current = current[key];
          } else {
            projectResult.securityIssues.push({
              type: 'missing_security_config',
              message: securityCheck.message,
              recommendation: securityCheck.recommendation
            });
            break;
          }
        }
      }

      // Check for known problematic packages
      const problematicPackages = [
        'lodash', // Should use lodash-es for tree shaking
        'moment', // Should use date-fns or dayjs
        'request', // Deprecated
        'node-uuid' // Should use uuid
      ];

      const allDependencies = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      for (const pkg of problematicPackages) {
        if (allDependencies[pkg]) {
          projectResult.securityIssues.push({
            type: 'problematic_package',
            package: pkg,
            message: `Using problematic package: ${pkg}`,
            recommendation: this.getPackageRecommendation(pkg)
          });
        }
      }

    } catch (error) {
      console.error(`    ❌ Error analyzing package.json for ${project.name}:`, error.message);
    }
  }

  async checkSecurityBestPractices(project, projectResult) {
    const securityFiles = [
      '.nvmrc',
      '.node-version',
      'package-lock.json',
      'yarn.lock'
    ];

    for (const file of securityFiles) {
      try {
        await fs.access(path.join(project.path, file));
        // File exists - good practice
      } catch (error) {
        if (file === 'package-lock.json' || file === 'yarn.lock') {
          projectResult.securityIssues.push({
            type: 'missing_lock_file',
            message: `Missing ${file}`,
            recommendation: `Commit ${file} to ensure consistent dependency versions`
          });
        }
      }
    }
  }

  async auditFlutterProject(project, projectResult) {
    try {
      console.log(`  🔍 Auditing Flutter project ${project.name}...`);
      
      // Check pubspec.yaml
      const pubspecPath = path.join(project.path, 'pubspec.yaml');
      await fs.access(pubspecPath);
      
      // Flutter doesn't have built-in security audit like npm
      // Check for common security issues
      projectResult.recommendations.push('Consider using flutter_security_checker for dependency auditing');
      
    } catch (error) {
      console.log(`    ⚠️  Could not audit Flutter project: ${error.message}`);
    }
  }

  getPackageRecommendation(packageName) {
    const recommendations = {
      'lodash': 'Use lodash-es for better tree shaking',
      'moment': 'Use date-fns or dayjs for smaller bundle size',
      'request': 'Use axios or node-fetch (request is deprecated)',
      'node-uuid': 'Use uuid package instead'
    };
    
    return recommendations[packageName] || 'Consider finding a more secure alternative';
  }

  async generateSecurityReport() {
    console.log('\n📋 Generating Security Audit Report');
    console.log('-'.repeat(40));

    // Generate recommendations based on findings
    if (this.auditResults.summary.criticalVulnerabilities > 0) {
      this.auditResults.recommendations.push(
        `URGENT: Fix ${this.auditResults.summary.criticalVulnerabilities} critical vulnerabilities immediately`
      );
    }

    if (this.auditResults.summary.highVulnerabilities > 0) {
      this.auditResults.recommendations.push(
        `HIGH PRIORITY: Address ${this.auditResults.summary.highVulnerabilities} high-severity vulnerabilities`
      );
    }

    if (this.auditResults.summary.outdatedPackages > 10) {
      this.auditResults.recommendations.push(
        'Consider updating outdated packages to latest versions'
      );
    }

    // Save detailed report
    const reportPath = path.join(process.cwd(), 'reports', `dependency-security-audit-${Date.now()}.json`);
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(this.auditResults, null, 2));

    console.log('\n' + '='.repeat(60));
    console.log('🔒 DEPENDENCY SECURITY AUDIT SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Vulnerabilities: ${this.auditResults.summary.totalVulnerabilities}`);
    console.log(`  Critical: ${this.auditResults.summary.criticalVulnerabilities}`);
    console.log(`  High: ${this.auditResults.summary.highVulnerabilities}`);
    console.log(`  Moderate: ${this.auditResults.summary.moderateVulnerabilities}`);
    console.log(`  Low: ${this.auditResults.summary.lowVulnerabilities}`);
    console.log(`Outdated Packages: ${this.auditResults.summary.outdatedPackages}`);
    console.log(`\nProjects Audited: ${this.auditResults.projects.length}`);
    
    if (this.auditResults.recommendations.length > 0) {
      console.log('\n🚨 Recommendations:');
      this.auditResults.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }
    
    console.log(`\nDetailed report saved: ${reportPath}`);
    console.log('='.repeat(60));

    return this.auditResults;
  }
}

// CLI execution
if (require.main === module) {
  const auditor = new DependencySecurityAudit();
  
  auditor.runComprehensiveAudit()
    .then(results => {
      const hasVulnerabilities = results.summary.totalVulnerabilities > 0;
      const hasCritical = results.summary.criticalVulnerabilities > 0;
      
      console.log('\n✅ Dependency security audit completed');
      
      if (hasCritical) {
        console.log('❌ CRITICAL vulnerabilities found - immediate action required');
        process.exit(1);
      } else if (hasVulnerabilities) {
        console.log('⚠️  Vulnerabilities found - review and fix recommended');
        process.exit(0);
      } else {
        console.log('✅ No vulnerabilities found');
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('\n❌ Dependency audit failed:', error.message);
      process.exit(1);
    });
}

module.exports = DependencySecurityAudit;
