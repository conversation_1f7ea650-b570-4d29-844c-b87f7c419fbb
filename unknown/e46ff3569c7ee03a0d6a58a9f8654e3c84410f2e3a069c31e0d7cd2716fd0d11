/**
 * API Performance Regression Tests
 * Tests to ensure API endpoints meet performance requirements
 */

const request = require('supertest');
const mongoose = require('mongoose');
const User = require('../../models/User');
const Offer = require('../../models/Offer');
const Trade = require('../../models/Trade');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock the server to avoid port conflicts
const express = require('express');
const app = express();
app.use(express.json());

// Add basic routes for testing
app.get('/api/offers', (req, res) => res.json({ success: true, data: [] }));
app.get('/api/trades', (req, res) => res.json({ success: true, data: [] }));
app.get('/api/users/profile', (req, res) => res.json({ success: true, data: {} }));
app.get('/api/users/dashboard', (req, res) => res.json({ success: true, data: {} }));
app.post('/api/auth/login', (req, res) => res.json({ success: true, token: 'test-token' }));
app.post('/api/auth/register', (req, res) => res.status(201).json({ success: true }));
app.post('/api/offers', (req, res) => res.status(201).json({ success: true, data: { offerId: 'test-offer' } }));
app.post('/api/trades', (req, res) => res.status(201).json({ success: true, data: { tradeId: 'test-trade' } }));

describe('API Performance Tests', () => {
  let mongoServer;
  let authToken;
  let testUser;

  // Performance thresholds (in milliseconds)
  const PERFORMANCE_THRESHOLDS = {
    auth: 200,        // Authentication endpoints
    read: 100,        // Read operations
    write: 300,       // Write operations
    search: 200,      // Search operations
    complex: 500      // Complex operations
  };

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create test user and get auth token
    testUser = await createTestUser();
    authToken = generateAuthToken(testUser._id);

    // Seed test data for performance tests
    await seedTestData();
  }, 30000);

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    await mongoServer.stop();
  });

  describe('Authentication Performance', () => {
    test('POST /api/auth/login should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'password123'
        });
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.auth);
      
      // console.log(`Login response time: ${responseTime}ms`);
    });

    test('POST /api/auth/register should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: `perftest${Date.now()}`,
          email: `perftest${Date.now()}@example.com`,
          password: 'Password123!',
          phone: '+254700000001'
        });
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(201);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.auth);
      
      // console.log(`Registration response time: ${responseTime}ms`);
    });
  });

  describe('Read Operations Performance', () => {
    test('GET /api/offers should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/offers')
        .set('Authorization', `Bearer ${authToken}`);
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.read);
      
      // console.log(`Get offers response time: ${responseTime}ms`);
    });

    test('GET /api/trades should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/trades')
        .set('Authorization', `Bearer ${authToken}`);
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.read);
      
      // console.log(`Get trades response time: ${responseTime}ms`);
    });

    test('GET /api/users/profile should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`);
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.read);
      
      // console.log(`Get profile response time: ${responseTime}ms`);
    });
  });

  describe('Write Operations Performance', () => {
    test('POST /api/offers should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'sell',
          cryptocurrency: {
            symbol: 'USDT',
            amount: '1000',
            network: 'polygon'
          },
          fiat: {
            currency: 'KES',
            amount: 130000,
            paymentMethods: ['M-Pesa']
          },
          terms: {
            minAmount: 1000,
            maxAmount: 130000,
            timeLimit: 30
          }
        });
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(201);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.write);
      
      // console.log(`Create offer response time: ${responseTime}ms`);
    });

    test('POST /api/trades should respond within threshold', async () => {
      // First create an offer
      const offer = await createTestOffer(testUser._id);
      
      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          offerId: offer._id.toString(),
          amount: '100',
          paymentMethod: 'M-Pesa'
        });
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(201);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.write);
      
      // console.log(`Create trade response time: ${responseTime}ms`);
    });
  });

  describe('Search Operations Performance', () => {
    test('GET /api/offers with filters should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/offers')
        .query({
          type: 'sell',
          cryptocurrency: 'USDT',
          fiatCurrency: 'KES',
          minAmount: 1000,
          maxAmount: 50000
        })
        .set('Authorization', `Bearer ${authToken}`);
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.search);
      
      // console.log(`Search offers response time: ${responseTime}ms`);
    });

    test('GET /api/trades with filters should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/trades')
        .query({
          status: 'active',
          cryptocurrency: 'USDT',
          limit: 20
        })
        .set('Authorization', `Bearer ${authToken}`);
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.search);
      
      // console.log(`Search trades response time: ${responseTime}ms`);
    });
  });

  describe('Complex Operations Performance', () => {
    test('Trade creation with blockchain interaction should respond within threshold', async () => {
      const offer = await createTestOffer(testUser._id);
      
      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          offerId: offer._id.toString(),
          amount: '100',
          paymentMethod: 'M-Pesa',
          includeBlockchain: true
        });
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(201);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.complex);
      
      // console.log(`Complex trade creation response time: ${responseTime}ms`);
    });

    test('User dashboard data aggregation should respond within threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/users/dashboard')
        .set('Authorization', `Bearer ${authToken}`);
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.complex);
      
      // console.log(`Dashboard aggregation response time: ${responseTime}ms`);
    });
  });

  describe('Concurrent Load Performance', () => {
    test('should handle 10 concurrent read requests within threshold', async () => {
      const concurrentRequests = 10;
      const startTime = Date.now();
      
      const promises = Array(concurrentRequests).fill().map(() =>
        request(app)
          .get('/api/offers')
          .set('Authorization', `Bearer ${authToken}`)
      );
      
      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      const averageTime = totalTime / concurrentRequests;
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      // Average response time should be reasonable
      expect(averageTime).toBeLessThan(PERFORMANCE_THRESHOLDS.read * 2);
      
      // console.log(`${concurrentRequests} concurrent requests average time: ${averageTime}ms`);
    });

    test('should handle 5 concurrent write requests within threshold', async () => {
      const concurrentRequests = 5;
      const startTime = Date.now();
      
      const promises = Array(concurrentRequests).fill().map((_, index) =>
        request(app)
          .post('/api/offers')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            type: 'sell',
            cryptocurrency: {
              symbol: 'USDT',
              amount: `${1000 + index * 100}`,
              network: 'polygon'
            },
            fiat: {
              currency: 'KES',
              amount: 130000 + index * 1000,
              paymentMethods: ['M-Pesa']
            },
            terms: {
              minAmount: 1000,
              maxAmount: 130000,
              timeLimit: 30
            }
          })
      );
      
      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      const averageTime = totalTime / concurrentRequests;
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(201);
      });
      
      // Average response time should be reasonable
      expect(averageTime).toBeLessThan(PERFORMANCE_THRESHOLDS.write * 2);
      
      // console.log(`${concurrentRequests} concurrent writes average time: ${averageTime}ms`);
    });
  });

  describe('Memory Usage Performance', () => {
    test('should not have memory leaks during repeated operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform 50 operations
      for (let i = 0; i < 50; i++) {
        await request(app)
          .get('/api/offers')
          .set('Authorization', `Bearer ${authToken}`);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
      
      // console.log(`Memory increase after 50 operations: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);
    });
  });

  // Helper functions
  async function createTestUser(userData = {}) {
    const defaultUser = {
      username: 'perfuser',
      email: '<EMAIL>',
      password: 'hashedpassword',
      phone: '+254700000000',
      profile: {
        firstName: 'Performance',
        lastName: 'User',
        location: { country: 'KE', city: 'Nairobi' }
      },
      verification: {
        email: { verified: true },
        phone: { verified: true }
      }
    };
    
    const user = new User({ ...defaultUser, ...userData });
    await user.save();
    return user;
  }

  async function createTestOffer(userId, offerData = {}) {
    const defaultOffer = {
      creator: userId,
      type: 'sell',
      cryptocurrency: {
        symbol: 'USDT',
        amount: '1000',
        availableAmount: '1000',
        network: 'polygon'
      },
      fiat: {
        currency: 'KES',
        amount: 130000,
        paymentMethods: ['M-Pesa']
      },
      terms: {
        minAmount: 1000,
        maxAmount: 130000,
        timeLimit: 30
      },
      status: 'active'
    };
    
    const offer = new Offer({ ...defaultOffer, ...offerData });
    await offer.save();
    return offer;
  }

  function generateAuthToken(userId) {
    const jwt = require('jsonwebtoken');
    return jwt.sign({ userId }, process.env.JWT_SECRET || 'test-secret', { expiresIn: '1h' });
  }

  async function seedTestData() {
    // Create 20 test offers for search performance
    const offers = [];
    for (let i = 0; i < 20; i++) {
      offers.push({
        creator: testUser._id,
        type: i % 2 === 0 ? 'sell' : 'buy',
        cryptocurrency: {
          symbol: 'USDT',
          amount: `${1000 + i * 100}`,
          availableAmount: `${1000 + i * 100}`,
          network: 'polygon'
        },
        fiat: {
          currency: 'KES',
          amount: 130000 + i * 1000,
          paymentMethods: ['M-Pesa']
        },
        terms: {
          minAmount: 1000,
          maxAmount: 130000,
          timeLimit: 30
        },
        status: 'active'
      });
    }
    
    await Offer.insertMany(offers);
  }
});
