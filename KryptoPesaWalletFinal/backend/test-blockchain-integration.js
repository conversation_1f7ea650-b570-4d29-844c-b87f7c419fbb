const { ethers } = require('ethers');
require('dotenv').config();

async function testBlockchainIntegration() {
  console.log('🧪 Testing Complete Blockchain Integration...\n');

  try {
    // Test 1: Key Manager Integration
    console.log('1️⃣ Testing Key Manager...');
    const keyManager = require('./src/services/keyManagement/keyManager');
    
    const testWalletId = '507f1f77bcf86cd799439011';
    const testPrivateKey = await keyManager.getPrivateKey(testWalletId, 'ethereum');
    console.log('✅ Key Manager working - Private key retrieved');
    console.log('   - Key length:', testPrivateKey.length, 'characters');
    console.log('   - Key format: 0x' + testPrivateKey.substring(0, 8) + '...');

    // Test 2: Ethereum Service Integration
    console.log('\n2️⃣ Testing Ethereum Service...');
    const ethereumService = require('./src/services/blockchain/ethereumService');
    
    // Test wallet creation
    const wallet = ethers.Wallet.createRandom();
    console.log('✅ Test wallet created');
    console.log('   - Address:', wallet.address);
    
    // Test provider connection
    const provider = ethereumService.getProvider('localhost');
    const network = await provider.getNetwork();
    console.log('✅ Provider connection working');
    console.log('   - Network:', network.name, 'Chain ID:', network.chainId);

    // Test 3: Smart Contract Integration
    console.log('\n3️⃣ Testing Smart Contract Integration...');
    const contractAddress = process.env.ESCROW_CONTRACT_ADDRESS;
    
    if (!contractAddress) {
      throw new Error('ESCROW_CONTRACT_ADDRESS not set');
    }
    
    const escrowContract = ethereumService.getEscrowContract('localhost');
    
    // Test contract read operations
    const nextTradeId = await escrowContract.nextTradeId();
    const feeCollector = await escrowContract.feeCollector();
    
    console.log('✅ Smart contract integration working');
    console.log('   - Contract Address:', contractAddress);
    console.log('   - Next Trade ID:', nextTradeId.toString());
    console.log('   - Fee Collector:', feeCollector);

    // Test 4: Transaction Service Integration
    console.log('\n4️⃣ Testing Transaction Service...');
    const transactionService = require('./src/services/transactionService');
    
    // Test transaction service initialization
    console.log('✅ Transaction service loaded');
    console.log('   - Service methods available:', Object.getOwnPropertyNames(Object.getPrototypeOf(transactionService)));

    // Test 5: Wallet Service Balance Fetching
    console.log('\n5️⃣ Testing Wallet Service...');
    const walletService = require('./src/services/walletService');
    
    console.log('✅ Wallet service loaded');
    console.log('   - Service methods available:', Object.getOwnPropertyNames(Object.getPrototypeOf(walletService)));

    // Test 6: Bitcoin Service Integration
    console.log('\n6️⃣ Testing Bitcoin Service...');
    const bitcoinService = require('./src/services/blockchain/bitcoinService');
    
    console.log('✅ Bitcoin service loaded');
    console.log('   - Service methods available:', Object.getOwnPropertyNames(Object.getPrototypeOf(bitcoinService)));

    // Test 7: Escrow Service Integration
    console.log('\n7️⃣ Testing Escrow Service...');
    const escrowService = require('./src/services/escrowService');
    
    console.log('✅ Escrow service loaded');
    console.log('   - Contract addresses:', escrowService.escrowContracts);

    console.log('\n🎉 All blockchain integration tests passed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Key Manager - Working');
    console.log('   ✅ Ethereum Service - Working');
    console.log('   ✅ Smart Contract - Working');
    console.log('   ✅ Transaction Service - Working');
    console.log('   ✅ Wallet Service - Working');
    console.log('   ✅ Bitcoin Service - Working');
    console.log('   ✅ Escrow Service - Working');
    console.log('\n🚀 Blockchain integration is ready for real transactions!');

    return true;

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    return false;
  }
}

// Run the test
testBlockchainIntegration()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test error:', error);
    process.exit(1);
  });
