import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  Modal,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

// Mock data for UI development
const mockOffers = [
  {
    id: '1',
    type: 'buy',
    cryptoCurrency: 'BTC',
    fiatCurrency: 'USD',
    price: 51250.75,
    minAmount: 0.001,
    maxAmount: 0.5,
    paymentMethods: ['Bank Transfer', 'Cash Deposit'],
    user: {
      name: 'Alice Trader',
      completedTrades: 42,
      rating: 4.8,
    },
  },
  {
    id: '2',
    type: 'sell',
    cryptoCurrency: 'ETH',
    fiatCurrency: 'EUR',
    price: 2850.25,
    minAmount: 0.1,
    maxAmount: 5,
    paymentMethods: ['PayPal', 'SEPA Transfer'],
    user: {
      name: 'Bob Crypto',
      completedTrades: 18,
      rating: 4.5,
    },
  },
  {
    id: '3',
    type: 'buy',
    cryptoCurrency: 'BTC',
    fiatCurrency: 'GBP',
    price: 49875.50,
    minAmount: 0.005,
    maxAmount: 1,
    paymentMethods: ['Bank Transfer', 'Revolut'],
    user: {
      name: 'Charlie Hodl',
      completedTrades: 67,
      rating: 4.9,
    },
  },
  {
    id: '4',
    type: 'sell',
    cryptoCurrency: 'ETH',
    fiatCurrency: 'USD',
    price: 2920.30,
    minAmount: 0.2,
    maxAmount: 10,
    paymentMethods: ['Zelle', 'Cash App', 'Venmo'],
    user: {
      name: 'Diana Block',
      completedTrades: 31,
      rating: 4.7,
    },
  },
];

const paymentMethodOptions = [
  'Bank Transfer', 'Cash Deposit', 'PayPal', 'SEPA Transfer', 
  'Revolut', 'Zelle', 'Cash App', 'Venmo', 'Western Union'
];

const cryptoCurrencyOptions = ['BTC', 'ETH', 'USDT', 'XRP', 'SOL'];
const fiatCurrencyOptions = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'];

export default function OffersScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  
  // Get initial type from route params if available
  const initialType = route.params?.type || 'buy';
  
  const [offerType, setOfferType] = useState(initialType);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  
  // Filter states
  const [selectedCrypto, setSelectedCrypto] = useState('');
  const [selectedFiat, setSelectedFiat] = useState('');
  const [selectedPaymentMethods, setSelectedPaymentMethods] = useState([]);
  const [minAmount, setMinAmount] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  
  // In a real app, we would fetch offers from Convex
  // const offers = useQuery(api.offers.getOffers, { type: offerType });
  
  // Filter offers based on current filters
  const filteredOffers = mockOffers.filter(offer => {
    // Filter by type
    if (offer.type !== offerType) return false;
    
    // Filter by search query (user name or payment method)
    if (searchQuery) {
      const matchesUser = offer.user.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesPayment = offer.paymentMethods.some(method => 
        method.toLowerCase().includes(searchQuery.toLowerCase())
      );
      if (!matchesUser && !matchesPayment) return false;
    }
    
    // Filter by crypto currency
    if (selectedCrypto && offer.cryptoCurrency !== selectedCrypto) return false;
    
    // Filter by fiat currency
    if (selectedFiat && offer.fiatCurrency !== selectedFiat) return false;
    
    // Filter by payment methods
    if (selectedPaymentMethods.length > 0) {
      const hasMatchingPaymentMethod = selectedPaymentMethods.some(method => 
        offer.paymentMethods.includes(method)
      );
      if (!hasMatchingPaymentMethod) return false;
    }
    
    // Filter by amount
    const minAmountValue = parseFloat(minAmount);
    if (!isNaN(minAmountValue) && offer.maxAmount < minAmountValue) return false;
    
    const maxAmountValue = parseFloat(maxAmount);
    if (!isNaN(maxAmountValue) && offer.minAmount > maxAmountValue) return false;
    
    return true;
  });
  
  // Check if we should show the create offer screen
  useEffect(() => {
    if (route.params?.action === 'create') {
      navigation.navigate('CreateOffer');
    }
  }, [route.params]);
  
  const togglePaymentMethod = (method) => {
    if (selectedPaymentMethods.includes(method)) {
      setSelectedPaymentMethods(selectedPaymentMethods.filter(m => m !== method));
    } else {
      setSelectedPaymentMethods([...selectedPaymentMethods, method]);
    }
  };
  
  const clearFilters = () => {
    setSelectedCrypto('');
    setSelectedFiat('');
    setSelectedPaymentMethods([]);
    setMinAmount('');
    setMaxAmount('');
  };
  
  const renderOfferItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.offerCard}
      onPress={() => navigation.navigate('OfferDetail', { offerId: item.id })}
    >
      <View style={styles.offerHeader}>
        <View style={styles.offerTypeContainer}>
          <Text style={[
            styles.offerTypeText,
            item.type === 'buy' ? styles.buyText : styles.sellText
          ]}>
            {item.type === 'buy' ? 'Buy' : 'Sell'}
          </Text>
        </View>
        <Text style={styles.offerPrice}>
          {item.price.toLocaleString()} {item.fiatCurrency}
        </Text>
      </View>
      
      <View style={styles.offerDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Currency:</Text>
          <View style={styles.cryptoContainer}>
            <Image 
              source={{ uri: `https://api.a0.dev/assets/image?text=${item.cryptoCurrency}&aspect=1:1&seed=${item.cryptoCurrency}` }}
              style={styles.cryptoIcon}
            />
            <Text style={styles.detailValue}>{item.cryptoCurrency}</Text>
          </View>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Limits:</Text>
          <Text style={styles.detailValue}>
            {item.minAmount} - {item.maxAmount} {item.cryptoCurrency}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment:</Text>
          <Text style={styles.detailValue} numberOfLines={1} ellipsizeMode="tail">
            {item.paymentMethods.join(', ')}
          </Text>
        </View>
      </View>
      
      <View style={styles.userSection}>
        <View style={styles.userInfo}>
          <Ionicons name="person-circle" size={24} color={theme.colors.textSecondary} />
          <Text style={styles.userName}>{item.user.name}</Text>
        </View>
        <View style={styles.userStats}>
          <Text style={styles.tradeCount}>{item.user.completedTrades} trades</Text>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color={theme.colors.warning} />
            <Text style={styles.ratingText}>{item.user.rating}</Text>
          </View>
        </View>
      </View>
      
      <TouchableOpacity 
        style={[
          styles.actionButton,
          item.type === 'buy' ? styles.buyButton : styles.sellButton
        ]}
        onPress={() => navigation.navigate('Trade', { offerId: item.id })}
      >
        <Text style={styles.actionButtonText}>
          {item.type === 'buy' ? 'Sell to Buyer' : 'Buy from Seller'}
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>P2P Offers</Text>
        <TouchableOpacity 
          style={styles.createButton}
          onPress={() => navigation.navigate('CreateOffer')}
        >
          <Ionicons name="add-circle" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.tabContainer}>
        <TouchableOpacity 
          style={[styles.tab, offerType === 'buy' && styles.activeTab]}
          onPress={() => setOfferType('buy')}
        >
          <Text style={[styles.tabText, offerType === 'buy' && styles.activeTabText]}>
            Buy Crypto
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, offerType === 'sell' && styles.activeTab]}
          onPress={() => setOfferType('sell')}
        >
          <Text style={[styles.tabText, offerType === 'sell' && styles.activeTabText]}>
            Sell Crypto
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by trader or payment method"
            placeholderTextColor={theme.colors.inputPlaceholder}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          ) : null}
        </View>
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => setFilterModalVisible(true)}
        >
          <Ionicons name="options" size={20} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
      
      {selectedCrypto || selectedFiat || selectedPaymentMethods.length > 0 || minAmount || maxAmount ? (
        <View style={styles.activeFiltersContainer}>
          <Text style={styles.activeFiltersTitle}>Active Filters:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersScrollView}>
            {selectedCrypto ? (
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>{selectedCrypto}</Text>
                <TouchableOpacity onPress={() => setSelectedCrypto('')}>
                  <Ionicons name="close-circle" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            ) : null}
            
            {selectedFiat ? (
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>{selectedFiat}</Text>
                <TouchableOpacity onPress={() => setSelectedFiat('')}>
                  <Ionicons name="close-circle" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            ) : null}
            
            {selectedPaymentMethods.map(method => (
              <View key={method} style={styles.filterTag}>
                <Text style={styles.filterTagText}>{method}</Text>
                <TouchableOpacity onPress={() => togglePaymentMethod(method)}>
                  <Ionicons name="close-circle" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            ))}
            
            {minAmount || maxAmount ? (
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>
                  {minAmount ? `Min: ${minAmount}` : ''}{minAmount && maxAmount ? ' - ' : ''}
                  {maxAmount ? `Max: ${maxAmount}` : ''}
                </Text>
                <TouchableOpacity onPress={() => { setMinAmount(''); setMaxAmount(''); }}>
                  <Ionicons name="close-circle" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            ) : null}
            
            <TouchableOpacity style={styles.clearFiltersButton} onPress={clearFilters}>
              <Text style={styles.clearFiltersText}>Clear All</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      ) : null}
      
      {filteredOffers.length > 0 ? (
        <FlatList
          data={filteredOffers}
          renderItem={renderOfferItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.offersList}
        />
      ) : (
        <View style={styles.emptyStateContainer}>
          <Ionicons name="search" size={48} color={theme.colors.border} />
          <Text style={styles.emptyStateText}>No offers found</Text>
          <Text style={styles.emptyStateSubText}>
            Try adjusting your filters or create your own offer
          </Text>
          <TouchableOpacity 
            style={styles.createOfferButton}
            onPress={() => navigation.navigate('CreateOffer')}
          >
            <Text style={styles.createOfferButtonText}>Create Offer</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {/* Filter Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={filterModalVisible}
        onRequestClose={() => setFilterModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Offers</Text>
              <TouchableOpacity onPress={() => setFilterModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalScrollView}>
              <Text style={styles.filterSectionTitle}>Cryptocurrency</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsScrollView}>
                {cryptoCurrencyOptions.map(crypto => (
                  <TouchableOpacity
                    key={crypto}
                    style={[
                      styles.optionButton,
                      selectedCrypto === crypto && styles.selectedOption
                    ]}
                    onPress={() => setSelectedCrypto(selectedCrypto === crypto ? '' : crypto)}
                  >
                    <Text style={[
                      styles.optionText,
                      selectedCrypto === crypto && styles.selectedOptionText
                    ]}>
                      {crypto}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              
              <Text style={styles.filterSectionTitle}>Fiat Currency</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsScrollView}>
                {fiatCurrencyOptions.map(fiat => (
                  <TouchableOpacity
                    key={fiat}
                    style={[
                      styles.optionButton,
                      selectedFiat === fiat && styles.selectedOption
                    ]}
                    onPress={() => setSelectedFiat(selectedFiat === fiat ? '' : fiat)}
                  >
                    <Text style={[
                      styles.optionText,
                      selectedFiat === fiat && styles.selectedOptionText
                    ]}>
                      {fiat}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              
              <Text style={styles.filterSectionTitle}>Amount Range</Text>
              <View style={styles.amountRangeContainer}>
                <View style={styles.amountInputContainer}>
                  <Text style={styles.amountLabel}>Min</Text>
                  <TextInput
                    style={styles.amountInput}
                    placeholder="0.001"
                    value={minAmount}
                    onChangeText={setMinAmount}
                    keyboardType="numeric"
                  />
                </View>
                <View style={styles.amountSeparator} />
                <View style={styles.amountInputContainer}>
                  <Text style={styles.amountLabel}>Max</Text>
                  <TextInput
                    style={styles.amountInput}
                    placeholder="No limit"
                    value={maxAmount}
                    onChangeText={setMaxAmount}
                    keyboardType="numeric"
                  />
                </View>
              </View>
              
              <Text style={styles.filterSectionTitle}>Payment Methods</Text>
              <View style={styles.paymentMethodsContainer}>
                {paymentMethodOptions.map(method => (
                  <TouchableOpacity
                    key={method}
                    style={styles.paymentMethodRow}
                    onPress={() => togglePaymentMethod(method)}
                  >
                    <Text style={styles.paymentMethodText}>{method}</Text>
                    <Switch
                      value={selectedPaymentMethods.includes(method)}
                      onValueChange={() => togglePaymentMethod(method)}
                      trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
                      thumbColor={selectedPaymentMethods.includes(method) ? theme.colors.primary : theme.colors.surface}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={styles.clearButton}
                onPress={clearFilters}
              >
                <Text style={styles.clearButtonText}>Clear All</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.applyButton}
                onPress={() => setFilterModalVisible(false)}
              >
                <Text style={styles.applyButtonText}>Apply Filters</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  createButton: {
    padding: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: theme.colors.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  activeTabText: {
    color: theme.colors.primary,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.inputText,
  },
  filterButton: {
    marginLeft: 12,
    padding: 12,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  activeFiltersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  activeFiltersTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  filtersScrollView: {
    flexDirection: 'row',
  },
  filterTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  filterTagText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: 4,
  },
  clearFiltersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.errorLight,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  clearFiltersText: {
    fontSize: 14,
    color: theme.colors.error,
    fontWeight: '500',
  },
  offersList: {
    padding: 16,
  },
  offerCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  offerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  offerTypeContainer: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  offerTypeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  buyText: {
    color: theme.colors.success,
  },
  sellText: {
    color: theme.colors.error,
  },
  offerPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  offerDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    maxWidth: '60%',
  },
  cryptoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cryptoIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 4,
  },
  userSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginLeft: 8,
  },
  userStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tradeCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
    marginLeft: 2,
  },
  actionButton: {
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  buyButton: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  sellButton: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  createOfferButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  createOfferButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalScrollView: {
    padding: 16,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
    marginTop: 8,
  },
  optionsScrollView: {
    marginBottom: 16,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginRight: 8,
  },
  selectedOption: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  optionText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  selectedOptionText: {
    color: 'white',
  },
  amountRangeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  amountInputContainer: {
    flex: 1,
  },
  amountLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  amountInput: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
  },
  amountSeparator: {
    width: 16,
  },
  paymentMethodsContainer: {
    marginBottom: 16,
  },
  paymentMethodRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  paymentMethodText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  clearButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    marginRight: 8,
  },
  clearButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  applyButton: {
    flex: 2,
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
});