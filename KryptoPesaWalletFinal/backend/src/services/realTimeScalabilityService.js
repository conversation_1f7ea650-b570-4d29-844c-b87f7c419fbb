const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');
const socketRedisAdapter = require('./socketRedisAdapter');

/**
 * Real-Time Features Scalability Service
 * Manages all real-time features for horizontal scaling across multiple server instances
 */
class RealTimeScalabilityService {
  constructor() {
    this.redisClient = null;
    this.isInitialized = false;
    this.subscriptions = new Map();
    this.eventHandlers = new Map();
    this.metrics = {
      totalEvents: 0,
      eventsPerSecond: 0,
      activeSubscriptions: 0,
      failedDeliveries: 0,
      averageLatency: 0,
      lastReset: new Date()
    };
    this.eventQueue = [];
    this.processingQueue = false;
    this.connectionPool = new Map(); // userId -> socketId mapping
    this.userPresence = new Map(); // userId -> presence data
    this.roomSubscriptions = new Map(); // roomId -> Set of userIds
  }

  /**
   * Initialize the real-time scalability service
   */
  async initialize() {
    try {
      this.redisClient = getRedisClient();
      if (!this.redisClient || !this.redisClient.isReady) {
        logger.warn('Redis not available for real-time scalability service');
        return false;
      }

      // Initialize event handlers
      this.initializeEventHandlers();

      // Subscribe to real-time channels
      await this.subscribeToChannels();

      // Start background processes
      this.startBackgroundProcesses();

      this.isInitialized = true;
      logger.info('Real-time scalability service initialized successfully');
      return true;
    } catch (error) {
      logger.error('Failed to initialize real-time scalability service:', error);
      return false;
    }
  }

  /**
   * Initialize event handlers for different real-time features
   */
  initializeEventHandlers() {
    // Trading events
    this.eventHandlers.set('trade_status_update', this.handleTradeStatusUpdate.bind(this));
    this.eventHandlers.set('trade_created', this.handleTradeCreated.bind(this));
    this.eventHandlers.set('trade_accepted', this.handleTradeAccepted.bind(this));
    this.eventHandlers.set('trade_completed', this.handleTradeCompleted.bind(this));
    this.eventHandlers.set('trade_disputed', this.handleTradeDisputed.bind(this));

    // Wallet events
    this.eventHandlers.set('balance_update', this.handleBalanceUpdate.bind(this));
    this.eventHandlers.set('transaction_confirmed', this.handleTransactionConfirmed.bind(this));
    this.eventHandlers.set('transaction_failed', this.handleTransactionFailed.bind(this));

    // Chat events
    this.eventHandlers.set('new_message', this.handleNewMessage.bind(this));
    this.eventHandlers.set('typing_indicator', this.handleTypingIndicator.bind(this));
    this.eventHandlers.set('message_read', this.handleMessageRead.bind(this));

    // System events
    this.eventHandlers.set('system_notification', this.handleSystemNotification.bind(this));
    this.eventHandlers.set('maintenance_mode', this.handleMaintenanceMode.bind(this));
    this.eventHandlers.set('price_update', this.handlePriceUpdate.bind(this));

    // Presence events
    this.eventHandlers.set('user_online', this.handleUserOnline.bind(this));
    this.eventHandlers.set('user_offline', this.handleUserOffline.bind(this));
    this.eventHandlers.set('user_activity', this.handleUserActivity.bind(this));

    // Admin events
    this.eventHandlers.set('admin_alert', this.handleAdminAlert.bind(this));
    this.eventHandlers.set('dispute_escalated', this.handleDisputeEscalated.bind(this));
  }

  /**
   * Subscribe to Redis channels for cross-server communication
   */
  async subscribeToChannels() {
    try {
      const channels = [
        'kryptopesa:realtime:trading',
        'kryptopesa:realtime:wallet',
        'kryptopesa:realtime:chat',
        'kryptopesa:realtime:system',
        'kryptopesa:realtime:presence',
        'kryptopesa:realtime:admin'
      ];

      for (const channel of channels) {
        await this.redisClient.subscribe(channel, (message) => {
          this.handleRedisMessage(channel, message);
        });
      }

      logger.info(`Subscribed to ${channels.length} real-time channels`);
    } catch (error) {
      logger.error('Failed to subscribe to Redis channels:', error);
    }
  }

  /**
   * Handle incoming Redis messages
   */
  async handleRedisMessage(channel, message) {
    try {
      const data = JSON.parse(message);
      const { event, payload, serverId, timestamp } = data;

      // Skip messages from same server instance
      if (serverId === process.env.SERVER_ID) {
        return;
      }

      // Add to event queue for processing
      this.eventQueue.push({
        channel,
        event,
        payload,
        serverId,
        timestamp: new Date(timestamp),
        receivedAt: new Date()
      });

      // Process queue if not already processing
      if (!this.processingQueue) {
        this.processEventQueue();
      }

      this.metrics.totalEvents++;
    } catch (error) {
      logger.error('Failed to handle Redis message:', error);
    }
  }

  /**
   * Process event queue
   */
  async processEventQueue() {
    if (this.processingQueue || this.eventQueue.length === 0) {
      return;
    }

    this.processingQueue = true;

    try {
      while (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift();
        await this.processEvent(event);
      }
    } catch (error) {
      logger.error('Error processing event queue:', error);
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * Process individual event
   */
  async processEvent(event) {
    try {
      const startTime = Date.now();
      const handler = this.eventHandlers.get(event.event);

      if (handler) {
        await handler(event.payload, event);
      } else {
        logger.warn(`No handler found for event: ${event.event}`);
      }

      // Update latency metrics
      const latency = Date.now() - startTime;
      this.updateLatencyMetrics(latency);
    } catch (error) {
      logger.error(`Failed to process event ${event.event}:`, error);
      this.metrics.failedDeliveries++;
    }
  }

  /**
   * Publish event to Redis for cross-server communication
   */
  async publishEvent(channel, event, payload, targetUsers = null) {
    try {
      if (!this.redisClient) {
        logger.warn('Redis not available, event not published');
        return false;
      }

      const message = JSON.stringify({
        event,
        payload,
        targetUsers,
        serverId: process.env.SERVER_ID || 'unknown',
        timestamp: new Date().toISOString()
      });

      await this.redisClient.publish(`kryptopesa:realtime:${channel}`, message);
      logger.debug(`Published event ${event} to channel ${channel}`);
      return true;
    } catch (error) {
      logger.error(`Failed to publish event ${event}:`, error);
      return false;
    }
  }

  /**
   * Register user connection
   */
  registerUserConnection(userId, socketId) {
    this.connectionPool.set(userId, socketId);
    this.updateUserPresence(userId, { status: 'online', lastSeen: new Date() });
    logger.debug(`User ${userId} connected with socket ${socketId}`);
  }

  /**
   * Unregister user connection
   */
  unregisterUserConnection(userId) {
    this.connectionPool.delete(userId);
    this.updateUserPresence(userId, { status: 'offline', lastSeen: new Date() });
    logger.debug(`User ${userId} disconnected`);
  }

  /**
   * Update user presence
   */
  updateUserPresence(userId, presenceData) {
    this.userPresence.set(userId, {
      ...this.userPresence.get(userId),
      ...presenceData,
      updatedAt: new Date()
    });

    // Publish presence update
    this.publishEvent('presence', 'user_presence_update', {
      userId,
      presence: presenceData
    });
  }

  /**
   * Subscribe user to room
   */
  subscribeToRoom(userId, roomId) {
    if (!this.roomSubscriptions.has(roomId)) {
      this.roomSubscriptions.set(roomId, new Set());
    }
    this.roomSubscriptions.get(roomId).add(userId);
    this.metrics.activeSubscriptions++;
  }

  /**
   * Unsubscribe user from room
   */
  unsubscribeFromRoom(userId, roomId) {
    if (this.roomSubscriptions.has(roomId)) {
      this.roomSubscriptions.get(roomId).delete(userId);
      if (this.roomSubscriptions.get(roomId).size === 0) {
        this.roomSubscriptions.delete(roomId);
      }
      this.metrics.activeSubscriptions--;
    }
  }

  /**
   * Send event to specific user
   */
  async sendToUser(userId, event, data) {
    try {
      const socketId = this.connectionPool.get(userId);
      if (socketId) {
        // User is connected to this server instance
        const io = require('../server').io;
        io.to(socketId).emit(event, data);
        return true;
      } else {
        // User might be connected to another server instance
        await this.publishEvent('system', 'user_targeted_message', {
          userId,
          event,
          data
        }, [userId]);
        return true;
      }
    } catch (error) {
      logger.error(`Failed to send event to user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Send event to room
   */
  async sendToRoom(roomId, event, data, excludeUserId = null) {
    try {
      const participants = this.roomSubscriptions.get(roomId);
      if (!participants) {
        return false;
      }

      for (const userId of participants) {
        if (userId !== excludeUserId) {
          await this.sendToUser(userId, event, data);
        }
      }

      return true;
    } catch (error) {
      logger.error(`Failed to send event to room ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Broadcast to all connected users
   */
  async broadcast(event, data) {
    try {
      const io = require('../server').io;
      io.emit(event, data);

      // Also publish for other server instances
      await this.publishEvent('system', 'global_broadcast', {
        event,
        data
      });

      return true;
    } catch (error) {
      logger.error('Failed to broadcast event:', error);
      return false;
    }
  }

  /**
   * Start background processes
   */
  startBackgroundProcesses() {
    // Metrics calculation every 10 seconds
    setInterval(() => {
      this.calculateMetrics();
    }, 10000);

    // Cleanup inactive connections every 5 minutes
    setInterval(() => {
      this.cleanupInactiveConnections();
    }, 5 * 60 * 1000);

    // Presence heartbeat every 30 seconds
    setInterval(() => {
      this.sendPresenceHeartbeat();
    }, 30000);

    logger.info('Real-time scalability background processes started');
  }

  /**
   * Calculate real-time metrics
   */
  calculateMetrics() {
    const now = new Date();
    const timeDiff = (now - this.metrics.lastReset) / 1000; // seconds

    if (timeDiff > 0) {
      this.metrics.eventsPerSecond = this.metrics.totalEvents / timeDiff;
    }

    this.metrics.activeSubscriptions = Array.from(this.roomSubscriptions.values())
      .reduce((total, room) => total + room.size, 0);

    // Reset counters every minute
    if (timeDiff >= 60) {
      this.metrics.totalEvents = 0;
      this.metrics.lastReset = now;
    }
  }

  /**
   * Update latency metrics
   */
  updateLatencyMetrics(latency) {
    this.metrics.averageLatency = (this.metrics.averageLatency + latency) / 2;
  }

  /**
   * Cleanup inactive connections
   */
  cleanupInactiveConnections() {
    const now = new Date();
    const inactiveThreshold = 5 * 60 * 1000; // 5 minutes

    for (const [userId, presence] of this.userPresence.entries()) {
      if (now - presence.updatedAt > inactiveThreshold) {
        this.unregisterUserConnection(userId);
        this.userPresence.delete(userId);
      }
    }
  }

  /**
   * Send presence heartbeat
   */
  async sendPresenceHeartbeat() {
    const onlineUsers = Array.from(this.connectionPool.keys());
    if (onlineUsers.length > 0) {
      await this.publishEvent('presence', 'presence_heartbeat', {
        serverId: process.env.SERVER_ID,
        onlineUsers,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get service metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      connectedUsers: this.connectionPool.size,
      activeRooms: this.roomSubscriptions.size,
      totalSubscriptions: this.metrics.activeSubscriptions,
      isInitialized: this.isInitialized,
      queueSize: this.eventQueue.length
    };
  }

  /**
   * Get user presence
   */
  getUserPresence(userId) {
    return this.userPresence.get(userId) || { status: 'offline' };
  }

  /**
   * Get room participants
   */
  getRoomParticipants(roomId) {
    return Array.from(this.roomSubscriptions.get(roomId) || []);
  }

  /**
   * Check if user is online
   */
  isUserOnline(userId) {
    return this.connectionPool.has(userId);
  }

  // ==================== EVENT HANDLERS ====================

  /**
   * Handle trade status update
   */
  async handleTradeStatusUpdate(payload) {
    const { tradeId, status, userId, data } = payload;
    await this.sendToRoom(`trade:${tradeId}`, 'trade_status_update', {
      tradeId,
      status,
      data,
      timestamp: new Date()
    });
  }

  /**
   * Handle trade created
   */
  async handleTradeCreated(payload) {
    const { trade, createdBy } = payload;
    // Notify potential buyers/sellers
    await this.broadcast('new_trade_available', {
      trade,
      createdBy,
      timestamp: new Date()
    });
  }

  /**
   * Handle trade accepted
   */
  async handleTradeAccepted(payload) {
    const { tradeId, acceptedBy, buyer, seller } = payload;

    // Notify both parties
    await this.sendToUser(buyer, 'trade_accepted', {
      tradeId,
      acceptedBy,
      timestamp: new Date()
    });

    await this.sendToUser(seller, 'trade_accepted', {
      tradeId,
      acceptedBy,
      timestamp: new Date()
    });
  }

  /**
   * Handle trade completed
   */
  async handleTradeCompleted(payload) {
    const { tradeId, completedBy, buyer, seller, amount } = payload;

    await this.sendToRoom(`trade:${tradeId}`, 'trade_completed', {
      tradeId,
      completedBy,
      amount,
      timestamp: new Date()
    });
  }

  /**
   * Handle trade disputed
   */
  async handleTradeDisputed(payload) {
    const { tradeId, disputedBy, reason, buyer, seller } = payload;

    // Notify trade participants
    await this.sendToRoom(`trade:${tradeId}`, 'trade_disputed', {
      tradeId,
      disputedBy,
      reason,
      timestamp: new Date()
    });

    // Notify admin
    await this.publishEvent('admin', 'dispute_escalated', {
      tradeId,
      disputedBy,
      reason,
      participants: [buyer, seller]
    });
  }

  /**
   * Handle balance update
   */
  async handleBalanceUpdate(payload) {
    const { userId, balances, currency } = payload;

    await this.sendToUser(userId, 'balance_update', {
      balances,
      currency,
      timestamp: new Date()
    });
  }

  /**
   * Handle transaction confirmed
   */
  async handleTransactionConfirmed(payload) {
    const { userId, txHash, amount, currency, confirmations } = payload;

    await this.sendToUser(userId, 'transaction_confirmed', {
      txHash,
      amount,
      currency,
      confirmations,
      timestamp: new Date()
    });
  }

  /**
   * Handle transaction failed
   */
  async handleTransactionFailed(payload) {
    const { userId, txHash, reason } = payload;

    await this.sendToUser(userId, 'transaction_failed', {
      txHash,
      reason,
      timestamp: new Date()
    });
  }

  /**
   * Handle new message
   */
  async handleNewMessage(payload) {
    const { tradeId, message, senderId } = payload;

    await this.sendToRoom(`trade:${tradeId}`, 'new_message', {
      message,
      senderId,
      timestamp: new Date()
    }, senderId);
  }

  /**
   * Handle typing indicator
   */
  async handleTypingIndicator(payload) {
    const { tradeId, userId, isTyping } = payload;

    await this.sendToRoom(`trade:${tradeId}`, 'typing_indicator', {
      userId,
      isTyping,
      timestamp: new Date()
    }, userId);
  }

  /**
   * Handle message read
   */
  async handleMessageRead(payload) {
    const { tradeId, messageId, readBy } = payload;

    await this.sendToRoom(`trade:${tradeId}`, 'message_read', {
      messageId,
      readBy,
      timestamp: new Date()
    }, readBy);
  }

  /**
   * Handle system notification
   */
  async handleSystemNotification(payload) {
    const { message, type, targetUsers } = payload;

    if (targetUsers && targetUsers.length > 0) {
      for (const userId of targetUsers) {
        await this.sendToUser(userId, 'system_notification', {
          message,
          type,
          timestamp: new Date()
        });
      }
    } else {
      await this.broadcast('system_notification', {
        message,
        type,
        timestamp: new Date()
      });
    }
  }

  /**
   * Handle maintenance mode
   */
  async handleMaintenanceMode(payload) {
    const { enabled, message, estimatedDuration } = payload;

    await this.broadcast('maintenance_mode', {
      enabled,
      message,
      estimatedDuration,
      timestamp: new Date()
    });
  }

  /**
   * Handle price update
   */
  async handlePriceUpdate(payload) {
    const { prices, currency } = payload;

    await this.broadcast('price_update', {
      prices,
      currency,
      timestamp: new Date()
    });
  }

  /**
   * Handle user online
   */
  async handleUserOnline(payload) {
    const { userId, userInfo } = payload;

    this.updateUserPresence(userId, {
      status: 'online',
      lastSeen: new Date(),
      ...userInfo
    });
  }

  /**
   * Handle user offline
   */
  async handleUserOffline(payload) {
    const { userId } = payload;

    this.updateUserPresence(userId, {
      status: 'offline',
      lastSeen: new Date()
    });
  }

  /**
   * Handle user activity
   */
  async handleUserActivity(payload) {
    const { userId, activity } = payload;

    this.updateUserPresence(userId, {
      lastActivity: activity,
      lastSeen: new Date()
    });
  }

  /**
   * Handle admin alert
   */
  async handleAdminAlert(payload) {
    const { alert, severity, data } = payload;

    // Send to admin dashboard
    await this.publishEvent('admin', 'admin_alert', {
      alert,
      severity,
      data,
      timestamp: new Date()
    });
  }

  /**
   * Handle dispute escalated
   */
  async handleDisputeEscalated(payload) {
    const { tradeId, disputedBy, reason, participants } = payload;

    // Notify admin dashboard
    await this.publishEvent('admin', 'new_dispute', {
      tradeId,
      disputedBy,
      reason,
      participants,
      timestamp: new Date()
    });
  }

  /**
   * Shutdown service
   */
  async shutdown() {
    try {
      if (this.redisClient) {
        await this.redisClient.unsubscribe();
      }

      this.connectionPool.clear();
      this.userPresence.clear();
      this.roomSubscriptions.clear();
      this.eventQueue.length = 0;

      this.isInitialized = false;
      logger.info('Real-time scalability service shutdown complete');
    } catch (error) {
      logger.error('Error during real-time scalability service shutdown:', error);
    }
  }
}

// Create singleton instance
const realTimeScalabilityService = new RealTimeScalabilityService();

module.exports = realTimeScalabilityService;
