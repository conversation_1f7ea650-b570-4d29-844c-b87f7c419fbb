/**
 * Circuit Breaker Comprehensive Testing
 * Tests all circuit breaker states and transitions
 */

// Set up test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long-for-testing';

const { CircuitBreaker, circuitBreakers } = require('../../utils/circuitBreaker');

describe('Circuit Breaker Comprehensive Testing', () => {
  let testCircuitBreaker;

  beforeEach(() => {
    // Create a test circuit breaker with low thresholds for testing
    testCircuitBreaker = new CircuitBreaker({
      name: 'TestService',
      failureThreshold: 3,
      timeout: 1000, // 1 second
      monitoringPeriod: 500
    });

    // Reset all circuit breakers
    Object.values(circuitBreakers).forEach(cb => {
      if (cb.reset) cb.reset();
    });
  });

  afterEach(() => {
    if (testCircuitBreaker && testCircuitBreaker.stopMonitoring) {
      testCircuitBreaker.stopMonitoring();
    }
  });

  describe('Circuit Breaker State Transitions', () => {
    test('should start in CLOSED state', () => {
      expect(testCircuitBreaker.state).toBe('CLOSED');
      expect(testCircuitBreaker.failureCount).toBe(0);
      expect(testCircuitBreaker.successCount).toBe(0);
    });

    test('should transition to OPEN state after failure threshold', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new Error('Service failure'));

      // Execute failing operations to reach threshold
      for (let i = 0; i < 3; i++) {
        try {
          await testCircuitBreaker.execute(failingOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      expect(testCircuitBreaker.state).toBe('OPEN');
      expect(testCircuitBreaker.failureCount).toBe(3);
    });

    test('should transition to HALF_OPEN state after timeout', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new Error('Service failure'));

      // Open the circuit breaker
      for (let i = 0; i < 3; i++) {
        try {
          await testCircuitBreaker.execute(failingOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      expect(testCircuitBreaker.state).toBe('OPEN');

      // Simulate timeout by setting nextAttempt to past
      testCircuitBreaker.nextAttempt = Date.now() - 1000;

      // Next execution should transition to HALF_OPEN
      try {
        await testCircuitBreaker.execute(failingOperation);
      } catch (error) {
        // Expected to fail
      }

      expect(testCircuitBreaker.state).toBe('HALF_OPEN');
    });

    test('should transition from HALF_OPEN to CLOSED on success', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new Error('Service failure'));
      const successfulOperation = jest.fn().mockResolvedValue('success');

      // Open the circuit breaker
      for (let i = 0; i < 3; i++) {
        try {
          await testCircuitBreaker.execute(failingOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      // Transition to HALF_OPEN
      testCircuitBreaker.nextAttempt = Date.now() - 1000;

      // Successful operation should close the circuit
      const result = await testCircuitBreaker.execute(successfulOperation);

      expect(result).toBe('success');
      expect(testCircuitBreaker.state).toBe('CLOSED');
      expect(testCircuitBreaker.failureCount).toBe(0);
    });

    test('should transition from HALF_OPEN back to OPEN on failure', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new Error('Service failure'));

      // Open the circuit breaker
      for (let i = 0; i < 3; i++) {
        try {
          await testCircuitBreaker.execute(failingOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      // Transition to HALF_OPEN
      testCircuitBreaker.nextAttempt = Date.now() - 1000;

      // Another failure should open the circuit again
      try {
        await testCircuitBreaker.execute(failingOperation);
      } catch (error) {
        // Expected to fail
      }

      expect(testCircuitBreaker.state).toBe('OPEN');
    });
  });

  describe('Circuit Breaker Fallback Mechanisms', () => {
    test('should execute fallback function when circuit is open', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new Error('Service failure'));
      const fallbackFunction = jest.fn().mockResolvedValue('fallback result');

      // Open the circuit breaker
      for (let i = 0; i < 3; i++) {
        try {
          await testCircuitBreaker.execute(failingOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      expect(testCircuitBreaker.state).toBe('OPEN');

      // Execute with fallback
      const result = await testCircuitBreaker.execute(failingOperation, fallbackFunction);

      expect(result).toBe('fallback result');
      expect(fallbackFunction).toHaveBeenCalled();
      expect(failingOperation).not.toHaveBeenCalledTimes(4); // Should not call main operation
    });

    test('should throw error when circuit is open and no fallback provided', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new Error('Service failure'));

      // Open the circuit breaker
      for (let i = 0; i < 3; i++) {
        try {
          await testCircuitBreaker.execute(failingOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      expect(testCircuitBreaker.state).toBe('OPEN');

      // Should throw circuit breaker error
      await expect(testCircuitBreaker.execute(failingOperation)).rejects.toThrow('Circuit breaker TestService is OPEN');
    });
  });

  describe('Circuit Breaker Metrics', () => {
    test('should track request metrics correctly', async () => {
      const successfulOperation = jest.fn().mockResolvedValue('success');
      const failingOperation = jest.fn().mockRejectedValue(new Error('failure'));

      // Execute some operations
      await testCircuitBreaker.execute(successfulOperation);
      await testCircuitBreaker.execute(successfulOperation);

      try {
        await testCircuitBreaker.execute(failingOperation);
      } catch (error) {
        // Expected to fail
      }

      const metrics = testCircuitBreaker.getMetrics();

      expect(metrics.totalRequests).toBe(3);
      expect(metrics.totalSuccesses).toBe(2);
      expect(metrics.totalFailures).toBe(1);
      expect(metrics.averageResponseTime).toBeGreaterThan(0);
    });

    test('should reset metrics when circuit breaker is reset', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new Error('failure'));

      // Generate some metrics
      for (let i = 0; i < 3; i++) {
        try {
          await testCircuitBreaker.execute(failingOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      expect(testCircuitBreaker.getMetrics().totalRequests).toBe(3);

      // Reset circuit breaker
      testCircuitBreaker.reset();

      const metrics = testCircuitBreaker.getMetrics();
      expect(metrics.totalRequests).toBe(0);
      expect(metrics.totalFailures).toBe(0);
      expect(testCircuitBreaker.state).toBe('CLOSED');
    });
  });

  describe('Expected Errors Handling', () => {
    test('should not trigger circuit breaker for expected errors', async () => {
      // Create circuit breaker with expected errors
      const cbWithExpectedErrors = new CircuitBreaker({
        name: 'TestServiceWithExpectedErrors',
        failureThreshold: 3,
        timeout: 1000,
        expectedErrors: ['ValidationError', 'NotFoundError']
      });

      const validationErrorOperation = jest.fn().mockRejectedValue(new Error('ValidationError: Invalid input'));
      const notFoundErrorOperation = jest.fn().mockRejectedValue(new Error('NotFoundError: Resource not found'));

      // Execute operations with expected errors
      for (let i = 0; i < 5; i++) {
        try {
          await cbWithExpectedErrors.execute(validationErrorOperation);
        } catch (error) {
          // Expected to fail
        }

        try {
          await cbWithExpectedErrors.execute(notFoundErrorOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      // Circuit breaker should remain closed
      expect(cbWithExpectedErrors.state).toBe('CLOSED');
      expect(cbWithExpectedErrors.failureCount).toBe(0);

      cbWithExpectedErrors.stopMonitoring();
    });

    test('should trigger circuit breaker for unexpected errors', async () => {
      const cbWithExpectedErrors = new CircuitBreaker({
        name: 'TestServiceWithExpectedErrors2',
        failureThreshold: 3,
        timeout: 1000,
        expectedErrors: ['ValidationError']
      });

      const networkErrorOperation = jest.fn().mockRejectedValue(new Error('NetworkError: Connection failed'));

      // Execute operations with unexpected errors
      for (let i = 0; i < 3; i++) {
        try {
          await cbWithExpectedErrors.execute(networkErrorOperation);
        } catch (error) {
          // Expected to fail
        }
      }

      // Circuit breaker should open for unexpected errors
      expect(cbWithExpectedErrors.state).toBe('OPEN');
      expect(cbWithExpectedErrors.failureCount).toBe(3);

      cbWithExpectedErrors.stopMonitoring();
    });
  });

  describe('Pre-configured Circuit Breakers', () => {
    test('should have database circuit breaker configured', () => {
      expect(circuitBreakers.database).toBeDefined();
      expect(circuitBreakers.database.name).toBe('Database');
      expect(circuitBreakers.database.failureThreshold).toBe(3);
    });

    test('should have blockchain circuit breaker configured', () => {
      expect(circuitBreakers.blockchain).toBeDefined();
      expect(circuitBreakers.blockchain.name).toBe('Blockchain');
      expect(circuitBreakers.blockchain.failureThreshold).toBe(5);
    });

    test('should have redis circuit breaker configured', () => {
      expect(circuitBreakers.redis).toBeDefined();
      expect(circuitBreakers.redis.name).toBe('Redis');
      expect(circuitBreakers.redis.failureThreshold).toBe(3);
    });

    test('should have external API circuit breaker configured', () => {
      expect(circuitBreakers.external).toBeDefined();
      expect(circuitBreakers.external.name).toBe('ExternalAPI');
      expect(circuitBreakers.external.failureThreshold).toBe(5);
    });
  });

  describe('Circuit Breaker Timeout Handling', () => {
    test('should handle operation timeouts', async () => {
      const slowOperation = jest.fn().mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => resolve('slow result'), 2000); // 2 seconds
        });
      });

      const fastCircuitBreaker = new CircuitBreaker({
        name: 'FastService',
        failureThreshold: 3,
        timeout: 500 // 500ms timeout
      });

      // Should timeout and count as failure
      try {
        await fastCircuitBreaker.execute(slowOperation);
      } catch (error) {
        expect(error.message).toContain('timeout');
      }

      expect(fastCircuitBreaker.failureCount).toBe(1);
      expect(fastCircuitBreaker.getMetrics().totalTimeouts).toBe(1);

      fastCircuitBreaker.stopMonitoring();
    });
  });
});
