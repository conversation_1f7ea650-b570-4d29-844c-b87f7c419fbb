const mongoose = require('mongoose');
const crypto = require('crypto');

const walletUserSchema = new mongoose.Schema({
  // Wallet-based identity (PRIMARY IDENTIFIER)
  walletAddress: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    index: true,
    match: /^0x[a-fA-F0-9]{40}$/ // Ethereum address format
  },
  publicKey: {
    type: String,
    required: true,
    trim: true
  },
  
  // Optional profile information (user can choose to provide)
  profile: {
    displayName: {
      type: String,
      trim: true,
      maxlength: 50,
      default: null
    },
    avatar: {
      type: String,
      default: null
    },
    bio: {
      type: String,
      maxlength: 500,
      default: null
    },
    // Location for trading preferences (optional)
    location: {
      country: {
        type: String,
        maxlength: 2, // ISO country code
        default: null
      },
      city: {
        type: String,
        maxlength: 100,
        default: null
      }
    }
  },

  // Trading reputation and statistics
  reputation: {
    score: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    totalTrades: {
      type: Number,
      default: 0,
      min: 0
    },
    completedTrades: {
      type: Number,
      default: 0,
      min: 0
    },
    cancelledTrades: {
      type: Number,
      default: 0,
      min: 0
    },
    disputedTrades: {
      type: Number,
      default: 0,
      min: 0
    },
    averageResponseTime: {
      type: Number, // in minutes
      default: null
    },
    lastTradeAt: {
      type: Date,
      default: null
    }
  },

  // User preferences
  preferences: {
    language: {
      type: String,
      enum: ['en', 'sw'], // English, Swahili
      default: 'en'
    },
    currency: {
      type: String,
      enum: ['KES', 'TZS', 'UGX', 'RWF', 'USD'],
      default: 'KES'
    },
    notifications: {
      push: {
        type: Boolean,
        default: true
      },
      tradeUpdates: {
        type: Boolean,
        default: true
      },
      priceAlerts: {
        type: Boolean,
        default: false
      }
    },
    trading: {
      autoReply: {
        type: Boolean,
        default: false
      },
      showOnlineStatus: {
        type: Boolean,
        default: true
      },
      allowDirectMessages: {
        type: Boolean,
        default: true
      }
    }
  },

  // Account status and security
  status: {
    type: String,
    enum: ['active', 'suspended', 'banned'],
    default: 'active'
  },
  isLocked: {
    type: Boolean,
    default: false
  },
  lockReason: {
    type: String,
    default: null
  },
  lockExpiresAt: {
    type: Date,
    default: null
  },

  // Activity tracking
  lastActive: {
    type: Date,
    default: Date.now
  },
  lastLoginIP: {
    type: String,
    default: null
  },
  deviceInfo: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },

  // KYC and verification (simplified)
  verification: {
    level: {
      type: Number,
      enum: [0, 1, 2, 3], // 0=none, 1=basic, 2=intermediate, 3=advanced
      default: 0
    },
    status: {
      type: String,
      enum: ['not_started', 'pending', 'approved', 'rejected'],
      default: 'not_started'
    },
    documents: [{
      type: {
        type: String,
        enum: ['national_id', 'passport', 'driving_license', 'proof_of_address']
      },
      status: {
        type: String,
        enum: ['pending', 'approved', 'rejected']
      },
      uploadedAt: Date,
      reviewedAt: Date,
      reviewedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'WalletUser'
      }
    }],
    tradingLimits: {
      daily: {
        type: Number,
        default: 1000 // USD equivalent
      },
      monthly: {
        type: Number,
        default: 10000 // USD equivalent
      }
    }
  },

  // Metadata
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      // Remove sensitive fields from JSON output
      delete ret.__v;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Indexes for performance
walletUserSchema.index({ walletAddress: 1 }, { unique: true });
walletUserSchema.index({ 'profile.displayName': 1 });
walletUserSchema.index({ status: 1 });
walletUserSchema.index({ lastActive: -1 });
walletUserSchema.index({ 'reputation.score': -1 });
walletUserSchema.index({ 'verification.level': 1 });

// Virtual for completion percentage
walletUserSchema.virtual('profileCompletion').get(function() {
  let completion = 0;
  const fields = [
    this.profile?.displayName,
    this.profile?.avatar,
    this.profile?.bio,
    this.profile?.location?.country,
    this.profile?.location?.city
  ];
  
  fields.forEach(field => {
    if (field) completion += 20;
  });
  
  return completion;
});

// Virtual for trading success rate
walletUserSchema.virtual('successRate').get(function() {
  if (this.reputation.totalTrades === 0) return 0;
  return (this.reputation.completedTrades / this.reputation.totalTrades) * 100;
});

// Virtual for display name fallback
walletUserSchema.virtual('displayName').get(function() {
  return this.profile?.displayName || 
         `${this.walletAddress.slice(0, 6)}...${this.walletAddress.slice(-4)}`;
});

// Methods
walletUserSchema.methods.updateLastActive = function() {
  this.lastActive = new Date();
  return this.save();
};

walletUserSchema.methods.updateReputation = function(tradeResult) {
  this.reputation.totalTrades += 1;
  
  switch (tradeResult) {
    case 'completed':
      this.reputation.completedTrades += 1;
      break;
    case 'cancelled':
      this.reputation.cancelledTrades += 1;
      break;
    case 'disputed':
      this.reputation.disputedTrades += 1;
      break;
  }
  
  // Recalculate score (simple algorithm)
  const total = this.reputation.totalTrades;
  const completed = this.reputation.completedTrades;
  const disputed = this.reputation.disputedTrades;
  
  if (total > 0) {
    this.reputation.score = Math.max(0, Math.min(5, 
      (completed / total) * 5 - (disputed / total) * 2
    ));
  }
  
  this.reputation.lastTradeAt = new Date();
  return this.save();
};

walletUserSchema.methods.canTrade = function(amount = 0) {
  if (this.status !== 'active' || this.isLocked) return false;
  
  // Check KYC limits
  const limits = this.verification.tradingLimits;
  // In a real implementation, you'd check current usage against limits
  
  return amount <= limits.daily;
};

walletUserSchema.methods.toPublicJSON = function() {
  return {
    walletAddress: this.walletAddress,
    displayName: this.displayName,
    avatar: this.profile?.avatar,
    bio: this.profile?.bio,
    location: this.profile?.location,
    reputation: this.reputation,
    verification: {
      level: this.verification.level,
      status: this.verification.status
    },
    lastActive: this.lastActive,
    profileCompletion: this.profileCompletion,
    successRate: this.successRate
  };
};

// Static methods
walletUserSchema.statics.findByWalletAddress = function(walletAddress) {
  return this.findOne({ walletAddress: walletAddress.toLowerCase() });
};

walletUserSchema.statics.createFromWallet = function(walletAddress, publicKey, additionalData = {}) {
  return this.create({
    walletAddress: walletAddress.toLowerCase(),
    publicKey,
    ...additionalData
  });
};

module.exports = mongoose.model('WalletUser', walletUserSchema);
