
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for services/blockchain</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> services/blockchain</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">13.76% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>34/247</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>9/75</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.28% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>8/56</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">13.8% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>33/239</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="bitcoinService.js"><a href="bitcoinService.js.html">bitcoinService.js</a></td>
	<td data-value="16" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16" class="pct low">16%</td>
	<td data-value="125" class="abs low">20/125</td>
	<td data-value="7.89" class="pct low">7.89%</td>
	<td data-value="38" class="abs low">3/38</td>
	<td data-value="17.24" class="pct low">17.24%</td>
	<td data-value="29" class="abs low">5/29</td>
	<td data-value="15.83" class="pct low">15.83%</td>
	<td data-value="120" class="abs low">19/120</td>
	</tr>

<tr>
	<td class="file low" data-value="ethereumService.js"><a href="ethereumService.js.html">ethereumService.js</a></td>
	<td data-value="11.47" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.47" class="pct low">11.47%</td>
	<td data-value="122" class="abs low">14/122</td>
	<td data-value="16.21" class="pct low">16.21%</td>
	<td data-value="37" class="abs low">6/37</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="27" class="abs low">3/27</td>
	<td data-value="11.76" class="pct low">11.76%</td>
	<td data-value="119" class="abs low">14/119</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-08T14:32:16.455Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    