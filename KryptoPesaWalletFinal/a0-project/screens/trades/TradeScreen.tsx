import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
  Modal,
  Alert,

} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';


// Mock data for UI development - Trade Details Screen
const mockTrade = {
  id: '1',
  status: 'payment_sent', // 'pending', 'payment_sent', 'completed', 'disputed', 'cancelled'
  type: 'buy', // from the current user's perspective
  cryptoCurrency: 'BTC',
  fiatCurrency: 'USD',
  cryptoAmount: 0.0025,
  fiatAmount: 125.5,
  price: 50200.00,
  paymentMethod: 'Bank Transfer',
  paymentDetails: 'Bank: Chase\nAccount Name: <PERSON>\nAccount Number: XXXX-XXXX-1234\nRouting Number: *********',
  escrowReleased: false,
  createdAt: '2023-07-05T17:30:00Z',
  timeRemaining: 30, // minutes
  counterparty: {
    id: '2',
    name: 'Alice Trader',
    completedTrades: 42,
    rating: 4.8,
    avatar: null,
  },
};

const mockMessages = [
  {
    id: '1',
    senderId: '1', // current user
    content: 'Hi, I\'m interested in this trade. I\'ll make the payment shortly.',
    timestamp: '2023-07-05T17:35:00Z',
    isRead: true,
  },
  {
    id: '2',
    senderId: '2', // counterparty
    content: 'Great! Please let me know once you\'ve made the payment.',
    timestamp: '2023-07-05T17:37:00Z',
    isRead: true,
  },
  {
    id: '3',
    senderId: '1', // current user
    content: 'I\'ve just sent the payment. Please check your account.',
    timestamp: '2023-07-05T17:45:00Z',
    isRead: true,
  },
  {
    id: '4',
    senderId: '1', // current user
    content: 'Great! Please let me know once you\'ve made the payment.',
    timestamp: '2023-07-05T14:37:00Z',
    isRead: true,
  },
  {
    id: '5',
    senderId: '2', // counterparty
    content: 'I\'ve just sent the payment. Please check your account.',
    timestamp: '2023-07-05T14:45:00Z',
    isRead: true,
  },
];

export default function TradeScreen() {
  const navigation = useNavigation();
  // const route = useRoute(); // Available for future use
  const { theme } = useTheme();
  // const tradeId = (route.params as any)?.tradeId; // Available for future use
  
  const [trade, setTrade] = useState(mockTrade);
  const [messages, setMessages] = useState(mockMessages);
  const [newMessage, setNewMessage] = useState('');
  const [disputeModalVisible, setDisputeModalVisible] = useState(false);
  const [disputeReason, setDisputeReason] = useState('');
  const [paymentMarkedModalVisible, setPaymentMarkedModalVisible] = useState(false);
  
  // In a real app, we would fetch this data from Convex
  // const trade = useQuery(api.trades.getTrade, { tradeId });
  // const messages = useQuery(api.messages.getTradeMessages, { tradeId });
  
  const sendMessage = () => {
    if (!newMessage.trim()) return;
    
    // In a real app, we would call a Convex mutation
    // const result = await sendMessageMutation({ tradeId, content: newMessage });
    
    // For now, just add the message to the local state
    const newMsg = {
      id: `local-${Date.now()}`,
      senderId: '1', // current user
      content: newMessage,
      timestamp: new Date().toISOString(),
      isRead: false,
    };
    
    setMessages([...messages, newMsg]);
    setNewMessage('');
  };
  
  const markAsPaid = () => {
    // In a real app, we would call a Convex mutation
    // const result = await markAsPaidMutation({ tradeId });
    
    // For now, just update the local state
    setTrade({ ...trade, status: 'paid' });
    setPaymentMarkedModalVisible(true);
    
    // Add system message
    const systemMsg = {
      id: `system-${Date.now()}`,
      senderId: 'system',
      content: 'Payment has been marked as sent. Please wait for the seller to release the crypto.',
      timestamp: new Date().toISOString(),
      isRead: true,
      isSystem: true,
    };
    
    setMessages([...messages, systemMsg]);
  };
  
  const releaseEscrow = () => {
    // Confirm with the user
    Alert.alert(
      'Release Escrow',
      'Are you sure you want to release the crypto? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Release',
          onPress: () => {
            // In a real app, we would call a Convex mutation
            // const result = await releaseEscrowMutation({ tradeId });
            
            // For now, just update the local state
            setTrade({ ...trade, status: 'completed', escrowReleased: true });
            
            // Add system message
            const systemMsg = {
              id: `system-${Date.now()}`,
              senderId: 'system',
              content: 'Escrow has been released. The trade is now complete.',
              timestamp: new Date().toISOString(),
              isRead: true,
              isSystem: true,
            };
            
            setMessages([...messages, systemMsg]);
          },
        },
      ],
    );
  };
  
  const raiseDispute = () => {
    if (!disputeReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for the dispute.');
      return;
    }
    
    // In a real app, we would call a Convex mutation
    // const result = await raiseDisputeMutation({ tradeId, reason: disputeReason });
    
    // For now, just update the local state
    setTrade({ ...trade, status: 'disputed' });
    setDisputeModalVisible(false);
    
    // Add system message
    const systemMsg = {
      id: `system-${Date.now()}`,
      senderId: 'system',
      content: `A dispute has been raised: "${disputeReason}". Our support team will review the case.`,
      timestamp: new Date().toISOString(),
      isRead: true,
      isSystem: true,
    };
    
    setMessages([...messages, systemMsg]);
  };
  
  const cancelTrade = () => {
    // Confirm with the user
    Alert.alert(
      'Cancel Trade',
      'Are you sure you want to cancel this trade?',
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes',
          onPress: () => {
            // In a real app, we would call a Convex mutation
            // const result = await cancelTradeMutation({ tradeId });
            
            // For now, just update the local state
            setTrade({ ...trade, status: 'cancelled' });
            
            // Add system message
            const systemMsg = {
              id: `system-${Date.now()}`,
              senderId: 'system',
              content: 'The trade has been cancelled.',
              timestamp: new Date().toISOString(),
              isRead: true,
              isSystem: true,
            };
            
            setMessages([...messages, systemMsg]);
          },
        },
      ],
    );
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning; // amber
      case 'paid':
        return theme.colors.primary; // blue
      case 'completed':
        return theme.colors.success; // green
      case 'disputed':
        return theme.colors.error; // red
      case 'cancelled':
        return theme.colors.textSecondary; // gray
      default:
        return theme.colors.textSecondary; // gray
    }
  };
  
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending Payment';
      case 'paid':
        return 'Payment Sent';
      case 'completed':
        return 'Completed';
      case 'disputed':
        return 'Disputed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Trade Details</Text>
        <TouchableOpacity 
          style={styles.menuButton}
          onPress={() => setDisputeModalVisible(true)}
        >
          <Ionicons name="ellipsis-vertical" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.tradeCard}>
          <View style={styles.tradeHeader}>
            <View style={styles.tradeType}>
              <Text style={[
                styles.tradeTypeText,
                trade.type === 'buy' ? styles.buyText : styles.sellText
              ]}>
                {trade.type === 'buy' ? 'Buying' : 'Selling'}
              </Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor(trade.status)}20` }]}>
              <Text style={[styles.statusText, { color: getStatusColor(trade.status) }]}>
                {getStatusText(trade.status)}
              </Text>
            </View>
          </View>
          
          <View style={styles.tradeDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Amount</Text>
              <View style={styles.detailValueContainer}>
                <Image 
                  source={{ uri: `https://api.a0.dev/assets/image?text=${trade.cryptoCurrency}&aspect=1:1&seed=${trade.cryptoCurrency}` }}
                  style={styles.currencyIcon}
                />
                <Text style={styles.detailValue}>
                  {trade.cryptoAmount} {trade.cryptoCurrency}
                </Text>
              </View>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Price</Text>
              <Text style={styles.detailValue}>
                {trade.price.toLocaleString()} {trade.fiatCurrency}/{trade.cryptoCurrency}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Total</Text>
              <Text style={styles.detailValue}>
                {trade.fiatAmount.toLocaleString()} {trade.fiatCurrency}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Payment Method</Text>
              <Text style={styles.detailValue}>{trade.paymentMethod}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Created</Text>
              <Text style={styles.detailValue}>{formatDate(trade.createdAt)}</Text>
            </View>
            
            {trade.status === 'pending' && (
              <View style={styles.timeRemainingContainer}>
                <Ionicons name="time-outline" size={16} color={theme.colors.warning} />
                <Text style={styles.timeRemainingText}>
                  {trade.timeRemaining} minutes remaining to complete payment
                </Text>
              </View>
            )}
          </View>
          
          <View style={styles.counterpartySection}>
            <Text style={styles.sectionTitle}>Trading with</Text>
            <View style={styles.counterpartyInfo}>
              <View style={styles.counterpartyLeft}>
                <Ionicons name="person-circle" size={40} color={theme.colors.textSecondary} />
                <View style={styles.counterpartyDetails}>
                  <Text style={styles.counterpartyName}>{trade.counterparty.name}</Text>
                  <View style={styles.counterpartyStats}>
                    <Text style={styles.tradeCount}>{trade.counterparty.completedTrades} trades</Text>
                    <View style={styles.ratingContainer}>
                      <Ionicons name="star" size={14} color={theme.colors.warning} />
                      <Text style={styles.ratingText}>{trade.counterparty.rating}</Text>
                    </View>
                  </View>
                </View>
              </View>
              <TouchableOpacity
                style={styles.viewProfileButton}
                onPress={() => (navigation as any).navigate('TraderProfile', { traderId: trade.counterparty.id })}
              >
                <Text style={styles.viewProfileText}>View Profile</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          {(trade.type === 'buy' && trade.status === 'pending') || 
           (trade.type === 'sell' && trade.status === 'paid') ? (
            <View style={styles.paymentSection}>
              <Text style={styles.sectionTitle}>Payment Details</Text>
              <View style={styles.paymentDetails}>
                <Text style={styles.paymentDetailsText}>{trade.paymentDetails}</Text>
                <TouchableOpacity style={styles.copyButton}>
                  <Ionicons name="copy-outline" size={18} color={theme.colors.primary} />
                </TouchableOpacity>
              </View>
              
              {trade.type === 'buy' && trade.status === 'pending' && (
                <TouchableOpacity 
                  style={styles.markAsPaidButton}
                  onPress={markAsPaid}
                >
                  <Text style={styles.markAsPaidButtonText}>I Have Paid</Text>
                </TouchableOpacity>
              )}
              
              {trade.type === 'sell' && trade.status === 'paid' && (
                <TouchableOpacity 
                  style={styles.releaseEscrowButton}
                  onPress={releaseEscrow}
                >
                  <Text style={styles.releaseEscrowButtonText}>Release Crypto</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : null}
          
          {trade.status === 'pending' && (
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={cancelTrade}
            >
              <Text style={styles.cancelButtonText}>Cancel Trade</Text>
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.chatSection}>
          <View style={styles.chatHeader}>
            <Text style={styles.sectionTitle}>Chat</Text>
            <TouchableOpacity
              style={styles.viewFullChatButton}
              onPress={() => (navigation as any).navigate('IndividualChat', {
                traderId: trade.counterparty.id,
                tradeId: trade.id,
                traderName: trade.counterparty.name
              })}
            >
              <Text style={styles.viewFullChatText}>View Full Chat</Text>
              <Ionicons name="chevron-forward" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
          <View style={styles.messagesContainer}>
            {messages.map((message) => (
              <View 
                key={message.id} 
                style={[
                  styles.messageItem,
                  message.senderId === '1' ? styles.sentMessage : 
                  (message as any).isSystem ? styles.systemMessage : styles.receivedMessage
                ]}
              >
                {(message as any).isSystem ? (
                  <View style={styles.systemMessageContent}>
                    <Ionicons name="information-circle" size={16} color={theme.colors.textSecondary} />
                    <Text style={styles.systemMessageText}>{message.content}</Text>
                  </View>
                ) : (
                  <View style={styles.messageContent}>
                    <Text style={styles.messageText}>{message.content}</Text>
                    <Text style={styles.messageTime}>
                      {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                  </View>
                )}
              </View>
            ))}
          </View>
          
          {trade.status !== 'completed' && trade.status !== 'cancelled' && (
            <View style={styles.messageInputContainer}>
              <TextInput
                style={styles.messageInput}
                placeholder="Type a message..."
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={newMessage}
                onChangeText={setNewMessage}
                multiline
              />
              <TouchableOpacity 
                style={styles.sendButton}
                onPress={sendMessage}
                disabled={!newMessage.trim()}
              >
                <Ionicons 
                  name="send" 
                  size={20} 
                  color={newMessage.trim() ? theme.colors.primary : theme.colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
      
      {/* Dispute Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={disputeModalVisible}
        onRequestClose={() => setDisputeModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Raise a Dispute</Text>
              <TouchableOpacity onPress={() => setDisputeModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalScrollView}>
              <Text style={styles.modalText}>
                Please provide details about why you're raising a dispute. Our support team will review your case.
              </Text>
              
              <Text style={styles.inputLabel}>Reason for dispute</Text>
              <TextInput
                style={styles.disputeInput}
                placeholder="Explain the issue in detail..."
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={disputeReason}
                onChangeText={setDisputeReason}
                multiline
                numberOfLines={5}
                textAlignVertical="top"
              />
              
              <View style={styles.warningContainer}>
                <Ionicons name="warning" size={20} color={theme.colors.warning} />
                <Text style={styles.warningText}>
                  Raising a dispute will pause the trade until our support team resolves the issue. Please only raise disputes for legitimate reasons.
                </Text>
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={styles.cancelDisputeButton}
                onPress={() => setDisputeModalVisible(false)}
              >
                <Text style={styles.cancelDisputeButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[
                  styles.raiseDisputeButton,
                  !disputeReason.trim() && styles.disabledButton
                ]}
                onPress={raiseDispute}
                disabled={!disputeReason.trim()}
              >
                <Text style={styles.raiseDisputeButtonText}>Raise Dispute</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      
      {/* Payment Marked Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={paymentMarkedModalVisible}
        onRequestClose={() => setPaymentMarkedModalVisible(false)}
      >
        <View style={styles.paymentMarkedOverlay}>
          <View style={styles.paymentMarkedContent}>
            <View style={styles.paymentMarkedIcon}>
              <Ionicons name="checkmark-circle" size={60} color={theme.colors.success} />
            </View>
            <Text style={styles.paymentMarkedTitle}>Payment Marked as Sent</Text>
            <Text style={styles.paymentMarkedText}>
              You've marked this payment as sent. The seller will be notified and should release the crypto once they confirm receipt.
            </Text>
            <TouchableOpacity 
              style={styles.paymentMarkedButton}
              onPress={() => setPaymentMarkedModalVisible(false)}
            >
              <Text style={styles.paymentMarkedButtonText}>Got it</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  menuButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  tradeCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 16,
    margin: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tradeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tradeType: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  tradeTypeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  buyText: {
    color: theme.colors.success,
  },
  sellText: {
    color: theme.colors.error,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tradeDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencyIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  timeRemainingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
  },
  timeRemainingText: {
    fontSize: 14,
    color: theme.colors.warning,
    marginLeft: 8,
  },
  counterpartySection: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  counterpartyInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  counterpartyLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  counterpartyDetails: {
    marginLeft: 12,
  },
  counterpartyName: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 4,
  },
  counterpartyStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tradeCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginLeft: 4,
  },
  viewProfileButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  viewProfileText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  paymentSection: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 16,
    marginBottom: 16,
  },
  paymentDetails: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    flexDirection: 'row',
  },
  paymentDetailsText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  copyButton: {
    padding: 4,
    alignSelf: 'flex-start',
  },
  markAsPaidButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  markAsPaidButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  releaseEscrowButton: {
    backgroundColor: theme.colors.success,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  releaseEscrowButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: theme.colors.error,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.error,
  },
  chatSection: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 16,
    margin: 16,
    marginTop: 0,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  viewFullChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewFullChatText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  messagesContainer: {
    marginBottom: 16,
  },
  messageItem: {
    marginBottom: 12,
    maxWidth: '80%',
  },
  sentMessage: {
    alignSelf: 'flex-end',
  },
  receivedMessage: {
    alignSelf: 'flex-start',
  },
  systemMessage: {
    alignSelf: 'center',
    maxWidth: '90%',
  },
  messageContent: {
    backgroundColor: theme.colors.primary,
    borderRadius: 16,
    borderBottomRightRadius: 4,
    padding: 12,
  },
  systemMessageContent: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 12,
    alignItems: 'center',
  },
  messageText: {
    fontSize: 14,
    color: 'white',
    marginBottom: 4,
  },
  systemMessageText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    flex: 1,
  },
  messageTime: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    alignSelf: 'flex-end',
  },
  messageInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  messageInput: {
    flex: 1,
    fontSize: 14,
    maxHeight: 100,
    color: theme.colors.text,
  },
  sendButton: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalScrollView: {
    padding: 16,
  },
  modalText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  disputeInput: {
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: theme.colors.inputText,
    height: 120,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
  },
  warningContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.warning,
    marginLeft: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  cancelDisputeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    marginRight: 8,
  },
  cancelDisputeButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  raiseDisputeButton: {
    flex: 2,
    backgroundColor: theme.colors.error,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  disabledButton: {
    backgroundColor: theme.colors.errorLight,
  },
  raiseDisputeButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  paymentMarkedOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentMarkedContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 24,
    width: '80%',
    alignItems: 'center',
  },
  paymentMarkedIcon: {
    marginBottom: 16,
  },
  paymentMarkedTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  paymentMarkedText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  paymentMarkedButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  paymentMarkedButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});