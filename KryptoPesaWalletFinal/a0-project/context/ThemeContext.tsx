import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Appearance, StatusBar } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system';
export type ColorScheme = 'light' | 'dark';

// Color palette interface
interface Colors {
  // Background colors
  background: string;
  backgroundSecondary: string;
  surface: string;
  card: string;
  modal: string;
  overlay: string;
  
  // Text colors
  text: string;
  textSecondary: string;
  textTertiary: string;
  textInverse: string;
  
  // Border colors
  border: string;
  borderLight: string;
  divider: string;
  
  // Brand colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  
  // Status colors
  success: string;
  successLight: string;
  warning: string;
  warningLight: string;
  error: string;
  errorLight: string;
  info: string;
  infoLight: string;
  
  // Trade status colors
  pending: string;
  paymentSent: string;
  escrowPending: string;
  completed: string;
  disputed: string;
  
  // Chat colors
  chatBubbleSent: string;
  chatBubbleReceived: string;
  chatBubbleSystem: string;
  chatTextSent: string;
  chatTextReceived: string;
  chatTextSystem: string;
  
  // Navigation colors
  tabBarBackground: string;
  tabBarActive: string;
  tabBarInactive: string;
  headerBackground: string;
  headerText: string;
  
  // Input colors
  inputBackground: string;
  inputBorder: string;
  inputText: string;
  inputPlaceholder: string;
  
  // Shadow colors
  shadow: string;
  shadowLight: string;
}

// Light theme colors
const lightColors: Colors = {
  // Background colors
  background: '#f9fafb',
  backgroundSecondary: '#f3f4f6',
  surface: '#ffffff',
  card: '#ffffff',
  modal: '#ffffff',
  overlay: 'rgba(0, 0, 0, 0.5)',
  
  // Text colors
  text: '#1f2937',
  textSecondary: '#6b7280',
  textTertiary: '#9ca3af',
  textInverse: '#ffffff',
  
  // Border colors
  border: '#e5e7eb',
  borderLight: '#f3f4f6',
  divider: '#e5e7eb',
  
  // Brand colors
  primary: '#3b82f6',
  primaryLight: '#dbeafe',
  primaryDark: '#1d4ed8',
  
  // Status colors
  success: '#10b981',
  successLight: '#d1fae5',
  warning: '#f59e0b',
  warningLight: '#fef3c7',
  error: '#ef4444',
  errorLight: '#fee2e2',
  info: '#3b82f6',
  infoLight: '#dbeafe',
  
  // Trade status colors
  pending: '#f59e0b',
  paymentSent: '#3b82f6',
  escrowPending: '#8b5cf6',
  completed: '#10b981',
  disputed: '#ef4444',
  
  // Chat colors
  chatBubbleSent: '#3b82f6',
  chatBubbleReceived: '#ffffff',
  chatBubbleSystem: '#f3f4f6',
  chatTextSent: '#ffffff',
  chatTextReceived: '#1f2937',
  chatTextSystem: '#6b7280',
  
  // Navigation colors
  tabBarBackground: '#ffffff',
  tabBarActive: '#3b82f6',
  tabBarInactive: '#9ca3af',
  headerBackground: '#ffffff',
  headerText: '#1f2937',
  
  // Input colors
  inputBackground: '#ffffff',
  inputBorder: '#e5e7eb',
  inputText: '#1f2937',
  inputPlaceholder: '#9ca3af',
  
  // Shadow colors
  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.1)',
};

// Dark theme colors
const darkColors: Colors = {
  // Background colors
  background: '#111827',
  backgroundSecondary: '#374151',
  surface: '#1f2937',
  card: '#374151',
  modal: '#1f2937',
  overlay: 'rgba(0, 0, 0, 0.7)',
  
  // Text colors
  text: '#f9fafb',
  textSecondary: '#d1d5db',
  textTertiary: '#9ca3af',
  textInverse: '#1f2937',
  
  // Border colors
  border: '#4b5563',
  borderLight: '#374151',
  divider: '#4b5563',
  
  // Brand colors
  primary: '#60a5fa',
  primaryLight: '#1e3a8a',
  primaryDark: '#3b82f6',
  
  // Status colors
  success: '#34d399',
  successLight: '#064e3b',
  warning: '#fbbf24',
  warningLight: '#451a03',
  error: '#f87171',
  errorLight: '#7f1d1d',
  info: '#60a5fa',
  infoLight: '#1e3a8a',
  
  // Trade status colors
  pending: '#fbbf24',
  paymentSent: '#60a5fa',
  escrowPending: '#a78bfa',
  completed: '#34d399',
  disputed: '#f87171',
  
  // Chat colors
  chatBubbleSent: '#3b82f6',
  chatBubbleReceived: '#374151',
  chatBubbleSystem: '#1f2937',
  chatTextSent: '#ffffff',
  chatTextReceived: '#f9fafb',
  chatTextSystem: '#d1d5db',
  
  // Navigation colors
  tabBarBackground: '#1f2937',
  tabBarActive: '#60a5fa',
  tabBarInactive: '#9ca3af',
  headerBackground: '#1f2937',
  headerText: '#f9fafb',
  
  // Input colors
  inputBackground: '#374151',
  inputBorder: '#4b5563',
  inputText: '#f9fafb',
  inputPlaceholder: '#9ca3af',
  
  // Shadow colors
  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.3)',
};

// Theme interface
interface Theme {
  colors: Colors;
  isDark: boolean;
}

// Theme context interface
interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  colorScheme: ColorScheme;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

// Create theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme storage key
const THEME_STORAGE_KEY = '@cryptop2p_theme_mode';

// Theme provider props
interface ThemeProviderProps {
  children: ReactNode;
}

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [themeMode, setThemeModeState] = useState<ThemeMode>('system');
  const [colorScheme, setColorScheme] = useState<ColorScheme>('light');

  // Get current theme based on mode and color scheme
  const getCurrentTheme = (scheme: ColorScheme): Theme => ({
    colors: scheme === 'dark' ? darkColors : lightColors,
    isDark: scheme === 'dark',
  });

  const theme = getCurrentTheme(colorScheme);

  // Load theme preference from storage
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedThemeMode = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedThemeMode && ['light', 'dark', 'system'].includes(savedThemeMode)) {
          setThemeModeState(savedThemeMode as ThemeMode);
        }
      } catch (error) {
        console.warn('Failed to load theme preference:', error);
      }
    };

    loadThemePreference();
  }, []);

  // Handle system theme changes
  useEffect(() => {
    const updateColorScheme = () => {
      if (themeMode === 'system') {
        const systemColorScheme = Appearance.getColorScheme() || 'light';
        setColorScheme(systemColorScheme);
      } else {
        setColorScheme(themeMode as ColorScheme);
      }
    };

    updateColorScheme();

    // Listen for system theme changes
    const subscription = Appearance.addChangeListener(updateColorScheme);
    return () => subscription?.remove();
  }, [themeMode]);

  // Update status bar style based on theme
  useEffect(() => {
    StatusBar.setBarStyle(colorScheme === 'dark' ? 'light-content' : 'dark-content');
  }, [colorScheme]);

  // Set theme mode with persistence
  const setThemeMode = async (mode: ThemeMode) => {
    try {
      setThemeModeState(mode);
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  };

  // Toggle between light and dark (ignoring system)
  const toggleTheme = () => {
    const newMode = colorScheme === 'dark' ? 'light' : 'dark';
    setThemeMode(newMode);
  };

  const contextValue: ThemeContextType = {
    theme,
    themeMode,
    colorScheme,
    setThemeMode,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Export theme objects for direct access if needed
export { lightColors, darkColors };
