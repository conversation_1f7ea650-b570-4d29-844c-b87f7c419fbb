const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');

/**
 * Connection Pool Manager
 * Manages WebSocket connections across multiple server instances for horizontal scaling
 */
class ConnectionPoolManager {
  constructor() {
    this.redisClient = null;
    this.localConnections = new Map(); // socketId -> connection info
    this.userConnections = new Map(); // userId -> Set of socketIds
    this.roomConnections = new Map(); // roomId -> Set of socketIds
    this.connectionMetrics = {
      totalConnections: 0,
      activeConnections: 0,
      peakConnections: 0,
      connectionsPerSecond: 0,
      disconnectionsPerSecond: 0,
      averageConnectionDuration: 0,
      lastReset: new Date()
    };
    this.serverId = process.env.SERVER_ID || `server_${Date.now()}`;
    this.heartbeatInterval = null;
    this.cleanupInterval = null;
  }

  /**
   * Initialize connection pool manager
   */
  async initialize() {
    try {
      this.redisClient = getRedisClient();
      
      if (this.redisClient) {
        // Subscribe to connection events from other servers
        await this.subscribeToConnectionEvents();
        
        // Start heartbeat for this server instance
        this.startHeartbeat();
        
        // Start cleanup process
        this.startCleanupProcess();
        
        logger.info(`Connection pool manager initialized for server ${this.serverId}`);
        return true;
      } else {
        logger.warn('Redis not available, running in single-server mode');
        return false;
      }
    } catch (error) {
      logger.error('Failed to initialize connection pool manager:', error);
      return false;
    }
  }

  /**
   * Register a new connection
   */
  async registerConnection(socketId, userId, connectionInfo = {}) {
    try {
      const connection = {
        socketId,
        userId,
        serverId: this.serverId,
        connectedAt: new Date(),
        lastActivity: new Date(),
        ...connectionInfo
      };

      // Store locally
      this.localConnections.set(socketId, connection);
      
      // Add to user connections
      if (!this.userConnections.has(userId)) {
        this.userConnections.set(userId, new Set());
      }
      this.userConnections.get(userId).add(socketId);

      // Store in Redis for cross-server visibility
      if (this.redisClient) {
        await this.redisClient.hset(
          'kryptopesa:connections',
          socketId,
          JSON.stringify(connection)
        );
        
        // Add to user's connection list
        await this.redisClient.sadd(`kryptopesa:user_connections:${userId}`, socketId);
        
        // Publish connection event
        await this.publishConnectionEvent('user_connected', {
          socketId,
          userId,
          serverId: this.serverId,
          timestamp: new Date()
        });
      }

      this.connectionMetrics.totalConnections++;
      this.connectionMetrics.activeConnections++;
      
      if (this.connectionMetrics.activeConnections > this.connectionMetrics.peakConnections) {
        this.connectionMetrics.peakConnections = this.connectionMetrics.activeConnections;
      }

      logger.debug(`Connection registered: ${socketId} for user ${userId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to register connection ${socketId}:`, error);
      return false;
    }
  }

  /**
   * Unregister a connection
   */
  async unregisterConnection(socketId) {
    try {
      const connection = this.localConnections.get(socketId);
      if (!connection) {
        return false;
      }

      const { userId } = connection;
      const connectionDuration = Date.now() - connection.connectedAt.getTime();

      // Remove from local storage
      this.localConnections.delete(socketId);
      
      // Remove from user connections
      if (this.userConnections.has(userId)) {
        this.userConnections.get(userId).delete(socketId);
        if (this.userConnections.get(userId).size === 0) {
          this.userConnections.delete(userId);
        }
      }

      // Remove from all rooms
      for (const [roomId, sockets] of this.roomConnections.entries()) {
        if (sockets.has(socketId)) {
          sockets.delete(socketId);
          if (sockets.size === 0) {
            this.roomConnections.delete(roomId);
          }
        }
      }

      // Remove from Redis
      if (this.redisClient) {
        await this.redisClient.hdel('kryptopesa:connections', socketId);
        await this.redisClient.srem(`kryptopesa:user_connections:${userId}`, socketId);
        
        // Publish disconnection event
        await this.publishConnectionEvent('user_disconnected', {
          socketId,
          userId,
          serverId: this.serverId,
          connectionDuration,
          timestamp: new Date()
        });
      }

      this.connectionMetrics.activeConnections--;
      this.updateAverageConnectionDuration(connectionDuration);

      logger.debug(`Connection unregistered: ${socketId} for user ${userId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to unregister connection ${socketId}:`, error);
      return false;
    }
  }

  /**
   * Join room
   */
  async joinRoom(socketId, roomId) {
    try {
      const connection = this.localConnections.get(socketId);
      if (!connection) {
        return false;
      }

      // Add to local room tracking
      if (!this.roomConnections.has(roomId)) {
        this.roomConnections.set(roomId, new Set());
      }
      this.roomConnections.get(roomId).add(socketId);

      // Store in Redis
      if (this.redisClient) {
        await this.redisClient.sadd(`kryptopesa:room_connections:${roomId}`, socketId);
        
        // Publish room join event
        await this.publishConnectionEvent('room_joined', {
          socketId,
          roomId,
          userId: connection.userId,
          serverId: this.serverId,
          timestamp: new Date()
        });
      }

      logger.debug(`Socket ${socketId} joined room ${roomId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to join room ${roomId} for socket ${socketId}:`, error);
      return false;
    }
  }

  /**
   * Leave room
   */
  async leaveRoom(socketId, roomId) {
    try {
      const connection = this.localConnections.get(socketId);
      if (!connection) {
        return false;
      }

      // Remove from local room tracking
      if (this.roomConnections.has(roomId)) {
        this.roomConnections.get(roomId).delete(socketId);
        if (this.roomConnections.get(roomId).size === 0) {
          this.roomConnections.delete(roomId);
        }
      }

      // Remove from Redis
      if (this.redisClient) {
        await this.redisClient.srem(`kryptopesa:room_connections:${roomId}`, socketId);
        
        // Publish room leave event
        await this.publishConnectionEvent('room_left', {
          socketId,
          roomId,
          userId: connection.userId,
          serverId: this.serverId,
          timestamp: new Date()
        });
      }

      logger.debug(`Socket ${socketId} left room ${roomId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to leave room ${roomId} for socket ${socketId}:`, error);
      return false;
    }
  }

  /**
   * Update connection activity
   */
  async updateActivity(socketId, activity = {}) {
    try {
      const connection = this.localConnections.get(socketId);
      if (!connection) {
        return false;
      }

      connection.lastActivity = new Date();
      connection.activity = { ...connection.activity, ...activity };

      // Update in Redis
      if (this.redisClient) {
        await this.redisClient.hset(
          'kryptopesa:connections',
          socketId,
          JSON.stringify(connection)
        );
      }

      return true;
    } catch (error) {
      logger.error(`Failed to update activity for socket ${socketId}:`, error);
      return false;
    }
  }

  /**
   * Get user connections across all servers
   */
  async getUserConnections(userId) {
    try {
      if (!this.redisClient) {
        // Single server mode
        return Array.from(this.userConnections.get(userId) || []);
      }

      const socketIds = await this.redisClient.smembers(`kryptopesa:user_connections:${userId}`);
      const connections = [];

      for (const socketId of socketIds) {
        const connectionData = await this.redisClient.hget('kryptopesa:connections', socketId);
        if (connectionData) {
          connections.push(JSON.parse(connectionData));
        }
      }

      return connections;
    } catch (error) {
      logger.error(`Failed to get connections for user ${userId}:`, error);
      return [];
    }
  }

  /**
   * Get room connections across all servers
   */
  async getRoomConnections(roomId) {
    try {
      if (!this.redisClient) {
        // Single server mode
        return Array.from(this.roomConnections.get(roomId) || []);
      }

      const socketIds = await this.redisClient.smembers(`kryptopesa:room_connections:${roomId}`);
      const connections = [];

      for (const socketId of socketIds) {
        const connectionData = await this.redisClient.hget('kryptopesa:connections', socketId);
        if (connectionData) {
          connections.push(JSON.parse(connectionData));
        }
      }

      return connections;
    } catch (error) {
      logger.error(`Failed to get connections for room ${roomId}:`, error);
      return [];
    }
  }

  /**
   * Check if user is online
   */
  async isUserOnline(userId) {
    try {
      if (!this.redisClient) {
        return this.userConnections.has(userId);
      }

      const connections = await this.redisClient.smembers(`kryptopesa:user_connections:${userId}`);
      return connections.length > 0;
    } catch (error) {
      logger.error(`Failed to check if user ${userId} is online:`, error);
      return false;
    }
  }

  /**
   * Get connection by socket ID
   */
  getConnection(socketId) {
    return this.localConnections.get(socketId);
  }

  /**
   * Get all local connections
   */
  getLocalConnections() {
    return Array.from(this.localConnections.values());
  }

  /**
   * Subscribe to connection events from other servers
   */
  async subscribeToConnectionEvents() {
    try {
      await this.redisClient.subscribe('kryptopesa:connection_events', (message) => {
        this.handleConnectionEvent(JSON.parse(message));
      });
      
      logger.info('Subscribed to connection events');
    } catch (error) {
      logger.error('Failed to subscribe to connection events:', error);
    }
  }

  /**
   * Publish connection event
   */
  async publishConnectionEvent(event, data) {
    try {
      if (!this.redisClient) return;

      const message = JSON.stringify({
        event,
        data,
        serverId: this.serverId,
        timestamp: new Date()
      });

      await this.redisClient.publish('kryptopesa:connection_events', message);
    } catch (error) {
      logger.error('Failed to publish connection event:', error);
    }
  }

  /**
   * Handle connection event from other servers
   */
  handleConnectionEvent(eventData) {
    const { event, data, serverId } = eventData;
    
    // Skip events from same server
    if (serverId === this.serverId) {
      return;
    }

    logger.debug(`Received connection event from ${serverId}: ${event}`, data);
    
    // Handle different event types if needed
    switch (event) {
      case 'user_connected':
        // Update global metrics or trigger actions
        break;
      case 'user_disconnected':
        // Handle user disconnection from other server
        break;
      case 'room_joined':
        // Handle room join from other server
        break;
      case 'room_left':
        // Handle room leave from other server
        break;
    }
  }

  /**
   * Start heartbeat process
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(async () => {
      try {
        if (this.redisClient) {
          await this.redisClient.setex(
            `kryptopesa:server_heartbeat:${this.serverId}`,
            60, // 1 minute TTL
            JSON.stringify({
              serverId: this.serverId,
              activeConnections: this.connectionMetrics.activeConnections,
              timestamp: new Date()
            })
          );
        }
      } catch (error) {
        logger.error('Failed to send heartbeat:', error);
      }
    }, 30000); // Every 30 seconds
  }

  /**
   * Start cleanup process
   */
  startCleanupProcess() {
    this.cleanupInterval = setInterval(async () => {
      await this.cleanupStaleConnections();
      this.calculateMetrics();
    }, 60000); // Every minute
  }

  /**
   * Cleanup stale connections
   */
  async cleanupStaleConnections() {
    try {
      const now = new Date();
      const staleThreshold = 5 * 60 * 1000; // 5 minutes

      for (const [socketId, connection] of this.localConnections.entries()) {
        if (now - connection.lastActivity > staleThreshold) {
          logger.warn(`Cleaning up stale connection: ${socketId}`);
          await this.unregisterConnection(socketId);
        }
      }
    } catch (error) {
      logger.error('Failed to cleanup stale connections:', error);
    }
  }

  /**
   * Calculate metrics
   */
  calculateMetrics() {
    const now = new Date();
    const timeDiff = (now - this.connectionMetrics.lastReset) / 1000;

    // Reset counters every 5 minutes
    if (timeDiff >= 300) {
      this.connectionMetrics.connectionsPerSecond = 0;
      this.connectionMetrics.disconnectionsPerSecond = 0;
      this.connectionMetrics.lastReset = now;
    }
  }

  /**
   * Update average connection duration
   */
  updateAverageConnectionDuration(duration) {
    this.connectionMetrics.averageConnectionDuration = 
      (this.connectionMetrics.averageConnectionDuration + duration) / 2;
  }

  /**
   * Get metrics
   */
  getMetrics() {
    return {
      ...this.connectionMetrics,
      localConnections: this.localConnections.size,
      userConnections: this.userConnections.size,
      roomConnections: this.roomConnections.size,
      serverId: this.serverId
    };
  }

  /**
   * Shutdown connection pool manager
   */
  async shutdown() {
    try {
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
      }
      
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      // Unregister all local connections
      for (const socketId of this.localConnections.keys()) {
        await this.unregisterConnection(socketId);
      }

      // Remove server heartbeat
      if (this.redisClient) {
        await this.redisClient.del(`kryptopesa:server_heartbeat:${this.serverId}`);
        await this.redisClient.unsubscribe('kryptopesa:connection_events');
      }

      logger.info('Connection pool manager shutdown complete');
    } catch (error) {
      logger.error('Error during connection pool manager shutdown:', error);
    }
  }
}

// Create singleton instance
const connectionPoolManager = new ConnectionPoolManager();

module.exports = connectionPoolManager;
