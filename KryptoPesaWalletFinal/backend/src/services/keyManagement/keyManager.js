const logger = require('../../utils/logger');
const { AppError } = require('../../middleware/errorHandler');
const keyManagementService = require('./index');

/**
 * Key Manager - Simplified interface for transaction services
 * Provides secure private key retrieval for blockchain transactions
 */
class KeyManager {
  constructor() {
    this.keyManagement = keyManagementService;
  }

  /**
   * Get private key for a specific wallet and network
   * @param {string} walletId - Wallet ID
   * @param {string} network - Network (bitcoin, ethereum, polygon)
   * @returns {Promise<string>} Private key in hex format
   */
  async getPrivateKey(walletId, network) {
    try {
      logger.info(`🔑 [KEY MANAGER] Retrieving private key for wallet ${walletId} on ${network}`);

      // For development/testing, use a deterministic key generation
      // In production, this would retrieve from secure storage
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        return this.generateDevelopmentKey(walletId, network);
      }

      // Production: Retrieve from secure key management service
      const encryptedKey = await this.keyManagement.getEncryptedKey(network, {
        walletId,
        purpose: 'transaction_signing'
      });

      if (!encryptedKey) {
        throw new AppError(`Private key not found for wallet ${walletId} on ${network}`, 404);
      }

      const privateKey = await this.keyManagement.decryptKey(encryptedKey, network);
      
      logger.info(`✅ [KEY MANAGER] Private key retrieved successfully for ${network}`);
      return privateKey;

    } catch (error) {
      logger.error(`❌ [KEY MANAGER] Failed to retrieve private key: ${error.message}`);
      throw new AppError(`Failed to retrieve private key: ${error.message}`, 500);
    }
  }

  /**
   * Generate deterministic private key for development
   * @param {string} walletId - Wallet ID
   * @param {string} network - Network
   * @returns {string} Private key in hex format
   */
  generateDevelopmentKey(walletId, network) {
    const crypto = require('crypto');
    
    // Create deterministic seed from wallet ID and network
    const seed = `${walletId}_${network}_${process.env.JWT_SECRET || 'dev_secret'}`;
    const hash = crypto.createHash('sha256').update(seed).digest('hex');
    
    // Ensure the key is valid for the network
    if (network === 'bitcoin') {
      // Bitcoin private key (32 bytes)
      return hash;
    } else {
      // Ethereum/Polygon private key (32 bytes, must be < secp256k1 curve order)
      const key = hash;
      // Ensure it's a valid private key (less than curve order)
      const keyBN = BigInt('0x' + key);
      const curveOrder = BigInt('0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141');
      
      if (keyBN >= curveOrder) {
        // If key is too large, hash it again
        return crypto.createHash('sha256').update(key + 'retry').digest('hex');
      }
      
      return key;
    }
  }

  /**
   * Store private key securely
   * @param {string} walletId - Wallet ID
   * @param {string} network - Network
   * @param {string} privateKey - Private key to store
   * @returns {Promise<boolean>} Success status
   */
  async storePrivateKey(walletId, network, privateKey) {
    try {
      logger.info(`🔑 [KEY MANAGER] Storing private key for wallet ${walletId} on ${network}`);

      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        // In development, we don't actually store keys (they're generated deterministically)
        logger.info(`✅ [KEY MANAGER] Development mode - private key stored (simulated)`);
        return true;
      }

      // Production: Store in secure key management service
      await this.keyManagement.encryptAndStorePrivateKey(network, privateKey, {
        walletId,
        timestamp: new Date().toISOString()
      });

      logger.info(`✅ [KEY MANAGER] Private key stored successfully for ${network}`);
      return true;

    } catch (error) {
      logger.error(`❌ [KEY MANAGER] Failed to store private key: ${error.message}`);
      throw new AppError(`Failed to store private key: ${error.message}`, 500);
    }
  }

  /**
   * Check if private key exists for wallet and network
   * @param {string} walletId - Wallet ID
   * @param {string} network - Network
   * @returns {Promise<boolean>} Whether key exists
   */
  async hasPrivateKey(walletId, network) {
    try {
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        // In development, keys are always available (generated on demand)
        return true;
      }

      // Production: Check if key exists in secure storage
      const encryptedKey = await this.keyManagement.getEncryptedKey(network, {
        walletId,
        purpose: 'existence_check'
      });

      return !!encryptedKey;

    } catch (error) {
      logger.error(`❌ [KEY MANAGER] Failed to check key existence: ${error.message}`);
      return false;
    }
  }

  /**
   * Rotate private key for security
   * @param {string} walletId - Wallet ID
   * @param {string} network - Network
   * @returns {Promise<string>} New private key
   */
  async rotatePrivateKey(walletId, network) {
    try {
      logger.info(`🔄 [KEY MANAGER] Rotating private key for wallet ${walletId} on ${network}`);

      // Generate new private key
      const crypto = require('crypto');
      const newPrivateKey = crypto.randomBytes(32).toString('hex');

      // Store the new key
      await this.storePrivateKey(walletId, network, newPrivateKey);

      logger.info(`✅ [KEY MANAGER] Private key rotated successfully for ${network}`);
      return newPrivateKey;

    } catch (error) {
      logger.error(`❌ [KEY MANAGER] Failed to rotate private key: ${error.message}`);
      throw new AppError(`Failed to rotate private key: ${error.message}`, 500);
    }
  }
}

// Export singleton instance
module.exports = new KeyManager();
