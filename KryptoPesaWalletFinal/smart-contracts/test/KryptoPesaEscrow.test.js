const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("KryptoPesaEscrow", function () {
  let escrow, token, owner, seller, buyer, feeCollector, disputeResolver;
  let tradeId;

  beforeEach(async function () {
    [owner, seller, buyer, feeCollector, disputeResolver] = await ethers.getSigners();

    // Deploy mock ERC20 token
    const MockToken = await ethers.getContractFactory("MockERC20");
    token = await MockToken.deploy("Test USDT", "TUSDT", 6);
    await token.waitForDeployment();

    // Deploy escrow contract
    const KryptoPesaEscrow = await ethers.getContractFactory("KryptoPesaEscrow");
    escrow = await KryptoPesaEscrow.deploy(feeCollector.address, disputeResolver.address);
    await escrow.waitForDeployment();

    // Authorize the test token
    await escrow.authorizeToken(await token.getAddress(), true);

    // Mint tokens to seller
    await token.mint(seller.address, ethers.parseUnits("1000", 6));

    // Approve escrow to spend seller's tokens
    await token.connect(seller).approve(await escrow.getAddress(), ethers.parseUnits("1000", 6));
  });

  describe("Trade Creation", function () {
    it("Should create a trade successfully", async function () {
      const amount = ethers.parseUnits("100", 6);
      const fiatAmount = 10000; // 100 USD in cents

      const tx = await escrow.connect(seller).createTrade(
        buyer.address,
        await token.getAddress(),
        amount,
        fiatAmount,
        "USD",
        "Bank Transfer",
        ethers.keccak256(ethers.toUtf8Bytes("payment-details"))
      );

      const receipt = await tx.wait();
      const event = receipt.logs.find(log => {
        try {
          const parsed = escrow.interface.parseLog(log);
          return parsed.name === "TradeCreated";
        } catch {
          return false;
        }
      });
      const parsedEvent = escrow.interface.parseLog(event);
      tradeId = parsedEvent.args.tradeId;

      expect(tradeId).to.equal(1);
      
      const trade = await escrow.getTrade(tradeId);
      expect(trade.seller).to.equal(seller.address);
      expect(trade.buyer).to.equal(buyer.address);
      expect(trade.amount).to.equal(amount);
      expect(trade.status).to.equal(0); // CREATED
    });

    it("Should not allow creating trade with unauthorized token", async function () {
      const unauthorizedToken = await (await ethers.getContractFactory("MockERC20")).deploy("Unauthorized", "UNAUTH", 18);
      
      await expect(
        escrow.connect(seller).createTrade(
          buyer.address,
          await unauthorizedToken.getAddress(),
          ethers.parseUnits("100", 18),
          10000,
          "USD",
          "Bank Transfer",
          ethers.keccak256(ethers.toUtf8Bytes("payment-details"))
        )
      ).to.be.revertedWith("Token not authorized");
    });
  });

  describe("Trade Funding", function () {
    beforeEach(async function () {
      const amount = ethers.parseUnits("100", 6);
      await escrow.connect(seller).createTrade(
        buyer.address,
        await token.getAddress(),
        amount,
        10000,
        "USD",
        "Bank Transfer",
        ethers.keccak256(ethers.toUtf8Bytes("payment-details"))
      );
      tradeId = 1;
    });

    it("Should fund trade successfully", async function () {
      const amount = ethers.parseUnits("100", 6);

      await expect(escrow.connect(seller).fundTrade(tradeId))
        .to.emit(escrow, "TradeFunded")
        .withArgs(tradeId, amount);

      const trade = await escrow.getTrade(tradeId);
      expect(trade.status).to.equal(1); // FUNDED

      // Check token balance
      expect(await token.balanceOf(await escrow.getAddress())).to.equal(amount);
    });

    it("Should not allow non-seller to fund", async function () {
      await expect(escrow.connect(buyer).fundTrade(tradeId))
        .to.be.revertedWith("Only seller can fund");
    });
  });

  describe("Payment Confirmation", function () {
    beforeEach(async function () {
      const amount = ethers.parseUnits("100", 6);
      await escrow.connect(seller).createTrade(
        buyer.address,
        await token.getAddress(),
        amount,
        10000,
        "USD",
        "Bank Transfer",
        ethers.keccak256(ethers.toUtf8Bytes("payment-details"))
      );
      tradeId = 1;
      await escrow.connect(seller).fundTrade(tradeId);
    });

    it("Should allow buyer to confirm payment sent", async function () {
      await expect(escrow.connect(buyer).confirmPaymentSent(tradeId))
        .to.emit(escrow, "PaymentSent")
        .withArgs(tradeId, buyer.address);

      const trade = await escrow.getTrade(tradeId);
      expect(trade.status).to.equal(2); // PAYMENT_SENT
      expect(trade.buyerConfirmed).to.be.true;
    });

    it("Should complete trade when seller confirms payment received", async function () {
      await escrow.connect(buyer).confirmPaymentSent(tradeId);

      const amount = ethers.parseUnits("100", 6);
      const commission = amount * 50n / 10000n; // 0.5%
      const sellerAmount = amount - commission;

      await expect(escrow.connect(seller).confirmPaymentReceived(tradeId))
        .to.emit(escrow, "TradeCompleted")
        .withArgs(tradeId, sellerAmount, commission);

      const trade = await escrow.getTrade(tradeId);
      expect(trade.status).to.equal(3); // COMPLETED

      // Check balances
      expect(await token.balanceOf(buyer.address)).to.equal(sellerAmount);
      expect(await token.balanceOf(feeCollector.address)).to.equal(commission);

      // Check reputation
      const [sellerRep, sellerTrades] = await escrow.getUserReputation(seller.address);
      const [buyerRep, buyerTrades] = await escrow.getUserReputation(buyer.address);
      expect(sellerRep).to.equal(1);
      expect(buyerRep).to.equal(1);
      expect(sellerTrades).to.equal(1);
      expect(buyerTrades).to.equal(1);
    });
  });

  describe("Disputes", function () {
    beforeEach(async function () {
      const amount = ethers.parseUnits("100", 6);
      await escrow.connect(seller).createTrade(
        buyer.address,
        await token.getAddress(),
        amount,
        10000,
        "USD",
        "Bank Transfer",
        ethers.keccak256(ethers.toUtf8Bytes("payment-details"))
      );
      tradeId = 1;
      await escrow.connect(seller).fundTrade(tradeId);
      await escrow.connect(buyer).confirmPaymentSent(tradeId);
    });

    it("Should create dispute successfully", async function () {
      const reason = "Payment not received";
      
      await expect(escrow.connect(seller).createDispute(tradeId, reason))
        .to.emit(escrow, "DisputeCreated")
        .withArgs(tradeId, seller.address, reason);

      const trade = await escrow.getTrade(tradeId);
      expect(trade.status).to.equal(4); // DISPUTED

      const dispute = await escrow.getDispute(tradeId);
      expect(dispute.initiator).to.equal(seller.address);
      expect(dispute.reason).to.equal(reason);
      expect(dispute.resolved).to.be.false;
    });

    it("Should resolve dispute in favor of buyer", async function () {
      await escrow.connect(seller).createDispute(tradeId, "Payment not received");

      const amount = ethers.parseUnits("100", 6);
      const commission = amount * 50n / 10000n;
      const buyerAmount = amount - commission;

      await expect(escrow.connect(disputeResolver).resolveDispute(tradeId, buyer.address))
        .to.emit(escrow, "DisputeResolved")
        .withArgs(tradeId, buyer.address);

      expect(await token.balanceOf(buyer.address)).to.equal(buyerAmount);
      expect(await token.balanceOf(feeCollector.address)).to.equal(commission);
    });
  });
});
