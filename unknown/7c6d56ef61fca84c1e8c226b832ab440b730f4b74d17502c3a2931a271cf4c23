const logger = require('./logger');

/**
 * Validate required environment variables
 */
const validateEnvironment = () => {
  const requiredVars = [
    'NODE_ENV',
    'PORT',
    'MONGODB_URI',
    'REDIS_URL',
    'JWT_SECRET',
    'ENCRYPTION_KEY',
    'SESSION_SECRET'
  ];

  // Additional required vars for production
  if (process.env.NODE_ENV === 'production') {
    requiredVars.push(
      'DB_PASSWORD',
      'REDIS_PASSWORD',
      'CORS_ORIGINS',
      'API_BASE_URL',
      'WS_URL'
    );
  }

  const missingVars = [];
  const warnings = [];

  // Check required variables
  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });

  // Check JWT secret strength
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
    warnings.push('JWT_SECRET should be at least 32 characters long for security');
  }

  // Check if using default/weak values
  const weakDefaults = {
    JWT_SECRET: ['secret', 'jwt-secret', 'your-secret-key', 'CHANGE_ME_IN_PRODUCTION_MINIMUM_32_CHARACTERS'],
    ENCRYPTION_KEY: ['change-me', 'test-key', 'CHANGE_ME_IN_PRODUCTION_MINIMUM_32_CHARACTERS'],
    SESSION_SECRET: ['change-me', 'test-secret', 'CHANGE_ME_IN_PRODUCTION_MINIMUM_32_CHARACTERS'],
    MONGODB_URI: ['mongodb://localhost:27017/kryptopesa'],
    REDIS_URL: ['redis://localhost:6379'],
    DB_PASSWORD: ['CHANGE_ME_IN_PRODUCTION'],
    REDIS_PASSWORD: ['CHANGE_ME_IN_PRODUCTION']
  };

  Object.entries(weakDefaults).forEach(([varName, weakValues]) => {
    if (process.env[varName] && weakValues.includes(process.env[varName])) {
      warnings.push(`${varName} appears to be using a default/weak value`);
    }
  });

  // Check production-specific requirements
  if (process.env.NODE_ENV === 'production') {
    const productionRequired = [
      'AWS_KMS_KEY_ID',
      'AWS_REGION',
      'SENTRY_DSN',
      'FIREBASE_PROJECT_ID',
      'FIREBASE_PRIVATE_KEY',
      'FIREBASE_CLIENT_EMAIL',
    ];

    productionRequired.forEach(varName => {
      if (!process.env[varName]) {
        warnings.push(`${varName} is recommended for production deployment`);
      }
    });

    // Check for development URLs in production
    const devPatterns = ['localhost', '127.0.0.1', 'dev', 'test'];
    const urlVars = ['MONGODB_URI', 'REDIS_URL', 'POLYGON_RPC_URL', 'ETHEREUM_RPC_URL'];
    
    urlVars.forEach(varName => {
      if (process.env[varName]) {
        const hasDevPattern = devPatterns.some(pattern => 
          process.env[varName].toLowerCase().includes(pattern)
        );
        if (hasDevPattern) {
          warnings.push(`${varName} appears to contain development URL in production`);
        }
      }
    });
  }

  // Report results
  if (missingVars.length > 0) {
    logger.error('Missing required environment variables:', missingVars);
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  if (warnings.length > 0) {
    warnings.forEach(warning => {
      logger.warn(`Environment warning: ${warning}`);
    });
  }

  logger.info('Environment validation completed successfully');
  return true;
};

/**
 * Sanitize environment variables for logging
 */
const sanitizeEnvForLogging = () => {
  const sensitiveKeys = [
    'JWT_SECRET',
    'MONGODB_URI',
    'REDIS_URL',
    'POLYGON_PRIVATE_KEY',
    'ETHEREUM_PRIVATE_KEY',
    'AWS_SECRET_ACCESS_KEY',
    'FIREBASE_PRIVATE_KEY',
    'SENTRY_DSN',
  ];

  const sanitized = {};
  
  Object.keys(process.env).forEach(key => {
    if (sensitiveKeys.some(sensitive => key.includes(sensitive))) {
      sanitized[key] = '[REDACTED]';
    } else {
      sanitized[key] = process.env[key];
    }
  });

  return sanitized;
};

/**
 * Get environment configuration summary
 */
const getEnvSummary = () => {
  return {
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT,
    hasDatabase: !!process.env.MONGODB_URI,
    hasRedis: !!process.env.REDIS_URL,
    hasBlockchain: !!(process.env.POLYGON_RPC_URL && process.env.ETHEREUM_RPC_URL),
    hasFirebase: !!(process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_PRIVATE_KEY),
    hasAWS: !!(process.env.AWS_REGION && process.env.AWS_KMS_KEY_ID),
    hasMonitoring: !!process.env.SENTRY_DSN,
  };
};

module.exports = {
  validateEnvironment,
  sanitizeEnvForLogging,
  getEnvSummary,
};
