"use strict";(self.webpackChunkkryptopesa_admin_dashboard=self.webpackChunkkryptopesa_admin_dashboard||[]).push([[209],{4642:(e,t,s)=>{s.d(t,{A:()=>d});var r=s(9662),n=s(579);const d=(0,r.A)((0,n.jsx)("path",{d:"M1 21h22L12 2zm12-3h-2v-2h2zm0-4h-2v-4h2z"}),"Warning")},7209:(e,t,s)=>{s.r(t),s.d(t,{default:()=>R});var r=s(5043),n=s(6446),d=s(5865),a=s(8903),i=s(2110),l=s(6494),c=s(3336),o=s(5795),h=s(1787),x=s(3193),u=s(9190),A=s(648),j=s(2143),m=s(1906),v=s(9650),p=s(1806),y=s(4882),g=s(8076),f=s(39),S=s(3460),C=s(3845),b=s(8483),w=s(7392),k=s(1574),D=s(35),B=s(6600),P=s(5316),T=s(9347),_=s(8606),z=s(9662),F=s(579);const I=(0,z.A)((0,F.jsx)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z"}),"Cancel");var U=s(4642),W=s(9484),L=s(3632),E=s(751),M=s(9722);const R=()=>{var e,t,s,z,R,O,V,q,G;const[H,J]=(0,r.useState)([]),[K,N]=(0,r.useState)(!0),[Q,X]=(0,r.useState)(null),[Y,Z]=(0,r.useState)(0),[$,ee]=(0,r.useState)(25),[te,se]=(0,r.useState)(0),[re,ne]=(0,r.useState)(""),[de,ae]=(0,r.useState)(""),[ie,le]=(0,r.useState)(null),[ce,oe]=(0,r.useState)(!1),he=async()=>{try{N(!0);const e={page:Y+1,limit:$,search:re||void 0,status:de||void 0,sortBy:"createdAt",sortOrder:"desc"},t=await M.A.get("/admin/trades",{params:e});t.data.success&&(J(t.data.data.trades),se(t.data.data.pagination.totalTrades))}catch(Q){console.error("Error fetching trades:",Q),X("Failed to fetch trades")}finally{N(!1)}};(0,r.useEffect)(()=>{he()},[Y,$,re,de]);const xe=async(e,t)=>{try{(await M.A.post("/admin/trades/".concat(e.tradeId,"/intervene"),{action:t,reason:"Admin ".concat(t," action")})).data.success&&he()}catch(Q){console.error("Error performing trade action:",Q)}},ue=e=>{switch(e){case"completed":return"success";case"cancelled":return"error";case"disputed":return"warning";case"active":return"info";case"funded":return"primary";default:return"default"}},Ae=e=>{switch(e){case"completed":return(0,F.jsx)(_.A,{});case"cancelled":return(0,F.jsx)(I,{});case"disputed":return(0,F.jsx)(U.A,{});default:return null}},je=(e,t)=>"".concat(parseFloat(e).toFixed(2)," ").concat(t),me=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,F.jsxs)(n.A,{children:[(0,F.jsx)(d.A,{variant:"h4",gutterBottom:!0,children:"Trade Management"}),(0,F.jsxs)(a.Ay,{container:!0,spacing:3,mb:3,children:[(0,F.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,F.jsx)(i.A,{children:(0,F.jsxs)(l.A,{children:[(0,F.jsx)(d.A,{color:"textSecondary",gutterBottom:!0,children:"Total Trades"}),(0,F.jsx)(d.A,{variant:"h4",children:te.toLocaleString()})]})})}),(0,F.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,F.jsx)(i.A,{children:(0,F.jsxs)(l.A,{children:[(0,F.jsx)(d.A,{color:"textSecondary",gutterBottom:!0,children:"Active Trades"}),(0,F.jsx)(d.A,{variant:"h4",children:H.filter(e=>["created","funded","payment_sent"].includes(e.status)).length})]})})}),(0,F.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,F.jsx)(i.A,{children:(0,F.jsxs)(l.A,{children:[(0,F.jsx)(d.A,{color:"textSecondary",gutterBottom:!0,children:"Completed Today"}),(0,F.jsx)(d.A,{variant:"h4",children:H.filter(e=>"completed"===e.status&&new Date(e.updatedAt).toDateString()===(new Date).toDateString()).length})]})})}),(0,F.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,F.jsx)(i.A,{children:(0,F.jsxs)(l.A,{children:[(0,F.jsx)(d.A,{color:"textSecondary",gutterBottom:!0,children:"Disputes"}),(0,F.jsx)(d.A,{variant:"h4",color:"warning.main",children:H.filter(e=>"disputed"===e.status).length})]})})})]}),(0,F.jsx)(c.A,{sx:{p:2,mb:3},children:(0,F.jsxs)(a.Ay,{container:!0,spacing:2,alignItems:"center",children:[(0,F.jsx)(a.Ay,{item:!0,xs:12,md:4,children:(0,F.jsx)(o.A,{fullWidth:!0,placeholder:"Search trades...",value:re,onChange:e=>ne(e.target.value),InputProps:{startAdornment:(0,F.jsx)(h.A,{position:"start",children:(0,F.jsx)(W.A,{})})}})}),(0,F.jsx)(a.Ay,{item:!0,xs:12,md:3,children:(0,F.jsxs)(x.A,{fullWidth:!0,children:[(0,F.jsx)(u.A,{children:"Status"}),(0,F.jsxs)(A.A,{value:de,onChange:e=>ae(e.target.value),label:"Status",children:[(0,F.jsx)(j.A,{value:"",children:"All Statuses"}),(0,F.jsx)(j.A,{value:"created",children:"Created"}),(0,F.jsx)(j.A,{value:"funded",children:"Funded"}),(0,F.jsx)(j.A,{value:"payment_sent",children:"Payment Sent"}),(0,F.jsx)(j.A,{value:"completed",children:"Completed"}),(0,F.jsx)(j.A,{value:"disputed",children:"Disputed"}),(0,F.jsx)(j.A,{value:"cancelled",children:"Cancelled"})]})]})}),(0,F.jsx)(a.Ay,{item:!0,xs:12,md:2,children:(0,F.jsx)(m.A,{variant:"outlined",onClick:()=>{ne(""),ae("")},children:"Clear Filters"})})]})}),(0,F.jsxs)(v.A,{component:c.A,children:[(0,F.jsxs)(p.A,{children:[(0,F.jsx)(y.A,{children:(0,F.jsxs)(g.A,{children:[(0,F.jsx)(f.A,{children:"Trade ID"}),(0,F.jsx)(f.A,{children:"Participants"}),(0,F.jsx)(f.A,{children:"Amount"}),(0,F.jsx)(f.A,{children:"Status"}),(0,F.jsx)(f.A,{children:"Created"}),(0,F.jsx)(f.A,{children:"Updated"}),(0,F.jsx)(f.A,{children:"Actions"})]})}),(0,F.jsx)(S.A,{children:H.map(e=>{var t,s,r,a,i,l;return(0,F.jsxs)(g.A,{hover:!0,children:[(0,F.jsx)(f.A,{children:(0,F.jsx)(d.A,{variant:"body2",fontFamily:"monospace",children:e.tradeId})}),(0,F.jsx)(f.A,{children:(0,F.jsxs)(n.A,{children:[(0,F.jsxs)(d.A,{variant:"body2",children:[(0,F.jsx)("strong",{children:"Seller:"})," ",(null===(t=e.seller)||void 0===t?void 0:t.username)||"Unknown"]}),(0,F.jsxs)(d.A,{variant:"body2",children:[(0,F.jsx)("strong",{children:"Buyer:"})," ",(null===(s=e.buyer)||void 0===s?void 0:s.username)||"Unknown"]})]})}),(0,F.jsx)(f.A,{children:(0,F.jsxs)(n.A,{children:[(0,F.jsx)(d.A,{variant:"body2",children:je(null===(r=e.cryptocurrency)||void 0===r?void 0:r.amount,null===(a=e.cryptocurrency)||void 0===a?void 0:a.symbol)}),(0,F.jsxs)(d.A,{variant:"caption",color:"textSecondary",children:["\u2248 ",je(null===(i=e.fiat)||void 0===i?void 0:i.amount,null===(l=e.fiat)||void 0===l?void 0:l.currency)]})]})}),(0,F.jsx)(f.A,{children:(0,F.jsx)(C.A,{icon:Ae(e.status),label:e.status.replace("_"," "),color:ue(e.status),size:"small"})}),(0,F.jsx)(f.A,{children:(0,F.jsx)(d.A,{variant:"body2",children:me(e.createdAt)})}),(0,F.jsx)(f.A,{children:(0,F.jsx)(d.A,{variant:"body2",children:me(e.updatedAt)})}),(0,F.jsx)(f.A,{children:(0,F.jsxs)(n.A,{display:"flex",gap:1,children:[(0,F.jsx)(b.A,{title:"View Details",children:(0,F.jsx)(w.A,{size:"small",onClick:()=>(e=>{le(e),oe(!0)})(e),children:(0,F.jsx)(L.A,{})})}),"disputed"===e.status&&(0,F.jsx)(b.A,{title:"Force Complete",children:(0,F.jsx)(w.A,{size:"small",color:"success",onClick:()=>xe(e,"force_complete"),children:(0,F.jsx)(_.A,{})})}),["created","funded","payment_sent"].includes(e.status)&&(0,F.jsx)(b.A,{title:"Cancel Trade",children:(0,F.jsx)(w.A,{size:"small",color:"error",onClick:()=>xe(e,"cancel"),children:(0,F.jsx)(E.A,{})})})]})})]},e._id)})})]}),(0,F.jsx)(k.A,{rowsPerPageOptions:[10,25,50,100],component:"div",count:te,rowsPerPage:$,page:Y,onPageChange:(e,t)=>{Z(t)},onRowsPerPageChange:e=>{ee(parseInt(e.target.value,10)),Z(0)}})]}),(0,F.jsxs)(D.A,{open:ce,onClose:()=>oe(!1),maxWidth:"md",fullWidth:!0,children:[(0,F.jsxs)(B.A,{children:["Trade Details - ",null===ie||void 0===ie?void 0:ie.tradeId]}),(0,F.jsx)(P.A,{children:ie&&(0,F.jsx)(n.A,{children:(0,F.jsxs)(a.Ay,{container:!0,spacing:2,children:[(0,F.jsxs)(a.Ay,{item:!0,xs:12,md:6,children:[(0,F.jsx)(d.A,{variant:"h6",gutterBottom:!0,children:"Trade Information"}),(0,F.jsxs)(d.A,{children:[(0,F.jsx)("strong",{children:"Status:"})," ",ie.status]}),(0,F.jsxs)(d.A,{children:[(0,F.jsx)("strong",{children:"Created:"})," ",me(ie.createdAt)]}),(0,F.jsxs)(d.A,{children:[(0,F.jsx)("strong",{children:"Updated:"})," ",me(ie.updatedAt)]})]}),(0,F.jsxs)(a.Ay,{item:!0,xs:12,md:6,children:[(0,F.jsx)(d.A,{variant:"h6",gutterBottom:!0,children:"Amount Details"}),(0,F.jsxs)(d.A,{children:[(0,F.jsx)("strong",{children:"Crypto:"})," ",je(null===(e=ie.cryptocurrency)||void 0===e?void 0:e.amount,null===(t=ie.cryptocurrency)||void 0===t?void 0:t.symbol)]}),(0,F.jsxs)(d.A,{children:[(0,F.jsx)("strong",{children:"Fiat:"})," ",je(null===(s=ie.fiat)||void 0===s?void 0:s.amount,null===(z=ie.fiat)||void 0===z?void 0:z.currency)]}),(0,F.jsxs)(d.A,{children:[(0,F.jsx)("strong",{children:"Rate:"})," ",null===(R=ie.fiat)||void 0===R?void 0:R.exchangeRate]})]}),(0,F.jsxs)(a.Ay,{item:!0,xs:12,children:[(0,F.jsx)(d.A,{variant:"h6",gutterBottom:!0,children:"Participants"}),(0,F.jsxs)(d.A,{children:[(0,F.jsx)("strong",{children:"Seller:"})," ",null===(O=ie.seller)||void 0===O?void 0:O.username," (",null===(V=ie.seller)||void 0===V?void 0:V.email,")"]}),(0,F.jsxs)(d.A,{children:[(0,F.jsx)("strong",{children:"Buyer:"})," ",null===(q=ie.buyer)||void 0===q?void 0:q.username," (",null===(G=ie.buyer)||void 0===G?void 0:G.email,")"]})]})]})})}),(0,F.jsx)(T.A,{children:(0,F.jsx)(m.A,{onClick:()=>oe(!1),children:"Close"})})]})]})}}}]);
//# sourceMappingURL=209.c7cd9cbd.chunk.js.map