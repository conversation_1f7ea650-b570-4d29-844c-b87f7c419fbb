# KryptoPesa Rate Limiting System

## Overview

KryptoPesa implements a comprehensive, production-ready rate limiting system designed to protect against abuse, DDoS attacks, and ensure fair resource usage across all API endpoints. The system uses Redis for distributed rate limiting and includes advanced features like intelligent threat detection, IP whitelisting/blacklisting, and comprehensive monitoring.

## Architecture

### Components

1. **Production Rate Limiting Middleware** (`productionRateLimit.js`)
   - Smart endpoint-based rate limiting
   - Redis-backed distributed limiting
   - Graceful fallback mechanisms

2. **Rate Limiting Service** (`rateLimitingService.js`)
   - Centralized rate limiting management
   - Metrics collection and monitoring
   - IP whitelist/blacklist management
   - Alert system for suspicious activity

3. **Advanced Rate Limiting** (`advancedRateLimit.js`)
   - Legacy system with progressive limiting
   - Enhanced security features
   - Threat detection capabilities

4. **Admin API** (`rateLimitAdmin.js`)
   - Administrative controls
   - Real-time monitoring
   - Emergency controls

## Rate Limiting Configuration

### Environment Variables

```bash
# Authentication Rate Limits
RATE_LIMIT_AUTH_MAX=5              # Max auth requests per 15 minutes
RATE_LIMIT_LOGIN_MAX=3             # Max login attempts per 15 minutes
RATE_LIMIT_PASSWORD_RESET_MAX=3    # Max password resets per hour

# Trading Rate Limits
RATE_LIMIT_TRADING_MAX=10          # Max trading requests per minute
RATE_LIMIT_TRADE_CREATION_MAX=5    # Max trade creation per 5 minutes

# Wallet Rate Limits
RATE_LIMIT_WALLET_MAX=20           # Max wallet requests per 5 minutes
RATE_LIMIT_WALLET_TX_MAX=5         # Max wallet transactions per 10 minutes

# Chat Rate Limits
RATE_LIMIT_CHAT_MAX=30             # Max chat messages per minute

# File Upload Rate Limits
RATE_LIMIT_FILE_UPLOAD_MAX=10      # Max file uploads per 10 minutes

# Admin Rate Limits
RATE_LIMIT_ADMIN_MAX=50            # Max admin requests per 5 minutes

# General API Rate Limits
RATE_LIMIT_GENERAL_MAX=1000        # Max general API requests per 15 minutes
RATE_LIMIT_PUBLIC_MAX=100          # Max public requests per minute
RATE_LIMIT_HEALTH_MAX=200          # Max health check requests per minute

# Alert Thresholds
RATE_LIMIT_ALERT_THRESHOLD=100     # Violations per minute before alert
RATE_LIMIT_SUSPICIOUS_THRESHOLD=50 # Suspicious IPs threshold
RATE_LIMIT_BLOCK_RATE_THRESHOLD=0.1 # Block rate threshold (10%)
```

### Endpoint-Specific Limits

| Endpoint Type | Window | Limit | Description |
|---------------|--------|-------|-------------|
| Login | 15 min | 3 | Extremely strict for security |
| Auth | 15 min | 5 | Very strict for authentication |
| Password Reset | 1 hour | 3 | Strict to prevent abuse |
| Trade Creation | 5 min | 5 | Moderate for trading |
| Trading | 1 min | 10 | Moderate for trading operations |
| Wallet Transactions | 10 min | 5 | Very strict for financial operations |
| Wallet | 5 min | 20 | Strict for wallet operations |
| Chat | 1 min | 30 | Moderate for messaging |
| File Upload | 10 min | 10 | Strict for uploads |
| Admin | 5 min | 50 | Moderate for admin operations |
| General API | 15 min | 1000 | Lenient for general use |
| Public | 1 min | 100 | Very lenient for public endpoints |
| Health | 1 min | 200 | Very lenient for monitoring |

## Features

### Smart Rate Limiting

The system automatically applies appropriate rate limits based on the endpoint being accessed:

```javascript
// Automatically detects endpoint type and applies appropriate limits
app.use(smartRateLimit);
```

### IP Management

#### Whitelisting
- Bypass rate limits for trusted IPs
- Configurable duration
- Admin-controlled via API

#### Blacklisting
- Block all requests from malicious IPs
- Automatic blacklisting for repeated violations
- Emergency blacklist capabilities

### Monitoring and Alerts

#### Metrics Tracked
- Total requests processed
- Blocked requests count
- Rate limit violations
- Suspicious IP addresses
- Top violators
- Block rate percentage

#### Alert Conditions
- High violation rate (>100 violations/minute)
- High block rate (>10% of requests blocked)
- Suspicious IP threshold exceeded (>50 suspicious IPs)

### Security Features

#### Threat Detection
- Violation tracking with TTL
- Suspicious activity monitoring
- Dynamic limit adjustment
- Enhanced logging for security events

#### Security Headers
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Requests remaining in window
- `X-RateLimit-Reset`: Window reset time
- `X-RateLimit-Type`: Type of rate limit applied
- `Retry-After`: Seconds to wait before retry

## API Usage

### Admin Endpoints

#### Get Metrics
```bash
GET /api/admin/rate-limiting/metrics
Authorization: Bearer <admin-token>
```

#### Get Alerts
```bash
GET /api/admin/rate-limiting/alerts
Authorization: Bearer <admin-token>
```

#### Get IP Status
```bash
GET /api/admin/rate-limiting/ip/:ip
Authorization: Bearer <admin-token>
```

#### Whitelist IP
```bash
POST /api/admin/rate-limiting/whitelist
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "ip": "*************",
  "duration": 3600,
  "reason": "Trusted partner"
}
```

#### Blacklist IP
```bash
POST /api/admin/rate-limiting/blacklist
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "ip": "*************",
  "duration": 86400,
  "reason": "Malicious activity detected"
}
```

#### Emergency Clear
```bash
POST /api/admin/rate-limiting/emergency-clear
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "confirm": "EMERGENCY_CLEAR_ALL_RATE_LIMITS"
}
```

## Implementation Details

### Redis Integration

The system uses Redis for distributed rate limiting across multiple server instances:

```javascript
// Redis key pattern: rl:prod:{type}:{ip}:{userId}
const key = `rl:prod:${type}:${ip}:${userId}`;
```

### Fallback Mechanisms

When Redis is unavailable:
- Requests are allowed to continue
- In-memory fallback for critical endpoints
- Graceful degradation without service interruption

### Performance Optimizations

- Redis pipeline operations for atomic updates
- Efficient key expiration management
- Minimal memory footprint
- Background cleanup processes

## Monitoring

### Health Checks

The system includes comprehensive health monitoring:

```javascript
// Check Redis connectivity and rate limiting health
const healthStatus = await rateLimitingService.performHealthCheck();
```

### Metrics Collection

Real-time metrics are collected and can be accessed via:
- Admin API endpoints
- Internal monitoring systems
- Redis-based storage with TTL

## Best Practices

### Production Deployment

1. **Redis Configuration**
   - Use Redis Cluster for high availability
   - Configure appropriate memory limits
   - Enable persistence for critical data

2. **Monitoring Setup**
   - Set up alerts for high violation rates
   - Monitor Redis performance
   - Track rate limiting effectiveness

3. **Security Considerations**
   - Regularly review suspicious IPs
   - Adjust limits based on traffic patterns
   - Implement automated response to attacks

### Troubleshooting

#### Common Issues

1. **High False Positives**
   - Review rate limits for specific endpoints
   - Check for legitimate high-traffic patterns
   - Consider whitelisting trusted IPs

2. **Redis Connection Issues**
   - Verify Redis connectivity
   - Check Redis memory usage
   - Review Redis logs for errors

3. **Performance Impact**
   - Monitor response times
   - Check Redis pipeline efficiency
   - Review rate limiting overhead

## Testing

Comprehensive test suite covers:
- Rate limit enforcement
- Redis integration
- Admin API functionality
- Edge cases and error handling
- Concurrent request handling

Run tests with:
```bash
npm test -- rateLimiting.test.js
```

## Future Enhancements

1. **Machine Learning Integration**
   - Adaptive rate limiting based on user behavior
   - Anomaly detection for sophisticated attacks

2. **Geographic Rate Limiting**
   - Country-based rate limiting
   - Regional traffic management

3. **Advanced Analytics**
   - Traffic pattern analysis
   - Predictive rate limiting
   - Performance optimization recommendations
