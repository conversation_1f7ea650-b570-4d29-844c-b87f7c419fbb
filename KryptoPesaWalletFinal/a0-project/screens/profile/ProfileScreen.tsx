import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
  Switch,
  Modal,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useKYC } from '../../context/KYCContext';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

// Mock data for UI development
const mockUser = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  profileComplete: true,
  kycVerified: true,
  twoFactorEnabled: false,
  completedTrades: 15,
  rating: 4.9,
  joinDate: '2023-01-15T00:00:00Z',
  paymentMethods: [
    {
      id: '1',
      name: 'Bank Transfer',
      instructions: 'Bank: Chase\nAccount Name: <PERSON>\nAccount Number: XXXX-XXXX-1234\nRouting Number: *********',
      isActive: true,
    },
    {
      id: '2',
      name: 'PayPal',
      instructions: 'PayPal Email: <EMAIL>',
      isActive: true,
    },
  ],
};

export default function ProfileScreen() {
  const navigation = useNavigation();
  const { logout } = useAuth();
  const { theme } = useTheme();
  const { kycData, getTradingLimit } = useKYC();
  
  const [user, setUser] = useState(mockUser);
  const [editMode, setEditMode] = useState(false);
  const [editedName, setEditedName] = useState(user.name);
  const [paymentMethodModalVisible, setPaymentMethodModalVisible] = useState(false);
  const [editingPaymentMethod, setEditingPaymentMethod] = useState(null);
  const [newPaymentMethodName, setNewPaymentMethodName] = useState('');
  const [newPaymentMethodInstructions, setNewPaymentMethodInstructions] = useState('');
  const [twoFactorModalVisible, setTwoFactorModalVisible] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  
  // In a real app, we would fetch this data from Convex
  // const user = useQuery(api.users.getCurrentUser);
  
  const handleSaveProfile = () => {
    if (!editedName.trim()) {
      Alert.alert('Error', 'Name cannot be empty');
      return;
    }
    
    // In a real app, we would call a Convex mutation
    // const result = await updateUserMutation({ name: editedName });
    
    // For now, just update the local state
    setUser({ ...user, name: editedName });
    setEditMode(false);
  };
  
  const handleToggleTwoFactor = () => {
    if (user.twoFactorEnabled) {
      // Disable 2FA
      Alert.alert(
        'Disable 2FA',
        'Are you sure you want to disable two-factor authentication? This will make your account less secure.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Disable',
            onPress: () => {
              // In a real app, we would call a Convex mutation
              // const result = await disableTwoFactorMutation();
              
              // For now, just update the local state
              setUser({ ...user, twoFactorEnabled: false });
            },
          },
        ],
      );
    } else {
      // Enable 2FA
      setTwoFactorModalVisible(true);
    }
  };
  
  const handleEnableTwoFactor = () => {
    if (verificationCode.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit verification code');
      return;
    }
    
    // In a real app, we would call a Convex mutation
    // const result = await enableTwoFactorMutation({ code: verificationCode });
    
    // For now, just update the local state
    setUser({ ...user, twoFactorEnabled: true });
    setTwoFactorModalVisible(false);
    setVerificationCode('');
    
    Alert.alert('Success', 'Two-factor authentication has been enabled for your account.');
  };
  
  const handleAddPaymentMethod = () => {
    setEditingPaymentMethod(null);
    setNewPaymentMethodName('');
    setNewPaymentMethodInstructions('');
    setPaymentMethodModalVisible(true);
  };
  
  const handleEditPaymentMethod = (paymentMethod) => {
    setEditingPaymentMethod(paymentMethod);
    setNewPaymentMethodName(paymentMethod.name);
    setNewPaymentMethodInstructions(paymentMethod.instructions);
    setPaymentMethodModalVisible(true);
  };
  
  const handleSavePaymentMethod = () => {
    if (!newPaymentMethodName.trim()) {
      Alert.alert('Error', 'Payment method name cannot be empty');
      return;
    }
    
    if (!newPaymentMethodInstructions.trim()) {
      Alert.alert('Error', 'Payment instructions cannot be empty');
      return;
    }
    
    // In a real app, we would call a Convex mutation
    // const result = await savePaymentMethodMutation({ 
    //   id: editingPaymentMethod?.id, 
    //   name: newPaymentMethodName, 
    //   instructions: newPaymentMethodInstructions 
    // });
    
    // For now, just update the local state
    if (editingPaymentMethod) {
      // Edit existing payment method
      const updatedPaymentMethods = user.paymentMethods.map(pm => 
        pm.id === editingPaymentMethod.id 
          ? { ...pm, name: newPaymentMethodName, instructions: newPaymentMethodInstructions }
          : pm
      );
      setUser({ ...user, paymentMethods: updatedPaymentMethods });
    } else {
      // Add new payment method
      const newPaymentMethod = {
        id: `new-${Date.now()}`,
        name: newPaymentMethodName,
        instructions: newPaymentMethodInstructions,
        isActive: true,
      };
      setUser({ ...user, paymentMethods: [...user.paymentMethods, newPaymentMethod] });
    }
    
    setPaymentMethodModalVisible(false);
  };
  
  const handleTogglePaymentMethod = (id) => {
    // In a real app, we would call a Convex mutation
    // const result = await togglePaymentMethodMutation({ id });
    
    // For now, just update the local state
    const updatedPaymentMethods = user.paymentMethods.map(pm => 
      pm.id === id ? { ...pm, isActive: !pm.isActive } : pm
    );
    setUser({ ...user, paymentMethods: updatedPaymentMethods });
  };
  
  const handleDeletePaymentMethod = (id) => {
    Alert.alert(
      'Delete Payment Method',
      'Are you sure you want to delete this payment method?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => {
            // In a real app, we would call a Convex mutation
            // const result = await deletePaymentMethodMutation({ id });
            
            // For now, just update the local state
            const updatedPaymentMethods = user.paymentMethods.filter(pm => pm.id !== id);
            setUser({ ...user, paymentMethods: updatedPaymentMethods });
          },
        },
      ],
    );
  };
  
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: () => {
            logout();
          },
        },
      ],
    );
  };
  
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => navigation.navigate('Settings')}
        >
          <Ionicons name="settings-outline" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{user.name.charAt(0)}</Text>
            </View>
            {user.kycVerified && (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark-circle" size={20} color={theme.colors.success} />
              </View>
            )}
          </View>
          
          <View style={styles.profileInfo}>
            {editMode ? (
              <TextInput
                style={styles.nameInput}
                value={editedName}
                onChangeText={setEditedName}
                placeholder="Your name"
                placeholderTextColor={theme.colors.inputPlaceholder}
                autoCapitalize="words"
              />
            ) : (
              <Text style={styles.profileName}>{user.name}</Text>
            )}
            <Text style={styles.profileEmail}>{user.email}</Text>
            <View style={styles.profileStats}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user.completedTrades}</Text>
                <Text style={styles.statLabel}>Trades</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <View style={styles.ratingContainer}>
                  <Text style={styles.statValue}>{user.rating}</Text>
                  <Ionicons name="star" size={16} color={theme.colors.warning} />
                </View>
                <Text style={styles.statLabel}>Rating</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{formatDate(user.joinDate)}</Text>
                <Text style={styles.statLabel}>Joined</Text>
              </View>
            </View>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Security</Text>
          
          <View style={styles.securityItem}>
            <View style={styles.securityItemLeft}>
              <Ionicons name="shield-checkmark" size={24} color={theme.colors.primary} style={styles.securityIcon} />
              <View>
                <Text style={styles.securityItemTitle}>Two-Factor Authentication</Text>
                <Text style={styles.securityItemDescription}>
                  Add an extra layer of security to your account
                </Text>
              </View>
            </View>
            <Switch
              value={user.twoFactorEnabled}
              onValueChange={handleToggleTwoFactor}
              trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
              thumbColor={user.twoFactorEnabled ? theme.colors.primary : theme.colors.surface}
            />
          </View>
          
          <View style={styles.securityItem}>
            <View style={styles.securityItemLeft}>
              <Ionicons name="card" size={24} color={theme.colors.primary} style={styles.securityIcon} />
              <View>
                <Text style={styles.securityItemTitle}>KYC Verification</Text>
                <Text style={styles.securityItemDescription}>
                  {kycData.status === 'approved'
                    ? `Verified - Trading limit: ${getTradingLimit()}`
                    : kycData.status === 'under_review'
                    ? 'Your verification is under review'
                    : kycData.status === 'rejected'
                    ? 'Verification rejected - Please resubmit'
                    : 'Verify your identity to unlock all features'
                  }
                </Text>
              </View>
            </View>
            {kycData.status === 'approved' ? (
              <View style={styles.verifiedTag}>
                <Text style={styles.verifiedTagText}>Verified</Text>
              </View>
            ) : kycData.status === 'under_review' ? (
              <View style={styles.pendingTag}>
                <Text style={styles.pendingTagText}>Under Review</Text>
              </View>
            ) : kycData.status === 'rejected' ? (
              <TouchableOpacity
                style={styles.rejectedButton}
                onPress={() => navigation.navigate('KYCVerification')}
              >
                <Text style={styles.rejectedButtonText}>Resubmit</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={styles.verifyButton}
                onPress={() => navigation.navigate('KYCVerification')}
              >
                <Text style={styles.verifyButtonText}>
                  {kycData.status === 'in_progress' ? 'Continue' : 'Verify'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
        
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Payment Methods</Text>
            <TouchableOpacity 
              style={styles.addButton}
              onPress={handleAddPaymentMethod}
            >
              <Ionicons name="add" size={20} color={theme.colors.primary} />
              <Text style={styles.addButtonText}>Add</Text>
            </TouchableOpacity>
          </View>
          
          {user.paymentMethods.length > 0 ? (
            user.paymentMethods.map((paymentMethod) => (
              <View key={paymentMethod.id} style={styles.paymentMethodItem}>
                <View style={styles.paymentMethodHeader}>
                  <Text style={styles.paymentMethodName}>{paymentMethod.name}</Text>
                  <View style={styles.paymentMethodActions}>
                    <Switch
                      value={paymentMethod.isActive}
                      onValueChange={() => handleTogglePaymentMethod(paymentMethod.id)}
                      trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
                      thumbColor={paymentMethod.isActive ? theme.colors.primary : theme.colors.surface}
                      style={styles.paymentMethodSwitch}
                    />
                    <TouchableOpacity 
                      style={styles.paymentMethodEditButton}
                      onPress={() => handleEditPaymentMethod(paymentMethod)}
                    >
                      <Ionicons name="create-outline" size={18} color={theme.colors.textSecondary} />
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={styles.paymentMethodDeleteButton}
                      onPress={() => handleDeletePaymentMethod(paymentMethod.id)}
                    >
                      <Ionicons name="trash-outline" size={18} color={theme.colors.error} />
                    </TouchableOpacity>
                  </View>
                </View>
                <Text style={styles.paymentMethodInstructions}>{paymentMethod.instructions}</Text>
              </View>
            ))
          ) : (
            <View style={styles.emptyStateContainer}>
              <Ionicons name="wallet-outline" size={48} color={theme.colors.border} />
              <Text style={styles.emptyStateText}>No payment methods</Text>
              <Text style={styles.emptyStateSubText}>
                Add payment methods to receive payments from buyers
              </Text>
            </View>
          )}
        </View>
        
        <TouchableOpacity 
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Ionicons name="log-out-outline" size={20} color={theme.colors.error} />
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
      
      {/* Payment Method Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={paymentMethodModalVisible}
        onRequestClose={() => setPaymentMethodModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingPaymentMethod ? 'Edit Payment Method' : 'Add Payment Method'}
              </Text>
              <TouchableOpacity onPress={() => setPaymentMethodModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalScrollView}>
              <Text style={styles.inputLabel}>Payment Method Name</Text>
              <TextInput
                style={styles.textInput}
                placeholder="e.g. Bank Transfer, PayPal, Venmo"
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={newPaymentMethodName}
                onChangeText={setNewPaymentMethodName}
              />
              
              <Text style={styles.inputLabel}>Payment Instructions</Text>
              <TextInput
                style={styles.textAreaInput}
                placeholder="Provide detailed instructions for the buyer to make payment"
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={newPaymentMethodInstructions}
                onChangeText={setNewPaymentMethodInstructions}
                multiline
                numberOfLines={5}
                textAlignVertical="top"
              />
              
              <View style={styles.warningContainer}>
                <Ionicons name="information-circle" size={20} color={theme.colors.primary} />
                <Text style={styles.warningText}>
                  These details will be shared with buyers when they choose to trade with you. Do not include sensitive information like passwords.
                </Text>
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setPaymentMethodModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[
                  styles.saveButton,
                  (!newPaymentMethodName.trim() || !newPaymentMethodInstructions.trim()) && styles.disabledButton
                ]}
                onPress={handleSavePaymentMethod}
                disabled={!newPaymentMethodName.trim() || !newPaymentMethodInstructions.trim()}
              >
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      
      {/* Two-Factor Authentication Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={twoFactorModalVisible}
        onRequestClose={() => setTwoFactorModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Enable Two-Factor Authentication</Text>
              <TouchableOpacity onPress={() => setTwoFactorModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalScrollView}>
              <View style={styles.twoFactorQrContainer}>
                <Image 
                  source={{ uri: 'https://api.a0.dev/assets/image?text=2FA-QR-CODE&aspect=1:1&seed=2fa' }}
                  style={styles.twoFactorQr}
                />
              </View>
              
              <Text style={styles.twoFactorInstructions}>
                1. Download an authenticator app like Google Authenticator or Authy.
              </Text>
              <Text style={styles.twoFactorInstructions}>
                2. Scan the QR code above with the app.
              </Text>
              <Text style={styles.twoFactorInstructions}>
                3. Enter the 6-digit verification code from the app below.
              </Text>
              
              <Text style={styles.inputLabel}>Verification Code</Text>
              <TextInput
                style={styles.codeInput}
                placeholder="000000"
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={verificationCode}
                onChangeText={setVerificationCode}
                keyboardType="number-pad"
                maxLength={6}
              />
              
              <View style={styles.warningContainer}>
                <Ionicons name="warning" size={20} color={theme.colors.warning} />
                <Text style={styles.warningText}>
                  Keep your backup codes in a safe place. You'll need them if you lose access to your authenticator app.
                </Text>
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setTwoFactorModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[
                  styles.saveButton,
                  verificationCode.length !== 6 && styles.disabledButton
                ]}
                onPress={handleEnableTwoFactor}
                disabled={verificationCode.length !== 6}
              >
                <Text style={styles.saveButtonText}>Enable</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  settingsButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
    padding: 2,
  },
  profileInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  nameInput: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.inputText,
    marginBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.primary,
    paddingBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 12,
  },
  profileStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  statDivider: {
    width: 1,
    height: 24,
    backgroundColor: theme.colors.border,
    marginHorizontal: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  section: {
    backgroundColor: theme.colors.surface,
    marginTop: 16,
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
    marginLeft: 4,
  },
  securityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  securityItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  securityIcon: {
    marginRight: 12,
  },
  securityItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 4,
  },
  securityItemDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  verifiedTag: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  verifiedTagText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.success,
  },
  verifyButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  verifyButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'white',
  },
  pendingTag: {
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.warning,
  },
  pendingTagText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.warning,
  },
  rejectedButton: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.error,
  },
  rejectedButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.error,
  },
  paymentMethodItem: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  paymentMethodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentMethodName: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
  },
  paymentMethodActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentMethodSwitch: {
    marginRight: 8,
  },
  paymentMethodEditButton: {
    padding: 4,
    marginRight: 8,
  },
  paymentMethodDeleteButton: {
    padding: 4,
  },
  paymentMethodInstructions: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    marginTop: 16,
    marginBottom: 32,
    paddingVertical: 16,
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.error,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalScrollView: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.inputText,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
  },
  textAreaInput: {
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.inputText,
    height: 120,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
  },
  warningContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    alignItems: 'flex-start',
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.primary,
    marginLeft: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  saveButton: {
    flex: 2,
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  disabledButton: {
    backgroundColor: theme.colors.primaryLight,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  twoFactorQrContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  twoFactorQr: {
    width: 200,
    height: 200,
    borderRadius: 8,
  },
  twoFactorInstructions: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  codeInput: {
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    padding: 12,
    fontSize: 24,
    color: theme.colors.inputText,
    marginBottom: 16,
    textAlign: 'center',
    letterSpacing: 8,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
  },
});