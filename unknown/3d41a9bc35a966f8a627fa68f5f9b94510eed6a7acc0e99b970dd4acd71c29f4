{"timestamp": "2025-07-06T00:02:36.428Z", "summary": {"totalIssues": 5, "criticalIssues": 0, "highIssues": 3, "mediumIssues": 1, "lowIssues": 1}, "categories": {"codeQuality": [{"type": "console_statements", "count": 122, "files": [{"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 77, "content": "console.log(`Login response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 97, "content": "console.log(`Registration response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 114, "content": "console.log(`Get offers response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 129, "content": "console.log(`Get trades response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 144, "content": "console.log(`Get profile response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 179, "content": "console.log(`Create offer response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 202, "content": "console.log(`Create trade response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 226, "content": "console.log(`Search offers response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 246, "content": "console.log(`Search trades response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 271, "content": "console.log(`Complex trade creation response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 286, "content": "console.log(`Dashboard aggregation response time: ${responseTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 313, "content": "console.log(`${concurrentRequests} concurrent requests average time: ${averageTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 356, "content": "console.log(`${concurrentRequests} concurrent writes average time: ${averageTime}ms`);"}, {"file": "backend/src/__tests__/performance/api.performance.test.js", "line": 382, "content": "console.log(`Memory increase after 50 operations: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 175, "content": "console.log('\\n🎉 DATABASE SEEDING COMPLETED!');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 176, "content": "console.log('=====================================');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 177, "content": "console.log('\\n📱 ADMIN DASHBOARD ACCESS:');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 178, "content": "console.log('URL: http://localhost:3001');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 179, "content": "console.log(`Email: ${ADMIN_CONFIG.email}`);"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 180, "content": "console.log(`Password: ${ADMIN_CONFIG.password}`);"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 181, "content": "console.log('\\n👥 TEST USERS CREATED:');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 183, "content": "console.log(`• ${user.username} (${user.email}) - Role: ${user.role}`);"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 185, "content": "console.log('\\n🔗 SYSTEM ENDPOINTS:');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 186, "content": "console.log('• Backend API: http://localhost:3000');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 187, "content": "console.log('• Admin Dashboard: http://localhost:3001');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 188, "content": "console.log('• Health Check: http://localhost:3000/health');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 189, "content": "console.log('\\n🧪 READY FOR TESTING!');"}, {"file": "admin-dashboard/src/components/ErrorBoundary.js", "line": 66, "content": "console.error('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> caught an error:', error, errorInfo);"}, {"file": "admin-dashboard/src/components/ErrorBoundary.js", "line": 100, "content": "console.error('Failed to report error:', reportingError);"}, {"file": "admin-dashboard/src/components/FeedbackSystem.js", "line": 193, "content": "console.error('Failed to submit feedback:', error);"}, {"file": "admin-dashboard/src/pages/AnalyticsPage.js", "line": 49, "content": "console.error('Error fetching analytics:', error);"}, {"file": "admin-dashboard/src/pages/DashboardPage.js", "line": 93, "content": "console.error('Error fetching dashboard stats:', error);"}, {"file": "admin-dashboard/src/pages/DisputesPage.js", "line": 83, "content": "console.log('Resolving dispute:', {"}, {"file": "admin-dashboard/src/pages/OffersPage.js", "line": 79, "content": "console.error('Error fetching offers:', error);"}, {"file": "admin-dashboard/src/pages/OffersPage.js", "line": 115, "content": "console.error('Error performing offer action:', error);"}, {"file": "admin-dashboard/src/pages/TradesPage.js", "line": 73, "content": "console.error('Error fetching trades:', error);"}, {"file": "admin-dashboard/src/pages/TradesPage.js", "line": 109, "content": "console.error('Error performing trade action:', error);"}, {"file": "admin-dashboard/src/pages/UsersPage.js", "line": 88, "content": "console.error('Error fetching users:', error);"}, {"file": "admin-dashboard/src/pages/UsersPage.js", "line": 177, "content": "console.error('Error executing user action:', error);"}, {"file": "admin-dashboard/src/services/AuthContext.js", "line": 43, "content": "console.error('Failed to get current user:', error);"}, {"file": "admin-dashboard/src/services/AuthContext.js", "line": 74, "content": "console.error('<PERSON><PERSON> failed:', error);"}, {"file": "admin-dashboard/src/services/api.js", "line": 129, "content": "console.error('No admin token available for WebSocket connection');"}, {"file": "admin-dashboard/src/services/api.js", "line": 141, "content": "console.log('Admin WebSocket connected');"}, {"file": "admin-dashboard/src/services/api.js", "line": 151, "content": "console.error('Error parsing WebSocket message:', error);"}, {"file": "admin-dashboard/src/services/api.js", "line": 156, "content": "console.log('Admin WebSocket disconnected');"}, {"file": "admin-dashboard/src/services/api.js", "line": 162, "content": "console.error('Admin WebSocket error:', error);"}, {"file": "admin-dashboard/src/services/api.js", "line": 166, "content": "console.error('Error creating WebSocket connection:', error);"}, {"file": "admin-dashboard/src/services/api.js", "line": 183, "content": "console.log(`Attempting to reconnect WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);"}, {"file": "admin-dashboard/src/services/api.js", "line": 212, "content": "console.error('Error in WebSocket event callback:', error);"}, {"file": "admin-dashboard/src/services/api.js", "line": 222, "content": "console.warn('WebSocket not connected, cannot send data');"}, {"file": "admin-dashboard/src/services/api.js", "line": 243, "content": "console.error('API Error:', message);"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 30, "content": "console.log('Crash reporting service initialized');"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 65, "content": "const originalConsoleError = console.error;"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 66, "content": "console.error = (...args) => {"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 67, "content": "// Call original console.error"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 101, "content": "console.warn(`Error throttled: ${errorKey}`);"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 120, "content": "console.warn('Error captured:', enhancedError);"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 122, "content": "console.error('Failed to capture error:', captureError);"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 197, "content": "console.log(`Reported ${errors.length} errors successfully`);"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 199, "content": "console.error('Failed to report errors:', reportError);"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 210, "content": "console.error('Failed to report critical error immediately:', reportError);"}, {"file": "admin-dashboard/src/services/crashReporting.js", "line": 326, "content": "console.log('Breadcrumb:', breadcrumb);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 26, "content": "console.warn('Service Workers not supported');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 43, "content": "console.log('PWA Service initialized successfully');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 46, "content": "console.error('Failed to initialize PWA Service:', error);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 58, "content": "console.log('Service Worker registered:', this.registration);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 74, "content": "console.log('Content is cached for offline use');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 87, "content": "console.error('Service Worker registration failed:', error);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 102, "content": "console.log('App is installable');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 113, "content": "console.log('App was installed');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 127, "content": "console.log('App is online');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 130, "content": "console.log('App is offline');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 144, "content": "console.warn('Push notifications not supported');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 193, "content": "console.log('Push subscription successful');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 196, "content": "console.error('Push subscription failed:', error);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 220, "content": "console.log('Push subscription saved to server');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 222, "content": "console.error('Failed to save push subscription:', error);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 239, "content": "console.log(`Install prompt outcome: ${outcome}`);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 250, "content": "console.error('Install prompt failed:', error);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 269, "content": "console.log('Service Worker updated');"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 271, "content": "console.error('Service Worker update failed:', error);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 306, "content": "console.log('Cache updated:', payload);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 310, "content": "console.log('Offline fallback triggered:', payload);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 314, "content": "console.log('Unknown service worker message:', type, payload);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 339, "content": "console.error('Listener error:', error);"}, {"file": "admin-dashboard/src/services/pwaService.js", "line": 364, "content": "console.log('PWA Event:', event, data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 61, "content": "console.log('WebSocket service initialized');"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 64, "content": "console.error('Failed to initialize WebSocket:', error);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 74, "content": "console.log('WebSocket connected');"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 89, "content": "console.log('WebSocket disconnected:', reason);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 99, "content": "console.log('Server disconnected client, not attempting reconnection');"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 106, "content": "console.error('WebSocket connection error:', error);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 113, "content": "console.error('WebSocket error:', error);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 124, "content": "console.log('Heartbeat response received:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 149, "content": "console.warn('Cannot subscribe - WebSocket not connected');"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 159, "content": "console.log(`Subscribed to channel: ${channel}`);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 172, "content": "console.log(`Unsubscribed from channel: ${channel}`);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 181, "content": "console.log('Message queued - WebSocket not connected');"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 221, "content": "console.error('Max reconnection attempts reached');"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 228, "content": "console.log(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 239, "content": "console.log('Received notification:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 247, "content": "console.log('System health update:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 252, "content": "console.log('Live stats update:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 257, "content": "console.log('Trade update:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 268, "content": "console.log('User activity:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 273, "content": "console.log('Security alert:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 290, "content": "console.log('System alert:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 301, "content": "console.log('Performance metrics:', data);"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 333, "content": "console.error('Connection listener error:', error);"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 37, "content": "console.log('Performance monitoring initialized');"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 329, "content": "console.log('LCP:', entry.startTime);"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 334, "content": "console.log('FID:', entry.processingStart - entry.startTime);"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 380, "content": "console.warn('Slow page load detected:', metrics);"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 384, "content": "console.warn('Slow API call detected:', metrics);"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 388, "content": "console.warn('Slow render detected:', metrics);"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 392, "content": "console.warn('High memory usage detected:', metrics);"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 396, "content": "console.error('Critical error detected:', metrics);"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 403, "content": "console.log('Performance report:', report);"}, {"file": "admin-dashboard/src/utils/security.js", "line": 200, "content": "console.warn('Security Event:', logEntry);"}, {"file": "admin-dashboard/src/utils/security.js", "line": 203, "content": "console.log('Security Event:', logEntry);"}, {"file": "admin-dashboard/src/utils/security.js", "line": 236, "content": "console.error('Session refresh failed:', error);"}, {"file": "admin-dashboard/src/utils/security.js", "line": 322, "content": "console.log('Admin Dashboard Security Initialized');"}], "severity": "medium", "description": "Console statements found in production code"}, {"type": "todo_comments", "count": 11, "items": [{"file": "backend/src/models/Trade.js", "line": 331, "content": "totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } },"}, {"file": "backend/src/models/Trade.js", "line": 332, "content": "averageTradeSize: { $avg: { $toDouble: '$cryptocurrency.amount' } },"}, {"file": "backend/src/routes/admin.js", "line": 63, "content": "totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } },"}, {"file": "backend/src/routes/admin.js", "line": 98, "content": "totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } }"}, {"file": "backend/src/routes/admin.js", "line": 116, "content": "totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } }"}, {"file": "backend/src/routes/admin.js", "line": 285, "content": "totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } }"}, {"file": "backend/src/routes/metrics.js", "line": 93, "content": "totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } },"}, {"file": "backend/src/routes/metrics.js", "line": 95, "content": "avgTradeSize: { $avg: { $toDouble: '$cryptocurrency.amount' } }"}, {"file": "backend/src/routes/trading.js", "line": 382, "content": "// TODO: Add admin role check"}, {"file": "admin-dashboard/src/utils/performanceMonitor.js", "line": 402, "content": "// TODO: Send to monitoring service (e.g., DataDog, New Relic)"}, {"file": "admin-dashboard/src/utils/security.js", "line": 201, "content": "// TODO: Send to security monitoring service"}], "severity": "low", "description": "TODO/FIXME comments found"}, {"type": "placeholder_implementations", "count": 13, "items": [{"file": "backend/src/middleware/responseCache.js", "line": 288, "content": "// Placeholder methods for cache warming"}, {"file": "backend/src/models/Wallet.js", "line": 145, "content": "// Placeholder calculation - would use real price feeds"}, {"file": "backend/src/services/deadLetterQueue.js", "line": 318, "content": "// Placeholder retry methods - implement based on specific operation types"}, {"file": "backend/src/services/transactionService.js", "line": 94, "content": "const privateKey = 'placeholder'; // Would be securely retrieved"}, {"file": "backend/src/services/walletService.js", "line": 38, "content": "address: 'tb1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh', // Testnet placeholder"}, {"file": "backend/src/services/walletService.js", "line": 39, "content": "publicKey: 'placeholder',"}, {"file": "admin-dashboard/src/components/FeedbackSystem.js", "line": 311, "content": "placeholder={`Brief ${selectedType?.label.toLowerCase()} title`}"}, {"file": "admin-dashboard/src/components/FeedbackSystem.js", "line": 321, "content": "placeholder=\"Please provide detailed information about your feedback...\""}, {"file": "admin-dashboard/src/components/FeedbackSystem.js", "line": 347, "content": "placeholder=\"<EMAIL>\""}, {"file": "admin-dashboard/src/pages/DisputesPage.js", "line": 190, "content": "placeholder=\"Explain the reasoning for this resolution...\""}, {"file": "admin-dashboard/src/pages/OffersPage.js", "line": 221, "content": "placeholder=\"Search offers...\""}, {"file": "admin-dashboard/src/pages/TradesPage.js", "line": 214, "content": "placeholder=\"Search trades...\""}, {"file": "admin-dashboard/src/pages/UsersPage.js", "line": 236, "content": "placeholder=\"Search users...\""}], "severity": "high", "description": "Placeholder implementations found"}], "configuration": [{"type": "environment_variables", "issues": [{"variable": "NODE_ENV", "issue": "Missing required environment variable"}, {"variable": "JWT_SECRET", "issue": "Missing required environment variable"}, {"variable": "DB_PASSWORD", "issue": "Missing required environment variable"}, {"variable": "REDIS_PASSWORD", "issue": "Missing required environment variable"}, {"variable": "ENCRYPTION_KEY", "issue": "Missing required environment variable"}], "severity": "high", "description": "Environment variable issues found"}, {"type": "hardcoded_values", "count": 19, "items": [{"file": "backend/src/__tests__/setup.js", "line": 182, "content": "process.env.JWT_SECRET = 'test-jwt-secret';"}, {"file": "backend/src/config/database.js", "line": 78, "content": "await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa', getConnectionOptions());"}, {"file": "backend/src/config/database.js", "line": 142, "content": "const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa';"}, {"file": "backend/src/config/redis.js", "line": 71, "content": "const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 99, "content": "const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa';"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 178, "content": "console.log('URL: http://localhost:3001');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 186, "content": "console.log('• Backend API: http://localhost:3000');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 187, "content": "console.log('• Admin Dashboard: http://localhost:3001');"}, {"file": "backend/src/scripts/seedDatabase.js", "line": 188, "content": "console.log('• Health Check: http://localhost:3000/health');"}, {"file": "backend/src/server.js", "line": 76, "content": ": ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:19006'],"}, {"file": "backend/src/server.js", "line": 108, "content": ": ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:19006'],"}, {"file": "backend/src/utils/validateEnv.js", "line": 36, "content": "MONGODB_URI: ['mongodb://localhost:27017/kryptopesa'],"}, {"file": "backend/src/utils/validateEnv.js", "line": 37, "content": "REDIS_URL: ['redis://localhost:6379'],"}, {"file": "admin-dashboard/src/services/AuthContext.js", "line": 15, "content": "axios.defaults.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';"}, {"file": "admin-dashboard/src/services/api.js", "line": 7, "content": ": 'http://localhost:8000/api',"}, {"file": "admin-dashboard/src/services/api.js", "line": 135, "content": ": 'ws://localhost:3000';"}, {"file": "admin-dashboard/src/services/websocketService.js", "line": 44, "content": "const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:3000';"}, {"file": "admin-dashboard/src/utils/security.js", "line": 35, "content": "process.env.REACT_APP_API_URL || 'http://localhost:3000',"}, {"file": "admin-dashboard/src/utils/security.js", "line": 36, "content": "process.env.REACT_APP_WS_URL || 'ws://localhost:3000',"}], "severity": "high", "description": "Hardcoded values found in production code"}], "security": [], "performance": [], "errorHandling": [], "testing": [], "documentation": [], "dependencies": []}, "fixes": 1, "recommendations": ["Resolve high-priority issues to ensure system stability"]}