-- Database Optimization Script for KryptoPesa
-- Optimized for 10,000+ concurrent users with high-performance indexing

-- =====================================================
-- PERFORMANCE CONFIGURATION
-- =====================================================

-- Increase shared buffers for better caching
ALTER SYSTEM SET shared_buffers = '2GB';

-- Optimize work memory for complex queries
ALTER SYSTEM SET work_mem = '256MB';

-- Increase maintenance work memory for faster index creation
ALTER SYSTEM SET maintenance_work_mem = '512MB';

-- Optimize effective cache size
ALTER SYSTEM SET effective_cache_size = '6GB';

-- Reduce random page cost for SSD storage
ALTER SYSTEM SET random_page_cost = 1.1;

-- Optimize checkpoint settings for write performance
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '64MB';

-- Increase max connections for high concurrency
ALTER SYSTEM SET max_connections = 500;

-- Optimize autovacuum for high-write workload
ALTER SYSTEM SET autovacuum_max_workers = 6;
ALTER SYSTEM SET autovacuum_naptime = '30s';

-- Apply configuration changes
SELECT pg_reload_conf();

-- =====================================================
-- OPTIMIZED INDEXES FOR HIGH-PERFORMANCE QUERIES
-- =====================================================

-- Users table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active 
ON users(email) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_phone_verified 
ON users(phone_number) WHERE is_verified = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at 
ON users(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_login 
ON users(last_login_at DESC) WHERE last_login_at IS NOT NULL;

-- Trading offers indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_active_type_crypto 
ON trading_offers(offer_type, cryptocurrency, is_active, created_at DESC) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_user_active 
ON trading_offers(user_id, is_active, created_at DESC) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_price_range 
ON trading_offers(cryptocurrency, price_per_unit, amount) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_location 
ON trading_offers(country, city) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_payment_methods 
ON trading_offers USING GIN(payment_methods) 
WHERE is_active = true;

-- Trades table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_buyer_status 
ON trades(buyer_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_seller_status 
ON trades(seller_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_offer_status 
ON trades(offer_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_status_updated 
ON trades(status, updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_escrow_status 
ON trades(escrow_status, escrow_funded_at) 
WHERE escrow_status IS NOT NULL;

-- Transactions table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_user_type_status 
ON transactions(user_id, transaction_type, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_trade_type 
ON transactions(trade_id, transaction_type, created_at DESC) 
WHERE trade_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_hash 
ON transactions(transaction_hash) 
WHERE transaction_hash IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_status_pending 
ON transactions(status, created_at) 
WHERE status = 'pending';

-- Wallets table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wallets_user_crypto 
ON wallets(user_id, cryptocurrency);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wallets_address 
ON wallets(wallet_address);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wallets_balance_updated 
ON wallets(balance_updated_at DESC) 
WHERE balance > 0;

-- Chat messages indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_messages_conversation 
ON chat_messages(conversation_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_messages_sender 
ON chat_messages(sender_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_messages_unread 
ON chat_messages(conversation_id, is_read, created_at) 
WHERE is_read = false;

-- Notifications indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread 
ON notifications(user_id, is_read, created_at DESC) 
WHERE is_read = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_type_created 
ON notifications(notification_type, created_at DESC);

-- Audit logs indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_action 
ON audit_logs(user_id, action, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_table_record 
ON audit_logs(table_name, record_id, created_at DESC);

-- =====================================================
-- PARTIAL INDEXES FOR SPECIFIC USE CASES
-- =====================================================

-- Active offers by cryptocurrency and type
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_btc_buy_active 
ON trading_offers(price_per_unit, amount, created_at DESC) 
WHERE cryptocurrency = 'BTC' AND offer_type = 'buy' AND is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_btc_sell_active 
ON trading_offers(price_per_unit, amount, created_at DESC) 
WHERE cryptocurrency = 'BTC' AND offer_type = 'sell' AND is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_usdt_buy_active 
ON trading_offers(price_per_unit, amount, created_at DESC) 
WHERE cryptocurrency = 'USDT' AND offer_type = 'buy' AND is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_usdt_sell_active 
ON trading_offers(price_per_unit, amount, created_at DESC) 
WHERE cryptocurrency = 'USDT' AND offer_type = 'sell' AND is_active = true;

-- Recent active trades
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_recent_active 
ON trades(created_at DESC, status) 
WHERE status IN ('pending', 'accepted', 'escrowed', 'paid');

-- Failed transactions for retry
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_failed_recent 
ON transactions(created_at DESC) 
WHERE status = 'failed' AND created_at > NOW() - INTERVAL '24 hours';

-- =====================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- Trading dashboard queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_dashboard 
ON trading_offers(user_id, is_active, offer_type, cryptocurrency, created_at DESC);

-- Trade history with pagination
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_history_pagination 
ON trades(user_id, created_at DESC, id) 
WHERE user_id IN (buyer_id, seller_id);

-- Wallet transaction history
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wallet_transactions 
ON transactions(user_id, created_at DESC, transaction_type, amount);

-- =====================================================
-- EXPRESSION INDEXES FOR COMPUTED VALUES
-- =====================================================

-- Case-insensitive email lookup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_lower 
ON users(LOWER(email));

-- Full-text search on offer descriptions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_offers_description_fts 
ON trading_offers USING gin(to_tsvector('english', description)) 
WHERE is_active = true;

-- =====================================================
-- MATERIALIZED VIEWS FOR ANALYTICS
-- =====================================================

-- Trading statistics materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS trading_stats_daily AS
SELECT 
    DATE(created_at) as trade_date,
    cryptocurrency,
    COUNT(*) as total_trades,
    SUM(amount) as total_volume,
    AVG(price_per_unit) as avg_price,
    COUNT(DISTINCT buyer_id) as unique_buyers,
    COUNT(DISTINCT seller_id) as unique_sellers
FROM trades 
WHERE status = 'completed'
GROUP BY DATE(created_at), cryptocurrency
ORDER BY trade_date DESC, cryptocurrency;

CREATE UNIQUE INDEX ON trading_stats_daily(trade_date, cryptocurrency);

-- User activity materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS user_activity_stats AS
SELECT 
    user_id,
    COUNT(CASE WHEN offer_type = 'buy' THEN 1 END) as buy_offers,
    COUNT(CASE WHEN offer_type = 'sell' THEN 1 END) as sell_offers,
    AVG(CASE WHEN status = 'completed' THEN 
        EXTRACT(EPOCH FROM completed_at - created_at)/3600 
    END) as avg_completion_hours,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_trades,
    MAX(created_at) as last_activity
FROM trading_offers 
GROUP BY user_id;

CREATE UNIQUE INDEX ON user_activity_stats(user_id);

-- =====================================================
-- REFRESH MATERIALIZED VIEWS PROCEDURE
-- =====================================================

CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY trading_stats_daily;
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_activity_stats;
    
    -- Log refresh
    INSERT INTO system_logs (log_level, message, created_at)
    VALUES ('INFO', 'Materialized views refreshed', NOW());
END;
$$ LANGUAGE plpgsql;

-- Schedule materialized view refresh (requires pg_cron extension)
-- SELECT cron.schedule('refresh-stats', '0 */6 * * *', 'SELECT refresh_materialized_views();');

-- =====================================================
-- QUERY OPTIMIZATION FUNCTIONS
-- =====================================================

-- Function to get active offers with caching hints
CREATE OR REPLACE FUNCTION get_active_offers(
    p_cryptocurrency VARCHAR(10) DEFAULT NULL,
    p_offer_type VARCHAR(10) DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    offer_type VARCHAR(10),
    cryptocurrency VARCHAR(10),
    amount DECIMAL(18,8),
    price_per_unit DECIMAL(18,2),
    payment_methods JSONB,
    description TEXT,
    created_at TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        o.id,
        o.user_id,
        o.offer_type,
        o.cryptocurrency,
        o.amount,
        o.price_per_unit,
        o.payment_methods,
        o.description,
        o.created_at
    FROM trading_offers o
    WHERE o.is_active = true
        AND (p_cryptocurrency IS NULL OR o.cryptocurrency = p_cryptocurrency)
        AND (p_offer_type IS NULL OR o.offer_type = p_offer_type)
    ORDER BY o.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get user trade history with performance optimization
CREATE OR REPLACE FUNCTION get_user_trade_history(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    offer_id UUID,
    counterpart_id UUID,
    amount DECIMAL(18,8),
    price_per_unit DECIMAL(18,2),
    status VARCHAR(20),
    created_at TIMESTAMP,
    completed_at TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.offer_id,
        CASE 
            WHEN t.buyer_id = p_user_id THEN t.seller_id
            ELSE t.buyer_id
        END as counterpart_id,
        t.amount,
        t.price_per_unit,
        t.status,
        t.created_at,
        t.completed_at
    FROM trades t
    WHERE t.buyer_id = p_user_id OR t.seller_id = p_user_id
    ORDER BY t.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE;

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- Create extension for monitoring (if not exists)
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- View for slow queries monitoring
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 100  -- Queries taking more than 100ms on average
ORDER BY mean_time DESC;

-- View for index usage monitoring
CREATE OR REPLACE VIEW index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'Never used'
        WHEN idx_scan < 100 THEN 'Rarely used'
        ELSE 'Frequently used'
    END as usage_status
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Procedure to analyze table statistics
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS void AS $$
BEGIN
    ANALYZE users;
    ANALYZE trading_offers;
    ANALYZE trades;
    ANALYZE transactions;
    ANALYZE wallets;
    ANALYZE chat_messages;
    ANALYZE notifications;
    
    INSERT INTO system_logs (log_level, message, created_at)
    VALUES ('INFO', 'Table statistics updated', NOW());
END;
$$ LANGUAGE plpgsql;

-- Procedure to reindex tables during maintenance window
CREATE OR REPLACE FUNCTION reindex_tables()
RETURNS void AS $$
BEGIN
    REINDEX TABLE CONCURRENTLY users;
    REINDEX TABLE CONCURRENTLY trading_offers;
    REINDEX TABLE CONCURRENTLY trades;
    REINDEX TABLE CONCURRENTLY transactions;
    
    INSERT INTO system_logs (log_level, message, created_at)
    VALUES ('INFO', 'Tables reindexed', NOW());
END;
$$ LANGUAGE plpgsql;
