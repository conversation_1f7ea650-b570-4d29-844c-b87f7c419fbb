const { authenticateWallet } = require('./walletAuth');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');

/**
 * Non-custodial wallet authentication middleware
 * This replaces traditional email/password authentication with wallet-based auth
 */

/**
 * Primary authentication middleware - wallet-based only
 * All authentication in KryptoPesa is now wallet-based for non-custodial operation
 */
const authenticate = async (req, res, next) => {
  try {
    // Use wallet authentication as the primary (and only) method
    return await authenticateWallet(req, res, next);
  } catch (error) {
    logger.error('Authentication failed:', error);
    return next(new AppError('Authentication required', 401, 'AUTHENTICATION_REQUIRED'));
  }
};

/**
 * Legacy 2FA middleware - not needed for wallet-based auth
 * Wallet signatures provide cryptographic proof of ownership
 */
const require2FA = async (req, res, next) => {
  // In wallet-based auth, the signature itself serves as 2FA
  // No additional 2FA needed as wallet signatures are cryptographically secure
  logger.info('2FA check skipped - wallet signature provides cryptographic authentication');
  next();
};

/**
 * Admin authentication - requires wallet auth + admin role
 */
const requireAdmin = async (req, res, next) => {
  try {
    // First authenticate with wallet
    await authenticate(req, res, (err) => {
      if (err) return next(err);
      
      // Check if user has admin role
      if (!req.user || !req.user.role || req.user.role !== 'admin') {
        return next(new AppError('Admin access required', 403, 'ADMIN_ACCESS_REQUIRED'));
      }
      
      next();
    });
  } catch (error) {
    logger.error('Admin authentication failed:', error);
    return next(new AppError('Admin authentication failed', 403, 'ADMIN_AUTH_FAILED'));
  }
};

/**
 * Optional authentication - doesn't fail if no auth provided
 * Useful for public endpoints that can show more data if user is authenticated
 */
const optionalAuth = async (req, res, next) => {
  try {
    const walletAddress = req.headers['x-wallet-address'];
    const signature = req.headers['x-wallet-signature'];
    
    if (walletAddress && signature) {
      // Try to authenticate, but don't fail if it doesn't work
      return await authenticateWallet(req, res, (err) => {
        if (err) {
          logger.warn('Optional authentication failed:', err.message);
          // Continue without authentication
          req.user = null;
        }
        next();
      });
    } else {
      // No auth headers provided, continue without authentication
      req.user = null;
      next();
    }
  } catch (error) {
    logger.warn('Optional authentication error:', error);
    req.user = null;
    next();
  }
};

module.exports = {
  authenticate,
  require2FA,
  requireAdmin,
  optionalAuth
};
