# KryptoPesa Ingress and SSL/TLS Configuration
# Enterprise-grade load balancing and SSL termination

apiVersion: v1
kind: Namespace
metadata:
  name: kryptopesa-production
  labels:
    environment: production
    app: kryptopesa

---
# SSL Certificate for Production Domain
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: kryptopesa-tls
  namespace: kryptopesa-production
spec:
  secretName: kryptopesa-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - api.kryptopesa.com
  - admin.kryptopesa.com
  - kryptopesa.com
  - www.kryptopesa.com

---
# ClusterIssuer for Let's Encrypt Production
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
          podTemplate:
            spec:
              nodeSelector:
                "kubernetes.io/os": linux

---
# Main API Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kryptopesa-api-ingress
  namespace: kryptopesa-production
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-buffering: "on"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "8k"
    nginx.ingress.kubernetes.io/client-max-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://kryptopesa.com,https://www.kryptopesa.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-expose-headers: "Content-Length,Content-Range"
    nginx.ingress.kubernetes.io/cors-max-age: "1728000"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
      more_set_headers "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';";
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload";
spec:
  tls:
  - hosts:
    - api.kryptopesa.com
    secretName: kryptopesa-tls-secret
  rules:
  - host: api.kryptopesa.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend
            port:
              number: 3000

---
# Admin Dashboard Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kryptopesa-admin-ingress
  namespace: kryptopesa-production
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: admin-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "KryptoPesa Admin Dashboard"
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
      more_set_headers "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';";
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload";
spec:
  tls:
  - hosts:
    - admin.kryptopesa.com
    secretName: kryptopesa-tls-secret
  rules:
  - host: admin.kryptopesa.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: admin
            port:
              number: 80

---
# Basic Auth Secret for Admin Dashboard
apiVersion: v1
kind: Secret
metadata:
  name: admin-basic-auth
  namespace: kryptopesa-production
type: Opaque
data:
  auth: YWRtaW46JGFwcjEkSDY1dnVhNzAkLnRiTXhPbGRBaVk5Y3lPcUJPeWMvMQo=  # admin:admin123 (change in production)

---
# Monitoring Ingress (Internal)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kryptopesa-monitoring-ingress
  namespace: kryptopesa-production
  annotations:
    kubernetes.io/ingress.class: nginx-internal
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: monitoring-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "KryptoPesa Monitoring"
spec:
  tls:
  - hosts:
    - monitoring.internal.kryptopesa.com
    secretName: kryptopesa-internal-tls
  rules:
  - host: monitoring.internal.kryptopesa.com
    http:
      paths:
      - path: /prometheus
        pathType: Prefix
        backend:
          service:
            name: prometheus
            port:
              number: 9090
      - path: /grafana
        pathType: Prefix
        backend:
          service:
            name: grafana
            port:
              number: 3000
      - path: /alertmanager
        pathType: Prefix
        backend:
          service:
            name: alertmanager
            port:
              number: 9093

---
# Basic Auth Secret for Monitoring
apiVersion: v1
kind: Secret
metadata:
  name: monitoring-basic-auth
  namespace: kryptopesa-production
type: Opaque
data:
  auth: bW9uaXRvcjokYXByMSRINjV2dWE3MCAkLnRiTXhPbGRBaVk5Y3lPcUJPeWMvMQo=  # monitor:monitor123 (change in production)

---
# Rate Limiting ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-rate-limit-config
  namespace: kryptopesa-production
data:
  rate-limit.conf: |
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=trading:10m rate=20r/s;
    limit_req_zone $binary_remote_addr zone=wallet:10m rate=15r/s;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn conn_limit_per_ip 20;
    
    # Request size limits
    client_max_body_size 50m;
    client_body_buffer_size 128k;
    client_header_buffer_size 3m;
    large_client_header_buffers 4 256k;

---
# WAF (Web Application Firewall) ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-waf-config
  namespace: kryptopesa-production
data:
  waf.conf: |
    # Block common attack patterns
    location ~* \.(php|asp|aspx|jsp)$ {
        deny all;
    }
    
    # Block SQL injection attempts
    if ($args ~* "(\;|\||`|>|<|'|\"|\$|\%|=|/bin/|/etc/|/var/|/tmp/|/usr/|/proc/)") {
        return 403;
    }
    
    # Block XSS attempts
    if ($args ~* "(<|%3C).*script.*(>|%3E)") {
        return 403;
    }
    
    # Block directory traversal
    if ($args ~* "\.\./") {
        return 403;
    }
    
    # Block user agent attacks
    if ($http_user_agent ~* (nmap|nikto|wikto|sf|sqlmap|bsqlbf|w3af|acunetix|havij|appscan)) {
        return 403;
    }
    
    # Block suspicious requests
    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS)$ ) {
        return 405;
    }

---
# DDoS Protection ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-ddos-protection
  namespace: kryptopesa-production
data:
  ddos-protection.conf: |
    # DDoS protection settings
    limit_req_status 429;
    limit_conn_status 429;
    
    # Slow request protection
    client_body_timeout 10s;
    client_header_timeout 10s;
    keepalive_timeout 5s 5s;
    send_timeout 10s;
    
    # Buffer overflow protection
    client_body_buffer_size 1k;
    client_header_buffer_size 1k;
    client_max_body_size 1k;
    large_client_header_buffers 2 1k;
