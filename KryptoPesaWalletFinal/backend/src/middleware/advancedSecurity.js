/**
 * Advanced Security Hardening Middleware
 * Enterprise-grade security for financial platform
 */

const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const validator = require('validator');
const xss = require('xss');
const hpp = require('hpp');
const mongoSanitize = require('express-mongo-sanitize');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

class AdvancedSecurityService {
  constructor() {
    this.redisClient = getRedisClient();
    this.securityMetrics = {
      blockedRequests: 0,
      suspiciousActivity: 0,
      rateLimitHits: 0,
      xssAttempts: 0,
      sqlInjectionAttempts: 0,
      bruteForceAttempts: 0
    };
    
    this.suspiciousPatterns = [
      /(\b(union|select|insert|delete|update|drop|create|alter|exec|execute)\b)/gi,
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /onload|onerror|onclick/gi,
      /\.\.\//g, // Path traversal
      /\/etc\/passwd/gi,
      /cmd\.exe/gi,
      /powershell/gi
    ];
  }

  /**
   * Configure comprehensive security headers
   */
  getSecurityHeaders() {
    return helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          objectSrc: ["'none'"],
          upgradeInsecureRequests: [],
        },
      },
      crossOriginEmbedderPolicy: false, // Disable for API compatibility
      hsts: {
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true
      },
      noSniff: true,
      frameguard: { action: 'deny' },
      xssFilter: true,
      referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
      permittedCrossDomainPolicies: false,
      dnsPrefetchControl: { allow: false },
      ieNoOpen: true,
      hidePoweredBy: true
    });
  }

  /**
   * Advanced rate limiting with adaptive thresholds
   */
  getAdvancedRateLimit() {
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: async (req) => {
        // Adaptive rate limiting based on user type and endpoint
        const userType = req.user?.role || 'anonymous';
        const endpoint = req.route?.path || req.path;
        
        // Different limits for different user types
        const limits = {
          admin: 1000,
          verified: 500,
          user: 200,
          anonymous: 50
        };

        // Stricter limits for sensitive endpoints
        if (this.isSensitiveEndpoint(endpoint)) {
          return Math.floor(limits[userType] * 0.2);
        }

        return limits[userType];
      },
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
      store: new rateLimit.MemoryStore(),
      keyGenerator: (req) => {
        // Use combination of IP and user ID for authenticated users
        return req.user ? `${req.ip}-${req.user.id}` : req.ip;
      },
      onLimitReached: (req, res, options) => {
        this.securityMetrics.rateLimitHits++;
        logger.warn('Rate limit exceeded', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.path,
          user: req.user?.id
        });
        
        // Track suspicious activity
        this.trackSuspiciousActivity(req, 'rate_limit_exceeded');
      }
    });
  }

  /**
   * Slow down middleware for brute force protection
   */
  getBruteForceProtection() {
    return slowDown({
      windowMs: 15 * 60 * 1000, // 15 minutes
      delayAfter: 5, // Allow 5 requests per windowMs without delay
      delayMs: 500, // Add 500ms delay per request after delayAfter
      maxDelayMs: 20000, // Maximum delay of 20 seconds
      onLimitReached: (req, res, options) => {
        this.securityMetrics.bruteForceAttempts++;
        logger.warn('Brute force protection triggered', {
          ip: req.ip,
          endpoint: req.path
        });
      }
    });
  }

  /**
   * Advanced input sanitization middleware
   */
  getInputSanitization() {
    return (req, res, next) => {
      try {
        // Sanitize against NoSQL injection
        mongoSanitize()(req, res, () => {});

        // Sanitize against parameter pollution
        hpp()(req, res, () => {});

        // Custom sanitization for all input fields
        this.sanitizeObject(req.body);
        this.sanitizeObject(req.query);
        this.sanitizeObject(req.params);

        // Check for suspicious patterns
        this.detectSuspiciousPatterns(req);

        next();
      } catch (error) {
        logger.error('Error in input sanitization:', error);
        res.status(400).json({
          success: false,
          error: 'Invalid input detected'
        });
      }
    };
  }

  /**
   * Recursively sanitize object properties
   */
  sanitizeObject(obj) {
    if (!obj || typeof obj !== 'object') return;

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'string') {
          // XSS protection
          obj[key] = xss(obj[key], {
            whiteList: {}, // No HTML tags allowed
            stripIgnoreTag: true,
            stripIgnoreTagBody: ['script']
          });

          // Additional sanitization
          obj[key] = validator.escape(obj[key]);
          
          // Remove null bytes
          obj[key] = obj[key].replace(/\0/g, '');
          
        } else if (typeof obj[key] === 'object') {
          this.sanitizeObject(obj[key]);
        }
      }
    }
  }

  /**
   * Detect suspicious patterns in requests
   */
  detectSuspiciousPatterns(req) {
    const checkString = JSON.stringify({
      body: req.body,
      query: req.query,
      params: req.params,
      headers: req.headers
    });

    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(checkString)) {
        this.securityMetrics.suspiciousActivity++;
        
        if (pattern.source.includes('script|javascript|vbscript')) {
          this.securityMetrics.xssAttempts++;
        }
        
        if (pattern.source.includes('union|select|insert')) {
          this.securityMetrics.sqlInjectionAttempts++;
        }

        logger.warn('Suspicious pattern detected', {
          pattern: pattern.source,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.path,
          user: req.user?.id
        });

        this.trackSuspiciousActivity(req, 'suspicious_pattern', pattern.source);
        break;
      }
    }
  }

  /**
   * Track suspicious activity for threat intelligence
   */
  async trackSuspiciousActivity(req, type, details = '') {
    try {
      const activity = {
        type,
        details,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.path,
        method: req.method,
        timestamp: new Date().toISOString(),
        user: req.user?.id,
        headers: req.headers,
        body: req.body ? JSON.stringify(req.body).substring(0, 1000) : null
      };

      // Store in Redis for real-time monitoring
      await this.redisClient.lpush('security:suspicious_activity', JSON.stringify(activity));
      await this.redisClient.ltrim('security:suspicious_activity', 0, 999); // Keep last 1000 entries

      // Check if IP should be temporarily blocked
      await this.checkForIPBlocking(req.ip, type);

    } catch (error) {
      logger.error('Error tracking suspicious activity:', error);
    }
  }

  /**
   * Check if IP should be temporarily blocked
   */
  async checkForIPBlocking(ip, activityType) {
    try {
      const key = `security:ip_activity:${ip}`;
      const current = await this.redisClient.get(key);
      const count = current ? parseInt(current) + 1 : 1;

      await this.redisClient.setex(key, 3600, count); // 1 hour window

      // Block IP if too many suspicious activities
      if (count >= 10) {
        await this.blockIP(ip, 'Excessive suspicious activity');
      }
    } catch (error) {
      logger.error('Error checking IP for blocking:', error);
    }
  }

  /**
   * Block IP address temporarily
   */
  async blockIP(ip, reason) {
    try {
      const blockKey = `security:blocked_ip:${ip}`;
      const blockData = {
        reason,
        timestamp: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      };

      await this.redisClient.setex(blockKey, 24 * 60 * 60, JSON.stringify(blockData));
      
      logger.warn(`IP blocked: ${ip}`, blockData);
      
      // Alert security team
      this.alertSecurityTeam('ip_blocked', { ip, reason });
      
    } catch (error) {
      logger.error('Error blocking IP:', error);
    }
  }

  /**
   * Middleware to check if IP is blocked
   */
  getIPBlockingMiddleware() {
    return async (req, res, next) => {
      try {
        const blockKey = `security:blocked_ip:${req.ip}`;
        const blocked = await this.redisClient.get(blockKey);
        
        if (blocked) {
          const blockData = JSON.parse(blocked);
          this.securityMetrics.blockedRequests++;
          
          return res.status(403).json({
            success: false,
            error: 'Access denied',
            message: 'Your IP address has been temporarily blocked due to suspicious activity'
          });
        }
        
        next();
      } catch (error) {
        logger.error('Error checking IP blocking:', error);
        next(); // Continue on error to avoid blocking legitimate users
      }
    };
  }

  /**
   * Check if endpoint is sensitive
   */
  isSensitiveEndpoint(endpoint) {
    const sensitivePatterns = [
      /\/auth\//,
      /\/wallet\//,
      /\/trade\//,
      /\/admin\//,
      /\/kyc\//,
      /\/password/,
      /\/reset/,
      /\/verify/
    ];

    return sensitivePatterns.some(pattern => pattern.test(endpoint));
  }

  /**
   * Alert security team
   */
  alertSecurityTeam(type, data) {
    // This would integrate with alerting systems like PagerDuty, Slack, etc.
    logger.error(`Security Alert: ${type}`, data);
    
    // Store alert for dashboard
    this.redisClient.lpush('security:alerts', JSON.stringify({
      type,
      data,
      timestamp: new Date().toISOString()
    }));
  }

  /**
   * Get security metrics
   */
  getSecurityMetrics() {
    return {
      ...this.securityMetrics,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Security audit middleware
   */
  getSecurityAuditMiddleware() {
    return (req, res, next) => {
      // Log all requests to sensitive endpoints
      if (this.isSensitiveEndpoint(req.path)) {
        logger.info('Security audit log', {
          ip: req.ip,
          method: req.method,
          endpoint: req.path,
          userAgent: req.get('User-Agent'),
          user: req.user?.id,
          timestamp: new Date().toISOString()
        });
      }
      
      next();
    };
  }
}

module.exports = new AdvancedSecurityService();
