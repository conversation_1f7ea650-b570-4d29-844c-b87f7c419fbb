const logger = require('./utils/logger');

/**
 * User Feedback System Component
 * Allows users to report issues and provide feedback
 */

import React, { useState } from 'react';
import {
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  Box,
  Typography,
  Chip,
  Alert,
  Slide,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Feedback,
  Close,
  Send,
  BugReport,
  Lightbulb,
  ThumbUp,
  ThumbDown
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import crashReporting from '../services/crashReporting';
import performanceMonitor from '../utils/performanceMonitor';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const FeedbackSystem = () => {
  const [open, setOpen] = useState(false);
  const [feedback, setFeedback] = useState({
    type: 'bug',
    severity: 'medium',
    title: '',
    description: '',
    rating: 0,
    email: '',
    includeSystemInfo: true
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  const feedbackTypes = [
    { value: 'bug', label: 'Bug Report', icon: <BugReport />, color: 'error' },
    { value: 'feature', label: 'Feature Request', icon: <Lightbulb />, color: 'info' },
    { value: 'improvement', label: 'Improvement', icon: <ThumbUp />, color: 'success' },
    { value: 'complaint', label: 'Complaint', icon: <ThumbDown />, color: 'warning' },
    { value: 'general', label: 'General Feedback', icon: <Feedback />, color: 'primary' }
  ];

  const severityLevels = [
    { value: 'low', label: 'Low', color: 'success' },
    { value: 'medium', label: 'Medium', color: 'warning' },
    { value: 'high', label: 'High', color: 'error' },
    { value: 'critical', label: 'Critical', color: 'error' }
  ];

  const handleOpen = () => {
    setOpen(true);
    performanceMonitor.recordUserInteraction('feedback_opened', 'feedback_fab');
  };

  const handleClose = () => {
    setOpen(false);
    setShowSuccess(false);
    // Reset form after a delay
    setTimeout(() => {
      setFeedback({
        type: 'bug',
        severity: 'medium',
        title: '',
        description: '',
        rating: 0,
        email: '',
        includeSystemInfo: true
      });
    }, 300);
  };

  const handleInputChange = (field) => (event) => {
    setFeedback(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleRatingChange = (event, newValue) => {
    setFeedback(prev => ({
      ...prev,
      rating: newValue
    }));
  };

  const collectSystemInfo = () => {
    if (!feedback.includeSystemInfo) return null;

    return {
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      performance: performanceMonitor.getMetrics(),
      memory: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
      } : null,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink
      } : null
    };
  };

  const validateForm = () => {
    if (!feedback.title.trim()) {
      enqueueSnackbar('Please provide a title for your feedback', { variant: 'warning' });
      return false;
    }
    if (!feedback.description.trim()) {
      enqueueSnackbar('Please provide a description', { variant: 'warning' });
      return false;
    }
    if (feedback.description.length < 10) {
      enqueueSnackbar('Please provide a more detailed description', { variant: 'warning' });
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const feedbackData = {
        ...feedback,
        systemInfo: collectSystemInfo(),
        submittedAt: new Date().toISOString(),
        userId: localStorage.getItem('admin_user_id'),
        sessionId: crashReporting.sessionId
      };

      // Submit to crash reporting service
      crashReporting.captureUserFeedback(feedbackData);

      // Also submit to feedback API
      const response = await fetch('/api/admin/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify(feedbackData)
      });

      if (!response.ok) {
        throw new Error(`Failed to submit feedback: ${response.status}`);
      }

      setShowSuccess(true);
      enqueueSnackbar('Thank you for your feedback!', { variant: 'success' });
      
      // Record successful submission
      performanceMonitor.recordUserInteraction('feedback_submitted', feedback.type);

      // Auto-close after success
      setTimeout(() => {
        handleClose();
      }, 2000);

    } catch (error) {
      // logger.error('Failed to submit feedback:', error);
      enqueueSnackbar('Failed to submit feedback. Please try again.', { variant: 'error' });
      
      // Record error
      crashReporting.captureError({
        type: 'feedback_submission_error',
        message: error.message,
        context: { feedback: feedback.type },
        timestamp: Date.now()
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedType = feedbackTypes.find(type => type.value === feedback.type);
  const selectedSeverity = severityLevels.find(level => level.value === feedback.severity);

  return (
    <>
      {/* Floating Action Button */}
      <Tooltip title="Send Feedback" placement="left">
        <Fab
          color="primary"
          aria-label="feedback"
          onClick={handleOpen}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: 1000
          }}
        >
          <Feedback />
        </Fab>
      </Tooltip>

      {/* Feedback Dialog */}
      <Dialog
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: 500 }
        }}
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Feedback color="primary" />
            <Typography variant="h6">Send Feedback</Typography>
          </Box>
          <IconButton onClick={handleClose} size="small">
            <Close />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers>
          {showSuccess ? (
            <Alert severity="success" sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Thank you for your feedback!
              </Typography>
              <Typography>
                Your feedback has been submitted successfully. We appreciate your input and will review it carefully.
              </Typography>
            </Alert>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              {/* Feedback Type */}
              <FormControl fullWidth>
                <InputLabel>Feedback Type</InputLabel>
                <Select
                  value={feedback.type}
                  onChange={handleInputChange('type')}
                  label="Feedback Type"
                >
                  {feedbackTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {type.icon}
                        {type.label}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Severity (for bugs and complaints) */}
              {(feedback.type === 'bug' || feedback.type === 'complaint') && (
                <FormControl fullWidth>
                  <InputLabel>Severity</InputLabel>
                  <Select
                    value={feedback.severity}
                    onChange={handleInputChange('severity')}
                    label="Severity"
                  >
                    {severityLevels.map((level) => (
                      <MenuItem key={level.value} value={level.value}>
                        <Chip
                          label={level.label}
                          color={level.color}
                          size="small"
                          variant="outlined"
                        />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}

              {/* Title */}
              <TextField
                fullWidth
                label="Title"
                value={feedback.title}
                onChange={handleInputChange('title')}
                placeholder={`Brief ${selectedType?.label.toLowerCase()} title`}
                required
              />

              {/* Description */}
              <TextField
                fullWidth
                label="Description"
                value={feedback.description}
                onChange={handleInputChange('description')}
                placeholder="Please provide detailed information about your feedback..."
                multiline
                rows={4}
                required
              />

              {/* Rating (for general feedback) */}
              {feedback.type === 'general' && (
                <Box>
                  <Typography component="legend" gutterBottom>
                    Overall Rating
                  </Typography>
                  <Rating
                    value={feedback.rating}
                    onChange={handleRatingChange}
                    size="large"
                  />
                </Box>
              )}

              {/* Email (optional) */}
              <TextField
                fullWidth
                label="Email (optional)"
                value={feedback.email}
                onChange={handleInputChange('email')}
                placeholder="<EMAIL>"
                type="email"
                helperText="Provide your email if you'd like us to follow up"
              />

              {/* System Info Toggle */}
              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Include system information to help us diagnose issues
                </Typography>
                <Chip
                  label={feedback.includeSystemInfo ? 'System info included' : 'System info excluded'}
                  color={feedback.includeSystemInfo ? 'success' : 'default'}
                  onClick={() => setFeedback(prev => ({ ...prev, includeSystemInfo: !prev.includeSystemInfo }))}
                  clickable
                  variant="outlined"
                />
              </Box>

              {/* Current Context */}
              <Box sx={{ bgcolor: 'grey.50', p: 2, borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Current Context:
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Page: {window.location.pathname}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Type: {selectedType?.label}
                </Typography>
                {selectedSeverity && (
                  <Typography variant="body2" color="text.secondary">
                    Severity: {selectedSeverity.label}
                  </Typography>
                )}
              </Box>
            </Box>
          )}
        </DialogContent>

        {!showSuccess && (
          <DialogActions sx={{ p: 3 }}>
            <Button onClick={handleClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              startIcon={<Send />}
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
            </Button>
          </DialogActions>
        )}
      </Dialog>
    </>
  );
};

export default FeedbackSystem;
