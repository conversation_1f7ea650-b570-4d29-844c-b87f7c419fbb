/**
 * Enhanced Performance Monitoring Service
 * Comprehensive performance monitoring with alerting for production environments
 */

const logger = require('../utils/logger');
const { monitoringIntegrationService } = require('./monitoringIntegrationService');

class PerformanceMonitoringService {
  constructor() {
    this.isInitialized = false;
    this.monitoringIntervals = new Map();
    
    // Performance metrics storage
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byMethod: new Map(),
        byRoute: new Map(),
        byStatusCode: new Map(),
        responseTimeHistory: []
      },
      responseTime: {
        samples: [],
        current: 0,
        average: 0,
        p50: 0,
        p95: 0,
        p99: 0,
        min: Infinity,
        max: 0
      },
      errors: {
        total: 0,
        rate: 0,
        byType: new Map(),
        recent: [],
        criticalErrors: 0
      },
      system: {
        memory: {
          heapUsed: 0,
          heapTotal: 0,
          external: 0,
          rss: 0,
          maxHeapUsed: 0
        },
        cpu: {
          user: 0,
          system: 0
        },
        activeConnections: 0,
        uptime: 0
      },
      database: {
        queryCount: 0,
        slowQueries: 0,
        averageQueryTime: 0,
        connectionPool: {
          active: 0,
          idle: 0,
          total: 0
        }
      },
      cache: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        size: 0
      }
    };

    // Alert thresholds for performance monitoring
    this.alertThresholds = {
      responseTime: {
        average: 1000,      // 1 second average
        p95: 2000,          // 2 seconds 95th percentile
        p99: 5000,          // 5 seconds 99th percentile
        critical: 10000     // 10 seconds critical
      },
      errorRate: {
        warning: 0.02,      // 2% warning
        critical: 0.05,     // 5% critical
        severe: 0.10        // 10% severe
      },
      memory: {
        warning: 0.80,      // 80% of heap limit
        critical: 0.90,     // 90% of heap limit
        severe: 0.95        // 95% of heap limit
      },
      cpu: {
        warning: 70,        // 70% CPU usage
        critical: 85,       // 85% CPU usage
        severe: 95          // 95% CPU usage
      },
      database: {
        slowQueryThreshold: 1000,  // 1 second
        slowQueryRate: 0.05,       // 5% slow queries
        connectionPoolWarning: 0.80 // 80% pool utilization
      },
      cache: {
        hitRateWarning: 0.70,      // 70% hit rate warning
        hitRateCritical: 0.50      // 50% hit rate critical
      }
    };

    // Monitoring intervals (in milliseconds)
    this.monitoringIntervals = {
      responseTime: 30000,    // 30 seconds
      errorRate: 60000,       // 1 minute
      systemMetrics: 30000,   // 30 seconds
      databaseMetrics: 60000, // 1 minute
      cacheMetrics: 30000,    // 30 seconds
      alertCheck: 60000       // 1 minute
    };

    // Sample windows for calculations
    this.sampleWindows = {
      responseTime: 1000,     // Keep last 1000 samples
      errors: 100,            // Keep last 100 errors
      systemMetrics: 120      // Keep last 2 hours (30s intervals)
    };

    // Alert cooldowns to prevent spam
    this.alertCooldowns = new Map();
    this.cooldownPeriod = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Initialize the performance monitoring service
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        logger.warn('Performance monitoring service already initialized');
        return true;
      }

      logger.info('Initializing Performance Monitoring Service...');

      // Start monitoring intervals
      this.startResponseTimeMonitoring();
      this.startErrorRateMonitoring();
      this.startSystemMetricsMonitoring();
      this.startDatabaseMetricsMonitoring();
      this.startCacheMetricsMonitoring();
      this.startAlertChecking();

      this.isInitialized = true;
      logger.info('✅ Performance Monitoring Service initialized successfully');
      return true;

    } catch (error) {
      logger.error('❌ Failed to initialize Performance Monitoring Service:', error);
      return false;
    }
  }

  /**
   * Record request metrics
   */
  recordRequest(method, route, statusCode, responseTime, error = null) {
    try {
      // Update request counters
      this.metrics.requests.total++;
      
      if (statusCode >= 200 && statusCode < 400) {
        this.metrics.requests.successful++;
      } else {
        this.metrics.requests.failed++;
      }

      // Update method counters
      const methodCount = this.metrics.requests.byMethod.get(method) || 0;
      this.metrics.requests.byMethod.set(method, methodCount + 1);

      // Update route counters
      const routeCount = this.metrics.requests.byRoute.get(route) || 0;
      this.metrics.requests.byRoute.set(route, routeCount + 1);

      // Update status code counters
      const statusCount = this.metrics.requests.byStatusCode.get(statusCode) || 0;
      this.metrics.requests.byStatusCode.set(statusCode, statusCount + 1);

      // Record response time
      this.recordResponseTime(responseTime);

      // Record error if present
      if (error) {
        this.recordError(error, method, route);
      }

      // Log slow requests
      if (responseTime > this.alertThresholds.responseTime.critical) {
        logger.error('Critical slow request detected', {
          method,
          route,
          responseTime,
          statusCode
        });
      } else if (responseTime > this.alertThresholds.responseTime.p99) {
        logger.warn('Very slow request detected', {
          method,
          route,
          responseTime,
          statusCode
        });
      }

    } catch (error) {
      logger.error('Error recording request metrics:', error);
    }
  }

  /**
   * Record response time sample
   */
  recordResponseTime(responseTime) {
    try {
      this.metrics.responseTime.samples.push({
        time: responseTime,
        timestamp: Date.now()
      });

      // Trim samples to window size
      if (this.metrics.responseTime.samples.length > this.sampleWindows.responseTime) {
        this.metrics.responseTime.samples.shift();
      }

      // Update current response time
      this.metrics.responseTime.current = responseTime;

      // Update min/max
      this.metrics.responseTime.min = Math.min(this.metrics.responseTime.min, responseTime);
      this.metrics.responseTime.max = Math.max(this.metrics.responseTime.max, responseTime);

      // Calculate percentiles (done periodically for performance)
      this.calculateResponseTimePercentiles();

    } catch (error) {
      logger.error('Error recording response time:', error);
    }
  }

  /**
   * Record error metrics
   */
  recordError(error, method = 'unknown', route = 'unknown') {
    try {
      this.metrics.errors.total++;

      const errorType = error.constructor.name;
      const errorCount = this.metrics.errors.byType.get(errorType) || 0;
      this.metrics.errors.byType.set(errorType, errorCount + 1);

      // Add to recent errors
      const errorRecord = {
        timestamp: Date.now(),
        type: errorType,
        message: error.message,
        method,
        route,
        stack: error.stack
      };

      this.metrics.errors.recent.push(errorRecord);

      // Trim recent errors to window size
      if (this.metrics.errors.recent.length > this.sampleWindows.errors) {
        this.metrics.errors.recent.shift();
      }

      // Check for critical errors
      if (this.isCriticalError(error)) {
        this.metrics.errors.criticalErrors++;
      }

    } catch (recordError) {
      logger.error('Error recording error metrics:', recordError);
    }
  }

  /**
   * Calculate response time percentiles
   */
  calculateResponseTimePercentiles() {
    try {
      if (this.metrics.responseTime.samples.length === 0) return;

      const times = this.metrics.responseTime.samples
        .map(sample => sample.time)
        .sort((a, b) => a - b);

      const len = times.length;
      
      // Calculate average
      const sum = times.reduce((acc, time) => acc + time, 0);
      this.metrics.responseTime.average = sum / len;

      // Calculate percentiles
      this.metrics.responseTime.p50 = this.getPercentile(times, 0.50);
      this.metrics.responseTime.p95 = this.getPercentile(times, 0.95);
      this.metrics.responseTime.p99 = this.getPercentile(times, 0.99);

    } catch (error) {
      logger.error('Error calculating response time percentiles:', error);
    }
  }

  /**
   * Get percentile value from sorted array
   */
  getPercentile(sortedArray, percentile) {
    const index = Math.ceil(sortedArray.length * percentile) - 1;
    return sortedArray[Math.max(0, index)] || 0;
  }

  /**
   * Check if error is critical
   */
  isCriticalError(error) {
    const criticalErrorTypes = [
      'DatabaseConnectionError',
      'OutOfMemoryError',
      'SecurityError',
      'AuthenticationError',
      'PaymentError',
      'EscrowError'
    ];

    return criticalErrorTypes.includes(error.constructor.name) ||
           error.message.toLowerCase().includes('critical') ||
           error.message.toLowerCase().includes('security') ||
           error.message.toLowerCase().includes('payment') ||
           error.message.toLowerCase().includes('escrow');
  }

  /**
   * Start response time monitoring
   */
  startResponseTimeMonitoring() {
    const interval = setInterval(() => {
      this.monitorResponseTime();
    }, this.monitoringIntervals.responseTime);

    this.monitoringIntervals.set('responseTime', interval);
    logger.debug('Response time monitoring started');
  }

  /**
   * Monitor response time metrics and trigger alerts
   */
  async monitorResponseTime() {
    try {
      const { average, p95, p99 } = this.metrics.responseTime;

      // Check average response time
      if (average > this.alertThresholds.responseTime.average) {
        await this.triggerAlert('HIGH_AVERAGE_RESPONSE_TIME', {
          message: `Average response time is ${average.toFixed(2)}ms (threshold: ${this.alertThresholds.responseTime.average}ms)`,
          severity: average > this.alertThresholds.responseTime.critical ? 'critical' : 'high',
          threshold: this.alertThresholds.responseTime.average,
          current: average,
          details: {
            p95: p95,
            p99: p99,
            sampleCount: this.metrics.responseTime.samples.length
          }
        });
      }

      // Check P95 response time
      if (p95 > this.alertThresholds.responseTime.p95) {
        await this.triggerAlert('HIGH_P95_RESPONSE_TIME', {
          message: `P95 response time is ${p95.toFixed(2)}ms (threshold: ${this.alertThresholds.responseTime.p95}ms)`,
          severity: p95 > this.alertThresholds.responseTime.critical ? 'critical' : 'high',
          threshold: this.alertThresholds.responseTime.p95,
          current: p95,
          details: {
            average: average,
            p99: p99,
            sampleCount: this.metrics.responseTime.samples.length
          }
        });
      }

      // Check P99 response time
      if (p99 > this.alertThresholds.responseTime.p99) {
        await this.triggerAlert('HIGH_P99_RESPONSE_TIME', {
          message: `P99 response time is ${p99.toFixed(2)}ms (threshold: ${this.alertThresholds.responseTime.p99}ms)`,
          severity: 'critical',
          threshold: this.alertThresholds.responseTime.p99,
          current: p99,
          details: {
            average: average,
            p95: p95,
            sampleCount: this.metrics.responseTime.samples.length
          }
        });
      }

    } catch (error) {
      logger.error('Error monitoring response time:', error);
    }
  }

  /**
   * Start error rate monitoring
   */
  startErrorRateMonitoring() {
    const interval = setInterval(() => {
      this.monitorErrorRate();
    }, this.monitoringIntervals.errorRate);

    this.monitoringIntervals.set('errorRate', interval);
    logger.debug('Error rate monitoring started');
  }

  /**
   * Monitor error rate and trigger alerts
   */
  async monitorErrorRate() {
    try {
      const totalRequests = this.metrics.requests.total;
      const totalErrors = this.metrics.requests.failed;
      const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;

      this.metrics.errors.rate = errorRate;

      // Check error rate thresholds
      if (errorRate >= this.alertThresholds.errorRate.severe) {
        await this.triggerAlert('SEVERE_ERROR_RATE', {
          message: `Error rate is ${(errorRate * 100).toFixed(2)}% (threshold: ${(this.alertThresholds.errorRate.severe * 100).toFixed(2)}%)`,
          severity: 'critical',
          threshold: this.alertThresholds.errorRate.severe,
          current: errorRate,
          details: {
            totalRequests,
            totalErrors,
            criticalErrors: this.metrics.errors.criticalErrors,
            recentErrors: this.metrics.errors.recent.slice(-5)
          }
        });
      } else if (errorRate >= this.alertThresholds.errorRate.critical) {
        await this.triggerAlert('CRITICAL_ERROR_RATE', {
          message: `Error rate is ${(errorRate * 100).toFixed(2)}% (threshold: ${(this.alertThresholds.errorRate.critical * 100).toFixed(2)}%)`,
          severity: 'critical',
          threshold: this.alertThresholds.errorRate.critical,
          current: errorRate,
          details: {
            totalRequests,
            totalErrors,
            criticalErrors: this.metrics.errors.criticalErrors
          }
        });
      } else if (errorRate >= this.alertThresholds.errorRate.warning) {
        await this.triggerAlert('HIGH_ERROR_RATE', {
          message: `Error rate is ${(errorRate * 100).toFixed(2)}% (threshold: ${(this.alertThresholds.errorRate.warning * 100).toFixed(2)}%)`,
          severity: 'high',
          threshold: this.alertThresholds.errorRate.warning,
          current: errorRate,
          details: {
            totalRequests,
            totalErrors
          }
        });
      }

      // Check for critical error spikes
      if (this.metrics.errors.criticalErrors > 0) {
        await this.triggerAlert('CRITICAL_ERRORS_DETECTED', {
          message: `${this.metrics.errors.criticalErrors} critical errors detected`,
          severity: 'critical',
          details: {
            criticalErrors: this.metrics.errors.criticalErrors,
            recentErrors: this.metrics.errors.recent.slice(-3)
          }
        });
      }

    } catch (error) {
      logger.error('Error monitoring error rate:', error);
    }
  }

  /**
   * Start system metrics monitoring
   */
  startSystemMetricsMonitoring() {
    const interval = setInterval(() => {
      this.monitorSystemMetrics();
    }, this.monitoringIntervals.systemMetrics);

    this.monitoringIntervals.set('systemMetrics', interval);
    logger.debug('System metrics monitoring started');
  }

  /**
   * Monitor system metrics and trigger alerts
   */
  async monitorSystemMetrics() {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      // Update system metrics
      this.metrics.system.memory = {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
        maxHeapUsed: Math.max(this.metrics.system.memory.maxHeapUsed, memUsage.heapUsed)
      };

      this.metrics.system.cpu = cpuUsage;
      this.metrics.system.uptime = process.uptime();

      // Check memory usage
      const heapUsagePercent = memUsage.heapUsed / memUsage.heapTotal;

      if (heapUsagePercent >= this.alertThresholds.memory.severe) {
        await this.triggerAlert('SEVERE_MEMORY_USAGE', {
          message: `Memory usage is ${(heapUsagePercent * 100).toFixed(2)}% (threshold: ${(this.alertThresholds.memory.severe * 100).toFixed(2)}%)`,
          severity: 'critical',
          threshold: this.alertThresholds.memory.severe,
          current: heapUsagePercent,
          details: {
            heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
            heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`,
            rss: `${(memUsage.rss / 1024 / 1024).toFixed(2)}MB`
          }
        });
      } else if (heapUsagePercent >= this.alertThresholds.memory.critical) {
        await this.triggerAlert('CRITICAL_MEMORY_USAGE', {
          message: `Memory usage is ${(heapUsagePercent * 100).toFixed(2)}% (threshold: ${(this.alertThresholds.memory.critical * 100).toFixed(2)}%)`,
          severity: 'critical',
          threshold: this.alertThresholds.memory.critical,
          current: heapUsagePercent,
          details: {
            heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
            heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`
          }
        });
      } else if (heapUsagePercent >= this.alertThresholds.memory.warning) {
        await this.triggerAlert('HIGH_MEMORY_USAGE', {
          message: `Memory usage is ${(heapUsagePercent * 100).toFixed(2)}% (threshold: ${(this.alertThresholds.memory.warning * 100).toFixed(2)}%)`,
          severity: 'medium',
          threshold: this.alertThresholds.memory.warning,
          current: heapUsagePercent,
          details: {
            heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
            heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`
          }
        });
      }

    } catch (error) {
      logger.error('Error monitoring system metrics:', error);
    }
  }

  /**
   * Start database metrics monitoring
   */
  startDatabaseMetricsMonitoring() {
    const interval = setInterval(() => {
      this.monitorDatabaseMetrics();
    }, this.monitoringIntervals.databaseMetrics);

    this.monitoringIntervals.set('databaseMetrics', interval);
    logger.debug('Database metrics monitoring started');
  }

  /**
   * Monitor database metrics
   */
  async monitorDatabaseMetrics() {
    try {
      // This would integrate with actual database monitoring
      // For now, we'll use placeholder metrics
      const mongoose = require('mongoose');

      if (mongoose.connection.readyState === 1) {
        // Database is connected, collect metrics
        const connectionStats = mongoose.connection.db.stats ? await mongoose.connection.db.stats() : {};

        // Update database metrics (placeholder implementation)
        this.metrics.database.connectionPool = {
          active: mongoose.connection.readyState,
          idle: 0,
          total: 1
        };
      }

    } catch (error) {
      logger.error('Error monitoring database metrics:', error);
    }
  }

  /**
   * Start cache metrics monitoring
   */
  startCacheMetricsMonitoring() {
    const interval = setInterval(() => {
      this.monitorCacheMetrics();
    }, this.monitoringIntervals.cacheMetrics);

    this.monitoringIntervals.set('cacheMetrics', interval);
    logger.debug('Cache metrics monitoring started');
  }

  /**
   * Monitor cache metrics
   */
  async monitorCacheMetrics() {
    try {
      // Calculate cache hit rate
      const totalCacheRequests = this.metrics.cache.hits + this.metrics.cache.misses;
      const hitRate = totalCacheRequests > 0 ? this.metrics.cache.hits / totalCacheRequests : 0;

      this.metrics.cache.hitRate = hitRate;

      // Check cache performance
      if (hitRate < this.alertThresholds.cache.hitRateCritical) {
        await this.triggerAlert('CRITICAL_CACHE_HIT_RATE', {
          message: `Cache hit rate is ${(hitRate * 100).toFixed(2)}% (threshold: ${(this.alertThresholds.cache.hitRateCritical * 100).toFixed(2)}%)`,
          severity: 'critical',
          threshold: this.alertThresholds.cache.hitRateCritical,
          current: hitRate,
          details: {
            hits: this.metrics.cache.hits,
            misses: this.metrics.cache.misses,
            totalRequests: totalCacheRequests
          }
        });
      } else if (hitRate < this.alertThresholds.cache.hitRateWarning) {
        await this.triggerAlert('LOW_CACHE_HIT_RATE', {
          message: `Cache hit rate is ${(hitRate * 100).toFixed(2)}% (threshold: ${(this.alertThresholds.cache.hitRateWarning * 100).toFixed(2)}%)`,
          severity: 'medium',
          threshold: this.alertThresholds.cache.hitRateWarning,
          current: hitRate,
          details: {
            hits: this.metrics.cache.hits,
            misses: this.metrics.cache.misses
          }
        });
      }

    } catch (error) {
      logger.error('Error monitoring cache metrics:', error);
    }
  }

  /**
   * Start alert checking
   */
  startAlertChecking() {
    const interval = setInterval(() => {
      this.checkAlerts();
    }, this.monitoringIntervals.alertCheck);

    this.monitoringIntervals.set('alertCheck', interval);
    logger.debug('Alert checking started');
  }

  /**
   * Check for alert conditions
   */
  async checkAlerts() {
    try {
      // This method can be used for complex alert conditions
      // that require multiple metrics or time-based analysis

      // Example: Check for sustained high error rate
      const recentErrors = this.metrics.errors.recent.filter(
        error => Date.now() - error.timestamp < 5 * 60 * 1000 // Last 5 minutes
      );

      if (recentErrors.length > 10) {
        await this.triggerAlert('SUSTAINED_HIGH_ERROR_RATE', {
          message: `${recentErrors.length} errors in the last 5 minutes`,
          severity: 'high',
          details: {
            recentErrorCount: recentErrors.length,
            timeWindow: '5 minutes'
          }
        });
      }

    } catch (error) {
      logger.error('Error checking alerts:', error);
    }
  }

  /**
   * Trigger performance alert
   */
  async triggerAlert(alertType, alertData) {
    try {
      // Check cooldown
      const lastAlert = this.alertCooldowns.get(alertType);
      if (lastAlert && Date.now() - lastAlert < this.cooldownPeriod) {
        return; // Skip alert due to cooldown
      }

      // Update cooldown
      this.alertCooldowns.set(alertType, Date.now());

      // Send alert through monitoring integration service
      await monitoringIntegrationService.triggerAlert(alertType, {
        ...alertData,
        source: 'performance_monitoring',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString()
      });

      logger.warn(`Performance alert triggered: ${alertType}`, alertData);

    } catch (error) {
      logger.error('Error triggering performance alert:', error);
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    try {
      return {
        requests: {
          total: this.metrics.requests.total,
          successful: this.metrics.requests.successful,
          failed: this.metrics.requests.failed,
          successRate: this.metrics.requests.total > 0 ?
            (this.metrics.requests.successful / this.metrics.requests.total) : 0,
          errorRate: this.metrics.errors.rate,
          byMethod: Object.fromEntries(this.metrics.requests.byMethod),
          byRoute: Object.fromEntries(this.metrics.requests.byRoute),
          byStatusCode: Object.fromEntries(this.metrics.requests.byStatusCode)
        },
        responseTime: {
          current: this.metrics.responseTime.current,
          average: this.metrics.responseTime.average,
          p50: this.metrics.responseTime.p50,
          p95: this.metrics.responseTime.p95,
          p99: this.metrics.responseTime.p99,
          min: this.metrics.responseTime.min === Infinity ? 0 : this.metrics.responseTime.min,
          max: this.metrics.responseTime.max,
          sampleCount: this.metrics.responseTime.samples.length
        },
        errors: {
          total: this.metrics.errors.total,
          rate: this.metrics.errors.rate,
          criticalErrors: this.metrics.errors.criticalErrors,
          byType: Object.fromEntries(this.metrics.errors.byType),
          recentCount: this.metrics.errors.recent.length
        },
        system: {
          memory: {
            heapUsed: `${(this.metrics.system.memory.heapUsed / 1024 / 1024).toFixed(2)}MB`,
            heapTotal: `${(this.metrics.system.memory.heapTotal / 1024 / 1024).toFixed(2)}MB`,
            heapUsagePercent: this.metrics.system.memory.heapTotal > 0 ?
              ((this.metrics.system.memory.heapUsed / this.metrics.system.memory.heapTotal) * 100).toFixed(2) : 0,
            rss: `${(this.metrics.system.memory.rss / 1024 / 1024).toFixed(2)}MB`,
            external: `${(this.metrics.system.memory.external / 1024 / 1024).toFixed(2)}MB`
          },
          uptime: this.metrics.system.uptime,
          activeConnections: this.metrics.system.activeConnections
        },
        database: this.metrics.database,
        cache: this.metrics.cache,
        isInitialized: this.isInitialized,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error getting performance metrics:', error);
      return null;
    }
  }

  /**
   * Record cache hit
   */
  recordCacheHit() {
    this.metrics.cache.hits++;
  }

  /**
   * Record cache miss
   */
  recordCacheMiss() {
    this.metrics.cache.misses++;
  }

  /**
   * Update active connections count
   */
  updateActiveConnections(count) {
    this.metrics.system.activeConnections = count;
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    try {
      this.metrics = {
        requests: {
          total: 0,
          successful: 0,
          failed: 0,
          byMethod: new Map(),
          byRoute: new Map(),
          byStatusCode: new Map(),
          responseTimeHistory: []
        },
        responseTime: {
          samples: [],
          current: 0,
          average: 0,
          p50: 0,
          p95: 0,
          p99: 0,
          min: Infinity,
          max: 0
        },
        errors: {
          total: 0,
          rate: 0,
          byType: new Map(),
          recent: [],
          criticalErrors: 0
        },
        system: {
          memory: {
            heapUsed: 0,
            heapTotal: 0,
            external: 0,
            rss: 0,
            maxHeapUsed: 0
          },
          cpu: {
            user: 0,
            system: 0
          },
          activeConnections: 0,
          uptime: 0
        },
        database: {
          queryCount: 0,
          slowQueries: 0,
          averageQueryTime: 0,
          connectionPool: {
            active: 0,
            idle: 0,
            total: 0
          }
        },
        cache: {
          hits: 0,
          misses: 0,
          hitRate: 0,
          size: 0
        }
      };

      logger.info('Performance metrics reset');
    } catch (error) {
      logger.error('Error resetting performance metrics:', error);
    }
  }

  /**
   * Shutdown the performance monitoring service
   */
  async shutdown() {
    try {
      logger.info('Shutting down Performance Monitoring Service...');

      // Clear all monitoring intervals
      for (const [name, interval] of this.monitoringIntervals) {
        clearInterval(interval);
        logger.debug(`Stopped ${name} monitoring interval`);
      }

      this.monitoringIntervals.clear();
      this.isInitialized = false;

      logger.info('✅ Performance Monitoring Service shutdown completed');
    } catch (error) {
      logger.error('❌ Error shutting down Performance Monitoring Service:', error);
    }
  }
}

// Create singleton instance
const performanceMonitoringService = new PerformanceMonitoringService();

module.exports = {
  performanceMonitoringService
};
