/**
 * Circuit Breaker Pattern Implementation
 * Prevents cascading failures by monitoring service health
 */

const logger = require('./logger');

class CircuitBreaker {
  constructor(options = {}) {
    this.name = options.name || 'CircuitBreaker';
    this.failureThreshold = options.failureThreshold || 5;
    this.timeout = options.timeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 seconds
    this.expectedErrors = options.expectedErrors || [];
    
    // State management
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.successCount = 0;
    this.nextAttempt = Date.now();
    this.lastFailureTime = null;
    
    // Metrics
    this.metrics = {
      totalRequests: 0,
      totalFailures: 0,
      totalSuccesses: 0,
      totalTimeouts: 0,
      averageResponseTime: 0,
      lastResetTime: Date.now()
    };

    // Start monitoring
    this.startMonitoring();
  }

  async execute(operation, fallback = null) {
    this.metrics.totalRequests++;
    
    if (this.state === 'OPEN') {
      if (Date.now() < this.nextAttempt) {
        logger.warn(`Circuit breaker ${this.name} is OPEN, rejecting request`);
        
        if (fallback && typeof fallback === 'function') {
          return await fallback();
        }
        
        throw new Error(`Circuit breaker ${this.name} is OPEN`);
      } else {
        this.state = 'HALF_OPEN';
        logger.info(`Circuit breaker ${this.name} entering HALF_OPEN state`);
      }
    }

    const startTime = Date.now();
    
    try {
      const result = await this.executeWithTimeout(operation);
      this.onSuccess(Date.now() - startTime);
      return result;
    } catch (error) {
      this.onFailure(error, Date.now() - startTime);
      throw error;
    }
  }

  async executeWithTimeout(operation) {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.metrics.totalTimeouts++;
        reject(new Error(`Operation timeout after ${this.timeout}ms`));
      }, this.timeout);

      try {
        const result = await operation();
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  onSuccess(responseTime) {
    this.metrics.totalSuccesses++;
    this.updateAverageResponseTime(responseTime);
    
    if (this.state === 'HALF_OPEN') {
      this.successCount++;
      
      // If we have enough successes, close the circuit
      if (this.successCount >= Math.ceil(this.failureThreshold / 2)) {
        this.reset();
        logger.info(`Circuit breaker ${this.name} reset to CLOSED state after successful recovery`);
      }
    } else {
      this.failureCount = Math.max(0, this.failureCount - 1);
    }
  }

  onFailure(error, responseTime) {
    this.metrics.totalFailures++;
    this.updateAverageResponseTime(responseTime);
    this.lastFailureTime = Date.now();
    
    // Check if this is an expected error that shouldn't trigger circuit breaker
    if (this.isExpectedError(error)) {
      logger.debug(`Circuit breaker ${this.name} ignoring expected error:`, error.message);
      return;
    }

    this.failureCount++;
    this.successCount = 0; // Reset success count on any failure
    
    logger.warn(`Circuit breaker ${this.name} failure ${this.failureCount}/${this.failureThreshold}:`, {
      error: error.message,
      state: this.state
    });

    if (this.failureCount >= this.failureThreshold) {
      this.trip();
    }
  }

  trip() {
    this.state = 'OPEN';
    this.nextAttempt = Date.now() + this.timeout;
    
    logger.error(`Circuit breaker ${this.name} TRIPPED - entering OPEN state for ${this.timeout}ms`, {
      failureCount: this.failureCount,
      threshold: this.failureThreshold,
      nextAttempt: new Date(this.nextAttempt).toISOString()
    });
  }

  reset() {
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.successCount = 0;
    this.nextAttempt = 0;
  }

  isExpectedError(error) {
    return this.expectedErrors.some(expectedError => {
      if (typeof expectedError === 'string') {
        return error.message.includes(expectedError);
      }
      if (expectedError instanceof RegExp) {
        return expectedError.test(error.message);
      }
      if (typeof expectedError === 'function') {
        return expectedError(error);
      }
      return false;
    });
  }

  updateAverageResponseTime(responseTime) {
    const totalRequests = this.metrics.totalRequests;
    this.metrics.averageResponseTime = 
      ((this.metrics.averageResponseTime * (totalRequests - 1)) + responseTime) / totalRequests;
  }

  startMonitoring() {
    this.monitoringInterval = setInterval(() => {
      this.logMetrics();

      // Auto-reset if we've been open for too long without attempts
      if (this.state === 'OPEN' && Date.now() > this.nextAttempt + (this.timeout * 2)) {
        logger.info(`Circuit breaker ${this.name} auto-resetting after extended open period`);
        this.reset();
      }
    }, this.monitoringPeriod);
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  logMetrics() {
    if (this.metrics.totalRequests > 0) {
      const successRate = (this.metrics.totalSuccesses / this.metrics.totalRequests * 100).toFixed(2);
      const failureRate = (this.metrics.totalFailures / this.metrics.totalRequests * 100).toFixed(2);
      
      logger.info(`Circuit breaker ${this.name} metrics:`, {
        state: this.state,
        totalRequests: this.metrics.totalRequests,
        successRate: `${successRate}%`,
        failureRate: `${failureRate}%`,
        averageResponseTime: `${Math.round(this.metrics.averageResponseTime)}ms`,
        currentFailures: this.failureCount
      });
    }
  }

  getState() {
    return {
      name: this.name,
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      nextAttempt: this.nextAttempt,
      lastFailureTime: this.lastFailureTime,
      metrics: { ...this.metrics }
    };
  }

  // Manual controls for testing/admin
  forceOpen() {
    this.state = 'OPEN';
    this.nextAttempt = Date.now() + this.timeout;
    logger.warn(`Circuit breaker ${this.name} manually forced OPEN`);
  }

  forceClose() {
    this.reset();
    logger.info(`Circuit breaker ${this.name} manually forced CLOSED`);
  }
}

// Factory function for creating circuit breakers with common configurations
function createCircuitBreaker(name, options = {}) {
  const defaultOptions = {
    name,
    failureThreshold: 5,
    timeout: 60000,
    monitoringPeriod: 30000
  };

  return new CircuitBreaker({ ...defaultOptions, ...options });
}

// Pre-configured circuit breakers for common services
const circuitBreakers = {
  database: createCircuitBreaker('Database', {
    failureThreshold: 3,
    timeout: 30000,
    expectedErrors: ['ValidationError', 'CastError']
  }),
  
  blockchain: createCircuitBreaker('Blockchain', {
    failureThreshold: 5,
    timeout: 120000,
    expectedErrors: ['insufficient funds', 'nonce too low']
  }),
  
  redis: createCircuitBreaker('Redis', {
    failureThreshold: 3,
    timeout: 10000
  }),
  
  external: createCircuitBreaker('ExternalAPI', {
    failureThreshold: 5,
    timeout: 30000
  })
};

// Function to stop all circuit breaker monitoring
function stopAllCircuitBreakers() {
  Object.values(circuitBreakers).forEach(cb => {
    cb.stopMonitoring();
  });
}

module.exports = {
  CircuitBreaker,
  createCircuitBreaker,
  circuitBreakers,
  stopAllCircuitBreakers
};
