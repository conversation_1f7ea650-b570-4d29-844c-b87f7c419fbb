import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

// Mock active trades data
const mockActiveTrades = [
  {
    id: '1',
    type: 'buy', // from current user's perspective
    status: 'payment_sent',
    cryptoCurrency: 'BTC',
    fiatCurrency: 'USD',
    cryptoAmount: 0.0025,
    fiatAmount: 125.5,
    price: 50200,
    paymentMethod: 'Bank Transfer',
    counterparty: {
      id: '2',
      name: 'Alice Trader',
      rating: 4.8,
      completedTrades: 42,
    },
    createdAt: '2023-07-05T17:30:00Z',
    timeRemaining: 25, // minutes
    lastActivity: '5 minutes ago',
    unreadMessages: 2,
  },
  {
    id: '2',
    type: 'sell',
    status: 'pending_payment',
    cryptoCurrency: 'ETH',
    fiatCurrency: 'USD',
    cryptoAmount: 0.5,
    fiatAmount: 950.0,
    price: 1900,
    paymentMethod: 'PayPal',
    counterparty: {
      id: '3',
      name: 'Bob Crypto',
      rating: 4.6,
      completedTrades: 28,
    },
    createdAt: '2023-07-05T16:45:00Z',
    timeRemaining: 40,
    lastActivity: '12 minutes ago',
    unreadMessages: 0,
  },
  {
    id: '3',
    type: 'buy',
    status: 'escrow_pending',
    cryptoCurrency: 'USDT',
    fiatCurrency: 'USD',
    cryptoAmount: 500,
    fiatAmount: 500.0,
    price: 1.0,
    paymentMethod: 'Zelle',
    counterparty: {
      id: '4',
      name: 'Charlie Exchange',
      rating: 4.9,
      completedTrades: 156,
    },
    createdAt: '2023-07-05T15:20:00Z',
    timeRemaining: 55,
    lastActivity: '2 hours ago',
    unreadMessages: 1,
  },
];

const getStatusColor = (status, theme) => {
  switch (status) {
    case 'pending_payment':
      return theme.colors.warning;
    case 'payment_sent':
      return theme.colors.primary;
    case 'escrow_pending':
      return theme.colors.purple;
    case 'completed':
      return theme.colors.success;
    case 'disputed':
      return theme.colors.error;
    case 'cancelled':
      return theme.colors.textSecondary;
    default:
      return theme.colors.textSecondary;
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 'pending_payment':
      return 'Pending Payment';
    case 'payment_sent':
      return 'Payment Sent';
    case 'escrow_pending':
      return 'Escrow Pending';
    case 'completed':
      return 'Completed';
    case 'disputed':
      return 'Disputed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return status;
  }
};

export default function ActiveTradesScreen() {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'buying', 'selling'

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const filteredTrades = mockActiveTrades.filter(trade => {
    if (filter === 'buying') return trade.type === 'buy';
    if (filter === 'selling') return trade.type === 'sell';
    return true;
  });

  const renderTradeItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.tradeCard}
      onPress={() => navigation.navigate('Trade', { tradeId: item.id })}
    >
      <View style={styles.tradeHeader}>
        <View style={styles.tradeTypeContainer}>
          <Text style={[styles.tradeType, { color: item.type === 'buy' ? theme.colors.success : theme.colors.error }]}>
            {item.type.toUpperCase()}
          </Text>
          <Text style={styles.tradePair}>
            {item.cryptoCurrency}/{item.fiatCurrency}
          </Text>
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status, theme) }]} />
          <Text style={[styles.statusText, { color: getStatusColor(item.status, theme) }]}>
            {getStatusText(item.status)}
          </Text>
        </View>
      </View>

      <View style={styles.tradeDetails}>
        <View style={styles.amountContainer}>
          <Text style={styles.cryptoAmount}>
            {item.cryptoAmount} {item.cryptoCurrency}
          </Text>
          <Text style={styles.fiatAmount}>
            ${item.fiatAmount.toLocaleString()}
          </Text>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>Price</Text>
          <Text style={styles.priceValue}>
            ${item.price.toLocaleString()}
          </Text>
        </View>
      </View>

      <View style={styles.tradeInfo}>
        <View style={styles.counterpartyInfo}>
          <Ionicons name="person-circle-outline" size={20} color={theme.colors.textSecondary} />
          <Text style={styles.counterpartyName}>{item.counterparty.name}</Text>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={theme.colors.warning} />
            <Text style={styles.rating}>{item.counterparty.rating}</Text>
          </View>
        </View>
        <Text style={styles.paymentMethod}>{item.paymentMethod}</Text>
      </View>

      <View style={styles.tradeFooter}>
        <View style={styles.timeInfo}>
          <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
          <Text style={styles.timeRemaining}>{item.timeRemaining}m remaining</Text>
        </View>
        <View style={styles.activityInfo}>
          <Text style={styles.lastActivity}>{item.lastActivity}</Text>
          {item.unreadMessages > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadCount}>{item.unreadMessages}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="swap-horizontal-outline" size={64} color={theme.colors.textTertiary} />
      <Text style={styles.emptyStateTitle}>No Active Trades</Text>
      <Text style={styles.emptyStateText}>
        You don't have any active trades at the moment.
      </Text>
      <TouchableOpacity 
        style={styles.browseOffersButton}
        onPress={() => navigation.navigate('Offers')}
      >
        <Text style={styles.browseOffersButtonText}>Browse Offers</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Active Trades</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <Ionicons name="refresh-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterTab, filter === 'all' && styles.activeFilterTab]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterTabText, filter === 'all' && styles.activeFilterTabText]}>
            All ({mockActiveTrades.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, filter === 'buying' && styles.activeFilterTab]}
          onPress={() => setFilter('buying')}
        >
          <Text style={[styles.filterTabText, filter === 'buying' && styles.activeFilterTabText]}>
            Buying ({mockActiveTrades.filter(t => t.type === 'buy').length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, filter === 'selling' && styles.activeFilterTab]}
          onPress={() => setFilter('selling')}
        >
          <Text style={[styles.filterTabText, filter === 'selling' && styles.activeFilterTabText]}>
            Selling ({mockActiveTrades.filter(t => t.type === 'sell').length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Trades List */}
      <FlatList
        data={filteredTrades}
        renderItem={renderTradeItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  refreshButton: {
    padding: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  activeFilterTab: {
    backgroundColor: theme.colors.primaryLight,
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  activeFilterTabText: {
    color: theme.colors.primaryDark,
    fontWeight: '600',
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  tradeCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tradeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  tradeTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tradeType: {
    fontSize: 14,
    fontWeight: '700',
  },
  tradePair: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  tradeDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  amountContainer: {
    flex: 1,
  },
  cryptoAmount: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 2,
  },
  fiatAmount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  priceLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  priceValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  tradeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  counterpartyInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    flex: 1,
  },
  counterpartyName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  rating: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  paymentMethod: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    backgroundColor: theme.colors.backgroundSecondary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tradeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeRemaining: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  activityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  lastActivity: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  unreadBadge: {
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 32,
  },
  browseOffersButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseOffersButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});
