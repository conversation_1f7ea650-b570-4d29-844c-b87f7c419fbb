const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const { AppError } = require('../middleware/errorHandler');
const fileSecurityService = require('../services/fileSecurityService');
const Trade = require('../models/Trade');
const Chat = require('../models/Chat');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

const router = express.Router();

/**
 * Secure file access middleware
 */
const verifyFileAccess = async (req, res, next) => {
  try {
    const { fileId } = req.params;
    const userId = req.user._id;

    // Get file record from Redis
    const redisClient = getRedisClient();
    let fileRecord = null;

    if (redisClient && redisClient.isReady) {
      const fileData = await redisClient.get(`file:${fileId}`);
      if (fileData) {
        fileRecord = JSON.parse(fileData);
      }
    }

    if (!fileRecord) {
      throw new AppError('File not found', 404);
    }

    // Check file permissions
    const hasAccess = await checkFilePermissions(fileRecord, userId, req.user.role);
    if (!hasAccess) {
      logger.warn(`Unauthorized file access attempt`, {
        fileId,
        userId,
        userRole: req.user.role,
        fileOwner: fileRecord.uploadedBy,
        ip: req.ip
      });
      
      throw new AppError('Access denied to this file', 403);
    }

    // Log file access
    logger.info(`File accessed`, {
      fileId,
      userId,
      filename: fileRecord.originalName,
      context: fileRecord.context,
      ip: req.ip
    });

    req.fileRecord = fileRecord;
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Check file access permissions
 */
async function checkFilePermissions(fileRecord, userId, userRole) {
  // Admin can access all files
  if (userRole === 'admin' || userRole === 'moderator') {
    return true;
  }

  // File owner can always access
  if (fileRecord.uploadedBy === userId.toString()) {
    return true;
  }

  // Context-specific access control
  switch (fileRecord.context) {
    case 'payment-proofs':
      return await checkTradeParticipantAccess(fileRecord, userId);
    
    case 'chat-attachments':
      return await checkChatParticipantAccess(fileRecord, userId);
    
    case 'documents':
      // Documents are private to owner only
      return false;
    
    default:
      return false;
  }
}

/**
 * Check if user is participant in trade for payment proof access
 */
async function checkTradeParticipantAccess(fileRecord, userId) {
  try {
    // Extract trade ID from file metadata or find trade with this file
    const trade = await Trade.findOne({
      'payment.proofOfPayment': { $regex: fileRecord.id }
    });

    if (!trade) {
      return false;
    }

    return trade.isParticipant(userId);
  } catch (error) {
    logger.error('Error checking trade participant access:', error);
    return false;
  }
}

/**
 * Check if user is participant in chat for attachment access
 */
async function checkChatParticipantAccess(fileRecord, userId) {
  try {
    // Find chat with this attachment
    const chat = await Chat.findOne({
      'messages.attachments': { $regex: fileRecord.id }
    });

    if (!chat) {
      return false;
    }

    return chat.participants.includes(userId);
  } catch (error) {
    logger.error('Error checking chat participant access:', error);
    return false;
  }
}

/**
 * Serve secure file with access control
 */
router.get('/:fileId', authenticate, verifyFileAccess, async (req, res, next) => {
  try {
    const { fileRecord } = req;
    const filePath = path.join(__dirname, '../../uploads/secure', fileRecord.context, fileRecord.filename);

    // Verify file exists
    try {
      await fs.access(filePath);
    } catch (error) {
      logger.error(`File not found on disk: ${filePath}`, {
        fileId: req.params.fileId,
        userId: req.user._id
      });
      throw new AppError('File not found', 404);
    }

    // Verify file integrity
    const isIntegrityValid = await verifyFileIntegrity(filePath, fileRecord.integrity);
    if (!isIntegrityValid) {
      logger.error(`File integrity check failed`, {
        fileId: req.params.fileId,
        userId: req.user._id,
        filePath
      });
      throw new AppError('File integrity check failed', 500);
    }

    // Set security headers
    res.set({
      'Content-Type': getMimeType(fileRecord.originalName),
      'Content-Disposition': `inline; filename="${fileRecord.originalName}"`,
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'Cache-Control': 'private, no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    // Stream file to client
    const fileStream = require('fs').createReadStream(filePath);
    fileStream.pipe(res);

    // Update access statistics
    await updateFileAccessStats(req.params.fileId, req.user._id);

  } catch (error) {
    next(error);
  }
});

/**
 * Get file metadata (for authorized users)
 */
router.get('/:fileId/metadata', authenticate, verifyFileAccess, async (req, res, next) => {
  try {
    const { fileRecord } = req;
    
    // Return sanitized metadata
    const metadata = {
      id: req.params.fileId,
      originalName: fileRecord.originalName,
      size: fileRecord.size,
      uploadedAt: fileRecord.uploadedAt,
      context: fileRecord.context,
      security: {
        scanned: fileRecord.security.scanned,
        scanDate: fileRecord.security.scanDate
      }
    };

    // Include additional metadata for file owner or admin
    if (fileRecord.uploadedBy === req.user._id.toString() || 
        ['admin', 'moderator'].includes(req.user.role)) {
      metadata.integrity = fileRecord.integrity;
      metadata.permissions = fileRecord.permissions;
    }

    res.json({
      success: true,
      data: metadata
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Delete file (owner or admin only)
 */
router.delete('/:fileId', authenticate, verifyFileAccess, async (req, res, next) => {
  try {
    const { fileRecord } = req;
    const userId = req.user._id.toString();

    // Check delete permissions
    if (fileRecord.uploadedBy !== userId && !['admin', 'moderator'].includes(req.user.role)) {
      throw new AppError('Insufficient permissions to delete this file', 403);
    }

    const filePath = path.join(__dirname, '../../uploads/secure', fileRecord.context, fileRecord.filename);

    // Delete file from disk
    try {
      await fs.unlink(filePath);
    } catch (error) {
      logger.warn(`File not found on disk during deletion: ${filePath}`);
    }

    // Remove from Redis
    const redisClient = getRedisClient();
    if (redisClient && redisClient.isReady) {
      await redisClient.del(`file:${req.params.fileId}`);
      await redisClient.sRem(`user_files:${fileRecord.uploadedBy}`, req.params.fileId);
    }

    logger.info(`File deleted`, {
      fileId: req.params.fileId,
      deletedBy: userId,
      originalName: fileRecord.originalName
    });

    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * List user's files
 */
router.get('/user/list', authenticate, async (req, res, next) => {
  try {
    const userId = req.user._id.toString();
    const { context, page = 1, limit = 20 } = req.query;

    const redisClient = getRedisClient();
    let userFiles = [];

    if (redisClient && redisClient.isReady) {
      const fileIds = await redisClient.sMembers(`user_files:${userId}`);
      
      for (const fileId of fileIds) {
        const fileData = await redisClient.get(`file:${fileId}`);
        if (fileData) {
          const fileRecord = JSON.parse(fileData);
          
          // Filter by context if specified
          if (!context || fileRecord.context === context) {
            userFiles.push({
              id: fileId,
              originalName: fileRecord.originalName,
              size: fileRecord.size,
              uploadedAt: fileRecord.uploadedAt,
              context: fileRecord.context,
              url: `/api/files/${fileId}`
            });
          }
        }
      }
    }

    // Sort by upload date (newest first)
    userFiles.sort((a, b) => new Date(b.uploadedAt) - new Date(a.uploadedAt));

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedFiles = userFiles.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        files: paginatedFiles,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: userFiles.length,
          pages: Math.ceil(userFiles.length / limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Utility functions
 */
async function verifyFileIntegrity(filePath, expectedIntegrity) {
  try {
    const buffer = await fs.readFile(filePath);
    const actualSha256 = crypto.createHash('sha256').update(buffer).digest('hex');
    return actualSha256 === expectedIntegrity.sha256;
  } catch (error) {
    logger.error('File integrity verification failed:', error);
    return false;
  }
}

function getMimeType(filename) {
  const ext = path.extname(filename).toLowerCase();
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.pdf': 'application/pdf'
  };
  return mimeTypes[ext] || 'application/octet-stream';
}

async function updateFileAccessStats(fileId, userId) {
  try {
    const redisClient = getRedisClient();
    if (redisClient && redisClient.isReady) {
      const key = `file_access:${fileId}`;
      const accessRecord = {
        userId,
        timestamp: new Date().toISOString(),
        ip: req.ip
      };
      
      await redisClient.lPush(key, JSON.stringify(accessRecord));
      await redisClient.lTrim(key, 0, 99); // Keep last 100 access records
      await redisClient.expire(key, 30 * 24 * 60 * 60); // 30 days
    }
  } catch (error) {
    logger.error('Failed to update file access stats:', error);
  }
}

module.exports = router;
