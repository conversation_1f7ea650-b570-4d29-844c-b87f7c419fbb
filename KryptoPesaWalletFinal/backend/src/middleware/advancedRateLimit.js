const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');

// Production-ready rate limit configurations for different endpoint types
const RATE_LIMITS = {
  // Authentication endpoints - very strict (prevent brute force)
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_AUTH_MAX) || 5, // 5 attempts per window
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Login attempts - extremely strict
  login: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_LOGIN_MAX) || 3, // 3 attempts per window
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Password reset - strict
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: parseInt(process.env.RATE_LIMIT_PASSWORD_RESET_MAX) || 3, // 3 attempts per hour
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Trading endpoints - moderate (prevent spam trading)
  trading: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_TRADING_MAX) || 10, // 10 trades per minute
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Trade creation - stricter
  tradeCreation: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: parseInt(process.env.RATE_LIMIT_TRADE_CREATION_MAX) || 5, // 5 trades per 5 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Wallet operations - strict (prevent abuse)
  wallet: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: parseInt(process.env.RATE_LIMIT_WALLET_MAX) || 20, // 20 operations per 5 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Wallet transactions - very strict
  walletTransaction: {
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: parseInt(process.env.RATE_LIMIT_WALLET_TX_MAX) || 5, // 5 transactions per 10 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Chat/messaging - moderate
  chat: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_CHAT_MAX) || 30, // 30 messages per minute
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // File uploads - strict
  fileUpload: {
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: parseInt(process.env.RATE_LIMIT_FILE_UPLOAD_MAX) || 10, // 10 uploads per 10 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Admin operations - very strict
  admin: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: parseInt(process.env.RATE_LIMIT_ADMIN_MAX) || 50, // 50 operations per 5 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // General API - lenient
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_GENERAL_MAX) || 1000, // 1000 requests per 15 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Public endpoints - very lenient
  public: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_PUBLIC_MAX) || 100, // 100 requests per minute
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Health checks - very lenient
  health: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_HEALTH_MAX) || 200, // 200 requests per minute
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    standardHeaders: true,
    legacyHeaders: false,
  },
};

// Production Redis store for distributed rate limiting with fallback
const createRedisStore = (windowMs = 15 * 60 * 1000) => {
  const redisClient = getRedisClient();

  if (!redisClient || !redisClient.isReady) {
    logger.warn('Redis not available for rate limiting, falling back to memory store');
    return undefined; // Falls back to memory store
  }

  return {
    incr: async (key) => {
      try {
        const pipeline = redisClient.pipeline();
        pipeline.incr(key);
        pipeline.expire(key, Math.ceil(windowMs / 1000));

        const results = await pipeline.exec();
        const current = results[0][1];

        return {
          totalHits: current,
          resetTime: new Date(Date.now() + windowMs),
          remainingPoints: Math.max(0, current)
        };
      } catch (error) {
        logger.error('Redis rate limit incr error:', error);
        // Fallback to allowing request on Redis error
        return { totalHits: 1, resetTime: new Date(Date.now() + windowMs) };
      }
    },

    decrement: async (key) => {
      try {
        const current = await redisClient.decr(key);
        return { totalHits: Math.max(0, current) };
      } catch (error) {
        logger.error('Redis rate limit decr error:', error);
        return { totalHits: 0 };
      }
    },

    resetKey: async (key) => {
      try {
        await redisClient.del(key);
        return true;
      } catch (error) {
        logger.error('Redis rate limit reset error:', error);
        return false;
      }
    },

    // Get current count without incrementing
    get: async (key) => {
      try {
        const current = await redisClient.get(key);
        return { totalHits: parseInt(current) || 0 };
      } catch (error) {
        logger.error('Redis rate limit get error:', error);
        return { totalHits: 0 };
      }
    },

    // Set TTL for existing key
    setTTL: async (key, ttlSeconds) => {
      try {
        await redisClient.expire(key, ttlSeconds);
        return true;
      } catch (error) {
        logger.error('Redis rate limit TTL error:', error);
        return false;
      }
    }
  };
};

// Enhanced rate limiter with custom logic and production features
const createAdvancedRateLimiter = (config, type) => {
  return rateLimit({
    ...config,
    store: createRedisStore(config.windowMs),
    standardHeaders: true,
    legacyHeaders: false,

    // Custom key generator to include user ID for authenticated requests
    keyGenerator: (req) => {
      const baseKey = req.ip;
      const userKey = req.user ? req.user._id.toString() : 'anonymous';
      const userAgent = req.get('User-Agent') ? req.get('User-Agent').substring(0, 50) : 'unknown';
      return `rl:${type}:${baseKey}:${userKey}:${Buffer.from(userAgent).toString('base64').substring(0, 10)}`;
    },

    // Custom message with retry information and security headers
    message: (req, res) => {
      const retryAfter = Math.round(config.windowMs / 1000);

      // Add security headers
      res.set({
        'X-RateLimit-Type': type,
        'X-RateLimit-Limit': config.max,
        'X-RateLimit-Window': config.windowMs,
        'Retry-After': retryAfter
      });

      return {
        success: false,
        error: 'Rate limit exceeded',
        message: `Too many ${type} requests. Please try again in ${retryAfter} seconds.`,
        retryAfter,
        limit: config.max,
        windowMs: config.windowMs,
        type,
        timestamp: new Date().toISOString()
      };
    },

    // Enhanced request handler with logging
    handler: (req, res, next, options) => {
      // Log rate limit violations for security monitoring
      logger.warn('Rate limit exceeded', {
        type,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        method: req.method,
        userId: req.user ? req.user._id : null,
        timestamp: new Date().toISOString()
      });

      // Send rate limit response
      res.status(429).json(options.message(req, res));
    },

    // Skip function for health checks and internal requests
    skip: (req) => {
      // Skip rate limiting for health checks
      if (req.path === '/health' || req.path === '/metrics') {
        return true;
      }

      // Skip for internal service requests (if configured)
      const internalToken = req.get('X-Internal-Service-Token');
      if (internalToken && internalToken === process.env.INTERNAL_SERVICE_TOKEN) {
        return true;
      }

      return false;
    },
  });
};

// Production-ready specific rate limiters
const authLimiter = createAdvancedRateLimiter(RATE_LIMITS.auth, 'auth');
const loginLimiter = createAdvancedRateLimiter(RATE_LIMITS.login, 'login');
const passwordResetLimiter = createAdvancedRateLimiter(RATE_LIMITS.passwordReset, 'passwordReset');
const tradingLimiter = createAdvancedRateLimiter(RATE_LIMITS.trading, 'trading');
const tradeCreationLimiter = createAdvancedRateLimiter(RATE_LIMITS.tradeCreation, 'tradeCreation');
const walletLimiter = createAdvancedRateLimiter(RATE_LIMITS.wallet, 'wallet');
const walletTransactionLimiter = createAdvancedRateLimiter(RATE_LIMITS.walletTransaction, 'walletTransaction');
const chatLimiter = createAdvancedRateLimiter(RATE_LIMITS.chat, 'chat');
const fileUploadLimiter = createAdvancedRateLimiter(RATE_LIMITS.fileUpload, 'fileUpload');
const adminLimiter = createAdvancedRateLimiter(RATE_LIMITS.admin, 'admin');
const generalLimiter = createAdvancedRateLimiter(RATE_LIMITS.general, 'general');
const publicLimiter = createAdvancedRateLimiter(RATE_LIMITS.public, 'public');
const healthLimiter = createAdvancedRateLimiter(RATE_LIMITS.health, 'health');

// Strict limiter for sensitive operations with enhanced security
const strictLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: parseInt(process.env.RATE_LIMIT_STRICT_MAX) || 3, // Only 3 attempts per minute
  store: createRedisStore(1 * 60 * 1000),
  standardHeaders: true,
  legacyHeaders: false,

  keyGenerator: (req) => {
    const baseKey = req.ip;
    const userKey = req.user ? req.user._id.toString() : 'anonymous';
    return `rl:strict:${baseKey}:${userKey}`;
  },

  message: (req, res) => {
    res.set({
      'X-RateLimit-Type': 'strict',
      'X-RateLimit-Limit': 3,
      'X-RateLimit-Window': 60000,
      'Retry-After': 60
    });

    return {
      success: false,
      error: 'Rate limit exceeded',
      message: 'Too many sensitive operations. Please wait before trying again.',
      retryAfter: 60,
      type: 'strict',
      timestamp: new Date().toISOString()
    };
  },

  handler: (req, res, next, options) => {
    logger.error('Strict rate limit exceeded - potential security threat', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      method: req.method,
      userId: req.user ? req.user._id : null,
      timestamp: new Date().toISOString(),
      severity: 'HIGH'
    });

    res.status(429).json(options.message(req, res));
  },
});

// Progressive rate limiter that increases restrictions based on violations
const progressiveLimiter = (req, res, next) => {
  const redisClient = getRedisClient();
  if (!redisClient || !redisClient.isReady) {
    return next();
  }
  
  const key = `progressive:${req.ip}`;
  
  redisClient.get(key)
    .then(violations => {
      const violationCount = parseInt(violations) || 0;
      
      // Increase restrictions based on violation count
      let maxRequests = 100;
      let windowMs = 15 * 60 * 1000;
      
      if (violationCount > 0) {
        maxRequests = Math.max(10, 100 - (violationCount * 20));
        windowMs = Math.min(60 * 60 * 1000, 15 * 60 * 1000 + (violationCount * 5 * 60 * 1000));
      }
      
      // Apply dynamic rate limit
      const dynamicLimiter = rateLimit({
        windowMs,
        max: maxRequests,
        store: createRedisStore(),
        keyGenerator: (req) => `dynamic:${req.ip}`,
        handler: (req, res, next, options) => {
          // Increment violation count
          redisClient.incr(key);
          redisClient.expire(key, 24 * 60 * 60); // Expire after 24 hours
          
          logger.warn('Progressive rate limit exceeded', {
            ip: req.ip,
            violations: violationCount + 1,
            maxRequests,
            windowMs,
          });
          
          res.status(429).json({
            success: false,
            error: 'Rate limit exceeded',
            message: 'Request rate too high. Restrictions have been increased.',
            retryAfter: Math.round(windowMs / 1000),
          });
        },
      });
      
      dynamicLimiter(req, res, next);
    })
    .catch(error => {
      logger.error('Progressive rate limiter error:', error);
      next(); // Continue without rate limiting on Redis error
    });
};

// Body size limiter based on endpoint type
const createBodySizeLimiter = (maxSize) => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length')) || 0;
    
    if (contentLength > maxSize) {
      logger.warn('Request body too large', {
        ip: req.ip,
        contentLength,
        maxSize,
        endpoint: req.originalUrl,
      });
      
      return res.status(413).json({
        success: false,
        error: 'Payload too large',
        message: `Request body must be smaller than ${Math.round(maxSize / 1024 / 1024)}MB`,
        maxSize,
      });
    }
    
    next();
  };
};

// Export all production-ready rate limiters
module.exports = {
  // Basic rate limiters
  authLimiter,
  loginLimiter,
  passwordResetLimiter,
  tradingLimiter,
  tradeCreationLimiter,
  walletLimiter,
  walletTransactionLimiter,
  chatLimiter,
  fileUploadLimiter,
  adminLimiter,
  generalLimiter,
  publicLimiter,
  healthLimiter,

  // Advanced rate limiters
  strictLimiter,
  progressiveLimiter,

  // Utility functions
  createBodySizeLimiter,
  createAdvancedRateLimiter,
  createRedisStore,

  // Configuration
  RATE_LIMITS,
};
