#!/usr/bin/env node

/**
 * KryptoPesa Performance Testing Suite
 * Comprehensive load testing for 10,000+ concurrent users
 */

const axios = require('axios');
const WebSocket = require('ws');
const { performance } = require('perf_hooks');

// Test configuration
const CONFIG = {
  baseURL: process.env.API_BASE_URL || 'http://localhost:3000',
  wsURL: process.env.WS_URL || 'ws://localhost:3000',
  maxConcurrentUsers: 10000,
  testDuration: 300000, // 5 minutes
  rampUpTime: 60000, // 1 minute
  endpoints: {
    auth: '/api/auth/login',
    trades: '/api/trades',
    offers: '/api/offers',
    wallet: '/api/wallet/balance',
    chat: '/api/chat'
  }
};

// Performance metrics storage
const metrics = {
  requests: {
    total: 0,
    successful: 0,
    failed: 0,
    timeouts: 0
  },
  responseTimes: [],
  errors: [],
  websockets: {
    connected: 0,
    disconnected: 0,
    messages: 0,
    errors: 0
  },
  database: {
    queryTimes: [],
    connectionErrors: 0
  }
};

// Test user pool
const testUsers = [];
const activeConnections = new Set();

/**
 * Generate test user credentials
 */
function generateTestUser(index) {
  return {
    identifier: `testuser${index}@kryptopesa.com`,
    password: 'TestPassword123!',
    token: null
  };
}

/**
 * Initialize test user pool
 */
function initializeTestUsers(count) {
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`🔧 Initializing ${count} test users...`);
  for (let i = 0; i < count; i++) {
    testUsers.push(generateTestUser(i));
  }
}

/**
 * Authenticate a test user
 */
async function authenticateUser(user) {
  const startTime = performance.now();
  
  try {
    const response = await axios.post(`${CONFIG.baseURL}${CONFIG.endpoints.auth}`, {
      identifier: user.identifier,
      password: user.password
    }, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'KryptoPesa-LoadTest/1.0'
      }
    });
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    metrics.responseTimes.push(responseTime);
    metrics.requests.total++;
    
    if (response.status === 200 && response.data.success) {
      user.token = response.data.data.token;
      metrics.requests.successful++;
      return true;
    } else {
      metrics.requests.failed++;
      return false;
    }
  } catch (error) {
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    metrics.responseTimes.push(responseTime);
    metrics.requests.total++;
    
    if (error.code === 'ECONNABORTED') {
      metrics.requests.timeouts++;
    } else {
      metrics.requests.failed++;
    }
    
    metrics.errors.push({
      endpoint: CONFIG.endpoints.auth,
      error: error.message,
      timestamp: new Date().toISOString()
    });
    
    return false;
  }
}

/**
 * Simulate API requests for authenticated user
 */
async function simulateUserActivity(user) {
  if (!user.token) return;
  
  const headers = {
    'Authorization': `Bearer ${user.token}`,
    'Content-Type': 'application/json',
    'User-Agent': 'KryptoPesa-LoadTest/1.0'
  };
  
  // Simulate various API calls
  const apiCalls = [
    // Get wallet balance
    () => axios.get(`${CONFIG.baseURL}${CONFIG.endpoints.wallet}`, { headers, timeout: 3000 }),
    
    // Get trades
    () => axios.get(`${CONFIG.baseURL}${CONFIG.endpoints.trades}`, { headers, timeout: 3000 }),
    
    // Get offers
    () => axios.get(`${CONFIG.baseURL}${CONFIG.endpoints.offers}`, { headers, timeout: 3000 }),
    
    // Get chat messages
    () => axios.get(`${CONFIG.baseURL}${CONFIG.endpoints.chat}`, { headers, timeout: 3000 })
  ];
  
  // Execute random API calls
  for (let i = 0; i < 5; i++) {
    const randomCall = apiCalls[Math.floor(Math.random() * apiCalls.length)];
    const startTime = performance.now();
    
    try {
      const response = await randomCall();
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      metrics.responseTimes.push(responseTime);
      metrics.requests.total++;
      
      if (response.status === 200) {
        metrics.requests.successful++;
      } else {
        metrics.requests.failed++;
      }
    } catch (error) {
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      metrics.responseTimes.push(responseTime);
      metrics.requests.total++;
      
      if (error.code === 'ECONNABORTED') {
        metrics.requests.timeouts++;
      } else {
        metrics.requests.failed++;
      }
    }
    
    // Wait between requests (simulate real user behavior)
    await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500));
  }
}

/**
 * Test WebSocket connections
 */
function testWebSocketConnection(user, index) {
  return new Promise((resolve) => {
    const ws = new WebSocket(`${CONFIG.wsURL}?token=${user.token}`);
    const connectionId = `ws-${index}`;
    
    ws.on('open', () => {
      metrics.websockets.connected++;
      activeConnections.add(connectionId);
      
      // Send test messages
      const messageInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({
            type: 'ping',
            timestamp: Date.now()
          }));
          metrics.websockets.messages++;
        }
      }, 5000);
      
      // Close connection after test duration
      setTimeout(() => {
        clearInterval(messageInterval);
        ws.close();
      }, CONFIG.testDuration * 0.8);
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        metrics.websockets.messages++;
      } catch (error) {
        metrics.websockets.errors++;
      }
    });
    
    ws.on('close', () => {
      metrics.websockets.disconnected++;
      activeConnections.delete(connectionId);
      resolve();
    });
    
    ws.on('error', (error) => {
      metrics.websockets.errors++;
      metrics.errors.push({
        endpoint: 'websocket',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      resolve();
    });
  });
}

/**
 * Calculate performance statistics
 */
function calculateStats() {
  const responseTimes = metrics.responseTimes.sort((a, b) => a - b);
  const total = responseTimes.length;
  
  if (total === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      p50: 0,
      p95: 0,
      p99: 0
    };
  }
  
  return {
    min: responseTimes[0],
    max: responseTimes[total - 1],
    avg: responseTimes.reduce((sum, time) => sum + time, 0) / total,
    p50: responseTimes[Math.floor(total * 0.5)],
    p95: responseTimes[Math.floor(total * 0.95)],
    p99: responseTimes[Math.floor(total * 0.99)]
  };
}

/**
 * Print real-time metrics
 */
function printMetrics() {
  const stats = calculateStats();
  const successRate = metrics.requests.total > 0 
    ? (metrics.requests.successful / metrics.requests.total * 100).toFixed(2)
    : 0;
  
  console.clear();
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('🚀 KryptoPesa Performance Test - Real-time Metrics');
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('=' .repeat(60));
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`📊 Requests: ${metrics.requests.total} | Success: ${successRate}% | Failed: ${metrics.requests.failed}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`⏱️  Response Times (ms): Min: ${stats.min.toFixed(2)} | Avg: ${stats.avg.toFixed(2)} | P95: ${stats.p95.toFixed(2)}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`🔌 WebSockets: Connected: ${metrics.websockets.connected} | Active: ${activeConnections.size}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`📈 Messages: ${metrics.websockets.messages} | Errors: ${metrics.websockets.errors}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('=' .repeat(60));
}

/**
 * Run load test
 */
async function runLoadTest() {
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('🚀 Starting KryptoPesa Performance Test Suite');
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`Target: ${CONFIG.maxConcurrentUsers} concurrent users`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`Duration: ${CONFIG.testDuration / 1000} seconds`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('');
  
  // Initialize test users
  initializeTestUsers(CONFIG.maxConcurrentUsers);
  
  // Start metrics display
  const metricsInterval = setInterval(printMetrics, 2000);
  
  // Ramp up users gradually
  const usersPerSecond = CONFIG.maxConcurrentUsers / (CONFIG.rampUpTime / 1000);
  const userPromises = [];
  
  for (let i = 0; i < CONFIG.maxConcurrentUsers; i++) {
    const delay = (i / usersPerSecond) * 1000;
    
    userPromises.push(
      new Promise(async (resolve) => {
        await new Promise(r => setTimeout(r, delay));
        
        const user = testUsers[i];
        
        // Authenticate user
        const authenticated = await authenticateUser(user);
        
        if (authenticated) {
          // Start user activity simulation
          const activityPromise = simulateUserActivity(user);
          
          // Start WebSocket connection
          const wsPromise = testWebSocketConnection(user, i);
          
          await Promise.all([activityPromise, wsPromise]);
        }
        
        resolve();
      })
    );
  }
  
  // Wait for test completion
  await Promise.all(userPromises);
  
  // Stop metrics display
  clearInterval(metricsInterval);
  
  // Print final results
  printFinalResults();
}

/**
 * Print final test results
 */
function printFinalResults() {
  const stats = calculateStats();
  const successRate = metrics.requests.total > 0 
    ? (metrics.requests.successful / metrics.requests.total * 100).toFixed(2)
    : 0;
  
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('\n🏁 Performance Test Complete - Final Results');
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('=' .repeat(80));
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('📊 REQUEST METRICS:');
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Total Requests: ${metrics.requests.total}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Successful: ${metrics.requests.successful} (${successRate}%)`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Failed: ${metrics.requests.failed}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Timeouts: ${metrics.requests.timeouts}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('');
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('⏱️  RESPONSE TIME METRICS (ms):');
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Minimum: ${stats.min.toFixed(2)}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Average: ${stats.avg.toFixed(2)}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Maximum: ${stats.max.toFixed(2)}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   50th Percentile: ${stats.p50.toFixed(2)}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   95th Percentile: ${stats.p95.toFixed(2)}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   99th Percentile: ${stats.p99.toFixed(2)}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('');
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('🔌 WEBSOCKET METRICS:');
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Connections Established: ${metrics.websockets.connected}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Connections Closed: ${metrics.websockets.disconnected}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   Messages Sent/Received: ${metrics.websockets.messages}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log(`   WebSocket Errors: ${metrics.websockets.errors}`);
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('');
  
  // Performance assessment
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('🎯 PERFORMANCE ASSESSMENT:');
  if (stats.p95 < 200 && successRate > 99) {
    if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('   ✅ EXCELLENT - Ready for production deployment');
  } else if (stats.p95 < 500 && successRate > 95) {
    if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('   ✅ GOOD - Acceptable for production with monitoring');
  } else if (stats.p95 < 1000 && successRate > 90) {
    if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('   ⚠️  FAIR - Needs optimization before production');
  } else {
    if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('   ❌ POOR - Significant performance issues detected');
  }
  
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('=' .repeat(80));
}

// Run the test if called directly
if (require.main === module) {
  runLoadTest().catch(console.error);
}

/**
 * Database performance testing
 */
async function testDatabasePerformance() {
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('🗄️  Testing database performance...');

  const dbTests = [
    // Test user queries
    async () => {
      const start = performance.now();
      await axios.get(`${CONFIG.baseURL}/api/users?limit=100`);
      return performance.now() - start;
    },

    // Test trade queries
    async () => {
      const start = performance.now();
      await axios.get(`${CONFIG.baseURL}/api/trades?limit=100`);
      return performance.now() - start;
    },

    // Test offer queries
    async () => {
      const start = performance.now();
      await axios.get(`${CONFIG.baseURL}/api/offers?limit=100`);
      return performance.now() - start;
    }
  ];

  for (const test of dbTests) {
    try {
      const queryTime = await test();
      metrics.database.queryTimes.push(queryTime);
    } catch (error) {
      metrics.database.connectionErrors++;
    }
  }
}

/**
 * Mobile app performance simulation
 */
async function simulateMobilePerformance() {
  if (process.env.NODE_ENV !== 'test' || process.env.VERBOSE_TESTS) console.log('📱 Simulating mobile app performance...');

  // Simulate low-bandwidth conditions
  const mobileHeaders = {
    'User-Agent': 'KryptoPesa-Mobile/1.0 (Android 8.0; Low-end device)',
    'Connection': 'keep-alive',
    'Accept-Encoding': 'gzip, deflate'
  };

  const mobileTests = [
    // Test app initialization
    () => axios.get(`${CONFIG.baseURL}/health`, { headers: mobileHeaders, timeout: 3000 }),

    // Test authentication
    () => axios.post(`${CONFIG.baseURL}${CONFIG.endpoints.auth}`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    }, { headers: mobileHeaders, timeout: 5000 }),

    // Test data loading
    () => axios.get(`${CONFIG.baseURL}${CONFIG.endpoints.offers}?mobile=true`, {
      headers: mobileHeaders,
      timeout: 4000
    })
  ];

  for (const test of mobileTests) {
    const start = performance.now();
    try {
      await test();
      const responseTime = performance.now() - start;
      metrics.responseTimes.push(responseTime);
      metrics.requests.successful++;
    } catch (error) {
      metrics.requests.failed++;
    }
    metrics.requests.total++;
  }
}

module.exports = {
  runLoadTest,
  calculateStats,
  testDatabasePerformance,
  simulateMobilePerformance,
  metrics
};
