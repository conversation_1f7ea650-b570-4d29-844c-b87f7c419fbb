import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    name: v.string(),
    email: v.string(),
    password: v.string(),
    profileComplete: v.boolean(),
    kycVerified: v.boolean(),
    twoFactorEnabled: v.boolean(),
    paymentMethods: v.array(
      v.object({
        id: v.string(),
        name: v.string(),
        instructions: v.string(),
        isActive: v.boolean(),
      })
    ),
    walletAddresses: v.array(
      v.object({
        currency: v.string(),
        address: v.string(),
      })
    ),
  }).index("by_email", ["email"]),

  offers: defineTable({
    userId: v.id("users"),
    type: v.string(), // "buy" or "sell"
    cryptoCurrency: v.string(),
    fiatCurrency: v.string(),
    price: v.number(),
    minAmount: v.number(),
    maxAmount: v.number(),
    paymentMethods: v.array(v.string()),
    terms: v.string(),
    isActive: v.boolean(),
  }).index("by_userId", ["userId"]),

  trades: defineTable({
    offerId: v.id("offers"),
    buyerId: v.id("users"),
    sellerId: v.id("users"),
    cryptoCurrency: v.string(),
    fiatCurrency: v.string(),
    cryptoAmount: v.number(),
    fiatAmount: v.number(),
    status: v.string(), // "pending", "paid", "completed", "disputed", "cancelled"
    paymentMethod: v.string(),
    paymentDetails: v.string(),
    escrowReleased: v.boolean(),
    disputeReason: v.optional(v.string()),
  })
    .index("by_buyerId", ["buyerId"])
    .index("by_sellerId", ["sellerId"]),

  wallets: defineTable({
    userId: v.id("users"),
    currency: v.string(),
    balance: v.number(),
    pendingBalance: v.number(),
  }).index("by_userId_and_currency", ["userId", "currency"]),

  transactions: defineTable({
    userId: v.id("users"),
    type: v.string(), // "deposit", "withdrawal", "trade", "fee"
    currency: v.string(),
    amount: v.number(),
    status: v.string(), // "pending", "completed", "failed"
    tradeId: v.optional(v.id("trades")),
    txHash: v.optional(v.string()),
  }).index("by_userId", ["userId"]),

  messages: defineTable({
    tradeId: v.id("trades"),
    senderId: v.id("users"),
    receiverId: v.id("users"),
    content: v.string(),
    isRead: v.boolean(),
    attachmentUrl: v.optional(v.string()),
  }).index("by_tradeId", ["tradeId"]),

  notifications: defineTable({
    userId: v.id("users"),
    type: v.string(),
    title: v.string(),
    message: v.string(),
    isRead: v.boolean(),
    relatedId: v.optional(v.string()), // Could be a trade ID, offer ID, etc.
  }).index("by_userId", ["userId"]),
});