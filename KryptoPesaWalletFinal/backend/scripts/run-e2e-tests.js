#!/usr/bin/env node

/**
 * End-to-End Test Runner
 * Runs comprehensive integration tests for KryptoPesa platform
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Test configuration
const TEST_CONFIG = {
  testTimeout: 30000,
  setupTimeout: 10000,
  teardownTimeout: 5000,
  maxWorkers: 1, // Run tests sequentially for E2E
  verbose: true,
  collectCoverage: true,
  coverageDirectory: 'coverage/e2e',
  testMatch: [
    '**/tests/e2e/**/*.test.js',
    '**/tests/integration/**/*.test.js'
  ]
};

// Environment setup
const TEST_ENV = {
  NODE_ENV: 'test',
  MONGODB_TEST_URI: 'mongodb://localhost:27017/kryptopesa_test',
  REDIS_TEST_URL: 'redis://localhost:6379/1',
  JWT_SECRET: 'test-jwt-secret-key',
  JWT_REFRESH_SECRET: 'test-jwt-refresh-secret',
  ENCRYPTION_KEY: 'test-encryption-key-32-characters',
  LOG_LEVEL: 'error', // Reduce log noise during tests
  RATE_LIMIT_DISABLED: 'true', // Disable rate limiting for tests
  BLOCKCHAIN_NETWORK: 'testnet'
};

class E2ETestRunner {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      skipped: 0,
      total: 0,
      duration: 0,
      coverage: null
    };
  }

  async run() {
    console.log('🚀 Starting KryptoPesa E2E Test Suite...\n');
    
    try {
      // Pre-test setup
      await this.setupTestEnvironment();
      
      // Run tests
      await this.runTests();
      
      // Generate reports
      await this.generateReports();
      
      // Cleanup
      await this.cleanup();
      
      // Display results
      this.displayResults();
      
      // Exit with appropriate code
      process.exit(this.testResults.failed > 0 ? 1 : 0);
      
    } catch (error) {
      console.error('❌ Test runner failed:', error.message);
      process.exit(1);
    }
  }

  async setupTestEnvironment() {
    console.log('🔧 Setting up test environment...');
    
    // Ensure test database is clean
    await this.cleanTestDatabase();
    
    // Verify required services
    await this.verifyServices();
    
    // Create test directories
    this.createTestDirectories();
    
    console.log('✅ Test environment ready\n');
  }

  async cleanTestDatabase() {
    const mongoose = require('mongoose');
    
    try {
      await mongoose.connect(TEST_ENV.MONGODB_TEST_URI);
      await mongoose.connection.db.dropDatabase();
      await mongoose.connection.close();
      console.log('  📦 Test database cleaned');
    } catch (error) {
      console.warn('  ⚠️  Could not clean test database:', error.message);
    }
  }

  async verifyServices() {
    const services = [
      { name: 'MongoDB', check: () => this.checkMongoDB() },
      { name: 'Redis', check: () => this.checkRedis() }
    ];

    for (const service of services) {
      try {
        await service.check();
        console.log(`  ✅ ${service.name} is available`);
      } catch (error) {
        console.warn(`  ⚠️  ${service.name} check failed:`, error.message);
      }
    }
  }

  async checkMongoDB() {
    const mongoose = require('mongoose');
    await mongoose.connect(TEST_ENV.MONGODB_TEST_URI);
    await mongoose.connection.close();
  }

  async checkRedis() {
    // Redis check would go here if needed
    return Promise.resolve();
  }

  createTestDirectories() {
    const dirs = ['coverage', 'coverage/e2e', 'test-results'];
    
    dirs.forEach(dir => {
      const fullPath = path.join(__dirname, '..', dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }
    });
  }

  async runTests() {
    console.log('🧪 Running E2E tests...\n');
    
    const startTime = Date.now();
    
    return new Promise((resolve, reject) => {
      const jestArgs = [
        '--config', this.createJestConfig(),
        '--runInBand', // Run tests serially
        '--forceExit', // Exit after tests complete
        '--detectOpenHandles', // Detect async operations that prevent exit
        '--verbose',
        '--coverage',
        '--coverageDirectory=coverage/e2e',
        '--testMatch=**/tests/{e2e,integration}/**/*.test.js'
      ];

      const jest = spawn('npx', ['jest', ...jestArgs], {
        cwd: path.join(__dirname, '..'),
        env: { ...process.env, ...TEST_ENV },
        stdio: 'inherit'
      });

      jest.on('close', (code) => {
        this.testResults.duration = Date.now() - startTime;
        
        if (code === 0) {
          console.log('\n✅ All tests passed!');
          resolve();
        } else {
          console.log('\n❌ Some tests failed');
          this.testResults.failed = 1; // Simplified for demo
          resolve(); // Don't reject, let the runner handle exit code
        }
      });

      jest.on('error', (error) => {
        console.error('Failed to start test process:', error);
        reject(error);
      });
    });
  }

  createJestConfig() {
    const configPath = path.join(__dirname, '..', 'jest.e2e.config.js');
    
    const config = `
module.exports = {
  testEnvironment: 'node',
  testTimeout: ${TEST_CONFIG.testTimeout},
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: ${JSON.stringify(TEST_CONFIG.testMatch)},
  collectCoverage: true,
  coverageDirectory: 'coverage/e2e',
  coverageReporters: ['text', 'lcov', 'html'],
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/tests/',
    '/coverage/'
  ],
  verbose: true,
  forceExit: true,
  detectOpenHandles: true,
  maxWorkers: 1
};
`;

    fs.writeFileSync(configPath, config);
    return configPath;
  }

  async generateReports() {
    console.log('\n📊 Generating test reports...');
    
    // Generate coverage report
    await this.generateCoverageReport();
    
    // Generate test summary
    await this.generateTestSummary();
    
    console.log('✅ Reports generated');
  }

  async generateCoverageReport() {
    const coveragePath = path.join(__dirname, '..', 'coverage', 'e2e');
    
    if (fs.existsSync(coveragePath)) {
      console.log(`  📈 Coverage report: file://${coveragePath}/index.html`);
    }
  }

  async generateTestSummary() {
    const summary = {
      timestamp: new Date().toISOString(),
      environment: 'test',
      results: this.testResults,
      configuration: TEST_CONFIG
    };

    const summaryPath = path.join(__dirname, '..', 'test-results', 'e2e-summary.json');
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    
    console.log(`  📋 Test summary: ${summaryPath}`);
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    // Clean test database
    await this.cleanTestDatabase();
    
    // Remove temporary files
    const tempFiles = [
      path.join(__dirname, '..', 'jest.e2e.config.js')
    ];
    
    tempFiles.forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    });
    
    console.log('✅ Cleanup complete');
  }

  displayResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 E2E TEST RESULTS SUMMARY');
    console.log('='.repeat(60));
    console.log(`Duration: ${(this.testResults.duration / 1000).toFixed(2)}s`);
    console.log(`Status: ${this.testResults.failed === 0 ? '✅ PASSED' : '❌ FAILED'}`);
    console.log('='.repeat(60));
  }
}

// Run if called directly
if (require.main === module) {
  const runner = new E2ETestRunner();
  runner.run().catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
}

module.exports = E2ETestRunner;
