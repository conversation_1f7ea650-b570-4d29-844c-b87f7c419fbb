const express = require('express');
const mongoose = require('mongoose');
const { body, validationResult, query } = require('express-validator');
const { authenticate } = require('../middleware/authMiddleware');
const Trade = require('../models/Trade');
const Offer = require('../models/Offer');
const Chat = require('../models/Chat');
const ethereumService = require('../services/blockchain/ethereumService');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// All trade routes require authentication
router.use(authenticate);

// Get active trades for user
router.get('/active', async (req, res, next) => {
  try {
    const trades = await Trade.findActiveTradesForUser(req.user._id);

    res.json({
      success: true,
      data: { trades }
    });
  } catch (error) {
    next(error);
  }
});

// Get trade history
router.get('/history', [
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 }),
  query('status').optional().isIn(['completed', 'cancelled', 'disputed'])
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const status = req.query.status;

    const query = {
      $or: [{ seller: req.user._id }, { buyer: req.user._id }]
    };

    if (status) {
      query.status = status;
    } else {
      query.status = { $in: ['completed', 'cancelled', 'disputed'] };
    }

    // Use optimized query service for better performance
    const { queryOptimization } = require('../services/queryOptimization');

    const result = await queryOptimization.getTradesOptimized(query, {
      limit,
      offset,
      sort: { createdAt: -1 },
      populate: [
        { path: 'seller', select: 'username profile.firstName profile.lastName reputation.score' },
        { path: 'buyer', select: 'username profile.firstName profile.lastName reputation.score' },
        { path: 'offer', select: 'offerId type cryptocurrency fiat' },
        { path: 'chat', select: 'lastMessage unreadCount' }
      ]
    });

    const { trades, pagination } = result;

    res.json({
      success: true,
      data: {
        trades,
        pagination
      }
    });
  } catch (error) {
    next(error);
  }
});

// Create new trade from offer
router.post('/', [
  body('offerId').notEmpty().withMessage('Offer ID is required'),
  body('amount').isNumeric().withMessage('Amount must be a number'),
  body('paymentMethod').notEmpty().withMessage('Payment method is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { offerId, amount, paymentMethod } = req.body;
    const buyerId = req.user._id;

    // Get the offer
    const offer = await Offer.findOne({ offerId, status: 'active' })
      .populate('creator');

    if (!offer) {
      throw new AppError('Offer not found or inactive', 404);
    }

    // Check if user is trying to trade with themselves
    if (offer.creator._id.toString() === buyerId.toString()) {
      throw new AppError('Cannot trade with yourself', 400);
    }

    // Validate amount
    const amountValidation = offer.isAmountValid(parseFloat(amount));
    if (!amountValidation.valid) {
      throw new AppError(amountValidation.reason, 400);
    }

    // Check if user meets requirements
    const requirementCheck = offer.meetsRequirements(req.user);
    if (!requirementCheck.meets) {
      throw new AppError(requirementCheck.reason, 403);
    }

    // Validate payment method
    const validPaymentMethod = offer.paymentMethods.find(pm => pm.method === paymentMethod);
    if (!validPaymentMethod) {
      throw new AppError('Invalid payment method for this offer', 400);
    }

    // Calculate trade details
    const cryptoAmount = parseFloat(amount);
    const fiatAmount = cryptoAmount * offer.fiat.effectivePrice;
    const commissionRate = 0.005; // 0.5%
    const commissionAmount = (cryptoAmount * commissionRate).toString();

    // Create trade
    const trade = new Trade({
      seller: offer.type === 'sell' ? offer.creator._id : buyerId,
      buyer: offer.type === 'sell' ? buyerId : offer.creator._id,
      offer: offer._id,
      cryptocurrency: {
        symbol: offer.cryptocurrency.symbol,
        contractAddress: offer.cryptocurrency.contractAddress,
        network: offer.cryptocurrency.network,
        amount: cryptoAmount.toString(),
        decimals: offer.cryptocurrency.symbol === 'BTC' ? 8 :
                 ['USDT', 'USDC'].includes(offer.cryptocurrency.symbol) ? 6 : 18
      },
      fiat: {
        currency: offer.fiat.currency,
        amount: fiatAmount,
        exchangeRate: offer.fiat.effectivePrice
      },
      payment: {
        method: paymentMethod,
        details: validPaymentMethod.details
      },
      commission: {
        rate: commissionRate,
        amount: commissionAmount,
        currency: offer.cryptocurrency.symbol
      },
      expiresAt: new Date(Date.now() + (offer.terms.timeLimit || 30) * 60 * 1000)
    });

    // Use database transaction for atomicity
    const session = await mongoose.startSession();
    let savedTrade;

    try {
      await session.withTransaction(async () => {
        // Save trade
        savedTrade = await trade.save({ session });

        // Create chat for the trade
        const chat = await Chat.createForTrade(
          savedTrade._id,
          savedTrade.seller,
          savedTrade.buyer,
          { session }
        );

        // Update trade with chat reference
        savedTrade.chat = chat._id;
        await savedTrade.save({ session });

        // Update offer available amount
        await offer.updateAvailableAmount(cryptoAmount, { session });

        // Add timeline entry
        await savedTrade.addTimelineEntry('created', req.user._id, 'Trade created from offer', { session });
      });

      // Populate trade for response (outside transaction)
      await savedTrade.populate('seller buyer offer chat');

      logger.info(`Trade created: ${savedTrade.tradeId} by user ${req.user.username}`);

      res.status(201).json({
        success: true,
        message: 'Trade created successfully',
        data: { trade: savedTrade }
      });

    } catch (transactionError) {
      logger.error('Trade creation transaction failed:', transactionError);
      throw new AppError('Failed to create trade. Please try again.', 500);
    } finally {
      await session.endSession();
    }

  } catch (error) {
    next(error);
  }
});

// Get specific trade
router.get('/:tradeId', async (req, res, next) => {
  try {
    const { tradeId } = req.params;

    const trade = await Trade.findOne({ tradeId })
      .populate('seller buyer offer chat dispute');

    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    // Check if user is participant
    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    res.json({
      success: true,
      data: { trade }
    });
  } catch (error) {
    next(error);
  }
});

// Fund trade (seller deposits crypto into escrow)
router.post('/:tradeId/fund', async (req, res, next) => {
  try {
    const { tradeId } = req.params;

    const trade = await Trade.findOne({ tradeId })
      .populate('seller buyer');

    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    // Check if user is the seller
    if (trade.seller._id.toString() !== req.user._id.toString()) {
      throw new AppError('Only seller can fund the trade', 403);
    }

    // Check trade status
    if (trade.status !== 'created') {
      throw new AppError('Trade cannot be funded in current status', 400);
    }

    // Check if trade has expired
    if (trade.isExpired) {
      trade.status = 'expired';
      await trade.save();
      throw new AppError('Trade has expired', 400);
    }

    try {
      // Create blockchain trade
      const blockchainResult = await ethereumService.createTrade({
        buyer: trade.buyer.addresses?.ethereum?.address,
        tokenAddress: trade.cryptocurrency.contractAddress,
        amount: trade.cryptocurrency.amount,
        fiatAmount: Math.round(trade.fiat.amount * 100), // Convert to cents
        fiatCurrency: trade.fiat.currency,
        paymentMethod: trade.payment.method,
        paymentHash: ethers.utils.keccak256(ethers.utils.toUtf8Bytes(JSON.stringify(trade.payment.details)))
      }, trade.cryptocurrency.network);

      // Update trade with blockchain info
      trade.blockchainTradeId = blockchainResult.tradeId;
      trade.escrow.transactionHash = blockchainResult.transactionHash;
      trade.escrow.blockNumber = blockchainResult.blockNumber;
      trade.escrow.gasUsed = blockchainResult.gasUsed;
      trade.escrow.funded = true;

      await trade.updateStatus('funded', req.user._id, 'Trade funded on blockchain', blockchainResult.transactionHash);

      logger.info(`Trade funded: ${trade.tradeId} - Blockchain ID: ${blockchainResult.tradeId}`);

      res.json({
        success: true,
        message: 'Trade funded successfully',
        data: {
          trade,
          blockchain: blockchainResult
        }
      });

    } catch (blockchainError) {
      logger.error(`Blockchain funding failed for trade ${trade.tradeId}:`, blockchainError);
      throw new AppError('Failed to fund trade on blockchain', 500);
    }

  } catch (error) {
    next(error);
  }
});

// Confirm payment sent (buyer)
router.post('/:tradeId/confirm-payment-sent', [
  body('proofOfPayment').optional().isArray(),
  body('reference').optional().isString()
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const { proofOfPayment, reference } = req.body;

    const trade = await Trade.findOne({ tradeId })
      .populate('seller buyer');

    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    // Check if user is the buyer
    if (trade.buyer._id.toString() !== req.user._id.toString()) {
      throw new AppError('Only buyer can confirm payment sent', 403);
    }

    // Check trade status
    if (trade.status !== 'funded') {
      throw new AppError('Trade must be funded before confirming payment', 400);
    }

    // Update payment info
    if (proofOfPayment) {
      trade.payment.proofOfPayment = proofOfPayment;
    }
    if (reference) {
      trade.payment.reference = reference;
    }

    // Update confirmations
    trade.confirmations.buyer.paymentSent = true;
    trade.confirmations.buyer.timestamp = new Date();

    try {
      // Confirm on blockchain
      const blockchainResult = await ethereumService.confirmPaymentSent(
        trade.blockchainTradeId,
        trade.cryptocurrency.network
      );

      await trade.updateStatus('payment_sent', req.user._id, 'Payment sent confirmed', blockchainResult.transactionHash);

      logger.info(`Payment sent confirmed for trade: ${trade.tradeId}`);

      res.json({
        success: true,
        message: 'Payment sent confirmation recorded',
        data: {
          trade,
          blockchain: blockchainResult
        }
      });

    } catch (blockchainError) {
      logger.error(`Blockchain payment confirmation failed for trade ${trade.tradeId}:`, blockchainError);
      throw new AppError('Failed to confirm payment on blockchain', 500);
    }

  } catch (error) {
    next(error);
  }
});

// Confirm payment received (seller)
router.post('/:tradeId/confirm-payment-received', async (req, res, next) => {
  try {
    const { tradeId } = req.params;

    const trade = await Trade.findOne({ tradeId })
      .populate('seller buyer');

    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    // Check if user is the seller
    if (trade.seller._id.toString() !== req.user._id.toString()) {
      throw new AppError('Only seller can confirm payment received', 403);
    }

    // Check trade status
    if (trade.status !== 'payment_sent') {
      throw new AppError('Buyer must confirm payment sent first', 400);
    }

    // Update confirmations
    trade.confirmations.seller.paymentReceived = true;
    trade.confirmations.seller.timestamp = new Date();

    try {
      // Complete trade on blockchain
      const blockchainResult = await ethereumService.confirmPaymentReceived(
        trade.blockchainTradeId,
        trade.cryptocurrency.network
      );

      await trade.updateStatus('completed', req.user._id, 'Payment received, trade completed', blockchainResult.transactionHash);

      // Update user reputations
      await trade.seller.updateReputation(true, false, false);
      await trade.buyer.updateReputation(true, false, false);

      logger.info(`Trade completed: ${trade.tradeId}`);

      res.json({
        success: true,
        message: 'Trade completed successfully',
        data: {
          trade,
          blockchain: blockchainResult
        }
      });

    } catch (blockchainError) {
      logger.error(`Blockchain trade completion failed for trade ${trade.tradeId}:`, blockchainError);
      throw new AppError('Failed to complete trade on blockchain', 500);
    }

  } catch (error) {
    next(error);
  }
});

// Create dispute
router.post('/:tradeId/dispute', [
  body('reason').notEmpty().withMessage('Dispute reason is required'),
  body('category').isIn([
    'payment_not_received',
    'payment_not_sent',
    'wrong_amount',
    'fake_payment_proof',
    'account_issues',
    'communication_issues',
    'other'
  ]).withMessage('Invalid dispute category'),
  body('evidence').optional().isArray()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { tradeId } = req.params;
    const { reason, category, evidence } = req.body;

    const trade = await Trade.findOne({ tradeId })
      .populate('seller buyer');

    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    // Check if user is participant
    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Only trade participants can create disputes', 403);
    }

    // Check if trade can be disputed
    if (!['funded', 'payment_sent'].includes(trade.status)) {
      throw new AppError('Trade cannot be disputed in current status', 400);
    }

    // Check if dispute already exists
    if (trade.dispute) {
      throw new AppError('Dispute already exists for this trade', 400);
    }

    // Use atomic transaction to create dispute and update trade
    const { dataConsistencyService } = require('../services/dataConsistency');

    const disputeData = {
      initiator: req.user._id,
      respondent: trade.getCounterparty(req.user._id),
      category,
      description: reason,
      evidence: evidence || [],
      metadata: {
        tradeValue: {
          crypto: trade.cryptocurrency.amount,
          fiat: trade.fiat.amount,
          currency: trade.fiat.currency
        }
      }
    };

    const result = await dataConsistencyService.executeAtomicDisputeCreation(
      trade._id,
      disputeData,
      req.user._id
    );

    const dispute = result.dispute;

    try {
      // Create dispute on blockchain
      const blockchainResult = await ethereumService.createDispute(
        trade.blockchainTradeId,
        reason,
        trade.cryptocurrency.network
      );

      dispute.addTimelineEntry('blockchain_dispute_created', req.user._id, `Blockchain dispute created: ${blockchainResult.transactionHash}`);
      await dispute.save();

    } catch (blockchainError) {
      logger.error(`Blockchain dispute creation failed for trade ${trade.tradeId}:`, blockchainError);
      // Continue without blockchain dispute - can be resolved manually
    }

    logger.info(`Dispute created for trade: ${trade.tradeId} by user ${req.user.username}`);

    res.status(201).json({
      success: true,
      message: 'Dispute created successfully',
      data: { dispute }
    });

  } catch (error) {
    next(error);
  }
});

// Cancel trade
router.post('/:tradeId/cancel', [
  body('reason').notEmpty().withMessage('Cancellation reason is required')
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const { reason } = req.body;

    const trade = await Trade.findOne({ tradeId })
      .populate('seller buyer');

    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    // Check if user is participant
    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Only trade participants can cancel trades', 403);
    }

    // Check if trade can be cancelled
    if (!['created', 'funded'].includes(trade.status)) {
      throw new AppError('Trade cannot be cancelled in current status', 400);
    }

    // If trade is funded, need to refund escrow
    if (trade.status === 'funded' && trade.escrow.funded) {
      // This would require admin intervention or automatic refund mechanism
      throw new AppError('Funded trades require dispute resolution for cancellation', 400);
    }

    trade.cancelReason = reason;
    await trade.updateStatus('cancelled', req.user._id, `Trade cancelled: ${reason}`);

    // Update user reputation for cancellation
    await req.user.updateReputation(false, true, false);

    // Restore offer availability if trade was created from offer
    if (trade.offer) {
      const offer = await Offer.findById(trade.offer);
      if (offer) {
        const currentAvailable = parseFloat(offer.cryptocurrency.availableAmount);
        const tradeAmount = parseFloat(trade.cryptocurrency.amount);
        offer.cryptocurrency.availableAmount = (currentAvailable + tradeAmount).toString();

        if (offer.status === 'inactive') {
          offer.status = 'active';
        }

        await offer.save();
      }
    }

    logger.info(`Trade cancelled: ${trade.tradeId} by user ${req.user.username}`);

    res.json({
      success: true,
      message: 'Trade cancelled successfully',
      data: { trade }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;