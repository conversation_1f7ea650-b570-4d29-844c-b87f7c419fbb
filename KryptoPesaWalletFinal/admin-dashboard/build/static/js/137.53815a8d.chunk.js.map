{"version": 3, "file": "static/js/137.53815a8d.chunk.js", "mappings": "+XAwBA,MAwLA,EAxLqBA,KACnB,MAAOC,EAAiBC,IAAsBC,EAAAA,EAAAA,UAAS,OAChDC,EAAYC,IAAiBF,EAAAA,EAAAA,WAAS,IACtCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KACtCK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAS,IA4BrCO,EAAkBC,IACtB,OAAQA,GACN,IAAK,OAAQ,MAAO,QACpB,IAAK,eAAgB,MAAO,UAC5B,IAAK,WAAY,MAAO,UACxB,QAAS,MAAO,YAIdC,EAAoBC,IACxB,OAAQA,GACN,IAAK,SAAU,MAAO,QACtB,IAAK,OAAQ,MAAO,UACpB,IAAK,SAAU,MAAO,OAEtB,QAAS,MAAO,YAuBpB,OACEC,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,EACFC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAJ,SAAC,wBAItCC,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAAAL,UACJC,EAAAA,EAAAA,KAACK,EAAAA,EAAc,CAAAN,UACbF,EAAAA,EAAAA,MAACS,EAAAA,EAAK,CAAAP,SAAA,EACJC,EAAAA,EAAAA,KAACO,EAAAA,EAAS,CAAAR,UACRF,EAAAA,EAAAA,MAACW,EAAAA,EAAQ,CAAAT,SAAA,EACPC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,gBACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,cACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,eACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,gBACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,cACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,YACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,YACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,cACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,aACXC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAC,kBAGfC,EAAAA,EAAAA,KAACU,EAAAA,EAAS,CAAAX,SAtFH,CACf,CACEY,GAAI,UACJC,QAAS,UACTC,UAAW,WACXC,WAAY,aACZC,SAAU,uBACVrB,OAAQ,OACRE,SAAU,OACVoB,UAAW,uBACXC,OAAQ,YAEV,CACEN,GAAI,UACJC,QAAS,UACTC,UAAW,eACXC,WAAY,aACZC,SAAU,eACVrB,OAAQ,eACRE,SAAU,SACVoB,UAAW,uBACXC,OAAQ,cAkEUC,IAAKC,IACbtB,EAAAA,EAAAA,MAACW,EAAAA,EAAQ,CAAAT,SAAA,EACPC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAEoB,EAAQR,MACpBX,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAEoB,EAAQP,WACpBZ,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAEoB,EAAQN,aACpBb,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAEoB,EAAQL,cACpBd,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAEoB,EAAQJ,SAASK,QAAQ,IAAK,QAC1CpB,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SAAEoB,EAAQF,UACpBjB,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,UACRC,EAAAA,EAAAA,KAACqB,EAAAA,EAAI,CACHC,MAAOH,EAAQzB,OACf6B,MAAO9B,EAAe0B,EAAQzB,QAC9B8B,KAAK,aAGTxB,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,UACRC,EAAAA,EAAAA,KAACqB,EAAAA,EAAI,CACHC,MAAOH,EAAQvB,SACf2B,MAAO5B,EAAiBwB,EAAQvB,UAChC4B,KAAK,aAGTxB,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,SACP,IAAI0B,KAAKN,EAAQH,WAAWU,wBAE/B1B,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,UACRC,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CACLzB,QAAQ,YACRsB,KAAK,QACLI,QAASA,IAxECT,KAC5BlC,EAAmBkC,GACnB/B,GAAc,IAsEmByC,CAAqBV,GACpCW,SAA6B,aAAnBX,EAAQzB,OAAsBK,SACzC,gBA9BUoB,EAAQR,eA0CjCd,EAAAA,EAAAA,MAACkC,EAAAA,EAAM,CAACC,KAAM7C,EAAY8C,QAASA,IAAM7C,GAAc,GAAQ8C,SAAS,KAAKC,WAAS,EAAApC,SAAA,EACpFF,EAAAA,EAAAA,MAACuC,EAAAA,EAAW,CAAArC,SAAA,CAAC,mBACqB,OAAff,QAAe,IAAfA,OAAe,EAAfA,EAAiB2B,OAEpCX,EAAAA,EAAAA,KAACqC,EAAAA,EAAa,CAAAtC,UACZF,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACwC,GAAI,CAAEC,GAAI,GAAIxC,SAAA,EACjBF,EAAAA,EAAAA,MAAC2C,EAAAA,EAAW,CAACL,WAAS,EAACM,OAAO,SAAQ1C,SAAA,EACpCC,EAAAA,EAAAA,KAAC0C,EAAAA,EAAU,CAAA3C,SAAC,yBACZF,EAAAA,EAAAA,MAAC8C,EAAAA,EAAM,CACLC,MAAOvD,EACPwD,SAAWC,GAAMxD,EAAcwD,EAAEC,OAAOH,OACxCtB,MAAM,sBAAqBvB,SAAA,EAE3BC,EAAAA,EAAAA,KAACgD,EAAAA,EAAQ,CAACJ,MAAM,kBAAiB7C,SAAC,qBAClCC,EAAAA,EAAAA,KAACgD,EAAAA,EAAQ,CAACJ,MAAM,mBAAkB7C,SAAC,sBACnCC,EAAAA,EAAAA,KAACgD,EAAAA,EAAQ,CAACJ,MAAM,iBAAgB7C,SAAC,oBACjCC,EAAAA,EAAAA,KAACgD,EAAAA,EAAQ,CAACJ,MAAM,YAAW7C,SAAC,qBAIhCC,EAAAA,EAAAA,KAACiD,EAAAA,EAAS,CACRd,WAAS,EACTe,WAAS,EACTC,KAAM,EACN7B,MAAM,YACNsB,MAAOrD,EACPsD,SAAWC,GAAMtD,EAAasD,EAAEC,OAAOH,OACvCH,OAAO,SACPW,YAAY,uDAIlBvD,EAAAA,EAAAA,MAACwD,EAAAA,EAAa,CAAAtD,SAAA,EACZC,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CAACC,QAASA,IAAMxC,GAAc,GAAOW,SAAC,YAC7CC,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CACLC,QApHqB0B,KAE7BC,QAAQC,IAAI,qBAAsB,CAChCC,UAAWzE,EAAgB2B,GAC3BtB,aACAE,cAGFH,GAAc,GACdH,EAAmB,MACnBK,EAAc,IACdE,EAAa,KA0GLU,QAAQ,YACR4B,UAAWzC,IAAeE,EAAUQ,SACrC,+B", "sources": ["pages/DisputesPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n} from '@mui/material';\n\nconst DisputesPage = () => {\n  const [selectedDispute, setSelectedDispute] = useState(null);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [resolution, setResolution] = useState('');\n  const [reasoning, setReasoning] = useState('');\n\n  // Mock data - would come from API\n  const disputes = [\n    {\n      id: 'DSP-001',\n      tradeId: 'TRD-001',\n      initiator: 'john_doe',\n      respondent: 'jane_smith',\n      category: 'payment_not_received',\n      status: 'open',\n      priority: 'high',\n      createdAt: '2023-07-15T10:30:00Z',\n      amount: '500 USDT',\n    },\n    {\n      id: 'DSP-002',\n      tradeId: 'TRD-002',\n      initiator: 'alice_trader',\n      respondent: 'bob_crypto',\n      category: 'wrong_amount',\n      status: 'under_review',\n      priority: 'medium',\n      createdAt: '2023-07-14T15:45:00Z',\n      amount: '1000 USDT',\n    },\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'open': return 'error';\n      case 'under_review': return 'warning';\n      case 'resolved': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'urgent': return 'error';\n      case 'high': return 'warning';\n      case 'medium': return 'info';\n      case 'low': return 'default';\n      default: return 'default';\n    }\n  };\n\n  const handleResolveDispute = (dispute) => {\n    setSelectedDispute(dispute);\n    setDialogOpen(true);\n  };\n\n  const handleSubmitResolution = () => {\n    // API call to resolve dispute\n    console.log('Resolving dispute:', {\n      disputeId: selectedDispute.id,\n      resolution,\n      reasoning,\n    });\n    \n    setDialogOpen(false);\n    setSelectedDispute(null);\n    setResolution('');\n    setReasoning('');\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Dispute Management\n      </Typography>\n\n      <Paper>\n        <TableContainer>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Dispute ID</TableCell>\n                <TableCell>Trade ID</TableCell>\n                <TableCell>Initiator</TableCell>\n                <TableCell>Respondent</TableCell>\n                <TableCell>Category</TableCell>\n                <TableCell>Amount</TableCell>\n                <TableCell>Status</TableCell>\n                <TableCell>Priority</TableCell>\n                <TableCell>Created</TableCell>\n                <TableCell>Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {disputes.map((dispute) => (\n                <TableRow key={dispute.id}>\n                  <TableCell>{dispute.id}</TableCell>\n                  <TableCell>{dispute.tradeId}</TableCell>\n                  <TableCell>{dispute.initiator}</TableCell>\n                  <TableCell>{dispute.respondent}</TableCell>\n                  <TableCell>{dispute.category.replace('_', ' ')}</TableCell>\n                  <TableCell>{dispute.amount}</TableCell>\n                  <TableCell>\n                    <Chip \n                      label={dispute.status} \n                      color={getStatusColor(dispute.status)}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Chip \n                      label={dispute.priority} \n                      color={getPriorityColor(dispute.priority)}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    {new Date(dispute.createdAt).toLocaleDateString()}\n                  </TableCell>\n                  <TableCell>\n                    <Button\n                      variant=\"contained\"\n                      size=\"small\"\n                      onClick={() => handleResolveDispute(dispute)}\n                      disabled={dispute.status === 'resolved'}\n                    >\n                      Resolve\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </Paper>\n\n      {/* Resolution Dialog */}\n      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          Resolve Dispute {selectedDispute?.id}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ mt: 2 }}>\n            <FormControl fullWidth margin=\"normal\">\n              <InputLabel>Resolution Decision</InputLabel>\n              <Select\n                value={resolution}\n                onChange={(e) => setResolution(e.target.value)}\n                label=\"Resolution Decision\"\n              >\n                <MenuItem value=\"favor_initiator\">Favor Initiator</MenuItem>\n                <MenuItem value=\"favor_respondent\">Favor Respondent</MenuItem>\n                <MenuItem value=\"partial_refund\">Partial Refund</MenuItem>\n                <MenuItem value=\"no_action\">No Action</MenuItem>\n              </Select>\n            </FormControl>\n            \n            <TextField\n              fullWidth\n              multiline\n              rows={4}\n              label=\"Reasoning\"\n              value={reasoning}\n              onChange={(e) => setReasoning(e.target.value)}\n              margin=\"normal\"\n              placeholder=\"Explain the reasoning for this resolution...\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>\n          <Button \n            onClick={handleSubmitResolution}\n            variant=\"contained\"\n            disabled={!resolution || !reasoning}\n          >\n            Submit Resolution\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default DisputesPage;\n"], "names": ["DisputesPage", "selectedDispute", "setSelectedDispute", "useState", "dialogOpen", "setDialogOpen", "resolution", "setResolution", "reasoning", "setReasoning", "getStatusColor", "status", "getPriorityColor", "priority", "_jsxs", "Box", "children", "_jsx", "Typography", "variant", "gutterBottom", "Paper", "TableContainer", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "id", "tradeId", "initiator", "respondent", "category", "createdAt", "amount", "map", "dispute", "replace", "Chip", "label", "color", "size", "Date", "toLocaleDateString", "<PERSON><PERSON>", "onClick", "handleResolveDispute", "disabled", "Dialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sx", "mt", "FormControl", "margin", "InputLabel", "Select", "value", "onChange", "e", "target", "MenuItem", "TextField", "multiline", "rows", "placeholder", "DialogActions", "handleSubmitResolution", "console", "log", "disputeId"], "sourceRoot": ""}