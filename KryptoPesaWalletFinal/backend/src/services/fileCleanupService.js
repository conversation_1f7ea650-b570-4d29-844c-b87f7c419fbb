const fs = require('fs').promises;
const path = require('path');
const cron = require('node-cron');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');
const Trade = require('../models/Trade');
const Chat = require('../models/Chat');

class FileCleanupService {
  constructor() {
    this.redisClient = getRedisClient();
    this.quarantineDir = path.join(__dirname, '../../quarantine');
    this.secureUploadDir = path.join(__dirname, '../../uploads/secure');
    this.tempUploadDir = path.join(__dirname, '../../temp-uploads');
    
    this.isRunning = false;
    this.initializeCleanupSchedule();
  }

  /**
   * Initialize automated cleanup schedules
   */
  initializeCleanupSchedule() {
    // Clean up temporary files every hour
    cron.schedule('0 * * * *', () => {
      this.cleanupTempFiles();
    });

    // Clean up quarantined files every day at 2 AM
    cron.schedule('0 2 * * *', () => {
      this.cleanupQuarantinedFiles();
    });

    // Clean up orphaned files every week on Sunday at 3 AM
    cron.schedule('0 3 * * 0', () => {
      this.cleanupOrphanedFiles();
    });

    // Clean up expired file records every day at 4 AM
    cron.schedule('0 4 * * *', () => {
      this.cleanupExpiredFileRecords();
    });

    logger.info('File cleanup service initialized with scheduled tasks');
  }

  /**
   * Clean up temporary upload files older than 1 hour
   */
  async cleanupTempFiles() {
    if (this.isRunning) return;
    this.isRunning = true;

    try {
      logger.info('Starting temporary file cleanup');
      
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      let cleanedCount = 0;
      let totalSize = 0;

      await this.processDirectory(this.tempUploadDir, async (filePath, stats) => {
        if (stats.mtime.getTime() < oneHourAgo) {
          try {
            await fs.unlink(filePath);
            cleanedCount++;
            totalSize += stats.size;
            
            logger.debug(`Cleaned temp file: ${filePath}`, {
              size: stats.size,
              age: Date.now() - stats.mtime.getTime()
            });
          } catch (error) {
            logger.error(`Failed to delete temp file: ${filePath}`, error);
          }
        }
      });

      logger.info('Temporary file cleanup completed', {
        filesRemoved: cleanedCount,
        totalSizeFreed: this.formatBytes(totalSize)
      });

    } catch (error) {
      logger.error('Temporary file cleanup failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Clean up quarantined files older than 7 days
   */
  async cleanupQuarantinedFiles() {
    try {
      logger.info('Starting quarantined file cleanup');
      
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      let cleanedCount = 0;
      let totalSize = 0;

      // Clean up quarantine records from Redis
      if (this.redisClient && this.redisClient.isReady) {
        const quarantineKeys = await this.redisClient.keys('quarantine:*');
        
        for (const key of quarantineKeys) {
          try {
            const recordData = await this.redisClient.get(key);
            if (recordData) {
              const record = JSON.parse(recordData);
              const quarantineTime = new Date(record.quarantinedAt).getTime();
              
              if (quarantineTime < sevenDaysAgo) {
                // Delete quarantined file
                const quarantinePath = path.join(this.quarantineDir, `quarantine_${record.id}`);
                try {
                  const stats = await fs.stat(quarantinePath);
                  await fs.unlink(quarantinePath);
                  totalSize += stats.size;
                  cleanedCount++;
                } catch (fileError) {
                  logger.warn(`Quarantined file not found: ${quarantinePath}`);
                }

                // Remove Redis record
                await this.redisClient.del(key);
              }
            }
          } catch (error) {
            logger.error(`Failed to process quarantine record: ${key}`, error);
          }
        }
      }

      // Clean up any remaining quarantine files
      await this.processDirectory(this.quarantineDir, async (filePath, stats) => {
        if (stats.mtime.getTime() < sevenDaysAgo) {
          try {
            await fs.unlink(filePath);
            cleanedCount++;
            totalSize += stats.size;
          } catch (error) {
            logger.error(`Failed to delete quarantine file: ${filePath}`, error);
          }
        }
      });

      logger.info('Quarantined file cleanup completed', {
        filesRemoved: cleanedCount,
        totalSizeFreed: this.formatBytes(totalSize)
      });

    } catch (error) {
      logger.error('Quarantined file cleanup failed:', error);
    }
  }

  /**
   * Clean up orphaned files not referenced in database
   */
  async cleanupOrphanedFiles() {
    try {
      logger.info('Starting orphaned file cleanup');
      
      let cleanedCount = 0;
      let totalSize = 0;
      const referencedFiles = new Set();

      // Collect all file references from database
      await this.collectFileReferences(referencedFiles);

      // Check secure upload directories
      const contexts = ['payment-proofs', 'chat-attachments', 'documents'];
      
      for (const context of contexts) {
        const contextDir = path.join(this.secureUploadDir, context);
        
        await this.processDirectory(contextDir, async (filePath, stats) => {
          const filename = path.basename(filePath);
          
          // Extract file ID from filename pattern
          const fileIdMatch = filename.match(/^[a-f0-9-]{36}/);
          if (fileIdMatch) {
            const potentialFileId = fileIdMatch[0];
            
            // Check if file is referenced
            if (!referencedFiles.has(potentialFileId)) {
              // Check if file record exists in Redis
              let hasRecord = false;
              if (this.redisClient && this.redisClient.isReady) {
                hasRecord = await this.redisClient.exists(`file:${potentialFileId}`);
              }

              if (!hasRecord) {
                try {
                  await fs.unlink(filePath);
                  cleanedCount++;
                  totalSize += stats.size;
                  
                  logger.debug(`Cleaned orphaned file: ${filePath}`, {
                    size: stats.size,
                    context
                  });
                } catch (error) {
                  logger.error(`Failed to delete orphaned file: ${filePath}`, error);
                }
              }
            }
          }
        });
      }

      logger.info('Orphaned file cleanup completed', {
        filesRemoved: cleanedCount,
        totalSizeFreed: this.formatBytes(totalSize)
      });

    } catch (error) {
      logger.error('Orphaned file cleanup failed:', error);
    }
  }

  /**
   * Clean up expired file records from Redis
   */
  async cleanupExpiredFileRecords() {
    try {
      logger.info('Starting expired file record cleanup');
      
      if (!this.redisClient || !this.redisClient.isReady) {
        logger.warn('Redis not available for file record cleanup');
        return;
      }

      let cleanedCount = 0;
      const fileKeys = await this.redisClient.keys('file:*');
      
      for (const key of fileKeys) {
        try {
          const ttl = await this.redisClient.ttl(key);
          
          // If TTL is -1, the key has no expiration set
          if (ttl === -1) {
            const recordData = await this.redisClient.get(key);
            if (recordData) {
              const record = JSON.parse(recordData);
              const createdAt = new Date(record.createdAt || record.uploadedAt).getTime();
              const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
              
              if (createdAt < thirtyDaysAgo) {
                await this.redisClient.del(key);
                cleanedCount++;
                
                // Also remove from user file index
                if (record.uploadedBy) {
                  await this.redisClient.sRem(`user_files:${record.uploadedBy}`, record.id);
                }
              }
            }
          }
        } catch (error) {
          logger.error(`Failed to process file record: ${key}`, error);
        }
      }

      logger.info('Expired file record cleanup completed', {
        recordsRemoved: cleanedCount
      });

    } catch (error) {
      logger.error('Expired file record cleanup failed:', error);
    }
  }

  /**
   * Collect all file references from database models
   */
  async collectFileReferences(referencedFiles) {
    try {
      // Collect from Trade model (payment proofs)
      const trades = await Trade.find({
        'payment.proofOfPayment': { $exists: true, $ne: [] }
      }).select('payment.proofOfPayment');

      for (const trade of trades) {
        if (trade.payment && trade.payment.proofOfPayment) {
          trade.payment.proofOfPayment.forEach(fileId => {
            if (fileId && typeof fileId === 'string') {
              referencedFiles.add(fileId);
            }
          });
        }
      }

      // Collect from Chat model (attachments)
      const chats = await Chat.find({
        'messages.attachments': { $exists: true, $ne: [] }
      }).select('messages.attachments');

      for (const chat of chats) {
        if (chat.messages) {
          chat.messages.forEach(message => {
            if (message.attachments) {
              message.attachments.forEach(fileId => {
                if (fileId && typeof fileId === 'string') {
                  referencedFiles.add(fileId);
                }
              });
            }
          });
        }
      }

      logger.debug(`Collected ${referencedFiles.size} file references from database`);

    } catch (error) {
      logger.error('Failed to collect file references:', error);
    }
  }

  /**
   * Process directory recursively
   */
  async processDirectory(dirPath, callback) {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await this.processDirectory(fullPath, callback);
        } else if (entry.isFile()) {
          try {
            const stats = await fs.stat(fullPath);
            await callback(fullPath, stats);
          } catch (error) {
            logger.error(`Failed to process file: ${fullPath}`, error);
          }
        }
      }
    } catch (error) {
      if (error.code !== 'ENOENT') {
        logger.error(`Failed to read directory: ${dirPath}`, error);
      }
    }
  }

  /**
   * Manual cleanup trigger for specific file types
   */
  async manualCleanup(options = {}) {
    const {
      cleanTemp = true,
      cleanQuarantine = true,
      cleanOrphaned = true,
      cleanExpired = true
    } = options;

    logger.info('Starting manual file cleanup', options);

    const results = {
      temp: { files: 0, size: 0 },
      quarantine: { files: 0, size: 0 },
      orphaned: { files: 0, size: 0 },
      expired: { records: 0 }
    };

    try {
      if (cleanTemp) {
        await this.cleanupTempFiles();
      }

      if (cleanQuarantine) {
        await this.cleanupQuarantinedFiles();
      }

      if (cleanOrphaned) {
        await this.cleanupOrphanedFiles();
      }

      if (cleanExpired) {
        await this.cleanupExpiredFileRecords();
      }

      logger.info('Manual file cleanup completed', results);
      return results;

    } catch (error) {
      logger.error('Manual file cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStats() {
    try {
      const stats = {
        tempFiles: await this.getDirectoryStats(this.tempUploadDir),
        quarantineFiles: await this.getDirectoryStats(this.quarantineDir),
        secureFiles: await this.getDirectoryStats(this.secureUploadDir),
        redisRecords: 0
      };

      if (this.redisClient && this.redisClient.isReady) {
        const fileKeys = await this.redisClient.keys('file:*');
        stats.redisRecords = fileKeys.length;
      }

      return stats;
    } catch (error) {
      logger.error('Failed to get cleanup stats:', error);
      return null;
    }
  }

  /**
   * Get directory statistics
   */
  async getDirectoryStats(dirPath) {
    let fileCount = 0;
    let totalSize = 0;

    await this.processDirectory(dirPath, (filePath, stats) => {
      fileCount++;
      totalSize += stats.size;
    });

    return {
      fileCount,
      totalSize: this.formatBytes(totalSize),
      totalSizeBytes: totalSize
    };
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

module.exports = new FileCleanupService();
