const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate } = require('../middleware/authMiddleware');
const tradingService = require('../services/tradingService');
const escrowService = require('../services/escrowService');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all trading routes
router.use(authenticate);

// Get all offers
router.get('/offers', async (req, res, next) => {
  try {
    const {
      cryptocurrency,
      fiatCurrency,
      type,
      minAmount,
      maxAmount,
      paymentMethod,
      location,
      limit = 50,
      offset = 0
    } = req.query;

    const filters = {
      cryptocurrency,
      fiatCurrency,
      type,
      minAmount: minAmount ? parseFloat(minAmount) : undefined,
      maxAmount: maxAmount ? parseFloat(maxAmount) : undefined,
      paymentMethod,
      location,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    const offers = await tradingService.getOffers(filters);

    res.json({
      success: true,
      data: { offers }
    });

  } catch (error) {
    next(error);
  }
});

// Create offer
router.post('/offers/create', [
  body('type').isIn(['buy', 'sell']).withMessage('Type must be buy or sell'),
  body('cryptocurrency').notEmpty().withMessage('Cryptocurrency is required'),
  body('fiatCurrency').notEmpty().withMessage('Fiat currency is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
  body('minAmount').isFloat({ min: 0 }).withMessage('Min amount must be a positive number'),
  body('maxAmount').isFloat({ min: 0 }).withMessage('Max amount must be a positive number'),
  body('paymentMethods').isArray().withMessage('Payment methods must be an array'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const offer = await tradingService.createOffer(req.user._id, req.body);

    res.status(201).json({
      success: true,
      message: 'Offer created successfully',
      data: { offer }
    });

  } catch (error) {
    next(error);
  }
});

// Get offer details
router.get('/offers/:offerId', async (req, res, next) => {
  try {
    const { offerId } = req.params;
    const offer = await tradingService.getOfferDetails(offerId);

    if (!offer) {
      return res.status(404).json({
        success: false,
        message: 'Offer not found'
      });
    }

    res.json({
      success: true,
      data: { offer }
    });

  } catch (error) {
    next(error);
  }
});

// Accept trade
router.post('/trades/accept', [
  body('offerId').notEmpty().withMessage('Offer ID is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
  body('paymentMethod').notEmpty().withMessage('Payment method is required'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const trade = await tradingService.acceptTrade(req.user._id, req.body);

    res.status(201).json({
      success: true,
      message: 'Trade accepted successfully',
      data: { trade }
    });

  } catch (error) {
    next(error);
  }
});

// Get active trades
router.get('/trades/active', async (req, res, next) => {
  try {
    const trades = await tradingService.getActiveTrades(req.user._id);

    res.json({
      success: true,
      data: { trades }
    });

  } catch (error) {
    next(error);
  }
});

// Get trade history
router.get('/trades/history', async (req, res, next) => {
  try {
    const { limit = 50, offset = 0, status } = req.query;

    const filters = {
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    if (status) {
      filters.status = status;
    }

    const trades = await tradingService.getTradeHistory(req.user._id, filters);

    res.json({
      success: true,
      data: { trades }
    });

  } catch (error) {
    next(error);
  }
});

// Get trade details
router.get('/trades/:tradeId', async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const trade = await tradingService.getTradeDetails(tradeId, req.user._id);

    if (!trade) {
      return res.status(404).json({
        success: false,
        message: 'Trade not found'
      });
    }

    res.json({
      success: true,
      data: { trade }
    });

  } catch (error) {
    next(error);
  }
});

// Fund escrow
router.post('/trades/fund-escrow/:tradeId', async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    
    const trade = await tradingService.getTradeDetails(tradeId, req.user._id);
    if (!trade) {
      return res.status(404).json({
        success: false,
        message: 'Trade not found'
      });
    }

    // Determine who should fund the escrow based on trade type
    const shouldFundEscrow = (trade.offer.type === 'sell' && trade.seller._id.toString() === req.user._id.toString()) ||
                            (trade.offer.type === 'buy' && trade.buyer._id.toString() === req.user._id.toString());

    if (!shouldFundEscrow) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to fund this escrow'
      });
    }

    const result = await escrowService.createEscrow(
      tradeId,
      trade.buyer._id,
      trade.seller._id,
      trade.cryptoAmount,
      trade.cryptocurrency,
      trade.offer.network || 'polygon'
    );

    res.json({
      success: true,
      message: 'Escrow funded successfully',
      data: result
    });

  } catch (error) {
    next(error);
  }
});

// Mark payment as sent
router.post('/trades/mark-payment-sent/:tradeId', [
  body('proofUrl').optional().isURL().withMessage('Proof URL must be valid'),
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const { proofUrl } = req.body;

    const success = await tradingService.markPaymentSent(tradeId, req.user._id, proofUrl);

    if (success) {
      res.json({
        success: true,
        message: 'Payment marked as sent'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to mark payment as sent'
      });
    }

  } catch (error) {
    next(error);
  }
});

// Confirm payment received
router.post('/trades/confirm-payment/:tradeId', async (req, res, next) => {
  try {
    const { tradeId } = req.params;

    const success = await tradingService.confirmPaymentReceived(tradeId, req.user._id);

    if (success) {
      // Release escrow funds
      await escrowService.releaseEscrow(tradeId, req.user._id);

      res.json({
        success: true,
        message: 'Payment confirmed and escrow released'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to confirm payment'
      });
    }

  } catch (error) {
    next(error);
  }
});

// Cancel trade
router.post('/trades/cancel/:tradeId', [
  body('reason').notEmpty().withMessage('Cancellation reason is required'),
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const { reason } = req.body;

    const success = await tradingService.cancelTrade(tradeId, req.user._id, reason);

    if (success) {
      // Cancel escrow if exists
      try {
        await escrowService.cancelEscrow(tradeId, req.user._id, reason);
      } catch (escrowError) {
        logger.warn(`Failed to cancel escrow for trade ${tradeId}: ${escrowError.message}`);
      }

      res.json({
        success: true,
        message: 'Trade cancelled successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to cancel trade'
      });
    }

  } catch (error) {
    next(error);
  }
});

// Initiate dispute
router.post('/trades/dispute/:tradeId', [
  body('reason').notEmpty().withMessage('Dispute reason is required'),
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const { reason } = req.body;

    const success = await tradingService.initiateDispute(tradeId, req.user._id, reason);

    if (success) {
      // Initiate escrow dispute
      await escrowService.initiateDispute(tradeId, req.user._id, reason);

      res.json({
        success: true,
        message: 'Dispute initiated successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to initiate dispute'
      });
    }

  } catch (error) {
    next(error);
  }
});

// Send trade message
router.post('/trades/message/:tradeId', [
  body('message').notEmpty().withMessage('Message is required'),
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const { message } = req.body;

    const success = await tradingService.sendTradeMessage(tradeId, req.user._id, message);

    if (success) {
      res.json({
        success: true,
        message: 'Message sent successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to send message'
      });
    }

  } catch (error) {
    next(error);
  }
});

// Get trading statistics
router.get('/stats', async (req, res, next) => {
  try {
    const stats = await tradingService.getTradingStats(req.user._id);

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    next(error);
  }
});

// Get escrow statistics (admin only)
router.get('/escrow/stats', authenticate, async (req, res, next) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin role required.'
      });
    }

    const stats = await escrowService.getEscrowStats();

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
