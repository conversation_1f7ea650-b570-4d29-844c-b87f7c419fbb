const { ethers } = require("hardhat");
const hre = require("hardhat");

async function main() {
  console.log("Deploying KryptoPesa Escrow Contract...");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);

  const balance = await ethers.provider.getBalance(deployer.address);
  console.log("Account balance:", ethers.formatEther(balance), "ETH");

  // Deploy the contract
  const KryptoPesaEscrow = await ethers.getContractFactory("KryptoPesaEscrow");
  
  // Set fee collector and dispute resolver addresses
  const feeCollector = deployer.address; // In production, use a dedicated address
  const disputeResolver = deployer.address; // In production, use a dedicated admin address
  
  console.log("Fee Collector:", feeCollector);
  console.log("Dispute Resolver:", disputeResolver);
  
  const escrow = await KryptoPesaEscrow.deploy(feeCollector, disputeResolver);
  await escrow.waitForDeployment();

  console.log("KryptoPesaEscrow deployed to:", await escrow.getAddress());
  console.log("Transaction hash:", escrow.deploymentTransaction().hash);

  // Wait for confirmations (fewer for localhost)
  const confirmations = hre.network.name === "localhost" ? 1 : 5;
  console.log(`Waiting for ${confirmations} confirmations...`);
  await escrow.deploymentTransaction().wait(confirmations);

  // Verify contract on Polygonscan (if on Polygon network)
  if (hre.network.name === "polygon" || hre.network.name === "mumbai") {
    console.log("Verifying contract on Polygonscan...");
    try {
      await hre.run("verify:verify", {
        address: contractAddress,
        constructorArguments: [feeCollector, disputeResolver],
      });
      console.log("Contract verified successfully");
    } catch (error) {
      console.log("Verification failed:", error.message);
    }
  }

  // Save deployment info
  const contractAddress = await escrow.getAddress();
  const deploymentTx = escrow.deploymentTransaction();
  const deploymentInfo = {
    network: hre.network.name,
    contractAddress: contractAddress,
    feeCollector: feeCollector,
    disputeResolver: disputeResolver,
    deployer: deployer.address,
    deploymentTime: new Date().toISOString(),
    transactionHash: deploymentTx.hash,
    blockNumber: deploymentTx.blockNumber,
  };

  console.log("\n=== Deployment Summary ===");
  console.log(JSON.stringify(deploymentInfo, null, 2));

  // Save to file
  const fs = require("fs");
  const path = require("path");
  
  const deploymentsDir = path.join(__dirname, "../deployments");
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir);
  }
  
  const deploymentFile = path.join(deploymentsDir, `${hre.network.name}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  
  console.log(`Deployment info saved to: ${deploymentFile}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
