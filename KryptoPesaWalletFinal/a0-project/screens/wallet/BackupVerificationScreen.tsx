import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface RouteParams {
  mnemonic: string[];
}

export default function BackupVerificationScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { mnemonic } = route.params as RouteParams;
  
  const [selectedWords, setSelectedWords] = useState<(string | undefined)[]>([]);
  const [shuffledWords, setShuffledWords] = useState<string[]>([]);
  const [verificationWords, setVerificationWords] = useState<number[]>([]);

  useEffect(() => {
    // Select 6 random positions for verification
    const positions = [];
    while (positions.length < 6) {
      const pos = Math.floor(Math.random() * 24);
      if (!positions.includes(pos)) {
        positions.push(pos);
      }
    }
    positions.sort((a, b) => a - b);
    setVerificationWords(positions);

    // Initialize selectedWords array with undefined values for each position
    setSelectedWords(new Array(positions.length).fill(undefined));

    // Create shuffled array of words for selection
    const wordsToVerify = positions.map(pos => mnemonic[pos]);
    const extraWords = ['apple', 'banana', 'cherry', 'dragon', 'elephant', 'forest'];
    const allWords = [...wordsToVerify, ...extraWords];
    setShuffledWords(allWords.sort(() => Math.random() - 0.5));
  }, [mnemonic]);

  const handleWordSelect = (word: string) => {
    // Find the first empty position
    const emptyIndex = selectedWords.findIndex(w => w === undefined || w === '');
    if (emptyIndex !== -1) {
      const newSelected = [...selectedWords];
      newSelected[emptyIndex] = word;
      setSelectedWords(newSelected);
    } else if (selectedWords.length < verificationWords.length) {
      setSelectedWords([...selectedWords, word]);
    }
  };

  const handleWordRemove = (index: number) => {
    const newSelected = [...selectedWords];
    newSelected[index] = undefined;
    setSelectedWords(newSelected);
  };

  const handleVerify = () => {
    const correctWords = verificationWords.map(pos => mnemonic[pos]);
    const isCorrect = selectedWords.every((word, index) => word === correctWords[index]);

    if (isCorrect) {
      navigation.navigate('SetPIN', { mnemonic });
    } else {
      Alert.alert(
        'Verification Failed',
        'The words you selected don\'t match your recovery phrase. Please try again.',
        [{ text: 'OK', onPress: () => setSelectedWords(new Array(verificationWords.length).fill(undefined)) }]
      );
    }
  };

  const isComplete = selectedWords.every(word => word !== undefined && word !== '');
  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Verify Backup</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.stepIndicator}>
          <View style={[styles.step, styles.completedStep]}>
            <Ionicons name="checkmark" size={16} color="white" />
          </View>
          <View style={[styles.stepLine, styles.completedLine]} />
          <View style={[styles.step, styles.activeStep]}>
            <Text style={styles.stepNumber}>2</Text>
          </View>
          <View style={styles.stepLine} />
          <View style={styles.step}>
            <Text style={styles.stepNumber}>3</Text>
          </View>
        </View>

        <View style={styles.titleContainer}>
          <Ionicons name="checkmark-circle" size={48} color={theme.colors.primary} />
          <Text style={styles.title}>Verify Your Backup</Text>
          <Text style={styles.subtitle}>
            Select the words in the correct order to verify you've saved your recovery phrase.
          </Text>
        </View>

        <View style={styles.verificationContainer}>
          <Text style={styles.instructionText}>
            Select words #{verificationWords.map(pos => pos + 1).join(', ')}:
          </Text>
          
          <View style={styles.selectedWordsContainer}>
            {verificationWords.map((pos, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.selectedWordSlot,
                  selectedWords[index] && styles.filledWordSlot
                ]}
                onPress={() => selectedWords[index] && handleWordRemove(index)}
              >
                <Text style={styles.wordPosition}>{pos + 1}</Text>
                <Text style={[
                  styles.selectedWord,
                  !selectedWords[index] && styles.emptyWord
                ]}>
                  {selectedWords[index] || 'Select word'}
                </Text>
                {selectedWords[index] && (
                  <Ionicons name="close" size={16} color={theme.colors.textSecondary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.wordsGrid}>
          <Text style={styles.gridTitle}>Select from these words:</Text>
          <View style={styles.wordOptions}>
            {shuffledWords.map((word, index) => {
              // Count how many times this word appears in the verification positions
              const requiredCount = verificationWords.filter(pos => mnemonic[pos] === word).length;
              // Count how many times this word has been selected
              const selectedCount = selectedWords.filter(w => w === word).length;
              // Word is disabled only if it's a correct word AND has been selected the required number of times
              // Decoy words (requiredCount = 0) should remain selectable
              const isDisabled = requiredCount > 0 && selectedCount >= requiredCount;

              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.wordOption,
                    isDisabled && styles.selectedWordOption
                  ]}
                  onPress={() => !isDisabled && handleWordSelect(word)}
                  disabled={isDisabled}
                >
                  <Text style={[
                    styles.wordOptionText,
                    isDisabled && styles.selectedWordOptionText
                  ]}>
                    {word}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.verifyButton, !isComplete && styles.disabledButton]}
          onPress={handleVerify}
          disabled={!isComplete}
        >
          <Text style={styles.verifyButtonText}>Verify & Continue</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
  },
  step: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeStep: {
    backgroundColor: theme.colors.primary,
  },
  completedStep: {
    backgroundColor: theme.colors.success,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  stepLine: {
    width: 40,
    height: 2,
    backgroundColor: theme.colors.border,
    marginHorizontal: 8,
  },
  completedLine: {
    backgroundColor: theme.colors.success,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  verificationContainer: {
    marginBottom: 32,
  },
  instructionText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  selectedWordsContainer: {
    gap: 12,
  },
  selectedWordSlot: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground,
    borderWidth: 2,
    borderColor: theme.colors.border,
    borderRadius: 12,
    padding: 16,
    minHeight: 56,
  },
  filledWordSlot: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primaryLight,
  },
  wordPosition: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
    marginRight: 12,
    minWidth: 20,
  },
  selectedWord: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
  },
  emptyWord: {
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  wordsGrid: {
    marginBottom: 24,
  },
  gridTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  wordOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  wordOption: {
    backgroundColor: theme.colors.cardBackground,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectedWordOption: {
    backgroundColor: theme.colors.disabled,
    borderColor: theme.colors.disabled,
  },
  wordOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  selectedWordOptionText: {
    color: theme.colors.textSecondary,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  verifyButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: theme.colors.disabled,
  },
  verifyButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
});
