const logger = require('../utils/logger');
const { monitoringIntegrationService } = require('./monitoringIntegrationService');
const Trade = require('../models/Trade');
const Dispute = require('../models/Dispute');
const Wallet = require('../models/Wallet');
const User = require('../models/User');

/**
 * Business Flow Monitoring Service
 * Monitors critical business flows and triggers alerts for anomalies
 */
class BusinessFlowMonitoringService {
  constructor() {
    this.monitoringIntervals = new Map();
    this.flowMetrics = {
      trades: {
        created: 0,
        funded: 0,
        completed: 0,
        failed: 0,
        disputed: 0,
        stuck: 0,
        avgCompletionTime: 0
      },
      escrows: {
        created: 0,
        funded: 0,
        released: 0,
        failed: 0,
        stuck: 0,
        avgFundingTime: 0
      },
      disputes: {
        created: 0,
        resolved: 0,
        escalated: 0,
        unresolved: 0,
        avgResolutionTime: 0
      },
      wallets: {
        created: 0,
        transactions: 0,
        failed: 0,
        balanceUpdates: 0
      },
      authentication: {
        logins: 0,
        failures: 0,
        suspiciousActivity: 0,
        blockedAttempts: 0
      }
    };
    
    this.alertThresholds = {
      tradeFailureRate: 0.05, // 5%
      escrowFailureRate: 0.02, // 2%
      disputeRate: 0.10, // 10%
      stuckTradeThreshold: 24 * 60 * 60 * 1000, // 24 hours
      stuckEscrowThreshold: 2 * 60 * 60 * 1000, // 2 hours
      unresolvedDisputeThreshold: 10,
      authFailureRate: 0.15, // 15%
      abnormalTradingVolumeThreshold: 5.0 // 5x normal volume
    };
    
    this.isInitialized = false;
  }

  /**
   * Initialize business flow monitoring
   */
  async initialize() {
    try {
      logger.info('🔍 Initializing Business Flow Monitoring Service...');
      
      // Start monitoring intervals
      await this.startTradeFlowMonitoring();
      await this.startEscrowFlowMonitoring();
      await this.startDisputeFlowMonitoring();
      await this.startWalletFlowMonitoring();
      await this.startAuthenticationFlowMonitoring();
      await this.startAbnormalActivityMonitoring();
      
      this.isInitialized = true;
      logger.info('✅ Business Flow Monitoring Service initialized successfully');
      
      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize Business Flow Monitoring Service:', error);
      throw error;
    }
  }

  /**
   * Start trade flow monitoring
   */
  async startTradeFlowMonitoring() {
    const interval = setInterval(async () => {
      try {
        await this.monitorTradeFlows();
      } catch (error) {
        logger.error('Error in trade flow monitoring:', error);
      }
    }, 5 * 60 * 1000); // Every 5 minutes
    
    this.monitoringIntervals.set('tradeFlow', interval);
    logger.info('🔄 Trade flow monitoring started');
  }

  /**
   * Monitor trade flows for anomalies
   */
  async monitorTradeFlows() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // Get trade statistics
      const recentTrades = await Trade.find({
        createdAt: { $gte: oneHourAgo }
      });

      const stuckTrades = await Trade.find({
        status: { $in: ['created', 'funded', 'payment_sent'] },
        createdAt: { $lt: oneDayAgo }
      });

      const failedTrades = await Trade.find({
        status: { $in: ['cancelled', 'refunded'] },
        createdAt: { $gte: oneHourAgo }
      });

      const disputedTrades = await Trade.find({
        status: 'disputed',
        createdAt: { $gte: oneHourAgo }
      });

      // Calculate metrics
      const totalTrades = recentTrades.length;
      const failureRate = totalTrades > 0 ? failedTrades.length / totalTrades : 0;
      const disputeRate = totalTrades > 0 ? disputedTrades.length / totalTrades : 0;

      // Update metrics
      this.flowMetrics.trades.created = totalTrades;
      this.flowMetrics.trades.failed = failedTrades.length;
      this.flowMetrics.trades.disputed = disputedTrades.length;
      this.flowMetrics.trades.stuck = stuckTrades.length;

      // Record metrics in monitoring system
      monitoringIntegrationService.recordTrade({
        status: 'monitoring_check',
        totalTrades,
        failedTrades: failedTrades.length,
        disputedTrades: disputedTrades.length,
        stuckTrades: stuckTrades.length
      });

      // Check for alerts
      if (failureRate > this.alertThresholds.tradeFailureRate) {
        await this.triggerAlert('HIGH_TRADE_FAILURE_RATE', {
          message: `Trade failure rate is ${(failureRate * 100).toFixed(2)}%`,
          threshold: this.alertThresholds.tradeFailureRate * 100,
          current: (failureRate * 100).toFixed(2),
          severity: 'high',
          details: {
            totalTrades,
            failedTrades: failedTrades.length,
            timeWindow: '1 hour'
          }
        });
      }

      if (disputeRate > this.alertThresholds.disputeRate) {
        await this.triggerAlert('HIGH_DISPUTE_RATE', {
          message: `Trade dispute rate is ${(disputeRate * 100).toFixed(2)}%`,
          threshold: this.alertThresholds.disputeRate * 100,
          current: (disputeRate * 100).toFixed(2),
          severity: 'medium',
          details: {
            totalTrades,
            disputedTrades: disputedTrades.length,
            timeWindow: '1 hour'
          }
        });
      }

      if (stuckTrades.length > 0) {
        await this.triggerAlert('STUCK_TRADES_DETECTED', {
          message: `${stuckTrades.length} trades stuck for more than 24 hours`,
          threshold: 0,
          current: stuckTrades.length,
          severity: 'critical',
          details: {
            stuckTrades: stuckTrades.map(t => ({
              id: t._id,
              status: t.status,
              createdAt: t.createdAt,
              cryptocurrency: t.cryptocurrency?.symbol,
              amount: t.fiat?.amount
            }))
          }
        });
      }

      logger.debug('Trade flow monitoring completed', {
        totalTrades,
        failureRate: (failureRate * 100).toFixed(2) + '%',
        disputeRate: (disputeRate * 100).toFixed(2) + '%',
        stuckTrades: stuckTrades.length
      });

    } catch (error) {
      logger.error('Error monitoring trade flows:', error);
    }
  }

  /**
   * Start escrow flow monitoring
   */
  async startEscrowFlowMonitoring() {
    const interval = setInterval(async () => {
      try {
        await this.monitorEscrowFlows();
      } catch (error) {
        logger.error('Error in escrow flow monitoring:', error);
      }
    }, 3 * 60 * 1000); // Every 3 minutes
    
    this.monitoringIntervals.set('escrowFlow', interval);
    logger.info('🔄 Escrow flow monitoring started');
  }

  /**
   * Monitor escrow flows for anomalies
   */
  async monitorEscrowFlows() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);

      // Get escrow statistics from trades
      const recentEscrows = await Trade.find({
        'escrow.contractAddress': { $exists: true },
        createdAt: { $gte: oneHourAgo }
      });

      const stuckEscrows = await Trade.find({
        status: { $in: ['created', 'funded'] },
        'escrow.funded': false,
        createdAt: { $lt: twoHoursAgo }
      });

      const failedEscrows = await Trade.find({
        status: { $in: ['cancelled', 'refunded'] },
        'escrow.contractAddress': { $exists: true },
        createdAt: { $gte: oneHourAgo }
      });

      // Calculate metrics
      const totalEscrows = recentEscrows.length;
      const escrowFailureRate = totalEscrows > 0 ? failedEscrows.length / totalEscrows : 0;

      // Update metrics
      this.flowMetrics.escrows.created = totalEscrows;
      this.flowMetrics.escrows.failed = failedEscrows.length;
      this.flowMetrics.escrows.stuck = stuckEscrows.length;

      // Record metrics in monitoring system
      monitoringIntegrationService.recordEscrow({
        status: 'monitoring_check',
        totalEscrows,
        failedEscrows: failedEscrows.length,
        stuckEscrows: stuckEscrows.length
      });

      // Check for alerts
      if (escrowFailureRate > this.alertThresholds.escrowFailureRate) {
        await this.triggerAlert('HIGH_ESCROW_FAILURE_RATE', {
          message: `Escrow failure rate is ${(escrowFailureRate * 100).toFixed(2)}%`,
          threshold: this.alertThresholds.escrowFailureRate * 100,
          current: (escrowFailureRate * 100).toFixed(2),
          severity: 'critical',
          details: {
            totalEscrows,
            failedEscrows: failedEscrows.length,
            timeWindow: '1 hour'
          }
        });
      }

      if (stuckEscrows.length > 0) {
        await this.triggerAlert('STUCK_ESCROWS_DETECTED', {
          message: `${stuckEscrows.length} escrows stuck for more than 2 hours`,
          threshold: 0,
          current: stuckEscrows.length,
          severity: 'critical',
          details: {
            stuckEscrows: stuckEscrows.map(e => ({
              id: e._id,
              status: e.status,
              createdAt: e.createdAt,
              contractAddress: e.escrow?.contractAddress,
              cryptocurrency: e.cryptocurrency?.symbol
            }))
          }
        });
      }

      logger.debug('Escrow flow monitoring completed', {
        totalEscrows,
        escrowFailureRate: (escrowFailureRate * 100).toFixed(2) + '%',
        stuckEscrows: stuckEscrows.length
      });

    } catch (error) {
      logger.error('Error monitoring escrow flows:', error);
    }
  }

  /**
   * Start dispute flow monitoring
   */
  async startDisputeFlowMonitoring() {
    const interval = setInterval(async () => {
      try {
        await this.monitorDisputeFlows();
      } catch (error) {
        logger.error('Error in dispute flow monitoring:', error);
      }
    }, 10 * 60 * 1000); // Every 10 minutes

    this.monitoringIntervals.set('disputeFlow', interval);
    logger.info('🔄 Dispute flow monitoring started');
  }

  /**
   * Monitor dispute flows for anomalies
   */
  async monitorDisputeFlows() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);

      // Get dispute statistics
      const recentDisputes = await Dispute.find({
        createdAt: { $gte: oneHourAgo }
      });

      const unresolvedDisputes = await Dispute.find({
        status: { $in: ['open', 'investigating'] }
      });

      const oldUnresolvedDisputes = await Dispute.find({
        status: { $in: ['open', 'investigating'] },
        createdAt: { $lt: threeDaysAgo }
      });

      // Update metrics
      this.flowMetrics.disputes.created = recentDisputes.length;
      this.flowMetrics.disputes.unresolved = unresolvedDisputes.length;

      // Record metrics in monitoring system
      await monitoringIntegrationService.updateDisputeMetrics();

      // Check for alerts
      if (unresolvedDisputes.length > this.alertThresholds.unresolvedDisputeThreshold) {
        await this.triggerAlert('HIGH_UNRESOLVED_DISPUTES', {
          message: `${unresolvedDisputes.length} unresolved disputes`,
          threshold: this.alertThresholds.unresolvedDisputeThreshold,
          current: unresolvedDisputes.length,
          severity: 'medium',
          details: {
            unresolvedDisputes: unresolvedDisputes.length,
            oldUnresolved: oldUnresolvedDisputes.length
          }
        });
      }

      if (oldUnresolvedDisputes.length > 0) {
        await this.triggerAlert('OLD_UNRESOLVED_DISPUTES', {
          message: `${oldUnresolvedDisputes.length} disputes unresolved for more than 3 days`,
          threshold: 0,
          current: oldUnresolvedDisputes.length,
          severity: 'high',
          details: {
            oldDisputes: oldUnresolvedDisputes.map(d => ({
              id: d._id,
              tradeId: d.trade,
              reason: d.reason,
              createdAt: d.createdAt,
              status: d.status
            }))
          }
        });
      }

      logger.debug('Dispute flow monitoring completed', {
        recentDisputes: recentDisputes.length,
        unresolvedDisputes: unresolvedDisputes.length,
        oldUnresolved: oldUnresolvedDisputes.length
      });

    } catch (error) {
      logger.error('Error monitoring dispute flows:', error);
    }
  }

  /**
   * Start wallet flow monitoring
   */
  async startWalletFlowMonitoring() {
    const interval = setInterval(async () => {
      try {
        await this.monitorWalletFlows();
      } catch (error) {
        logger.error('Error in wallet flow monitoring:', error);
      }
    }, 15 * 60 * 1000); // Every 15 minutes

    this.monitoringIntervals.set('walletFlow', interval);
    logger.info('🔄 Wallet flow monitoring started');
  }

  /**
   * Monitor wallet flows for anomalies
   */
  async monitorWalletFlows() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Get wallet statistics
      const recentWallets = await Wallet.find({
        createdAt: { $gte: oneHourAgo }
      });

      const walletsWithErrors = await Wallet.find({
        'status': 'error',
        updatedAt: { $gte: oneHourAgo }
      });

      // Update metrics
      this.flowMetrics.wallets.created = recentWallets.length;
      this.flowMetrics.wallets.failed = walletsWithErrors.length;

      // Record metrics in monitoring system
      monitoringIntegrationService.recordWalletOperation('monitoring_check', {
        totalWallets: recentWallets.length,
        failedWallets: walletsWithErrors.length
      });

      // Check for wallet creation failures
      if (walletsWithErrors.length > 0) {
        await this.triggerAlert('WALLET_OPERATION_FAILURES', {
          message: `${walletsWithErrors.length} wallet operations failed in the last hour`,
          threshold: 0,
          current: walletsWithErrors.length,
          severity: 'medium',
          details: {
            failedWallets: walletsWithErrors.length,
            timeWindow: '1 hour'
          }
        });
      }

      logger.debug('Wallet flow monitoring completed', {
        recentWallets: recentWallets.length,
        walletsWithErrors: walletsWithErrors.length
      });

    } catch (error) {
      logger.error('Error monitoring wallet flows:', error);
    }
  }

  /**
   * Start authentication flow monitoring
   */
  async startAuthenticationFlowMonitoring() {
    const interval = setInterval(async () => {
      try {
        await this.monitorAuthenticationFlows();
      } catch (error) {
        logger.error('Error in authentication flow monitoring:', error);
      }
    }, 5 * 60 * 1000); // Every 5 minutes

    this.monitoringIntervals.set('authFlow', interval);
    logger.info('🔄 Authentication flow monitoring started');
  }

  /**
   * Monitor authentication flows for anomalies
   */
  async monitorAuthenticationFlows() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Get authentication statistics from user login attempts
      const recentLogins = await User.find({
        lastLogin: { $gte: oneHourAgo }
      });

      const recentFailedLogins = await User.find({
        'loginAttempts.count': { $gt: 0 },
        'loginAttempts.lastAttempt': { $gte: oneHourAgo }
      });

      const blockedUsers = await User.find({
        'loginAttempts.blocked': true,
        'loginAttempts.blockedUntil': { $gt: now }
      });

      // Calculate metrics
      const totalLogins = recentLogins.length;
      const failedLogins = recentFailedLogins.length;
      const authFailureRate = totalLogins > 0 ? failedLogins / totalLogins : 0;

      // Update metrics
      this.flowMetrics.authentication.logins = totalLogins;
      this.flowMetrics.authentication.failures = failedLogins;
      this.flowMetrics.authentication.blockedAttempts = blockedUsers.length;

      // Record metrics in monitoring system
      monitoringIntegrationService.recordSecurityEvent({
        eventType: 'auth_monitoring',
        severity: 'low',
        details: {
          totalLogins,
          failedLogins,
          blockedUsers: blockedUsers.length,
          authFailureRate: (authFailureRate * 100).toFixed(2) + '%'
        }
      });

      // Check for alerts
      if (authFailureRate > this.alertThresholds.authFailureRate) {
        await this.triggerAlert('HIGH_AUTH_FAILURE_RATE', {
          message: `Authentication failure rate is ${(authFailureRate * 100).toFixed(2)}%`,
          threshold: this.alertThresholds.authFailureRate * 100,
          current: (authFailureRate * 100).toFixed(2),
          severity: 'medium',
          details: {
            totalLogins,
            failedLogins,
            timeWindow: '1 hour'
          }
        });
      }

      if (blockedUsers.length > 5) {
        await this.triggerAlert('MULTIPLE_BLOCKED_USERS', {
          message: `${blockedUsers.length} users currently blocked due to failed login attempts`,
          threshold: 5,
          current: blockedUsers.length,
          severity: 'medium',
          details: {
            blockedUsers: blockedUsers.length
          }
        });
      }

      logger.debug('Authentication flow monitoring completed', {
        totalLogins,
        failedLogins,
        authFailureRate: (authFailureRate * 100).toFixed(2) + '%',
        blockedUsers: blockedUsers.length
      });

    } catch (error) {
      logger.error('Error monitoring authentication flows:', error);
    }
  }

  /**
   * Trigger alert through monitoring system
   */
  async triggerAlert(type, alertData) {
    try {
      await monitoringIntegrationService.triggerAlert({
        type,
        ...alertData
      });

      logger.warn(`Business flow alert triggered: ${type}`, alertData);
    } catch (error) {
      logger.error('Failed to trigger business flow alert:', error);
    }
  }

  /**
   * Start abnormal activity monitoring
   */
  async startAbnormalActivityMonitoring() {
    const interval = setInterval(async () => {
      try {
        await this.monitorAbnormalActivity();
      } catch (error) {
        logger.error('Error in abnormal activity monitoring:', error);
      }
    }, 10 * 60 * 1000); // Every 10 minutes

    this.monitoringIntervals.set('abnormalActivity', interval);
    logger.info('🔄 Abnormal activity monitoring started');
  }

  /**
   * Monitor for abnormal trading activity
   */
  async monitorAbnormalActivity() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // Get trading volume statistics
      const recentTrades = await Trade.find({
        createdAt: { $gte: oneHourAgo },
        status: { $ne: 'cancelled' }
      });

      const dailyTrades = await Trade.find({
        createdAt: { $gte: oneDayAgo },
        status: { $ne: 'cancelled' }
      });

      // Calculate volume metrics
      const hourlyVolume = recentTrades.reduce((sum, trade) => {
        return sum + (trade.fiat?.amount || 0);
      }, 0);

      const dailyVolume = dailyTrades.reduce((sum, trade) => {
        return sum + (trade.fiat?.amount || 0);
      }, 0);

      const avgHourlyVolume = dailyVolume / 24;
      const volumeRatio = avgHourlyVolume > 0 ? hourlyVolume / avgHourlyVolume : 0;

      // Check for abnormal volume spikes
      if (volumeRatio > this.alertThresholds.abnormalTradingVolumeThreshold) {
        await this.triggerAlert('ABNORMAL_TRADING_VOLUME', {
          message: `Trading volume is ${volumeRatio.toFixed(2)}x higher than normal`,
          threshold: this.alertThresholds.abnormalTradingVolumeThreshold,
          current: volumeRatio.toFixed(2),
          severity: 'medium',
          details: {
            hourlyVolume: hourlyVolume.toFixed(2),
            avgHourlyVolume: avgHourlyVolume.toFixed(2),
            volumeRatio: volumeRatio.toFixed(2),
            recentTrades: recentTrades.length
          }
        });
      }

      // Check for suspicious user patterns
      const userTradeCount = {};
      recentTrades.forEach(trade => {
        const buyerId = trade.buyer.toString();
        const sellerId = trade.seller.toString();
        userTradeCount[buyerId] = (userTradeCount[buyerId] || 0) + 1;
        userTradeCount[sellerId] = (userTradeCount[sellerId] || 0) + 1;
      });

      const suspiciousUsers = Object.entries(userTradeCount)
        .filter(([userId, count]) => count > 10) // More than 10 trades per hour
        .map(([userId, count]) => ({ userId, count }));

      if (suspiciousUsers.length > 0) {
        await this.triggerAlert('SUSPICIOUS_USER_ACTIVITY', {
          message: `${suspiciousUsers.length} users with abnormally high trading activity`,
          threshold: 10,
          current: suspiciousUsers.length,
          severity: 'medium',
          details: {
            suspiciousUsers: suspiciousUsers.slice(0, 5), // Limit to first 5
            timeWindow: '1 hour'
          }
        });
      }

      // Check for rapid-fire trade creation
      const rapidTrades = await Trade.find({
        createdAt: { $gte: new Date(now.getTime() - 5 * 60 * 1000) } // Last 5 minutes
      });

      if (rapidTrades.length > 20) { // More than 20 trades in 5 minutes
        await this.triggerAlert('RAPID_TRADE_CREATION', {
          message: `${rapidTrades.length} trades created in the last 5 minutes`,
          threshold: 20,
          current: rapidTrades.length,
          severity: 'high',
          details: {
            rapidTrades: rapidTrades.length,
            timeWindow: '5 minutes'
          }
        });
      }

      logger.debug('Abnormal activity monitoring completed', {
        hourlyVolume: hourlyVolume.toFixed(2),
        volumeRatio: volumeRatio.toFixed(2),
        suspiciousUsers: suspiciousUsers.length,
        rapidTrades: rapidTrades.length
      });

    } catch (error) {
      logger.error('Error monitoring abnormal activity:', error);
    }
  }

  /**
   * Record business flow event
   */
  recordBusinessFlowEvent(flowType, eventType, eventData) {
    try {
      const timestamp = new Date().toISOString();

      // Update internal metrics
      if (this.flowMetrics[flowType]) {
        if (this.flowMetrics[flowType][eventType] !== undefined) {
          this.flowMetrics[flowType][eventType]++;
        }
      }

      // Record in monitoring system based on flow type
      switch (flowType) {
        case 'trade':
          monitoringIntegrationService.recordTrade(eventData);
          break;
        case 'escrow':
          monitoringIntegrationService.recordEscrow(eventData);
          break;
        case 'wallet':
          monitoringIntegrationService.recordWalletOperation(eventType, eventData);
          break;
        case 'security':
          monitoringIntegrationService.recordSecurityEvent(eventData);
          break;
        default:
          logger.debug('Unknown flow type for business flow event', { flowType, eventType });
      }

      logger.debug('Business flow event recorded', {
        flowType,
        eventType,
        timestamp
      });

    } catch (error) {
      logger.error('Failed to record business flow event:', error);
    }
  }

  /**
   * Get current flow metrics
   */
  getFlowMetrics() {
    return {
      ...this.flowMetrics,
      isInitialized: this.isInitialized,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Shutdown monitoring service
   */
  async shutdown() {
    try {
      // Clear all monitoring intervals
      for (const [name, interval] of this.monitoringIntervals) {
        clearInterval(interval);
        logger.info(`Stopped ${name} monitoring`);
      }
      
      this.monitoringIntervals.clear();
      this.isInitialized = false;
      
      logger.info('✅ Business Flow Monitoring Service shutdown completed');
    } catch (error) {
      logger.error('Error during Business Flow Monitoring Service shutdown:', error);
    }
  }
}

// Export singleton instance
const businessFlowMonitoringService = new BusinessFlowMonitoringService();
module.exports = { businessFlowMonitoringService };
