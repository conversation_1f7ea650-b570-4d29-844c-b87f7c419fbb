const { ethers } = require('ethers');
const logger = require('../../utils/logger');
const { circuitBreakers } = require('../../utils/circuitBreaker');

class EthereumService {
  constructor() {
    this.providers = {
      polygon: new ethers.providers.JsonRpcProvider(process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com'),
      ethereum: new ethers.providers.JsonRpcProvider(process.env.ETHEREUM_RPC_URL || 'https://eth.llamarpc.com'),
      localhost: new ethers.providers.JsonRpcProvider('http://127.0.0.1:8545')
    };

    // For testing, use simple wallet setup
    this.wallets = {
      polygon: process.env.POLYGON_PRIVATE_KEY ? new ethers.Wallet(process.env.POLYGON_PRIVATE_KEY, this.providers.polygon) : null,
      ethereum: process.env.ETHEREUM_PRIVATE_KEY ? new ethers.Wallet(process.env.ETHEREUM_PRIVATE_KEY, this.providers.ethereum) : null,
      localhost: process.env.POLYGON_PRIVATE_KEY ? new ethers.Wallet(process.env.POLYGON_PRIVATE_KEY, this.providers.localhost) : null
    };

    // Contract ABIs (simplified for demo)
    this.escrowABI = [
      "function createTrade(address buyer, address tokenAddress, uint256 amount, uint256 fiatAmount, string fiatCurrency, string paymentMethod, bytes32 paymentHash) external returns (uint256)",
      "function fundTrade(uint256 tradeId) external",
      "function confirmPaymentSent(uint256 tradeId) external",
      "function confirmPaymentReceived(uint256 tradeId) external",
      "function createDispute(uint256 tradeId, string reason) external",
      "function resolveDispute(uint256 tradeId, address winner) external",
      "function getTrade(uint256 tradeId) external view returns (tuple(uint256 tradeId, address seller, address buyer, address tokenAddress, uint256 amount, uint256 fiatAmount, string fiatCurrency, uint256 commissionRate, uint8 status, uint256 createdAt, uint256 expiresAt, uint256 completedAt, bool sellerConfirmed, bool buyerConfirmed, string paymentMethod, bytes32 paymentHash))",
      "function nextTradeId() external view returns (uint256)",
      "function defaultCommissionRate() external view returns (uint256)",
      "function feeCollector() external view returns (address)",
      "function disputeResolver() external view returns (address)",
      "function authorizedTokens(address) external view returns (bool)",
      "function userReputation(address) external view returns (uint256)",
      "function completedTrades(address) external view returns (uint256)",
      "function authorizeToken(address token, bool authorized) external",
      "function setCommissionRate(uint256 rate) external",
      "function emergencyRefund(uint256 tradeId) external",
      "event TradeCreated(uint256 indexed tradeId, address indexed seller, address indexed buyer, uint256 amount, string fiatCurrency)",
      "event TradeFunded(uint256 indexed tradeId, uint256 amount)",
      "event PaymentSent(uint256 indexed tradeId, address indexed buyer)",
      "event TradeCompleted(uint256 indexed tradeId, address indexed buyer, address indexed seller)",
      "event TradeCancelled(uint256 indexed tradeId, string reason)",
      "event DisputeCreated(uint256 indexed tradeId, address indexed initiator, string reason)",
      "event DisputeResolved(uint256 indexed tradeId, address indexed winner)"
    ];

    this.initialized = false;
    this.maxRetries = 3;
    this.baseDelay = 1000; // 1 second
  }

  /**
   * Execute blockchain operation with retry logic and circuit breaker
   */
  async executeWithRetry(operation, maxRetries = this.maxRetries) {
    return await circuitBreakers.blockchain.execute(async () => {
      let lastError;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await operation();
        } catch (error) {
          lastError = error;

          // Don't retry for certain errors
          if (this.isNonRetryableError(error)) {
            throw error;
          }

          if (attempt < maxRetries) {
            const delay = this.calculateBackoffDelay(attempt);
            logger.warn(`Blockchain operation failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, {
              error: error.message,
              attempt,
              maxRetries
            });
            await this.sleep(delay);
          }
        }
      }

      throw lastError;
    });
  }

  /**
   * Check if error should not be retried
   */
  isNonRetryableError(error) {
    const nonRetryableMessages = [
      'insufficient funds',
      'nonce too low',
      'replacement transaction underpriced',
      'invalid signature',
      'execution reverted',
      'gas required exceeds allowance'
    ];

    return nonRetryableMessages.some(msg =>
      error.message.toLowerCase().includes(msg.toLowerCase())
    );
  }

  /**
   * Calculate exponential backoff delay
   */
  calculateBackoffDelay(attempt) {
    const jitter = Math.random() * 0.1; // Add 10% jitter
    return Math.floor(this.baseDelay * Math.pow(2, attempt - 1) * (1 + jitter));
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async initialize() {
    if (!this.initialized) {
      this.initialized = true;
      logger.info('Ethereum service initialized');
    }

    // ERC20 ABI for token interactions
    this.erc20ABI = [
      "function balanceOf(address owner) view returns (uint256)",
      "function transfer(address to, uint256 amount) returns (bool)",
      "function transferFrom(address from, address to, uint256 amount) returns (bool)",
      "function approve(address spender, uint256 amount) returns (bool)",
      "function allowance(address owner, address spender) view returns (uint256)",
      "function decimals() view returns (uint8)",
      "function symbol() view returns (string)",
      "function name() view returns (string)"
    ];
  }
  
  /**
   * Get provider for specific network
   */
  getProvider(network = 'polygon') {
    return this.providers[network];
  }
  
  /**
   * Get wallet for specific network
   */
  async getWallet(network = 'polygon') {
    if (!this.initialized) {
      await this.initialize();
    }
    return this.wallets[network];
  }
  
  /**
   * Get escrow contract instance
   */
  getEscrowContract(network = 'polygon') {
    const contractAddress = process.env.ESCROW_CONTRACT_ADDRESS;
    const provider = this.getProvider(network);
    return new ethers.Contract(contractAddress, this.escrowABI, provider);
  }
  
  /**
   * Get ERC20 token contract instance
   */
  getTokenContract(tokenAddress, network = 'polygon') {
    return new ethers.Contract(tokenAddress, this.erc20ABI, this.getProvider(network));
  }
  
  /**
   * Get token balance for an address
   */
  async getTokenBalance(tokenAddress, userAddress, network = 'polygon') {
    try {
      const contract = this.getTokenContract(tokenAddress, network);
      const balance = await contract.balanceOf(userAddress);
      const decimals = await contract.decimals();
      
      return {
        balance: balance.toString(),
        decimals,
        formatted: ethers.utils.formatUnits(balance, decimals)
      };
    } catch (error) {
      logger.error(`Error getting token balance: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get native token (ETH/MATIC) balance
   */
  async getNativeBalance(userAddress, network = 'polygon') {
    try {
      const provider = this.getProvider(network);
      const balance = await provider.getBalance(userAddress);
      
      return {
        balance: balance.toString(),
        decimals: 18,
        formatted: ethers.utils.formatEther(balance)
      };
    } catch (error) {
      logger.error(`Error getting native balance: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Create a new trade on the blockchain
   */
  async createTrade(tradeData, network = 'polygon') {
    return await this.executeWithRetry(async () => {
      const escrow = this.getEscrowContract(network);

      // Estimate gas first
      const gasEstimate = await escrow.estimateGas.createTrade(
        tradeData.buyer,
        tradeData.tokenAddress,
        tradeData.amount,
        tradeData.fiatAmount,
        tradeData.fiatCurrency,
        tradeData.paymentMethod,
        tradeData.paymentHash
      );

      // Add 20% buffer to gas estimate
      const gasLimit = gasEstimate.mul(120).div(100);

      const tx = await escrow.createTrade(
        tradeData.buyer,
        tradeData.tokenAddress,
        tradeData.amount,
        tradeData.fiatAmount,
        tradeData.fiatCurrency,
        tradeData.paymentMethod,
        tradeData.paymentHash,
        { gasLimit }
      );

      logger.info(`Trade creation transaction sent: ${tx.hash}`);
      const receipt = await tx.wait();

      // Extract trade ID from event
      const event = receipt.events.find(e => e.event === 'TradeCreated');
      if (!event) {
        throw new Error('TradeCreated event not found in transaction receipt');
      }

      const tradeId = event.args.tradeId.toNumber();

      logger.info(`Trade created successfully: ${tradeId}`, {
        transactionHash: receipt.transactionHash,
        gasUsed: receipt.gasUsed.toString()
      });

      return {
        tradeId,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    });
  }
  
  /**
   * Fund a trade (seller deposits crypto into escrow)
   */
  async fundTrade(tradeId, network = 'polygon') {
    return await this.executeWithRetry(async () => {
      const escrow = this.getEscrowContract(network);

      // Estimate gas first
      const gasEstimate = await escrow.estimateGas.fundTrade(tradeId);
      const gasLimit = gasEstimate.mul(120).div(100);

      const tx = await escrow.fundTrade(tradeId, { gasLimit });

      logger.info(`Trade funding transaction sent: ${tx.hash}`);
      const receipt = await tx.wait();

      logger.info(`Trade funded successfully: ${tradeId}`, {
        transactionHash: receipt.transactionHash,
        gasUsed: receipt.gasUsed.toString()
      });

      return {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    });
  }
  
  /**
   * Confirm payment sent (buyer)
   */
  async confirmPaymentSent(tradeId, network = 'polygon') {
    try {
      const escrow = this.getEscrowContract(network);
      
      const tx = await escrow.confirmPaymentSent(tradeId);
      const receipt = await tx.wait();
      
      return {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    } catch (error) {
      logger.error(`Error confirming payment sent: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Confirm payment received (seller)
   */
  async confirmPaymentReceived(tradeId, network = 'polygon') {
    try {
      const escrow = this.getEscrowContract(network);
      
      const tx = await escrow.confirmPaymentReceived(tradeId);
      const receipt = await tx.wait();
      
      return {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    } catch (error) {
      logger.error(`Error confirming payment received: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Create a dispute
   */
  async createDispute(tradeId, reason, network = 'polygon') {
    try {
      const escrow = this.getEscrowContract(network);
      
      const tx = await escrow.createDispute(tradeId, reason);
      const receipt = await tx.wait();
      
      return {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    } catch (error) {
      logger.error(`Error creating dispute: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get trade details from blockchain
   */
  async getTrade(tradeId, network = 'polygon') {
    try {
      const escrow = this.getEscrowContract(network);
      const trade = await escrow.getTrade(tradeId);
      
      return {
        tradeId: trade.tradeId.toNumber(),
        seller: trade.seller,
        buyer: trade.buyer,
        tokenAddress: trade.tokenAddress,
        amount: trade.amount.toString(),
        fiatAmount: trade.fiatAmount.toNumber(),
        fiatCurrency: trade.fiatCurrency,
        status: trade.status,
        createdAt: trade.createdAt.toNumber(),
        expiresAt: trade.expiresAt.toNumber()
      };
    } catch (error) {
      logger.error(`Error getting trade: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get user reputation from blockchain
   */
  async getUserReputation(userAddress, network = 'polygon') {
    try {
      const escrow = this.getEscrowContract(network);
      const [reputation, completedTrades] = await escrow.getUserReputation(userAddress);
      
      return {
        reputation: reputation.toNumber(),
        completedTrades: completedTrades.toNumber()
      };
    } catch (error) {
      logger.error(`Error getting user reputation: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Estimate gas for a transaction
   */
  async estimateGas(contractMethod, params, network = 'polygon') {
    try {
      const gasEstimate = await contractMethod.estimateGas(...params);
      const gasPrice = await this.getProvider(network).getGasPrice();
      
      return {
        gasLimit: gasEstimate.toString(),
        gasPrice: gasPrice.toString(),
        estimatedCost: gasEstimate.mul(gasPrice).toString()
      };
    } catch (error) {
      logger.error(`Error estimating gas: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Monitor transaction status
   */
  async waitForTransaction(txHash, network = 'polygon', confirmations = 1) {
    try {
      const provider = this.getProvider(network);
      const receipt = await provider.waitForTransaction(txHash, confirmations);
      
      return {
        status: receipt.status === 1 ? 'success' : 'failed',
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        confirmations: receipt.confirmations
      };
    } catch (error) {
      logger.error(`Error waiting for transaction: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new EthereumService();
