/**
 * Transaction Monitoring Service Tests
 * Tests for transaction state persistence and recovery
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { transactionMonitoringService, TransactionState } = require('../services/transactionMonitoring');
const User = require('../models/User');

describe('Transaction Monitoring Service', () => {
  let mongoServer;
  let testUser;

  beforeAll(async () => {
    // Start in-memory MongoDB instance
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Create test user
    testUser = await User.create({
      email: '<EMAIL>',
      username: 'testuser',
      password: 'password123',
      firstName: 'Test',
      lastName: 'User',
      isVerified: true
    });
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean up transaction states before each test
    await TransactionState.deleteMany({});
  });

  describe('Transaction State Management', () => {
    test('should start transaction monitoring', async () => {
      const transactionId = 'test_tx_123';
      const operationData = {
        tradeData: { amount: '100' },
        offerData: { offerId: 'offer_123' }
      };

      const transactionState = await transactionMonitoringService.startTransaction(
        transactionId,
        'trade_creation',
        testUser._id,
        operationData,
        { priority: 'high' }
      );

      expect(transactionState.transactionId).toBe(transactionId);
      expect(transactionState.type).toBe('trade_creation');
      expect(transactionState.status).toBe('pending');
      expect(transactionState.metadata.priority).toBe('high');
    });

    test('should update transaction status', async () => {
      const transactionId = 'test_tx_456';
      
      // Start transaction
      await transactionMonitoringService.startTransaction(
        transactionId,
        'escrow_creation',
        testUser._id,
        { tradeId: 'trade_123' }
      );

      // Mark as in progress
      await transactionMonitoringService.markInProgress(transactionId);
      
      let state = await TransactionState.findOne({ transactionId });
      expect(state.status).toBe('in_progress');

      // Mark as completed
      await transactionMonitoringService.markCompleted(transactionId, { result: 'success' });
      
      state = await TransactionState.findOne({ transactionId });
      expect(state.status).toBe('completed');
      expect(state.completedAt).toBeDefined();
    });

    test('should mark transaction as failed with error details', async () => {
      const transactionId = 'test_tx_789';
      
      await transactionMonitoringService.startTransaction(
        transactionId,
        'balance_update',
        testUser._id,
        { balanceUpdates: [] }
      );

      const error = new Error('Test error');
      error.code = 'TEST_ERROR';

      await transactionMonitoringService.markFailed(transactionId, error);
      
      const state = await TransactionState.findOne({ transactionId });
      expect(state.status).toBe('failed');
      expect(state.errorDetails.message).toBe('Test error');
      expect(state.errorDetails.code).toBe('TEST_ERROR');
    });
  });

  describe('Retry Logic', () => {
    test('should schedule retry for failed transaction', async () => {
      const transactionId = 'test_retry_123';
      
      await transactionMonitoringService.startTransaction(
        transactionId,
        'dispute_creation',
        testUser._id,
        { disputeData: {} },
        { maxRetries: 3 }
      );

      // Schedule retry
      const canRetry = await transactionMonitoringService.scheduleRetry(transactionId, 1000);
      expect(canRetry).toBe(true);

      const state = await TransactionState.findOne({ transactionId });
      expect(state.retryCount).toBe(1);
      expect(state.nextRetryAt).toBeDefined();
      expect(state.status).toBe('pending');
    });

    test('should not retry beyond max attempts', async () => {
      const transactionId = 'test_max_retry_123';
      
      const transactionState = await transactionMonitoringService.startTransaction(
        transactionId,
        'escrow_release',
        testUser._id,
        { releaseData: {} },
        { maxRetries: 2 }
      );

      // Simulate max retries reached
      await TransactionState.findOneAndUpdate(
        { transactionId },
        { retryCount: 2 }
      );

      const canRetry = await transactionMonitoringService.scheduleRetry(transactionId);
      expect(canRetry).toBe(false);

      const state = await TransactionState.findOne({ transactionId });
      expect(state.status).toBe('failed');
    });

    test('should get pending transactions for retry', async () => {
      // Create multiple pending transactions
      await transactionMonitoringService.startTransaction(
        'pending_1',
        'trade_creation',
        testUser._id,
        { data: 'test1' }
      );

      await transactionMonitoringService.startTransaction(
        'pending_2',
        'escrow_creation',
        testUser._id,
        { data: 'test2' }
      );

      // One with future retry time (should not be included)
      await transactionMonitoringService.startTransaction(
        'pending_3',
        'balance_update',
        testUser._id,
        { data: 'test3' }
      );

      await TransactionState.findOneAndUpdate(
        { transactionId: 'pending_3' },
        { nextRetryAt: new Date(Date.now() + 60000) } // 1 minute in future
      );

      const pendingTransactions = await transactionMonitoringService.getPendingTransactions();
      expect(pendingTransactions).toHaveLength(2);
      expect(pendingTransactions.map(t => t.transactionId)).toContain('pending_1');
      expect(pendingTransactions.map(t => t.transactionId)).toContain('pending_2');
    });
  });

  describe('Transaction Recovery', () => {
    test('should recover pending transactions on startup', async () => {
      // Create transactions in different states
      await TransactionState.create({
        transactionId: 'recover_pending',
        type: 'trade_creation',
        status: 'pending',
        userId: testUser._id,
        operationData: { test: 'data' }
      });

      await TransactionState.create({
        transactionId: 'recover_in_progress',
        type: 'escrow_creation',
        status: 'in_progress',
        userId: testUser._id,
        operationData: { test: 'data' }
      });

      await TransactionState.create({
        transactionId: 'recover_completed',
        type: 'balance_update',
        status: 'completed',
        userId: testUser._id,
        operationData: { test: 'data' }
      });

      // Simulate recovery
      await transactionMonitoringService.recoverPendingTransactions();

      // Check that in_progress was reset to pending
      const inProgressState = await TransactionState.findOne({ transactionId: 'recover_in_progress' });
      expect(inProgressState.status).toBe('pending');

      // Check that completed transaction was not modified
      const completedState = await TransactionState.findOne({ transactionId: 'recover_completed' });
      expect(completedState.status).toBe('completed');
    });
  });

  describe('Monitoring Statistics', () => {
    test('should provide monitoring statistics', async () => {
      // Create transactions in different states
      await TransactionState.create({
        transactionId: 'stats_pending',
        type: 'trade_creation',
        status: 'pending',
        userId: testUser._id,
        operationData: {}
      });

      await TransactionState.create({
        transactionId: 'stats_completed',
        type: 'escrow_creation',
        status: 'completed',
        userId: testUser._id,
        operationData: {}
      });

      await TransactionState.create({
        transactionId: 'stats_failed',
        type: 'balance_update',
        status: 'failed',
        userId: testUser._id,
        operationData: {},
        createdAt: new Date() // Recent failure
      });

      const stats = await transactionMonitoringService.getMonitoringStats();
      
      expect(stats.statusDistribution.pending).toBe(1);
      expect(stats.statusDistribution.completed).toBe(1);
      expect(stats.statusDistribution.failed).toBe(1);
      expect(stats.recentFailures).toBe(1);
    });
  });

  describe('Cleanup Operations', () => {
    test('should cleanup old completed transactions', async () => {
      const oldDate = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000); // 8 days ago

      // Create old completed transaction
      await TransactionState.create({
        transactionId: 'old_completed',
        type: 'trade_creation',
        status: 'completed',
        userId: testUser._id,
        operationData: {},
        createdAt: oldDate
      });

      // Create recent completed transaction
      await TransactionState.create({
        transactionId: 'recent_completed',
        type: 'escrow_creation',
        status: 'completed',
        userId: testUser._id,
        operationData: {},
        createdAt: new Date()
      });

      // Create old pending transaction (should not be deleted)
      await TransactionState.create({
        transactionId: 'old_pending',
        type: 'balance_update',
        status: 'pending',
        userId: testUser._id,
        operationData: {},
        createdAt: oldDate
      });

      const deletedCount = await transactionMonitoringService.cleanupOldTransactions(7);
      expect(deletedCount).toBe(1);

      // Verify only old completed transaction was deleted
      const remaining = await TransactionState.find({});
      expect(remaining).toHaveLength(2);
      expect(remaining.map(t => t.transactionId)).toContain('recent_completed');
      expect(remaining.map(t => t.transactionId)).toContain('old_pending');
    });
  });

  describe('Transaction State Queries', () => {
    test('should get transaction state by ID', async () => {
      const transactionId = 'query_test_123';
      
      await transactionMonitoringService.startTransaction(
        transactionId,
        'trade_creation',
        testUser._id,
        { test: 'data' },
        { priority: 'critical' }
      );

      const state = await transactionMonitoringService.getTransactionState(transactionId);
      expect(state.transactionId).toBe(transactionId);
      expect(state.userId.toString()).toBe(testUser._id.toString());
      expect(state.metadata.priority).toBe('critical');
    });

    test('should return null for non-existent transaction', async () => {
      const state = await transactionMonitoringService.getTransactionState('non_existent');
      expect(state).toBeNull();
    });
  });

  describe('Blockchain Transaction Monitoring', () => {
    test('should start blockchain transaction monitoring', async () => {
      const txHash = '0x1234567890abcdef';
      const network = 'polygon';
      const transactionData = {
        hash: txHash,
        type: 'send',
        symbol: 'USDT',
        amount: '100',
        from: '0xfrom',
        to: '0xto',
        network,
        status: 'pending',
        walletId: 'wallet_123'
      };

      const transactionState = await transactionMonitoringService.startBlockchainTransactionMonitoring(
        txHash,
        network,
        testUser._id,
        transactionData,
        { priority: 'high' }
      );

      expect(transactionState.transactionId).toBe(`blockchain_${txHash}`);
      expect(transactionState.type).toBe('blockchain_transaction');
      expect(transactionState.status).toBe('pending');
      expect(transactionState.blockchainData.txHash).toBe(txHash);
      expect(transactionState.blockchainData.network).toBe(network);
      expect(transactionState.metadata.priority).toBe('high');
    });

    test('should update blockchain transaction with confirmation data', async () => {
      const txHash = '0xabcdef1234567890';
      const transactionId = `blockchain_${txHash}`;

      // Start monitoring
      await transactionMonitoringService.startBlockchainTransactionMonitoring(
        txHash,
        'ethereum',
        testUser._id,
        {
          hash: txHash,
          type: 'escrow_fund',
          symbol: 'ETH',
          amount: '1.5',
          from: '0xfrom',
          to: '0xto',
          network: 'ethereum',
          status: 'pending'
        }
      );

      // Update with blockchain confirmation data
      const blockchainData = {
        txHash,
        network: 'ethereum',
        blockNumber: 12345,
        confirmations: 15,
        gasUsed: '21000',
        status: 'confirmed'
      };

      await transactionMonitoringService.updateTransactionStatus(
        transactionId,
        'completed',
        null,
        { blockchainData }
      );

      const state = await TransactionState.findOne({ transactionId });
      expect(state.status).toBe('completed');
      expect(state.blockchainData.blockNumber).toBe(12345);
      expect(state.blockchainData.confirmations).toBe(15);
      expect(state.completedAt).toBeDefined();
    });

    test('should handle blockchain transaction failure', async () => {
      const txHash = '0xfailed123456789';
      const transactionId = `blockchain_${txHash}`;

      await transactionMonitoringService.startBlockchainTransactionMonitoring(
        txHash,
        'bitcoin',
        testUser._id,
        {
          hash: txHash,
          type: 'send',
          symbol: 'BTC',
          amount: '0.1',
          from: 'bc1from',
          to: 'bc1to',
          network: 'bitcoin',
          status: 'pending'
        }
      );

      const error = new Error('Transaction failed on blockchain');
      error.code = 'BLOCKCHAIN_TX_FAILED';

      await transactionMonitoringService.updateTransactionStatus(
        transactionId,
        'failed',
        error
      );

      const state = await TransactionState.findOne({ transactionId });
      expect(state.status).toBe('failed');
      expect(state.errorDetails.message).toBe('Transaction failed on blockchain');
      expect(state.errorDetails.code).toBe('BLOCKCHAIN_TX_FAILED');
    });

    test('should recover blockchain transactions on restart', async () => {
      // Create blockchain transactions in different states
      await TransactionState.create({
        transactionId: 'blockchain_0xpending123',
        type: 'blockchain_transaction',
        status: 'pending',
        userId: testUser._id,
        operationData: { txHash: '0xpending123' },
        blockchainData: {
          txHash: '0xpending123',
          network: 'polygon',
          confirmations: 0
        },
        metadata: { priority: 'high' }
      });

      await TransactionState.create({
        transactionId: 'blockchain_0xinprogress456',
        type: 'blockchain_transaction',
        status: 'in_progress',
        userId: testUser._id,
        operationData: { txHash: '0xinprogress456' },
        blockchainData: {
          txHash: '0xinprogress456',
          network: 'ethereum',
          confirmations: 5
        },
        metadata: { priority: 'critical' }
      });

      // Simulate recovery
      await transactionMonitoringService.recoverPendingTransactions();

      // Check that in_progress blockchain transaction was reset to pending
      const inProgressState = await TransactionState.findOne({
        transactionId: 'blockchain_0xinprogress456'
      });
      expect(inProgressState.status).toBe('pending');

      // Check that high priority transaction gets immediate retry scheduling
      const highPriorityState = await TransactionState.findOne({
        transactionId: 'blockchain_0xpending123'
      });
      expect(highPriorityState.status).toBe('pending');
    });
  });

  describe('Service Lifecycle', () => {
    test('should handle graceful shutdown', async () => {
      // This test ensures the shutdown method doesn't throw errors
      await expect(transactionMonitoringService.shutdown()).resolves.not.toThrow();
    });
  });
});
