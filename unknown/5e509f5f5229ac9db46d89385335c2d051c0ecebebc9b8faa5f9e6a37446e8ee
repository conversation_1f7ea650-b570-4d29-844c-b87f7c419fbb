const Trade = require('../models/Trade');
const User = require('../models/User');
const Message = require('../models/Message');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');

class ChatService {
  constructor() {
    this.activeConnections = new Map(); // userId -> socket
    this.userRooms = new Map(); // userId -> Set of roomIds
    this.roomUsers = new Map(); // roomId -> Set of userIds
    this.typingUsers = new Map(); // roomId -> Set of userIds
  }

  /**
   * Initialize chat service with Socket.IO
   */
  initialize(io) {
    this.io = io;
    
    io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    logger.info('Chat service initialized with Socket.IO');
  }

  /**
   * Handle new socket connection
   */
  handleConnection(socket) {
    logger.info(`New socket connection: ${socket.id}`);

    // Authentication middleware
    socket.on('authenticate', async (data) => {
      try {
        await this.authenticateSocket(socket, data);
      } catch (error) {
        logger.error(`Socket authentication failed: ${error.message}`);
        socket.emit('auth_error', { message: 'Authentication failed' });
        socket.disconnect();
      }
    });

    // Join trade room
    socket.on('join_trade', async (data) => {
      try {
        await this.joinTradeRoom(socket, data);
      } catch (error) {
        logger.error(`Failed to join trade room: ${error.message}`);
        socket.emit('error', { message: 'Failed to join trade room' });
      }
    });

    // Leave trade room
    socket.on('leave_trade', async (data) => {
      try {
        await this.leaveTradeRoom(socket, data);
      } catch (error) {
        logger.error(`Failed to leave trade room: ${error.message}`);
      }
    });

    // Send message
    socket.on('send_message', async (data) => {
      try {
        await this.handleSendMessage(socket, data);
      } catch (error) {
        logger.error(`Failed to send message: ${error.message}`);
        socket.emit('message_error', { message: 'Failed to send message' });
      }
    });

    // Typing indicators
    socket.on('typing_start', (data) => {
      this.handleTypingStart(socket, data);
    });

    socket.on('typing_stop', (data) => {
      this.handleTypingStop(socket, data);
    });

    // Mark messages as read
    socket.on('mark_read', async (data) => {
      try {
        await this.markMessagesAsRead(socket, data);
      } catch (error) {
        logger.error(`Failed to mark messages as read: ${error.message}`);
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      this.handleDisconnect(socket);
    });
  }

  /**
   * Authenticate socket connection
   */
  async authenticateSocket(socket, data) {
    const { userId, token } = data;

    if (!userId || !token) {
      throw new AppError('Missing authentication data', 400);
    }

    // Verify token (simplified - in production use proper JWT verification)
    const user = await User.findById(userId);
    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Store user info in socket
    socket.userId = userId;
    socket.user = user;

    // Store active connection
    this.activeConnections.set(userId, socket);

    // Update user online status
    await User.findByIdAndUpdate(userId, {
      isOnline: true,
      lastSeen: new Date()
    });

    // Join user to their personal room for notifications
    socket.join(`user_${userId}`);

    socket.emit('authenticated', {
      userId,
      username: user.username
    });

    logger.info(`User ${userId} authenticated and connected`);
  }

  /**
   * Join trade room for chat
   */
  async joinTradeRoom(socket, data) {
    const { tradeId } = data;
    const userId = socket.userId;

    if (!userId) {
      throw new AppError('Not authenticated', 401);
    }

    // Verify user is part of this trade
    const trade = await Trade.findById(tradeId).populate('buyer seller');
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    const isParticipant = trade.buyer._id.toString() === userId ||
                         trade.seller._id.toString() === userId;

    if (!isParticipant) {
      throw new AppError('Access denied', 403);
    }

    const roomId = `trade_${tradeId}`;

    // Join socket room
    socket.join(roomId);

    // Track user rooms
    if (!this.userRooms.has(userId)) {
      this.userRooms.set(userId, new Set());
    }
    this.userRooms.get(userId).add(roomId);

    // Track room users
    if (!this.roomUsers.has(roomId)) {
      this.roomUsers.set(roomId, new Set());
    }
    this.roomUsers.get(roomId).add(userId);

    // Get recent messages
    const messages = await this.getTradeMessages(tradeId, 50);

    socket.emit('joined_trade', {
      tradeId,
      roomId,
      messages,
      participants: [
        {
          id: trade.buyer._id,
          username: trade.buyer.username,
          isOnline: this.activeConnections.has(trade.buyer._id.toString())
        },
        {
          id: trade.seller._id,
          username: trade.seller.username,
          isOnline: this.activeConnections.has(trade.seller._id.toString())
        }
      ]
    });

    // Notify other participants
    socket.to(roomId).emit('user_joined', {
      userId,
      username: socket.user.username
    });

    logger.info(`User ${userId} joined trade room ${roomId}`);
  }

  /**
   * Leave trade room
   */
  async leaveTradeRoom(socket, data) {
    const { tradeId } = data;
    const userId = socket.userId;
    const roomId = `trade_${tradeId}`;

    // Leave socket room
    socket.leave(roomId);

    // Update tracking
    if (this.userRooms.has(userId)) {
      this.userRooms.get(userId).delete(roomId);
    }

    if (this.roomUsers.has(roomId)) {
      this.roomUsers.get(roomId).delete(userId);
    }

    // Stop typing if user was typing
    this.handleTypingStop(socket, { tradeId });

    // Notify other participants
    socket.to(roomId).emit('user_left', {
      userId,
      username: socket.user.username
    });

    logger.info(`User ${userId} left trade room ${roomId}`);
  }

  /**
   * Handle sending message
   */
  async handleSendMessage(socket, data) {
    const { tradeId, message, type = 'text', attachmentUrl } = data;
    const userId = socket.userId;
    const roomId = `trade_${tradeId}`;

    if (!message || message.trim().length === 0) {
      throw new AppError('Message cannot be empty', 400);
    }

    // Verify user is in the room
    if (!this.userRooms.get(userId)?.has(roomId)) {
      throw new AppError('Not in trade room', 403);
    }

    // Create message
    const messageDoc = new Message({
      trade: tradeId,
      sender: userId,
      message: message.trim(),
      type,
      attachmentUrl,
      timestamp: new Date()
    });

    await messageDoc.save();
    await messageDoc.populate('sender', 'username firstName lastName');

    // Add to trade messages
    await Trade.findByIdAndUpdate(tradeId, {
      $push: {
        messages: {
          id: messageDoc._id,
          senderId: userId,
          senderUsername: messageDoc.sender.username,
          message: message.trim(),
          type,
          timestamp: new Date(),
          isRead: false,
          attachmentUrl
        }
      }
    });

    const messageData = {
      id: messageDoc._id,
      tradeId,
      senderId: userId,
      senderUsername: messageDoc.sender.username,
      message: message.trim(),
      type,
      timestamp: messageDoc.timestamp,
      attachmentUrl
    };

    // Broadcast to room
    this.io.to(roomId).emit('new_message', messageData);

    // Send push notification to offline users
    await this.sendMessageNotification(tradeId, userId, message);

    logger.info(`Message sent in trade ${tradeId} by user ${userId}`);
  }

  /**
   * Handle typing start
   */
  handleTypingStart(socket, data) {
    const { tradeId } = data;
    const userId = socket.userId;
    const roomId = `trade_${tradeId}`;

    if (!this.typingUsers.has(roomId)) {
      this.typingUsers.set(roomId, new Set());
    }

    this.typingUsers.get(roomId).add(userId);

    // Broadcast to others in room
    socket.to(roomId).emit('user_typing', {
      userId,
      username: socket.user.username,
      isTyping: true
    });

    // Auto-stop typing after 3 seconds
    setTimeout(() => {
      this.handleTypingStop(socket, data);
    }, 3000);
  }

  /**
   * Handle typing stop
   */
  handleTypingStop(socket, data) {
    const { tradeId } = data;
    const userId = socket.userId;
    const roomId = `trade_${tradeId}`;

    if (this.typingUsers.has(roomId)) {
      this.typingUsers.get(roomId).delete(userId);
    }

    // Broadcast to others in room
    socket.to(roomId).emit('user_typing', {
      userId,
      username: socket.user.username,
      isTyping: false
    });
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(socket, data) {
    const { tradeId, messageIds } = data;
    const userId = socket.userId;

    // Update messages in database
    await Message.updateMany(
      {
        _id: { $in: messageIds },
        trade: tradeId,
        sender: { $ne: userId }
      },
      { isRead: true }
    );

    // Update trade messages
    await Trade.updateOne(
      { _id: tradeId },
      {
        $set: {
          'messages.$[elem].isRead': true
        }
      },
      {
        arrayFilters: [
          {
            'elem.id': { $in: messageIds },
            'elem.senderId': { $ne: userId }
          }
        ]
      }
    );

    // Notify sender that messages were read
    const roomId = `trade_${tradeId}`;
    socket.to(roomId).emit('messages_read', {
      messageIds,
      readBy: userId
    });

    logger.info(`Messages marked as read in trade ${tradeId} by user ${userId}`);
  }

  /**
   * Handle socket disconnect
   */
  handleDisconnect(socket) {
    const userId = socket.userId;

    if (userId) {
      // Remove from active connections
      this.activeConnections.delete(userId);

      // Update user offline status
      User.findByIdAndUpdate(userId, {
        isOnline: false,
        lastSeen: new Date()
      }).catch(error => {
        logger.error(`Failed to update user offline status: ${error.message}`);
      });

      // Clean up room tracking
      if (this.userRooms.has(userId)) {
        const userRooms = this.userRooms.get(userId);
        userRooms.forEach(roomId => {
          if (this.roomUsers.has(roomId)) {
            this.roomUsers.get(roomId).delete(userId);
          }
          
          // Notify room that user went offline
          socket.to(roomId).emit('user_offline', {
            userId,
            username: socket.user?.username
          });
        });
        this.userRooms.delete(userId);
      }

      logger.info(`User ${userId} disconnected`);
    }
  }

  /**
   * Get trade messages
   */
  async getTradeMessages(tradeId, limit = 50) {
    try {
      const messages = await Message.find({ trade: tradeId })
        .populate('sender', 'username firstName lastName')
        .sort({ timestamp: -1 })
        .limit(limit);

      return messages.reverse().map(msg => ({
        id: msg._id,
        senderId: msg.sender._id,
        senderUsername: msg.sender.username,
        message: msg.message,
        type: msg.type,
        timestamp: msg.timestamp,
        isRead: msg.isRead,
        attachmentUrl: msg.attachmentUrl
      }));
    } catch (error) {
      logger.error(`Failed to get trade messages: ${error.message}`);
      return [];
    }
  }

  /**
   * Send message notification to offline users
   */
  async sendMessageNotification(tradeId, senderId, message) {
    try {
      const trade = await Trade.findById(tradeId).populate('buyer seller');
      if (!trade) return;

      const recipientId = trade.buyer._id.toString() === senderId 
        ? trade.seller._id.toString() 
        : trade.buyer._id.toString();

      // Check if recipient is online
      if (this.activeConnections.has(recipientId)) {
        return; // User is online, no need for push notification
      }

      // Send push notification (implementation depends on your push service)
      // This would integrate with Firebase Cloud Messaging or similar
      logger.info(`Would send push notification to user ${recipientId} for message in trade ${tradeId}`);
    } catch (error) {
      logger.error(`Failed to send message notification: ${error.message}`);
    }
  }

  /**
   * Broadcast trade update to participants
   */
  async broadcastTradeUpdate(tradeId, updateData) {
    try {
      const roomId = `trade_${tradeId}`;
      this.io.to(roomId).emit('trade_update', {
        tradeId,
        ...updateData
      });

      logger.info(`Trade update broadcasted to room ${roomId}`);
    } catch (error) {
      logger.error(`Failed to broadcast trade update: ${error.message}`);
    }
  }

  /**
   * Send system message to trade
   */
  async sendSystemMessage(tradeId, message, type = 'system') {
    try {
      const messageDoc = new Message({
        trade: tradeId,
        sender: null, // System message
        message,
        type,
        timestamp: new Date()
      });

      await messageDoc.save();

      // Add to trade messages
      await Trade.findByIdAndUpdate(tradeId, {
        $push: {
          messages: {
            id: messageDoc._id,
            senderId: null,
            senderUsername: 'System',
            message,
            type,
            timestamp: new Date(),
            isRead: false
          }
        }
      });

      const messageData = {
        id: messageDoc._id,
        tradeId,
        senderId: null,
        senderUsername: 'System',
        message,
        type,
        timestamp: messageDoc.timestamp
      };

      // Broadcast to room
      const roomId = `trade_${tradeId}`;
      this.io.to(roomId).emit('new_message', messageData);

      logger.info(`System message sent to trade ${tradeId}: ${message}`);
    } catch (error) {
      logger.error(`Failed to send system message: ${error.message}`);
    }
  }

  /**
   * Get online users count
   */
  getOnlineUsersCount() {
    return this.activeConnections.size;
  }

  /**
   * Get room participants
   */
  getRoomParticipants(roomId) {
    return Array.from(this.roomUsers.get(roomId) || []);
  }

  /**
   * Check if user is online
   */
  isUserOnline(userId) {
    return this.activeConnections.has(userId);
  }
}

module.exports = new ChatService();
