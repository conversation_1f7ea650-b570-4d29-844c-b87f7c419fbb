const { ethers } = require('ethers');
const Trade = require('../models/Trade');
const Wallet = require('../models/Wallet');
const ethereumService = require('./blockchain/ethereumService');
const bitcoinService = require('./blockchain/bitcoinService');
const transactionService = require('./transactionService');
const { transactionMonitoringService } = require('./transactionMonitoring');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');
const { businessFlowMonitoringService } = require('./businessFlowMonitoring');

class EscrowService {
  constructor() {
    this.escrowContracts = {
      ethereum: process.env.ETHEREUM_ESCROW_CONTRACT,
      polygon: process.env.POLYGON_ESCROW_CONTRACT,
      localhost: process.env.ESCROW_CONTRACT_ADDRESS // For testing
    };
    
    this.escrowABI = [
      // Main trading functions
      "function createTrade(address _buyer, address _tokenAddress, uint256 _amount, uint256 _fiatAmount, string memory _fiatCurrency, string memory _paymentMethod, bytes32 _paymentHash) external returns (uint256)",
      "function fundTrade(uint256 _tradeId) external",
      "function confirmPaymentSent(uint256 _tradeId) external",
      "function confirmPaymentReceived(uint256 _tradeId) external",
      "function cancelTrade(uint256 _tradeId, string memory _reason) external",
      "function createDispute(uint256 _tradeId, string memory _reason) external",
      "function resolveDispute(uint256 _tradeId, address _winner) external",

      // View functions
      "function getTrade(uint256 _tradeId) external view returns (tuple(uint256 tradeId, address seller, address buyer, address tokenAddress, uint256 amount, uint256 fiatAmount, string fiatCurrency, uint256 commissionRate, uint8 status, uint256 createdAt, uint256 expiresAt, uint256 completedAt, bool sellerConfirmed, bool buyerConfirmed, string paymentMethod, bytes32 paymentHash))",
      "function nextTradeId() external view returns (uint256)",
      "function defaultCommissionRate() external view returns (uint256)",
      "function feeCollector() external view returns (address)",
      "function disputeResolver() external view returns (address)",
      "function authorizedTokens(address) external view returns (bool)",
      "function userReputation(address) external view returns (uint256)",
      "function completedTrades(address) external view returns (uint256)",

      // Admin functions
      "function authorizeToken(address _token, bool _authorized) external",
      "function setCommissionRate(uint256 _rate) external",
      "function emergencyRefund(uint256 _tradeId) external",

      // Events
      "event TradeCreated(uint256 indexed tradeId, address indexed seller, address indexed buyer, uint256 amount, string fiatCurrency)",
      "event TradeFunded(uint256 indexed tradeId, uint256 amount)",
      "event PaymentSent(uint256 indexed tradeId, address indexed buyer)",
      "event TradeCompleted(uint256 indexed tradeId, address indexed buyer, address indexed seller)",
      "event TradeCancelled(uint256 indexed tradeId, string reason)",
      "event DisputeCreated(uint256 indexed tradeId, address indexed initiator, string reason)",
      "event DisputeResolved(uint256 indexed tradeId, address indexed winner)"
    ];

    this.pendingEscrows = new Map();
    this.escrowTimeouts = new Map();
  }

  /**
   * Create escrow for a trade
   */
  async createEscrow(tradeId, buyerUserId, sellerUserId, amount, cryptocurrency, network) {
    try {
      logger.info(`Creating escrow for trade ${tradeId}`, {
        buyerUserId,
        sellerUserId,
        amount,
        cryptocurrency,
        network
      });

      // Get trade details
      const trade = await Trade.findById(tradeId).populate('buyer seller offer');
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      // Get buyer wallet
      const buyerWallet = await Wallet.findOne({ user: buyerUserId });
      if (!buyerWallet) {
        throw new AppError('Buyer wallet not found', 404);
      }

      // Get seller wallet
      const sellerWallet = await Wallet.findOne({ user: sellerUserId });
      if (!sellerWallet) {
        throw new AppError('Seller wallet not found', 404);
      }

      let escrowResult;

      if (network === 'bitcoin') {
        escrowResult = await this.createBitcoinEscrow(trade, buyerWallet, sellerWallet, amount);
      } else {
        escrowResult = await this.createEthereumEscrow(trade, buyerWallet, sellerWallet, amount, cryptocurrency, network);
      }

      // Use atomic transaction to update trade with escrow details
      const { dataConsistencyService } = require('./dataConsistency');

      const escrowData = {
        escrowId: escrowResult.escrowId,
        contractAddress: escrowResult.contractAddress,
        txHash: escrowResult.txHash,
        amount: amount.toString(),
        cryptocurrency,
        network,
        status: 'active',
        createdAt: new Date()
      };

      await dataConsistencyService.executeAtomicEscrowCreation(
        tradeId,
        escrowData,
        trade.seller // Assuming seller creates escrow
      );

      // Start escrow monitoring
      this.monitorEscrow(tradeId, escrowResult.escrowId, network);

      // Record business flow event
      businessFlowMonitoringService.recordBusinessFlowEvent('escrow', 'created', {
        status: 'created',
        tradeId,
        escrowId: escrowResult.escrowId,
        txHash: escrowResult.txHash,
        network,
        amount
      });

      logger.info(`Escrow created successfully for trade ${tradeId}`, {
        escrowId: escrowResult.escrowId,
        txHash: escrowResult.txHash
      });

      return {
        success: true,
        escrowId: escrowResult.escrowId,
        txHash: escrowResult.txHash,
        contractAddress: escrowResult.contractAddress
      };

    } catch (error) {
      // Record business flow event for escrow failure
      businessFlowMonitoringService.recordBusinessFlowEvent('escrow', 'failed', {
        status: 'failed',
        tradeId,
        network,
        amount,
        error: error.message
      });

      logger.error(`Error creating escrow for trade ${tradeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create Bitcoin escrow (multi-signature)
   */
  async createBitcoinEscrow(trade, buyerWallet, sellerWallet, amount) {
    try {
      // For Bitcoin, we'll use a 2-of-3 multisig with platform as arbiter
      const buyerAddress = buyerWallet.addresses.bitcoin.address;
      const sellerAddress = sellerWallet.addresses.bitcoin.address;
      const platformAddress = process.env.BITCOIN_PLATFORM_ADDRESS;

      // Create multisig address (simplified - in production use proper multisig)
      const escrowAddress = await this.createBitcoinMultisig([
        buyerAddress,
        sellerAddress,
        platformAddress
      ]);

      // Send funds to escrow address
      const txResult = await bitcoinService.createTransaction(
        buyerAddress,
        escrowAddress,
        parseInt(amount * 100000000), // Convert to satoshis
        buyerWallet.getPrivateKey('bitcoin') // This would be securely retrieved
      );

      const txHash = await bitcoinService.broadcastTransaction(txResult.txHex);

      // Start persistent monitoring for Bitcoin escrow transaction
      await transactionMonitoringService.startBlockchainTransactionMonitoring(
        txHash,
        'bitcoin',
        trade.buyer,
        {
          hash: txHash,
          type: 'escrow_fund',
          symbol: 'BTC',
          amount: amount.toString(),
          from: buyerAddress,
          to: escrowAddress,
          network: 'bitcoin',
          status: 'pending',
          relatedTrade: trade._id
        },
        {
          priority: 'critical',
          sessionId: `escrow_${trade._id}`,
          clientInfo: 'EscrowService'
        }
      );

      return {
        escrowId: escrowAddress,
        contractAddress: escrowAddress,
        txHash,
        type: 'bitcoin_multisig'
      };

    } catch (error) {
      logger.error(`Error creating Bitcoin escrow: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create Ethereum/Polygon escrow using smart contract
   */
  async createEthereumEscrow(trade, buyerWallet, sellerWallet, amount, cryptocurrency, network) {
    try {
      const contractAddress = this.escrowContracts[network];
      if (!contractAddress) {
        throw new AppError(`Escrow contract not deployed on ${network}`, 500);
      }

      const provider = ethereumService.getProvider(network);
      const buyerSigner = ethereumService.getWallet(network); // This would use buyer's wallet
      
      const escrowContract = new ethers.Contract(contractAddress, this.escrowABI, buyerSigner);

      const buyerAddress = buyerWallet.addresses.ethereum.address;
      const sellerAddress = sellerWallet.addresses.ethereum.address;
      const tradeIdBytes32 = ethers.utils.formatBytes32String(trade._id.toString());

      let tx;
      
      if (cryptocurrency === 'ETH' || cryptocurrency === 'MATIC') {
        // Native token escrow
        const amountWei = ethers.utils.parseEther(amount.toString());
        tx = await escrowContract.createEscrow(
          buyerAddress,
          sellerAddress,
          amountWei,
          tradeIdBytes32,
          { value: amountWei }
        );
      } else {
        // ERC20 token escrow
        const tokenAddress = this.getTokenAddress(cryptocurrency, network);
        const tokenContract = ethereumService.getTokenContract(tokenAddress, network);
        const decimals = await tokenContract.decimals();
        const amountTokens = ethers.utils.parseUnits(amount.toString(), decimals);

        // First approve the escrow contract
        const approveTx = await tokenContract.approve(contractAddress, amountTokens);
        await approveTx.wait();

        // Then create escrow
        tx = await escrowContract.createEscrow(
          buyerAddress,
          sellerAddress,
          amountTokens,
          tradeIdBytes32
        );
      }

      const receipt = await tx.wait();

      // Extract escrow ID from event logs
      const escrowCreatedEvent = receipt.events.find(e => e.event === 'EscrowCreated');
      const escrowId = escrowCreatedEvent.args.escrowId.toString();

      // Start persistent monitoring for Ethereum escrow transaction
      await transactionMonitoringService.startBlockchainTransactionMonitoring(
        receipt.transactionHash,
        network,
        trade.buyer,
        {
          hash: receipt.transactionHash,
          type: 'escrow_fund',
          symbol: cryptocurrency,
          amount: amount.toString(),
          from: buyerAddress,
          to: contractAddress,
          network,
          gasUsed: receipt.gasUsed.toString(),
          status: 'confirmed', // Already confirmed since we waited for receipt
          relatedTrade: trade._id,
          escrowId
        },
        {
          priority: 'critical',
          sessionId: `escrow_${trade._id}`,
          clientInfo: 'EscrowService'
        }
      );

      return {
        escrowId,
        contractAddress,
        txHash: receipt.transactionHash,
        type: 'smart_contract'
      };

    } catch (error) {
      logger.error(`Error creating Ethereum escrow: ${error.message}`);
      throw error;
    }
  }

  /**
   * Release escrow funds to seller
   */
  async releaseEscrow(tradeId, releasedBy) {
    try {
      logger.info(`Releasing escrow for trade ${tradeId}`, { releasedBy });

      const trade = await Trade.findById(tradeId).populate('buyer seller');
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      if (!trade.escrow || trade.escrow.status !== 'active') {
        throw new AppError('No active escrow found for this trade', 400);
      }

      let releaseResult;

      if (trade.escrow.network === 'bitcoin') {
        releaseResult = await this.releaseBitcoinEscrow(trade);
      } else {
        releaseResult = await this.releaseEthereumEscrow(trade);
      }

      // Use atomic transaction to update trade status and escrow release
      const { dataConsistencyService } = require('./dataConsistency');

      const releaseData = {
        releasedAt: new Date(),
        releasedBy: releasedBy,
        releaseTxHash: releaseResult.txHash,
        status: 'released'
      };

      await dataConsistencyService.executeAtomicEscrowRelease(
        tradeId,
        releaseData,
        releasedBy
      );

      // Calculate and process platform commission
      await this.processPlatformCommission(trade);

      // Stop monitoring this escrow
      this.stopEscrowMonitoring(tradeId);

      logger.info(`Escrow released successfully for trade ${tradeId}`, {
        txHash: releaseResult.txHash
      });

      return {
        success: true,
        txHash: releaseResult.txHash
      };

    } catch (error) {
      logger.error(`Error releasing escrow for trade ${tradeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Release Bitcoin escrow
   */
  async releaseBitcoinEscrow(trade) {
    try {
      const sellerWallet = await Wallet.findOne({ user: trade.seller._id });
      const sellerAddress = sellerWallet.addresses.bitcoin.address;
      const escrowAddress = trade.escrow.escrowId;
      const amount = parseFloat(trade.escrow.amount);

      // Create transaction from multisig to seller
      // This is simplified - in production, you'd need proper multisig signing
      const txResult = await bitcoinService.createTransaction(
        escrowAddress,
        sellerAddress,
        parseInt(amount * 100000000), // Convert to satoshis
        'multisig_private_key' // This would be the multisig private key
      );

      const txHash = await bitcoinService.broadcastTransaction(txResult.txHex);

      // Start persistent monitoring for Bitcoin escrow release transaction
      await transactionMonitoringService.startBlockchainTransactionMonitoring(
        txHash,
        'bitcoin',
        trade.seller._id,
        {
          hash: txHash,
          type: 'escrow_release',
          symbol: 'BTC',
          amount: amount.toString(),
          from: escrowAddress,
          to: sellerAddress,
          network: 'bitcoin',
          status: 'pending',
          relatedTrade: trade._id
        },
        {
          priority: 'critical',
          sessionId: `escrow_release_${trade._id}`,
          clientInfo: 'EscrowService'
        }
      );

      return { txHash };

    } catch (error) {
      logger.error(`Error releasing Bitcoin escrow: ${error.message}`);
      throw error;
    }
  }

  /**
   * Release Ethereum escrow
   */
  async releaseEthereumEscrow(trade) {
    try {
      const contractAddress = this.escrowContracts[trade.escrow.network];
      const provider = ethereumService.getProvider(trade.escrow.network);
      const platformSigner = ethereumService.getWallet(trade.escrow.network);
      
      const escrowContract = new ethers.Contract(contractAddress, this.escrowABI, platformSigner);
      
      const tx = await escrowContract.releaseEscrow(trade.escrow.escrowId);
      const receipt = await tx.wait();

      // Start persistent monitoring for Ethereum escrow release transaction
      await transactionMonitoringService.startBlockchainTransactionMonitoring(
        receipt.transactionHash,
        trade.escrow.network,
        trade.seller._id,
        {
          hash: receipt.transactionHash,
          type: 'escrow_release',
          symbol: trade.cryptocurrency.symbol,
          amount: trade.escrow.amount,
          from: contractAddress,
          to: trade.seller.walletAddress,
          network: trade.escrow.network,
          gasUsed: receipt.gasUsed.toString(),
          status: 'confirmed', // Already confirmed since we waited for receipt
          relatedTrade: trade._id,
          escrowId: trade.escrow.escrowId
        },
        {
          priority: 'critical',
          sessionId: `escrow_release_${trade._id}`,
          clientInfo: 'EscrowService'
        }
      );

      return { txHash: receipt.transactionHash };

    } catch (error) {
      logger.error(`Error releasing Ethereum escrow: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cancel escrow and return funds to buyer
   */
  async cancelEscrow(tradeId, cancelledBy, reason) {
    try {
      logger.info(`Cancelling escrow for trade ${tradeId}`, { cancelledBy, reason });

      const trade = await Trade.findById(tradeId).populate('buyer seller');
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      if (!trade.escrow || trade.escrow.status !== 'active') {
        throw new AppError('No active escrow found for this trade', 400);
      }

      let cancelResult;

      if (trade.escrow.network === 'bitcoin') {
        cancelResult = await this.cancelBitcoinEscrow(trade);
      } else {
        cancelResult = await this.cancelEthereumEscrow(trade);
      }

      // Update trade status
      trade.escrow.status = 'cancelled';
      trade.escrow.cancelledAt = new Date();
      trade.escrow.cancelledBy = cancelledBy;
      trade.escrow.cancelReason = reason;
      trade.escrow.cancelTxHash = cancelResult.txHash;
      trade.status = 'cancelled';
      trade.cancelledAt = new Date();

      await trade.save();

      // Stop monitoring this escrow
      this.stopEscrowMonitoring(tradeId);

      logger.info(`Escrow cancelled successfully for trade ${tradeId}`, {
        txHash: cancelResult.txHash
      });

      return {
        success: true,
        txHash: cancelResult.txHash
      };

    } catch (error) {
      logger.error(`Error cancelling escrow for trade ${tradeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cancel Ethereum escrow
   */
  async cancelEthereumEscrow(trade) {
    try {
      const contractAddress = this.escrowContracts[trade.escrow.network];
      const provider = ethereumService.getProvider(trade.escrow.network);
      const platformSigner = ethereumService.getWallet(trade.escrow.network);
      
      const escrowContract = new ethers.Contract(contractAddress, this.escrowABI, platformSigner);
      
      const tx = await escrowContract.cancelEscrow(trade.escrow.escrowId);
      const receipt = await tx.wait();

      return { txHash: receipt.transactionHash };

    } catch (error) {
      logger.error(`Error cancelling Ethereum escrow: ${error.message}`);
      throw error;
    }
  }

  /**
   * Process platform commission
   */
  async processPlatformCommission(trade) {
    try {
      const commissionRate = 0.01; // 1% commission
      const tradeAmount = parseFloat(trade.escrow.amount);
      const commissionAmount = tradeAmount * commissionRate;

      // Record commission transaction
      const commissionRecord = {
        tradeId: trade._id,
        amount: commissionAmount.toString(),
        cryptocurrency: trade.escrow.cryptocurrency,
        network: trade.escrow.network,
        rate: commissionRate,
        processedAt: new Date()
      };

      // Add commission to platform wallet (simplified)
      logger.info(`Platform commission processed: ${commissionAmount} ${trade.escrow.cryptocurrency}`, {
        tradeId: trade._id,
        commission: commissionRecord
      });

      return commissionRecord;

    } catch (error) {
      logger.error(`Error processing platform commission: ${error.message}`);
      throw error;
    }
  }

  /**
   * Monitor escrow status
   */
  monitorEscrow(tradeId, escrowId, network) {
    try {
      this.pendingEscrows.set(tradeId, {
        escrowId,
        network,
        startTime: Date.now()
      });

      // Set timeout for automatic dispute (24 hours)
      const timeoutId = setTimeout(() => {
        this.handleEscrowTimeout(tradeId);
      }, 24 * 60 * 60 * 1000); // 24 hours

      this.escrowTimeouts.set(tradeId, timeoutId);

      logger.info(`Started monitoring escrow for trade ${tradeId}`, {
        escrowId,
        network
      });

    } catch (error) {
      logger.error(`Error starting escrow monitoring: ${error.message}`);
    }
  }

  /**
   * Stop escrow monitoring
   */
  stopEscrowMonitoring(tradeId) {
    try {
      this.pendingEscrows.delete(tradeId);
      
      const timeoutId = this.escrowTimeouts.get(tradeId);
      if (timeoutId) {
        clearTimeout(timeoutId);
        this.escrowTimeouts.delete(tradeId);
      }

      logger.info(`Stopped monitoring escrow for trade ${tradeId}`);

    } catch (error) {
      logger.error(`Error stopping escrow monitoring: ${error.message}`);
    }
  }

  /**
   * Handle escrow timeout
   */
  async handleEscrowTimeout(tradeId) {
    try {
      logger.warn(`Escrow timeout for trade ${tradeId} - initiating dispute`);

      const trade = await Trade.findById(tradeId);
      if (trade && trade.escrow && trade.escrow.status === 'active') {
        // Automatically initiate dispute
        await this.initiateDispute(tradeId, 'system', 'Automatic dispute due to timeout');
      }

    } catch (error) {
      logger.error(`Error handling escrow timeout: ${error.message}`);
    }
  }

  /**
   * Initiate dispute
   */
  async initiateDispute(tradeId, initiatedBy, reason) {
    try {
      logger.info(`Initiating dispute for trade ${tradeId}`, { initiatedBy, reason });

      const trade = await Trade.findById(tradeId);
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      // Update trade with dispute information
      trade.dispute = {
        initiatedBy,
        reason,
        status: 'open',
        initiatedAt: new Date()
      };

      trade.status = 'disputed';
      await trade.save();

      // Notify admin for manual resolution
      // This would trigger admin notifications

      logger.info(`Dispute initiated for trade ${tradeId}`);

      return {
        success: true,
        disputeId: trade._id
      };

    } catch (error) {
      logger.error(`Error initiating dispute: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get token contract address
   */
  getTokenAddress(symbol, network) {
    const tokenAddresses = {
      polygon: {
        USDT: process.env.POLYGON_USDT_ADDRESS || '******************************************',
        USDC: process.env.POLYGON_USDC_ADDRESS || '******************************************',
        DAI: process.env.POLYGON_DAI_ADDRESS || '******************************************'
      },
      ethereum: {
        USDT: process.env.ETHEREUM_USDT_ADDRESS || '******************************************',
        USDC: process.env.ETHEREUM_USDC_ADDRESS || '******************************************',
        DAI: process.env.ETHEREUM_DAI_ADDRESS || '******************************************'
      }
    };

    return tokenAddresses[network]?.[symbol];
  }

  /**
   * Create Bitcoin multisig address (simplified)
   */
  async createBitcoinMultisig(addresses) {
    // This is a simplified implementation
    // In production, you'd use proper Bitcoin multisig creation
    const multisigAddress = `2N${Math.random().toString(36).substring(2, 15)}`;
    return multisigAddress;
  }

  /**
   * Get escrow statistics
   */
  async getEscrowStats() {
    try {
      const activeEscrows = this.pendingEscrows.size;
      const totalEscrows = await Trade.countDocuments({ 'escrow.status': { $exists: true } });
      const completedEscrows = await Trade.countDocuments({ 'escrow.status': 'released' });
      const disputedEscrows = await Trade.countDocuments({ 'escrow.status': 'disputed' });

      return {
        active: activeEscrows,
        total: totalEscrows,
        completed: completedEscrows,
        disputed: disputedEscrows,
        successRate: totalEscrows > 0 ? (completedEscrows / totalEscrows) * 100 : 0
      };

    } catch (error) {
      logger.error(`Error getting escrow stats: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new EscrowService();
