"use strict";(self.webpackChunkkryptopesa_admin_dashboard=self.webpackChunkkryptopesa_admin_dashboard||[]).push([[212],{2143:(e,t,r)=>{r.d(t,{A:()=>I});var n=r(8587),a=r(8168),o=r(5043),s=r(8387),i=r(8610),l=r(7266),c=r(4535),d=r(1475),u=r(8206),m=r(1347),h=r(6236),p=r(5013),A=r(5849),v=r(5658),x=r(1424),g=r(909),b=r(2532),f=r(2372);function y(e){return(0,f.Ay)("MuiMenuItem",e)}const j=(0,b.A)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var S=r(579);const C=["autoFocus","component","dense","divider","disableGutters","focusVisibleClassName","role","tabIndex","className"],w=(0,c.Ay)(h.A,{shouldForwardProp:e=>(0,d.A)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})(e=>{let{theme:t,ownerState:r}=e;return(0,a.A)({},t.typography.body1,{display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap"},!r.disableGutters&&{paddingLeft:16,paddingRight:16},r.divider&&{borderBottom:"1px solid ".concat((t.vars||t).palette.divider),backgroundClip:"padding-box"},{"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(j.selected)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,l.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),["&.".concat(j.focusVisible)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.focusOpacity,"))"):(0,l.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},["&.".concat(j.selected,":hover")]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.hoverOpacity,"))"):(0,l.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,l.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}},["&.".concat(j.focusVisible)]:{backgroundColor:(t.vars||t).palette.action.focus},["&.".concat(j.disabled)]:{opacity:(t.vars||t).palette.action.disabledOpacity},["& + .".concat(v.A.root)]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},["& + .".concat(v.A.inset)]:{marginLeft:52},["& .".concat(g.A.root)]:{marginTop:0,marginBottom:0},["& .".concat(g.A.inset)]:{paddingLeft:36},["& .".concat(x.A.root)]:{minWidth:36}},!r.dense&&{[t.breakpoints.up("sm")]:{minHeight:"auto"}},r.dense&&(0,a.A)({minHeight:32,paddingTop:4,paddingBottom:4},t.typography.body2,{["& .".concat(x.A.root," svg")]:{fontSize:"1.25rem"}}))}),I=o.forwardRef(function(e,t){const r=(0,u.b)({props:e,name:"MuiMenuItem"}),{autoFocus:l=!1,component:c="li",dense:d=!1,divider:h=!1,disableGutters:v=!1,focusVisibleClassName:x,role:g="menuitem",tabIndex:b,className:f}=r,j=(0,n.A)(r,C),I=o.useContext(m.A),M=o.useMemo(()=>({dense:d||I.dense||!1,disableGutters:v}),[I.dense,d,v]),z=o.useRef(null);(0,p.A)(()=>{l&&z.current&&z.current.focus()},[l]);const k=(0,a.A)({},r,{dense:M.dense,divider:h,disableGutters:v}),L=(e=>{const{disabled:t,dense:r,divider:n,disableGutters:o,selected:s,classes:l}=e,c={root:["root",r&&"dense",t&&"disabled",!o&&"gutters",n&&"divider",s&&"selected"]},d=(0,i.A)(c,y,l);return(0,a.A)({},l,d)})(r),B=(0,A.A)(z,t);let P;return r.disabled||(P=void 0!==b?b:-1),(0,S.jsx)(m.A.Provider,{value:M,children:(0,S.jsx)(w,(0,a.A)({ref:B,role:g,tabIndex:P,component:c,focusVisibleClassName:(0,s.A)(L.focusVisible,x),className:(0,s.A)(L.root,f)},j,{ownerState:k,classes:L}))})})},2796:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(9662),a=r(579);const o=(0,n.A)((0,a.jsx)("path",{d:"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"}),"TrendingUp")},4162:(e,t,r)=>{r.d(t,{A:()=>m});var n=r(8168),a=r(8587),o=r(3462),s=r(5006),i=r(6004),l=r(9523);const c=["className","elementType","ownerState","externalForwardedProps","getSlotOwnerState","internalForwardedProps"],d=["component","slots","slotProps"],u=["component"];function m(e,t){const{className:r,elementType:m,ownerState:h,externalForwardedProps:p,getSlotOwnerState:A,internalForwardedProps:v}=t,x=(0,a.A)(t,c),{component:g,slots:b={[e]:void 0},slotProps:f={[e]:void 0}}=p,y=(0,a.A)(p,d),j=b[e]||m,S=(0,i.A)(f[e],h),C=(0,l.A)((0,n.A)({className:r},x,{externalForwardedProps:"root"===e?y:void 0,externalSlotProps:S})),{props:{component:w},internalRef:I}=C,M=(0,a.A)(C.props,u),z=(0,o.A)(I,null==S?void 0:S.ref,t.ref),k=A?A(M):{},L=(0,n.A)({},h,k),B="root"===e?w||g:w,P=(0,s.A)(j,(0,n.A)({},"root"===e&&!g&&!b[e]&&v,"root"!==e&&!b[e]&&v,M,B&&{as:B},{ref:z}),L);return Object.keys(k).forEach(e=>{delete P[e]}),[j,P]}},4194:(e,t,r)=>{r.d(t,{A:()=>P});var n=r(8587),a=r(8168),o=r(5043),s=r(8387),i=r(8610),l=r(7266),c=r(4535),d=r(8206),u=r(4162),m=r(6803),h=r(3336),p=r(2532),A=r(2372);function v(e){return(0,A.Ay)("MuiAlert",e)}const x=(0,p.A)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var g=r(7392),b=r(9662),f=r(579);const y=(0,b.A)((0,f.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),j=(0,b.A)((0,f.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),S=(0,b.A)((0,f.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),C=(0,b.A)((0,f.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),w=(0,b.A)((0,f.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close"),I=["action","children","className","closeText","color","components","componentsProps","icon","iconMapping","onClose","role","severity","slotProps","slots","variant"],M=(0,c.Ay)(h.A,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],t["".concat(r.variant).concat((0,m.A)(r.color||r.severity))]]}})(e=>{let{theme:t}=e;const r="light"===t.palette.mode?l.e$:l.a,n="light"===t.palette.mode?l.a:l.e$;return(0,a.A)({},t.typography.body2,{backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(t.palette).filter(e=>{let[,t]=e;return t.main&&t.light}).map(e=>{let[a]=e;return{props:{colorSeverity:a,variant:"standard"},style:{color:t.vars?t.vars.palette.Alert["".concat(a,"Color")]:r(t.palette[a].light,.6),backgroundColor:t.vars?t.vars.palette.Alert["".concat(a,"StandardBg")]:n(t.palette[a].light,.9),["& .".concat(x.icon)]:t.vars?{color:t.vars.palette.Alert["".concat(a,"IconColor")]}:{color:t.palette[a].main}}}}),...Object.entries(t.palette).filter(e=>{let[,t]=e;return t.main&&t.light}).map(e=>{let[n]=e;return{props:{colorSeverity:n,variant:"outlined"},style:{color:t.vars?t.vars.palette.Alert["".concat(n,"Color")]:r(t.palette[n].light,.6),border:"1px solid ".concat((t.vars||t).palette[n].light),["& .".concat(x.icon)]:t.vars?{color:t.vars.palette.Alert["".concat(n,"IconColor")]}:{color:t.palette[n].main}}}}),...Object.entries(t.palette).filter(e=>{let[,t]=e;return t.main&&t.dark}).map(e=>{let[r]=e;return{props:{colorSeverity:r,variant:"filled"},style:(0,a.A)({fontWeight:t.typography.fontWeightMedium},t.vars?{color:t.vars.palette.Alert["".concat(r,"FilledColor")],backgroundColor:t.vars.palette.Alert["".concat(r,"FilledBg")]}:{backgroundColor:"dark"===t.palette.mode?t.palette[r].dark:t.palette[r].main,color:t.palette.getContrastText(t.palette[r].main)})}})]})}),z=(0,c.Ay)("div",{name:"MuiAlert",slot:"Icon",overridesResolver:(e,t)=>t.icon})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),k=(0,c.Ay)("div",{name:"MuiAlert",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0",minWidth:0,overflow:"auto"}),L=(0,c.Ay)("div",{name:"MuiAlert",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),B={success:(0,f.jsx)(y,{fontSize:"inherit"}),warning:(0,f.jsx)(j,{fontSize:"inherit"}),error:(0,f.jsx)(S,{fontSize:"inherit"}),info:(0,f.jsx)(C,{fontSize:"inherit"})},P=o.forwardRef(function(e,t){const r=(0,d.b)({props:e,name:"MuiAlert"}),{action:o,children:l,className:c,closeText:h="Close",color:p,components:A={},componentsProps:x={},icon:b,iconMapping:y=B,onClose:j,role:S="alert",severity:C="success",slotProps:P={},slots:D={},variant:R="standard"}=r,T=(0,n.A)(r,I),O=(0,a.A)({},r,{color:p,severity:C,variant:R,colorSeverity:p||C}),N=(e=>{const{variant:t,color:r,severity:n,classes:a}=e,o={root:["root","color".concat((0,m.A)(r||n)),"".concat(t).concat((0,m.A)(r||n)),"".concat(t)],icon:["icon"],message:["message"],action:["action"]};return(0,i.A)(o,v,a)})(O),H={slots:(0,a.A)({closeButton:A.CloseButton,closeIcon:A.CloseIcon},D),slotProps:(0,a.A)({},x,P)},[F,V]=(0,u.A)("closeButton",{elementType:g.A,externalForwardedProps:H,ownerState:O}),[E,W]=(0,u.A)("closeIcon",{elementType:w,externalForwardedProps:H,ownerState:O});return(0,f.jsxs)(M,(0,a.A)({role:S,elevation:0,ownerState:O,className:(0,s.A)(N.root,c),ref:t},T,{children:[!1!==b?(0,f.jsx)(z,{ownerState:O,className:N.icon,children:b||y[C]||B[C]}):null,(0,f.jsx)(k,{ownerState:O,className:N.message,children:l}),null!=o?(0,f.jsx)(L,{ownerState:O,className:N.action,children:o}):null,null==o&&j?(0,f.jsx)(L,{ownerState:O,className:N.action,children:(0,f.jsx)(F,(0,a.A)({size:"small","aria-label":h,title:h,color:"inherit",onClick:j},V,{children:(0,f.jsx)(E,(0,a.A)({fontSize:"small"},W))}))}):null]}))})},4389:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(9662),a=r(579);const o=(0,n.A)((0,a.jsx)("path",{d:"m16 18 2.29-2.29-4.88-4.88-4 4L2 7.41 3.41 6l6 6 4-4 6.3 6.29L22 12v6z"}),"TrendingDown")},4642:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(9662),a=r(579);const o=(0,n.A)((0,a.jsx)("path",{d:"M1 21h22L12 2zm12-3h-2v-2h2zm0-4h-2v-4h2z"}),"Warning")},9212:(e,t,r)=>{r.r(t),r.d(t,{default:()=>me});var n=r(5043),a=r(2110),o=r(6494),s=r(6446),i=r(5865),l=r(7528),c=r(8587),d=r(8168),u=r(8387),m=r(8610),h=r(3290),p=r(7266),A=r(875),v=r(6803),x=r(4535),g=r(8206),b=r(2532),f=r(2372);function y(e){return(0,f.Ay)("MuiLinearProgress",e)}(0,b.A)("MuiLinearProgress",["root","colorPrimary","colorSecondary","determinate","indeterminate","buffer","query","dashed","dashedColorPrimary","dashedColorSecondary","bar","barColorPrimary","barColorSecondary","bar1Indeterminate","bar1Determinate","bar1Buffer","bar2Indeterminate","bar2Buffer"]);var j,S,C,w,I,M,z=r(579);const k=["className","color","value","valueBuffer","variant"];let L,B,P,D,R,T;const O=(0,h.i7)(L||(L=j||(j=(0,l.A)(["\n  0% {\n    left: -35%;\n    right: 100%;\n  }\n\n  60% {\n    left: 100%;\n    right: -90%;\n  }\n\n  100% {\n    left: 100%;\n    right: -90%;\n  }\n"])))),N=(0,h.i7)(B||(B=S||(S=(0,l.A)(["\n  0% {\n    left: -200%;\n    right: 100%;\n  }\n\n  60% {\n    left: 107%;\n    right: -8%;\n  }\n\n  100% {\n    left: 107%;\n    right: -8%;\n  }\n"])))),H=(0,h.i7)(P||(P=C||(C=(0,l.A)(["\n  0% {\n    opacity: 1;\n    background-position: 0 -23px;\n  }\n\n  60% {\n    opacity: 0;\n    background-position: 0 -23px;\n  }\n\n  100% {\n    opacity: 1;\n    background-position: -200px -23px;\n  }\n"])))),F=(e,t)=>"inherit"===t?"currentColor":e.vars?e.vars.palette.LinearProgress["".concat(t,"Bg")]:"light"===e.palette.mode?(0,p.a)(e.palette[t].main,.62):(0,p.e$)(e.palette[t].main,.5),V=(0,x.Ay)("span",{name:"MuiLinearProgress",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t["color".concat((0,v.A)(r.color))],t[r.variant]]}})(e=>{let{ownerState:t,theme:r}=e;return(0,d.A)({position:"relative",overflow:"hidden",display:"block",height:4,zIndex:0,"@media print":{colorAdjust:"exact"},backgroundColor:F(r,t.color)},"inherit"===t.color&&"buffer"!==t.variant&&{backgroundColor:"none","&::before":{content:'""',position:"absolute",left:0,top:0,right:0,bottom:0,backgroundColor:"currentColor",opacity:.3}},"buffer"===t.variant&&{backgroundColor:"transparent"},"query"===t.variant&&{transform:"rotate(180deg)"})}),E=(0,x.Ay)("span",{name:"MuiLinearProgress",slot:"Dashed",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.dashed,t["dashedColor".concat((0,v.A)(r.color))]]}})(e=>{let{ownerState:t,theme:r}=e;const n=F(r,t.color);return(0,d.A)({position:"absolute",marginTop:0,height:"100%",width:"100%"},"inherit"===t.color&&{opacity:.3},{backgroundImage:"radial-gradient(".concat(n," 0%, ").concat(n," 16%, transparent 42%)"),backgroundSize:"10px 10px",backgroundPosition:"0 -23px"})},(0,h.AH)(D||(D=w||(w=(0,l.A)(["\n    animation: "," 3s infinite linear;\n  "]))),H)),W=(0,x.Ay)("span",{name:"MuiLinearProgress",slot:"Bar1",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.bar,t["barColor".concat((0,v.A)(r.color))],("indeterminate"===r.variant||"query"===r.variant)&&t.bar1Indeterminate,"determinate"===r.variant&&t.bar1Determinate,"buffer"===r.variant&&t.bar1Buffer]}})(e=>{let{ownerState:t,theme:r}=e;return(0,d.A)({width:"100%",position:"absolute",left:0,bottom:0,top:0,transition:"transform 0.2s linear",transformOrigin:"left",backgroundColor:"inherit"===t.color?"currentColor":(r.vars||r).palette[t.color].main},"determinate"===t.variant&&{transition:"transform .".concat(4,"s linear")},"buffer"===t.variant&&{zIndex:1,transition:"transform .".concat(4,"s linear")})},e=>{let{ownerState:t}=e;return("indeterminate"===t.variant||"query"===t.variant)&&(0,h.AH)(R||(R=I||(I=(0,l.A)(["\n      width: auto;\n      animation: "," 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\n    "]))),O)}),G=(0,x.Ay)("span",{name:"MuiLinearProgress",slot:"Bar2",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.bar,t["barColor".concat((0,v.A)(r.color))],("indeterminate"===r.variant||"query"===r.variant)&&t.bar2Indeterminate,"buffer"===r.variant&&t.bar2Buffer]}})(e=>{let{ownerState:t,theme:r}=e;return(0,d.A)({width:"100%",position:"absolute",left:0,bottom:0,top:0,transition:"transform 0.2s linear",transformOrigin:"left"},"buffer"!==t.variant&&{backgroundColor:"inherit"===t.color?"currentColor":(r.vars||r).palette[t.color].main},"inherit"===t.color&&{opacity:.3},"buffer"===t.variant&&{backgroundColor:F(r,t.color),transition:"transform .".concat(4,"s linear")})},e=>{let{ownerState:t}=e;return("indeterminate"===t.variant||"query"===t.variant)&&(0,h.AH)(T||(T=M||(M=(0,l.A)(["\n      width: auto;\n      animation: "," 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;\n    "]))),N)}),q=n.forwardRef(function(e,t){const r=(0,g.b)({props:e,name:"MuiLinearProgress"}),{className:n,color:a="primary",value:o,valueBuffer:s,variant:i="indeterminate"}=r,l=(0,c.A)(r,k),h=(0,d.A)({},r,{color:a,variant:i}),p=(e=>{const{classes:t,variant:r,color:n}=e,a={root:["root","color".concat((0,v.A)(n)),r],dashed:["dashed","dashedColor".concat((0,v.A)(n))],bar1:["bar","barColor".concat((0,v.A)(n)),("indeterminate"===r||"query"===r)&&"bar1Indeterminate","determinate"===r&&"bar1Determinate","buffer"===r&&"bar1Buffer"],bar2:["bar","buffer"!==r&&"barColor".concat((0,v.A)(n)),"buffer"===r&&"color".concat((0,v.A)(n)),("indeterminate"===r||"query"===r)&&"bar2Indeterminate","buffer"===r&&"bar2Buffer"]};return(0,m.A)(a,y,t)})(h),x=(0,A.I)(),b={},f={bar1:{},bar2:{}};if("determinate"===i||"buffer"===i)if(void 0!==o){b["aria-valuenow"]=Math.round(o),b["aria-valuemin"]=0,b["aria-valuemax"]=100;let e=o-100;x&&(e=-e),f.bar1.transform="translateX(".concat(e,"%)")}else 0;if("buffer"===i)if(void 0!==s){let e=(s||0)-100;x&&(e=-e),f.bar2.transform="translateX(".concat(e,"%)")}else 0;return(0,z.jsxs)(V,(0,d.A)({className:(0,u.A)(p.root,n),ownerState:h,role:"progressbar"},b,{ref:t},l,{children:["buffer"===i?(0,z.jsx)(E,{className:p.dashed,ownerState:h}):null,(0,z.jsx)(W,{className:p.bar1,ownerState:h,style:f.bar1}),"determinate"===i?null:(0,z.jsx)(G,{className:p.bar2,ownerState:h,style:f.bar2})]}))});var U=r(4194),X=r(3193),_=r(9190),$=r(648),Z=r(2143),K=r(8903),J=r(3336),Q=r(9650),Y=r(1806),ee=r(4882),te=r(8076),re=r(39),ne=r(3460),ae=r(3845),oe=r(2796),se=r(4389),ie=r(5155),le=r(1973);const ce=(0,r(9662).A)((0,z.jsx)("path",{d:"M4 10h3v7H4zm6.5 0h3v7h-3zM2 19h20v3H2zm15-9h3v7h-3zm-5-9L2 6v2h20V6z"}),"AccountBalance");var de=r(4642),ue=r(9722);const me=()=>{var e,t,r,l,c,d,u,m,h,p,A,v,x;const[g,b]=(0,n.useState)(null),[f,y]=(0,n.useState)(!0),[j,S]=(0,n.useState)(null),[C,w]=(0,n.useState)("7d");(0,n.useEffect)(()=>{(async()=>{try{y(!0);const e=await ue.A.get("/admin/dashboard/stats");e.data.success&&b(e.data.data)}catch(j){console.error("Error fetching analytics:",j),S("Failed to fetch analytics data")}finally{y(!1)}})()},[C]);const I=e=>{let{title:t,value:r,change:n,icon:l,color:c="primary"}=e;return(0,z.jsx)(a.A,{children:(0,z.jsx)(o.A,{children:(0,z.jsxs)(s.A,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[(0,z.jsxs)(s.A,{children:[(0,z.jsx)(i.A,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:t}),(0,z.jsx)(i.A,{variant:"h4",component:"div",children:r}),n&&(0,z.jsxs)(s.A,{display:"flex",alignItems:"center",mt:1,children:[n>0?(0,z.jsx)(oe.A,{color:"success",fontSize:"small"}):(0,z.jsx)(se.A,{color:"error",fontSize:"small"}),(0,z.jsxs)(i.A,{variant:"body2",color:n>0?"success.main":"error.main",sx:{ml:.5},children:[Math.abs(n),"%"]})]})]}),(0,z.jsx)(s.A,{color:"".concat(c,".main"),children:l})]})})})};return f?(0,z.jsxs)(s.A,{children:[(0,z.jsx)(i.A,{variant:"h4",gutterBottom:!0,children:"Analytics Dashboard"}),(0,z.jsx)(q,{})]}):j?(0,z.jsxs)(s.A,{children:[(0,z.jsx)(i.A,{variant:"h4",gutterBottom:!0,children:"Analytics Dashboard"}),(0,z.jsx)(U.A,{severity:"error",children:j})]}):(0,z.jsxs)(s.A,{children:[(0,z.jsxs)(s.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[(0,z.jsx)(i.A,{variant:"h4",gutterBottom:!0,children:"Analytics Dashboard"}),(0,z.jsxs)(X.A,{sx:{minWidth:120},children:[(0,z.jsx)(_.A,{children:"Time Range"}),(0,z.jsxs)($.A,{value:C,label:"Time Range",onChange:e=>w(e.target.value),children:[(0,z.jsx)(Z.A,{value:"24h",children:"Last 24 Hours"}),(0,z.jsx)(Z.A,{value:"7d",children:"Last 7 Days"}),(0,z.jsx)(Z.A,{value:"30d",children:"Last 30 Days"}),(0,z.jsx)(Z.A,{value:"90d",children:"Last 90 Days"})]})]})]}),(0,z.jsxs)(K.Ay,{container:!0,spacing:3,mb:4,children:[(0,z.jsx)(K.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,z.jsx)(I,{title:"Total Users",value:(null===g||void 0===g||null===(e=g.statistics)||void 0===e||null===(t=e.users)||void 0===t||null===(r=t.total)||void 0===r?void 0:r.toLocaleString())||"0",change:5.2,icon:(0,z.jsx)(ie.A,{fontSize:"large"}),color:"primary"})}),(0,z.jsx)(K.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,z.jsx)(I,{title:"Active Trades",value:(null===g||void 0===g||null===(l=g.statistics)||void 0===l||null===(c=l.trades)||void 0===c?void 0:c.active)||"0",change:-2.1,icon:(0,z.jsx)(le.A,{fontSize:"large"}),color:"info"})}),(0,z.jsx)(K.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,z.jsx)(I,{title:"Total Volume (USD)",value:"$".concat(((null===g||void 0===g||null===(d=g.statistics)||void 0===d||null===(u=d.trades)||void 0===u?void 0:u.volume)||0).toLocaleString()),change:12.5,icon:(0,z.jsx)(ce,{fontSize:"large"}),color:"success"})}),(0,z.jsx)(K.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,z.jsx)(I,{title:"Pending Disputes",value:(null===g||void 0===g||null===(m=g.statistics)||void 0===m||null===(h=m.disputes)||void 0===h?void 0:h.pending)||"0",change:0,icon:(0,z.jsx)(de.A,{fontSize:"large"}),color:"warning"})})]}),(0,z.jsxs)(K.Ay,{container:!0,spacing:3,mb:4,children:[(0,z.jsx)(K.Ay,{item:!0,xs:12,md:6,children:(0,z.jsxs)(J.A,{sx:{p:3},children:[(0,z.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"User Growth"}),(0,z.jsx)(s.A,{height:300,display:"flex",alignItems:"center",justifyContent:"center",children:(0,z.jsx)(i.A,{color:"textSecondary",children:"Chart visualization would be implemented here using a library like Chart.js or Recharts"})})]})}),(0,z.jsx)(K.Ay,{item:!0,xs:12,md:6,children:(0,z.jsxs)(J.A,{sx:{p:3},children:[(0,z.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Trading Volume"}),(0,z.jsx)(s.A,{height:300,display:"flex",alignItems:"center",justifyContent:"center",children:(0,z.jsx)(i.A,{color:"textSecondary",children:"Chart visualization would be implemented here using a library like Chart.js or Recharts"})})]})})]}),(0,z.jsxs)(K.Ay,{container:!0,spacing:3,children:[(0,z.jsx)(K.Ay,{item:!0,xs:12,md:6,children:(0,z.jsxs)(J.A,{sx:{p:3},children:[(0,z.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Recent Trades"}),(0,z.jsx)(Q.A,{children:(0,z.jsxs)(Y.A,{size:"small",children:[(0,z.jsx)(ee.A,{children:(0,z.jsxs)(te.A,{children:[(0,z.jsx)(re.A,{children:"Trade ID"}),(0,z.jsx)(re.A,{children:"Amount"}),(0,z.jsx)(re.A,{children:"Status"}),(0,z.jsx)(re.A,{children:"Date"})]})}),(0,z.jsx)(ne.A,{children:(null===g||void 0===g||null===(p=g.recentActivity)||void 0===p||null===(A=p.trades)||void 0===A?void 0:A.map(e=>{var t,r;return(0,z.jsxs)(te.A,{children:[(0,z.jsx)(re.A,{children:e.tradeId}),(0,z.jsxs)(re.A,{children:[null===(t=e.cryptocurrency)||void 0===t?void 0:t.amount," ",null===(r=e.cryptocurrency)||void 0===r?void 0:r.symbol]}),(0,z.jsx)(re.A,{children:(0,z.jsx)(ae.A,{label:e.status,size:"small",color:"completed"===e.status?"success":"disputed"===e.status?"error":"default"})}),(0,z.jsx)(re.A,{children:new Date(e.createdAt).toLocaleDateString()})]},e._id)}))||(0,z.jsx)(te.A,{children:(0,z.jsx)(re.A,{colSpan:4,align:"center",children:(0,z.jsx)(i.A,{color:"textSecondary",children:"No recent trades"})})})})]})})]})}),(0,z.jsx)(K.Ay,{item:!0,xs:12,md:6,children:(0,z.jsxs)(J.A,{sx:{p:3},children:[(0,z.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Recent Disputes"}),(0,z.jsx)(Q.A,{children:(0,z.jsxs)(Y.A,{size:"small",children:[(0,z.jsx)(ee.A,{children:(0,z.jsxs)(te.A,{children:[(0,z.jsx)(re.A,{children:"Dispute ID"}),(0,z.jsx)(re.A,{children:"Trade ID"}),(0,z.jsx)(re.A,{children:"Status"}),(0,z.jsx)(re.A,{children:"Date"})]})}),(0,z.jsx)(ne.A,{children:(null===g||void 0===g||null===(v=g.recentActivity)||void 0===v||null===(x=v.disputes)||void 0===x?void 0:x.map(e=>{var t;return(0,z.jsxs)(te.A,{children:[(0,z.jsx)(re.A,{children:e.disputeId}),(0,z.jsx)(re.A,{children:null===(t=e.trade)||void 0===t?void 0:t.tradeId}),(0,z.jsx)(re.A,{children:(0,z.jsx)(ae.A,{label:e.status,size:"small",color:"resolved"===e.status?"success":"investigating"===e.status?"warning":"error"})}),(0,z.jsx)(re.A,{children:new Date(e.createdAt).toLocaleDateString()})]},e._id)}))||(0,z.jsx)(te.A,{children:(0,z.jsx)(re.A,{colSpan:4,align:"center",children:(0,z.jsx)(i.A,{color:"textSecondary",children:"No recent disputes"})})})})]})})]})})]}),(0,z.jsxs)(K.Ay,{container:!0,spacing:3,mt:2,children:[(0,z.jsx)(K.Ay,{item:!0,xs:12,md:4,children:(0,z.jsxs)(J.A,{sx:{p:3},children:[(0,z.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Top Cryptocurrencies"}),(0,z.jsx)(s.A,{children:["USDT","USDC","BTC","ETH","DAI"].map((e,t)=>(0,z.jsxs)(s.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1,children:[(0,z.jsx)(i.A,{variant:"body2",children:e}),(0,z.jsxs)(s.A,{display:"flex",alignItems:"center",gap:1,children:[(0,z.jsx)(q,{variant:"determinate",value:100*Math.random(),sx:{width:60,height:6}}),(0,z.jsxs)(i.A,{variant:"body2",color:"textSecondary",children:[Math.floor(100*Math.random()),"%"]})]})]},e))})]})}),(0,z.jsx)(K.Ay,{item:!0,xs:12,md:4,children:(0,z.jsxs)(J.A,{sx:{p:3},children:[(0,z.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Top Fiat Currencies"}),(0,z.jsx)(s.A,{children:["KES","USD","TZS","UGX","RWF"].map((e,t)=>(0,z.jsxs)(s.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1,children:[(0,z.jsx)(i.A,{variant:"body2",children:e}),(0,z.jsxs)(s.A,{display:"flex",alignItems:"center",gap:1,children:[(0,z.jsx)(q,{variant:"determinate",value:100*Math.random(),sx:{width:60,height:6}}),(0,z.jsxs)(i.A,{variant:"body2",color:"textSecondary",children:[Math.floor(100*Math.random()),"%"]})]})]},e))})]})}),(0,z.jsx)(K.Ay,{item:!0,xs:12,md:4,children:(0,z.jsxs)(J.A,{sx:{p:3},children:[(0,z.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"System Health"}),(0,z.jsxs)(s.A,{children:[(0,z.jsxs)(s.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1,children:[(0,z.jsx)(i.A,{variant:"body2",children:"API Response Time"}),(0,z.jsx)(ae.A,{label:"Good",color:"success",size:"small"})]}),(0,z.jsxs)(s.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1,children:[(0,z.jsx)(i.A,{variant:"body2",children:"Database Performance"}),(0,z.jsx)(ae.A,{label:"Excellent",color:"success",size:"small"})]}),(0,z.jsxs)(s.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1,children:[(0,z.jsx)(i.A,{variant:"body2",children:"WebSocket Connections"}),(0,z.jsx)(ae.A,{label:"Stable",color:"success",size:"small"})]}),(0,z.jsxs)(s.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1,children:[(0,z.jsx)(i.A,{variant:"body2",children:"Error Rate"}),(0,z.jsx)(ae.A,{label:"Low",color:"success",size:"small"})]})]})]})})]})]})}}}]);
//# sourceMappingURL=212.557afd79.chunk.js.map