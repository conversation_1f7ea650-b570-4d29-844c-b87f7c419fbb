/**
 * Circuit Breaker Monitoring Service
 * Comprehensive monitoring and alerting for circuit breaker states and transitions
 */

const logger = require('../utils/logger');
const { circuitBreakers } = require('../utils/circuitBreaker');
const { monitoringIntegrationService } = require('./monitoringIntegrationService');

class CircuitBreakerMonitoringService {
  constructor() {
    this.isInitialized = false;
    this.monitoringInterval = null;
    
    // Circuit breaker state history
    this.stateHistory = new Map();
    
    // Metrics collection
    this.metrics = {
      stateTransitions: new Map(),
      totalTrips: new Map(),
      totalResets: new Map(),
      averageRecoveryTime: new Map(),
      failurePatterns: new Map(),
      lastStateChange: new Map(),
      healthScores: new Map()
    };

    // Alert thresholds
    this.alertThresholds = {
      rapidTrips: 3,              // 3 trips in 5 minutes
      rapidTripsWindow: 5 * 60 * 1000, // 5 minutes
      longOpenDuration: 10 * 60 * 1000, // 10 minutes
      highFailureRate: 0.50,      // 50% failure rate
      lowSuccessRate: 0.30,       // 30% success rate
      stuckInHalfOpen: 2 * 60 * 1000 // 2 minutes in half-open
    };

    // Monitoring configuration
    this.monitoringConfig = {
      interval: 30000,            // 30 seconds
      stateHistoryLimit: 1000,    // Keep last 1000 state changes
      metricsRetentionPeriod: 24 * 60 * 60 * 1000 // 24 hours
    };

    // Initialize state tracking for all circuit breakers
    this.initializeStateTracking();
  }

  /**
   * Initialize the circuit breaker monitoring service
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        logger.warn('Circuit breaker monitoring service already initialized');
        return true;
      }

      logger.info('Initializing Circuit Breaker Monitoring Service...');

      // Start monitoring all circuit breakers
      this.startMonitoring();

      // Initialize metrics for all circuit breakers
      this.initializeMetrics();

      this.isInitialized = true;
      logger.info('✅ Circuit Breaker Monitoring Service initialized successfully');
      return true;

    } catch (error) {
      logger.error('❌ Failed to initialize Circuit Breaker Monitoring Service:', error);
      return false;
    }
  }

  /**
   * Initialize state tracking for all circuit breakers
   */
  initializeStateTracking() {
    Object.keys(circuitBreakers).forEach(name => {
      this.stateHistory.set(name, []);
      this.recordStateChange(name, 'CLOSED', 'initialization');
    });
  }

  /**
   * Initialize metrics for all circuit breakers
   */
  initializeMetrics() {
    Object.keys(circuitBreakers).forEach(name => {
      this.metrics.stateTransitions.set(name, {
        closedToOpen: 0,
        openToHalfOpen: 0,
        halfOpenToClosed: 0,
        halfOpenToOpen: 0
      });
      
      this.metrics.totalTrips.set(name, 0);
      this.metrics.totalResets.set(name, 0);
      this.metrics.averageRecoveryTime.set(name, 0);
      this.metrics.failurePatterns.set(name, []);
      this.metrics.lastStateChange.set(name, Date.now());
      this.metrics.healthScores.set(name, 100);
    });
  }

  /**
   * Start monitoring circuit breakers
   */
  startMonitoring() {
    this.monitoringInterval = setInterval(() => {
      this.monitorCircuitBreakers();
    }, this.monitoringConfig.interval);

    logger.debug('Circuit breaker monitoring started');
  }

  /**
   * Monitor all circuit breakers for state changes and issues
   */
  async monitorCircuitBreakers() {
    try {
      for (const [name, circuitBreaker] of Object.entries(circuitBreakers)) {
        await this.monitorCircuitBreaker(name, circuitBreaker);
      }

      // Check for alert conditions
      await this.checkAlertConditions();

      // Clean up old metrics
      this.cleanupOldMetrics();

    } catch (error) {
      logger.error('Error monitoring circuit breakers:', error);
    }
  }

  /**
   * Monitor individual circuit breaker
   */
  async monitorCircuitBreaker(name, circuitBreaker) {
    try {
      const currentState = circuitBreaker.getState();
      const lastKnownState = this.getLastKnownState(name);

      // Check for state transitions
      if (lastKnownState && currentState.state !== lastKnownState.state) {
        await this.handleStateTransition(name, lastKnownState.state, currentState.state);
      }

      // Update metrics
      this.updateCircuitBreakerMetrics(name, currentState);

      // Calculate health score
      this.calculateHealthScore(name, currentState);

      // Record current state
      this.recordStateChange(name, currentState.state, 'monitoring');

    } catch (error) {
      logger.error(`Error monitoring circuit breaker ${name}:`, error);
    }
  }

  /**
   * Handle circuit breaker state transitions
   */
  async handleStateTransition(name, fromState, toState) {
    try {
      logger.info(`Circuit breaker ${name} state transition: ${fromState} → ${toState}`);

      // Record state transition
      const transitions = this.metrics.stateTransitions.get(name);
      const transitionKey = `${fromState.toLowerCase()}To${toState.charAt(0).toUpperCase() + toState.slice(1).toLowerCase()}`;
      
      if (transitions[transitionKey] !== undefined) {
        transitions[transitionKey]++;
      }

      // Handle specific transitions
      switch (toState) {
        case 'OPEN':
          await this.handleCircuitBreakerTrip(name, fromState);
          break;
        case 'CLOSED':
          await this.handleCircuitBreakerReset(name, fromState);
          break;
        case 'HALF_OPEN':
          await this.handleCircuitBreakerHalfOpen(name, fromState);
          break;
      }

      // Update last state change time
      this.metrics.lastStateChange.set(name, Date.now());

    } catch (error) {
      logger.error(`Error handling state transition for ${name}:`, error);
    }
  }

  /**
   * Handle circuit breaker trip (transition to OPEN)
   */
  async handleCircuitBreakerTrip(name, fromState) {
    try {
      // Increment trip counter
      const currentTrips = this.metrics.totalTrips.get(name) || 0;
      this.metrics.totalTrips.set(name, currentTrips + 1);

      // Record failure pattern
      const patterns = this.metrics.failurePatterns.get(name) || [];
      patterns.push({
        timestamp: Date.now(),
        fromState,
        toState: 'OPEN',
        type: 'trip'
      });

      // Keep only recent patterns
      const recentPatterns = patterns.filter(
        p => Date.now() - p.timestamp < this.monitoringConfig.metricsRetentionPeriod
      );
      this.metrics.failurePatterns.set(name, recentPatterns);

      // Send alert
      await this.triggerAlert('CIRCUIT_BREAKER_TRIPPED', {
        circuitBreaker: name,
        fromState,
        toState: 'OPEN',
        tripCount: currentTrips + 1,
        message: `Circuit breaker ${name} has tripped and is now OPEN`,
        severity: 'high'
      });

      logger.warn(`Circuit breaker ${name} tripped (trip #${currentTrips + 1})`);

    } catch (error) {
      logger.error(`Error handling circuit breaker trip for ${name}:`, error);
    }
  }

  /**
   * Handle circuit breaker reset (transition to CLOSED)
   */
  async handleCircuitBreakerReset(name, fromState) {
    try {
      // Increment reset counter
      const currentResets = this.metrics.totalResets.get(name) || 0;
      this.metrics.totalResets.set(name, currentResets + 1);

      // Calculate recovery time if coming from OPEN state
      if (fromState === 'OPEN') {
        const recoveryTime = this.calculateRecoveryTime(name);
        if (recoveryTime > 0) {
          this.updateAverageRecoveryTime(name, recoveryTime);
        }
      }

      // Send alert for successful recovery
      await this.triggerAlert('CIRCUIT_BREAKER_RECOVERED', {
        circuitBreaker: name,
        fromState,
        toState: 'CLOSED',
        resetCount: currentResets + 1,
        message: `Circuit breaker ${name} has recovered and is now CLOSED`,
        severity: 'info'
      });

      logger.info(`Circuit breaker ${name} reset and recovered (reset #${currentResets + 1})`);

    } catch (error) {
      logger.error(`Error handling circuit breaker reset for ${name}:`, error);
    }
  }

  /**
   * Handle circuit breaker half-open state
   */
  async handleCircuitBreakerHalfOpen(name, fromState) {
    try {
      logger.info(`Circuit breaker ${name} entering HALF_OPEN state from ${fromState}`);

      // Set timeout to check if stuck in half-open
      setTimeout(async () => {
        const currentState = circuitBreakers[name].getState();
        if (currentState.state === 'HALF_OPEN') {
          await this.triggerAlert('CIRCUIT_BREAKER_STUCK_HALF_OPEN', {
            circuitBreaker: name,
            duration: this.alertThresholds.stuckInHalfOpen,
            message: `Circuit breaker ${name} has been stuck in HALF_OPEN state`,
            severity: 'medium'
          });
        }
      }, this.alertThresholds.stuckInHalfOpen);

    } catch (error) {
      logger.error(`Error handling circuit breaker half-open for ${name}:`, error);
    }
  }

  /**
   * Update circuit breaker metrics
   */
  updateCircuitBreakerMetrics(name, state) {
    try {
      // Update health score based on current metrics
      this.calculateHealthScore(name, state);

    } catch (error) {
      logger.error(`Error updating metrics for ${name}:`, error);
    }
  }

  /**
   * Calculate health score for circuit breaker
   */
  calculateHealthScore(name, state) {
    try {
      let score = 100;

      // Reduce score based on current state
      switch (state.state) {
        case 'OPEN':
          score -= 50;
          break;
        case 'HALF_OPEN':
          score -= 20;
          break;
        case 'CLOSED':
          // No reduction for closed state
          break;
      }

      // Reduce score based on failure rate
      const totalRequests = state.metrics.totalRequests;
      const totalFailures = state.metrics.totalFailures;
      
      if (totalRequests > 0) {
        const failureRate = totalFailures / totalRequests;
        if (failureRate > this.alertThresholds.highFailureRate) {
          score -= 30;
        } else if (failureRate > 0.25) {
          score -= 15;
        }
      }

      // Reduce score based on recent trips
      const recentTrips = this.getRecentTrips(name);
      if (recentTrips >= this.alertThresholds.rapidTrips) {
        score -= 25;
      }

      // Ensure score doesn't go below 0
      score = Math.max(0, score);

      this.metrics.healthScores.set(name, score);

    } catch (error) {
      logger.error(`Error calculating health score for ${name}:`, error);
    }
  }

  /**
   * Get recent trips for a circuit breaker
   */
  getRecentTrips(name) {
    const patterns = this.metrics.failurePatterns.get(name) || [];
    const cutoff = Date.now() - this.alertThresholds.rapidTripsWindow;
    
    return patterns.filter(p => 
      p.timestamp > cutoff && 
      p.type === 'trip'
    ).length;
  }

  /**
   * Calculate recovery time from last trip
   */
  calculateRecoveryTime(name) {
    const history = this.stateHistory.get(name) || [];
    
    // Find last OPEN state
    const lastOpenIndex = history.findLastIndex(entry => entry.state === 'OPEN');
    if (lastOpenIndex === -1) return 0;

    const openTime = history[lastOpenIndex].timestamp;
    return Date.now() - openTime;
  }

  /**
   * Update average recovery time
   */
  updateAverageRecoveryTime(name, recoveryTime) {
    const currentAverage = this.metrics.averageRecoveryTime.get(name) || 0;
    const resetCount = this.metrics.totalResets.get(name) || 1;

    const newAverage = ((currentAverage * (resetCount - 1)) + recoveryTime) / resetCount;
    this.metrics.averageRecoveryTime.set(name, newAverage);
  }

  /**
   * Check for alert conditions across all circuit breakers
   */
  async checkAlertConditions() {
    try {
      for (const [name, circuitBreaker] of Object.entries(circuitBreakers)) {
        const state = circuitBreaker.getState();

        // Check for rapid trips
        const recentTrips = this.getRecentTrips(name);
        if (recentTrips >= this.alertThresholds.rapidTrips) {
          await this.triggerAlert('CIRCUIT_BREAKER_RAPID_TRIPS', {
            circuitBreaker: name,
            tripCount: recentTrips,
            timeWindow: this.alertThresholds.rapidTripsWindow / 1000 / 60, // minutes
            message: `Circuit breaker ${name} has ${recentTrips} trips in ${this.alertThresholds.rapidTripsWindow / 1000 / 60} minutes`,
            severity: 'critical'
          });
        }

        // Check for long open duration
        if (state.state === 'OPEN') {
          const openDuration = Date.now() - (this.metrics.lastStateChange.get(name) || Date.now());
          if (openDuration > this.alertThresholds.longOpenDuration) {
            await this.triggerAlert('CIRCUIT_BREAKER_LONG_OPEN', {
              circuitBreaker: name,
              duration: openDuration,
              message: `Circuit breaker ${name} has been OPEN for ${Math.round(openDuration / 1000 / 60)} minutes`,
              severity: 'high'
            });
          }
        }

        // Check for high failure rate
        const totalRequests = state.metrics.totalRequests;
        const totalFailures = state.metrics.totalFailures;

        if (totalRequests > 10) { // Only check if we have enough data
          const failureRate = totalFailures / totalRequests;
          if (failureRate > this.alertThresholds.highFailureRate) {
            await this.triggerAlert('CIRCUIT_BREAKER_HIGH_FAILURE_RATE', {
              circuitBreaker: name,
              failureRate: (failureRate * 100).toFixed(2),
              threshold: (this.alertThresholds.highFailureRate * 100).toFixed(2),
              message: `Circuit breaker ${name} has high failure rate: ${(failureRate * 100).toFixed(2)}%`,
              severity: 'high'
            });
          }
        }

        // Check for low success rate in half-open state
        if (state.state === 'HALF_OPEN' && state.successCount > 0) {
          const successRate = state.successCount / (state.successCount + state.failureCount);
          if (successRate < this.alertThresholds.lowSuccessRate) {
            await this.triggerAlert('CIRCUIT_BREAKER_LOW_SUCCESS_RATE', {
              circuitBreaker: name,
              successRate: (successRate * 100).toFixed(2),
              threshold: (this.alertThresholds.lowSuccessRate * 100).toFixed(2),
              message: `Circuit breaker ${name} has low success rate in HALF_OPEN: ${(successRate * 100).toFixed(2)}%`,
              severity: 'medium'
            });
          }
        }
      }

    } catch (error) {
      logger.error('Error checking circuit breaker alert conditions:', error);
    }
  }

  /**
   * Record state change in history
   */
  recordStateChange(name, state, reason) {
    try {
      const history = this.stateHistory.get(name) || [];

      history.push({
        timestamp: Date.now(),
        state,
        reason
      });

      // Keep only recent history
      const recentHistory = history.slice(-this.monitoringConfig.stateHistoryLimit);
      this.stateHistory.set(name, recentHistory);

    } catch (error) {
      logger.error(`Error recording state change for ${name}:`, error);
    }
  }

  /**
   * Get last known state for circuit breaker
   */
  getLastKnownState(name) {
    const history = this.stateHistory.get(name) || [];
    return history.length > 0 ? history[history.length - 1] : null;
  }

  /**
   * Clean up old metrics data
   */
  cleanupOldMetrics() {
    try {
      const cutoff = Date.now() - this.monitoringConfig.metricsRetentionPeriod;

      // Clean up failure patterns
      for (const [name, patterns] of this.metrics.failurePatterns.entries()) {
        const recentPatterns = patterns.filter(p => p.timestamp > cutoff);
        this.metrics.failurePatterns.set(name, recentPatterns);
      }

      // Clean up state history
      for (const [name, history] of this.stateHistory.entries()) {
        const recentHistory = history.filter(h => h.timestamp > cutoff);
        this.stateHistory.set(name, recentHistory);
      }

    } catch (error) {
      logger.error('Error cleaning up old metrics:', error);
    }
  }

  /**
   * Trigger alert through monitoring integration service
   */
  async triggerAlert(alertType, alertData) {
    try {
      if (monitoringIntegrationService && typeof monitoringIntegrationService.triggerAlert === 'function') {
        await monitoringIntegrationService.triggerAlert(alertType, alertData);
      } else {
        logger.warn('Monitoring integration service not available for circuit breaker alerts');
        logger.warn(`Circuit Breaker Alert [${alertType}]:`, alertData);
      }
    } catch (error) {
      logger.error('Error triggering circuit breaker alert:', error);
    }
  }

  /**
   * Get comprehensive circuit breaker metrics
   */
  getCircuitBreakerMetrics() {
    try {
      const metrics = {};

      for (const [name, circuitBreaker] of Object.entries(circuitBreakers)) {
        const state = circuitBreaker.getState();
        const history = this.stateHistory.get(name) || [];
        const transitions = this.metrics.stateTransitions.get(name) || {};
        const patterns = this.metrics.failurePatterns.get(name) || [];

        metrics[name] = {
          // Current state
          currentState: state.state,
          failureCount: state.failureCount,
          successCount: state.successCount,
          nextAttempt: state.nextAttempt,
          lastFailureTime: state.lastFailureTime,

          // Circuit breaker metrics
          totalRequests: state.metrics.totalRequests,
          totalFailures: state.metrics.totalFailures,
          totalSuccesses: state.metrics.totalSuccesses,
          totalTimeouts: state.metrics.totalTimeouts,
          averageResponseTime: state.metrics.averageResponseTime,

          // Monitoring metrics
          totalTrips: this.metrics.totalTrips.get(name) || 0,
          totalResets: this.metrics.totalResets.get(name) || 0,
          averageRecoveryTime: this.metrics.averageRecoveryTime.get(name) || 0,
          healthScore: this.metrics.healthScores.get(name) || 100,
          lastStateChange: this.metrics.lastStateChange.get(name),

          // State transitions
          stateTransitions: transitions,

          // Recent activity
          recentTrips: this.getRecentTrips(name),
          stateHistory: history.slice(-10), // Last 10 state changes
          failurePatterns: patterns.slice(-5), // Last 5 failure patterns

          // Calculated metrics
          failureRate: state.metrics.totalRequests > 0 ?
            (state.metrics.totalFailures / state.metrics.totalRequests) : 0,
          successRate: state.metrics.totalRequests > 0 ?
            (state.metrics.totalSuccesses / state.metrics.totalRequests) : 0,
          uptime: this.calculateUptime(name)
        };
      }

      return {
        circuitBreakers: metrics,
        summary: this.getCircuitBreakerSummary(metrics),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Error getting circuit breaker metrics:', error);
      return { error: error.message };
    }
  }

  /**
   * Calculate uptime percentage for circuit breaker
   */
  calculateUptime(name) {
    try {
      const history = this.stateHistory.get(name) || [];
      if (history.length < 2) return 100;

      const totalTime = Date.now() - history[0].timestamp;
      let downTime = 0;

      for (let i = 0; i < history.length - 1; i++) {
        if (history[i].state === 'OPEN') {
          const duration = history[i + 1].timestamp - history[i].timestamp;
          downTime += duration;
        }
      }

      // Check if currently down
      const lastEntry = history[history.length - 1];
      if (lastEntry.state === 'OPEN') {
        downTime += Date.now() - lastEntry.timestamp;
      }

      const uptime = ((totalTime - downTime) / totalTime) * 100;
      return Math.max(0, Math.min(100, uptime));

    } catch (error) {
      logger.error(`Error calculating uptime for ${name}:`, error);
      return 100;
    }
  }

  /**
   * Get circuit breaker summary
   */
  getCircuitBreakerSummary(metrics) {
    try {
      const summary = {
        totalCircuitBreakers: Object.keys(metrics).length,
        healthyCount: 0,
        degradedCount: 0,
        unhealthyCount: 0,
        openCount: 0,
        halfOpenCount: 0,
        closedCount: 0,
        averageHealthScore: 0,
        totalTrips: 0,
        totalResets: 0,
        averageUptime: 0
      };

      let totalHealthScore = 0;
      let totalUptime = 0;

      for (const [name, cbMetrics] of Object.entries(metrics)) {
        // Count by state
        switch (cbMetrics.currentState) {
          case 'OPEN':
            summary.openCount++;
            break;
          case 'HALF_OPEN':
            summary.halfOpenCount++;
            break;
          case 'CLOSED':
            summary.closedCount++;
            break;
        }

        // Count by health
        if (cbMetrics.healthScore >= 80) {
          summary.healthyCount++;
        } else if (cbMetrics.healthScore >= 50) {
          summary.degradedCount++;
        } else {
          summary.unhealthyCount++;
        }

        // Accumulate totals
        summary.totalTrips += cbMetrics.totalTrips;
        summary.totalResets += cbMetrics.totalResets;
        totalHealthScore += cbMetrics.healthScore;
        totalUptime += cbMetrics.uptime;
      }

      // Calculate averages
      const count = summary.totalCircuitBreakers;
      if (count > 0) {
        summary.averageHealthScore = totalHealthScore / count;
        summary.averageUptime = totalUptime / count;
      }

      return summary;

    } catch (error) {
      logger.error('Error generating circuit breaker summary:', error);
      return {};
    }
  }

  /**
   * Force circuit breaker state for testing
   */
  async forceCircuitBreakerState(name, state) {
    try {
      const circuitBreaker = circuitBreakers[name];
      if (!circuitBreaker) {
        throw new Error(`Circuit breaker ${name} not found`);
      }

      const currentState = circuitBreaker.getState().state;

      switch (state.toLowerCase()) {
        case 'open':
          circuitBreaker.forceOpen();
          break;
        case 'closed':
          circuitBreaker.forceClose();
          break;
        default:
          throw new Error(`Invalid state: ${state}. Use 'open' or 'closed'`);
      }

      // Record the forced state change
      this.recordStateChange(name, state.toUpperCase(), 'forced');

      logger.info(`Circuit breaker ${name} forced from ${currentState} to ${state.toUpperCase()}`);

      return {
        success: true,
        circuitBreaker: name,
        previousState: currentState,
        newState: state.toUpperCase(),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error(`Error forcing circuit breaker state for ${name}:`, error);
      throw error;
    }
  }

  /**
   * Reset circuit breaker metrics
   */
  resetCircuitBreakerMetrics(name = null) {
    try {
      if (name) {
        // Reset specific circuit breaker
        if (!circuitBreakers[name]) {
          throw new Error(`Circuit breaker ${name} not found`);
        }

        this.stateHistory.set(name, []);
        this.metrics.stateTransitions.set(name, {
          closedToOpen: 0,
          openToHalfOpen: 0,
          halfOpenToClosed: 0,
          halfOpenToOpen: 0
        });
        this.metrics.totalTrips.set(name, 0);
        this.metrics.totalResets.set(name, 0);
        this.metrics.averageRecoveryTime.set(name, 0);
        this.metrics.failurePatterns.set(name, []);
        this.metrics.lastStateChange.set(name, Date.now());
        this.metrics.healthScores.set(name, 100);

        logger.info(`Circuit breaker metrics reset for ${name}`);
        return { success: true, circuitBreaker: name };

      } else {
        // Reset all circuit breakers
        this.initializeMetrics();
        this.initializeStateTracking();

        logger.info('All circuit breaker metrics reset');
        return { success: true, circuitBreaker: 'all' };
      }

    } catch (error) {
      logger.error('Error resetting circuit breaker metrics:', error);
      throw error;
    }
  }

  /**
   * Shutdown the monitoring service
   */
  async shutdown() {
    try {
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
      }

      this.isInitialized = false;
      logger.info('Circuit Breaker Monitoring Service shutdown completed');

    } catch (error) {
      logger.error('Error shutting down Circuit Breaker Monitoring Service:', error);
    }
  }
}

// Create singleton instance
const circuitBreakerMonitoringService = new CircuitBreakerMonitoringService();

module.exports = {
  CircuitBreakerMonitoringService,
  circuitBreakerMonitoringService
};
