import { Clipboard } from 'react-native';

/**
 * Clipboard utility that handles the deprecation warning gracefully
 * This is a temporary solution until we can properly migrate to @react-native-clipboard/clipboard
 */
export const clipboardUtils = {
  /**
   * Copy text to clipboard
   * @param text - Text to copy
   */
  setString: async (text: string): Promise<void> => {
    try {
      // Suppress the deprecation warning temporarily
      const originalWarn = console.warn;
      console.warn = (...args) => {
        if (args[0]?.includes?.('Clipboard has been extracted')) {
          return; // Suppress this specific warning
        }
        originalWarn(...args);
      };

      await Clipboard.setString(text);

      // Restore original console.warn
      console.warn = originalWarn;
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      throw error;
    }
  },

  /**
   * Get text from clipboard
   */
  getString: async (): Promise<string> => {
    try {
      // Suppress the deprecation warning temporarily
      const originalWarn = console.warn;
      console.warn = (...args) => {
        if (args[0]?.includes?.('Clipboard has been extracted')) {
          return; // Suppress this specific warning
        }
        originalWarn(...args);
      };

      const result = await Clipboard.getString();

      // Restore original console.warn
      console.warn = originalWarn;

      return result;
    } catch (error) {
      console.error('Failed to get from clipboard:', error);
      throw error;
    }
  }
};
