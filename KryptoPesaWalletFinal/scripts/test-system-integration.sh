#!/bin/bash

# KryptoPesa System Integration Test Script
# This script tests the complete system integration

set -e

echo "🧪 KryptoPesa System Integration Tests"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test database connections
test_database_connections() {
    print_status "Testing database connections..."
    
    # Test MongoDB
    if docker-compose exec -T mongodb mongosh --eval "db.runCommand('ping')" &> /dev/null; then
        print_success "MongoDB connection successful"
    else
        print_error "MongoDB connection failed"
        return 1
    fi
    
    # Test Redis
    if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
        print_success "Redis connection successful"
    else
        print_error "Redis connection failed"
        return 1
    fi
}

# Test backend API
test_backend_api() {
    print_status "Testing backend API..."
    
    # Test health endpoint
    if curl -f http://localhost:3000/health &> /dev/null; then
        print_success "Backend health check passed"
    else
        print_error "Backend health check failed"
        return 1
    fi
    
    # Test API endpoints
    local endpoints=(
        "/api/auth/me"
        "/api/offers"
        "/api/trades"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000$endpoint)
        if [[ $status_code == "200" || $status_code == "401" ]]; then
            print_success "API endpoint $endpoint is responding"
        else
            print_error "API endpoint $endpoint failed (status: $status_code)"
            return 1
        fi
    done
}

# Test smart contracts
test_smart_contracts() {
    print_status "Testing smart contracts..."
    
    cd smart-contracts
    
    # Run contract tests
    if npm test &> /dev/null; then
        print_success "Smart contract tests passed"
    else
        print_error "Smart contract tests failed"
        cd ..
        return 1
    fi
    
    cd ..
}

# Test WebSocket connection
test_websocket_connection() {
    print_status "Testing WebSocket connection..."
    
    # Create a simple WebSocket test
    cat > test_websocket.js << 'EOF'
const io = require('socket.io-client');

const socket = io('http://localhost:3000', {
    transports: ['websocket'],
    timeout: 5000
});

socket.on('connect', () => {
    console.log('WebSocket connected successfully');
    socket.disconnect();
    process.exit(0);
});

socket.on('connect_error', (error) => {
    console.error('WebSocket connection failed:', error.message);
    process.exit(1);
});

setTimeout(() => {
    console.error('WebSocket connection timeout');
    process.exit(1);
}, 5000);
EOF

    if node test_websocket.js; then
        print_success "WebSocket connection test passed"
    else
        print_error "WebSocket connection test failed"
        rm -f test_websocket.js
        return 1
    fi
    
    rm -f test_websocket.js
}

# Test admin dashboard
test_admin_dashboard() {
    print_status "Testing admin dashboard..."
    
    # Wait for admin dashboard to start
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -f http://localhost:3001 &> /dev/null; then
            print_success "Admin dashboard is accessible"
            return 0
        fi
        
        sleep 2
        ((attempt++))
    done
    
    print_error "Admin dashboard is not accessible"
    return 1
}

# Test mobile app build
test_mobile_app_build() {
    print_status "Testing mobile app build..."
    
    cd mobile
    
    # Test if dependencies are installed
    if [ ! -d "node_modules" ]; then
        print_error "Mobile app dependencies not installed"
        cd ..
        return 1
    fi
    
    # Test Metro bundler start (without actually starting)
    if npm run start --help &> /dev/null; then
        print_success "Mobile app Metro bundler is ready"
    else
        print_error "Mobile app Metro bundler test failed"
        cd ..
        return 1
    fi
    
    cd ..
}

# Test environment configuration
test_environment_config() {
    print_status "Testing environment configuration..."
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        print_error ".env file not found"
        return 1
    fi
    
    # Check required environment variables
    local required_vars=(
        "NODE_ENV"
        "PORT"
        "MONGODB_URI"
        "REDIS_URL"
        "JWT_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if grep -q "^$var=" .env; then
            print_success "Environment variable $var is set"
        else
            print_error "Environment variable $var is missing"
            return 1
        fi
    done
}

# Test file permissions and structure
test_file_structure() {
    print_status "Testing file structure and permissions..."
    
    local required_dirs=(
        "backend"
        "mobile"
        "admin-dashboard"
        "smart-contracts"
        "shared"
        "docs"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            print_success "Directory $dir exists"
        else
            print_error "Directory $dir is missing"
            return 1
        fi
    done
    
    # Check if scripts are executable
    if [ -x "scripts/setup-dev-environment.sh" ]; then
        print_success "Setup script is executable"
    else
        print_error "Setup script is not executable"
        return 1
    fi
}

# Test Docker services
test_docker_services() {
    print_status "Testing Docker services..."
    
    local services=("mongodb" "redis")
    
    for service in "${services[@]}"; do
        if docker-compose ps $service | grep -q "Up"; then
            print_success "Docker service $service is running"
        else
            print_error "Docker service $service is not running"
            return 1
        fi
    done
}

# Performance test
test_performance() {
    print_status "Running basic performance tests..."
    
    # Test API response time
    local response_time=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:3000/health)
    local response_time_ms=$(echo "$response_time * 1000" | bc)
    
    if (( $(echo "$response_time < 1.0" | bc -l) )); then
        print_success "API response time: ${response_time_ms}ms (Good)"
    else
        print_error "API response time: ${response_time_ms}ms (Slow)"
        return 1
    fi
    
    # Test database query performance
    local db_response_time=$(docker-compose exec -T mongodb mongosh --eval "
        var start = new Date();
        db.runCommand('ping');
        var end = new Date();
        print(end - start);
    " | tail -1)
    
    if [ "$db_response_time" -lt 100 ]; then
        print_success "Database response time: ${db_response_time}ms (Good)"
    else
        print_error "Database response time: ${db_response_time}ms (Slow)"
        return 1
    fi
}

# Security test
test_security() {
    print_status "Running basic security tests..."
    
    # Test CORS headers
    local cors_header=$(curl -s -H "Origin: http://malicious-site.com" -I http://localhost:3000/health | grep -i "access-control-allow-origin" || echo "")
    
    if [[ $cors_header == *"localhost"* ]] || [[ $cors_header == *"kryptopesa.com"* ]]; then
        print_success "CORS is properly configured"
    else
        print_error "CORS configuration may be insecure"
        return 1
    fi
    
    # Test rate limiting
    local rate_limit_header=$(curl -s -I http://localhost:3000/api/auth/me | grep -i "x-ratelimit" || echo "")
    
    if [[ $rate_limit_header == *"x-ratelimit"* ]]; then
        print_success "Rate limiting is enabled"
    else
        print_error "Rate limiting may not be configured"
        return 1
    fi
}

# Generate test report
generate_test_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="test-report-$(date '+%Y%m%d-%H%M%S').txt"
    
    cat > $report_file << EOF
KryptoPesa System Integration Test Report
Generated: $timestamp

Test Results:
- Database Connections: $database_test_result
- Backend API: $api_test_result
- Smart Contracts: $contracts_test_result
- WebSocket Connection: $websocket_test_result
- Admin Dashboard: $admin_test_result
- Mobile App Build: $mobile_test_result
- Environment Config: $env_test_result
- File Structure: $structure_test_result
- Docker Services: $docker_test_result
- Performance: $performance_test_result
- Security: $security_test_result

Overall Status: $overall_status
EOF

    print_status "Test report generated: $report_file"
}

# Main test execution
main() {
    local failed_tests=0
    
    echo "Starting system integration tests..."
    echo ""
    
    # Run all tests
    test_file_structure && structure_test_result="PASS" || { structure_test_result="FAIL"; ((failed_tests++)); }
    test_environment_config && env_test_result="PASS" || { env_test_result="FAIL"; ((failed_tests++)); }
    test_docker_services && docker_test_result="PASS" || { docker_test_result="FAIL"; ((failed_tests++)); }
    test_database_connections && database_test_result="PASS" || { database_test_result="FAIL"; ((failed_tests++)); }
    test_backend_api && api_test_result="PASS" || { api_test_result="FAIL"; ((failed_tests++)); }
    test_smart_contracts && contracts_test_result="PASS" || { contracts_test_result="FAIL"; ((failed_tests++)); }
    test_websocket_connection && websocket_test_result="PASS" || { websocket_test_result="FAIL"; ((failed_tests++)); }
    test_admin_dashboard && admin_test_result="PASS" || { admin_test_result="FAIL"; ((failed_tests++)); }
    test_mobile_app_build && mobile_test_result="PASS" || { mobile_test_result="FAIL"; ((failed_tests++)); }
    test_performance && performance_test_result="PASS" || { performance_test_result="FAIL"; ((failed_tests++)); }
    test_security && security_test_result="PASS" || { security_test_result="FAIL"; ((failed_tests++)); }
    
    # Determine overall status
    if [ $failed_tests -eq 0 ]; then
        overall_status="ALL TESTS PASSED"
        print_success "🎉 All integration tests passed!"
    else
        overall_status="$failed_tests TESTS FAILED"
        print_error "❌ $failed_tests tests failed"
    fi
    
    generate_test_report
    
    echo ""
    echo "Integration test summary: $overall_status"
    
    exit $failed_tests
}

# Run main function
main
