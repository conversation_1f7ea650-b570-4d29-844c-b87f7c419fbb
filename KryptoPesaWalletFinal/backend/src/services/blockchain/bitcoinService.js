const bitcoin = require('bitcoinjs-lib');
const axios = require('axios');
const logger = require('../../utils/logger');
const { circuitBreakers } = require('../../utils/circuitBreaker');

class BitcoinService {
  constructor() {
    this.network = process.env.NODE_ENV === 'production' ? bitcoin.networks.bitcoin : bitcoin.networks.testnet;
    this.apiBaseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://blockstream.info/api'
      : 'https://blockstream.info/testnet/api';
    
    this.initialized = false;
    this.maxRetries = 3;
    this.baseDelay = 1000;
  }

  /**
   * Execute Bitcoin operation with retry logic and circuit breaker
   */
  async executeWithRetry(operation, maxRetries = this.maxRetries) {
    return await circuitBreakers.blockchain.execute(async () => {
      let lastError;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await operation();
        } catch (error) {
          lastError = error;

          if (this.isNonRetryableError(error)) {
            throw error;
          }

          if (attempt < maxRetries) {
            const delay = this.calculateBackoffDelay(attempt);
            logger.warn(`Bitcoin operation failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, {
              error: error.message,
              attempt,
              maxRetries
            });
            await this.sleep(delay);
          }
        }
      }

      throw lastError;
    });
  }

  isNonRetryableError(error) {
    const nonRetryableMessages = [
      'invalid address',
      'insufficient funds',
      'invalid transaction',
      'invalid private key'
    ];
    
    return nonRetryableMessages.some(msg => 
      error.message.toLowerCase().includes(msg)
    );
  }

  calculateBackoffDelay(attempt) {
    return this.baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async initialize() {
    if (!this.initialized) {
      this.initialized = true;
      logger.info(`Bitcoin service initialized for ${this.network === bitcoin.networks.bitcoin ? 'mainnet' : 'testnet'}`);
    }
  }

  /**
   * Generate Bitcoin address from mnemonic
   */
  generateAddressFromMnemonic(mnemonic, accountIndex = 0, addressIndex = 0) {
    try {
      const bip39 = require('bip39');
      const { BIP32Factory } = require('bip32');
      const ecc = require('tiny-secp256k1');
      const bip32 = BIP32Factory(ecc);

      if (!bip39.validateMnemonic(mnemonic)) {
        throw new Error('Invalid mnemonic phrase');
      }

      const seed = bip39.mnemonicToSeedSync(mnemonic);
      const root = bip32.fromSeed(seed, this.network);
      
      // BIP44 derivation path: m/44'/0'/account'/0/address_index
      const path = `m/44'/0'/${accountIndex}'/0/${addressIndex}`;
      const child = root.derivePath(path);
      
      const { address } = bitcoin.payments.p2pkh({
        pubkey: child.publicKey,
        network: this.network
      });

      return {
        address,
        publicKey: child.publicKey.toString('hex'),
        derivationPath: path,
        privateKey: child.privateKey.toString('hex') // Only return during wallet creation
      };
    } catch (error) {
      logger.error(`Error generating Bitcoin address: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get Bitcoin balance for address
   */
  async getBalance(address) {
    return await this.executeWithRetry(async () => {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/address/${address}`);
        const data = response.data;

        return {
          balance: data.chain_stats.funded_txo_sum.toString(),
          unconfirmedBalance: data.mempool_stats.funded_txo_sum.toString(),
          decimals: 8,
          formatted: (data.chain_stats.funded_txo_sum / 100000000).toFixed(8)
        };
      } catch (error) {
        logger.error(`Error getting Bitcoin balance: ${error.message}`);
        throw error;
      }
    });
  }

  /**
   * Get UTXOs for address
   */
  async getUTXOs(address) {
    return await this.executeWithRetry(async () => {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/address/${address}/utxo`);
        return response.data.map(utxo => ({
          txid: utxo.txid,
          vout: utxo.vout,
          value: utxo.value,
          status: utxo.status
        }));
      } catch (error) {
        logger.error(`Error getting UTXOs: ${error.message}`);
        throw error;
      }
    });
  }

  /**
   * Get transaction history for address
   */
  async getTransactionHistory(address, limit = 50) {
    return await this.executeWithRetry(async () => {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/address/${address}/txs`);
        const transactions = response.data.slice(0, limit);

        return transactions.map(tx => ({
          hash: tx.txid,
          blockHeight: tx.status.block_height,
          blockTime: tx.status.block_time,
          confirmations: tx.status.confirmed ? 1 : 0,
          fee: tx.fee,
          inputs: tx.vin.map(input => ({
            txid: input.txid,
            vout: input.vout,
            value: input.prevout?.value || 0
          })),
          outputs: tx.vout.map(output => ({
            value: output.value,
            address: output.scriptpubkey_address
          }))
        }));
      } catch (error) {
        logger.error(`Error getting transaction history: ${error.message}`);
        throw error;
      }
    });
  }

  /**
   * Estimate transaction fee
   */
  async estimateFee(targetBlocks = 6) {
    return await this.executeWithRetry(async () => {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/fee-estimates`);
        const feeRate = response.data[targetBlocks.toString()] || response.data['6'];
        
        return {
          feeRate: Math.ceil(feeRate), // sat/vB
          targetBlocks,
          estimatedMinutes: targetBlocks * 10
        };
      } catch (error) {
        logger.error(`Error estimating fee: ${error.message}`);
        throw error;
      }
    });
  }

  /**
   * Create and sign Bitcoin transaction
   */
  async createTransaction(fromAddress, toAddress, amount, privateKeyHex, feeRate = null) {
    return await this.executeWithRetry(async () => {
      try {
        // Get UTXOs
        const utxos = await this.getUTXOs(fromAddress);
        
        if (utxos.length === 0) {
          throw new Error('No UTXOs available');
        }

        // Get fee rate if not provided
        if (!feeRate) {
          const feeEstimate = await this.estimateFee();
          feeRate = feeEstimate.feeRate;
        }

        // Create transaction
        const psbt = new bitcoin.Psbt({ network: this.network });
        
        let totalInput = 0;
        for (const utxo of utxos) {
          if (totalInput >= amount + (feeRate * 250)) break; // Rough fee estimation
          
          psbt.addInput({
            hash: utxo.txid,
            index: utxo.vout,
            witnessUtxo: {
              script: bitcoin.address.toOutputScript(fromAddress, this.network),
              value: utxo.value
            }
          });
          
          totalInput += utxo.value;
        }

        if (totalInput < amount) {
          throw new Error('Insufficient funds');
        }

        // Add output
        psbt.addOutput({
          address: toAddress,
          value: amount
        });

        // Add change output if needed
        const estimatedFee = feeRate * 250; // Rough estimation
        const change = totalInput - amount - estimatedFee;
        
        if (change > 546) { // Dust threshold
          psbt.addOutput({
            address: fromAddress,
            value: change
          });
        }

        // Sign transaction
        const privateKey = Buffer.from(privateKeyHex, 'hex');
        psbt.signAllInputs(bitcoin.ECPair.fromPrivateKey(privateKey));
        psbt.finalizeAllInputs();

        const tx = psbt.extractTransaction();
        
        return {
          txHex: tx.toHex(),
          txId: tx.getId(),
          fee: estimatedFee,
          size: tx.byteLength()
        };
      } catch (error) {
        logger.error(`Error creating Bitcoin transaction: ${error.message}`);
        throw error;
      }
    });
  }

  /**
   * Broadcast transaction to network
   */
  async broadcastTransaction(txHex) {
    return await this.executeWithRetry(async () => {
      try {
        const response = await axios.post(`${this.apiBaseUrl}/tx`, txHex, {
          headers: { 'Content-Type': 'text/plain' }
        });
        
        logger.info(`Bitcoin transaction broadcasted: ${response.data}`);
        return response.data; // Transaction ID
      } catch (error) {
        logger.error(`Error broadcasting Bitcoin transaction: ${error.message}`);
        throw error;
      }
    });
  }

  /**
   * Get transaction details
   */
  async getTransaction(txId) {
    return await this.executeWithRetry(async () => {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/tx/${txId}`);
        const tx = response.data;

        return {
          txId: tx.txid,
          blockHeight: tx.status.block_height,
          blockTime: tx.status.block_time,
          confirmations: tx.status.confirmed ? 1 : 0,
          fee: tx.fee,
          size: tx.size,
          weight: tx.weight,
          inputs: tx.vin,
          outputs: tx.vout
        };
      } catch (error) {
        logger.error(`Error getting Bitcoin transaction: ${error.message}`);
        throw error;
      }
    });
  }

  /**
   * Validate Bitcoin address
   */
  validateAddress(address) {
    try {
      bitcoin.address.toOutputScript(address, this.network);
      return true;
    } catch (error) {
      return false;
    }
  }
}

module.exports = new BitcoinService();
