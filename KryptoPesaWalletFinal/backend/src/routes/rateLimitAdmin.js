const express = require('express');
const router = express.Router();
const rateLimitingService = require('../services/rateLimitingService');
const { requireAdmin } = require('../middleware/authMiddleware');
const logger = require('../utils/logger');

/**
 * Admin API for Rate Limiting Management
 * Requires admin authentication
 */

// Apply admin authentication to all routes
router.use(requireAdmin);

// Get rate limiting metrics and status
router.get('/metrics', async (req, res) => {
  try {
    const metrics = rateLimitingService.getMetrics();
    const healthStatus = await rateLimitingService.performHealthCheck();
    
    res.json({
      success: true,
      data: {
        metrics,
        health: healthStatus,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get rate limiting metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve rate limiting metrics',
      message: error.message
    });
  }
});

// Get rate limiting alerts
router.get('/alerts', async (req, res) => {
  try {
    const redisClient = rateLimitingService.redisClient;
    if (!redisClient || !redisClient.isReady) {
      return res.status(503).json({
        success: false,
        error: 'Redis not available',
        message: 'Cannot retrieve alerts without Redis connection'
      });
    }

    const alerts = await redisClient.lrange('rate_limit:alerts', 0, 49); // Get last 50 alerts
    const parsedAlerts = alerts.map(alert => {
      try {
        return JSON.parse(alert);
      } catch (e) {
        return { error: 'Failed to parse alert', raw: alert };
      }
    });

    res.json({
      success: true,
      data: {
        alerts: parsedAlerts,
        count: parsedAlerts.length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get rate limiting alerts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve rate limiting alerts',
      message: error.message
    });
  }
});

// Get IP status and rate limit information
router.get('/ip/:ip', async (req, res) => {
  try {
    const { ip } = req.params;
    
    // Validate IP format (basic validation)
    const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
    if (!ipRegex.test(ip)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid IP address format'
      });
    }

    const status = await rateLimitingService.getIPStatus(ip);
    const isWhitelisted = await rateLimitingService.isWhitelisted(ip);
    const isBlacklisted = await rateLimitingService.isBlacklisted(ip);

    res.json({
      success: true,
      data: {
        ip,
        status,
        whitelisted: isWhitelisted,
        blacklisted: isBlacklisted,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get IP status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve IP status',
      message: error.message
    });
  }
});

// Whitelist an IP address
router.post('/whitelist', async (req, res) => {
  try {
    const { ip, duration = 3600, reason } = req.body;
    
    if (!ip) {
      return res.status(400).json({
        success: false,
        error: 'IP address is required'
      });
    }

    // Validate IP format
    const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
    if (!ipRegex.test(ip)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid IP address format'
      });
    }

    const success = await rateLimitingService.whitelistIP(ip, duration);
    
    if (success) {
      // Log admin action
      logger.info('IP whitelisted by admin', {
        ip,
        duration,
        reason,
        adminId: req.user._id,
        adminUsername: req.user.username,
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        message: `IP ${ip} has been whitelisted for ${duration} seconds`,
        data: {
          ip,
          duration,
          expiresAt: new Date(Date.now() + duration * 1000).toISOString()
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to whitelist IP address'
      });
    }
  } catch (error) {
    logger.error('Failed to whitelist IP:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to whitelist IP address',
      message: error.message
    });
  }
});

// Blacklist an IP address
router.post('/blacklist', async (req, res) => {
  try {
    const { ip, duration = 24 * 60 * 60, reason } = req.body; // Default 24 hours
    
    if (!ip) {
      return res.status(400).json({
        success: false,
        error: 'IP address is required'
      });
    }

    // Validate IP format
    const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
    if (!ipRegex.test(ip)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid IP address format'
      });
    }

    const success = await rateLimitingService.blacklistIP(ip, duration);
    
    if (success) {
      // Log admin action
      logger.warn('IP blacklisted by admin', {
        ip,
        duration,
        reason,
        adminId: req.user._id,
        adminUsername: req.user.username,
        timestamp: new Date().toISOString(),
        severity: 'HIGH'
      });

      res.json({
        success: true,
        message: `IP ${ip} has been blacklisted for ${duration} seconds`,
        data: {
          ip,
          duration,
          expiresAt: new Date(Date.now() + duration * 1000).toISOString()
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to blacklist IP address'
      });
    }
  } catch (error) {
    logger.error('Failed to blacklist IP:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to blacklist IP address',
      message: error.message
    });
  }
});

// Remove IP from whitelist
router.delete('/whitelist/:ip', async (req, res) => {
  try {
    const { ip } = req.params;
    
    const redisClient = rateLimitingService.redisClient;
    if (!redisClient || !redisClient.isReady) {
      return res.status(503).json({
        success: false,
        error: 'Redis not available'
      });
    }

    const whitelistKey = `rate_limit:whitelist:${ip}`;
    const result = await redisClient.del(whitelistKey);
    
    // Log admin action
    logger.info('IP removed from whitelist by admin', {
      ip,
      adminId: req.user._id,
      adminUsername: req.user.username,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: `IP ${ip} removed from whitelist`,
      data: { ip, removed: result > 0 }
    });
  } catch (error) {
    logger.error('Failed to remove IP from whitelist:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to remove IP from whitelist',
      message: error.message
    });
  }
});

// Remove IP from blacklist
router.delete('/blacklist/:ip', async (req, res) => {
  try {
    const { ip } = req.params;
    
    const redisClient = rateLimitingService.redisClient;
    if (!redisClient || !redisClient.isReady) {
      return res.status(503).json({
        success: false,
        error: 'Redis not available'
      });
    }

    const blacklistKey = `rate_limit:blacklist:${ip}`;
    const result = await redisClient.del(blacklistKey);
    
    // Log admin action
    logger.info('IP removed from blacklist by admin', {
      ip,
      adminId: req.user._id,
      adminUsername: req.user.username,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: `IP ${ip} removed from blacklist`,
      data: { ip, removed: result > 0 }
    });
  } catch (error) {
    logger.error('Failed to remove IP from blacklist:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to remove IP from blacklist',
      message: error.message
    });
  }
});

// Reset rate limiting metrics
router.post('/reset-metrics', async (req, res) => {
  try {
    rateLimitingService.resetMetrics();
    
    // Log admin action
    logger.info('Rate limiting metrics reset by admin', {
      adminId: req.user._id,
      adminUsername: req.user.username,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Rate limiting metrics have been reset',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to reset metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset metrics',
      message: error.message
    });
  }
});

// Clear all rate limiting data (emergency use only)
router.post('/emergency-clear', async (req, res) => {
  try {
    const { confirm } = req.body;
    
    if (confirm !== 'EMERGENCY_CLEAR_ALL_RATE_LIMITS') {
      return res.status(400).json({
        success: false,
        error: 'Emergency confirmation required',
        message: 'Set confirm to "EMERGENCY_CLEAR_ALL_RATE_LIMITS" to proceed'
      });
    }

    const redisClient = rateLimitingService.redisClient;
    if (!redisClient || !redisClient.isReady) {
      return res.status(503).json({
        success: false,
        error: 'Redis not available'
      });
    }

    // Clear all rate limiting keys
    const patterns = ['rl:*', 'rate_limit:*', 'progressive:*'];
    let totalCleared = 0;

    for (const pattern of patterns) {
      const keys = await redisClient.keys(pattern);
      if (keys.length > 0) {
        const result = await redisClient.del(...keys);
        totalCleared += result;
      }
    }

    // Reset metrics
    rateLimitingService.resetMetrics();

    // Log emergency action
    logger.warn('EMERGENCY: All rate limiting data cleared by admin', {
      adminId: req.user._id,
      adminUsername: req.user.username,
      keysCleared: totalCleared,
      timestamp: new Date().toISOString(),
      severity: 'CRITICAL'
    });

    res.json({
      success: true,
      message: 'All rate limiting data has been cleared',
      data: {
        keysCleared: totalCleared,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to clear rate limiting data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear rate limiting data',
      message: error.message
    });
  }
});

module.exports = router;
