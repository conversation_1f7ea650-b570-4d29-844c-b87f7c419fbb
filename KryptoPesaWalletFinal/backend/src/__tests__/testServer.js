/**
 * Test Server Setup
 * Provides a server instance for testing without background timers
 */

// Set up test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long-for-testing';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-chars-long-for-testing';
process.env.SESSION_SECRET = 'test-session-secret-32-chars-long-for-testing';
process.env.MONGODB_URI = 'mongodb://localhost:27017/kryptopesa_test';
process.env.REDIS_URL = 'redis://localhost:6379';

// Mock cron to prevent background jobs
const originalCron = require('node-cron');
const mockCron = {
  schedule: () => ({
    start: () => {},
    stop: () => {},
    destroy: () => {}
  })
};

// Replace cron with mock before importing services
require.cache[require.resolve('node-cron')] = {
  exports: mockCron,
  loaded: true,
  id: require.resolve('node-cron')
};

// Mock setInterval for chat service
const originalSetInterval = global.setInterval;
const testIntervals = new Set();

global.setInterval = function(callback, delay) {
  const id = originalSetInterval(callback, delay);
  testIntervals.add(id);
  return id;
};

// Function to clean up test intervals
function cleanupTestIntervals() {
  testIntervals.forEach(id => {
    clearInterval(id);
  });
  testIntervals.clear();
}

// Import the server after mocking
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');

// Create Express app
const app = express();

// Basic middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Import routes (without background services)
const { router: authRoutes } = require('../routes/auth');
const walletRoutes = require('../routes/wallet');
const tradingRoutes = require('../routes/trading');

// Add routes
app.use('/api/auth', authRoutes);
app.use('/api/wallet', walletRoutes);
app.use('/api/trading', tradingRoutes);

// Enhanced error handler for tests
app.use((err, req, res, next) => {
  console.error('Test server error:', err);

  // Handle AppError properly
  if (err.statusCode && err.isOperational) {
    return res.status(err.statusCode).json({
      success: false,
      message: err.message,
      status: err.status
    });
  }

  // Handle validation errors
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: Object.values(err.errors).map(e => e.message)
    });
  }

  // Handle MongoDB duplicate key errors
  if (err.code === 11000) {
    return res.status(400).json({
      success: false,
      message: 'Duplicate entry',
      error: 'Resource already exists'
    });
  }

  // Default error
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', environment: 'test' });
});

module.exports = {
  app,
  cleanupTestIntervals
};
