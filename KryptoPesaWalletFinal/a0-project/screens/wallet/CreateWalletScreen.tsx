import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Clipboard,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

// Mock mnemonic generation - replace with actual crypto library
const generateMnemonic = () => {
  const words = [
    'abandon', 'ability', 'able', 'about', 'above', 'absent', 'absorb', 'abstract',
    'absurd', 'abuse', 'access', 'accident', 'account', 'accuse', 'achieve', 'acid',
    'acoustic', 'acquire', 'across', 'act', 'action', 'actor', 'actress', 'actual'
  ];
  
  const mnemonic = [];
  for (let i = 0; i < 24; i++) {
    mnemonic.push(words[Math.floor(Math.random() * words.length)]);
  }
  return mnemonic;
};

export default function CreateWalletScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [mnemonic, setMnemonic] = useState<string[]>([]);
  const [isRevealed, setIsRevealed] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  useEffect(() => {
    // Generate mnemonic on component mount
    setMnemonic(generateMnemonic());
  }, []);

  const handleRevealPhrase = () => {
    Alert.alert(
      'Security Warning',
      'Make sure no one is watching your screen. Your recovery phrase is the only way to restore your wallet.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'I Understand', onPress: () => setIsRevealed(true) }
      ]
    );
  };

  const handleCopyPhrase = async () => {
    const phraseString = mnemonic.join(' ');
    await Clipboard.setString(phraseString);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  const handleContinue = () => {
    if (!isRevealed) {
      Alert.alert('Error', 'Please reveal and secure your recovery phrase first.');
      return;
    }
    navigation.navigate('BackupVerification', { mnemonic });
  };

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Wallet</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.stepIndicator}>
          <View style={[styles.step, styles.activeStep]}>
            <Text style={styles.stepNumber}>1</Text>
          </View>
          <View style={styles.stepLine} />
          <View style={styles.step}>
            <Text style={styles.stepNumber}>2</Text>
          </View>
          <View style={styles.stepLine} />
          <View style={styles.step}>
            <Text style={styles.stepNumber}>3</Text>
          </View>
        </View>

        <View style={styles.titleContainer}>
          <Ionicons name="shield-checkmark" size={48} color={theme.colors.primary} />
          <Text style={styles.title}>Secure Your Wallet</Text>
          <Text style={styles.subtitle}>
            Your recovery phrase is the master key to your wallet. Keep it safe and never share it.
          </Text>
        </View>

        <View style={styles.warningBox}>
          <Ionicons name="warning" size={24} color={theme.colors.warning} />
          <View style={styles.warningContent}>
            <Text style={styles.warningTitle}>Important Security Tips:</Text>
            <Text style={styles.warningText}>• Write down your phrase on paper</Text>
            <Text style={styles.warningText}>• Store it in a secure location</Text>
            <Text style={styles.warningText}>• Never share it with anyone</Text>
            <Text style={styles.warningText}>• KryptoPesa cannot recover lost phrases</Text>
          </View>
        </View>

        <View style={styles.phraseContainer}>
          {!isRevealed ? (
            <TouchableOpacity style={styles.revealButton} onPress={handleRevealPhrase}>
              <Ionicons name="eye-off" size={24} color={theme.colors.primary} />
              <Text style={styles.revealText}>Tap to reveal your recovery phrase</Text>
            </TouchableOpacity>
          ) : (
            <>
              <View style={styles.phraseGrid}>
                {mnemonic.map((word, index) => (
                  <View key={index} style={styles.wordContainer}>
                    <Text style={styles.wordNumber}>{index + 1}</Text>
                    <Text style={styles.word}>{word}</Text>
                  </View>
                ))}
              </View>
              
              <TouchableOpacity style={styles.copyButton} onPress={handleCopyPhrase}>
                <Ionicons 
                  name={isCopied ? "checkmark" : "copy"} 
                  size={20} 
                  color={theme.colors.primary} 
                />
                <Text style={styles.copyText}>
                  {isCopied ? 'Copied!' : 'Copy to clipboard'}
                </Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.continueButton, !isRevealed && styles.disabledButton]}
          onPress={handleContinue}
          disabled={!isRevealed}
        >
          <Text style={styles.continueButtonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
  },
  step: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeStep: {
    backgroundColor: theme.colors.primary,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  stepLine: {
    width: 40,
    height: 2,
    backgroundColor: theme.colors.border,
    marginHorizontal: 8,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  warningBox: {
    flexDirection: 'row',
    backgroundColor: theme.colors.warningLight,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  warningContent: {
    flex: 1,
    marginLeft: 12,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  warningText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  phraseContainer: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
  },
  revealButton: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  revealText: {
    fontSize: 16,
    color: theme.colors.primary,
    marginTop: 12,
    fontWeight: '500',
  },
  phraseGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  wordContainer: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  wordNumber: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginRight: 8,
    minWidth: 20,
  },
  word: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    padding: 12,
  },
  copyText: {
    fontSize: 16,
    color: theme.colors.primary,
    marginLeft: 8,
    fontWeight: '500',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  continueButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: theme.colors.disabled,
  },
  continueButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
});
