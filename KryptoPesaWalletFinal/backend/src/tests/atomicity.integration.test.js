/**
 * Atomicity and Consistency Integration Tests
 * Comprehensive tests to verify all multi-document operations are atomic
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Trade = require('../models/Trade');
const Chat = require('../models/Chat');
const Offer = require('../models/Offer');
const Dispute = require('../models/Dispute');
const Wallet = require('../models/Wallet');
const User = require('../models/User');

// Services to test
const escrowService = require('../services/escrowService');
const walletService = require('../services/walletService');
const tradingService = require('../services/tradingService');
const { dataConsistencyService } = require('../services/dataConsistency');

describe('Atomicity and Consistency Integration Tests', () => {
  let mongoServer;
  let testUser1, testUser2;
  let testOffer;

  beforeAll(async () => {
    // Start in-memory MongoDB instance with replica set for transactions
    mongoServer = await MongoMemoryServer.create({
      instance: {
        replSet: 'rs0'
      }
    });
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Initialize replica set
    try {
      await mongoose.connection.db.admin().command({
        replSetInitiate: {
          _id: 'rs0',
          members: [{ _id: 0, host: 'localhost:27017' }]
        }
      });
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for replica set
    } catch (error) {
      // Replica set might already be initialized
    }

    // Create test users
    testUser1 = await User.create({
      email: '<EMAIL>',
      username: 'seller',
      password: 'password123',
      firstName: 'Test',
      lastName: 'Seller',
      isVerified: true
    });

    testUser2 = await User.create({
      email: '<EMAIL>',
      username: 'buyer',
      password: 'password123',
      firstName: 'Test',
      lastName: 'Buyer',
      isVerified: true
    });

    // Create test wallets
    await Wallet.create({
      user: testUser1._id,
      balances: [{
        symbol: 'USDT',
        network: 'polygon',
        balance: '1000',
        lastUpdated: new Date()
      }],
      addresses: {
        ethereum: {
          address: '0x123...seller',
          publicKey: 'seller_public_key'
        },
        bitcoin: {
          address: 'bc1seller123',
          publicKey: 'seller_btc_public_key'
        }
      }
    });

    await Wallet.create({
      user: testUser2._id,
      balances: [{
        symbol: 'USDT',
        network: 'polygon',
        balance: '0',
        lastUpdated: new Date()
      }],
      addresses: {
        ethereum: {
          address: '0x456...buyer',
          publicKey: 'buyer_public_key'
        },
        bitcoin: {
          address: 'bc1buyer456',
          publicKey: 'buyer_btc_public_key'
        }
      }
    });
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean up collections before each test
    await Trade.deleteMany({});
    await Chat.deleteMany({});
    await Dispute.deleteMany({});
    await Offer.deleteMany({});

    // Create fresh test offer
    testOffer = await Offer.create({
      user: testUser1._id,
      type: 'sell',
      cryptocurrency: {
        symbol: 'USDT',
        network: 'polygon',
        amount: '1000',
        availableAmount: '1000'
      },
      fiatCurrency: {
        currency: 'KES',
        amount: '130000',
        paymentMethods: ['M-Pesa']
      },
      terms: 'Test offer terms',
      status: 'active'
    });
  });

  describe('Trade Creation Atomicity', () => {
    test('should create trade, chat, and update offer atomically', async () => {
      const initialOfferAmount = parseFloat(testOffer.cryptocurrency.availableAmount);
      
      const tradeData = {
        amount: 13000,
        paymentMethod: 'M-Pesa'
      };

      const result = await tradingService.acceptOffer(testOffer._id, testUser2._id, tradeData);

      // Verify all operations completed atomically
      expect(result).toBeDefined();
      expect(result.chat).toBeDefined();

      // Verify offer was updated
      const updatedOffer = await Offer.findById(testOffer._id);
      const newAmount = parseFloat(updatedOffer.cryptocurrency.availableAmount);
      expect(newAmount).toBeLessThan(initialOfferAmount);

      // Verify chat exists and is linked to trade
      const chat = await Chat.findById(result.chat);
      expect(chat).toBeDefined();
      expect(chat.trade.toString()).toBe(result._id.toString());
    });

    test('should rollback all changes if offer update fails', async () => {
      // Create offer with insufficient amount
      const smallOffer = await Offer.create({
        user: testUser1._id,
        type: 'sell',
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '10',
          availableAmount: '10'
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '1300',
          paymentMethods: ['M-Pesa']
        },
        terms: 'Small offer',
        status: 'active'
      });

      const tradeData = {
        amount: 2600, // Request more than available
        paymentMethod: 'M-Pesa'
      };

      await expect(
        tradingService.acceptOffer(smallOffer._id, testUser2._id, tradeData)
      ).rejects.toThrow();

      // Verify no trade was created
      const trades = await Trade.find({});
      expect(trades).toHaveLength(0);

      // Verify no chat was created
      const chats = await Chat.find({});
      expect(chats).toHaveLength(0);

      // Verify offer was not modified
      const unchangedOffer = await Offer.findById(smallOffer._id);
      expect(parseFloat(unchangedOffer.cryptocurrency.availableAmount)).toBe(10);
    });
  });

  describe('Escrow Operations Atomicity', () => {
    let testTrade;

    beforeEach(async () => {
      testTrade = await Trade.create({
        seller: testUser1._id,
        buyer: testUser2._id,
        offer: testOffer._id,
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '100'
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '13000'
        },
        status: 'created'
      });
    });

    test('should create escrow and update trade atomically', async () => {
      // Mock blockchain operations
      escrowService.createEthereumEscrow = jest.fn().mockResolvedValue({
        escrowId: 'test_escrow_123',
        contractAddress: '0x123...abc',
        txHash: '0xabc...123'
      });
      escrowService.monitorEscrow = jest.fn();

      const result = await escrowService.createEscrow(testTrade._id, '100', 'USDT', 'polygon');

      expect(result.success).toBe(true);

      // Verify trade was updated atomically
      const updatedTrade = await Trade.findById(testTrade._id);
      expect(updatedTrade.status).toBe('escrowed');
      expect(updatedTrade.escrow.escrowId).toBe('test_escrow_123');
      expect(updatedTrade.escrow.status).toBe('active');
    });

    test('should release escrow and complete trade atomically', async () => {
      // Set up trade with active escrow
      testTrade.status = 'escrowed';
      testTrade.escrow = {
        escrowId: 'test_escrow_123',
        contractAddress: '0x123...abc',
        txHash: '0xabc...123',
        amount: '100',
        cryptocurrency: 'USDT',
        network: 'polygon',
        status: 'active',
        createdAt: new Date()
      };
      await testTrade.save();

      // Mock blockchain operations
      escrowService.releaseEthereumEscrow = jest.fn().mockResolvedValue({
        txHash: '0xrelease...123'
      });
      escrowService.processPlatformCommission = jest.fn();
      escrowService.stopEscrowMonitoring = jest.fn();

      const result = await escrowService.releaseEscrow(testTrade._id, testUser2._id);

      expect(result.success).toBe(true);

      // Verify trade was updated atomically
      const updatedTrade = await Trade.findById(testTrade._id);
      expect(updatedTrade.status).toBe('completed');
      expect(updatedTrade.escrow.status).toBe('released');
      expect(updatedTrade.escrow.releasedBy.toString()).toBe(testUser2._id.toString());
      expect(updatedTrade.completedAt).toBeDefined();
    });
  });

  describe('Dispute Creation Atomicity', () => {
    let testTrade;

    beforeEach(async () => {
      testTrade = await Trade.create({
        seller: testUser1._id,
        buyer: testUser2._id,
        offer: testOffer._id,
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '100'
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '13000'
        },
        status: 'funded'
      });
    });

    test('should create dispute and update trade atomically', async () => {
      const disputeData = {
        initiator: testUser2._id,
        category: 'payment_not_received',
        description: 'Payment was not received',
        evidence: []
      };

      const result = await dataConsistencyService.executeAtomicDisputeCreation(
        testTrade._id,
        disputeData,
        testUser2._id
      );

      // Verify dispute was created
      expect(result.dispute).toBeDefined();
      expect(result.dispute.category).toBe('payment_not_received');

      // Verify trade was updated atomically
      expect(result.trade.status).toBe('disputed');
      expect(result.trade.dispute.toString()).toBe(result.dispute._id.toString());

      // Verify timeline entries were added
      const disputeTimelineEntry = result.dispute.timeline.find(entry => entry.action === 'created');
      expect(disputeTimelineEntry).toBeDefined();

      const tradeTimelineEntry = result.trade.timeline.find(entry => entry.status === 'disputed');
      expect(tradeTimelineEntry).toBeDefined();
    });
  });

  describe('Wallet Balance Updates Atomicity', () => {
    test('should update multiple balances atomically', async () => {
      // Mock blockchain service responses
      const ethereumService = require('../services/blockchain/ethereumService');
      const bitcoinService = require('../services/blockchain/bitcoinService');

      ethereumService.getNativeBalance = jest.fn()
        .mockResolvedValueOnce({ balance: '1.5' }) // MATIC
        .mockResolvedValueOnce({ balance: '2.0' }); // ETH
      
      ethereumService.getTokenBalance = jest.fn()
        .mockResolvedValueOnce({ balance: '1000' }) // USDT
        .mockResolvedValueOnce({ balance: '500' }); // USDC

      bitcoinService.getBalance = jest.fn().mockResolvedValue({ balance: '0.05' });

      // Set environment variables for token contracts
      process.env.USDT_CONTRACT_ADDRESS = '0xusdt...contract';
      process.env.USDC_CONTRACT_ADDRESS = '0xusdc...contract';

      const result = await walletService.updateBalances(testUser1._id);

      expect(result).toBeDefined();
      expect(result.balances.length).toBeGreaterThan(0);

      // Verify all balances were updated atomically
      const wallet = await Wallet.findOne({ user: testUser1._id });
      const maticBalance = wallet.getBalance('MATIC', 'polygon');
      const ethBalance = wallet.getBalance('ETH', 'ethereum');
      const usdtBalance = wallet.getBalance('USDT', 'polygon');
      const btcBalance = wallet.getBalance('BTC', 'bitcoin');

      expect(maticBalance).toBe('1.5');
      expect(ethBalance).toBe('2.0');
      expect(usdtBalance).toBe('1000');
      expect(btcBalance).toBe('0.05');
    });
  });

  describe('Concurrent Operations Safety', () => {
    test('should handle concurrent atomic operations without data corruption', async () => {
      const concurrentOperations = [];

      // Create multiple concurrent trade creation operations
      for (let i = 0; i < 3; i++) {
        const tradeData = {
          amount: 1000, // Each trade takes 1000 KES worth
          paymentMethod: 'M-Pesa'
        };

        concurrentOperations.push(
          tradingService.acceptOffer(testOffer._id, testUser2._id, tradeData)
        );
      }

      // Execute all operations concurrently
      const results = await Promise.allSettled(concurrentOperations);

      // Count successful and failed operations
      const successful = results.filter(r => r.status === 'fulfilled');
      const failed = results.filter(r => r.status === 'rejected');

      // At least one should succeed, others might fail due to insufficient offer amount
      expect(successful.length).toBeGreaterThan(0);
      expect(successful.length + failed.length).toBe(3);

      // Verify offer consistency - available amount should never go negative
      const finalOffer = await Offer.findById(testOffer._id);
      expect(parseFloat(finalOffer.cryptocurrency.availableAmount)).toBeGreaterThanOrEqual(0);

      // Verify no orphaned chats or trades
      const trades = await Trade.find({});
      const chats = await Chat.find({});
      
      expect(trades.length).toBe(successful.length);
      expect(chats.length).toBe(successful.length);

      // Verify all trades have corresponding chats
      for (const trade of trades) {
        expect(trade.chat).toBeDefined();
        const chat = await Chat.findById(trade.chat);
        expect(chat).toBeDefined();
        expect(chat.trade.toString()).toBe(trade._id.toString());
      }
    });
  });
});
