/**
 * Data Consistency & Transaction Monitoring Routes
 * Admin-only endpoints for monitoring data consistency and transaction state
 */

const express = require('express');
const router = express.Router();
const { authenticate, authorize } = require('../middleware/authMiddleware');
const { dataConsistencyService } = require('../services/dataConsistency');
const { transactionMonitoringService, TransactionState } = require('../services/transactionMonitoring');
const logger = require('../utils/logger');

// All routes require admin authentication
router.use(authenticate);
router.use(authorize('admin'));

/**
 * Get data consistency metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const consistencyMetrics = dataConsistencyService.getConsistencyMetrics();
    const monitoringStats = await transactionMonitoringService.getMonitoringStats();
    
    res.json({
      success: true,
      data: {
        consistency: consistencyMetrics,
        monitoring: monitoringStats,
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }
    });
  } catch (error) {
    logger.error('Error getting data consistency metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get data consistency metrics',
      error: error.message
    });
  }
});

/**
 * Get transaction monitoring statistics
 */
router.get('/transactions/stats', async (req, res) => {
  try {
    const stats = await transactionMonitoringService.getMonitoringStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Error getting transaction monitoring stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transaction monitoring statistics',
      error: error.message
    });
  }
});

/**
 * Get pending transactions
 */
router.get('/transactions/pending', async (req, res) => {
  try {
    const { limit = 50, page = 1 } = req.query;
    const skip = (page - 1) * limit;
    
    const pendingTransactions = await TransactionState.find({
      status: { $in: ['pending', 'in_progress'] }
    })
    .populate('userId', 'email username')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));
    
    const total = await TransactionState.countDocuments({
      status: { $in: ['pending', 'in_progress'] }
    });
    
    res.json({
      success: true,
      data: {
        transactions: pendingTransactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting pending transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get pending transactions',
      error: error.message
    });
  }
});

/**
 * Get failed transactions
 */
router.get('/transactions/failed', async (req, res) => {
  try {
    const { limit = 50, page = 1, hours = 24 } = req.query;
    const skip = (page - 1) * limit;
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    const failedTransactions = await TransactionState.find({
      status: 'failed',
      createdAt: { $gte: since }
    })
    .populate('userId', 'email username')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));
    
    const total = await TransactionState.countDocuments({
      status: 'failed',
      createdAt: { $gte: since }
    });
    
    res.json({
      success: true,
      data: {
        transactions: failedTransactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        },
        timeRange: `Last ${hours} hours`
      }
    });
  } catch (error) {
    logger.error('Error getting failed transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get failed transactions',
      error: error.message
    });
  }
});

/**
 * Get specific transaction state
 */
router.get('/transactions/:transactionId', async (req, res) => {
  try {
    const { transactionId } = req.params;
    
    const transactionState = await transactionMonitoringService.getTransactionState(transactionId);
    
    if (!transactionState) {
      return res.status(404).json({
        success: false,
        message: 'Transaction state not found'
      });
    }
    
    res.json({
      success: true,
      data: transactionState
    });
  } catch (error) {
    logger.error('Error getting transaction state:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transaction state',
      error: error.message
    });
  }
});

/**
 * Retry a failed transaction
 */
router.post('/transactions/:transactionId/retry', async (req, res) => {
  try {
    const { transactionId } = req.params;
    
    const transactionState = await TransactionState.findOne({ transactionId });
    
    if (!transactionState) {
      return res.status(404).json({
        success: false,
        message: 'Transaction state not found'
      });
    }
    
    if (!['failed', 'timeout'].includes(transactionState.status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot retry transaction in status: ${transactionState.status}`
      });
    }
    
    const canRetry = await transactionMonitoringService.scheduleRetry(transactionId);
    
    if (!canRetry) {
      return res.status(400).json({
        success: false,
        message: 'Maximum retry attempts exceeded'
      });
    }
    
    res.json({
      success: true,
      message: 'Transaction retry scheduled successfully'
    });
  } catch (error) {
    logger.error('Error retrying transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retry transaction',
      error: error.message
    });
  }
});

/**
 * Reset data consistency metrics
 */
router.post('/metrics/reset', async (req, res) => {
  try {
    dataConsistencyService.resetMetrics();
    
    res.json({
      success: true,
      message: 'Data consistency metrics reset successfully'
    });
  } catch (error) {
    logger.error('Error resetting metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset metrics',
      error: error.message
    });
  }
});

/**
 * Cleanup old transaction records
 */
router.post('/transactions/cleanup', async (req, res) => {
  try {
    const { olderThanDays = 7 } = req.body;
    
    const deletedCount = await transactionMonitoringService.cleanupOldTransactions(olderThanDays);
    
    res.json({
      success: true,
      message: `Cleaned up ${deletedCount} old transaction records`,
      data: { deletedCount, olderThanDays }
    });
  } catch (error) {
    logger.error('Error cleaning up transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup old transactions',
      error: error.message
    });
  }
});

/**
 * Get transaction history for a specific user
 */
router.get('/users/:userId/transactions', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 20, page = 1, status } = req.query;
    const skip = (page - 1) * limit;
    
    const query = { userId };
    if (status) {
      query.status = status;
    }
    
    const transactions = await TransactionState.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await TransactionState.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting user transaction history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user transaction history',
      error: error.message
    });
  }
});

/**
 * Health check for data consistency services
 */
router.get('/health', async (req, res) => {
  try {
    const consistencyMetrics = dataConsistencyService.getConsistencyMetrics();
    const monitoringStats = await transactionMonitoringService.getMonitoringStats();
    
    const health = {
      dataConsistency: {
        status: 'healthy',
        activeTransactions: consistencyMetrics.activeTransactions,
        successRate: consistencyMetrics.successRate
      },
      transactionMonitoring: {
        status: monitoringStats.isMonitoring ? 'healthy' : 'stopped',
        pendingTransactions: monitoringStats.statusDistribution?.pending || 0,
        recentFailures: monitoringStats.recentFailures
      },
      overall: 'healthy'
    };
    
    // Determine overall health
    if (consistencyMetrics.successRate < 95 || monitoringStats.recentFailures > 10) {
      health.overall = 'degraded';
    }
    
    if (!monitoringStats.isMonitoring || consistencyMetrics.successRate < 90) {
      health.overall = 'unhealthy';
    }
    
    const statusCode = health.overall === 'healthy' ? 200 : 
                      health.overall === 'degraded' ? 200 : 503;
    
    res.status(statusCode).json({
      success: true,
      data: health
    });
  } catch (error) {
    logger.error('Error checking data consistency health:', error);
    res.status(503).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

module.exports = router;
