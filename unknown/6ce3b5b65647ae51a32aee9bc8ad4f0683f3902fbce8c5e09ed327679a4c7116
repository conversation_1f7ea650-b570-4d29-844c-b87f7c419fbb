# KryptoPesa Production Environment Configuration
# DO NOT COMMIT THIS FILE TO VERSION CONTROL

# Environment
NODE_ENV=production

# Server Configuration
PORT=3000
HOST=0.0.0.0

# Database Configuration
MONGODB_URI=mongodb://production-mongodb-cluster:27017/kryptopesa
DB_PASSWORD=kryptopesa_production_db_password_2025_secure
DB_USER=kryptopesa_user
DB_NAME=kryptopesa

# Redis Configuration
REDIS_URL=redis://production-redis-cluster:6379
REDIS_PASSWORD=kryptopesa_production_redis_password_2025_secure

# JWT Configuration
JWT_SECRET=kryptopesa_production_jwt_secret_key_2025_very_secure_32_chars_minimum
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Encryption
ENCRYPTION_KEY=kryptopesa_production_encryption_key_2025_very_secure_32_chars_minimum
ENCRYPTION_ALGORITHM=aes-256-gcm

# API Configuration
API_BASE_URL=https://api.kryptopesa.com
WS_URL=wss://api.kryptopesa.com/ws
ADMIN_URL=https://admin.kryptopesa.com

# CORS Origins (comma-separated)
CORS_ORIGINS=https://app.kryptopesa.com,https://admin.kryptopesa.com,https://kryptopesa.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_AUTH_MAX=10

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=kryptopesa_production_session_secret_2025_very_secure_32_chars_minimum
CSRF_SECRET=kryptopesa_production_csrf_secret_2025_very_secure_32_chars_minimum

# Email Configuration
SMTP_HOST=smtp.production-email-service.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=CHANGE_ME_IN_PRODUCTION
EMAIL_FROM=<EMAIL>

# SMS Configuration
SMS_SERVICE=twilio
SMS_ACCOUNT_SID=CHANGE_ME_IN_PRODUCTION
SMS_AUTH_TOKEN=CHANGE_ME_IN_PRODUCTION
SMS_FROM=+**********

# Push Notifications
FCM_SERVER_KEY=CHANGE_ME_IN_PRODUCTION
VAPID_PUBLIC_KEY=CHANGE_ME_IN_PRODUCTION
VAPID_PRIVATE_KEY=CHANGE_ME_IN_PRODUCTION

# AWS Configuration
AWS_ACCESS_KEY_ID=CHANGE_ME_IN_PRODUCTION
AWS_SECRET_ACCESS_KEY=CHANGE_ME_IN_PRODUCTION
AWS_REGION=us-east-1
AWS_S3_BUCKET=kryptopesa-production-assets

# Blockchain Configuration
BITCOIN_NETWORK=mainnet
ETHEREUM_NETWORK=mainnet
BITCOIN_RPC_URL=https://production-bitcoin-node:8332
ETHEREUM_RPC_URL=https://production-ethereum-node:8545

# Monitoring & Logging
LOG_LEVEL=info
SENTRY_DSN=CHANGE_ME_IN_PRODUCTION
DATADOG_API_KEY=CHANGE_ME_IN_PRODUCTION

# Backup Configuration
BACKUP_ENCRYPTION_PASSWORD=CHANGE_ME_IN_PRODUCTION
BACKUP_S3_BUCKET=kryptopesa-production-backups
BACKUP_RETENTION_DAYS=30

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/kryptopesa.crt
SSL_KEY_PATH=/etc/ssl/private/kryptopesa.key

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_TRADING=true
ENABLE_WITHDRAWALS=true
ENABLE_DEPOSITS=true
MAINTENANCE_MODE=false

# Performance
MAX_REQUEST_SIZE=10mb
REQUEST_TIMEOUT=30000
DB_CONNECTION_POOL_SIZE=50
REDIS_CONNECTION_POOL_SIZE=20

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_ALLOWED_IPS=127.0.0.1,10.0.0.0/8,**********/12,***********/16

# Alerting
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
SLACK_WEBHOOK_URL=CHANGE_ME_IN_PRODUCTION
DISCORD_WEBHOOK_URL=CHANGE_ME_IN_PRODUCTION

# Business Configuration
PLATFORM_COMMISSION_RATE=0.01
MIN_TRADE_AMOUNT=10
MAX_TRADE_AMOUNT=100000
ESCROW_TIMEOUT_HOURS=24
DISPUTE_TIMEOUT_HOURS=72
