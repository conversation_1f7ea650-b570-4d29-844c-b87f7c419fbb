/**
 * Real-time System Health Monitor Component
 * Displays live system metrics and health status
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  Tooltip,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Memory,
  Storage,
  NetworkCheck,
  Speed,
  Security,
  Warning,
  CheckCircle,
  Error,
  ExpandMore,
  ExpandLess,
  Refresh
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import websocketService from '../services/websocketService';

const SystemHealthMonitor = () => {
  const [expanded, setExpanded] = useState(false);
  const [historicalData, setHistoricalData] = useState([]);
  const [lastUpdate, setLastUpdate] = useState(null);
  
  // Get system health from Redux store
  const systemHealth = useSelector(state => state.admin.systemHealth);
  const isConnected = useSelector(state => state.admin.websocketConnected);

  useEffect(() => {
    // Subscribe to system health updates
    websocketService.subscribe('admin:system_health');
    
    // Request initial health data
    websocketService.send('admin:request_health', {});
    
    return () => {
      websocketService.unsubscribe('admin:system_health');
    };
  }, []);

  useEffect(() => {
    if (systemHealth) {
      setLastUpdate(new Date());
      
      // Add to historical data (keep last 20 points)
      setHistoricalData(prev => {
        const newData = [...prev, {
          timestamp: new Date().toLocaleTimeString(),
          cpu: systemHealth.cpu?.usage || 0,
          memory: systemHealth.memory?.usage || 0,
          disk: systemHealth.disk?.usage || 0
        }];
        
        return newData.slice(-20);
      });
    }
  }, [systemHealth]);

  const getHealthStatus = () => {
    if (!systemHealth) return { status: 'unknown', color: 'default', icon: <Warning /> };
    
    const { overall } = systemHealth;
    
    if (overall >= 90) {
      return { status: 'excellent', color: 'success', icon: <CheckCircle /> };
    } else if (overall >= 70) {
      return { status: 'good', color: 'info', icon: <CheckCircle /> };
    } else if (overall >= 50) {
      return { status: 'warning', color: 'warning', icon: <Warning /> };
    } else {
      return { status: 'critical', color: 'error', icon: <Error /> };
    }
  };

  const getUsageColor = (usage) => {
    if (usage >= 90) return 'error';
    if (usage >= 70) return 'warning';
    return 'primary';
  };

  const handleRefresh = () => {
    websocketService.send('admin:request_health', {});
  };

  const healthStatus = getHealthStatus();

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="h2">
            System Health Monitor
          </Typography>
          <Box display="flex" alignItems="center" gap={1}>
            <Chip
              icon={healthStatus.icon}
              label={`${systemHealth?.overall || 0}% Healthy`}
              color={healthStatus.color}
              size="small"
            />
            <Chip
              icon={<NetworkCheck />}
              label={isConnected ? 'Connected' : 'Disconnected'}
              color={isConnected ? 'success' : 'error'}
              size="small"
              variant="outlined"
            />
            <Tooltip title="Refresh">
              <IconButton size="small" onClick={handleRefresh}>
                <Refresh />
              </IconButton>
            </Tooltip>
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
          </Box>
        </Box>

        {lastUpdate && (
          <Typography variant="caption" color="text.secondary" display="block" mb={2}>
            Last updated: {lastUpdate.toLocaleTimeString()}
          </Typography>
        )}

        {/* Main Health Metrics */}
        <Grid container spacing={2} mb={2}>
          <Grid item xs={12} sm={4}>
            <Box>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Memory color="primary" />
                <Typography variant="subtitle2">CPU Usage</Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={systemHealth?.cpu?.usage || 0}
                color={getUsageColor(systemHealth?.cpu?.usage || 0)}
                sx={{ height: 8, borderRadius: 4 }}
              />
              <Typography variant="caption" color="text.secondary">
                {systemHealth?.cpu?.usage || 0}% ({systemHealth?.cpu?.cores || 0} cores)
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Box>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Speed color="primary" />
                <Typography variant="subtitle2">Memory Usage</Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={systemHealth?.memory?.usage || 0}
                color={getUsageColor(systemHealth?.memory?.usage || 0)}
                sx={{ height: 8, borderRadius: 4 }}
              />
              <Typography variant="caption" color="text.secondary">
                {systemHealth?.memory?.used || 0}GB / {systemHealth?.memory?.total || 0}GB
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Box>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Storage color="primary" />
                <Typography variant="subtitle2">Disk Usage</Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={systemHealth?.disk?.usage || 0}
                color={getUsageColor(systemHealth?.disk?.usage || 0)}
                sx={{ height: 8, borderRadius: 4 }}
              />
              <Typography variant="caption" color="text.secondary">
                {systemHealth?.disk?.used || 0}GB / {systemHealth?.disk?.total || 0}GB
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Alerts */}
        {systemHealth?.alerts && systemHealth.alerts.length > 0 && (
          <Box mb={2}>
            {systemHealth.alerts.map((alert, index) => (
              <Alert
                key={index}
                severity={alert.severity || 'warning'}
                sx={{ mb: 1 }}
              >
                {alert.message}
              </Alert>
            ))}
          </Box>
        )}

        {/* Expanded Details */}
        <Collapse in={expanded}>
          <Grid container spacing={2}>
            {/* Historical Chart */}
            <Grid item xs={12} md={8}>
              <Typography variant="subtitle2" gutterBottom>
                Resource Usage Trend
              </Typography>
              <Box height={200}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={historicalData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis domain={[0, 100]} />
                    <RechartsTooltip />
                    <Line
                      type="monotone"
                      dataKey="cpu"
                      stroke="#1976d2"
                      strokeWidth={2}
                      name="CPU %"
                    />
                    <Line
                      type="monotone"
                      dataKey="memory"
                      stroke="#ed6c02"
                      strokeWidth={2}
                      name="Memory %"
                    />
                    <Line
                      type="monotone"
                      dataKey="disk"
                      stroke="#2e7d32"
                      strokeWidth={2}
                      name="Disk %"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </Grid>

            {/* Service Status */}
            <Grid item xs={12} md={4}>
              <Typography variant="subtitle2" gutterBottom>
                Service Status
              </Typography>
              <List dense>
                {systemHealth?.services?.map((service, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {service.status === 'healthy' ? (
                        <CheckCircle color="success" />
                      ) : service.status === 'warning' ? (
                        <Warning color="warning" />
                      ) : (
                        <Error color="error" />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={service.name}
                      secondary={`${service.status} - ${service.responseTime || 0}ms`}
                    />
                  </ListItem>
                )) || []}
              </List>
            </Grid>

            {/* Network Status */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Network & Database
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {systemHealth?.network?.latency || 0}
                    </Typography>
                    <Typography variant="caption">Network Latency (ms)</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {systemHealth?.database?.connections || 0}
                    </Typography>
                    <Typography variant="caption">DB Connections</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {systemHealth?.api?.requestsPerMinute || 0}
                    </Typography>
                    <Typography variant="caption">Requests/min</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {systemHealth?.api?.errorRate || 0}%
                    </Typography>
                    <Typography variant="caption">Error Rate</Typography>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default SystemHealthMonitor;
