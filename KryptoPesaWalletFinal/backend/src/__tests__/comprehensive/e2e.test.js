/**
 * Comprehensive End-to-End Testing
 * Complete user journey tests including edge cases
 */

// Set up test environment variables before importing server
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long-for-testing';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-chars-long-for-testing';
process.env.SESSION_SECRET = 'test-session-secret-32-chars-long-for-testing';
process.env.MONGODB_URI = 'mongodb://localhost:27017/kryptopesa_test';
process.env.REDIS_URL = 'redis://localhost:6379';

const testEnvironment = require('../testEnvironment');
const request = require('supertest');
const { app, cleanupTestIntervals } = require('../testServer');

describe('Comprehensive E2E Tests', () => {
  beforeAll(async () => {
    await testEnvironment.setup();
  }, 60000);

  beforeEach(async () => {
    await testEnvironment.cleanDatabase();
  });

  afterEach(async () => {
    await testEnvironment.cleanup();
  });

  afterAll(async () => {
    cleanupTestIntervals();
    await testEnvironment.teardown();
  });

  describe('Complete User Onboarding Journey', () => {
    test('should complete full user registration and setup', async () => {
      // Step 1: User registration
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'newuser',
          phone: '+254700000001',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'New',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });


      expect(registerResponse.status).toBe(201);
      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.user.email).toBe('<EMAIL>');
      expect(registerResponse.body.data.accessToken).toBeDefined();

      const authToken = registerResponse.body.data.accessToken;
      const userId = registerResponse.body.data.user._id;

      // Step 2: Email verification (simulated)
      const verifyResponse = await request(app)
        .post('/api/auth/verify-email')
        .send({
          userId: userId,
          verificationCode: '123456' // Mock code
        });

      expect(verifyResponse.status).toBe(200);
      expect(verifyResponse.body.success).toBe(true);

      // Step 3: Create wallet
      const walletResponse = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`);

      expect(walletResponse.status).toBe(201);
      expect(walletResponse.body.success).toBe(true);
      expect(walletResponse.body.wallet.address).toBeDefined();
      expect(walletResponse.body.wallet.mnemonic).toBeDefined();

      // Step 4: Setup security (2FA simulation)
      const securityResponse = await request(app)
        .post('/api/security/setup-2fa')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          method: 'sms',
          phoneNumber: '+254700000000'
        });

      expect(securityResponse.status).toBe(200);
      expect(securityResponse.body.success).toBe(true);

      // Step 5: Complete profile
      const profileResponse = await request(app)
        .patch('/api/user/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          firstName: 'John',
          lastName: 'Doe',
          location: 'Nairobi',
          phoneNumber: '+254700000000'
        });

      expect(profileResponse.status).toBe(200);
      expect(profileResponse.body.success).toBe(true);

      // Step 6: Verify complete setup
      const finalProfileResponse = await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${authToken}`);

      expect(finalProfileResponse.status).toBe(200);
      expect(finalProfileResponse.body.user.isVerified).toBe(true);
      expect(finalProfileResponse.body.user.hasWallet).toBe(true);
      expect(finalProfileResponse.body.user.securityLevel).toBe('high');
    });
  });

  describe('Complete P2P Trading Journey', () => {
    let sellerToken, buyerToken, sellerId, buyerId;

    beforeEach(async () => {
      // Create seller
      const sellerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'seller',
          password: 'password123'
        });

      sellerToken = sellerResponse.body.token;
      sellerId = sellerResponse.body.user._id;

      // Create buyer
      const buyerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'buyer',
          password: 'password123'
        });

      buyerToken = buyerResponse.body.token;
      buyerId = buyerResponse.body.user._id;

      // Create wallets for both users
      await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${sellerToken}`);

      await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${buyerToken}`);

      // Add balance to seller's wallet
      await request(app)
        .patch('/api/wallet/balance')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          BTC: 1.0,
          ETH: 10.0,
          USDT: 50000
        });
    });

    test('should complete successful P2P trade', async () => {
      // Step 1: Seller creates offer
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          type: 'sell',
          cryptocurrency: 'BTC',
          amount: 0.5,
          pricePerUnit: 45000,
          paymentMethods: ['M-Pesa', 'Bank Transfer'],
          location: 'Nairobi',
          description: 'Quick BTC sale, M-Pesa preferred'
        });

      expect(offerResponse.status).toBe(201);
      const offerId = offerResponse.body.offer._id;

      // Step 2: Buyer searches and finds offer
      const searchResponse = await request(app)
        .get('/api/offers/search')
        .query({
          type: 'sell',
          cryptocurrency: 'BTC',
          location: 'Nairobi',
          paymentMethod: 'M-Pesa'
        });

      expect(searchResponse.status).toBe(200);
      expect(searchResponse.body.offers.length).toBeGreaterThan(0);
      expect(searchResponse.body.offers[0]._id).toBe(offerId);

      // Step 3: Buyer initiates trade
      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          offerId: offerId,
          amount: 0.1,
          totalPrice: 4500,
          paymentMethod: 'M-Pesa'
        });

      expect(tradeResponse.status).toBe(201);
      const tradeId = tradeResponse.body.trade._id;

      // Step 4: Seller accepts trade
      const acceptResponse = await request(app)
        .patch(`/api/trades/${tradeId}/status`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({ status: 'accepted' });

      expect(acceptResponse.status).toBe(200);

      // Step 5: Escrow creation
      const escrowResponse = await request(app)
        .post('/api/escrow')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({ tradeId: tradeId });

      expect(escrowResponse.status).toBe(201);
      const escrowId = escrowResponse.body.escrow._id;

      // Step 6: Chat communication
      const chatResponse = await request(app)
        .post('/api/chat/message')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          tradeId: tradeId,
          message: 'Hello! I am ready to send M-Pesa payment. Please provide your number.'
        });

      expect(chatResponse.status).toBe(201);

      const replyResponse = await request(app)
        .post('/api/chat/message')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          tradeId: tradeId,
          message: 'Hi! My M-Pesa number is 0700000000. Please send KES 4500.'
        });

      expect(replyResponse.status).toBe(201);

      // Step 7: Buyer makes payment and uploads proof
      const paymentResponse = await request(app)
        .patch(`/api/trades/${tradeId}/payment`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          paymentProof: 'https://example.com/mpesa-receipt.jpg',
          paymentMethod: 'M-Pesa',
          transactionId: 'QHX123456789'
        });

      expect(paymentResponse.status).toBe(200);

      // Step 8: Seller confirms payment and releases escrow
      const confirmResponse = await request(app)
        .patch(`/api/trades/${tradeId}/confirm-payment`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({ confirmed: true });

      expect(confirmResponse.status).toBe(200);

      const releaseResponse = await request(app)
        .patch(`/api/escrow/${escrowId}/release`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({ confirmed: true });

      expect(releaseResponse.status).toBe(200);

      // Step 9: Verify trade completion
      const finalTradeResponse = await request(app)
        .get(`/api/trades/${tradeId}`)
        .set('Authorization', `Bearer ${buyerToken}`);

      expect(finalTradeResponse.status).toBe(200);
      expect(finalTradeResponse.body.trade.status).toBe('completed');

      // Step 10: Verify balances updated
      const sellerBalanceResponse = await request(app)
        .get('/api/wallet/balance')
        .set('Authorization', `Bearer ${sellerToken}`);

      const buyerBalanceResponse = await request(app)
        .get('/api/wallet/balance')
        .set('Authorization', `Bearer ${buyerToken}`);

      expect(sellerBalanceResponse.body.balance.BTC).toBe(0.9); // 1.0 - 0.1
      expect(buyerBalanceResponse.body.balance.BTC).toBe(0.1); // 0 + 0.1

      // Step 11: Leave reviews
      const sellerReviewResponse = await request(app)
        .post('/api/reviews')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          tradeId: tradeId,
          rating: 5,
          comment: 'Great buyer, fast payment!'
        });

      expect(sellerReviewResponse.status).toBe(201);

      const buyerReviewResponse = await request(app)
        .post('/api/reviews')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          tradeId: tradeId,
          rating: 5,
          comment: 'Excellent seller, quick release!'
        });

      expect(buyerReviewResponse.status).toBe(201);
    });

    test('should handle disputed trade resolution', async () => {
      // Create and accept trade
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          type: 'sell',
          cryptocurrency: 'BTC',
          amount: 0.1,
          pricePerUnit: 45000,
          paymentMethods: ['M-Pesa'],
          location: 'Nairobi'
        });

      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          offerId: offerResponse.body.offer._id,
          amount: 0.05,
          totalPrice: 2250
        });

      const tradeId = tradeResponse.body.trade._id;

      await request(app)
        .patch(`/api/trades/${tradeId}/status`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({ status: 'accepted' });

      await request(app)
        .post('/api/escrow')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({ tradeId: tradeId });

      // Buyer uploads payment proof
      await request(app)
        .patch(`/api/trades/${tradeId}/payment`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          paymentProof: 'https://example.com/fake-receipt.jpg',
          paymentMethod: 'M-Pesa',
          transactionId: 'FAKE123456'
        });

      // Seller disputes payment
      const disputeResponse = await request(app)
        .post('/api/disputes')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          tradeId: tradeId,
          reason: 'Payment not received',
          description: 'Buyer claims to have sent payment but I have not received anything',
          evidence: ['https://example.com/bank-statement.pdf']
        });

      expect(disputeResponse.status).toBe(201);
      expect(disputeResponse.body.dispute.status).toBe('open');

      // Buyer responds to dispute
      const responseResponse = await request(app)
        .post(`/api/disputes/${disputeResponse.body.dispute._id}/respond`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          response: 'I sent the payment as shown in the receipt',
          evidence: ['https://example.com/mpesa-confirmation.jpg']
        });

      expect(responseResponse.status).toBe(200);

      // Admin resolves dispute (simulated)
      const resolutionResponse = await request(app)
        .patch(`/api/disputes/${disputeResponse.body.dispute._id}/resolve`)
        .set('Authorization', `Bearer ${sellerToken}`) // Admin token in real scenario
        .send({
          resolution: 'buyer_wins',
          reason: 'Payment proof is valid',
          adminNotes: 'M-Pesa transaction verified through operator'
        });

      expect(resolutionResponse.status).toBe(200);

      // Verify trade status
      const finalTradeResponse = await request(app)
        .get(`/api/trades/${tradeId}`)
        .set('Authorization', `Bearer ${buyerToken}`);

      expect(finalTradeResponse.body.trade.status).toBe('completed');
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    test('should handle network interruption during trade', async () => {
      // Simulate network interruption by testing incomplete operations
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'user',
          password: 'password123'
        });

      const authToken = registerResponse.body.token;

      // Start creating offer but simulate interruption
      const incompleteOfferResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'sell',
          cryptocurrency: 'BTC'
          // Missing required fields to simulate incomplete request
        });

      expect(incompleteOfferResponse.status).toBe(400);
      expect(incompleteOfferResponse.body.success).toBe(false);

      // Verify system state remains consistent
      const offersResponse = await request(app)
        .get('/api/offers')
        .set('Authorization', `Bearer ${authToken}`);

      expect(offersResponse.status).toBe(200);
      expect(offersResponse.body.offers.length).toBe(0);
    });

    test('should handle concurrent trade attempts on same offer', async () => {
      // Create seller and offer
      const sellerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'seller',
          password: 'password123'
        });

      const sellerToken = sellerResponse.body.token;

      await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${sellerToken}`);

      await request(app)
        .patch('/api/wallet/balance')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({ BTC: 1.0 });

      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          type: 'sell',
          cryptocurrency: 'BTC',
          amount: 0.1,
          pricePerUnit: 45000,
          paymentMethods: ['M-Pesa'],
          location: 'Nairobi'
        });

      const offerId = offerResponse.body.offer._id;

      // Create multiple buyers
      const buyer1Response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'buyer1',
          password: 'password123'
        });

      const buyer2Response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'buyer2',
          password: 'password123'
        });

      const buyer1Token = buyer1Response.body.token;
      const buyer2Token = buyer2Response.body.token;

      // Both buyers try to create trades simultaneously
      const tradePromises = [
        request(app)
          .post('/api/trades')
          .set('Authorization', `Bearer ${buyer1Token}`)
          .send({
            offerId: offerId,
            amount: 0.1,
            totalPrice: 4500
          }),
        request(app)
          .post('/api/trades')
          .set('Authorization', `Bearer ${buyer2Token}`)
          .send({
            offerId: offerId,
            amount: 0.1,
            totalPrice: 4500
          })
      ];

      const tradeResponses = await Promise.all(tradePromises);

      // Only one should succeed
      const successfulTrades = tradeResponses.filter(r => r.status === 201);
      const failedTrades = tradeResponses.filter(r => r.status !== 201);

      expect(successfulTrades.length).toBe(1);
      expect(failedTrades.length).toBe(1);
      expect(failedTrades[0].body.error).toContain('insufficient');
    });
  });
});
