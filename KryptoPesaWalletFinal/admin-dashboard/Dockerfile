# Admin Dashboard Production Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM node:18-alpine as builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./

# Install dependencies with production optimizations
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# Copy source code
COPY . .

# Set production environment
ENV NODE_ENV=production
ENV GENERATE_SOURCEMAP=false
ENV INLINE_RUNTIME_CHUNK=false

# Build the application with optimizations
RUN npm run build && \
    # Remove source maps in production
    find build -name "*.map" -type f -delete && \
    # Optimize images (if imagemin is available)
    npm run optimize:images || true

# Production stage
FROM nginx:1.24-alpine

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache curl && \
    rm -rf /var/cache/apk/*

# Create nginx user and group
RUN addgroup -g 101 -S nginx && \
    adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# Copy built files with proper permissions
COPY --from=builder --chown=nginx:nginx /app/build /usr/share/nginx/html

# Copy production nginx configuration
COPY --chown=nginx:nginx nginx.production.conf /etc/nginx/conf.d/default.conf

# Remove default nginx config
RUN rm -f /etc/nginx/conf.d/default.conf.bak

# Create log directories
RUN mkdir -p /var/log/nginx && \
    chown -R nginx:nginx /var/log/nginx

# Security: Remove unnecessary packages and files
RUN rm -rf /var/cache/apk/* /tmp/* /var/tmp/*

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Use non-root user
USER nginx

# Expose port
EXPOSE 80

# Start nginx with optimized settings
CMD ["nginx", "-g", "daemon off; worker_processes auto;"]
