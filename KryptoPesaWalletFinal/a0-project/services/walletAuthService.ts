import { authTokens, API_ENDPOINTS, ApiResponse } from './api';

// Lazy import for apiClient to avoid early initialization
const getApiClient = () => {
  const { apiClient } = require('./api');
  return apiClient;
};

// Types for wallet authentication
export interface ChallengeRequest {
  walletAddress: string;
  action?: string;
}

export interface ChallengeResponse {
  challenge: string;
  nonce: string;
  expiresAt: number;
}

export interface VerifyRequest {
  walletAddress: string;
  signature: string;
  nonce: string;
}

export interface VerifyResponse {
  success: boolean;
  user: {
    _id: string;
    walletAddress: string;
    profile: {
      displayName: string;
    };
    reputation: {
      score: number;
      totalTrades: number;
    };
  };
  sessionId: string;
}

export interface WalletAuthResponse {
  user: VerifyResponse['user'];
  sessionId: string;
}

// Wallet Authentication Service
class WalletAuthService {
  /**
   * Step 1: Request authentication challenge from backend
   */
  async requestChallenge(walletAddress: string, action: string = 'login'): Promise<ChallengeResponse> {
    try {
      const response = await getApiClient().post<ChallengeResponse>(
        API_ENDPOINTS.WALLET_AUTH.CHALLENGE,
        { walletAddress, action }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get challenge');
      }
    } catch (error) {
      console.error('Challenge request error:', error);
      throw error;
    }
  }

  /**
   * Step 2: Verify wallet signature and complete authentication
   */
  async verifySignature(walletAddress: string, signature: string, nonce: string): Promise<WalletAuthResponse> {
    try {
      const response = await getApiClient().post<VerifyResponse>(
        API_ENDPOINTS.WALLET_AUTH.VERIFY,
        { walletAddress, signature, nonce }
      );

      if (response.success && response.data) {
        // Store session and wallet address (no JWT tokens in wallet auth)
        await authTokens.setTokens(
          response.data.sessionId, // Use sessionId as access token
          '', // No refresh token needed
          walletAddress
        );

        return {
          user: response.data.user,
          sessionId: response.data.sessionId
        };
      } else {
        throw new Error(response.message || 'Signature verification failed');
      }
    } catch (error) {
      console.error('Signature verification error:', error);
      throw error;
    }
  }

  /**
   * Complete wallet authentication flow (challenge + verify)
   */
  async authenticateWithWallet(walletAddress: string, signMessage: (message: string) => Promise<string>): Promise<WalletAuthResponse> {
    try {
      // Step 1: Get challenge
      const challenge = await this.requestChallenge(walletAddress);

      // Step 2: Sign challenge
      const signature = await signMessage(challenge.challenge);

      // Step 3: Verify signature
      return await this.verifySignature(walletAddress, signature, challenge.nonce);
    } catch (error) {
      console.error('Wallet authentication error:', error);
      throw error;
    }
  }

  /**
   * Refresh authentication tokens
   */
  async refreshTokens(): Promise<WalletAuthResponse> {
    try {
      const response = await getApiClient().post<WalletAuthResponse>(
        API_ENDPOINTS.WALLET_AUTH.REFRESH
      );

      if (response.success && response.data) {
        // Update stored tokens
        await authTokens.setTokens(
          response.data.accessToken,
          response.data.refreshToken
        );

        return response.data;
      } else {
        throw new Error(response.message || 'Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      // Clear invalid tokens
      await authTokens.clearTokens();
      throw error;
    }
  }

  /**
   * Logout user and clear tokens
   */
  async logout(): Promise<void> {
    try {
      // Clear local tokens
      await authTokens.clearTokens();
      
      // Note: Backend doesn't require explicit logout for wallet auth
      // as tokens are stateless JWTs
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear tokens even if API call fails
      await authTokens.clearTokens();
    }
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    return authTokens.isAuthenticated();
  }

  /**
   * Get current wallet address
   */
  async getCurrentWalletAddress(): Promise<string | null> {
    return authTokens.getWalletAddress();
  }

  /**
   * Generate authentication message for signing
   * This creates a standardized message that the user signs with their wallet
   */
  generateAuthMessage(walletAddress: string, timestamp: number): string {
    return `KryptoPesa Authentication\n\nWallet: ${walletAddress}\nTimestamp: ${timestamp}\n\nBy signing this message, you authenticate with KryptoPesa using your wallet.`;
  }

  /**
   * Generate current timestamp for authentication
   */
  generateTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * Validate authentication message format
   */
  validateAuthMessage(message: string, walletAddress: string, timestamp: number): boolean {
    const expectedMessage = this.generateAuthMessage(walletAddress, timestamp);
    return message === expectedMessage;
  }

  /**
   * Check if timestamp is within acceptable range (5 minutes)
   */
  isTimestampValid(timestamp: number): boolean {
    const now = Math.floor(Date.now() / 1000);
    const fiveMinutes = 5 * 60;
    return Math.abs(now - timestamp) <= fiveMinutes;
  }

  /**
   * Prepare authentication data for wallet signing
   */
  prepareAuthData(walletAddress: string): {
    message: string;
    timestamp: number;
  } {
    const timestamp = this.generateTimestamp();
    const message = this.generateAuthMessage(walletAddress, timestamp);
    
    return { message, timestamp };
  }

  /**
   * Complete authentication after wallet signing
   */
  async completeAuthentication(
    walletAddress: string,
    signature: string,
    message: string,
    timestamp: number
  ): Promise<WalletAuthResponse> {
    // Validate inputs
    if (!this.validateAuthMessage(message, walletAddress, timestamp)) {
      throw new Error('Invalid authentication message');
    }

    if (!this.isTimestampValid(timestamp)) {
      throw new Error('Authentication timestamp expired');
    }

    // Perform authentication
    return this.authenticateWithWallet({
      walletAddress,
      signature,
      message,
      timestamp,
    });
  }
}

// Create and export service instance
export const walletAuthService = new WalletAuthService();

// Helper functions for wallet integration
export const walletAuthHelpers = {
  /**
   * Format wallet address for display
   */
  formatWalletAddress(address: string): string {
    if (address.length <= 10) return address;
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  },

  /**
   * Validate Ethereum wallet address format
   */
  isValidEthereumAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  },

  /**
   * Validate Bitcoin wallet address format (basic)
   */
  isValidBitcoinAddress(address: string): boolean {
    return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) || 
           /^bc1[a-z0-9]{39,59}$/.test(address);
  },

  /**
   * Determine wallet type from address
   */
  getWalletType(address: string): 'ethereum' | 'bitcoin' | 'unknown' {
    if (this.isValidEthereumAddress(address)) return 'ethereum';
    if (this.isValidBitcoinAddress(address)) return 'bitcoin';
    return 'unknown';
  },

  /**
   * Generate QR code data for wallet connection
   */
  generateWalletConnectData(walletAddress: string): string {
    const timestamp = Math.floor(Date.now() / 1000);
    return JSON.stringify({
      action: 'kryptopesa_auth',
      walletAddress,
      timestamp,
      domain: 'kryptopesa.com',
    });
  },
};

export default walletAuthService;
