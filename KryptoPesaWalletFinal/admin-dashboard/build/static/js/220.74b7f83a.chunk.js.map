{"version": 3, "file": "static/js/220.74b7f83a.chunk.js", "mappings": "+kBA+CA,MAupBA,EAvpBkBA,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GACtB,MAAOC,GAAOC,KAAYC,EAAAA,EAAAA,UAAS,KAC5BC,GAASC,KAAcF,EAAAA,EAAAA,WAAS,IAChCG,GAAOC,KAAYJ,EAAAA,EAAAA,UAAS,OAC5BK,GAAMC,KAAWN,EAAAA,EAAAA,UAAS,IAC1BO,GAAaC,KAAkBR,EAAAA,EAAAA,UAAS,KACxCS,GAAYC,KAAiBV,EAAAA,EAAAA,UAAS,IACtCW,GAAYC,KAAiBZ,EAAAA,EAAAA,UAAS,KACtCa,GAAcC,KAAmBd,EAAAA,EAAAA,UAAS,KAC1Ce,GAAYC,KAAiBhB,EAAAA,EAAAA,UAAS,KACtCiB,GAAgBC,KAAqBlB,EAAAA,EAAAA,UAAS,KAC9CmB,GAAcC,KAAmBpB,EAAAA,EAAAA,UAAS,OAC1CqB,GAAgBC,KAAqBtB,EAAAA,EAAAA,WAAS,IAC9CuB,GAAkBC,KAAuBxB,EAAAA,EAAAA,WAAS,IAClDyB,GAAYC,KAAiB1B,EAAAA,EAAAA,UAAS,KACtC2B,GAAcC,KAAmB5B,EAAAA,EAAAA,UAAS,KAC1C6B,GAAUC,KAAe9B,EAAAA,EAAAA,UAAS,CAAE+B,MAAM,EAAOC,QAAS,GAAIC,SAAU,YAGzEC,GAAaC,UACjB,IACEjC,IAAW,GACX,MAAMkC,EAAS,CACb/B,KAAMA,GAAO,EACbgC,MAAO9B,GACP+B,OAAQ3B,SAAc4B,EACtBC,OAAQ3B,SAAgB0B,EACxBE,KAAM1B,SAAcwB,EACpBG,SAAUzB,SAAkBsB,EAC5BI,OAAQ,YACRC,UAAW,QAGPC,QAAiBC,EAAAA,EAAMC,IAAI,eAAgB,CAAEX,WAE/CS,EAASG,KAAKC,UAChBlD,GAAS8C,EAASG,KAAKA,KAAKlD,OAC5BY,GAAcmC,EAASG,KAAKA,KAAKE,WAAWzC,YAEhD,CAAE,MAAON,IACPgD,QAAQhD,MAAM,wBAAyBA,IACvCC,GAAS,wBACX,CAAC,QACCF,IAAW,EACb,IAGFkD,EAAAA,EAAAA,WAAU,KACRlB,MACC,CAAC7B,GAAME,GAAaI,GAAYE,GAAcE,GAAYE,KAG7D,MAMMoC,GAAqBA,CAACC,EAAYC,KACtC,OAAQD,GACN,IAAK,SACHxC,GAAgByC,GAChB,MACF,IAAK,OACHvC,GAAcuC,GACd,MACF,IAAK,WACHrC,GAAkBqC,GAGtBjD,GAAQ,IAcJkD,GAAmBrB,MAAOsB,EAAMC,KACpCtC,GAAgBqC,GAChB/B,GAAcgC,GACdlC,IAAoB,IAuDhBmC,GAAkBnB,IACtB,OAAQA,GACN,IAAK,SAAU,MAAO,UACtB,IAAK,YAAa,MAAO,UACzB,IAAK,SAAU,MAAO,QAEtB,QAAS,MAAO,YAKdoB,GAAgBnB,IACpB,OAAQA,GACN,IAAK,QAAS,MAAO,QACrB,IAAK,YAAa,MAAO,UACzB,IAAK,OAAQ,MAAO,UACpB,QAAS,MAAO,YAIpB,OAAIxC,IAA4B,IAAjBH,GAAM+D,QAEjBC,EAAAA,EAAAA,KAACC,EAAAA,EAAG,CAACC,QAAQ,OAAOC,eAAe,SAASC,WAAW,SAASC,UAAU,QAAOC,UAC/EN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAAAD,SAAC,wBAMhBE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAAAK,SAAA,EACFN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,KAAKC,cAAY,EAAAJ,SAAC,oBAIrCjE,KACC2D,EAAAA,EAAAA,KAACW,EAAAA,EAAK,CAACxC,SAAS,QAAQyC,GAAI,CAAEC,GAAI,GAAIP,SACnCjE,MAKL2D,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CAACF,GAAI,CAAEG,EAAG,EAAGF,GAAI,GAAIP,UACzBE,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAGd,WAAW,SAAQE,SAAA,EAC7CN,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAEf,UACvBN,EAAAA,EAAAA,KAACsB,EAAAA,EAAS,CACRC,WAAS,EACTC,YAAY,kBACZ/B,MAAO5C,GACP4E,SA1IUC,IACpB5E,GAAc4E,EAAMC,OAAOlC,OAC3BjD,GAAQ,IAyIEoF,WAAY,CACVC,gBACE7B,EAAAA,EAAAA,KAAC8B,EAAAA,EAAc,CAACC,SAAS,QAAOzB,UAC9BN,EAAAA,EAAAA,KAACgC,EAAAA,EAAU,YAOrBhC,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAEf,UACvBE,EAAAA,EAAAA,MAACyB,EAAAA,EAAW,CAACV,WAAS,EAAAjB,SAAA,EACpBN,EAAAA,EAAAA,KAACkC,EAAAA,EAAU,CAAA5B,SAAC,YACZE,EAAAA,EAAAA,MAAC2B,EAAAA,EAAM,CACL1C,MAAO1C,GACPqF,MAAM,SACNX,SAAWY,GAAM9C,GAAmB,SAAU8C,EAAEV,OAAOlC,OAAOa,SAAA,EAE9DN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,GAAEa,SAAC,SACnBN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,SAAQa,SAAC,YACzBN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,YAAWa,SAAC,eAC5BN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,SAAQa,SAAC,YACzBN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,UAASa,SAAC,qBAKhCN,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAEf,UACvBE,EAAAA,EAAAA,MAACyB,EAAAA,EAAW,CAACV,WAAS,EAAAjB,SAAA,EACpBN,EAAAA,EAAAA,KAACkC,EAAAA,EAAU,CAAA5B,SAAC,UACZE,EAAAA,EAAAA,MAAC2B,EAAAA,EAAM,CACL1C,MAAOxC,GACPmF,MAAM,OACNX,SAAWY,GAAM9C,GAAmB,OAAQ8C,EAAEV,OAAOlC,OAAOa,SAAA,EAE5DN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,GAAEa,SAAC,SACnBN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,OAAMa,SAAC,UACvBN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,YAAWa,SAAC,eAC5BN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,QAAOa,SAAC,mBAK9BN,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAEf,UACvBE,EAAAA,EAAAA,MAACyB,EAAAA,EAAW,CAACV,WAAS,EAAAjB,SAAA,EACpBN,EAAAA,EAAAA,KAACkC,EAAAA,EAAU,CAAA5B,SAAC,cACZE,EAAAA,EAAAA,MAAC2B,EAAAA,EAAM,CACL1C,MAAOtC,GACPiF,MAAM,WACNX,SAAWY,GAAM9C,GAAmB,WAAY8C,EAAEV,OAAOlC,OAAOa,SAAA,EAEhEN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,GAAEa,SAAC,SACnBN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,OAAMa,SAAC,cACvBN,EAAAA,EAAAA,KAACsC,EAAAA,EAAQ,CAAC7C,MAAM,QAAOa,SAAC,wBAK9BN,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAEf,UACvBN,EAAAA,EAAAA,KAACuC,EAAAA,EAAM,CACLhB,WAAS,EACTd,QAAQ,WACR+B,WAAWxC,EAAAA,EAAAA,KAACyC,EAAAA,EAAU,IACtBC,QAASA,KACP5F,GAAc,IACdE,GAAgB,IAChBE,GAAc,IACdE,GAAkB,IAClBZ,GAAQ,IACR8D,SACH,0BAQPE,EAAAA,EAAAA,MAACmC,EAAAA,EAAc,CAACC,UAAW9B,EAAAA,EAAMR,SAAA,EAC/BE,EAAAA,EAAAA,MAACqC,EAAAA,EAAK,CAAAvC,SAAA,EACJN,EAAAA,EAAAA,KAAC8C,EAAAA,EAAS,CAAAxC,UACRE,EAAAA,EAAAA,MAACuC,EAAAA,EAAQ,CAAAzC,SAAA,EACPN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,SAAC,UACXN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,SAAC,aACXN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,SAAC,YACXN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,SAAC,UACXN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,SAAC,gBACXN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,SAAC,cACXN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,SAAC,YACXN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,SAAC,kBAGfN,EAAAA,EAAAA,KAACiD,EAAAA,EAAS,CAAA3C,SACPtE,GAAMkH,IAAKvD,IAAI,IAAAwD,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACdzD,EAAAA,EAAAA,MAACuC,EAAAA,EAAQ,CAAgBmB,OAAK,EAAA5D,SAAA,EAC5BN,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,UACRE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACC,QAAQ,OAAOE,WAAW,SAAS+D,IAAK,EAAE7D,SAAA,EAC7CN,EAAAA,EAAAA,KAACoE,EAAAA,EAAM,CAAA9D,SACQ,QAAZ6C,EAAAxD,EAAK0E,eAAO,IAAAlB,GAAZA,EAAcmB,QACbtE,EAAAA,EAAAA,KAAA,OAAKuE,IAAK5E,EAAK0E,QAAQC,OAAQE,IAAK7E,EAAK8E,YAEzCzE,EAAAA,EAAAA,KAAC0E,EAAAA,EAAU,OAGflE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAAAK,SAAA,EACFE,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,YAAWH,SAAA,CAChB,QADgB8C,EAC5BzD,EAAK0E,eAAO,IAAAjB,OAAA,EAAZA,EAAcuB,UAAU,IAAc,QAAbtB,EAAC1D,EAAK0E,eAAO,IAAAhB,OAAA,EAAZA,EAAcuB,aAE3CpE,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAA,CAAC,IAC9CX,EAAK8E,qBAMfzE,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,UACRE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAAAK,SAAA,EACFE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACC,QAAQ,OAAOE,WAAW,SAAS+D,IAAK,EAAGtD,GAAI,GAAIP,SAAA,EACtDN,EAAAA,EAAAA,KAAC8E,EAAAA,EAAS,CAACC,SAAS,QAAQF,MAAM,YAClC7E,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAEX,EAAKqF,YAEpCxE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACC,QAAQ,OAAOE,WAAW,SAAS+D,IAAK,EAAE7D,SAAA,EAC7CN,EAAAA,EAAAA,KAACiF,EAAAA,EAAS,CAACF,SAAS,QAAQF,MAAM,YAClC7E,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAEX,EAAKuF,iBAKxClF,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,UACRN,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAOzC,EAAKjB,OACZmG,MAAOhF,GAAeF,EAAKjB,QAC3B0G,KAAK,aAITpF,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,UACRN,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAOzC,EAAKhB,KACZkG,MAAO/E,GAAaH,EAAKhB,MACzByG,KAAK,aAIT5E,EAAAA,EAAAA,MAACwC,EAAAA,EAAS,CAAA1C,SAAA,EACRE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACC,QAAQ,OAAOE,WAAW,SAAS+D,IAAK,EAAE7D,SAAA,EAC7CN,EAAAA,EAAAA,KAACqF,EAAAA,EAAQ,CAACN,SAAS,QAAQF,MAAM,aACjCrE,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAA,EACT,QAAfgD,EAAA3D,EAAK2F,kBAAU,IAAAhC,OAAA,EAAfA,EAAiBiC,QAAS,EAAE,cAGjC/E,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,UAAUoE,MAAM,gBAAevE,SAAA,EACjC,QAAfiD,EAAA5D,EAAK2F,kBAAU,IAAA/B,OAAA,EAAfA,EAAiBiC,cAAe,EAAE,iBAIvCxF,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,UACRE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAAAK,SAAA,EACFN,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAwB,QAAjBoB,EAAA7D,EAAK8F,oBAAY,IAAAjC,GAAU,QAAVC,EAAjBD,EAAmBkC,gBAAQ,IAAAjC,GAA3BA,EAA6B7E,SAAW,cAAgB,aAC/DiG,MAAwB,QAAjBnB,EAAA/D,EAAK8F,oBAAY,IAAA/B,GAAU,QAAVC,EAAjBD,EAAmBgC,gBAAQ,IAAA/B,GAA3BA,EAA6B/E,SAAW,UAAY,UAC3DwG,KAAK,QACLxE,GAAI,CAAEC,GAAI,OAEM,QAAjB+C,EAAAjE,EAAK8F,oBAAY,IAAA7B,GAAO,QAAPC,EAAjBD,EAAmBoB,aAAK,IAAAnB,OAAP,EAAjBA,EAA0BjF,YACzBoB,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAM,QACNyC,MAAM,OACNO,KAAK,QACLxE,GAAI,CAAE+E,GAAI,OAGI,QAAjB7B,EAAAnE,EAAK8F,oBAAY,IAAA3B,GAAO,QAAPC,EAAjBD,EAAmBoB,aAAK,IAAAnB,OAAP,EAAjBA,EAA0BnF,YACzBoB,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAM,QACNyC,MAAM,OACNO,KAAK,gBAMb5E,EAAAA,EAAAA,MAACwC,EAAAA,EAAS,CAAA1C,SAAA,EACRN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SACxB,IAAIsF,KAAKjG,EAAKkG,WAAWC,wBAE5BtF,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,UAAUoE,MAAM,gBAAevE,SAAA,CAAC,gBACpC,IAAIsF,KAAKjG,EAAKoG,YAAYD,4BAI5C9F,EAAAA,EAAAA,KAACgD,EAAAA,EAAS,CAAA1C,UACRE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACC,QAAQ,OAAOiE,IAAK,EAAE7D,SAAA,EACzBN,EAAAA,EAAAA,KAACgG,EAAAA,EAAO,CAACC,MAAM,eAAc3F,UAC3BN,EAAAA,EAAAA,KAACkG,EAAAA,EAAU,CACTd,KAAK,QACL1C,QAASA,KACPpF,GAAgBqC,GAChBnC,IAAkB,IAClB8C,UAEFN,EAAAA,EAAAA,KAACmG,EAAAA,EAAQ,QAII,WAAhBxG,EAAKjB,SACJsB,EAAAA,EAAAA,KAACgG,EAAAA,EAAO,CAACC,MAAM,eAAc3F,UAC3BN,EAAAA,EAAAA,KAACkG,EAAAA,EAAU,CACTd,KAAK,QACLP,MAAM,UACNnC,QAASA,IAAMhD,GAAiBC,EAAM,WAAWW,UAEjDN,EAAAA,EAAAA,KAACoG,EAAAA,EAAS,QAKC,WAAhBzG,EAAKjB,SACJsB,EAAAA,EAAAA,KAACgG,EAAAA,EAAO,CAACC,MAAM,WAAU3F,UACvBN,EAAAA,EAAAA,KAACkG,EAAAA,EAAU,CACTd,KAAK,QACLP,MAAM,QACNnC,QAASA,IAAMhD,GAAiBC,EAAM,OAAOW,UAE7CN,EAAAA,EAAAA,KAACoG,EAAAA,EAAS,QAKf,CAAC,YAAa,UAAUC,SAAS1G,EAAKjB,UACrCsB,EAAAA,EAAAA,KAACgG,EAAAA,EAAO,CAACC,MAAM,gBAAe3F,UAC5BN,EAAAA,EAAAA,KAACkG,EAAAA,EAAU,CACTd,KAAK,QACLP,MAAM,UACNnC,QAASA,IAAMhD,GAAiBC,EAAM,YAAYW,UAElDN,EAAAA,EAAAA,KAACsG,EAAAA,EAAU,UAKE,QAAlBtC,EAACrE,EAAK8F,oBAAY,IAAAzB,GAAU,QAAVC,EAAjBD,EAAmB0B,gBAAQ,IAAAzB,GAA3BA,EAA6BrF,YAC7BoB,EAAAA,EAAAA,KAACgG,EAAAA,EAAO,CAACC,MAAM,kBAAiB3F,UAC9BN,EAAAA,EAAAA,KAACkG,EAAAA,EAAU,CACTd,KAAK,QACLP,MAAM,UACNnC,QAASA,IAAMhD,GAAiBC,EAAM,UAAUW,UAEhDN,EAAAA,EAAAA,KAACsG,EAAAA,EAAU,eA1JR3G,EAAK4G,aAqK1BvG,EAAAA,EAAAA,KAACwG,EAAAA,EAAe,CACdC,mBAAoB,CAAC,GAAI,GAAI,GAAI,KACjC7D,UAAU,MACV8D,MAAO/J,GACPF,YAAaA,GACbF,KAAMA,GACNoK,aA9XiBC,CAAClF,EAAOmF,KAC/BrK,GAAQqK,IA8XFC,oBA3XyBpF,IAC/BhF,GAAeqK,SAASrF,EAAMC,OAAOlC,MAAO,KAC5CjD,GAAQ,UA8XNgE,EAAAA,EAAAA,MAACwG,EAAAA,EAAM,CACL/I,KAAMV,GACN0J,QAASA,IAAMzJ,IAAkB,GACjC0J,SAAS,KACT3F,WAAS,EAAAjB,SAAA,EAETE,EAAAA,EAAAA,MAAC2G,EAAAA,EAAW,CAAA7G,SAAA,CAAC,iBACgB,OAAZjD,SAAY,IAAZA,QAAY,EAAZA,GAAcoH,aAE/BzE,EAAAA,EAAAA,KAACoH,EAAAA,EAAa,CAAA9G,SACXjD,KACCmD,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAEZ,SAAA,EACzBN,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAEf,UACvBN,EAAAA,EAAAA,KAACqH,EAAAA,EAAI,CAAA/G,UACHE,EAAAA,EAAAA,MAAC8G,EAAAA,EAAW,CAAAhH,SAAA,EACVN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,KAAKC,cAAY,EAAAJ,SAAC,yBAGtCE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,UAClDE,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAA,CACJ,QADI1F,EACxByC,GAAagH,eAAO,IAAAzJ,OAAA,EAApBA,EAAsB+J,UAAU,IAAsB,QAArB9J,EAACwC,GAAagH,eAAO,IAAAxJ,OAAA,EAApBA,EAAsB+J,gBAG7DpE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,cAClDE,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAA,CAAC,IAAEjD,GAAaoH,gBAE7CjE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,WAClDN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAEjD,GAAa2H,YAE5CxE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,WAClDN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAEjD,GAAa6H,YAE5C1E,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,cAClDE,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAA,CACJ,QADIxF,EACxBuC,GAAagH,eAAO,IAAAvJ,GAAU,QAAVC,EAApBD,EAAsByM,gBAAQ,IAAAxM,OAAV,EAApBA,EAAgCyM,KAAK,KAAuB,QAArBxM,EAACqC,GAAagH,eAAO,IAAArJ,GAAU,QAAVC,EAApBD,EAAsBuM,gBAAQ,IAAAtM,OAAV,EAApBA,EAAgCwM,sBAOnFzH,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAEf,UACvBN,EAAAA,EAAAA,KAACqH,EAAAA,EAAI,CAAA/G,UACHE,EAAAA,EAAAA,MAAC8G,EAAAA,EAAW,CAAAhH,SAAA,EACVN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,KAAKC,cAAY,EAAAJ,SAAC,oBAGtCE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,YAClDN,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAO/E,GAAaqB,OACpBmG,MAAOhF,GAAexC,GAAaqB,QACnC0G,KAAK,cAGT5E,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,UAClDN,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAO/E,GAAasB,KACpBkG,MAAO/E,GAAazC,GAAasB,MACjCyG,KAAK,cAGT5E,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,sBAClDE,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SAAA,EACD,QAAvBpF,EAAAmC,GAAaiI,kBAAU,IAAApK,OAAA,EAAvBA,EAAyBqK,QAAS,EAAE,cAGzC/E,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,kBAClDN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAOH,UACD,QAAvBnF,EAAAkC,GAAaiI,kBAAU,IAAAnK,OAAA,EAAvBA,EAAyBqK,cAAe,QAG7ChF,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,iBAClDN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SACxB,IAAIsF,KAAKvI,GAAawI,WAAWC,2BAGtCtF,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACY,GAAI,EAAEP,SAAA,EACTN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,iBAClDN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAOH,SACxB,IAAIsF,KAAKvI,GAAa0I,YAAYD,kCAO7C9F,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAGd,UAChBN,EAAAA,EAAAA,KAACqH,EAAAA,EAAI,CAAA/G,UACHE,EAAAA,EAAAA,MAAC8G,EAAAA,EAAW,CAAAhH,SAAA,EACVN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,KAAKC,cAAY,EAAAJ,SAAC,yBAGtCE,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAEZ,SAAA,EACzBN,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,EAAEd,UACfE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACyH,UAAU,SAAQpH,SAAA,EACrBN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,WAClDN,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAgC,QAAzBhH,EAAAiC,GAAaoI,oBAAY,IAAArK,GAAO,QAAPC,EAAzBD,EAA2B4J,aAAK,IAAA3J,GAAhCA,EAAkCuD,SAAW,WAAa,aACjEiG,MAAgC,QAAzBvJ,EAAA+B,GAAaoI,oBAAY,IAAAnK,GAAO,QAAPC,GAAzBD,EAA2B0J,aAAK,IAAAzJ,IAAhCA,GAAkCqD,SAAW,UAAY,UAChEwG,KAAK,gBAIXpF,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,EAAEd,UACfE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACyH,UAAU,SAAQpH,SAAA,EACrBN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,WAClDN,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAgC,QAAzB5G,GAAA6B,GAAaoI,oBAAY,IAAAjK,IAAO,QAAPC,GAAzBD,GAA2B0J,aAAK,IAAAzJ,IAAhCA,GAAkCmD,SAAW,WAAa,aACjEiG,MAAgC,QAAzBnJ,GAAA2B,GAAaoI,oBAAY,IAAA/J,IAAO,QAAPC,GAAzBD,GAA2BwJ,aAAK,IAAAvJ,IAAhCA,GAAkCiD,SAAW,UAAY,UAChEwG,KAAK,gBAIXpF,EAAAA,EAAAA,KAACgB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,EAAEd,UACfE,EAAAA,EAAAA,MAACP,EAAAA,EAAG,CAACyH,UAAU,SAAQpH,SAAA,EACrBN,EAAAA,EAAAA,KAACO,EAAAA,EAAU,CAACE,QAAQ,QAAQoE,MAAM,gBAAevE,SAAC,cAClDN,EAAAA,EAAAA,KAACmF,EAAAA,EAAI,CACH/C,MAAgC,QAAzBxG,GAAAyB,GAAaoI,oBAAY,IAAA7J,IAAU,QAAVC,GAAzBD,GAA2B8J,gBAAQ,IAAA7J,IAAnCA,GAAqC+C,SAAW,WAAa,aACpEiG,MAAgC,QAAzB/I,GAAAuB,GAAaoI,oBAAY,IAAA3J,IAAU,QAAVC,GAAzBD,GAA2B4J,gBAAQ,IAAA3J,IAAnCA,GAAqC6C,SAAW,UAAY,UACnEwG,KAAK,+BAWzBpF,EAAAA,EAAAA,KAAC2H,EAAAA,EAAa,CAAArH,UACZN,EAAAA,EAAAA,KAACuC,EAAAA,EAAM,CAACG,QAASA,IAAMlF,IAAkB,GAAO8C,SAAC,gBAKrDE,EAAAA,EAAAA,MAACwG,EAAAA,EAAM,CACL/I,KAAMR,GACNwJ,QAASA,IAAMvJ,IAAoB,GACnCwJ,SAAS,KACT3F,WAAS,EAAAjB,SAAA,EAETE,EAAAA,EAAAA,MAAC2G,EAAAA,EAAW,CAAA7G,SAAA,CAAC,WACF3C,GAAW,YAEtB6C,EAAAA,EAAAA,MAAC4G,EAAAA,EAAa,CAAA9G,SAAA,EACZE,EAAAA,EAAAA,MAACD,EAAAA,EAAU,CAACG,cAAY,EAAAJ,SAAA,CAAC,4BACG3C,GAAW,UAAoB,OAAZN,SAAY,IAAZA,QAAY,EAAZA,GAAcoH,SAAS,SAEtEzE,EAAAA,EAAAA,KAACsB,EAAAA,EAAS,CACRC,WAAS,EACTqG,WAAS,EACTC,KAAM,EACNzF,MAAM,oBACN3C,MAAO5B,GACP4D,SAAWY,GAAMvE,GAAgBuE,EAAEV,OAAOlC,OAC1CqI,OAAO,SACPC,UAAQ,QAGZvH,EAAAA,EAAAA,MAACmH,EAAAA,EAAa,CAAArH,SAAA,EACZN,EAAAA,EAAAA,KAACuC,EAAAA,EAAM,CAACG,QAASA,IAAMhF,IAAoB,GAAO4C,SAAC,YACnDE,EAAAA,EAAAA,MAAC+B,EAAAA,EAAM,CACLG,QAjiBgBrE,UACxB,IACE,IAAI2J,EAAW,GACX9I,EAAO,CAAC,EAEZ,OAAQvB,IACN,IAAK,UACHqK,EAAQ,gBAAAC,OAAmB5K,GAAakJ,IAAG,WAC3CrH,EAAO,CAAER,OAAQ,YAAawJ,OAAQrK,IACtC,MACF,IAAK,MACHmK,EAAQ,gBAAAC,OAAmB5K,GAAakJ,IAAG,WAC3CrH,EAAO,CAAER,OAAQ,SAAUwJ,OAAQrK,IACnC,MACF,IAAK,WACHmK,EAAQ,gBAAAC,OAAmB5K,GAAakJ,IAAG,WAC3CrH,EAAO,CAAER,OAAQ,SAAUwJ,OAAQrK,IACnC,MACF,IAAK,SACHmK,EAAQ,gBAAAC,OAAmB5K,GAAakJ,IAAG,WAC3CrH,EAAO,CACLiJ,aAAc,cACdC,UAAU,EACVC,MAAOxK,WAKUmB,EAAAA,EAAMsJ,IAAIN,EAAU9I,IAE9BA,KAAKC,UAChBnB,GAAY,CACVC,MAAM,EACNC,QAAQ,QAAD+J,OAAUtK,GAAU,eAC3BQ,SAAU,YAEZC,KAEJ,CAAE,MAAO/B,IAAQ,IAADkM,EAAAC,EACdnJ,QAAQhD,MAAM,+BAAgCA,IAC9C2B,GAAY,CACVC,MAAM,EACNC,SAAuB,QAAdqK,EAAAlM,GAAM0C,gBAAQ,IAAAwJ,GAAM,QAANC,EAAdD,EAAgBrJ,YAAI,IAAAsJ,OAAN,EAAdA,EAAsBtK,UAAO,aAAA+J,OAAiBtK,GAAU,SACjEQ,SAAU,SAEd,CAAC,QACCT,IAAoB,GACpBI,GAAgB,GAClB,GAkfQ+G,MAAM,UACNpE,QAAQ,YACRgI,UAAW5K,GAAa6K,OAAOpI,SAAA,CAChC,WACU3C,aAMfqC,EAAAA,EAAAA,KAAC2I,EAAAA,EAAQ,CACP1K,KAAMF,GAASE,KACf2K,iBAAkB,IAClB3B,QAASA,IAAMjJ,IAAW6K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAI9K,IAAQ,IAAEE,MAAM,KAASqC,UAEzDN,EAAAA,EAAAA,KAACW,EAAAA,EAAK,CACJsG,QAASA,IAAMjJ,IAAW6K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAI9K,IAAQ,IAAEE,MAAM,KAChDE,SAAUJ,GAASI,SACnByC,GAAI,CAAEkI,MAAO,QAASxI,SAErBvC,GAASG,e", "sources": ["pages/UsersPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  TextField,\n  InputAdornment,\n  IconButton,\n  Chip,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Card,\n  CardContent,\n  Avatar,\n  Tooltip,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  Visibility as ViewIcon,\n  Block as BlockIcon,\n  CheckCircle as VerifyIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  LocationOn as LocationIcon,\n  Star as StarIcon\n} from '@mui/icons-material';\nimport axios from 'axios';\n\nconst UsersPage = () => {\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  const [totalUsers, setTotalUsers] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [verifiedFilter, setVerifiedFilter] = useState('');\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [userDetailOpen, setUserDetailOpen] = useState(false);\n  const [actionDialogOpen, setActionDialogOpen] = useState(false);\n  const [actionType, setActionType] = useState('');\n  const [actionReason, setActionReason] = useState('');\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n\n  // Fetch users data\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: page + 1,\n        limit: rowsPerPage,\n        search: searchTerm || undefined,\n        status: statusFilter || undefined,\n        role: roleFilter || undefined,\n        verified: verifiedFilter || undefined,\n        sortBy: 'createdAt',\n        sortOrder: 'desc'\n      };\n\n      const response = await axios.get('/admin/users', { params });\n\n      if (response.data.success) {\n        setUsers(response.data.data.users);\n        setTotalUsers(response.data.data.pagination.totalUsers);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      setError('Failed to fetch users');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n  }, [page, rowsPerPage, searchTerm, statusFilter, roleFilter, verifiedFilter]);\n\n  // Handle search\n  const handleSearch = (event) => {\n    setSearchTerm(event.target.value);\n    setPage(0);\n  };\n\n  // Handle filter changes\n  const handleFilterChange = (filterType, value) => {\n    switch (filterType) {\n      case 'status':\n        setStatusFilter(value);\n        break;\n      case 'role':\n        setRoleFilter(value);\n        break;\n      case 'verified':\n        setVerifiedFilter(value);\n        break;\n    }\n    setPage(0);\n  };\n\n  // Handle pagination\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  const handleChangeRowsPerPage = (event) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  // Handle user actions\n  const handleUserAction = async (user, action) => {\n    setSelectedUser(user);\n    setActionType(action);\n    setActionDialogOpen(true);\n  };\n\n  const executeUserAction = async () => {\n    try {\n      let endpoint = '';\n      let data = {};\n\n      switch (actionType) {\n        case 'suspend':\n          endpoint = `/admin/users/${selectedUser._id}/status`;\n          data = { status: 'suspended', reason: actionReason };\n          break;\n        case 'ban':\n          endpoint = `/admin/users/${selectedUser._id}/status`;\n          data = { status: 'banned', reason: actionReason };\n          break;\n        case 'activate':\n          endpoint = `/admin/users/${selectedUser._id}/status`;\n          data = { status: 'active', reason: actionReason };\n          break;\n        case 'verify':\n          endpoint = `/admin/users/${selectedUser._id}/verify`;\n          data = {\n            documentType: 'national_id', // Default, should be from form\n            approved: true,\n            notes: actionReason\n          };\n          break;\n      }\n\n      const response = await axios.put(endpoint, data);\n\n      if (response.data.success) {\n        setSnackbar({\n          open: true,\n          message: `User ${actionType} successful`,\n          severity: 'success'\n        });\n        fetchUsers(); // Refresh the list\n      }\n    } catch (error) {\n      console.error('Error executing user action:', error);\n      setSnackbar({\n        open: true,\n        message: error.response?.data?.message || `Failed to ${actionType} user`,\n        severity: 'error'\n      });\n    } finally {\n      setActionDialogOpen(false);\n      setActionReason('');\n    }\n  };\n\n  // Get status chip color\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active': return 'success';\n      case 'suspended': return 'warning';\n      case 'banned': return 'error';\n      case 'pending': return 'default';\n      default: return 'default';\n    }\n  };\n\n  // Get role chip color\n  const getRoleColor = (role) => {\n    switch (role) {\n      case 'admin': return 'error';\n      case 'moderator': return 'warning';\n      case 'user': return 'primary';\n      default: return 'default';\n    }\n  };\n\n  if (loading && users.length === 0) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <Typography>Loading users...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        User Management\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Filters and Search */}\n      <Paper sx={{ p: 2, mb: 2 }}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={12} md={4}>\n            <TextField\n              fullWidth\n              placeholder=\"Search users...\"\n              value={searchTerm}\n              onChange={handleSearch}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n              }}\n            />\n          </Grid>\n\n          <Grid item xs={12} md={2}>\n            <FormControl fullWidth>\n              <InputLabel>Status</InputLabel>\n              <Select\n                value={statusFilter}\n                label=\"Status\"\n                onChange={(e) => handleFilterChange('status', e.target.value)}\n              >\n                <MenuItem value=\"\">All</MenuItem>\n                <MenuItem value=\"active\">Active</MenuItem>\n                <MenuItem value=\"suspended\">Suspended</MenuItem>\n                <MenuItem value=\"banned\">Banned</MenuItem>\n                <MenuItem value=\"pending\">Pending</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} md={2}>\n            <FormControl fullWidth>\n              <InputLabel>Role</InputLabel>\n              <Select\n                value={roleFilter}\n                label=\"Role\"\n                onChange={(e) => handleFilterChange('role', e.target.value)}\n              >\n                <MenuItem value=\"\">All</MenuItem>\n                <MenuItem value=\"user\">User</MenuItem>\n                <MenuItem value=\"moderator\">Moderator</MenuItem>\n                <MenuItem value=\"admin\">Admin</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} md={2}>\n            <FormControl fullWidth>\n              <InputLabel>Verified</InputLabel>\n              <Select\n                value={verifiedFilter}\n                label=\"Verified\"\n                onChange={(e) => handleFilterChange('verified', e.target.value)}\n              >\n                <MenuItem value=\"\">All</MenuItem>\n                <MenuItem value=\"true\">Verified</MenuItem>\n                <MenuItem value=\"false\">Unverified</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} md={2}>\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              startIcon={<FilterIcon />}\n              onClick={() => {\n                setSearchTerm('');\n                setStatusFilter('');\n                setRoleFilter('');\n                setVerifiedFilter('');\n                setPage(0);\n              }}\n            >\n              Clear Filters\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Users Table */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>User</TableCell>\n              <TableCell>Contact</TableCell>\n              <TableCell>Status</TableCell>\n              <TableCell>Role</TableCell>\n              <TableCell>Reputation</TableCell>\n              <TableCell>Verified</TableCell>\n              <TableCell>Joined</TableCell>\n              <TableCell>Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {users.map((user) => (\n              <TableRow key={user._id} hover>\n                <TableCell>\n                  <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                    <Avatar>\n                      {user.profile?.avatar ? (\n                        <img src={user.profile.avatar} alt={user.username} />\n                      ) : (\n                        <PersonIcon />\n                      )}\n                    </Avatar>\n                    <Box>\n                      <Typography variant=\"subtitle2\">\n                        {user.profile?.firstName} {user.profile?.lastName}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        @{user.username}\n                      </Typography>\n                    </Box>\n                  </Box>\n                </TableCell>\n\n                <TableCell>\n                  <Box>\n                    <Box display=\"flex\" alignItems=\"center\" gap={1} mb={0.5}>\n                      <EmailIcon fontSize=\"small\" color=\"action\" />\n                      <Typography variant=\"body2\">{user.email}</Typography>\n                    </Box>\n                    <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                      <PhoneIcon fontSize=\"small\" color=\"action\" />\n                      <Typography variant=\"body2\">{user.phone}</Typography>\n                    </Box>\n                  </Box>\n                </TableCell>\n\n                <TableCell>\n                  <Chip\n                    label={user.status}\n                    color={getStatusColor(user.status)}\n                    size=\"small\"\n                  />\n                </TableCell>\n\n                <TableCell>\n                  <Chip\n                    label={user.role}\n                    color={getRoleColor(user.role)}\n                    size=\"small\"\n                  />\n                </TableCell>\n\n                <TableCell>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                    <StarIcon fontSize=\"small\" color=\"warning\" />\n                    <Typography variant=\"body2\">\n                      {user.reputation?.score || 0}/100\n                    </Typography>\n                  </Box>\n                  <Typography variant=\"caption\" color=\"textSecondary\">\n                    {user.reputation?.totalTrades || 0} trades\n                  </Typography>\n                </TableCell>\n\n                <TableCell>\n                  <Box>\n                    <Chip\n                      label={user.verification?.identity?.verified ? 'ID Verified' : 'Unverified'}\n                      color={user.verification?.identity?.verified ? 'success' : 'default'}\n                      size=\"small\"\n                      sx={{ mb: 0.5 }}\n                    />\n                    {user.verification?.email?.verified && (\n                      <Chip\n                        label=\"Email\"\n                        color=\"info\"\n                        size=\"small\"\n                        sx={{ mr: 0.5 }}\n                      />\n                    )}\n                    {user.verification?.phone?.verified && (\n                      <Chip\n                        label=\"Phone\"\n                        color=\"info\"\n                        size=\"small\"\n                      />\n                    )}\n                  </Box>\n                </TableCell>\n\n                <TableCell>\n                  <Typography variant=\"body2\">\n                    {new Date(user.createdAt).toLocaleDateString()}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"textSecondary\">\n                    Last active: {new Date(user.lastActive).toLocaleDateString()}\n                  </Typography>\n                </TableCell>\n\n                <TableCell>\n                  <Box display=\"flex\" gap={1}>\n                    <Tooltip title=\"View Details\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => {\n                          setSelectedUser(user);\n                          setUserDetailOpen(true);\n                        }}\n                      >\n                        <ViewIcon />\n                      </IconButton>\n                    </Tooltip>\n\n                    {user.status === 'active' && (\n                      <Tooltip title=\"Suspend User\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"warning\"\n                          onClick={() => handleUserAction(user, 'suspend')}\n                        >\n                          <BlockIcon />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n\n                    {user.status !== 'banned' && (\n                      <Tooltip title=\"Ban User\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleUserAction(user, 'ban')}\n                        >\n                          <BlockIcon />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n\n                    {['suspended', 'banned'].includes(user.status) && (\n                      <Tooltip title=\"Activate User\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"success\"\n                          onClick={() => handleUserAction(user, 'activate')}\n                        >\n                          <VerifyIcon />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n\n                    {!user.verification?.identity?.verified && (\n                      <Tooltip title=\"Verify Identity\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"primary\"\n                          onClick={() => handleUserAction(user, 'verify')}\n                        >\n                          <VerifyIcon />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n\n        <TablePagination\n          rowsPerPageOptions={[10, 25, 50, 100]}\n          component=\"div\"\n          count={totalUsers}\n          rowsPerPage={rowsPerPage}\n          page={page}\n          onPageChange={handleChangePage}\n          onRowsPerPageChange={handleChangeRowsPerPage}\n        />\n      </TableContainer>\n\n      {/* User Detail Dialog */}\n      <Dialog\n        open={userDetailOpen}\n        onClose={() => setUserDetailOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          User Details: {selectedUser?.username}\n        </DialogTitle>\n        <DialogContent>\n          {selectedUser && (\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <Card>\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Profile Information\n                    </Typography>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Name</Typography>\n                      <Typography variant=\"body1\">\n                        {selectedUser.profile?.firstName} {selectedUser.profile?.lastName}\n                      </Typography>\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Username</Typography>\n                      <Typography variant=\"body1\">@{selectedUser.username}</Typography>\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Email</Typography>\n                      <Typography variant=\"body1\">{selectedUser.email}</Typography>\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Phone</Typography>\n                      <Typography variant=\"body1\">{selectedUser.phone}</Typography>\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Location</Typography>\n                      <Typography variant=\"body1\">\n                        {selectedUser.profile?.location?.city}, {selectedUser.profile?.location?.country}\n                      </Typography>\n                    </Box>\n                  </CardContent>\n                </Card>\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <Card>\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Account Status\n                    </Typography>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Status</Typography>\n                      <Chip\n                        label={selectedUser.status}\n                        color={getStatusColor(selectedUser.status)}\n                        size=\"small\"\n                      />\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Role</Typography>\n                      <Chip\n                        label={selectedUser.role}\n                        color={getRoleColor(selectedUser.role)}\n                        size=\"small\"\n                      />\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Reputation Score</Typography>\n                      <Typography variant=\"body1\">\n                        {selectedUser.reputation?.score || 0}/100\n                      </Typography>\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Total Trades</Typography>\n                      <Typography variant=\"body1\">\n                        {selectedUser.reputation?.totalTrades || 0}\n                      </Typography>\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Joined Date</Typography>\n                      <Typography variant=\"body1\">\n                        {new Date(selectedUser.createdAt).toLocaleDateString()}\n                      </Typography>\n                    </Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"textSecondary\">Last Active</Typography>\n                      <Typography variant=\"body1\">\n                        {new Date(selectedUser.lastActive).toLocaleDateString()}\n                      </Typography>\n                    </Box>\n                  </CardContent>\n                </Card>\n              </Grid>\n\n              <Grid item xs={12}>\n                <Card>\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Verification Status\n                    </Typography>\n                    <Grid container spacing={2}>\n                      <Grid item xs={4}>\n                        <Box textAlign=\"center\">\n                          <Typography variant=\"body2\" color=\"textSecondary\">Email</Typography>\n                          <Chip\n                            label={selectedUser.verification?.email?.verified ? 'Verified' : 'Unverified'}\n                            color={selectedUser.verification?.email?.verified ? 'success' : 'default'}\n                            size=\"small\"\n                          />\n                        </Box>\n                      </Grid>\n                      <Grid item xs={4}>\n                        <Box textAlign=\"center\">\n                          <Typography variant=\"body2\" color=\"textSecondary\">Phone</Typography>\n                          <Chip\n                            label={selectedUser.verification?.phone?.verified ? 'Verified' : 'Unverified'}\n                            color={selectedUser.verification?.phone?.verified ? 'success' : 'default'}\n                            size=\"small\"\n                          />\n                        </Box>\n                      </Grid>\n                      <Grid item xs={4}>\n                        <Box textAlign=\"center\">\n                          <Typography variant=\"body2\" color=\"textSecondary\">Identity</Typography>\n                          <Chip\n                            label={selectedUser.verification?.identity?.verified ? 'Verified' : 'Unverified'}\n                            color={selectedUser.verification?.identity?.verified ? 'success' : 'default'}\n                            size=\"small\"\n                          />\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </CardContent>\n                </Card>\n              </Grid>\n            </Grid>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setUserDetailOpen(false)}>Close</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Action Confirmation Dialog */}\n      <Dialog\n        open={actionDialogOpen}\n        onClose={() => setActionDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle>\n          Confirm {actionType} User\n        </DialogTitle>\n        <DialogContent>\n          <Typography gutterBottom>\n            Are you sure you want to {actionType} user \"{selectedUser?.username}\"?\n          </Typography>\n          <TextField\n            fullWidth\n            multiline\n            rows={3}\n            label=\"Reason (required)\"\n            value={actionReason}\n            onChange={(e) => setActionReason(e.target.value)}\n            margin=\"normal\"\n            required\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setActionDialogOpen(false)}>Cancel</Button>\n          <Button\n            onClick={executeUserAction}\n            color=\"primary\"\n            variant=\"contained\"\n            disabled={!actionReason.trim()}\n          >\n            Confirm {actionType}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Snackbar for notifications */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={() => setSnackbar({ ...snackbar, open: false })}\n      >\n        <Alert\n          onClose={() => setSnackbar({ ...snackbar, open: false })}\n          severity={snackbar.severity}\n          sx={{ width: '100%' }}\n        >\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default UsersPage;\n"], "names": ["UsersPage", "_selectedUser$profile", "_selectedUser$profile2", "_selectedUser$profile3", "_selectedUser$profile4", "_selectedUser$profile5", "_selectedUser$profile6", "_selectedUser$reputat", "_selectedUser$reputat2", "_selectedUser$verific", "_selectedUser$verific2", "_selectedUser$verific3", "_selectedUser$verific4", "_selectedUser$verific5", "_selectedUser$verific6", "_selectedUser$verific7", "_selectedUser$verific8", "_selectedUser$verific9", "_selectedUser$verific0", "_selectedUser$verific1", "_selectedUser$verific10", "users", "setUsers", "useState", "loading", "setLoading", "error", "setError", "page", "setPage", "rowsPerPage", "setRowsPerPage", "totalUsers", "setTotalUsers", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "verifiedFilter", "setVerifiedFilter", "selected<PERSON>ser", "setSelectedUser", "userDetailOpen", "setUserDetailOpen", "actionDialogOpen", "setActionDialogOpen", "actionType", "setActionType", "actionReason", "setActionReason", "snackbar", "setSnackbar", "open", "message", "severity", "fetchUsers", "async", "params", "limit", "search", "undefined", "status", "role", "verified", "sortBy", "sortOrder", "response", "axios", "get", "data", "success", "pagination", "console", "useEffect", "handleFilterChange", "filterType", "value", "handleUserAction", "user", "action", "getStatusColor", "getRoleColor", "length", "_jsx", "Box", "display", "justifyContent", "alignItems", "minHeight", "children", "Typography", "_jsxs", "variant", "gutterBottom", "<PERSON><PERSON>", "sx", "mb", "Paper", "p", "Grid", "container", "spacing", "item", "xs", "md", "TextField", "fullWidth", "placeholder", "onChange", "event", "target", "InputProps", "startAdornment", "InputAdornment", "position", "SearchIcon", "FormControl", "InputLabel", "Select", "label", "e", "MenuItem", "<PERSON><PERSON>", "startIcon", "FilterIcon", "onClick", "TableContainer", "component", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "map", "_user$profile", "_user$profile2", "_user$profile3", "_user$reputation", "_user$reputation2", "_user$verification", "_user$verification$id", "_user$verification2", "_user$verification2$i", "_user$verification3", "_user$verification3$e", "_user$verification4", "_user$verification4$p", "_user$verification5", "_user$verification5$i", "hover", "gap", "Avatar", "profile", "avatar", "src", "alt", "username", "PersonIcon", "firstName", "lastName", "color", "EmailIcon", "fontSize", "email", "PhoneIcon", "phone", "Chip", "size", "StarIcon", "reputation", "score", "totalTrades", "verification", "identity", "mr", "Date", "createdAt", "toLocaleDateString", "lastActive", "<PERSON><PERSON><PERSON>", "title", "IconButton", "ViewIcon", "BlockIcon", "includes", "VerifyIcon", "_id", "TablePagination", "rowsPerPageOptions", "count", "onPageChange", "handleChangePage", "newPage", "onRowsPerPageChange", "parseInt", "Dialog", "onClose", "max<PERSON><PERSON><PERSON>", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "location", "city", "country", "textAlign", "DialogActions", "multiline", "rows", "margin", "required", "endpoint", "concat", "reason", "documentType", "approved", "notes", "put", "_error$response", "_error$response$data", "disabled", "trim", "Snackbar", "autoHideDuration", "_objectSpread", "width"], "sourceRoot": ""}