import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

export default function SettingsScreen() {
  const navigation = useNavigation();
  const { theme, themeMode, setThemeMode } = useTheme();
  const styles = createStyles(theme);
  
  // Settings state - in a real app this would come from Convex
  const [settings, setSettings] = useState({
    notifications: {
      pushNotifications: true,
      emailNotifications: true,
      tradeAlerts: true,
      priceAlerts: false,
      securityAlerts: true,
    },
    security: {
      twoFactorAuth: false,
      biometricAuth: true,
      autoLock: true,
      autoLockTime: 5, // minutes
    },
    trading: {
      autoReply: true,
      showOnlineStatus: true,
      allowDirectMessages: true,
    },
    privacy: {
      profileVisibility: 'public', // 'public', 'verified', 'private'
      showTradingHistory: true,
      dataCollection: false,
    },
  });
  
  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value,
      },
    }));
    
    // In a real app, we would save to Convex
    // await updateUserSettingsMutation({ category, key, value });
  };
  
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            // In a real app, we would call logout from AuthContext
            Alert.alert('Logged out', 'You have been logged out successfully');
          },
        },
      ]
    );
  };
  
  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Account Deletion', 'Account deletion request submitted');
          },
        },
      ]
    );
  };
  
  const SettingSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );
  
  const SettingRow = ({ 
    icon, 
    title, 
    description, 
    value, 
    onValueChange, 
    type = 'switch',
    onPress,
    showArrow = false,
  }: {
    icon: string;
    title: string;
    description?: string;
    value?: any;
    onValueChange?: (value: any) => void;
    type?: 'switch' | 'button' | 'info';
    onPress?: () => void;
    showArrow?: boolean;
  }) => (
    <TouchableOpacity
      style={styles.settingRow}
      onPress={onPress}
      disabled={type === 'switch' || type === 'info'}
    >
      <View style={styles.settingIcon}>
        <Ionicons name={icon} size={20} color={theme.colors.primary} />
      </View>
      
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      
      <View style={styles.settingControl}>
        {type === 'switch' && (
          <Switch
            value={value}
            onValueChange={onValueChange}
            trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
            thumbColor={value ? theme.colors.primary : theme.colors.surface}
          />
        )}
        {type === 'info' && (
          <Text style={styles.settingValue}>{value}</Text>
        )}
        {(type === 'button' || showArrow) && (
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        )}
      </View>
    </TouchableOpacity>
  );
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={{ width: 24 }} />
      </View>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Theme */}
        <SettingSection title="Appearance">
          <View style={styles.themeContainer}>
            <View style={styles.themeHeader}>
              <Ionicons name="color-palette" size={20} color={theme.colors.primary} />
              <Text style={styles.themeTitle}>Theme</Text>
            </View>
            <View style={styles.themeOptions}>
              <TouchableOpacity
                style={[styles.themeOption, themeMode === 'light' && styles.themeOptionActive]}
                onPress={() => setThemeMode('light')}
              >
                <Ionicons name="sunny" size={20} color={themeMode === 'light' ? theme.colors.primary : theme.colors.textSecondary} />
                <Text style={[styles.themeOptionText, themeMode === 'light' && styles.themeOptionTextActive]}>Light</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.themeOption, themeMode === 'dark' && styles.themeOptionActive]}
                onPress={() => setThemeMode('dark')}
              >
                <Ionicons name="moon" size={20} color={themeMode === 'dark' ? theme.colors.primary : theme.colors.textSecondary} />
                <Text style={[styles.themeOptionText, themeMode === 'dark' && styles.themeOptionTextActive]}>Dark</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.themeOption, themeMode === 'system' && styles.themeOptionActive]}
                onPress={() => setThemeMode('system')}
              >
                <Ionicons name="phone-portrait" size={20} color={themeMode === 'system' ? theme.colors.primary : theme.colors.textSecondary} />
                <Text style={[styles.themeOptionText, themeMode === 'system' && styles.themeOptionTextActive]}>System</Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.themeDescription}>
              {themeMode === 'system'
                ? 'Automatically switch between light and dark themes based on your device settings'
                : `Currently using ${themeMode} theme`
              }
            </Text>
          </View>
        </SettingSection>

        {/* Notifications */}
        <SettingSection title="Notifications">
          <SettingRow
            icon="notifications"
            title="Push Notifications"
            description="Receive notifications on your device"
            value={settings.notifications.pushNotifications}
            onValueChange={(value) => updateSetting('notifications', 'pushNotifications', value)}
          />
          <SettingRow
            icon="mail"
            title="Email Notifications"
            description="Receive notifications via email"
            value={settings.notifications.emailNotifications}
            onValueChange={(value) => updateSetting('notifications', 'emailNotifications', value)}
          />
          <SettingRow
            icon="swap-horizontal"
            title="Trade Alerts"
            description="Notifications for trade activities"
            value={settings.notifications.tradeAlerts}
            onValueChange={(value) => updateSetting('notifications', 'tradeAlerts', value)}
          />
          <SettingRow
            icon="trending-up"
            title="Price Alerts"
            description="Notifications for price changes"
            value={settings.notifications.priceAlerts}
            onValueChange={(value) => updateSetting('notifications', 'priceAlerts', value)}
          />
          <SettingRow
            icon="shield-checkmark"
            title="Security Alerts"
            description="Important security notifications"
            value={settings.notifications.securityAlerts}
            onValueChange={(value) => updateSetting('notifications', 'securityAlerts', value)}
          />
        </SettingSection>
        
        {/* Security */}
        <SettingSection title="Security">
          <SettingRow
            icon="key"
            title="Two-Factor Authentication"
            description="Add an extra layer of security"
            value={settings.security.twoFactorAuth}
            onValueChange={(value) => updateSetting('security', 'twoFactorAuth', value)}
          />
          <SettingRow
            icon="finger-print"
            title="Biometric Authentication"
            description="Use fingerprint or face ID"
            value={settings.security.biometricAuth}
            onValueChange={(value) => updateSetting('security', 'biometricAuth', value)}
          />
          <SettingRow
            icon="lock-closed"
            title="Auto Lock"
            description="Automatically lock the app"
            value={settings.security.autoLock}
            onValueChange={(value) => updateSetting('security', 'autoLock', value)}
          />
          <SettingRow
            icon="time"
            title="Auto Lock Time"
            description="Time before auto lock activates"
            type="info"
            value={`${settings.security.autoLockTime} minutes`}
          />
        </SettingSection>
        
        {/* Trading */}
        <SettingSection title="Trading">
          <SettingRow
            icon="chatbubble"
            title="Auto Reply"
            description="Automatically reply to trade requests"
            value={settings.trading.autoReply}
            onValueChange={(value) => updateSetting('trading', 'autoReply', value)}
          />
          <SettingRow
            icon="radio"
            title="Show Online Status"
            description="Let others see when you're online"
            value={settings.trading.showOnlineStatus}
            onValueChange={(value) => updateSetting('trading', 'showOnlineStatus', value)}
          />
          <SettingRow
            icon="chatbubbles"
            title="Allow Direct Messages"
            description="Allow traders to message you directly"
            value={settings.trading.allowDirectMessages}
            onValueChange={(value) => updateSetting('trading', 'allowDirectMessages', value)}
          />
        </SettingSection>
        
        {/* Privacy */}
        <SettingSection title="Privacy">
          <SettingRow
            icon="eye"
            title="Profile Visibility"
            description="Who can see your profile"
            type="info"
            value={settings.privacy.profileVisibility}
            showArrow
          />
          <SettingRow
            icon="list"
            title="Show Trading History"
            description="Display your trading history publicly"
            value={settings.privacy.showTradingHistory}
            onValueChange={(value) => updateSetting('privacy', 'showTradingHistory', value)}
          />
          <SettingRow
            icon="analytics"
            title="Data Collection"
            description="Allow anonymous usage analytics"
            value={settings.privacy.dataCollection}
            onValueChange={(value) => updateSetting('privacy', 'dataCollection', value)}
          />
        </SettingSection>
        
        {/* Account */}
        <SettingSection title="Account">
          <SettingRow
            icon="person"
            title="Edit Profile"
            description="Update your profile information"
            type="button"
            onPress={() => navigation.navigate('Profile')}
            showArrow
          />
          <SettingRow
            icon="help-circle"
            title="Help & Support"
            description="Get help and contact support"
            type="button"
            onPress={() => navigation.navigate('HelpSupport')}
            showArrow
          />
          <SettingRow
            icon="document-text"
            title="Terms & Privacy"
            description="Read our terms and privacy policy"
            type="button"
            showArrow
          />
        </SettingSection>
        
        {/* Danger Zone */}
        <SettingSection title="Danger Zone">
          <SettingRow
            icon="log-out"
            title="Logout"
            description="Sign out of your account"
            type="button"
            onPress={handleLogout}
          />
          <SettingRow
            icon="trash"
            title="Delete Account"
            description="Permanently delete your account"
            type="button"
            onPress={handleDeleteAccount}
          />
        </SettingSection>
        
        <View style={styles.footer}>
          <Text style={styles.footerText}>CryptoP2P v1.0.0</Text>
          <Text style={styles.footerText}>© 2024 CryptoP2P. All rights reserved.</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: theme.colors.surface,
    marginTop: 16,
    paddingVertical: 8,
  },
  // Theme-specific styles
  themeContainer: {
    padding: 16,
  },
  themeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  themeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  themeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  themeOption: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.background,
  },
  themeOptionActive: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primaryLight,
  },
  themeOptionText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
    fontWeight: '500',
  },
  themeOptionTextActive: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  themeDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.backgroundSecondary,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.primaryLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  settingControl: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingValue: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'right',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  footerText: {
    fontSize: 12,
    color: theme.colors.textTertiary,
    textAlign: 'center',
    marginBottom: 4,
  },
});
