/**
 * Dead Letter Queue Service
 * Handles failed operations for retry and monitoring
 */

const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

class DeadLetterQueue {
  constructor() {
    this.redisClient = null;
    this.queues = {
      blockchain: 'dlq:blockchain',
      database: 'dlq:database',
      external: 'dlq:external',
      notification: 'dlq:notification'
    };
    this.maxRetries = 5;
    this.retryDelays = [1000, 5000, 15000, 60000, 300000]; // 1s, 5s, 15s, 1m, 5m
    this.isProcessing = false;
  }

  async initialize() {
    try {
      this.redisClient = getRedisClient();
      if (this.redisClient && this.redisClient.isReady) {
        logger.info('Dead Letter Queue initialized with Redis backend');
        this.startProcessing();
      } else {
        logger.warn('Dead Letter Queue initialized without Redis - using memory fallback');
        this.memoryQueue = new Map();
      }
    } catch (error) {
      logger.error('Failed to initialize Dead Letter Queue:', error);
      this.memoryQueue = new Map();
    }
  }

  /**
   * Add failed operation to dead letter queue
   */
  async addFailedOperation(queueType, operation, error, metadata = {}) {
    const failedOperation = {
      id: this.generateId(),
      queueType,
      operation: {
        type: operation.type,
        data: operation.data,
        originalTimestamp: operation.timestamp || new Date().toISOString()
      },
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code,
        timestamp: new Date().toISOString()
      },
      metadata: {
        ...metadata,
        retryCount: 0,
        maxRetries: this.maxRetries,
        nextRetryAt: new Date(Date.now() + this.retryDelays[0]).toISOString()
      },
      createdAt: new Date().toISOString(),
      status: 'pending'
    };

    try {
      if (this.redisClient && this.redisClient.isReady) {
        await this.redisClient.lPush(
          this.queues[queueType] || this.queues.external,
          JSON.stringify(failedOperation)
        );
      } else {
        // Memory fallback
        const queueName = this.queues[queueType] || this.queues.external;
        if (!this.memoryQueue.has(queueName)) {
          this.memoryQueue.set(queueName, []);
        }
        this.memoryQueue.get(queueName).push(failedOperation);
      }

      logger.warn('Operation added to dead letter queue:', {
        id: failedOperation.id,
        queueType,
        operationType: operation.type,
        error: error.message
      });

    } catch (queueError) {
      logger.error('Failed to add operation to dead letter queue:', queueError);
    }
  }

  /**
   * Process dead letter queue items
   */
  async processQueue(queueType) {
    const queueName = this.queues[queueType];
    if (!queueName) return;

    try {
      let operation;
      
      if (this.redisClient && this.redisClient.isReady) {
        const item = await this.redisClient.rPop(queueName);
        if (item) {
          operation = JSON.parse(item);
        }
      } else if (this.memoryQueue && this.memoryQueue.has(queueName)) {
        const queue = this.memoryQueue.get(queueName);
        operation = queue.shift();
      }

      if (!operation) return;

      // Check if it's time to retry
      if (new Date() < new Date(operation.metadata.nextRetryAt)) {
        // Put it back in the queue
        await this.requeueOperation(queueType, operation);
        return;
      }

      // Check if max retries exceeded
      if (operation.metadata.retryCount >= operation.metadata.maxRetries) {
        await this.moveToFailedQueue(operation);
        return;
      }

      // Attempt to retry the operation
      await this.retryOperation(operation);

    } catch (error) {
      logger.error(`Error processing ${queueType} dead letter queue:`, error);
    }
  }

  /**
   * Retry a failed operation
   */
  async retryOperation(operation) {
    try {
      logger.info(`Retrying operation ${operation.id} (attempt ${operation.metadata.retryCount + 1})`);

      let success = false;
      
      switch (operation.operation.type) {
        case 'blockchain_transaction':
          success = await this.retryBlockchainOperation(operation);
          break;
        case 'database_operation':
          success = await this.retryDatabaseOperation(operation);
          break;
        case 'external_api_call':
          success = await this.retryExternalApiCall(operation);
          break;
        case 'notification':
          success = await this.retryNotification(operation);
          break;
        default:
          logger.warn(`Unknown operation type: ${operation.operation.type}`);
          success = false;
      }

      if (success) {
        logger.info(`Operation ${operation.id} retried successfully`);
        await this.markAsCompleted(operation);
      } else {
        await this.scheduleNextRetry(operation);
      }

    } catch (error) {
      logger.error(`Failed to retry operation ${operation.id}:`, error);
      await this.scheduleNextRetry(operation);
    }
  }

  /**
   * Schedule next retry attempt
   */
  async scheduleNextRetry(operation) {
    operation.metadata.retryCount++;
    
    if (operation.metadata.retryCount >= operation.metadata.maxRetries) {
      await this.moveToFailedQueue(operation);
      return;
    }

    const delayIndex = Math.min(operation.metadata.retryCount - 1, this.retryDelays.length - 1);
    const delay = this.retryDelays[delayIndex];
    operation.metadata.nextRetryAt = new Date(Date.now() + delay).toISOString();

    await this.requeueOperation(operation.queueType, operation);
  }

  /**
   * Requeue operation for later retry
   */
  async requeueOperation(queueType, operation) {
    const queueName = this.queues[queueType];
    
    try {
      if (this.redisClient && this.redisClient.isReady) {
        await this.redisClient.lPush(queueName, JSON.stringify(operation));
      } else if (this.memoryQueue) {
        if (!this.memoryQueue.has(queueName)) {
          this.memoryQueue.set(queueName, []);
        }
        this.memoryQueue.get(queueName).push(operation);
      }
    } catch (error) {
      logger.error('Failed to requeue operation:', error);
    }
  }

  /**
   * Move operation to permanent failed queue
   */
  async moveToFailedQueue(operation) {
    operation.status = 'failed';
    operation.finalFailureAt = new Date().toISOString();

    try {
      const failedQueueName = `${this.queues[operation.queueType]}:failed`;
      
      if (this.redisClient && this.redisClient.isReady) {
        await this.redisClient.lPush(failedQueueName, JSON.stringify(operation));
      } else if (this.memoryQueue) {
        if (!this.memoryQueue.has(failedQueueName)) {
          this.memoryQueue.set(failedQueueName, []);
        }
        this.memoryQueue.get(failedQueueName).push(operation);
      }

      logger.error(`Operation ${operation.id} permanently failed after ${operation.metadata.retryCount} attempts`);

    } catch (error) {
      logger.error('Failed to move operation to failed queue:', error);
    }
  }

  /**
   * Mark operation as completed
   */
  async markAsCompleted(operation) {
    operation.status = 'completed';
    operation.completedAt = new Date().toISOString();

    // Optionally store completed operations for audit
    if (process.env.STORE_COMPLETED_DLQ_OPERATIONS === 'true') {
      try {
        const completedQueueName = `${this.queues[operation.queueType]}:completed`;
        
        if (this.redisClient && this.redisClient.isReady) {
          await this.redisClient.lPush(completedQueueName, JSON.stringify(operation));
          // Keep only last 1000 completed operations
          await this.redisClient.lTrim(completedQueueName, 0, 999);
        }
      } catch (error) {
        logger.error('Failed to store completed operation:', error);
      }
    }
  }

  /**
   * Start background processing
   */
  startProcessing() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    
    // Process each queue type every 30 seconds
    setInterval(async () => {
      for (const queueType of Object.keys(this.queues)) {
        await this.processQueue(queueType);
      }
    }, 30000);

    logger.info('Dead Letter Queue processing started');
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    const stats = {};
    
    try {
      for (const [queueType, queueName] of Object.entries(this.queues)) {
        if (this.redisClient && this.redisClient.isReady) {
          const pending = await this.redisClient.lLen(queueName);
          const failed = await this.redisClient.lLen(`${queueName}:failed`);
          const completed = await this.redisClient.lLen(`${queueName}:completed`);
          
          stats[queueType] = { pending, failed, completed };
        } else if (this.memoryQueue) {
          const pending = this.memoryQueue.get(queueName)?.length || 0;
          const failed = this.memoryQueue.get(`${queueName}:failed`)?.length || 0;
          const completed = this.memoryQueue.get(`${queueName}:completed`)?.length || 0;
          
          stats[queueType] = { pending, failed, completed };
        }
      }
    } catch (error) {
      logger.error('Failed to get queue stats:', error);
    }
    
    return stats;
  }

  /**
   * Generate unique ID for operations
   */
  generateId() {
    return `dlq_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Retry methods for specific operation types
  async retryBlockchainOperation(operation) {
    try {
      const { data } = operation;
      logger.info(`Retrying blockchain operation: ${data.type}`, { operationId: operation.id });

      switch (data.type) {
        case 'transaction':
          return await this.retryTransaction(data);
        case 'escrow_create':
          return await this.retryEscrowCreation(data);
        case 'escrow_release':
          return await this.retryEscrowRelease(data);
        case 'balance_update':
          return await this.retryBalanceUpdate(data);
        default:
          logger.warn(`Unknown blockchain operation type: ${data.type}`);
          return false;
      }
    } catch (error) {
      logger.error(`Blockchain operation retry failed: ${error.message}`, { operationId: operation.id });
      return false;
    }
  }

  async retryDatabaseOperation(operation) {
    try {
      const { data } = operation;
      logger.info(`Retrying database operation: ${data.type}`, { operationId: operation.id });

      switch (data.type) {
        case 'user_update':
          return await this.retryUserUpdate(data);
        case 'trade_update':
          return await this.retryTradeUpdate(data);
        case 'wallet_update':
          return await this.retryWalletUpdate(data);
        case 'audit_log':
          return await this.retryAuditLog(data);
        default:
          logger.warn(`Unknown database operation type: ${data.type}`);
          return false;
      }
    } catch (error) {
      logger.error(`Database operation retry failed: ${error.message}`, { operationId: operation.id });
      return false;
    }
  }

  async retryExternalApiCall(operation) {
    try {
      const { data } = operation;
      logger.info(`Retrying external API call: ${data.service}`, { operationId: operation.id });

      switch (data.service) {
        case 'price_feed':
          return await this.retryPriceFeed(data);
        case 'notification_service':
          return await this.retryNotificationService(data);
        case 'blockchain_rpc':
          return await this.retryBlockchainRPC(data);
        case 'file_upload':
          return await this.retryFileUpload(data);
        default:
          logger.warn(`Unknown external service: ${data.service}`);
          return false;
      }
    } catch (error) {
      logger.error(`External API retry failed: ${error.message}`, { operationId: operation.id });
      return false;
    }
  }

  async retryNotification(operation) {
    try {
      const { data } = operation;
      logger.info(`Retrying notification: ${data.type}`, { operationId: operation.id });

      switch (data.type) {
        case 'push_notification':
          return await this.retryPushNotification(data);
        case 'email':
          return await this.retryEmail(data);
        case 'sms':
          return await this.retrySMS(data);
        case 'websocket':
          return await this.retryWebSocketMessage(data);
        default:
          logger.warn(`Unknown notification type: ${data.type}`);
          return false;
      }
    } catch (error) {
      logger.error(`Notification retry failed: ${error.message}`, { operationId: operation.id });
      return false;
    }
  }
  // Specific retry implementations for blockchain operations
  async retryTransaction(data) {
    const transactionService = require('./transactionService');
    return await transactionService.sendTransaction(data.userId, data.transactionData);
  }

  async retryEscrowCreation(data) {
    const escrowService = require('./escrowService');
    return await escrowService.createEscrow(
      data.tradeId, data.buyerUserId, data.sellerUserId,
      data.amount, data.cryptocurrency, data.network
    );
  }

  async retryEscrowRelease(data) {
    const escrowService = require('./escrowService');
    return await escrowService.releaseEscrow(data.tradeId, data.releasedBy);
  }

  async retryBalanceUpdate(data) {
    const walletService = require('./walletService');
    return await walletService.updateBalances(data.userId);
  }

  // Specific retry implementations for database operations
  async retryUserUpdate(data) {
    const User = require('../models/User');
    return await User.findByIdAndUpdate(data.userId, data.updateData, { new: true });
  }

  async retryTradeUpdate(data) {
    const Trade = require('../models/Trade');
    return await Trade.findByIdAndUpdate(data.tradeId, data.updateData, { new: true });
  }

  async retryWalletUpdate(data) {
    const Wallet = require('../models/Wallet');
    return await Wallet.findOneAndUpdate(
      { user: data.userId }, data.updateData, { new: true }
    );
  }

  async retryAuditLog(data) {
    const { createAuditLog } = require('../middleware/auditLogger');
    return await createAuditLog(data.auditData);
  }

  // Specific retry implementations for external API calls
  async retryPriceFeed(data) {
    const priceService = require('./priceService');
    return await priceService.getPrice(data.cryptocurrency, data.fiatCurrency);
  }

  async retryNotificationService(data) {
    // Implement notification service retry logic
    const axios = require('axios');
    return await axios.post(data.url, data.payload, {
      headers: data.headers,
      timeout: 10000
    });
  }

  async retryBlockchainRPC(data) {
    const axios = require('axios');
    return await axios.post(data.rpcUrl, data.rpcPayload, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    });
  }

  async retryFileUpload(data) {
    const cloudinary = require('cloudinary').v2;
    return await cloudinary.uploader.upload(data.filePath, data.options);
  }

  // Specific retry implementations for notifications
  async retryPushNotification(data) {
    // Implement push notification retry using Firebase or similar
    const admin = require('firebase-admin');
    if (admin.apps.length === 0) {
      logger.warn('Firebase not initialized, skipping push notification retry');
      return false;
    }

    return await admin.messaging().send(data.message);
  }

  async retryEmail(data) {
    const nodemailer = require('nodemailer');
    const transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });

    return await transporter.sendMail(data.mailOptions);
  }

  async retrySMS(data) {
    // Implement SMS retry logic using Twilio or similar
    logger.info('SMS retry not implemented - would use Twilio or similar service');
    return false;
  }

  async retryWebSocketMessage(data) {
    const socketService = require('./socketService');
    if (socketService.io) {
      socketService.io.to(data.room).emit(data.event, data.payload);
      return true;
    }
    return false;
  }
}

// Create singleton instance
const deadLetterQueue = new DeadLetterQueue();

module.exports = {
  DeadLetterQueue,
  deadLetterQueue
};
