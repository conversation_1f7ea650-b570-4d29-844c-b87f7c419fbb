#!/bin/bash

# KryptoPesa Production Readiness Check
# Comprehensive validation for production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Test counters
CRITICAL_ISSUES=0
WARNINGS=0
PASSED_CHECKS=0

# Function to record test results
record_result() {
    if [ $1 -eq 0 ]; then
        print_success "$2"
        ((PASSED_CHECKS++))
    elif [ $1 -eq 2 ]; then
        print_warning "$2"
        ((WARNINGS++))
    else
        print_error "$2"
        ((CRITICAL_ISSUES++))
    fi
}

# Check environment variables
check_environment() {
    print_header "Environment Configuration Check"
    
    # Critical environment variables
    critical_vars=(
        "NODE_ENV"
        "MONGODB_URI"
        "REDIS_URL"
        "JWT_SECRET"
        "POLYGON_RPC_URL"
        "ETHEREUM_RPC_URL"
    )
    
    for var in "${critical_vars[@]}"; do
        if [ -z "${!var}" ]; then
            record_result 1 "$var is not set"
        else
            record_result 0 "$var is configured"
        fi
    done
    
    # Check JWT secret strength
    if [ ${#JWT_SECRET} -lt 32 ]; then
        record_result 1 "JWT_SECRET is too short (minimum 32 characters)"
    else
        record_result 0 "JWT_SECRET has adequate length"
    fi
    
    # Check for development URLs in production
    if [ "$NODE_ENV" = "production" ]; then
        if [[ "$MONGODB_URI" == *"localhost"* ]] || [[ "$MONGODB_URI" == *"127.0.0.1"* ]]; then
            record_result 1 "MONGODB_URI contains localhost in production"
        else
            record_result 0 "MONGODB_URI is production-ready"
        fi
        
        if [[ "$REDIS_URL" == *"localhost"* ]] || [[ "$REDIS_URL" == *"127.0.0.1"* ]]; then
            record_result 1 "REDIS_URL contains localhost in production"
        else
            record_result 0 "REDIS_URL is production-ready"
        fi
    fi
}

# Check dependencies
check_dependencies() {
    print_header "Dependencies Check"
    
    # Check Node.js version
    node_version=$(node --version | cut -d'v' -f2)
    if [ "$(printf '%s\n' "18.0.0" "$node_version" | sort -V | head -n1)" = "18.0.0" ]; then
        record_result 0 "Node.js version is adequate ($node_version)"
    else
        record_result 1 "Node.js version is too old ($node_version, minimum 18.0.0)"
    fi
    
    # Check if all packages are installed
    if [ -f "backend/package.json" ]; then
        cd backend
        if npm list --depth=0 &> /dev/null; then
            record_result 0 "Backend dependencies are installed"
        else
            record_result 1 "Backend dependencies are missing or have conflicts"
        fi
        cd ..
    fi
    
    if [ -f "mobile/package.json" ]; then
        cd mobile
        if npm list --depth=0 &> /dev/null; then
            record_result 0 "Mobile dependencies are installed"
        else
            record_result 1 "Mobile dependencies are missing or have conflicts"
        fi
        cd ..
    fi
}

# Check security configurations
check_security() {
    print_header "Security Configuration Check"
    
    # Check for security middleware files
    if [ -f "backend/src/middleware/advancedRateLimit.js" ]; then
        record_result 0 "Advanced rate limiting middleware present"
    else
        record_result 1 "Advanced rate limiting middleware missing"
    fi
    
    if [ -f "backend/src/middleware/securityValidation.js" ]; then
        record_result 0 "Security validation middleware present"
    else
        record_result 1 "Security validation middleware missing"
    fi
    
    if [ -f "backend/src/services/keyManagement/secureKeyService.js" ]; then
        record_result 0 "Secure key management service present"
    else
        record_result 1 "Secure key management service missing"
    fi
    
    # Check for dangerous patterns in code
    if grep -r "process.env.*PRIVATE_KEY" backend/src/ --exclude-dir=keyManagement 2>/dev/null; then
        record_result 1 "Direct private key access found in code"
    else
        record_result 0 "No direct private key access found"
    fi
    
    # Check for console.log in production code
    if [ "$NODE_ENV" = "production" ]; then
        console_logs=$(find backend/src mobile/src -name "*.js" -exec grep -l "console\.log" {} \; 2>/dev/null | wc -l)
        if [ $console_logs -gt 0 ]; then
            record_result 2 "$console_logs files contain console.log statements"
        else
            record_result 0 "No console.log statements found"
        fi
    fi
}

# Check smart contract security
check_smart_contracts() {
    print_header "Smart Contract Security Check"
    
    if [ -f "smart-contracts/contracts/KryptoPesaEscrow.sol" ]; then
        # Check for reentrancy protection
        if grep -q "ReentrancyGuard" smart-contracts/contracts/KryptoPesaEscrow.sol; then
            record_result 0 "Reentrancy protection implemented"
        else
            record_result 1 "Reentrancy protection missing"
        fi
        
        # Check for access control
        if grep -q "Ownable" smart-contracts/contracts/KryptoPesaEscrow.sol; then
            record_result 0 "Access control implemented"
        else
            record_result 1 "Access control missing"
        fi
        
        # Check for pausable functionality
        if grep -q "Pausable" smart-contracts/contracts/KryptoPesaEscrow.sol; then
            record_result 0 "Emergency pause functionality implemented"
        else
            record_result 2 "Emergency pause functionality missing"
        fi
        
        # Check for proper status updates
        if grep -q "trade.status = TradeStatus.COMPLETED" smart-contracts/contracts/KryptoPesaEscrow.sol; then
            record_result 0 "Proper status updates implemented"
        else
            record_result 1 "Status update pattern may have race conditions"
        fi
    else
        record_result 1 "Smart contract file not found"
    fi
}

# Check database configuration
check_database() {
    print_header "Database Configuration Check"
    
    # Check for transaction handling
    if grep -r "startSession\|withTransaction" backend/src/ &> /dev/null; then
        record_result 0 "Database transactions implemented"
    else
        record_result 1 "Database transactions missing"
    fi
    
    # Check for proper indexing
    if grep -r "index.*true" backend/src/models/ &> /dev/null; then
        record_result 0 "Database indexes configured"
    else
        record_result 2 "Database indexes may need review"
    fi
    
    # Check for connection pooling
    if grep -r "maxPoolSize\|minPoolSize" backend/src/ &> /dev/null; then
        record_result 0 "Database connection pooling configured"
    else
        record_result 2 "Database connection pooling may need configuration"
    fi
}

# Check mobile app optimization
check_mobile_optimization() {
    print_header "Mobile App Optimization Check"
    
    # Check for memory optimization
    if [ -f "mobile/src/store/middleware/memoryOptimizer.js" ]; then
        record_result 0 "Memory optimization middleware present"
    else
        record_result 1 "Memory optimization middleware missing"
    fi
    
    # Check for error boundaries
    if [ -f "mobile/src/components/ErrorBoundary.js" ]; then
        record_result 0 "Error boundary component present"
    else
        record_result 1 "Error boundary component missing"
    fi
    
    # Check bundle optimization
    if grep -q "minifierConfig" mobile/metro.config.js 2>/dev/null; then
        record_result 0 "Bundle optimization configured"
    else
        record_result 2 "Bundle optimization may need configuration"
    fi
    
    # Check for proper cleanup in useEffect
    cleanup_count=$(grep -r "return.*clearInterval\|return.*removeEventListener\|return.*unsubscribe" mobile/src/ 2>/dev/null | wc -l)
    if [ $cleanup_count -gt 5 ]; then
        record_result 0 "Proper cleanup patterns found"
    else
        record_result 2 "May need more cleanup patterns in useEffect hooks"
    fi
}

# Check testing coverage
check_testing() {
    print_header "Testing Coverage Check"
    
    # Check for test files
    test_files=$(find . -name "*.test.js" -o -name "*.spec.js" 2>/dev/null | wc -l)
    if [ $test_files -gt 10 ]; then
        record_result 0 "Adequate test coverage ($test_files test files)"
    elif [ $test_files -gt 5 ]; then
        record_result 2 "Moderate test coverage ($test_files test files)"
    else
        record_result 1 "Insufficient test coverage ($test_files test files)"
    fi
    
    # Check for integration tests
    if [ -f "scripts/test-system-integration.sh" ]; then
        record_result 0 "Integration test script present"
    else
        record_result 2 "Integration test script missing"
    fi
    
    # Check for security audit script
    if [ -f "scripts/security-audit.sh" ]; then
        record_result 0 "Security audit script present"
    else
        record_result 1 "Security audit script missing"
    fi
}

# Check documentation
check_documentation() {
    print_header "Documentation Check"
    
    # Check for essential documentation
    docs=(
        "README.md"
        "CHANGELOG.md"
        "docs/DEPLOYMENT_GUIDE.md"
        "docs/API_DOCUMENTATION.md"
    )
    
    for doc in "${docs[@]}"; do
        if [ -f "$doc" ]; then
            record_result 0 "$doc present"
        else
            record_result 2 "$doc missing"
        fi
    done
}

# Main execution
main() {
    print_header "KryptoPesa Production Readiness Check"
    print_info "Validating system for production deployment..."
    echo
    
    # Load environment variables if .env exists
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    check_environment
    check_dependencies
    check_security
    check_smart_contracts
    check_database
    check_mobile_optimization
    check_testing
    check_documentation
    
    # Print summary
    print_header "Production Readiness Summary"
    echo -e "Passed Checks: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Warnings: ${YELLOW}$WARNINGS${NC}"
    echo -e "Critical Issues: ${RED}$CRITICAL_ISSUES${NC}"
    echo
    
    if [ $CRITICAL_ISSUES -eq 0 ]; then
        print_success "System is ready for production deployment!"
        if [ $WARNINGS -gt 0 ]; then
            print_warning "Please review warnings for optimal production setup"
        fi
        echo
        print_info "Next steps:"
        echo "1. Run security audit: ./scripts/security-audit.sh"
        echo "2. Run integration tests: ./scripts/test-system-integration.sh"
        echo "3. Deploy to staging environment"
        echo "4. Conduct user acceptance testing"
        echo "5. Deploy to production"
        exit 0
    else
        print_error "Critical issues must be resolved before production deployment"
        echo
        print_info "Please fix the critical issues and run this check again"
        exit 1
    fi
}

# Run main function
main "$@"
