/**
 * Jest Global Setup
 * Common setup for all tests
 */

const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');
const Redis = require('ioredis-mock');

// Global test configuration
global.testConfig = {
  mongoServer: null,
  redisClient: null,
  testTimeout: 30000,
  apiBaseUrl: 'http://localhost:3001'
};

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.JWT_EXPIRES_IN = '24h';
process.env.BCRYPT_SALT_ROUNDS = '10';
process.env.RATE_LIMIT_WINDOW_MS = '900000';
process.env.RATE_LIMIT_MAX_REQUESTS = '100';

// Mock external services
jest.mock('ethers', () => ({
  JsonRpcProvider: jest.fn(),
  Wallet: jest.fn(),
  Contract: jest.fn(),
  isAddress: jest.fn(),
  parseEther: jest.fn(),
  formatEther: jest.fn(),
  verifyMessage: jest.fn(),
  getDefaultProvider: jest.fn(),
}));

// Mock Redis
jest.mock('ioredis', () => require('ioredis-mock'));

// Mock logger to prevent console spam during tests
jest.mock('../../src/utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
}));

// Mock file system operations
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  writeFileSync: jest.fn(),
  readFileSync: jest.fn(),
  existsSync: jest.fn().mockReturnValue(true),
  mkdirSync: jest.fn(),
}));

// Global setup before all tests
beforeAll(async () => {
  // Start in-memory MongoDB
  global.testConfig.mongoServer = await MongoMemoryServer.create();
  const mongoUri = global.testConfig.mongoServer.getUri();
  process.env.MONGODB_URI = mongoUri;
  
  // Setup Redis mock
  global.testConfig.redisClient = new Redis();
  
  // Set test timeouts
  jest.setTimeout(global.testConfig.testTimeout);
  
  console.log('🧪 Test environment initialized');
  console.log(`📦 MongoDB URI: ${mongoUri}`);
});

// Global cleanup after all tests
afterAll(async () => {
  // Close MongoDB connection
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
  
  // Stop MongoDB server
  if (global.testConfig.mongoServer) {
    await global.testConfig.mongoServer.stop();
  }
  
  // Close Redis connection
  if (global.testConfig.redisClient) {
    global.testConfig.redisClient.disconnect();
  }
  
  console.log('🧹 Test environment cleaned up');
});

// Global test utilities
global.testUtils = {
  // Create test user
  createTestUser: (overrides = {}) => ({
    walletAddress: '******************************************',
    profile: {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '+254712345678',
      country: 'KE',
      city: 'Nairobi'
    },
    isActive: true,
    createdAt: new Date(),
    ...overrides
  }),
  
  // Create test trade
  createTestTrade: (overrides = {}) => ({
    seller: new mongoose.Types.ObjectId(),
    buyer: new mongoose.Types.ObjectId(),
    offer: new mongoose.Types.ObjectId(),
    cryptocurrency: {
      symbol: 'BTC',
      network: 'bitcoin',
      amount: 0.001
    },
    fiatAmount: 50.00,
    fiatCurrency: 'USD',
    paymentMethod: {
      method: 'mpesa',
      details: {
        phoneNumber: '+254712345678',
        name: 'Test User'
      }
    },
    status: 'pending',
    createdAt: new Date(),
    ...overrides
  }),
  
  // Create test offer
  createTestOffer: (overrides = {}) => ({
    creator: new mongoose.Types.ObjectId(),
    type: 'sell',
    cryptocurrency: {
      symbol: 'BTC',
      network: 'bitcoin'
    },
    amount: {
      min: 0.001,
      max: 0.1
    },
    pricing: {
      type: 'market_percentage',
      value: 2.0
    },
    paymentMethods: [{
      method: 'mpesa',
      details: {
        phoneNumber: '+254712345678',
        name: 'Test User'
      }
    }],
    terms: 'Test trading terms',
    location: {
      country: 'KE',
      city: 'Nairobi'
    },
    isActive: true,
    createdAt: new Date(),
    ...overrides
  }),
  
  // Generate test JWT token
  generateTestToken: (payload = {}) => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      {
        userId: new mongoose.Types.ObjectId(),
        walletAddress: '******************************************',
        ...payload
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );
  },
  
  // Wait for async operations
  waitFor: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Clean database collections
  cleanDatabase: async () => {
    if (mongoose.connection.readyState === 1) {
      const collections = mongoose.connection.collections;
      for (const key in collections) {
        await collections[key].deleteMany({});
      }
    }
  },
  
  // Mock external API responses
  mockExternalAPI: {
    coinGecko: {
      success: {
        bitcoin: { usd: 45000 },
        ethereum: { usd: 3000 }
      },
      error: new Error('API rate limit exceeded')
    },
    blockchain: {
      success: {
        balance: '1000000000000000000', // 1 ETH in wei
        transactions: []
      },
      error: new Error('Network error')
    }
  }
};

// Custom matchers
expect.extend({
  toBeValidObjectId(received) {
    const pass = mongoose.Types.ObjectId.isValid(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid ObjectId`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid ObjectId`,
        pass: false,
      };
    }
  },
  
  toBeValidEthereumAddress(received) {
    const pass = /^0x[a-fA-F0-9]{40}$/.test(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid Ethereum address`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid Ethereum address`,
        pass: false,
      };
    }
  },
  
  toHaveValidTimestamp(received) {
    const pass = received instanceof Date && !isNaN(received.getTime());
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid timestamp`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid timestamp`,
        pass: false,
      };
    }
  }
});

// Error handling for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Suppress console warnings during tests
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  // Suppress specific warnings that are expected during tests
  const message = args[0];
  if (typeof message === 'string') {
    if (message.includes('DeprecationWarning') || 
        message.includes('ExperimentalWarning')) {
      return;
    }
  }
  originalConsoleWarn.apply(console, args);
};
