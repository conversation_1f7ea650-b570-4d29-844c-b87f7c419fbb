const User = require('../../models/User');
const bcrypt = require('bcryptjs');

describe('User Model', () => {
  describe('User Creation', () => {
    it('should create a valid user', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000000',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser._id).toBeDefined();
      expect(savedUser.username).toBe(userData.username);
      expect(savedUser.email).toBe(userData.email);
      expect(savedUser.phone).toBe(userData.phone);
      expect(savedUser.profile.firstName).toBe(userData.profile.firstName);
      expect(savedUser.profile.lastName).toBe(userData.profile.lastName);
      expect(savedUser.role).toBe('user'); // default role
      expect(savedUser.status).toBe('active'); // default status
    });

    it('should hash password before saving', async () => {
      const userData = {
        username: 'testuser2',
        email: '<EMAIL>',
        password: 'plainpassword',
        phone: '+254700000001',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);
      await user.save();

      expect(user.password).not.toBe('plainpassword');
      expect(user.password.length).toBeGreaterThan(20);
      
      // Verify password can be compared
      const isMatch = await bcrypt.compare('plainpassword', user.password);
      expect(isMatch).toBe(true);
    });

    it('should require username, email, password, and phone', async () => {
      const user = new User({});
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.username).toBeDefined();
      expect(error.errors.email).toBeDefined();
      expect(error.errors.password).toBeDefined();
      expect(error.errors.phone).toBeDefined();
    });

    it('should enforce unique username', async () => {
      const userData1 = {
        username: 'uniqueuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000001',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const userData2 = {
        username: 'uniqueuser', // same username
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000002',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      await new User(userData1).save();

      let error;
      try {
        await new User(userData2).save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.code).toBe(11000); // MongoDB duplicate key error
    });

    it('should enforce unique email', async () => {
      const userData1 = {
        username: 'user1',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000001',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const userData2 = {
        username: 'user2',
        email: '<EMAIL>', // same email
        password: 'password123',
        phone: '+254700000002',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      await new User(userData1).save();

      let error;
      try {
        await new User(userData2).save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.code).toBe(11000); // MongoDB duplicate key error
    });

    it('should validate email format', async () => {
      const userData = {
        username: 'testuser',
        email: 'invalid-email',
        password: 'password123',
        phone: '+254700000000'
      };

      const user = new User(userData);
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.email).toBeDefined();
    });

    it('should validate phone format', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: 'invalid-phone'
      };

      const user = new User(userData);
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.phone).toBeDefined();
    });
  });

  describe('Password Security', () => {
    it('should never store passwords in plaintext', async () => {
      const plainPassword = 'MySecurePassword123!';
      const userData = {
        username: 'securitytest',
        email: '<EMAIL>',
        password: plainPassword,
        phone: '+254700000002',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);
      await user.save();

      // Password should be hashed
      expect(user.password).not.toBe(plainPassword);
      expect(user.password).toMatch(/^\$2[aby]\$\d+\$/); // bcrypt hash pattern
      expect(user.password.length).toBeGreaterThan(50); // bcrypt hashes are typically 60 chars
    });

    it('should use proper bcrypt rounds', async () => {
      const userData = {
        username: 'roundstest',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        phone: '+254700000003',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);
      await user.save();

      // Extract rounds from bcrypt hash
      const hashParts = user.password.split('$');
      const rounds = parseInt(hashParts[2]);

      // Should use at least 1 round (test environment setting)
      const expectedRounds = parseInt(process.env.BCRYPT_ROUNDS) || 1;
      expect(rounds).toBeGreaterThanOrEqual(expectedRounds);
    });

    it('should rehash password when changed', async () => {
      const user = await createTestUser();
      const originalHash = user.password;

      // Change password
      user.password = 'NewPassword123!';
      await user.save();

      // Hash should be different
      expect(user.password).not.toBe(originalHash);
      expect(user.password).toMatch(/^\$2[aby]\$\d+\$/);
    });

    it('should not rehash password when not modified', async () => {
      const user = await createTestUser();
      const originalHash = user.password;

      // Update other field
      user.profile.firstName = 'Updated';
      await user.save();

      // Hash should remain the same
      expect(user.password).toBe(originalHash);
    });

    it('should handle password comparison edge cases', async () => {
      const user = await createTestUser();
      // Need to select password field for comparison
      const userWithPassword = await User.findById(user._id).select('+password');

      // Test with null/undefined
      try {
        const nullMatch = await userWithPassword.comparePassword(null);
        expect(nullMatch).toBe(false);
      } catch (error) {
        // bcrypt may throw errors for invalid inputs, which is acceptable
        expect(error).toBeDefined();
      }

      try {
        const undefinedMatch = await userWithPassword.comparePassword(undefined);
        expect(undefinedMatch).toBe(false);
      } catch (error) {
        expect(error).toBeDefined();
      }

      // Test with empty string
      const emptyMatch = await userWithPassword.comparePassword('');
      expect(emptyMatch).toBe(false);

      // Test with very long string
      const longPassword = 'a'.repeat(1000);
      const longMatch = await userWithPassword.comparePassword(longPassword);
      expect(longMatch).toBe(false);
    });
  });

  describe('User Methods', () => {
    let user;

    beforeEach(async () => {
      // Create user with plaintext password for testing
      const userData = {
        username: 'methodstest' + Date.now(),
        email: 'methodstest' + Date.now() + '@example.com',
        password: 'password123',
        phone: '+254700000000',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        },
        verification: {
          email: { verified: true },
          phone: { verified: true }
        }
      };

      user = new User(userData);
      await user.save();
      // Get user with password field for comparison tests
      user = await User.findById(user._id).select('+password');
    });

    it('should compare password correctly', async () => {
      const isMatch = await user.comparePassword('password123');
      expect(isMatch).toBe(true);

      const isNotMatch = await user.comparePassword('wrongpassword');
      expect(isNotMatch).toBe(false);
    });

    it('should update reputation score', async () => {
      expect(user.reputation.score).toBe(0); // default score

      // Complete a trade to update reputation
      await user.updateReputation(true, false, false, 5);
      expect(user.reputation.score).toBeGreaterThan(0);
      expect(user.reputation.totalTrades).toBe(1);
      expect(user.reputation.completedTrades).toBe(1);
      expect(user.reputation.averageRating).toBe(5);
      expect(user.reputation.totalRatings).toBe(1);
    });

    it('should get full name', () => {
      user.profile.firstName = 'John';
      user.profile.lastName = 'Doe';
      
      expect(user.getFullName()).toBe('John Doe');
    });

    it('should check if user is verified', () => {
      expect(user.isVerified()).toBe(true); // created with verified status

      user.verification.email.verified = false;
      expect(user.isVerified()).toBe(false);
    });

    it('should update last active timestamp', async () => {
      const originalLastActive = user.lastActive;
      
      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));
      
      user.updateLastActive();
      expect(user.lastActive.getTime()).toBeGreaterThan(originalLastActive.getTime());
    });
  });

  describe('User Validation', () => {
    it('should validate role enum', async () => {
      const userData = {
        username: 'rolevalidationuser' + Date.now(), // Make username unique
        email: 'rolevalidation' + Date.now() + '@example.com',
        password: 'password123',
        phone: '+254700000099',
        role: 'invalid-role',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);

      // Test validation without saving
      const validationError = user.validateSync();
      expect(validationError).toBeDefined();
      expect(validationError.errors.role).toBeDefined();
    });

    it('should validate status enum', async () => {
      const userData = {
        username: 'statusvalidationuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000098',
        status: 'invalid-status',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.status).toBeDefined();
    });
  });

  describe('User Indexes', () => {
    it('should have proper indexes', async () => {
      const indexes = await User.collection.getIndexes();
      
      // Check for username index
      expect(indexes).toHaveProperty('username_1');
      
      // Check for email index
      expect(indexes).toHaveProperty('email_1');
      
      // Check for phone index
      expect(indexes).toHaveProperty('phone_1');
    });
  });
});
