// Export all services and utilities (excluding apiClient to avoid early initialization)
// export * from './api'; // Commented out to prevent early apiClient initialization
export * from './walletAuthService';
export * from './tradingService';
export * from './walletService';
export * from './userService';
export * from './websocketService';

// Re-export specific items from api.ts (excluding apiClient to avoid early initialization)
export { authTokens, API_ENDPOINTS, EAST_AFRICAN_CONFIG } from './api';
export type { ApiResponse, PaginatedResponse } from './api';

// Note: apiClient is not exported from index to avoid early initialization
// Individual services handle their own lazy loading of apiClient
// If you need direct API access, import { apiClient } from './api' directly
export { walletAuthService, walletAuthHelpers } from './walletAuthService';
export { tradingService, eastAfricanHelpers } from './tradingService';
export { walletService, walletUtils } from './walletService';
export { userService, userUtils } from './userService';
export { websocketService, useWebSocket } from './websocketService';

// Service configuration
export const SERVICES_CONFIG = {
  API_TIMEOUT: 30000, // 30 seconds
  WEBSOCKET_RECONNECT_ATTEMPTS: 5,
  WEBSOCKET_RECONNECT_DELAY: 1000,
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
};

// Service status types
export interface ServiceStatus {
  api: boolean;
  websocket: boolean;
  lastCheck: string;
}

// Service health checker
export class ServiceHealthChecker {
  private static instance: ServiceHealthChecker;
  private status: ServiceStatus = {
    api: false,
    websocket: false,
    lastCheck: new Date().toISOString(),
  };

  static getInstance(): ServiceHealthChecker {
    if (!ServiceHealthChecker.instance) {
      ServiceHealthChecker.instance = new ServiceHealthChecker();
    }
    return ServiceHealthChecker.instance;
  }

  async checkApiHealth(): Promise<boolean> {
    try {
      const { apiClient: client } = require('./api');
      const response = await client.get('/system/health');
      this.status.api = response.success;
      this.status.lastCheck = new Date().toISOString();
      return this.status.api;
    } catch (error) {
      console.error('API health check failed:', error);
      this.status.api = false;
      this.status.lastCheck = new Date().toISOString();
      return false;
    }
  }

  checkWebSocketHealth(): boolean {
    this.status.websocket = websocketService.isConnected();
    this.status.lastCheck = new Date().toISOString();
    return this.status.websocket;
  }

  async checkAllServices(): Promise<ServiceStatus> {
    await this.checkApiHealth();
    this.checkWebSocketHealth();
    return this.getStatus();
  }

  getStatus(): ServiceStatus {
    return { ...this.status };
  }
}

// Export health checker instance
export const serviceHealthChecker = ServiceHealthChecker.getInstance();

// Service initialization helper
export const initializeServices = async (): Promise<void> => {
  try {
    console.log('Initializing KryptoPesa services...');
    
    // Check if user is authenticated
    const isAuthenticated = await authTokens.isAuthenticated();
    
    if (isAuthenticated) {
      // Initialize WebSocket connection
      await websocketService.connect();
      console.log('WebSocket service initialized');
    }
    
    // Check API health
    await serviceHealthChecker.checkApiHealth();
    console.log('API service health checked');
    
    console.log('KryptoPesa services initialized successfully');
  } catch (error) {
    console.error('Failed to initialize services:', error);
    throw error;
  }
};

// Service cleanup helper
export const cleanupServices = async (): Promise<void> => {
  try {
    console.log('Cleaning up KryptoPesa services...');
    
    // Disconnect WebSocket
    websocketService.disconnect();
    
    // Clear authentication tokens
    await authTokens.clearTokens();
    
    console.log('KryptoPesa services cleaned up successfully');
  } catch (error) {
    console.error('Failed to cleanup services:', error);
  }
};

// Error handling utilities
export const handleServiceError = (error: any, serviceName: string): void => {
  console.error(`${serviceName} service error:`, error);
  
  // Handle specific error types
  if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
    // Token expired or invalid
    console.log('Authentication error detected, clearing tokens');
    authTokens.clearTokens();
  } else if (error.message?.includes('Network')) {
    // Network connectivity issue
    console.log('Network error detected');
  } else if (error.message?.includes('timeout')) {
    // Request timeout
    console.log('Request timeout detected');
  }
};

// Service retry utility
export const retryServiceCall = async <T>(
  serviceCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await serviceCall();
    } catch (error) {
      lastError = error;
      console.warn(`Service call attempt ${attempt} failed:`, error);
      
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }
  
  throw lastError;
};

// Cache utility for service responses
export class ServiceCache {
  private static cache = new Map<string, { data: any; timestamp: number }>();
  
  static set(key: string, data: any, duration: number = SERVICES_CONFIG.CACHE_DURATION): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now() + duration,
    });
  }
  
  static get<T>(key: string): T | null {
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    if (Date.now() > cached.timestamp) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data as T;
  }
  
  static clear(): void {
    this.cache.clear();
  }
  
  static delete(key: string): void {
    this.cache.delete(key);
  }
}

// Export cache instance
export const serviceCache = ServiceCache;

// Default export with all services - using lazy initialization
export default {
  get api() {
    const { apiClient } = require('./api');
    return apiClient;
  },
  get walletAuth() {
    const { walletAuthService } = require('./walletAuthService');
    return walletAuthService;
  },
  get trading() {
    const { tradingService } = require('./tradingService');
    return tradingService;
  },
  get wallet() {
    const { walletService } = require('./walletService');
    return walletService;
  },
  get user() {
    const { userService } = require('./userService');
    return userService;
  },
  get websocket() {
    const { websocketService } = require('./websocketService');
    return websocketService;
  },
  healthChecker: serviceHealthChecker,
  cache: serviceCache,
  initialize: initializeServices,
  cleanup: cleanupServices,
};
