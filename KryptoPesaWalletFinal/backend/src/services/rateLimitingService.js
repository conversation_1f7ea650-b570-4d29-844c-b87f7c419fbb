const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');

/**
 * Production-ready Rate Limiting Service
 * Manages rate limiting configurations, monitoring, and enforcement
 */
class RateLimitingService {
  constructor() {
    this.redisClient = null;
    this.metrics = {
      totalRequests: 0,
      blockedRequests: 0,
      rateLimitViolations: 0,
      suspiciousIPs: new Set(),
      topViolators: new Map(),
      lastReset: new Date()
    };
    this.alertThresholds = {
      violationsPerMinute: parseInt(process.env.RATE_LIMIT_ALERT_THRESHOLD) || 100,
      suspiciousIPThreshold: parseInt(process.env.RATE_LIMIT_SUSPICIOUS_THRESHOLD) || 50,
      blockRateThreshold: parseFloat(process.env.RATE_LIMIT_BLOCK_RATE_THRESHOLD) || 0.1
    };
    this.monitoringInterval = null;
  }

  async initialize() {
    try {
      this.redisClient = getRedisClient();
      if (!this.redisClient || !this.redisClient.isReady) {
        logger.warn('Redis not available for rate limiting service');
        return false;
      }

      // Start monitoring
      this.startMonitoring();
      
      // Load existing metrics
      await this.loadMetrics();
      
      logger.info('Rate limiting service initialized successfully');
      return true;
    } catch (error) {
      logger.error('Failed to initialize rate limiting service:', error);
      return false;
    }
  }

  async loadMetrics() {
    try {
      if (!this.redisClient) return;

      const metricsData = await this.redisClient.get('rate_limit:metrics');
      if (metricsData) {
        const parsed = JSON.parse(metricsData);
        this.metrics = {
          ...this.metrics,
          ...parsed,
          suspiciousIPs: new Set(parsed.suspiciousIPs || []),
          topViolators: new Map(parsed.topViolators || [])
        };
      }
    } catch (error) {
      logger.error('Failed to load rate limiting metrics:', error);
    }
  }

  async saveMetrics() {
    try {
      if (!this.redisClient) return;

      const metricsToSave = {
        ...this.metrics,
        suspiciousIPs: Array.from(this.metrics.suspiciousIPs),
        topViolators: Array.from(this.metrics.topViolators.entries())
      };

      await this.redisClient.setex(
        'rate_limit:metrics',
        3600, // 1 hour TTL
        JSON.stringify(metricsToSave)
      );
    } catch (error) {
      logger.error('Failed to save rate limiting metrics:', error);
    }
  }

  startMonitoring() {
    // Monitor every 5 minutes
    this.monitoringInterval = setInterval(async () => {
      await this.performHealthCheck();
      await this.checkAlertThresholds();
      await this.saveMetrics();
      await this.cleanupOldData();
    }, 5 * 60 * 1000);

    logger.info('Rate limiting monitoring started');
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Rate limiting monitoring stopped');
    }
  }

  async performHealthCheck() {
    try {
      if (!this.redisClient) return { status: 'unhealthy', reason: 'Redis not available' };

      // Test Redis connectivity
      const testKey = 'rate_limit:health_check';
      await this.redisClient.setex(testKey, 10, 'test');
      const result = await this.redisClient.get(testKey);
      await this.redisClient.del(testKey);

      if (result !== 'test') {
        throw new Error('Redis read/write test failed');
      }

      return { status: 'healthy', timestamp: new Date() };
    } catch (error) {
      logger.error('Rate limiting health check failed:', error);
      return { status: 'unhealthy', reason: error.message };
    }
  }

  async checkAlertThresholds() {
    try {
      const now = new Date();
      const timeSinceReset = (now - this.metrics.lastReset) / 1000 / 60; // minutes

      // Check violation rate
      const violationsPerMinute = this.metrics.rateLimitViolations / Math.max(timeSinceReset, 1);
      if (violationsPerMinute > this.alertThresholds.violationsPerMinute) {
        await this.sendAlert('HIGH_VIOLATION_RATE', {
          violationsPerMinute: Math.round(violationsPerMinute),
          threshold: this.alertThresholds.violationsPerMinute
        });
      }

      // Check block rate
      const blockRate = this.metrics.blockedRequests / Math.max(this.metrics.totalRequests, 1);
      if (blockRate > this.alertThresholds.blockRateThreshold) {
        await this.sendAlert('HIGH_BLOCK_RATE', {
          blockRate: Math.round(blockRate * 100),
          threshold: Math.round(this.alertThresholds.blockRateThreshold * 100)
        });
      }

      // Check suspicious IPs
      if (this.metrics.suspiciousIPs.size > this.alertThresholds.suspiciousIPThreshold) {
        await this.sendAlert('SUSPICIOUS_IP_THRESHOLD', {
          suspiciousIPs: this.metrics.suspiciousIPs.size,
          threshold: this.alertThresholds.suspiciousIPThreshold
        });
      }
    } catch (error) {
      logger.error('Failed to check alert thresholds:', error);
    }
  }

  async sendAlert(type, data) {
    const alert = {
      type,
      data,
      timestamp: new Date(),
      severity: this.getAlertSeverity(type)
    };

    logger.warn('Rate limiting alert triggered', alert);

    // Store alert in Redis for admin dashboard
    try {
      if (this.redisClient) {
        await this.redisClient.lpush(
          'rate_limit:alerts',
          JSON.stringify(alert)
        );
        await this.redisClient.ltrim('rate_limit:alerts', 0, 99); // Keep last 100 alerts
      }
    } catch (error) {
      logger.error('Failed to store rate limiting alert:', error);
    }
  }

  getAlertSeverity(type) {
    const severityMap = {
      'HIGH_VIOLATION_RATE': 'HIGH',
      'HIGH_BLOCK_RATE': 'MEDIUM',
      'SUSPICIOUS_IP_THRESHOLD': 'HIGH',
      'REDIS_CONNECTION_FAILED': 'CRITICAL'
    };
    return severityMap[type] || 'LOW';
  }

  async cleanupOldData() {
    try {
      if (!this.redisClient) return;

      // Clean up old rate limit keys (older than 24 hours)
      const pattern = 'rl:*';
      const keys = await this.redisClient.keys(pattern);
      
      for (const key of keys) {
        const ttl = await this.redisClient.ttl(key);
        if (ttl === -1) { // No expiration set
          await this.redisClient.expire(key, 24 * 60 * 60); // Set 24 hour expiration
        }
      }

      // Reset metrics if they're older than 1 hour
      const now = new Date();
      const hoursSinceReset = (now - this.metrics.lastReset) / 1000 / 60 / 60;
      if (hoursSinceReset >= 1) {
        this.resetMetrics();
      }
    } catch (error) {
      logger.error('Failed to cleanup old rate limiting data:', error);
    }
  }

  resetMetrics() {
    this.metrics = {
      totalRequests: 0,
      blockedRequests: 0,
      rateLimitViolations: 0,
      suspiciousIPs: new Set(),
      topViolators: new Map(),
      lastReset: new Date()
    };
    logger.info('Rate limiting metrics reset');
  }

  // Track request for metrics
  trackRequest(ip, userAgent, endpoint, blocked = false) {
    this.metrics.totalRequests++;
    
    if (blocked) {
      this.metrics.blockedRequests++;
      this.metrics.rateLimitViolations++;
      
      // Track violators
      const violatorKey = `${ip}:${userAgent?.substring(0, 50) || 'unknown'}`;
      const currentCount = this.metrics.topViolators.get(violatorKey) || 0;
      this.metrics.topViolators.set(violatorKey, currentCount + 1);
      
      // Mark as suspicious if too many violations
      if (currentCount > 10) {
        this.metrics.suspiciousIPs.add(ip);
      }
    }
  }

  // Get current metrics
  getMetrics() {
    const now = new Date();
    const timeSinceReset = (now - this.metrics.lastReset) / 1000 / 60; // minutes
    
    return {
      ...this.metrics,
      suspiciousIPs: Array.from(this.metrics.suspiciousIPs),
      topViolators: Array.from(this.metrics.topViolators.entries()).slice(0, 10),
      violationsPerMinute: Math.round(this.metrics.rateLimitViolations / Math.max(timeSinceReset, 1)),
      blockRate: Math.round((this.metrics.blockedRequests / Math.max(this.metrics.totalRequests, 1)) * 100),
      timeSinceReset: Math.round(timeSinceReset)
    };
  }

  // Get rate limiting status for IP
  async getIPStatus(ip) {
    try {
      if (!this.redisClient) return { status: 'unknown' };

      const keys = await this.redisClient.keys(`rl:*:${ip}:*`);
      const status = {
        ip,
        activeRateLimits: keys.length,
        violations: 0,
        suspicious: this.metrics.suspiciousIPs.has(ip),
        lastViolation: null
      };

      // Get violation count
      const violationKey = `progressive:violations:${ip}:*`;
      const violationKeys = await this.redisClient.keys(violationKey);
      for (const key of violationKeys) {
        const count = await this.redisClient.get(key);
        status.violations += parseInt(count) || 0;
      }

      return status;
    } catch (error) {
      logger.error('Failed to get IP status:', error);
      return { status: 'error', error: error.message };
    }
  }

  // Whitelist IP (remove rate limits)
  async whitelistIP(ip, duration = 3600) {
    try {
      if (!this.redisClient) return false;

      const whitelistKey = `rate_limit:whitelist:${ip}`;
      await this.redisClient.setex(whitelistKey, duration, 'true');
      
      // Remove from suspicious IPs
      this.metrics.suspiciousIPs.delete(ip);
      
      logger.info(`IP ${ip} whitelisted for ${duration} seconds`);
      return true;
    } catch (error) {
      logger.error('Failed to whitelist IP:', error);
      return false;
    }
  }

  // Check if IP is whitelisted
  async isWhitelisted(ip) {
    try {
      if (!this.redisClient) return false;
      
      const whitelistKey = `rate_limit:whitelist:${ip}`;
      const result = await this.redisClient.get(whitelistKey);
      return result === 'true';
    } catch (error) {
      logger.error('Failed to check whitelist status:', error);
      return false;
    }
  }

  // Blacklist IP (block all requests)
  async blacklistIP(ip, duration = 24 * 60 * 60) {
    try {
      if (!this.redisClient) return false;

      const blacklistKey = `rate_limit:blacklist:${ip}`;
      await this.redisClient.setex(blacklistKey, duration, 'true');
      
      logger.warn(`IP ${ip} blacklisted for ${duration} seconds`);
      return true;
    } catch (error) {
      logger.error('Failed to blacklist IP:', error);
      return false;
    }
  }

  // Check if IP is blacklisted
  async isBlacklisted(ip) {
    try {
      if (!this.redisClient) return false;
      
      const blacklistKey = `rate_limit:blacklist:${ip}`;
      const result = await this.redisClient.get(blacklistKey);
      return result === 'true';
    } catch (error) {
      logger.error('Failed to check blacklist status:', error);
      return false;
    }
  }
}

// Create singleton instance
const rateLimitingService = new RateLimitingService();

module.exports = rateLimitingService;
