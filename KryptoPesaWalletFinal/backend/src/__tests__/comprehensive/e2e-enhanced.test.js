/**
 * Enhanced End-to-End Testing
 * Comprehensive user journey tests for all critical flows
 */

// Set up test environment variables before importing server
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long-for-testing';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-chars-long-for-testing';
process.env.SESSION_SECRET = 'test-session-secret-32-chars-long-for-testing';
process.env.MONGODB_URI = 'mongodb://localhost:27017/kryptopesa_test';
process.env.REDIS_URL = 'redis://localhost:6379';

const testEnvironment = require('../testEnvironment');
const request = require('supertest');
const { app, cleanupTestIntervals } = require('../testServer');

describe('Enhanced E2E Tests - Critical User Flows', () => {
  beforeAll(async () => {
    await testEnvironment.setup();
  }, 60000);

  beforeEach(async () => {
    await testEnvironment.cleanDatabase();
  });

  afterEach(async () => {
    await testEnvironment.cleanup();
  });

  afterAll(async () => {
    cleanupTestIntervals();
    await testEnvironment.teardown();
  });

  describe('User Authentication Flow', () => {
    test('should complete full authentication journey', async () => {
      // Step 1: User registration
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testuser',
          phone: '+254700000001',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Test',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(registerResponse.status).toBe(201);
      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.user.email).toBe('<EMAIL>');
      expect(registerResponse.body.data.accessToken).toBeDefined();

      // Step 2: Login with credentials
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'Krypt0P3sa!2024#Secure'
        });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.data.accessToken).toBeDefined();

      // Step 3: Access protected endpoint
      const authToken = loginResponse.body.data.accessToken;
      const protectedResponse = await request(app)
        .get('/api/trading/offers')
        .set('Authorization', `Bearer ${authToken}`);

      expect(protectedResponse.status).toBe(200);
      expect(protectedResponse.body.success).toBe(true);
    });

    test('should handle authentication errors correctly', async () => {
      // Test invalid credentials
      const invalidLoginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(invalidLoginResponse.status).toBe(401);
      expect(invalidLoginResponse.body.success).toBe(false);

      // Test unauthorized access
      const unauthorizedResponse = await request(app)
        .get('/api/trading/offers');

      expect(unauthorizedResponse.status).toBe(401);
      expect(unauthorizedResponse.body.success).toBe(false);
    });
  });

  describe('Wallet Management Flow', () => {
    let authToken;

    beforeEach(async () => {
      // Create and authenticate user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'walletuser',
          phone: '+254700000002',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Wallet',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      authToken = registerResponse.body.data.accessToken;
    });

    test('should complete wallet creation and management', async () => {
      // Step 1: Create wallet
      const walletResponse = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`);

      expect(walletResponse.status).toBe(201);
      expect(walletResponse.body.success).toBe(true);
      expect(walletResponse.body.data.addresses.ethereum).toBeDefined();
      expect(walletResponse.body.data.addresses.bitcoin).toBeDefined();
      expect(walletResponse.body.data.mnemonic).toBeDefined();

      // Step 2: Get balance statistics (simpler endpoint)
      const balanceStatsResponse = await request(app)
        .get('/api/wallet/security/balance-stats')
        .set('Authorization', `Bearer ${authToken}`);

      expect(balanceStatsResponse.status).toBe(200);
      expect(balanceStatsResponse.body.success).toBe(true);

      // Step 3: Get transaction history
      const historyResponse = await request(app)
        .get('/api/wallet/transactions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(historyResponse.status).toBe(200);
      expect(historyResponse.body.success).toBe(true);
      expect(Array.isArray(historyResponse.body.data.transactions)).toBe(true);
    });

    test('should prevent duplicate wallet creation', async () => {
      // Create first wallet
      await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`);

      // Attempt to create second wallet
      const duplicateResponse = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`);

      expect(duplicateResponse.status).toBe(400);
      expect(duplicateResponse.body.success).toBe(false);
    });
  });

  describe('Trading Platform Access Flow', () => {
    let authToken;

    beforeEach(async () => {
      // Create and authenticate user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'trader',
          phone: '+254700000003',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Trader',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      authToken = registerResponse.body.data.accessToken;
    });

    test('should access trading platform features', async () => {
      // Step 1: Get available offers
      const offersResponse = await request(app)
        .get('/api/trading/offers')
        .set('Authorization', `Bearer ${authToken}`);

      expect(offersResponse.status).toBe(200);
      expect(offersResponse.body.success).toBe(true);
      expect(Array.isArray(offersResponse.body.data.offers)).toBe(true);

      // Step 2: Get active trades
      const tradesResponse = await request(app)
        .get('/api/trading/trades/active')
        .set('Authorization', `Bearer ${authToken}`);

      expect(tradesResponse.status).toBe(200);
      expect(tradesResponse.body.success).toBe(true);
      expect(Array.isArray(tradesResponse.body.data.trades)).toBe(true);
    });

    test('should handle trading endpoint validation', async () => {
      // Test non-existent offer ID (valid ObjectId format)
      const invalidOfferResponse = await request(app)
        .get('/api/trading/offers/507f1f77bcf86cd799439011')
        .set('Authorization', `Bearer ${authToken}`);

      expect(invalidOfferResponse.status).toBe(404);
      expect(invalidOfferResponse.body.success).toBe(false);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle malformed requests gracefully', async () => {
      // Test malformed JSON
      const malformedResponse = await request(app)
        .post('/api/auth/register')
        .send('invalid-json');

      expect(malformedResponse.status).toBe(400);

      // Test missing required fields
      const incompleteResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>'
          // Missing required fields
        });

      expect(incompleteResponse.status).toBe(400);
      expect(incompleteResponse.body.success).toBe(false);
    });

    test('should handle rate limiting properly', async () => {
      const requests = [];

      // Make multiple rapid requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app)
            .post('/api/auth/login')
            .send({
              identifier: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }

      const responses = await Promise.all(requests);

      // All requests should return 401 (unauthorized) or 429 (rate limited)
      const validResponses = responses.filter(r => r.status === 401 || r.status === 429);
      expect(validResponses.length).toBe(10);

      // At least some should be unauthorized
      const unauthorizedResponses = responses.filter(r => r.status === 401);
      expect(unauthorizedResponses.length).toBeGreaterThan(0);
    });

    test('should handle concurrent user registrations', async () => {
      const registrations = [];

      // Attempt concurrent registrations with same email
      for (let i = 0; i < 3; i++) {
        registrations.push(
          request(app)
            .post('/api/auth/register')
            .send({
              email: '<EMAIL>',
              username: `user${i}`,
              phone: `+25470000000${i}`,
              password: 'Krypt0P3sa!2024#Secure',
              firstName: 'Concurrent',
              lastName: 'User',
              country: 'KE',
              city: 'Nairobi'
            })
        );
      }

      const responses = await Promise.all(registrations);

      // Only one should succeed
      const successfulRegistrations = responses.filter(r => r.status === 201);
      expect(successfulRegistrations.length).toBe(1);

      // Others should fail with conflict (400) or server error (500)
      const failedRegistrations = responses.filter(r => r.status === 400 || r.status === 500);
      expect(failedRegistrations.length).toBe(2);
    });
  });

  describe('Security and Validation Flow', () => {
    test('should enforce password security requirements', async () => {
      // Test weak password
      const weakPasswordResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'weakpass',
          phone: '+254700000004',
          password: '123456',
          firstName: 'Weak',
          lastName: 'Password',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(weakPasswordResponse.status).toBe(400);
      expect(weakPasswordResponse.body.success).toBe(false);
      expect(weakPasswordResponse.body.errors).toBeDefined();
    });

    test('should validate input data properly', async () => {
      // Test invalid email format
      const invalidEmailResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: 'invalid-email',
          username: 'testuser',
          phone: '+254700000005',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Test',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(invalidEmailResponse.status).toBe(400);
      expect(invalidEmailResponse.body.success).toBe(false);

      // Test invalid phone format
      const invalidPhoneResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testuser',
          phone: 'invalid-phone',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Test',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(invalidPhoneResponse.status).toBe(400);
      expect(invalidPhoneResponse.body.success).toBe(false);
    });
  });

  describe('Advanced User Flows', () => {
    test('should handle complete user lifecycle', async () => {
      // Step 1: Register user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'lifecycle',
          phone: '+254700000010',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Life',
          lastName: 'Cycle',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(registerResponse.status).toBe(201);
      const authToken = registerResponse.body.data.accessToken;

      // Step 2: Create wallet
      const walletResponse = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`);

      expect(walletResponse.status).toBe(201);

      // Step 3: Access trading platform
      const offersResponse = await request(app)
        .get('/api/trading/offers')
        .set('Authorization', `Bearer ${authToken}`);

      expect(offersResponse.status).toBe(200);

      // Step 4: Get active trades
      const tradesResponse = await request(app)
        .get('/api/trading/trades/active')
        .set('Authorization', `Bearer ${authToken}`);

      expect(tradesResponse.status).toBe(200);

      // Step 5: Get transaction history
      const historyResponse = await request(app)
        .get('/api/wallet/transactions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(historyResponse.status).toBe(200);
    });

    test('should handle session management', async () => {
      // Register user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'session',
          phone: '+254700000011',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Session',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      const authToken = registerResponse.body.data.accessToken;

      // Test multiple authenticated requests
      for (let i = 0; i < 3; i++) {
        const response = await request(app)
          .get('/api/trading/offers')
          .set('Authorization', `Bearer ${authToken}`);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      }
    });
  });

  describe('Performance and Load Testing', () => {
    test('should handle multiple simultaneous authentications', async () => {
      const requests = [];

      // Create multiple authentication requests
      for (let i = 0; i < 5; i++) {
        requests.push(
          request(app)
            .post('/api/auth/register')
            .send({
              email: `perf${i}@example.com`,
              username: `perf${i}`,
              phone: `+25470000001${i}`,
              password: 'Krypt0P3sa!2024#Secure',
              firstName: 'Perf',
              lastName: `User${i}`,
              country: 'KE',
              city: 'Nairobi'
            })
        );
      }

      const responses = await Promise.all(requests);

      // All should succeed
      const successfulRegistrations = responses.filter(r => r.status === 201);
      expect(successfulRegistrations.length).toBe(5);
    });

    test('should handle rapid API requests', async () => {
      // Register user first
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'rapid',
          phone: '+254700000020',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Rapid',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      const authToken = registerResponse.body.data.accessToken;
      const requests = [];

      // Make rapid requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app)
            .get('/api/trading/offers')
            .set('Authorization', `Bearer ${authToken}`)
        );
      }

      const responses = await Promise.all(requests);

      // All should succeed or be rate limited
      const validResponses = responses.filter(r => r.status === 200 || r.status === 429);
      expect(validResponses.length).toBe(10);
    });
  });
});
