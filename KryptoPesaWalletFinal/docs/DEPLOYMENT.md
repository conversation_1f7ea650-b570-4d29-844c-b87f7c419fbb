# KryptoPesa Deployment Guide

## Overview
This guide covers the complete deployment process for the KryptoPesa P2P cryptocurrency trading platform.

## Prerequisites

### System Requirements
- Node.js 18+ 
- MongoDB 6+
- Redis 7+
- Docker & Docker Compose
- Git

### Development Tools
- React Native CLI
- Android Studio (for Android builds)
- Xcode (for iOS builds, macOS only)
- Hardhat (for smart contract deployment)

## Environment Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd KryptoPesaWalletFinal
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### Required Environment Variables
```bash
# Server Configuration
NODE_ENV=production
PORT=3000
API_BASE_URL=https://api.kryptopesa.com

# Database
MONGODB_URI=mongodb://localhost:27017/kryptopesa
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Blockchain
POLYGON_RPC_URL=https://polygon-rpc.com
POLYGON_PRIVATE_KEY=your-polygon-private-key
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-infura-key

# Smart Contracts
ESCROW_CONTRACT_ADDRESS=0x...
USDT_CONTRACT_ADDRESS=******************************************
USDC_CONTRACT_ADDRESS=******************************************

# External APIs
COINGECKO_API_KEY=your-coingecko-api-key
FIREBASE_SERVER_KEY=your-firebase-server-key

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Smart Contract Deployment

### 1. Install Dependencies
```bash
cd smart-contracts
npm install
```

### 2. Compile Contracts
```bash
npm run compile
```

### 3. Deploy to Polygon
```bash
# Deploy to Polygon mainnet
npm run deploy:polygon

# Deploy to Mumbai testnet
npm run deploy:mumbai
```

### 4. Verify Contracts
```bash
npm run verify
```

## Backend Deployment

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Database Setup
```bash
# Start MongoDB
sudo systemctl start mongod

# Start Redis
sudo systemctl start redis
```

### 3. Run Database Migrations
```bash
npm run seed
```

### 4. Start Backend Server
```bash
# Development
npm run dev

# Production
npm start
```

### 5. PM2 Process Management (Production)
```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start src/server.js --name "kryptopesa-api"

# Save PM2 configuration
pm2 save
pm2 startup
```

## Mobile App Deployment

### 1. Install Dependencies
```bash
cd mobile
npm install
```

### 2. Android Build
```bash
# Debug build
npm run android

# Release build
cd android
./gradlew assembleRelease
```

### 3. iOS Build (macOS only)
```bash
# Install iOS dependencies
cd ios && pod install && cd ..

# Debug build
npm run ios

# Release build
npm run build:ios
```

### 4. App Store Deployment
- Follow platform-specific guidelines for Google Play Store and Apple App Store
- Update app signing certificates
- Configure app metadata and screenshots

## Admin Dashboard Deployment

### 1. Install Dependencies
```bash
cd admin-dashboard
npm install
```

### 2. Build for Production
```bash
npm run build
```

### 3. Deploy to Web Server
```bash
# Copy build files to web server
scp -r build/* user@server:/var/www/admin.kryptopesa.com/
```

## Docker Deployment

### 1. Build Docker Images
```bash
# Build all services
docker-compose build

# Build specific service
docker-compose build backend
```

### 2. Start Services
```bash
# Start all services
docker-compose up -d

# Start specific services
docker-compose up -d backend mongodb redis
```

### 3. Docker Compose Configuration
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - mongodb
      - redis
  
  mongodb:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
  
  redis:
    image: redis:7
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongodb_data:
  redis_data:
```

## SSL/TLS Configuration

### 1. Obtain SSL Certificate
```bash
# Using Let's Encrypt
sudo certbot --nginx -d api.kryptopesa.com
sudo certbot --nginx -d admin.kryptopesa.com
```

### 2. Nginx Configuration
```nginx
server {
    listen 443 ssl;
    server_name api.kryptopesa.com;
    
    ssl_certificate /etc/letsencrypt/live/api.kryptopesa.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.kryptopesa.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Monitoring & Logging

### 1. Application Monitoring
```bash
# Install monitoring tools
npm install -g pm2-logrotate
pm2 install pm2-server-monit
```

### 2. Log Management
```bash
# View logs
pm2 logs kryptopesa-api

# Log rotation
pm2 install pm2-logrotate
```

### 3. Health Checks
```bash
# API health check
curl https://api.kryptopesa.com/health

# Database health check
curl https://api.kryptopesa.com/health/db
```

## Security Checklist

- [ ] Environment variables secured
- [ ] SSL/TLS certificates installed
- [ ] Firewall configured
- [ ] Database access restricted
- [ ] API rate limiting enabled
- [ ] Input validation implemented
- [ ] CORS properly configured
- [ ] Security headers added
- [ ] Regular security updates scheduled

## Backup Strategy

### 1. Database Backup
```bash
# MongoDB backup
mongodump --uri="mongodb://localhost:27017/kryptopesa" --out=/backup/mongodb/

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --uri="$MONGODB_URI" --out="/backup/mongodb/$DATE"
```

### 2. File Backup
```bash
# Application files backup
tar -czf /backup/app/kryptopesa_$DATE.tar.gz /var/www/kryptopesa/
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MongoDB service status
   - Verify connection string
   - Check firewall settings

2. **Smart Contract Deployment Failed**
   - Verify network configuration
   - Check gas settings
   - Ensure sufficient balance

3. **Mobile App Build Failed**
   - Clear React Native cache
   - Verify Android/iOS SDK versions
   - Check signing certificates

### Support Contacts
- Technical Support: <EMAIL>
- Emergency Contact: +254700000000
