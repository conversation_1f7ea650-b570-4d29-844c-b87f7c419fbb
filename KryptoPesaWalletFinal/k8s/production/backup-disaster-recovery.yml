# KryptoPesa Backup and Disaster Recovery
# Automated backup system with disaster recovery capabilities

apiVersion: v1
kind: Namespace
metadata:
  name: backup-system
  labels:
    name: backup-system
    environment: production

---
# Backup CronJob for MongoDB
apiVersion: batch/v1
kind: CronJob
metadata:
  name: mongodb-backup
  namespace: backup-system
  labels:
    app: mongodb-backup
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 7
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
          - name: mongodb-backup
            image: mongo:6
            command:
            - /bin/bash
            - -c
            - |
              set -e
              TIMESTAMP=$(date +%Y%m%d_%H%M%S)
              BACKUP_NAME="mongodb_backup_${TIMESTAMP}"
              
              echo "Starting MongoDB backup: $BACKUP_NAME"
              
              # Create backup
              mongodump --uri="${MONGODB_URI}" --archive="/backup/${BACKUP_NAME}.gz" --gzip
              
              # Verify backup
              if [ -f "/backup/${BACKUP_NAME}.gz" ]; then
                echo "Backup created successfully: ${BACKUP_NAME}.gz"
                
                # Upload to cloud storage
                aws s3 cp "/backup/${BACKUP_NAME}.gz" "s3://${BACKUP_BUCKET}/mongodb/${BACKUP_NAME}.gz"
                
                # Cleanup local backup (keep only latest 3)
                cd /backup
                ls -t mongodb_backup_*.gz | tail -n +4 | xargs -r rm
                
                echo "Backup completed and uploaded to S3"
              else
                echo "Backup failed!"
                exit 1
              fi
            env:
            - name: MONGODB_URI
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: mongodb-uri
            - name: BACKUP_BUCKET
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: backup-bucket
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: aws-access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: aws-secret-access-key
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            resources:
              requests:
                cpu: 100m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 1Gi
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc

---
# Backup CronJob for Redis
apiVersion: batch/v1
kind: CronJob
metadata:
  name: redis-backup
  namespace: backup-system
  labels:
    app: redis-backup
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 7
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
          - name: redis-backup
            image: redis:7-alpine
            command:
            - /bin/sh
            - -c
            - |
              set -e
              TIMESTAMP=$(date +%Y%m%d_%H%M%S)
              BACKUP_NAME="redis_backup_${TIMESTAMP}"
              
              echo "Starting Redis backup: $BACKUP_NAME"
              
              # Create Redis backup
              redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} --rdb "/backup/${BACKUP_NAME}.rdb"
              
              # Compress backup
              gzip "/backup/${BACKUP_NAME}.rdb"
              
              # Verify backup
              if [ -f "/backup/${BACKUP_NAME}.rdb.gz" ]; then
                echo "Backup created successfully: ${BACKUP_NAME}.rdb.gz"
                
                # Upload to cloud storage
                aws s3 cp "/backup/${BACKUP_NAME}.rdb.gz" "s3://${BACKUP_BUCKET}/redis/${BACKUP_NAME}.rdb.gz"
                
                # Cleanup local backup (keep only latest 3)
                cd /backup
                ls -t redis_backup_*.rdb.gz | tail -n +4 | xargs -r rm
                
                echo "Redis backup completed and uploaded to S3"
              else
                echo "Redis backup failed!"
                exit 1
              fi
            env:
            - name: REDIS_HOST
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: redis-host
            - name: REDIS_PORT
              value: "6379"
            - name: BACKUP_BUCKET
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: backup-bucket
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: aws-access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: aws-secret-access-key
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            resources:
              requests:
                cpu: 100m
                memory: 128Mi
              limits:
                cpu: 200m
                memory: 256Mi
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc

---
# Application Data Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: application-backup
  namespace: backup-system
  labels:
    app: application-backup
spec:
  schedule: "0 4 * * *"  # Daily at 4 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 7
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
          - name: application-backup
            image: alpine:latest
            command:
            - /bin/sh
            - -c
            - |
              set -e
              apk add --no-cache aws-cli tar gzip
              
              TIMESTAMP=$(date +%Y%m%d_%H%M%S)
              BACKUP_NAME="application_backup_${TIMESTAMP}"
              
              echo "Starting application data backup: $BACKUP_NAME"
              
              # Create backup directory
              mkdir -p /backup/app_data
              
              # Backup uploaded files
              if [ -d "/app/uploads" ]; then
                tar -czf "/backup/${BACKUP_NAME}_uploads.tar.gz" -C /app uploads/
                echo "Uploads backup created"
              fi
              
              # Backup logs (last 7 days)
              if [ -d "/app/logs" ]; then
                find /app/logs -name "*.log" -mtime -7 | tar -czf "/backup/${BACKUP_NAME}_logs.tar.gz" -T -
                echo "Logs backup created"
              fi
              
              # Backup configuration files
              if [ -d "/app/config" ]; then
                tar -czf "/backup/${BACKUP_NAME}_config.tar.gz" -C /app config/
                echo "Config backup created"
              fi
              
              # Upload to cloud storage
              aws s3 sync /backup/ "s3://${BACKUP_BUCKET}/application/" --exclude "*" --include "${BACKUP_NAME}_*"
              
              # Cleanup local backups (keep only latest 3)
              cd /backup
              ls -t application_backup_*.tar.gz | tail -n +10 | xargs -r rm
              
              echo "Application backup completed and uploaded to S3"
            env:
            - name: BACKUP_BUCKET
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: backup-bucket
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: aws-access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: backup-secrets
                  key: aws-secret-access-key
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            - name: app-uploads
              mountPath: /app/uploads
              readOnly: true
            - name: app-logs
              mountPath: /app/logs
              readOnly: true
            resources:
              requests:
                cpu: 100m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 1Gi
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          - name: app-uploads
            persistentVolumeClaim:
              claimName: app-uploads-pvc
          - name: app-logs
            persistentVolumeClaim:
              claimName: app-logs-pvc

---
# Backup Storage PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-pvc
  namespace: backup-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd

---
# Disaster Recovery Job Template
apiVersion: batch/v1
kind: Job
metadata:
  name: disaster-recovery-template
  namespace: backup-system
  labels:
    app: disaster-recovery
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: disaster-recovery
        image: alpine:latest
        command:
        - /bin/sh
        - -c
        - |
          set -e
          apk add --no-cache aws-cli mongo-tools redis-tools
          
          echo "Starting disaster recovery process..."
          
          # Download latest backups from S3
          echo "Downloading latest backups..."
          aws s3 sync "s3://${BACKUP_BUCKET}/" /recovery/
          
          # Restore MongoDB
          if [ "${RESTORE_MONGODB}" = "true" ]; then
            echo "Restoring MongoDB..."
            LATEST_MONGO_BACKUP=$(ls -t /recovery/mongodb/mongodb_backup_*.gz | head -1)
            if [ -f "$LATEST_MONGO_BACKUP" ]; then
              mongorestore --uri="${MONGODB_URI}" --archive="$LATEST_MONGO_BACKUP" --gzip --drop
              echo "MongoDB restore completed"
            else
              echo "No MongoDB backup found!"
              exit 1
            fi
          fi
          
          # Restore Redis
          if [ "${RESTORE_REDIS}" = "true" ]; then
            echo "Restoring Redis..."
            LATEST_REDIS_BACKUP=$(ls -t /recovery/redis/redis_backup_*.rdb.gz | head -1)
            if [ -f "$LATEST_REDIS_BACKUP" ]; then
              gunzip -c "$LATEST_REDIS_BACKUP" > /tmp/dump.rdb
              redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} FLUSHALL
              redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} --rdb /tmp/dump.rdb
              echo "Redis restore completed"
            else
              echo "No Redis backup found!"
              exit 1
            fi
          fi
          
          # Restore application data
          if [ "${RESTORE_APPLICATION}" = "true" ]; then
            echo "Restoring application data..."
            LATEST_APP_BACKUP=$(ls -t /recovery/application/application_backup_*_uploads.tar.gz | head -1)
            if [ -f "$LATEST_APP_BACKUP" ]; then
              tar -xzf "$LATEST_APP_BACKUP" -C /app/
              echo "Application data restore completed"
            else
              echo "No application backup found!"
              exit 1
            fi
          fi
          
          echo "Disaster recovery completed successfully!"
        env:
        - name: BACKUP_BUCKET
          valueFrom:
            secretKeyRef:
              name: backup-secrets
              key: backup-bucket
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: backup-secrets
              key: mongodb-uri
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: backup-secrets
              key: redis-host
        - name: RESTORE_MONGODB
          value: "true"
        - name: RESTORE_REDIS
          value: "true"
        - name: RESTORE_APPLICATION
          value: "true"
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: backup-secrets
              key: aws-access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: backup-secrets
              key: aws-secret-access-key
        volumeMounts:
        - name: recovery-storage
          mountPath: /recovery
        - name: app-uploads
          mountPath: /app/uploads
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1
            memory: 2Gi
      volumes:
      - name: recovery-storage
        persistentVolumeClaim:
          claimName: backup-pvc
      - name: app-uploads
        persistentVolumeClaim:
          claimName: app-uploads-pvc
