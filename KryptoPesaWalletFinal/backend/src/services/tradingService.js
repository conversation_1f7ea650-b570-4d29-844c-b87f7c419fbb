const Trade = require('../models/Trade');
const Offer = require('../models/Offer');
const User = require('../models/User');
const priceService = require('./priceService');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');
const { monitoringIntegrationService } = require('./monitoringIntegrationService');
const { businessFlowMonitoringService } = require('./businessFlowMonitoring');

class TradingService {
  constructor() {
    this.supportedCryptocurrencies = ['BTC', 'ETH', 'MATIC', 'USDT', 'USDC', 'DAI'];
    this.supportedFiatCurrencies = ['USD', 'KES', 'TZS', 'UGX'];
    this.supportedPaymentMethods = ['M-Pesa', 'Airtel Money', 'Bank Transfer', 'Cash'];
  }

  /**
   * Get offers with filters
   */
  async getOffers(filters = {}) {
    try {
      const {
        cryptocurrency,
        fiatCurrency,
        type,
        minAmount,
        maxAmount,
        paymentMethod,
        location,
        limit = 50,
        offset = 0
      } = filters;

      const query = { status: 'active' };

      if (cryptocurrency) query.cryptocurrency = cryptocurrency;
      if (fiatCurrency) query.fiatCurrency = fiatCurrency;
      if (type) query.type = type;
      if (paymentMethod) query.paymentMethods = { $in: [paymentMethod] };
      if (location) query.location = new RegExp(location, 'i');

      if (minAmount || maxAmount) {
        query.amount = {};
        if (minAmount) query.amount.$gte = minAmount;
        if (maxAmount) query.amount.$lte = maxAmount;
      }

      // Use optimized query with caching
      const { performanceOptimization } = require('./performanceOptimization');
      const cacheKey = `offers:${JSON.stringify(query)}:${limit}:${offset}`;

      const offers = await performanceOptimization.cacheQuery(
        cacheKey,
        () => Offer.find(query)
          .populate('user', 'username firstName lastName rating completedTrades lastSeen isOnline isVerified')
          .sort({ createdAt: -1 })
          .limit(limit)
          .skip(offset)
          .lean(),
        120 // Cache for 2 minutes
      );

      // Add current prices and calculate margins
      const enrichedOffers = await Promise.all(offers.map(async (offer) => {
        const currentPrice = await priceService.getPrice(offer.cryptocurrency, offer.fiatCurrency);
        const margin = currentPrice ? ((offer.price - currentPrice) / currentPrice) * 100 : 0;

        return {
          ...offer.toObject(),
          currentPrice,
          margin,
          user: {
            id: offer.user._id,
            username: offer.user.username,
            userRating: offer.user.rating || 0,
            completedTrades: offer.user.completedTrades || 0,
            isOnline: offer.user.isOnline || false,
            isVerified: offer.user.isVerified || false
          }
        };
      }));

      logger.info(`Retrieved ${enrichedOffers.length} offers with filters`, filters);
      return enrichedOffers;

    } catch (error) {
      logger.error(`Error getting offers: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create new offer
   */
  async createOffer(userId, offerData) {
    try {
      const {
        type,
        cryptocurrency,
        fiatCurrency,
        amount,
        minAmount,
        maxAmount,
        price,
        margin,
        paymentMethods,
        terms,
        location
      } = offerData;

      // Validate input
      this.validateOfferData(offerData);

      // Calculate price if margin is provided
      let finalPrice = price;
      if (margin && !price) {
        const currentPrice = await priceService.getPrice(cryptocurrency, fiatCurrency);
        if (currentPrice) {
          finalPrice = currentPrice * (1 + margin / 100);
        }
      }

      const offer = new Offer({
        user: userId,
        type,
        cryptocurrency,
        fiatCurrency,
        amount,
        minAmount,
        maxAmount,
        price: finalPrice,
        margin: margin || 0,
        paymentMethods,
        terms,
        location,
        status: 'active'
      });

      await offer.save();
      await offer.populate('user', 'username firstName lastName rating completedTrades');

      logger.info(`Offer created: ${offer._id} by user ${userId}`, {
        type,
        cryptocurrency,
        amount,
        price: finalPrice
      });

      return offer;

    } catch (error) {
      logger.error(`Error creating offer: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get offer details
   */
  async getOfferDetails(offerId) {
    try {
      const offer = await Offer.findById(offerId)
        .populate('user', 'username firstName lastName rating completedTrades lastSeen isOnline isVerified');

      if (!offer) {
        throw new AppError('Offer not found', 404);
      }

      // Add current price and margin calculation
      const currentPrice = await priceService.getPrice(offer.cryptocurrency, offer.fiatCurrency);
      const margin = currentPrice ? ((offer.price - currentPrice) / currentPrice) * 100 : 0;

      return {
        ...offer.toObject(),
        currentPrice,
        margin
      };

    } catch (error) {
      logger.error(`Error getting offer details: ${error.message}`);
      throw error;
    }
  }

  /**
   * Accept trade
   */
  async acceptTrade(userId, tradeData) {
    try {
      const { offerId, amount, paymentMethod, message } = tradeData;

      const offer = await Offer.findById(offerId).populate('user');
      if (!offer) {
        throw new AppError('Offer not found', 404);
      }

      if (offer.status !== 'active') {
        throw new AppError('Offer is not active', 400);
      }

      if (offer.user._id.toString() === userId.toString()) {
        throw new AppError('Cannot accept your own offer', 400);
      }

      // Validate amount range
      if (amount < offer.minAmount || amount > offer.maxAmount) {
        throw new AppError(`Amount must be between ${offer.minAmount} and ${offer.maxAmount}`, 400);
      }

      // Validate payment method
      if (!offer.paymentMethods.includes(paymentMethod)) {
        throw new AppError('Payment method not supported for this offer', 400);
      }

      // Calculate crypto amount
      const cryptoAmount = amount / offer.price;

      // Use atomic transaction to create trade with initial message
      const { dataConsistencyService } = require('./dataConsistency');

      const tradeData = {
        offer: offerId,
        buyer: offer.type === 'sell' ? userId : offer.user._id,
        seller: offer.type === 'sell' ? offer.user._id : userId,
        amount,
        cryptoAmount,
        price: offer.price,
        cryptocurrency: offer.cryptocurrency,
        fiatCurrency: offer.fiatCurrency,
        paymentMethod,
        status: 'pending'
      };

      // Add initial message if provided
      if (message) {
        tradeData.messages = [{
          sender: userId,
          message,
          type: 'user',
          timestamp: new Date()
        }];
      }

      const offerData = {
        offerId: offerId
      };

      const result = await dataConsistencyService.executeAtomicTradeCreation(
        tradeData,
        offerData,
        userId
      );

      const trade = result.trade;
      await trade.populate(['buyer', 'seller', 'offer']);

      // Record business flow event
      businessFlowMonitoringService.recordBusinessFlowEvent('trade', 'created', {
        status: 'created',
        cryptocurrency: offer.cryptocurrency,
        amount: cryptoAmount,
        fiatAmount: amount,
        tradeId: trade._id
      });

      logger.info(`Trade accepted: ${trade._id}`, {
        offerId,
        amount,
        cryptoAmount,
        buyer: trade.buyer._id,
        seller: trade.seller._id
      });

      return trade;

    } catch (error) {
      logger.error(`Error accepting trade: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get active trades for user
   */
  async getActiveTrades(userId) {
    try {
      const trades = await Trade.find({
        $or: [{ buyer: userId }, { seller: userId }],
        status: { $in: ['pending', 'accepted', 'escrowed', 'paid'] }
      })
        .populate('buyer', 'username firstName lastName rating isOnline')
        .populate('seller', 'username firstName lastName rating isOnline')
        .populate('offer')
        .sort({ createdAt: -1 });

      logger.info(`Retrieved ${trades.length} active trades for user ${userId}`);
      return trades;

    } catch (error) {
      logger.error(`Error getting active trades: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get trade history for user
   */
  async getTradeHistory(userId, filters = {}) {
    try {
      const { limit = 50, offset = 0, status } = filters;

      const query = {
        $or: [{ buyer: userId }, { seller: userId }]
      };

      if (status) {
        query.status = status;
      } else {
        // Default to completed/historical trades
        query.status = { $in: ['completed', 'cancelled', 'disputed'] };
      }

      const trades = await Trade.find(query)
        .populate('buyer', 'username firstName lastName rating isOnline')
        .populate('seller', 'username firstName lastName rating isOnline')
        .populate('offer')
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(offset);

      const total = await Trade.countDocuments(query);

      logger.info(`Retrieved ${trades.length} trade history entries for user ${userId}`);
      return {
        trades,
        pagination: {
          limit,
          offset,
          total,
          hasMore: offset + limit < total
        }
      };

    } catch (error) {
      logger.error(`Error getting trade history: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get trade details
   */
  async getTradeDetails(tradeId, userId) {
    try {
      const trade = await Trade.findById(tradeId)
        .populate('buyer', 'username firstName lastName rating isOnline isVerified')
        .populate('seller', 'username firstName lastName rating isOnline isVerified')
        .populate('offer');

      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      // Check if user is participant
      const isParticipant = trade.buyer._id.toString() === userId.toString() ||
                           trade.seller._id.toString() === userId.toString();

      if (!isParticipant) {
        throw new AppError('Access denied', 403);
      }

      return trade;

    } catch (error) {
      logger.error(`Error getting trade details: ${error.message}`);
      throw error;
    }
  }

  /**
   * Mark payment as sent
   */
  async markPaymentSent(tradeId, userId, proofUrl) {
    try {
      const trade = await Trade.findById(tradeId);
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      // Only buyer can mark payment as sent
      if (trade.buyer.toString() !== userId.toString()) {
        throw new AppError('Only buyer can mark payment as sent', 403);
      }

      if (trade.status !== 'escrowed') {
        throw new AppError('Trade must be escrowed before marking payment', 400);
      }

      trade.status = 'paid';
      trade.paidAt = new Date();

      // Add payment proof if provided
      if (proofUrl) {
        trade.proofs.push({
          uploadedBy: userId,
          type: 'payment',
          description: 'Payment proof',
          fileUrl: proofUrl,
          uploadedAt: new Date()
        });
      }

      // Add system message
      trade.messages.push({
        sender: userId,
        message: 'Payment has been sent',
        type: 'system',
        timestamp: new Date()
      });

      await trade.save();

      logger.info(`Payment marked as sent for trade ${tradeId}`);
      return true;

    } catch (error) {
      logger.error(`Error marking payment as sent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Confirm payment received
   */
  async confirmPaymentReceived(tradeId, userId) {
    try {
      const trade = await Trade.findById(tradeId);
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      // Only seller can confirm payment received
      if (trade.seller.toString() !== userId.toString()) {
        throw new AppError('Only seller can confirm payment received', 403);
      }

      if (trade.status !== 'paid') {
        throw new AppError('Payment must be marked as sent first', 400);
      }

      trade.status = 'completed';
      trade.completedAt = new Date();

      // Add system message
      trade.messages.push({
        sender: userId,
        message: 'Payment confirmed and trade completed',
        type: 'system',
        timestamp: new Date()
      });

      await trade.save();

      // Update user statistics
      await this.updateUserStats(trade.buyer);
      await this.updateUserStats(trade.seller);

      logger.info(`Payment confirmed for trade ${tradeId}`);
      return true;

    } catch (error) {
      logger.error(`Error confirming payment: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cancel trade
   */
  async cancelTrade(tradeId, userId, reason) {
    try {
      const trade = await Trade.findById(tradeId);
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      // Check if user is participant
      const isParticipant = trade.buyer.toString() === userId.toString() ||
                           trade.seller.toString() === userId.toString();

      if (!isParticipant) {
        throw new AppError('Access denied', 403);
      }

      if (trade.status === 'completed' || trade.status === 'cancelled') {
        throw new AppError('Trade cannot be cancelled', 400);
      }

      trade.status = 'cancelled';
      trade.cancelledAt = new Date();
      trade.cancelReason = reason;

      // Add system message
      trade.messages.push({
        sender: userId,
        message: `Trade cancelled: ${reason}`,
        type: 'system',
        timestamp: new Date()
      });

      await trade.save();

      // Record business flow event
      businessFlowMonitoringService.recordBusinessFlowEvent('trade', 'failed', {
        status: 'cancelled',
        tradeId: trade._id,
        reason,
        cancelledBy: userId,
        cryptocurrency: trade.cryptocurrency?.symbol,
        amount: trade.fiat?.amount,
        error: reason
      });

      logger.info(`Trade cancelled: ${tradeId} by user ${userId}`);
      return true;

    } catch (error) {
      logger.error(`Error cancelling trade: ${error.message}`);
      throw error;
    }
  }

  /**
   * Initiate dispute
   */
  async initiateDispute(tradeId, userId, reason) {
    try {
      const trade = await Trade.findById(tradeId);
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      // Check if user is participant
      const isParticipant = trade.buyer.toString() === userId.toString() ||
                           trade.seller.toString() === userId.toString();

      if (!isParticipant) {
        throw new AppError('Access denied', 403);
      }

      if (trade.status === 'completed' || trade.status === 'cancelled') {
        throw new AppError('Cannot dispute completed or cancelled trade', 400);
      }

      trade.status = 'disputed';
      trade.disputedAt = new Date();
      trade.dispute = {
        initiatedBy: userId,
        reason,
        status: 'open',
        initiatedAt: new Date()
      };

      // Add system message
      trade.messages.push({
        sender: userId,
        message: `Dispute initiated: ${reason}`,
        type: 'system',
        timestamp: new Date()
      });

      await trade.save();

      // Record business flow event
      businessFlowMonitoringService.recordBusinessFlowEvent('trade', 'disputed', {
        status: 'disputed',
        tradeId: trade._id,
        reason,
        initiatedBy: userId,
        cryptocurrency: trade.cryptocurrency?.symbol,
        amount: trade.fiat?.amount
      });

      logger.info(`Dispute initiated for trade ${tradeId} by user ${userId}`);
      return true;

    } catch (error) {
      logger.error(`Error initiating dispute: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send trade message
   */
  async sendTradeMessage(tradeId, userId, message) {
    try {
      const trade = await Trade.findById(tradeId);
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      // Check if user is participant
      const isParticipant = trade.buyer.toString() === userId.toString() ||
                           trade.seller.toString() === userId.toString();

      if (!isParticipant) {
        throw new AppError('Access denied', 403);
      }

      trade.messages.push({
        sender: userId,
        message,
        type: 'user',
        timestamp: new Date()
      });

      await trade.save();

      logger.info(`Message sent in trade ${tradeId} by user ${userId}`);
      return true;

    } catch (error) {
      logger.error(`Error sending trade message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get trading statistics
   */
  async getTradingStats(userId) {
    try {
      const totalTrades = await Trade.countDocuments({
        $or: [{ buyer: userId }, { seller: userId }]
      });

      const completedTrades = await Trade.countDocuments({
        $or: [{ buyer: userId }, { seller: userId }],
        status: 'completed'
      });

      const activeTrades = await Trade.countDocuments({
        $or: [{ buyer: userId }, { seller: userId }],
        status: { $in: ['pending', 'accepted', 'escrowed', 'paid'] }
      });

      const activeOffers = await Offer.countDocuments({
        user: userId,
        status: 'active'
      });

      // Calculate total volume
      const volumeResult = await Trade.aggregate([
        {
          $match: {
            $or: [{ buyer: userId }, { seller: userId }],
            status: 'completed'
          }
        },
        {
          $group: {
            _id: '$fiatCurrency',
            totalVolume: { $sum: '$amount' }
          }
        }
      ]);

      const volumeByCurrency = {};
      let totalVolume = 0;
      volumeResult.forEach(item => {
        volumeByCurrency[item._id] = item.totalVolume;
        totalVolume += item.totalVolume;
      });

      // Get user rating
      const user = await User.findById(userId);
      const averageRating = user?.rating || 0;

      const stats = {
        totalTrades,
        activeTrades,
        completedTrades,
        totalVolume,
        averageRating,
        activeOffers,
        volumeByCurrency,
        lastUpdated: new Date()
      };

      logger.info(`Retrieved trading stats for user ${userId}`);
      return stats;

    } catch (error) {
      logger.error(`Error getting trading stats: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update user statistics
   */
  async updateUserStats(userId) {
    try {
      const completedTrades = await Trade.countDocuments({
        $or: [{ buyer: userId }, { seller: userId }],
        status: 'completed'
      });

      await User.findByIdAndUpdate(userId, {
        completedTrades,
        lastSeen: new Date()
      });

      logger.info(`Updated stats for user ${userId}: ${completedTrades} completed trades`);

    } catch (error) {
      logger.error(`Error updating user stats: ${error.message}`);
    }
  }

  /**
   * Validate offer data
   */
  validateOfferData(offerData) {
    const {
      type,
      cryptocurrency,
      fiatCurrency,
      amount,
      minAmount,
      maxAmount,
      paymentMethods
    } = offerData;

    if (!['buy', 'sell'].includes(type)) {
      throw new AppError('Invalid offer type', 400);
    }

    if (!this.supportedCryptocurrencies.includes(cryptocurrency)) {
      throw new AppError('Unsupported cryptocurrency', 400);
    }

    if (!this.supportedFiatCurrencies.includes(fiatCurrency)) {
      throw new AppError('Unsupported fiat currency', 400);
    }

    if (minAmount > maxAmount) {
      throw new AppError('Min amount cannot be greater than max amount', 400);
    }

    if (amount < minAmount || amount > maxAmount) {
      throw new AppError('Amount must be within min/max range', 400);
    }

    if (!Array.isArray(paymentMethods) || paymentMethods.length === 0) {
      throw new AppError('At least one payment method is required', 400);
    }

    const invalidMethods = paymentMethods.filter(method => 
      !this.supportedPaymentMethods.includes(method)
    );

    if (invalidMethods.length > 0) {
      throw new AppError(`Unsupported payment methods: ${invalidMethods.join(', ')}`, 400);
    }
  }
}

module.exports = new TradingService();
