/**
 * Unit Tests for Wallet Service
 * Comprehensive testing for wallet operations
 */

const { describe, it, expect, beforeEach, afterEach, jest } = require('@jest/globals');
const WalletService = require('../../../src/services/walletService');
const { ethers } = require('ethers');

// Mock dependencies
jest.mock('ethers');
jest.mock('../../../src/utils/logger');
jest.mock('../../../src/config/redis');

describe('WalletService', () => {
  let walletService;
  let mockProvider;
  let mockWallet;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock ethers provider
    mockProvider = {
      getBalance: jest.fn(),
      getTransactionCount: jest.fn(),
      estimateGas: jest.fn(),
      sendTransaction: jest.fn(),
      getTransaction: jest.fn(),
      getTransactionReceipt: jest.fn(),
      getGasPrice: jest.fn(),
    };

    // Mock ethers wallet
    mockWallet = {
      address: '******************************************',
      privateKey: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
      connect: jest.fn().mockReturnValue(mockWallet),
      signMessage: jest.fn(),
      signTransaction: jest.fn(),
    };

    ethers.JsonRpcProvider.mockReturnValue(mockProvider);
    ethers.Wallet.mockReturnValue(mockWallet);
    ethers.isAddress.mockReturnValue(true);
    ethers.parseEther.mockImplementation((value) => BigInt(value) * BigInt(10**18));
    ethers.formatEther.mockImplementation((value) => (Number(value) / 10**18).toString());

    walletService = new WalletService();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Wallet Creation', () => {
    it('should create a new wallet successfully', async () => {
      const result = await walletService.createWallet();

      expect(result).toHaveProperty('address');
      expect(result).toHaveProperty('privateKey');
      expect(result).toHaveProperty('mnemonic');
      expect(ethers.Wallet).toHaveBeenCalled();
    });

    it('should validate wallet address format', () => {
      const validAddress = '******************************************';
      const invalidAddress = 'invalid-address';

      ethers.isAddress.mockReturnValueOnce(true);
      expect(walletService.isValidAddress(validAddress)).toBe(true);

      ethers.isAddress.mockReturnValueOnce(false);
      expect(walletService.isValidAddress(invalidAddress)).toBe(false);
    });

    it('should import wallet from private key', async () => {
      const privateKey = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      
      const result = await walletService.importWallet(privateKey);

      expect(result).toHaveProperty('address');
      expect(result.address).toBe(mockWallet.address);
      expect(ethers.Wallet).toHaveBeenCalledWith(privateKey);
    });

    it('should throw error for invalid private key', async () => {
      const invalidPrivateKey = 'invalid-key';
      ethers.Wallet.mockImplementation(() => {
        throw new Error('Invalid private key');
      });

      await expect(walletService.importWallet(invalidPrivateKey))
        .rejects.toThrow('Invalid private key');
    });
  });

  describe('Balance Operations', () => {
    it('should get ETH balance successfully', async () => {
      const address = '******************************************';
      const mockBalance = BigInt('1000000000000000000'); // 1 ETH
      
      mockProvider.getBalance.mockResolvedValue(mockBalance);
      ethers.formatEther.mockReturnValue('1.0');

      const balance = await walletService.getBalance(address);

      expect(balance).toBe('1.0');
      expect(mockProvider.getBalance).toHaveBeenCalledWith(address);
    });

    it('should handle balance fetch errors', async () => {
      const address = '******************************************';
      
      mockProvider.getBalance.mockRejectedValue(new Error('Network error'));

      await expect(walletService.getBalance(address))
        .rejects.toThrow('Network error');
    });

    it('should get token balance for ERC20 tokens', async () => {
      const address = '******************************************';
      const tokenAddress = '******************************************';
      const mockBalance = BigInt('1000000'); // 1 USDT (6 decimals)

      // Mock contract interaction
      const mockContract = {
        balanceOf: jest.fn().mockResolvedValue(mockBalance),
        decimals: jest.fn().mockResolvedValue(6),
      };
      ethers.Contract.mockReturnValue(mockContract);

      const balance = await walletService.getTokenBalance(address, tokenAddress);

      expect(balance).toBe('1.0');
      expect(mockContract.balanceOf).toHaveBeenCalledWith(address);
    });
  });

  describe('Transaction Operations', () => {
    it('should send ETH transaction successfully', async () => {
      const fromAddress = '******************************************';
      const toAddress = '0x8ba1f109551bD432803012645Hac136c';
      const amount = '1.0';
      const privateKey = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';

      const mockTxHash = '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
      const mockTx = {
        hash: mockTxHash,
        wait: jest.fn().mockResolvedValue({ status: 1 }),
      };

      mockProvider.getTransactionCount.mockResolvedValue(0);
      mockProvider.getGasPrice.mockResolvedValue(BigInt('20000000000')); // 20 gwei
      mockProvider.estimateGas.mockResolvedValue(BigInt('21000'));
      mockWallet.sendTransaction.mockResolvedValue(mockTx);

      const result = await walletService.sendTransaction({
        from: fromAddress,
        to: toAddress,
        amount,
        privateKey,
      });

      expect(result).toHaveProperty('hash', mockTxHash);
      expect(mockWallet.sendTransaction).toHaveBeenCalled();
    });

    it('should handle insufficient balance error', async () => {
      const fromAddress = '******************************************';
      const toAddress = '0x8ba1f109551bD432803012645Hac136c';
      const amount = '1000.0'; // Large amount
      const privateKey = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';

      mockProvider.getBalance.mockResolvedValue(BigInt('100000000000000000')); // 0.1 ETH
      mockWallet.sendTransaction.mockRejectedValue(new Error('insufficient funds'));

      await expect(walletService.sendTransaction({
        from: fromAddress,
        to: toAddress,
        amount,
        privateKey,
      })).rejects.toThrow('insufficient funds');
    });

    it('should estimate gas fees correctly', async () => {
      const fromAddress = '******************************************';
      const toAddress = '0x8ba1f109551bD432803012645Hac136c';
      const amount = '1.0';

      mockProvider.estimateGas.mockResolvedValue(BigInt('21000'));
      mockProvider.getGasPrice.mockResolvedValue(BigInt('20000000000')); // 20 gwei

      const gasEstimate = await walletService.estimateGas({
        from: fromAddress,
        to: toAddress,
        amount,
      });

      expect(gasEstimate).toHaveProperty('gasLimit');
      expect(gasEstimate).toHaveProperty('gasPrice');
      expect(gasEstimate).toHaveProperty('totalCost');
    });
  });

  describe('Message Signing', () => {
    it('should sign message correctly', async () => {
      const message = 'Hello, KryptoPesa!';
      const privateKey = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const mockSignature = '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c';

      mockWallet.signMessage.mockResolvedValue(mockSignature);

      const signature = await walletService.signMessage(message, privateKey);

      expect(signature).toBe(mockSignature);
      expect(mockWallet.signMessage).toHaveBeenCalledWith(message);
    });

    it('should verify message signature', async () => {
      const message = 'Hello, KryptoPesa!';
      const signature = '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c';
      const address = '******************************************';

      ethers.verifyMessage.mockReturnValue(address);

      const isValid = await walletService.verifySignature(message, signature, address);

      expect(isValid).toBe(true);
      expect(ethers.verifyMessage).toHaveBeenCalledWith(message, signature);
    });

    it('should reject invalid signature', async () => {
      const message = 'Hello, KryptoPesa!';
      const signature = 'invalid-signature';
      const address = '******************************************';

      ethers.verifyMessage.mockReturnValue('0xDifferentAddress');

      const isValid = await walletService.verifySignature(message, signature, address);

      expect(isValid).toBe(false);
    });
  });

  describe('Transaction History', () => {
    it('should fetch transaction history', async () => {
      const address = '******************************************';
      const mockTransactions = [
        {
          hash: '0xabc123',
          from: address,
          to: '0xdef456',
          value: BigInt('1000000000000000000'),
          blockNumber: 12345,
          timestamp: 1640995200,
        },
      ];

      // Mock external API call for transaction history
      walletService.getTransactionHistory = jest.fn().mockResolvedValue(mockTransactions);

      const history = await walletService.getTransactionHistory(address);

      expect(history).toHaveLength(1);
      expect(history[0]).toHaveProperty('hash', '0xabc123');
    });

    it('should handle empty transaction history', async () => {
      const address = '******************************************';

      walletService.getTransactionHistory = jest.fn().mockResolvedValue([]);

      const history = await walletService.getTransactionHistory(address);

      expect(history).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle network connectivity issues', async () => {
      const address = '******************************************';
      
      mockProvider.getBalance.mockRejectedValue(new Error('ECONNREFUSED'));

      await expect(walletService.getBalance(address))
        .rejects.toThrow('ECONNREFUSED');
    });

    it('should handle invalid transaction parameters', async () => {
      const invalidParams = {
        from: 'invalid-address',
        to: 'invalid-address',
        amount: 'invalid-amount',
        privateKey: 'invalid-key',
      };

      ethers.isAddress.mockReturnValue(false);

      await expect(walletService.sendTransaction(invalidParams))
        .rejects.toThrow();
    });

    it('should handle gas estimation failures', async () => {
      const params = {
        from: '******************************************',
        to: '0x8ba1f109551bD432803012645Hac136c',
        amount: '1.0',
      };

      mockProvider.estimateGas.mockRejectedValue(new Error('Gas estimation failed'));

      await expect(walletService.estimateGas(params))
        .rejects.toThrow('Gas estimation failed');
    });
  });

  describe('Security Features', () => {
    it('should not expose private keys in logs', async () => {
      const privateKey = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      
      const wallet = await walletService.importWallet(privateKey);
      
      // Ensure private key is not in the returned object for logging
      expect(wallet).not.toHaveProperty('privateKey');
      expect(wallet).toHaveProperty('address');
    });

    it('should validate transaction amounts', async () => {
      const negativeAmount = '-1.0';
      const zeroAmount = '0';
      const invalidAmount = 'abc';

      expect(() => walletService.validateAmount(negativeAmount)).toThrow();
      expect(() => walletService.validateAmount(zeroAmount)).toThrow();
      expect(() => walletService.validateAmount(invalidAmount)).toThrow();
    });

    it('should implement rate limiting for transactions', async () => {
      const params = {
        from: '******************************************',
        to: '0x8ba1f109551bD432803012645Hac136c',
        amount: '1.0',
        privateKey: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
      };

      // Mock rate limiting
      walletService.checkRateLimit = jest.fn()
        .mockResolvedValueOnce(true)  // First call passes
        .mockResolvedValueOnce(false); // Second call fails

      await expect(walletService.sendTransaction(params)).resolves.toBeDefined();
      await expect(walletService.sendTransaction(params)).rejects.toThrow('Rate limit exceeded');
    });
  });
});
