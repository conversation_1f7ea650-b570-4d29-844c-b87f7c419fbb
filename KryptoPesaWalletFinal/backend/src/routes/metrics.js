const express = require('express');
const { performanceMonitor } = require('../middleware/performanceMonitoring');
const { performanceMonitoringService } = require('../services/performanceMonitoringService');
const { circuitBreakerMonitoringService } = require('../services/circuitBreakerMonitoring');
const User = require('../models/User');
const Trade = require('../models/Trade');
const Offer = require('../models/Offer');
const Dispute = require('../models/Dispute');

const router = express.Router();

// Middleware to restrict access to metrics (optional authentication)
const authenticateMetrics = (req, res, next) => {
  const metricsToken = process.env.METRICS_TOKEN;
  
  if (metricsToken) {
    const providedToken = req.headers['x-metrics-token'] || req.query.token;
    
    if (providedToken !== metricsToken) {
      return res.status(401).json({
        error: 'Unauthorized access to metrics endpoint'
      });
    }
  }
  
  next();
};

// Apply authentication middleware
router.use(authenticateMetrics);

// Basic metrics endpoint
router.get('/', (req, res) => {
  try {
    const legacyMetrics = performanceMonitor.getMetrics();
    const enhancedMetrics = performanceMonitoringService.getPerformanceMetrics();

    res.json({
      legacy: legacyMetrics,
      enhanced: enhancedMetrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to retrieve metrics',
      message: error.message
    });
  }
});

// Prometheus-style metrics endpoint
router.get('/prometheus', (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    const prometheusMetrics = formatPrometheusMetrics(metrics);
    
    res.set('Content-Type', 'text/plain');
    res.send(prometheusMetrics);
  } catch (error) {
    res.status(500).send(`# Error generating metrics: ${error.message}`);
  }
});

// Application-specific metrics
router.get('/application', async (req, res) => {
  try {
    const [
      totalUsers,
      activeUsers,
      totalTrades,
      activeTrades,
      completedTrades,
      totalOffers,
      activeOffers,
      totalDisputes,
      openDisputes
    ] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ 
        lastActive: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } 
      }),
      Trade.countDocuments(),
      Trade.countDocuments({ 
        status: { $in: ['created', 'funded', 'payment_sent'] } 
      }),
      Trade.countDocuments({ status: 'completed' }),
      Offer.countDocuments(),
      Offer.countDocuments({ status: 'active' }),
      Dispute.countDocuments(),
      Dispute.countDocuments({ 
        status: { $in: ['open', 'investigating'] } 
      })
    ]);

    // Calculate volume metrics
    const volumeMetrics = await Trade.aggregate([
      { $match: { status: 'completed' } },
      {
        $group: {
          _id: null,
          totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } },
          totalFiatVolume: { $sum: '$fiat.amount' },
          avgTradeSize: { $avg: { $toDouble: '$cryptocurrency.amount' } }
        }
      }
    ]);

    // Recent activity metrics
    const recentMetrics = await Promise.all([
      Trade.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }),
      User.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }),
      Offer.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      })
    ]);

    const applicationMetrics = {
      users: {
        total: totalUsers,
        active24h: activeUsers,
        new24h: recentMetrics[1],
        activityRate: totalUsers > 0 ? (activeUsers / totalUsers * 100).toFixed(2) : 0
      },
      trades: {
        total: totalTrades,
        active: activeTrades,
        completed: completedTrades,
        new24h: recentMetrics[0],
        completionRate: totalTrades > 0 ? (completedTrades / totalTrades * 100).toFixed(2) : 0,
        volume: volumeMetrics[0] || { totalVolume: 0, totalFiatVolume: 0, avgTradeSize: 0 }
      },
      offers: {
        total: totalOffers,
        active: activeOffers,
        new24h: recentMetrics[2],
        activeRate: totalOffers > 0 ? (activeOffers / totalOffers * 100).toFixed(2) : 0
      },
      disputes: {
        total: totalDisputes,
        open: openDisputes,
        disputeRate: totalTrades > 0 ? (totalDisputes / totalTrades * 100).toFixed(2) : 0
      },
      timestamp: new Date().toISOString()
    };

    res.json(applicationMetrics);
  } catch (error) {
    res.status(500).json({
      error: 'Failed to retrieve application metrics',
      message: error.message
    });
  }
});

// Health score endpoint
router.get('/health-score', (req, res) => {
  try {
    const healthScore = performanceMonitor.getHealthScore();
    const metrics = performanceMonitor.getMetrics();
    
    let status = 'healthy';
    if (healthScore < 70) status = 'degraded';
    if (healthScore < 50) status = 'unhealthy';
    if (healthScore < 30) status = 'critical';

    res.json({
      score: healthScore,
      status,
      factors: {
        errorRate: metrics.errors.errorRate,
        avgResponseTime: metrics.responseTime.average,
        p95ResponseTime: metrics.responseTime.p95,
        memoryUsage: metrics.memory.current.heapUsed,
        activeConnections: metrics.activeConnections
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to calculate health score',
      message: error.message
    });
  }
});

// Enhanced performance metrics endpoint
router.get('/performance', (req, res) => {
  try {
    const metrics = performanceMonitoringService.getPerformanceMetrics();
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve enhanced performance metrics',
      message: error.message
    });
  }
});

// Performance alerts endpoint
router.get('/performance/alerts', (req, res) => {
  try {
    const metrics = performanceMonitoringService.getPerformanceMetrics();

    const alerts = [];

    // Check response time alerts
    if (metrics.responseTime.average > 1000) {
      alerts.push({
        type: 'HIGH_AVERAGE_RESPONSE_TIME',
        severity: metrics.responseTime.average > 5000 ? 'critical' : 'warning',
        message: `Average response time is ${metrics.responseTime.average.toFixed(2)}ms`,
        threshold: 1000,
        current: metrics.responseTime.average
      });
    }

    // Check error rate alerts
    if (metrics.errors.rate > 0.02) {
      alerts.push({
        type: 'HIGH_ERROR_RATE',
        severity: metrics.errors.rate > 0.05 ? 'critical' : 'warning',
        message: `Error rate is ${(metrics.errors.rate * 100).toFixed(2)}%`,
        threshold: 0.02,
        current: metrics.errors.rate
      });
    }

    // Check memory usage alerts
    const memoryPercent = parseFloat(metrics.system.memory.heapUsagePercent);
    if (memoryPercent > 80) {
      alerts.push({
        type: 'HIGH_MEMORY_USAGE',
        severity: memoryPercent > 90 ? 'critical' : 'warning',
        message: `Memory usage is ${memoryPercent}%`,
        threshold: 80,
        current: memoryPercent
      });
    }

    res.json({
      success: true,
      data: {
        alerts,
        alertCount: alerts.length,
        criticalAlerts: alerts.filter(a => a.severity === 'critical').length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve performance alerts',
      message: error.message
    });
  }
});

// Reset metrics endpoint (for testing/debugging)
router.post('/reset', (req, res) => {
  try {
    performanceMonitor.reset();
    performanceMonitoringService.resetMetrics();
    res.json({
      message: 'Metrics reset successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to reset metrics',
      message: error.message
    });
  }
});

// Real-time metrics endpoint (for dashboards)
router.get('/realtime', (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    const currentMemory = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    const realtimeMetrics = {
      timestamp: new Date().toISOString(),
      requests: {
        total: metrics.requests.total,
        rps: metrics.requests.requestsPerSecond.toFixed(2),
        active: metrics.activeConnections
      },
      performance: {
        avgResponseTime: metrics.responseTime.average.toFixed(2),
        p95ResponseTime: metrics.responseTime.p95.toFixed(2),
        errorRate: (metrics.errors.errorRate * 100).toFixed(2)
      },
      system: {
        memory: {
          heapUsed: (currentMemory.heapUsed / 1024 / 1024).toFixed(2),
          heapTotal: (currentMemory.heapTotal / 1024 / 1024).toFixed(2),
          rss: (currentMemory.rss / 1024 / 1024).toFixed(2)
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        uptime: process.uptime()
      },
      health: {
        score: performanceMonitor.getHealthScore(),
        status: performanceMonitor.getHealthScore() >= 70 ? 'healthy' : 'degraded'
      }
    };

    res.json(realtimeMetrics);
  } catch (error) {
    res.status(500).json({
      error: 'Failed to retrieve real-time metrics',
      message: error.message
    });
  }
});

// Helper function to format metrics for Prometheus
function formatPrometheusMetrics(metrics) {
  const lines = [];
  
  // Request metrics
  lines.push('# HELP http_requests_total Total number of HTTP requests');
  lines.push('# TYPE http_requests_total counter');
  lines.push(`http_requests_total ${metrics.requests.total}`);
  
  lines.push('# HELP http_requests_successful_total Total number of successful HTTP requests');
  lines.push('# TYPE http_requests_successful_total counter');
  lines.push(`http_requests_successful_total ${metrics.requests.successful}`);
  
  lines.push('# HELP http_requests_failed_total Total number of failed HTTP requests');
  lines.push('# TYPE http_requests_failed_total counter');
  lines.push(`http_requests_failed_total ${metrics.requests.failed}`);
  
  // Response time metrics
  lines.push('# HELP http_request_duration_ms HTTP request duration in milliseconds');
  lines.push('# TYPE http_request_duration_ms histogram');
  lines.push(`http_request_duration_ms_avg ${metrics.responseTime.average}`);
  lines.push(`http_request_duration_ms_p95 ${metrics.responseTime.p95}`);
  lines.push(`http_request_duration_ms_p99 ${metrics.responseTime.p99}`);
  
  // Memory metrics
  const currentMemory = process.memoryUsage();
  lines.push('# HELP process_memory_heap_used_bytes Process heap memory used in bytes');
  lines.push('# TYPE process_memory_heap_used_bytes gauge');
  lines.push(`process_memory_heap_used_bytes ${currentMemory.heapUsed}`);
  
  lines.push('# HELP process_memory_rss_bytes Process RSS memory in bytes');
  lines.push('# TYPE process_memory_rss_bytes gauge');
  lines.push(`process_memory_rss_bytes ${currentMemory.rss}`);
  
  // Error metrics
  lines.push('# HELP application_errors_total Total number of application errors');
  lines.push('# TYPE application_errors_total counter');
  lines.push(`application_errors_total ${metrics.errors.total}`);
  
  // Active connections
  lines.push('# HELP http_active_connections Current number of active HTTP connections');
  lines.push('# TYPE http_active_connections gauge');
  lines.push(`http_active_connections ${metrics.activeConnections}`);
  
  // Health score
  lines.push('# HELP application_health_score Application health score (0-100)');
  lines.push('# TYPE application_health_score gauge');
  lines.push(`application_health_score ${performanceMonitor.getHealthScore()}`);
  
  return lines.join('\n') + '\n';
}

// Circuit Breaker Metrics Endpoints

// Circuit breaker metrics endpoint
router.get('/circuit-breakers', (req, res) => {
  try {
    const metrics = circuitBreakerMonitoringService.getCircuitBreakerMetrics();

    res.json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve circuit breaker metrics',
      message: error.message
    });
  }
});

// Circuit breaker state endpoint
router.get('/circuit-breakers/state', (req, res) => {
  try {
    const metrics = circuitBreakerMonitoringService.getCircuitBreakerMetrics();

    // Extract just the state information
    const states = {};
    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      states[name] = {
        state: cbMetrics.currentState,
        healthScore: cbMetrics.healthScore,
        failureCount: cbMetrics.failureCount,
        successCount: cbMetrics.successCount,
        uptime: cbMetrics.uptime,
        lastStateChange: cbMetrics.lastStateChange
      };
    }

    res.json({
      success: true,
      data: {
        states,
        summary: metrics.summary,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve circuit breaker states',
      message: error.message
    });
  }
});

// Circuit breaker alerts endpoint
router.get('/circuit-breakers/alerts', (req, res) => {
  try {
    const metrics = circuitBreakerMonitoringService.getCircuitBreakerMetrics();
    const alerts = [];

    // Generate alerts based on current state
    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      // Health score alerts
      if (cbMetrics.healthScore < 50) {
        alerts.push({
          type: 'CIRCUIT_BREAKER_UNHEALTHY',
          circuitBreaker: name,
          severity: cbMetrics.healthScore < 25 ? 'critical' : 'high',
          message: `Circuit breaker ${name} health score is ${cbMetrics.healthScore}`,
          healthScore: cbMetrics.healthScore
        });
      }

      // State alerts
      if (cbMetrics.currentState === 'OPEN') {
        alerts.push({
          type: 'CIRCUIT_BREAKER_OPEN',
          circuitBreaker: name,
          severity: 'high',
          message: `Circuit breaker ${name} is in OPEN state`,
          state: cbMetrics.currentState
        });
      }

      // High failure rate alerts
      if (cbMetrics.failureRate > 0.5) {
        alerts.push({
          type: 'CIRCUIT_BREAKER_HIGH_FAILURE_RATE',
          circuitBreaker: name,
          severity: 'medium',
          message: `Circuit breaker ${name} has high failure rate: ${(cbMetrics.failureRate * 100).toFixed(2)}%`,
          failureRate: cbMetrics.failureRate
        });
      }

      // Low uptime alerts
      if (cbMetrics.uptime < 95) {
        alerts.push({
          type: 'CIRCUIT_BREAKER_LOW_UPTIME',
          circuitBreaker: name,
          severity: cbMetrics.uptime < 90 ? 'high' : 'medium',
          message: `Circuit breaker ${name} has low uptime: ${cbMetrics.uptime.toFixed(2)}%`,
          uptime: cbMetrics.uptime
        });
      }

      // Recent trips alerts
      if (cbMetrics.recentTrips >= 3) {
        alerts.push({
          type: 'CIRCUIT_BREAKER_RAPID_TRIPS',
          circuitBreaker: name,
          severity: 'critical',
          message: `Circuit breaker ${name} has ${cbMetrics.recentTrips} recent trips`,
          recentTrips: cbMetrics.recentTrips
        });
      }
    }

    res.json({
      success: true,
      data: {
        alerts,
        alertCount: alerts.length,
        criticalAlerts: alerts.filter(a => a.severity === 'critical').length,
        highAlerts: alerts.filter(a => a.severity === 'high').length,
        mediumAlerts: alerts.filter(a => a.severity === 'medium').length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve circuit breaker alerts',
      message: error.message
    });
  }
});

// Circuit breaker control endpoints (for testing/admin)
router.post('/circuit-breakers/:name/force-state', (req, res) => {
  try {
    const { name } = req.params;
    const { state } = req.body;

    if (!state || !['open', 'closed'].includes(state.toLowerCase())) {
      return res.status(400).json({
        success: false,
        error: 'Invalid state. Use "open" or "closed"'
      });
    }

    const result = circuitBreakerMonitoringService.forceCircuitBreakerState(name, state);

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Reset circuit breaker metrics endpoint
router.post('/circuit-breakers/reset', (req, res) => {
  try {
    const { circuitBreaker } = req.body;

    const result = circuitBreakerMonitoringService.resetCircuitBreakerMetrics(circuitBreaker);

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Circuit breaker Prometheus metrics
router.get('/circuit-breakers/prometheus', (req, res) => {
  try {
    const metrics = circuitBreakerMonitoringService.getCircuitBreakerMetrics();
    const lines = [];

    // Circuit breaker state metrics
    lines.push('# HELP circuit_breaker_state Current state of circuit breaker (0=CLOSED, 1=HALF_OPEN, 2=OPEN)');
    lines.push('# TYPE circuit_breaker_state gauge');

    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      const stateValue = cbMetrics.currentState === 'CLOSED' ? 0 :
                        cbMetrics.currentState === 'HALF_OPEN' ? 1 : 2;
      lines.push(`circuit_breaker_state{name="${name}"} ${stateValue}`);
    }

    // Circuit breaker health score
    lines.push('# HELP circuit_breaker_health_score Health score of circuit breaker (0-100)');
    lines.push('# TYPE circuit_breaker_health_score gauge');

    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      lines.push(`circuit_breaker_health_score{name="${name}"} ${cbMetrics.healthScore}`);
    }

    // Circuit breaker failure rate
    lines.push('# HELP circuit_breaker_failure_rate Failure rate of circuit breaker (0-1)');
    lines.push('# TYPE circuit_breaker_failure_rate gauge');

    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      lines.push(`circuit_breaker_failure_rate{name="${name}"} ${cbMetrics.failureRate}`);
    }

    // Circuit breaker uptime
    lines.push('# HELP circuit_breaker_uptime Uptime percentage of circuit breaker (0-100)');
    lines.push('# TYPE circuit_breaker_uptime gauge');

    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      lines.push(`circuit_breaker_uptime{name="${name}"} ${cbMetrics.uptime}`);
    }

    // Circuit breaker total trips
    lines.push('# HELP circuit_breaker_total_trips Total number of circuit breaker trips');
    lines.push('# TYPE circuit_breaker_total_trips counter');

    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      lines.push(`circuit_breaker_total_trips{name="${name}"} ${cbMetrics.totalTrips}`);
    }

    // Circuit breaker total resets
    lines.push('# HELP circuit_breaker_total_resets Total number of circuit breaker resets');
    lines.push('# TYPE circuit_breaker_total_resets counter');

    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      lines.push(`circuit_breaker_total_resets{name="${name}"} ${cbMetrics.totalResets}`);
    }

    res.set('Content-Type', 'text/plain');
    res.send(lines.join('\n') + '\n');
  } catch (error) {
    res.status(500).send(`# Error generating circuit breaker metrics: ${error.message}`);
  }
});

module.exports = router;
