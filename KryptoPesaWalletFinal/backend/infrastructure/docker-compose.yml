# Docker Compose Configuration for KryptoPesa High-Availability Deployment
# Supports auto-scaling and load balancing for 10,000+ concurrent users

version: '3.8'

services:
  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: kryptopesa-nginx
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Status endpoint
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl:ro
      - nginx-cache:/var/cache/nginx
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - api-1
      - api-2
      - api-3
      - api-4
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # API Server Instances (Horizontal Scaling)
  api-1:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kryptopesa-api-1
    environment:
      - NODE_ENV=production
      - PORT=3001
      - INSTANCE_ID=api-1
      - DB_HOST=postgres-primary
      - DB_READ_REPLICA_HOST=postgres-replica
      - REDIS_HOST=redis-cluster
      - MONGODB_URI=mongodb://mongo-1:27017,mongo-2:27017,mongo-3:27017/kryptopesa?replicaSet=rs0
    volumes:
      - ./logs/api-1:/app/logs
    depends_on:
      - postgres-primary
      - redis-cluster
      - mongo-1
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  api-2:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kryptopesa-api-2
    environment:
      - NODE_ENV=production
      - PORT=3002
      - INSTANCE_ID=api-2
      - DB_HOST=postgres-primary
      - DB_READ_REPLICA_HOST=postgres-replica
      - REDIS_HOST=redis-cluster
      - MONGODB_URI=mongodb://mongo-1:27017,mongo-2:27017,mongo-3:27017/kryptopesa?replicaSet=rs0
    volumes:
      - ./logs/api-2:/app/logs
    depends_on:
      - postgres-primary
      - redis-cluster
      - mongo-1
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  api-3:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kryptopesa-api-3
    environment:
      - NODE_ENV=production
      - PORT=3003
      - INSTANCE_ID=api-3
      - DB_HOST=postgres-primary
      - DB_READ_REPLICA_HOST=postgres-replica
      - REDIS_HOST=redis-cluster
      - MONGODB_URI=mongodb://mongo-1:27017,mongo-2:27017,mongo-3:27017/kryptopesa?replicaSet=rs0
    volumes:
      - ./logs/api-3:/app/logs
    depends_on:
      - postgres-primary
      - redis-cluster
      - mongo-1
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  api-4:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kryptopesa-api-4
    environment:
      - NODE_ENV=production
      - PORT=3004
      - INSTANCE_ID=api-4
      - DB_HOST=postgres-primary
      - DB_READ_REPLICA_HOST=postgres-replica
      - REDIS_HOST=redis-cluster
      - MONGODB_URI=mongodb://mongo-1:27017,mongo-2:27017,mongo-3:27017/kryptopesa?replicaSet=rs0
    volumes:
      - ./logs/api-4:/app/logs
    depends_on:
      - postgres-primary
      - redis-cluster
      - mongo-1
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Primary Database
  postgres-primary:
    image: postgres:15-alpine
    container_name: kryptopesa-postgres-primary
    environment:
      - POSTGRES_DB=kryptopesa
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=${POSTGRES_REPLICATION_PASSWORD}
    volumes:
      - postgres-primary-data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Read Replica
  postgres-replica:
    image: postgres:15-alpine
    container_name: kryptopesa-postgres-replica
    environment:
      - POSTGRES_DB=kryptopesa
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - PGUSER=postgres
    volumes:
      - postgres-replica-data:/var/lib/postgresql/data
    depends_on:
      - postgres-primary
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Redis Cluster for Caching and Sessions
  redis-cluster:
    image: redis:7-alpine
    container_name: kryptopesa-redis
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Replica Set for Document Storage
  mongo-1:
    image: mongo:6
    container_name: kryptopesa-mongo-1
    command: mongod --replSet rs0 --bind_ip_all
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    volumes:
      - mongo-1-data:/data/db
    ports:
      - "27017:27017"
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  mongo-2:
    image: mongo:6
    container_name: kryptopesa-mongo-2
    command: mongod --replSet rs0 --bind_ip_all
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    volumes:
      - mongo-2-data:/data/db
    ports:
      - "27018:27017"
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  mongo-3:
    image: mongo:6
    container_name: kryptopesa-mongo-3
    command: mongod --replSet rs0 --bind_ip_all
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    volumes:
      - mongo-3-data:/data/db
    ports:
      - "27019:27017"
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: kryptopesa-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  grafana:
    image: grafana/grafana:latest
    container_name: kryptopesa-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Log Aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: kryptopesa-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: kryptopesa-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - kryptopesa-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

# Volumes for persistent data
volumes:
  postgres-primary-data:
  postgres-replica-data:
  redis-data:
  mongo-1-data:
  mongo-2-data:
  mongo-3-data:
  prometheus-data:
  grafana-data:
  elasticsearch-data:
  nginx-cache:

# Network configuration
networks:
  kryptopesa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
