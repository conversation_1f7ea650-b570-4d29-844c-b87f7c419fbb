{"version": 3, "file": "static/js/629.0425286b.chunk.js", "mappings": "8MAA4E,MAA8M,EAAzLA,KAAiBC,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,CAAC,KAACC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAJ,SAAC,mBAA2B,KAACC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACG,MAAM,gBAAeL,SAAC,wCAAgD,M", "sources": ["pages/SettingsPage.js"], "sourcesContent": ["import React from 'react'; import { Box, Typography } from '@mui/material'; const SettingsPage = () => { return ( <Box> <Typography variant=\"h4\" gutterBottom> SettingsPage </Typography> <Typography color=\"textSecondary\"> This page is under development... </Typography> </Box> ); }; export default SettingsPage;\n"], "names": ["SettingsPage", "_jsxs", "Box", "children", "_jsx", "Typography", "variant", "gutterBottom", "color"], "sourceRoot": ""}