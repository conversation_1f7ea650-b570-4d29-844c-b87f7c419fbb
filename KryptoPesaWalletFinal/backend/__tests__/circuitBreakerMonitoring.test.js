/**
 * Circuit Breaker Monitoring Service Tests
 * Comprehensive testing for circuit breaker state transitions and monitoring
 */

const sinon = require('sinon');
const { CircuitBreakerMonitoringService } = require('../src/services/circuitBreakerMonitoring');
const { CircuitBreaker } = require('../src/utils/circuitBreaker');

describe('Circuit Breaker Monitoring Service', () => {
  let sandbox;
  let testCircuitBreaker;
  let monitoringService;

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
    
    // Create test circuit breaker
    testCircuitBreaker = new CircuitBreaker({
      name: 'TestService',
      failureThreshold: 3,
      timeout: 5000,
      monitoringPeriod: 1000
    });

    // Add test circuit breaker to the collection
    circuitBreakers.testService = testCircuitBreaker;

    // Create fresh monitoring service instance for testing
    monitoringService = new CircuitBreakerMonitoringService();

    // Mock monitoring integration service
    sandbox.stub(monitoringIntegrationService, 'triggerAlert').resolves();

    // Initialize monitoring service
    await monitoringService.initialize();
  });

  afterEach(async () => {
    await monitoringService.shutdown();
    delete circuitBreakers.testService;
    sandbox.restore();
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      const freshService = new CircuitBreakerMonitoringService();
      const result = await freshService.initialize();

      expect(result).toBe(true);
      expect(freshService.isInitialized).toBe(true);

      await freshService.shutdown();
    });

    test('should not initialize twice', async () => {
      const result = await monitoringService.initialize();
      expect(result).to.be.true;
    });

    test('should initialize state tracking for all circuit breakers', () => {
      expect(monitoringService.stateHistory.has('testService')).to.be.true;
      expect(monitoringService.metrics.stateTransitions.has('testService')).to.be.true;
      expect(monitoringService.metrics.totalTrips.has('testService')).to.be.true;
    });
  });

  describe('State Transition Monitoring', () => {
    test('should detect CLOSED to OPEN transition', async () => {
      // Force multiple failures to trip the circuit breaker
      for (let i = 0; i < 3; i++) {
        try {
          await testCircuitBreaker.execute(async () => {
            throw new Error('Test failure');
          });
        } catch (error) {
          // Expected to fail
        }
      }

      // Wait for monitoring to detect the state change
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Manually trigger monitoring check
      await monitoringService.monitorCircuitBreaker('testService', testCircuitBreaker);

      const state = testCircuitBreaker.getState();
      expect(state.state).to.equal('OPEN');
      
      const trips = monitoringService.metrics.totalTrips.get('testService');
      expect(trips).to.be.greaterThan(0);
    });

    test('should detect OPEN to HALF_OPEN transition', async () => {
      // First trip the circuit breaker
      testCircuitBreaker.forceOpen();
      await monitoringService.monitorCircuitBreaker('testService', testCircuitBreaker);

      // Wait for timeout and force half-open
      testCircuitBreaker.nextAttempt = Date.now() - 1000; // Make it ready for half-open
      
      // Simulate a request that would trigger half-open
      try {
        await testCircuitBreaker.execute(async () => {
          return 'success';
        });
      } catch (error) {
        // May fail, but should transition to half-open first
      }

      await monitoringService.monitorCircuitBreaker('testService', testCircuitBreaker);

      const transitions = monitoringService.metrics.stateTransitions.get('testService');
      expect(transitions.openToHalfOpen).to.be.greaterThanOrEqual(0);
    });

    test('should detect HALF_OPEN to CLOSED transition', async () => {
      // Set circuit breaker to half-open state
      testCircuitBreaker.state = 'HALF_OPEN';
      testCircuitBreaker.successCount = 0;
      testCircuitBreaker.failureCount = 0;

      // Execute successful operations to close the circuit
      const successesNeeded = Math.ceil(testCircuitBreaker.failureThreshold / 2);
      for (let i = 0; i < successesNeeded; i++) {
        await testCircuitBreaker.execute(async () => {
          return 'success';
        });
      }

      await monitoringService.monitorCircuitBreaker('testService', testCircuitBreaker);

      const state = testCircuitBreaker.getState();
      expect(state.state).to.equal('CLOSED');
      
      const resets = monitoringService.metrics.totalResets.get('testService');
      expect(resets).to.be.greaterThan(0);
    });

    test('should detect HALF_OPEN to OPEN transition', async () => {
      // Set circuit breaker to half-open state
      testCircuitBreaker.state = 'HALF_OPEN';
      testCircuitBreaker.successCount = 0;
      testCircuitBreaker.failureCount = 0;

      // Execute failing operation to trip back to open
      try {
        await testCircuitBreaker.execute(async () => {
          throw new Error('Test failure in half-open');
        });
      } catch (error) {
        // Expected to fail
      }

      await monitoringService.monitorCircuitBreaker('testService', testCircuitBreaker);

      const transitions = monitoringService.metrics.stateTransitions.get('testService');
      expect(transitions.halfOpenToOpen).to.be.greaterThanOrEqual(0);
    });
  });

  describe('Alert Conditions', () => {
    test('should trigger alert for circuit breaker trip', async () => {
      // Trip the circuit breaker
      testCircuitBreaker.forceOpen();
      
      await monitoringService.handleCircuitBreakerTrip('testService', 'CLOSED');

      expect(monitoringIntegrationService.triggerAlert).to.have.been.calledWith(
        'CIRCUIT_BREAKER_TRIPPED',
        sinon.match({
          circuitBreaker: 'testService',
          fromState: 'CLOSED',
          toState: 'OPEN',
          severity: 'high'
        })
      );
    });

    test('should trigger alert for circuit breaker recovery', async () => {
      await monitoringService.handleCircuitBreakerReset('testService', 'OPEN');

      expect(monitoringIntegrationService.triggerAlert).to.have.been.calledWith(
        'CIRCUIT_BREAKER_RECOVERED',
        sinon.match({
          circuitBreaker: 'testService',
          fromState: 'OPEN',
          toState: 'CLOSED',
          severity: 'info'
        })
      );
    });

    test('should trigger alert for rapid trips', async () => {
      // Simulate rapid trips
      const patterns = [];
      for (let i = 0; i < 4; i++) {
        patterns.push({
          timestamp: Date.now() - (i * 60000), // 1 minute apart
          type: 'trip'
        });
      }
      monitoringService.metrics.failurePatterns.set('testService', patterns);

      await monitoringService.checkAlertConditions();

      expect(monitoringIntegrationService.triggerAlert).to.have.been.calledWith(
        'CIRCUIT_BREAKER_RAPID_TRIPS',
        sinon.match({
          circuitBreaker: 'testService',
          severity: 'critical'
        })
      );
    });

    test('should trigger alert for long open duration', async () => {
      // Set circuit breaker to open with old timestamp
      testCircuitBreaker.forceOpen();
      monitoringService.metrics.lastStateChange.set('testService', Date.now() - (15 * 60 * 1000)); // 15 minutes ago

      await monitoringService.checkAlertConditions();

      expect(monitoringIntegrationService.triggerAlert).to.have.been.calledWith(
        'CIRCUIT_BREAKER_LONG_OPEN',
        sinon.match({
          circuitBreaker: 'testService',
          severity: 'high'
        })
      );
    });

    test('should trigger alert for high failure rate', async () => {
      // Set high failure rate in circuit breaker metrics
      testCircuitBreaker.metrics.totalRequests = 100;
      testCircuitBreaker.metrics.totalFailures = 60; // 60% failure rate

      await monitoringService.checkAlertConditions();

      expect(monitoringIntegrationService.triggerAlert).to.have.been.calledWith(
        'CIRCUIT_BREAKER_HIGH_FAILURE_RATE',
        sinon.match({
          circuitBreaker: 'testService',
          severity: 'high'
        })
      );
    });

    test('should trigger alert for stuck in half-open', (done) => {
      // Set circuit breaker to half-open
      testCircuitBreaker.state = 'HALF_OPEN';
      
      // Trigger half-open handler which sets timeout
      monitoringService.handleCircuitBreakerHalfOpen('testService', 'OPEN');

      // Wait for timeout (should be much shorter in test)
      setTimeout(() => {
        expect(monitoringIntegrationService.triggerAlert).to.have.been.calledWith(
          'CIRCUIT_BREAKER_STUCK_HALF_OPEN',
          sinon.match({
            circuitBreaker: 'testService',
            severity: 'medium'
          })
        );
        done();
      }, 100); // Short timeout for testing
    });
  });

  describe('Metrics Collection', () => {
    test('should collect comprehensive circuit breaker metrics', () => {
      const metrics = monitoringService.getCircuitBreakerMetrics();

      expect(metrics).to.have.property('circuitBreakers');
      expect(metrics).to.have.property('summary');
      expect(metrics).to.have.property('timestamp');

      expect(metrics.circuitBreakers).to.have.property('testService');
      
      const testServiceMetrics = metrics.circuitBreakers.testService;
      expect(testServiceMetrics).to.have.property('currentState');
      expect(testServiceMetrics).to.have.property('totalRequests');
      expect(testServiceMetrics).to.have.property('totalFailures');
      expect(testServiceMetrics).to.have.property('totalSuccesses');
      expect(testServiceMetrics).to.have.property('healthScore');
      expect(testServiceMetrics).to.have.property('uptime');
      expect(testServiceMetrics).to.have.property('stateTransitions');
    });

    test('should calculate health score correctly', () => {
      // Test healthy circuit breaker
      testCircuitBreaker.state = 'CLOSED';
      testCircuitBreaker.metrics.totalRequests = 100;
      testCircuitBreaker.metrics.totalFailures = 5; // 5% failure rate
      
      monitoringService.calculateHealthScore('testService', testCircuitBreaker.getState());
      
      const healthScore = monitoringService.metrics.healthScores.get('testService');
      expect(healthScore).to.be.greaterThan(80);
    });

    test('should calculate health score for degraded circuit breaker', () => {
      // Test degraded circuit breaker
      testCircuitBreaker.state = 'OPEN';
      testCircuitBreaker.metrics.totalRequests = 100;
      testCircuitBreaker.metrics.totalFailures = 60; // 60% failure rate
      
      monitoringService.calculateHealthScore('testService', testCircuitBreaker.getState());
      
      const healthScore = monitoringService.metrics.healthScores.get('testService');
      expect(healthScore).to.be.lessThan(50);
    });

    test('should calculate uptime correctly', () => {
      // Add some state history
      const history = [
        { timestamp: Date.now() - 10000, state: 'CLOSED' },
        { timestamp: Date.now() - 8000, state: 'OPEN' },
        { timestamp: Date.now() - 5000, state: 'CLOSED' },
        { timestamp: Date.now(), state: 'CLOSED' }
      ];
      monitoringService.stateHistory.set('testService', history);

      const uptime = monitoringService.calculateUptime('testService');
      expect(uptime).to.be.greaterThan(0);
      expect(uptime).to.be.lessThanOrEqual(100);
    });

    test('should generate circuit breaker summary', () => {
      const testMetrics = {
        testService1: {
          currentState: 'CLOSED',
          healthScore: 90,
          totalTrips: 2,
          totalResets: 2,
          uptime: 95
        },
        testService2: {
          currentState: 'OPEN',
          healthScore: 30,
          totalTrips: 5,
          totalResets: 3,
          uptime: 70
        }
      };

      const summary = monitoringService.getCircuitBreakerSummary(testMetrics);

      expect(summary.totalCircuitBreakers).to.equal(2);
      expect(summary.healthyCount).to.equal(1);
      expect(summary.unhealthyCount).to.equal(1);
      expect(summary.openCount).to.equal(1);
      expect(summary.closedCount).to.equal(1);
      expect(summary.totalTrips).to.equal(7);
      expect(summary.totalResets).to.equal(5);
      expect(summary.averageHealthScore).to.equal(60);
      expect(summary.averageUptime).to.equal(82.5);
    });
  });

  describe('Manual Controls', () => {
    test('should force circuit breaker to open state', async () => {
      const result = await monitoringService.forceCircuitBreakerState('testService', 'open');

      expect(result.success).to.be.true;
      expect(result.newState).to.equal('OPEN');
      expect(testCircuitBreaker.getState().state).to.equal('OPEN');
    });

    test('should force circuit breaker to closed state', async () => {
      testCircuitBreaker.forceOpen();
      
      const result = await monitoringService.forceCircuitBreakerState('testService', 'closed');

      expect(result.success).to.be.true;
      expect(result.newState).to.equal('CLOSED');
      expect(testCircuitBreaker.getState().state).to.equal('CLOSED');
    });

    test('should throw error for invalid circuit breaker name', async () => {
      try {
        await monitoringService.forceCircuitBreakerState('nonexistent', 'open');
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('not found');
      }
    });

    test('should throw error for invalid state', async () => {
      try {
        await monitoringService.forceCircuitBreakerState('testService', 'invalid');
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('Invalid state');
      }
    });
  });

  describe('Metrics Reset', () => {
    test('should reset specific circuit breaker metrics', () => {
      // Add some metrics first
      monitoringService.metrics.totalTrips.set('testService', 5);
      monitoringService.metrics.totalResets.set('testService', 3);

      const result = monitoringService.resetCircuitBreakerMetrics('testService');

      expect(result.success).to.be.true;
      expect(monitoringService.metrics.totalTrips.get('testService')).to.equal(0);
      expect(monitoringService.metrics.totalResets.get('testService')).to.equal(0);
    });

    test('should reset all circuit breaker metrics', () => {
      // Add some metrics first
      monitoringService.metrics.totalTrips.set('testService', 5);
      
      const result = monitoringService.resetCircuitBreakerMetrics();

      expect(result.success).to.be.true;
      expect(result.circuitBreaker).to.equal('all');
      expect(monitoringService.metrics.totalTrips.get('testService')).to.equal(0);
    });

    test('should throw error for nonexistent circuit breaker', () => {
      try {
        monitoringService.resetCircuitBreakerMetrics('nonexistent');
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('not found');
      }
    });
  });

  describe('Cleanup and Shutdown', () => {
    test('should clean up old metrics data', () => {
      // Add old failure patterns
      const oldPatterns = [
        { timestamp: Date.now() - (25 * 60 * 60 * 1000), type: 'trip' }, // 25 hours ago
        { timestamp: Date.now() - (1 * 60 * 60 * 1000), type: 'trip' }   // 1 hour ago
      ];
      monitoringService.metrics.failurePatterns.set('testService', oldPatterns);

      monitoringService.cleanupOldMetrics();

      const patterns = monitoringService.metrics.failurePatterns.get('testService');
      expect(patterns).to.have.length(1); // Only recent pattern should remain
    });

    test('should shutdown gracefully', async () => {
      await monitoringService.shutdown();

      expect(monitoringService.isInitialized).to.be.false;
      expect(monitoringService.monitoringInterval).to.be.null;
    });
  });
});
