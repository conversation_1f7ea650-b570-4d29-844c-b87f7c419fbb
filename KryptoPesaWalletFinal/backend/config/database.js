const { Pool } = require('pg');
const Redis = require('redis');
const mongoose = require('mongoose');

/**
 * Optimized Database Configuration for 10,000+ Concurrent Users
 * Implements connection pooling, read replicas, and caching strategies
 */

// PostgreSQL Configuration with Connection Pooling
const createPostgresPool = () => {
  const config = {
    // Primary database connection
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'kryptopesa',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD,
    
    // Connection Pool Configuration for High Concurrency
    max: parseInt(process.env.DB_POOL_MAX) || 50, // Maximum connections
    min: parseInt(process.env.DB_POOL_MIN) || 10, // Minimum connections
    idleTimeoutMillis: 30000, // Close idle connections after 30s
    connectionTimeoutMillis: 10000, // Connection timeout
    maxUses: 7500, // Rotate connections after 7500 uses
    
    // Performance Optimizations
    statement_timeout: 30000, // 30 second query timeout
    query_timeout: 30000,
    application_name: 'KryptoPesa-API',
    
    // SSL Configuration for Production
    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false,
  };

  const pool = new Pool(config);

  // Connection event handlers
  pool.on('connect', (client) => {
    console.log('New PostgreSQL client connected');
    
    // Set session-level optimizations
    client.query(`
      SET work_mem = '256MB';
      SET maintenance_work_mem = '512MB';
      SET effective_cache_size = '4GB';
      SET random_page_cost = 1.1;
      SET seq_page_cost = 1.0;
    `).catch(err => console.error('Failed to set session optimizations:', err));
  });

  pool.on('error', (err) => {
    console.error('PostgreSQL pool error:', err);
  });

  pool.on('remove', () => {
    console.log('PostgreSQL client removed from pool');
  });

  return pool;
};

// Read Replica Configuration for Load Distribution
const createReadReplicaPool = () => {
  if (!process.env.DB_READ_REPLICA_HOST) {
    return null; // No read replica configured
  }

  const config = {
    host: process.env.DB_READ_REPLICA_HOST,
    port: process.env.DB_READ_REPLICA_PORT || 5432,
    database: process.env.DB_NAME || 'kryptopesa',
    user: process.env.DB_READ_REPLICA_USER || process.env.DB_USER,
    password: process.env.DB_READ_REPLICA_PASSWORD || process.env.DB_PASSWORD,
    
    // Smaller pool for read operations
    max: parseInt(process.env.DB_READ_POOL_MAX) || 30,
    min: parseInt(process.env.DB_READ_POOL_MIN) || 5,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 10000,
    
    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false,
  };

  return new Pool(config);
};

// Redis Configuration for Caching and Session Management
const createRedisClient = () => {
  const redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD,
    db: process.env.REDIS_DB || 0,
    
    // Connection Pool Configuration
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
    maxLoadingTimeout: 5000,
    
    // Performance Optimizations
    lazyConnect: true,
    keepAlive: 30000,
    family: 4, // IPv4
    
    // Cluster configuration for high availability
    enableOfflineQueue: false,
  };

  const client = Redis.createClient(redisConfig);

  client.on('connect', () => {
    console.log('Redis client connected');
  });

  client.on('error', (err) => {
    console.error('Redis client error:', err);
  });

  client.on('ready', () => {
    console.log('Redis client ready');
    
    // Set Redis optimizations
    client.config('SET', 'maxmemory-policy', 'allkeys-lru');
    client.config('SET', 'timeout', '300');
  });

  return client;
};

// MongoDB Configuration for Document Storage
const createMongoConnection = () => {
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa';
  
  const options = {
    // Connection Pool Configuration
    maxPoolSize: 50, // Maximum connections
    minPoolSize: 10, // Minimum connections
    maxIdleTimeMS: 30000, // Close idle connections
    serverSelectionTimeoutMS: 10000, // Server selection timeout
    socketTimeoutMS: 45000, // Socket timeout
    
    // Performance Optimizations
    bufferMaxEntries: 0, // Disable mongoose buffering
    bufferCommands: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    
    // Write Concern for Performance
    writeConcern: {
      w: 'majority',
      j: true, // Journal acknowledgment
      wtimeout: 10000
    },
    
    // Read Preference for Load Distribution
    readPreference: 'secondaryPreferred',
    readConcern: { level: 'majority' }
  };

  mongoose.connect(mongoUri, options);

  mongoose.connection.on('connected', () => {
    console.log('MongoDB connected');
  });

  mongoose.connection.on('error', (err) => {
    console.error('MongoDB connection error:', err);
  });

  mongoose.connection.on('disconnected', () => {
    console.log('MongoDB disconnected');
  });

  return mongoose.connection;
};

// Database Health Check
const checkDatabaseHealth = async () => {
  const health = {
    postgres: false,
    redis: false,
    mongodb: false,
    timestamp: new Date().toISOString()
  };

  try {
    // Check PostgreSQL
    const pgResult = await postgresPool.query('SELECT 1');
    health.postgres = pgResult.rows.length > 0;
  } catch (err) {
    console.error('PostgreSQL health check failed:', err);
  }

  try {
    // Check Redis
    const redisResult = await redisClient.ping();
    health.redis = redisResult === 'PONG';
  } catch (err) {
    console.error('Redis health check failed:', err);
  }

  try {
    // Check MongoDB
    const mongoResult = await mongoose.connection.db.admin().ping();
    health.mongodb = mongoResult.ok === 1;
  } catch (err) {
    console.error('MongoDB health check failed:', err);
  }

  return health;
};

// Connection Pool Statistics
const getPoolStats = () => {
  return {
    postgres: {
      totalCount: postgresPool.totalCount,
      idleCount: postgresPool.idleCount,
      waitingCount: postgresPool.waitingCount
    },
    redis: {
      status: redisClient.status,
      commandQueueLength: redisClient.commandQueueLength || 0
    },
    mongodb: {
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port
    }
  };
};

// Graceful Shutdown
const closeConnections = async () => {
  console.log('Closing database connections...');
  
  try {
    await postgresPool.end();
    console.log('PostgreSQL pool closed');
  } catch (err) {
    console.error('Error closing PostgreSQL pool:', err);
  }

  try {
    if (readReplicaPool) {
      await readReplicaPool.end();
      console.log('Read replica pool closed');
    }
  } catch (err) {
    console.error('Error closing read replica pool:', err);
  }

  try {
    await redisClient.quit();
    console.log('Redis client closed');
  } catch (err) {
    console.error('Error closing Redis client:', err);
  }

  try {
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  } catch (err) {
    console.error('Error closing MongoDB connection:', err);
  }
};

// Initialize connections
const postgresPool = createPostgresPool();
const readReplicaPool = createReadReplicaPool();
const redisClient = createRedisClient();
const mongoConnection = createMongoConnection();

// Export configured connections
module.exports = {
  // Database connections
  postgres: postgresPool,
  readReplica: readReplicaPool,
  redis: redisClient,
  mongodb: mongoConnection,
  
  // Utility functions
  checkHealth: checkDatabaseHealth,
  getStats: getPoolStats,
  close: closeConnections,
  
  // Query helpers
  query: async (text, params, useReadReplica = false) => {
    const pool = useReadReplica && readReplicaPool ? readReplicaPool : postgresPool;
    const start = Date.now();
    
    try {
      const result = await pool.query(text, params);
      const duration = Date.now() - start;
      
      // Log slow queries
      if (duration > 1000) {
        console.warn(`Slow query detected (${duration}ms):`, text);
      }
      
      return result;
    } catch (err) {
      console.error('Database query error:', err);
      throw err;
    }
  },
  
  // Transaction helper
  transaction: async (callback) => {
    const client = await postgresPool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (err) {
      await client.query('ROLLBACK');
      throw err;
    } finally {
      client.release();
    }
  }
};
