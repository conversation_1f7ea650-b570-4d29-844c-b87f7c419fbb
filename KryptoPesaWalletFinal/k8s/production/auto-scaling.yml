# KryptoPesa Auto-Scaling Configuration
# Enterprise-grade horizontal and vertical pod autoscaling for production

apiVersion: v1
kind: Namespace
metadata:
  name: kryptopesa-production
  labels:
    environment: production
    app: kryptopesa

---
# Horizontal Pod Autoscaler for Backend
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: kryptopesa-production
  labels:
    app: backend
    environment: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-blue
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      selectPolicy: Min

---
# Horizontal Pod Autoscaler for Admin Dashboard
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: admin-hpa
  namespace: kryptopesa-production
  labels:
    app: admin
    environment: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: admin-blue
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
# Vertical Pod Autoscaler for Backend (Optional)
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: backend-vpa
  namespace: kryptopesa-production
  labels:
    app: backend
    environment: production
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-blue
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: backend
      minAllowed:
        cpu: 100m
        memory: 256Mi
      maxAllowed:
        cpu: 2
        memory: 4Gi
      controlledResources: ["cpu", "memory"]

---
# Pod Disruption Budget for Backend
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: backend-pdb
  namespace: kryptopesa-production
  labels:
    app: backend
    environment: production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: backend

---
# Pod Disruption Budget for Admin
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: admin-pdb
  namespace: kryptopesa-production
  labels:
    app: admin
    environment: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: admin

---
# Resource Quotas for Production Namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: production-quota
  namespace: kryptopesa-production
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    pods: "50"
    services: "20"
    persistentvolumeclaims: "10"
    secrets: "20"
    configmaps: "20"

---
# Limit Range for Pod Resources
apiVersion: v1
kind: LimitRange
metadata:
  name: production-limits
  namespace: kryptopesa-production
spec:
  limits:
  - type: Pod
    max:
      cpu: "4"
      memory: 8Gi
    min:
      cpu: 50m
      memory: 64Mi
  - type: Container
    default:
      cpu: 500m
      memory: 1Gi
    defaultRequest:
      cpu: 100m
      memory: 256Mi
    max:
      cpu: "2"
      memory: 4Gi
    min:
      cpu: 50m
      memory: 64Mi

---
# Priority Class for Critical Workloads
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: kryptopesa-critical
value: 1000
globalDefault: false
description: "Priority class for critical KryptoPesa workloads"

---
# Priority Class for Normal Workloads
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: kryptopesa-normal
value: 100
globalDefault: false
description: "Priority class for normal KryptoPesa workloads"

---
# Custom Metrics API Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-metrics-config
  namespace: kryptopesa-production
data:
  config.yaml: |
    rules:
    - seriesQuery: 'http_requests_total{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^(.*)_total"
        as: "${1}_per_second"
      metricsQuery: 'rate(<<.Series>>{<<.LabelMatchers>>}[2m])'
    - seriesQuery: 'trading_volume_total{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^(.*)_total"
        as: "${1}_per_minute"
      metricsQuery: 'rate(<<.Series>>{<<.LabelMatchers>>}[1m])'

---
# Network Policy for Auto-Scaling Components
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: autoscaling-network-policy
  namespace: kryptopesa-production
spec:
  podSelector:
    matchLabels:
      app: backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 8080  # Metrics port
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080  # Metrics port
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
