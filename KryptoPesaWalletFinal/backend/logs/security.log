{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: REDIS_URL appears to be using a default/weak value\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: AWS_KMS_KEY_ID is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: AWS_REGION is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: SENTRY_DSN is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: FIREBASE_PRIVATE_KEY is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: FIREBASE_CLIENT_EMAIL is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: MONGODB_URI appears to contain development URL in production\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: REDIS_URL appears to contain development URL in production\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"endpoint":"/health","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mSuspicious user agent detected:\u001b[39m","timestamp":"2025-07-03 20:21:15:2115","userAgent":"curl/8.7.1"}
{"endpoint":"/metrics","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mSuspicious user agent detected:\u001b[39m","timestamp":"2025-07-03 20:21:43:2143","userAgent":"curl/8.7.1"}
{"endpoint":"/metrics/prometheus","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mSuspicious user agent detected:\u001b[39m","timestamp":"2025-07-03 20:22:01:221","userAgent":"curl/8.7.1"}
{"endpoint":"/health","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mSuspicious user agent detected:\u001b[39m","timestamp":"2025-07-03 20:35:57:3557","userAgent":"curl/8.7.1"}
