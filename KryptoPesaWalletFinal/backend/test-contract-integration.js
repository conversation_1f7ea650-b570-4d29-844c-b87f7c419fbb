const { ethers } = require('ethers');
require('dotenv').config();

async function testContractIntegration() {
  console.log('🧪 Testing Smart Contract Integration...\n');

  try {
    // Test 1: Connect to localhost network
    console.log('1️⃣ Testing network connection...');
    const provider = new ethers.providers.JsonRpcProvider('http://127.0.0.1:8545');
    const network = await provider.getNetwork();
    console.log('✅ Connected to network:', network.name, 'Chain ID:', network.chainId);

    // Test 2: Check contract address
    console.log('\n2️⃣ Testing contract configuration...');
    const contractAddress = process.env.ESCROW_CONTRACT_ADDRESS;
    console.log('Contract Address:', contractAddress);
    
    if (!contractAddress) {
      throw new Error('ESCROW_CONTRACT_ADDRESS not set in environment');
    }

    // Test 3: Check if contract exists
    console.log('\n3️⃣ Testing contract existence...');
    const code = await provider.getCode(contractAddress);
    if (code === '0x') {
      throw new Error('No contract found at address');
    }
    console.log('✅ Contract exists at address');

    // Test 4: Test contract interaction (basic call)
    console.log('\n4️⃣ Testing contract interaction...');
    
    // Simple ABI for testing basic functions
    const testABI = [
      "function nextTradeId() view returns (uint256)",
      "function defaultCommissionRate() view returns (uint256)",
      "function feeCollector() view returns (address)"
    ];

    const contract = new ethers.Contract(contractAddress, testABI, provider);
    
    const nextTradeId = await contract.nextTradeId();
    const commissionRate = await contract.defaultCommissionRate();
    const feeCollector = await contract.feeCollector();
    
    console.log('✅ Contract interaction successful:');
    console.log('   - Next Trade ID:', nextTradeId.toString());
    console.log('   - Commission Rate:', commissionRate.toString(), 'basis points');
    console.log('   - Fee Collector:', feeCollector);

    console.log('\n🎉 All tests passed! Smart contract integration is working correctly.');
    return true;

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
testContractIntegration()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test error:', error);
    process.exit(1);
  });
