{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\u001b[39m","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/mongoose/lib/connection.js:816:11)\n    at NativeConnection.openUri (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/mongoose/lib/connection.js:791:11)\n    at async connectDB (/Users/<USER>/KryptoPesaWalletFinal/backend/src/config/database.js:15:18)","timestamp":"2025-07-03 16:56:23:5623"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mApplication error recorded Operation `offers.find()` buffering timed out after 10000ms\u001b[39m","method":"GET","stack":"MongooseError: Operation `offers.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:185:23)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)","timestamp":"2025-07-03 19:52:10:5210","type":"MongooseError","url":"/api/offers","userAgent":"curl/8.7.1"}
{"error":"Operation `offers.find()` buffering timed out after 10000ms","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDevelopment Error:\u001b[39m","method":"GET","stack":"MongooseError: Operation `offers.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:185:23)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)","timestamp":"2025-07-03 19:52:10:5210","url":"/api/offers","userAgent":"curl/8.7.1"}
{"action":"user_login","error":"Operation `audit_logs.insertOne()` buffering timed out after 10000ms","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to create audit log entry\u001b[39m","resource":"user","timestamp":"2025-07-03 19:52:36:5236"}
{"action":"user_login","error":"Operation `audit_logs.insertOne()` buffering timed out after 10000ms","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to create audit log entry\u001b[39m","resource":"user","timestamp":"2025-07-03 19:52:36:5236"}
{"action":"user_login","error":"Operation `audit_logs.insertOne()` buffering timed out after 10000ms","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to create audit log entry\u001b[39m","resource":"user","timestamp":"2025-07-03 19:52:36:5236"}
{"action":"user_login","error":"Operation `audit_logs.insertOne()` buffering timed out after 10000ms","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to create audit log entry\u001b[39m","resource":"user","timestamp":"2025-07-03 19:52:36:5236"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mApplication error recorded Expected property name or '}' in JSON at position 1 (line 1 column 2)\u001b[39m","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/types/json.js:92:19)\n    at /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:238:16)\n    at done (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-03 19:57:25:5725","type":"SyntaxError","url":"/api/offers","userAgent":"curl/8.7.1"}
{"error":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDevelopment Error:\u001b[39m","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/types/json.js:92:19)\n    at /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:238:16)\n    at done (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-03 19:57:25:5725","url":"/api/offers","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mApplication error recorded Expected property name or '}' in JSON at position 1 (line 1 column 2)\u001b[39m","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/types/json.js:92:19)\n    at /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:238:16)\n    at done (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-03 19:57:32:5732","type":"SyntaxError","url":"/api/offers","userAgent":"curl/8.7.1"}
{"error":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDevelopment Error:\u001b[39m","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/types/json.js:92:19)\n    at /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:238:16)\n    at done (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-03 19:57:32:5732","url":"/api/offers","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mApplication error recorded Expected property name or '}' in JSON at position 1 (line 1 column 2)\u001b[39m","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/types/json.js:92:19)\n    at /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/body-parser/lib/read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:238:16)\n    at done (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-03 20:06:53:653","type":"SyntaxError","url":"/api/auth/login","userAgent":"curl/8.7.1"}
{"data":{"action":"system_error","endpoint":"/api/auth/login","error":{"message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)"},"ip":"::1","method":"POST","resource":"system","severity":"medium","statusCode":400,"success":false,"userAgent":"curl/8.7.1"},"error":"Operation `audit_logs.insertOne()` buffering timed out after 10000ms","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to create manual audit log entry\u001b[39m","timestamp":"2025-07-03 20:07:03:73"}
{"error":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnhandled Error:\u001b[39m","method":"POST","timestamp":"2025-07-03 20:07:03:73","url":"/api/auth/login"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mApplication error recorded this.sendCommand is not a function\u001b[39m","method":"GET","stack":"TypeError: this.sendCommand is not a function\n    at RedisStore.loadScript (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/rate-limit-redis/dist/index.cjs:44:31)\n    at RedisStore.runCommandWithRetry (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/rate-limit-redis/dist/index.cjs:72:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RedisStore.increment (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/rate-limit-redis/dist/index.cjs:77:21)\n    at async /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/express-rate-limit/dist/index.cjs:596:40\n    at async /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/express-rate-limit/dist/index.cjs:576:5","timestamp":"2025-07-03 20:12:14:1214","type":"TypeError","url":"/health","userAgent":"curl/8.7.1"}
{"error":"this.sendCommand is not a function","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnhandled Error:\u001b[39m","method":"GET","timestamp":"2025-07-03 20:12:14:1214","url":"/health"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mApplication error recorded this.sendCommand is not a function\u001b[39m","method":"GET","stack":"TypeError: this.sendCommand is not a function\n    at RedisStore.loadScript (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/rate-limit-redis/dist/index.cjs:44:31)\n    at RedisStore.runCommandWithRetry (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/rate-limit-redis/dist/index.cjs:72:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RedisStore.increment (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/rate-limit-redis/dist/index.cjs:77:21)\n    at async /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/express-rate-limit/dist/index.cjs:596:40\n    at async /Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/express-rate-limit/dist/index.cjs:576:5","timestamp":"2025-07-03 20:12:22:1222","type":"TypeError","url":"/health","userAgent":"curl/8.7.1"}
{"error":"this.sendCommand is not a function","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnhandled Error:\u001b[39m","method":"GET","timestamp":"2025-07-03 20:12:22:1222","url":"/health"}
