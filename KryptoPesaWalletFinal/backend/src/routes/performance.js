/**
 * Performance Monitoring Routes
 * Endpoints for monitoring service layer performance metrics
 */

const express = require('express');
const router = express.Router();
const { serviceLayerPerformance } = require('../services/serviceLayerPerformance');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const logger = require('../utils/logger');

/**
 * @route GET /api/performance/metrics
 * @desc Get comprehensive performance metrics
 * @access Admin only
 */
router.get('/metrics', authenticate, authorize('admin'), async (req, res) => {
  try {
    const metrics = serviceLayerPerformance.getPerformanceMetrics();
    
    res.json({
      success: true,
      data: {
        ...metrics,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      }
    });
  } catch (error) {
    logger.error('Error getting performance metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve performance metrics'
    });
  }
});

/**
 * @route GET /api/performance/cache-stats
 * @desc Get detailed cache statistics
 * @access Admin only
 */
router.get('/cache-stats', authenticate, authorize('admin'), async (req, res) => {
  try {
    const metrics = serviceLayerPerformance.getPerformanceMetrics();
    
    const cacheStats = {
      distributedCache: {
        size: metrics.memoryCacheSize,
        hitRate: metrics.cacheHitRate,
        hits: serviceLayerPerformance.performanceMetrics.cacheHits,
        misses: serviceLayerPerformance.performanceMetrics.cacheMisses
      },
      bulkQueues: metrics.bulkQueueSizes,
      thresholds: serviceLayerPerformance.thresholds
    };
    
    res.json({
      success: true,
      data: cacheStats
    });
  } catch (error) {
    logger.error('Error getting cache statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve cache statistics'
    });
  }
});

/**
 * @route GET /api/performance/query-stats
 * @desc Get query performance statistics
 * @access Admin only
 */
router.get('/query-stats', authenticate, authorize('admin'), async (req, res) => {
  try {
    const metrics = serviceLayerPerformance.getPerformanceMetrics();
    
    const queryStats = {
      totalOperations: metrics.totalOperations,
      averageResponseTime: metrics.averageResponseTime,
      slowOperations: metrics.slowOperations,
      slowOperationRate: metrics.slowOperationRate,
      queryOptimizations: metrics.queryOptimizations,
      priceApiCalls: metrics.priceApiCalls,
      priceApiErrors: metrics.priceApiErrors,
      errorRate: metrics.errorRate
    };
    
    res.json({
      success: true,
      data: queryStats
    });
  } catch (error) {
    logger.error('Error getting query statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve query statistics'
    });
  }
});

/**
 * @route POST /api/performance/reset-metrics
 * @desc Reset performance metrics
 * @access Admin only
 */
router.post('/reset-metrics', authenticate, authorize('admin'), async (req, res) => {
  try {
    serviceLayerPerformance.resetMetrics();
    
    logger.info('Performance metrics reset by admin');
    
    res.json({
      success: true,
      message: 'Performance metrics reset successfully'
    });
  } catch (error) {
    logger.error('Error resetting performance metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset performance metrics'
    });
  }
});

/**
 * @route POST /api/performance/optimize-indexes
 * @desc Trigger database index optimization
 * @access Admin only
 */
router.post('/optimize-indexes', authenticate, authorize('admin'), async (req, res) => {
  try {
    await serviceLayerPerformance.optimizeIndexes();
    
    logger.info('Database index optimization triggered by admin');
    
    res.json({
      success: true,
      message: 'Database index optimization completed successfully'
    });
  } catch (error) {
    logger.error('Error optimizing database indexes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to optimize database indexes'
    });
  }
});

/**
 * @route GET /api/performance/health
 * @desc Get service health status based on performance metrics
 * @access Admin only
 */
router.get('/health', authenticate, authorize('admin'), async (req, res) => {
  try {
    const metrics = serviceLayerPerformance.getPerformanceMetrics();
    
    // Determine health status based on metrics
    const healthChecks = {
      cachePerformance: {
        status: parseFloat(metrics.cacheHitRate) > 70 ? 'healthy' : 'warning',
        value: metrics.cacheHitRate,
        threshold: '70%'
      },
      responseTime: {
        status: metrics.averageResponseTime < 500 ? 'healthy' : 'warning',
        value: `${metrics.averageResponseTime}ms`,
        threshold: '500ms'
      },
      errorRate: {
        status: parseFloat(metrics.errorRate) < 1 ? 'healthy' : 'critical',
        value: metrics.errorRate,
        threshold: '1%'
      },
      slowOperations: {
        status: parseFloat(metrics.slowOperationRate) < 5 ? 'healthy' : 'warning',
        value: metrics.slowOperationRate,
        threshold: '5%'
      }
    };
    
    // Overall health status
    const hasCritical = Object.values(healthChecks).some(check => check.status === 'critical');
    const hasWarning = Object.values(healthChecks).some(check => check.status === 'warning');
    
    const overallStatus = hasCritical ? 'critical' : hasWarning ? 'warning' : 'healthy';
    
    res.json({
      success: true,
      data: {
        overallStatus,
        checks: healthChecks,
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }
    });
  } catch (error) {
    logger.error('Error getting service health status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve service health status'
    });
  }
});

/**
 * @route GET /api/performance/bulk-queue-status
 * @desc Get bulk operation queue status
 * @access Admin only
 */
router.get('/bulk-queue-status', authenticate, authorize('admin'), async (req, res) => {
  try {
    const metrics = serviceLayerPerformance.getPerformanceMetrics();
    
    const queueStatus = {
      queues: metrics.bulkQueueSizes,
      batchSize: serviceLayerPerformance.thresholds.bulkBatchSize,
      totalQueuedItems: Object.values(metrics.bulkQueueSizes).reduce((sum, size) => sum + size, 0)
    };
    
    res.json({
      success: true,
      data: queueStatus
    });
  } catch (error) {
    logger.error('Error getting bulk queue status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve bulk queue status'
    });
  }
});

/**
 * @route POST /api/performance/process-bulk-queues
 * @desc Manually trigger bulk queue processing
 * @access Admin only
 */
router.post('/process-bulk-queues', authenticate, authorize('admin'), async (req, res) => {
  try {
    const processedQueues = [];
    
    for (const queueKey of serviceLayerPerformance.bulkOperationQueue.keys()) {
      await serviceLayerPerformance.processBulkQueue(queueKey);
      processedQueues.push(queueKey);
    }
    
    logger.info('Bulk queues manually processed by admin:', processedQueues);
    
    res.json({
      success: true,
      message: 'Bulk queues processed successfully',
      data: {
        processedQueues,
        count: processedQueues.length
      }
    });
  } catch (error) {
    logger.error('Error processing bulk queues:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process bulk queues'
    });
  }
});

/**
 * @route GET /api/performance/system-info
 * @desc Get system performance information
 * @access Admin only
 */
router.get('/system-info', authenticate, authorize('admin'), async (req, res) => {
  try {
    const systemInfo = {
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
        uptime: process.uptime()
      },
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      environment: process.env.NODE_ENV,
      pid: process.pid
    };
    
    res.json({
      success: true,
      data: systemInfo
    });
  } catch (error) {
    logger.error('Error getting system information:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system information'
    });
  }
});

module.exports = router;
