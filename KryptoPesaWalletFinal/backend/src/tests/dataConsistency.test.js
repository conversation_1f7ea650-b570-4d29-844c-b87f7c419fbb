/**
 * Data Consistency Service Tests
 * Comprehensive tests for atomic operations and transaction handling
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { dataConsistencyService } = require('../services/dataConsistency');
const { transactionMonitoringService } = require('../services/transactionMonitoring');
const Trade = require('../models/Trade');
const Chat = require('../models/Chat');
const Offer = require('../models/Offer');
const Dispute = require('../models/Dispute');
const Wallet = require('../models/Wallet');
const User = require('../models/User');

describe('Data Consistency Service', () => {
  let mongoServer;
  let testUser1, testUser2;
  let testOffer;

  beforeAll(async () => {
    // Start in-memory MongoDB instance
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Create test users
    testUser1 = await User.create({
      email: '<EMAIL>',
      username: 'seller',
      password: 'password123',
      firstName: 'Test',
      lastName: 'Seller',
      isVerified: true
    });

    testUser2 = await User.create({
      email: '<EMAIL>',
      username: 'buyer',
      password: 'password123',
      firstName: 'Test',
      lastName: 'Buyer',
      isVerified: true
    });

    // Create test offer
    testOffer = await Offer.create({
      user: testUser1._id,
      type: 'sell',
      cryptocurrency: {
        symbol: 'USDT',
        network: 'polygon',
        amount: '1000',
        availableAmount: '1000'
      },
      fiatCurrency: {
        currency: 'KES',
        amount: '130000',
        paymentMethods: ['M-Pesa']
      },
      terms: 'Test offer terms',
      status: 'active'
    });

    // Create test wallets
    await Wallet.create({
      user: testUser1._id,
      balances: [{
        symbol: 'USDT',
        network: 'polygon',
        balance: '1000',
        lastUpdated: new Date()
      }]
    });

    await Wallet.create({
      user: testUser2._id,
      balances: [{
        symbol: 'USDT',
        network: 'polygon',
        balance: '0',
        lastUpdated: new Date()
      }]
    });
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean up collections before each test
    await Trade.deleteMany({});
    await Chat.deleteMany({});
    await Dispute.deleteMany({});
  });

  describe('Atomic Trade Creation', () => {
    test('should create trade, chat, and update offer atomically', async () => {
      const tradeData = {
        seller: testUser1._id,
        buyer: testUser2._id,
        offer: testOffer._id,
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '100'
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '13000'
        },
        status: 'created'
      };

      const offerData = {
        offerId: testOffer._id
      };

      const result = await dataConsistencyService.executeAtomicTradeCreation(
        tradeData,
        offerData,
        testUser2._id
      );

      // Verify trade was created
      expect(result.trade).toBeDefined();
      expect(result.trade.seller.toString()).toBe(testUser1._id.toString());
      expect(result.trade.buyer.toString()).toBe(testUser2._id.toString());

      // Verify chat was created and linked
      expect(result.chat).toBeDefined();
      expect(result.trade.chat.toString()).toBe(result.chat._id.toString());

      // Verify offer was updated
      const updatedOffer = await Offer.findById(testOffer._id);
      expect(parseFloat(updatedOffer.cryptocurrency.availableAmount)).toBe(900);

      // Verify timeline entry was added
      expect(result.trade.timeline).toHaveLength(1);
      expect(result.trade.timeline[0].status).toBe('created');
    });

    test('should rollback all changes if any operation fails', async () => {
      const tradeData = {
        seller: testUser1._id,
        buyer: testUser2._id,
        offer: testOffer._id,
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '2000' // More than available
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '260000'
        },
        status: 'created'
      };

      const offerData = {
        offerId: testOffer._id
      };

      await expect(
        dataConsistencyService.executeAtomicTradeCreation(
          tradeData,
          offerData,
          testUser2._id
        )
      ).rejects.toThrow();

      // Verify no trade was created
      const trades = await Trade.find({});
      expect(trades).toHaveLength(0);

      // Verify no chat was created
      const chats = await Chat.find({});
      expect(chats).toHaveLength(0);

      // Verify offer was not modified
      const offer = await Offer.findById(testOffer._id);
      expect(parseFloat(offer.cryptocurrency.availableAmount)).toBe(1000);
    });
  });

  describe('Atomic Escrow Creation', () => {
    let testTrade;

    beforeEach(async () => {
      testTrade = await Trade.create({
        seller: testUser1._id,
        buyer: testUser2._id,
        offer: testOffer._id,
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '100'
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '13000'
        },
        status: 'created'
      });
    });

    test('should create escrow and update trade status atomically', async () => {
      const escrowData = {
        escrowId: 'escrow_123',
        contractAddress: '0x123...abc',
        txHash: '0xabc...123',
        amount: '100'
      };

      const result = await dataConsistencyService.executeAtomicEscrowCreation(
        testTrade._id,
        escrowData,
        testUser1._id
      );

      // Verify trade was updated
      expect(result.status).toBe('funded');
      expect(result.escrow.escrowId).toBe('escrow_123');
      expect(result.escrow.status).toBe('active');

      // Verify timeline entry was added
      const timelineEntry = result.timeline.find(entry => entry.status === 'escrow_created');
      expect(timelineEntry).toBeDefined();
    });

    test('should fail if trade is not in correct status', async () => {
      // Update trade to wrong status
      testTrade.status = 'completed';
      await testTrade.save();

      const escrowData = {
        escrowId: 'escrow_123',
        contractAddress: '0x123...abc',
        txHash: '0xabc...123',
        amount: '100'
      };

      await expect(
        dataConsistencyService.executeAtomicEscrowCreation(
          testTrade._id,
          escrowData,
          testUser1._id
        )
      ).rejects.toThrow('Cannot create escrow for trade in status: completed');
    });
  });

  describe('Atomic Dispute Creation', () => {
    let testTrade;

    beforeEach(async () => {
      testTrade = await Trade.create({
        seller: testUser1._id,
        buyer: testUser2._id,
        offer: testOffer._id,
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '100'
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '13000'
        },
        status: 'funded',
        escrow: {
          escrowId: 'escrow_123',
          status: 'active'
        }
      });
    });

    test('should create dispute and update trade status atomically', async () => {
      const disputeData = {
        initiator: testUser2._id,
        category: 'payment_not_received',
        description: 'Payment was not received',
        evidence: []
      };

      const result = await dataConsistencyService.executeAtomicDisputeCreation(
        testTrade._id,
        disputeData,
        testUser2._id
      );

      // Verify dispute was created
      expect(result.dispute).toBeDefined();
      expect(result.dispute.category).toBe('payment_not_received');

      // Verify trade was updated
      expect(result.trade.status).toBe('disputed');
      expect(result.trade.dispute.toString()).toBe(result.dispute._id.toString());

      // Verify timeline entries were added
      const tradeTimelineEntry = result.trade.timeline.find(entry => entry.status === 'disputed');
      expect(tradeTimelineEntry).toBeDefined();
    });

    test('should fail if dispute already exists', async () => {
      // Create existing dispute
      const existingDispute = await Dispute.create({
        trade: testTrade._id,
        initiator: testUser2._id,
        category: 'other',
        description: 'Existing dispute'
      });

      testTrade.dispute = existingDispute._id;
      await testTrade.save();

      const disputeData = {
        initiator: testUser2._id,
        category: 'payment_not_received',
        description: 'Payment was not received',
        evidence: []
      };

      await expect(
        dataConsistencyService.executeAtomicDisputeCreation(
          testTrade._id,
          disputeData,
          testUser2._id
        )
      ).rejects.toThrow('Dispute already exists for this trade');
    });
  });

  describe('Atomic Balance Update', () => {
    test('should update multiple balances and add transaction atomically', async () => {
      const balanceUpdates = [
        {
          symbol: 'USDT',
          network: 'polygon',
          newBalance: '500'
        }
      ];

      const transactionData = {
        hash: '0xabc123',
        type: 'send',
        symbol: 'USDT',
        amount: '500',
        to: '0x123...abc',
        status: 'confirmed'
      };

      const result = await dataConsistencyService.executeAtomicBalanceUpdate(
        testUser1._id,
        balanceUpdates,
        transactionData
      );

      // Verify balance was updated
      const updatedWallet = await Wallet.findOne({ user: testUser1._id });
      const balance = updatedWallet.getBalance('USDT', 'polygon');
      expect(balance).toBe('500');

      // Verify transaction was added
      expect(updatedWallet.transactions).toHaveLength(1);
      expect(updatedWallet.transactions[0].hash).toBe('0xabc123');
    });
  });

  describe('Transaction Retry Logic', () => {
    test('should retry failed transactions with exponential backoff', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockRejectedValueOnce(new Error('Another failure'))
        .mockResolvedValueOnce({ success: true });

      // Mock the withTransaction method to use our mock operation
      const originalWithTransaction = dataConsistencyService.withTransaction;
      dataConsistencyService.withTransaction = jest.fn().mockImplementation(async (operation) => {
        return await mockOperation();
      });

      try {
        const result = await dataConsistencyService.withTransaction(mockOperation);
        expect(result).toEqual({ success: true });
        expect(mockOperation).toHaveBeenCalledTimes(3);
      } finally {
        // Restore original method
        dataConsistencyService.withTransaction = originalWithTransaction;
      }
    });
  });

  describe('Metrics and Monitoring', () => {
    test('should track transaction metrics correctly', async () => {
      // Reset metrics
      dataConsistencyService.resetMetrics();

      const tradeData = {
        seller: testUser1._id,
        buyer: testUser2._id,
        offer: testOffer._id,
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '50'
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '6500'
        },
        status: 'created'
      };

      const offerData = {
        offerId: testOffer._id
      };

      await dataConsistencyService.executeAtomicTradeCreation(
        tradeData,
        offerData,
        testUser2._id
      );

      const metrics = dataConsistencyService.getConsistencyMetrics();
      expect(metrics.totalTransactions).toBe(1);
      expect(metrics.successfulTransactions).toBe(1);
      expect(metrics.successRate).toBe(100);
    });
  });

  describe('Service Integration Tests', () => {
    test('should handle escrow service atomic operations', async () => {
      const escrowService = require('../services/escrowService');

      // Create a test trade first
      const testTrade = await Trade.create({
        seller: testUser1._id,
        buyer: testUser2._id,
        offer: testOffer._id,
        cryptocurrency: {
          symbol: 'USDT',
          network: 'polygon',
          amount: '100'
        },
        fiatCurrency: {
          currency: 'KES',
          amount: '13000'
        },
        status: 'created'
      });

      // Mock the blockchain escrow creation
      const mockEscrowResult = {
        escrowId: 'test_escrow_123',
        contractAddress: '0x123...abc',
        txHash: '0xabc...123'
      };

      // Mock the escrow creation methods
      escrowService.createEthereumEscrow = jest.fn().mockResolvedValue(mockEscrowResult);
      escrowService.monitorEscrow = jest.fn();

      // Test atomic escrow creation
      const result = await escrowService.createEscrow(testTrade._id, '100', 'USDT', 'polygon');

      expect(result.success).toBe(true);
      expect(result.escrowId).toBe('test_escrow_123');

      // Verify trade was updated atomically
      const updatedTrade = await Trade.findById(testTrade._id);
      expect(updatedTrade.status).toBe('escrowed');
      expect(updatedTrade.escrow.escrowId).toBe('test_escrow_123');
    });

    test('should handle wallet service atomic balance updates', async () => {
      const walletService = require('../services/walletService');
      const ethereumService = require('../services/blockchain/ethereumService');
      const bitcoinService = require('../services/blockchain/bitcoinService');

      // Mock blockchain service responses
      ethereumService.getNativeBalance = jest.fn().mockResolvedValue({ balance: '1.5' });
      ethereumService.getTokenBalance = jest.fn().mockResolvedValue({ balance: '1000' });
      bitcoinService.getBalance = jest.fn().mockResolvedValue({ balance: '0.05' });

      // Test atomic balance updates
      const result = await walletService.updateBalances(testUser1._id);

      expect(result).toBeDefined();
      expect(result.balances).toBeDefined();

      // Verify all balances were updated atomically
      const wallet = await Wallet.findOne({ user: testUser1._id });
      expect(wallet.balances.length).toBeGreaterThan(0);
    });

    test('should handle trading service atomic trade creation', async () => {
      const tradingService = require('../services/tradingService');

      const tradeData = {
        amount: 5000,
        paymentMethod: 'M-Pesa',
        message: 'Initial trade message'
      };

      // Test atomic trade creation with message
      const result = await tradingService.acceptOffer(testOffer._id, testUser2._id, tradeData);

      expect(result).toBeDefined();
      expect(result.buyer.toString()).toBe(testUser2._id.toString());
      expect(result.seller.toString()).toBe(testUser1._id.toString());

      // Verify chat was created
      expect(result.chat).toBeDefined();

      // Verify offer was updated
      const updatedOffer = await Offer.findById(testOffer._id);
      expect(parseFloat(updatedOffer.cryptocurrency.availableAmount)).toBeLessThan(1000);
    });
  });

  describe('Concurrent Operations', () => {
    test('should handle concurrent atomic operations safely', async () => {
      // Create multiple test trades
      const tradePromises = [];

      for (let i = 0; i < 5; i++) {
        const tradeData = {
          seller: testUser1._id,
          buyer: testUser2._id,
          offer: testOffer._id,
          cryptocurrency: {
            symbol: 'USDT',
            network: 'polygon',
            amount: '10'
          },
          fiatCurrency: {
            currency: 'KES',
            amount: '1300'
          },
          status: 'created'
        };

        const offerData = {
          offerId: testOffer._id
        };

        tradePromises.push(
          dataConsistencyService.executeAtomicTradeCreation(
            tradeData,
            offerData,
            testUser2._id
          )
        );
      }

      // Execute all operations concurrently
      const results = await Promise.allSettled(tradePromises);

      // Some should succeed, some might fail due to insufficient offer amount
      const successful = results.filter(r => r.status === 'fulfilled');
      const failed = results.filter(r => r.status === 'rejected');

      expect(successful.length + failed.length).toBe(5);

      // Verify offer consistency
      const finalOffer = await Offer.findById(testOffer._id);
      expect(parseFloat(finalOffer.cryptocurrency.availableAmount)).toBeGreaterThanOrEqual(0);
    });
  });
});
