#!/bin/bash

# Comprehensive Test Runner for KryptoPesa
# Runs all test suites with coverage reporting and quality gates

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_DIR="./backend"
FRONTEND_DIR="./a0-project"
COVERAGE_THRESHOLD=90
PERFORMANCE_THRESHOLD=2000 # 2 seconds max response time

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        error "npm is not installed"
        exit 1
    fi
    
    # Check Docker (for test databases)
    if ! command -v docker &> /dev/null; then
        warning "Docker is not installed - using in-memory databases"
    fi
    
    # Check Android SDK (for mobile tests)
    if [ -z "$ANDROID_HOME" ]; then
        warning "ANDROID_HOME not set - Android tests will be skipped"
    fi
    
    success "Prerequisites check completed"
}

# Setup test environment
setup_test_environment() {
    log "Setting up test environment..."
    
    # Set test environment variables
    export NODE_ENV=test
    export CI=true
    export COVERAGE_THRESHOLD=$COVERAGE_THRESHOLD
    
    # Create test directories
    mkdir -p test-results/backend
    mkdir -p test-results/frontend
    mkdir -p test-results/e2e
    mkdir -p test-results/performance
    
    # Start test services if needed
    if command -v docker &> /dev/null; then
        log "Starting test databases..."
        docker-compose -f docker-compose.test.yml up -d
        sleep 10
    fi
    
    success "Test environment setup completed"
}

# Run backend tests
run_backend_tests() {
    log "Running backend tests..."
    
    cd $BACKEND_DIR
    
    # Install dependencies
    npm ci --silent
    
    # Run linting
    log "Running backend linting..."
    npm run lint 2>&1 | tee ../test-results/backend/lint.log
    
    # Run unit tests
    log "Running backend unit tests..."
    npm run test:unit -- --coverage --coverageReporters=json-summary --coverageReporters=lcov --coverageReporters=text 2>&1 | tee ../test-results/backend/unit.log
    
    # Run integration tests
    log "Running backend integration tests..."
    npm run test:integration -- --coverage --coverageReporters=json-summary 2>&1 | tee ../test-results/backend/integration.log
    
    # Check coverage threshold
    COVERAGE=$(node -e "console.log(JSON.parse(require('fs').readFileSync('./coverage/coverage-summary.json')).total.lines.pct)")
    if (( $(echo "$COVERAGE < $COVERAGE_THRESHOLD" | bc -l) )); then
        error "Backend coverage ($COVERAGE%) is below threshold ($COVERAGE_THRESHOLD%)"
        exit 1
    fi
    
    success "Backend tests completed with $COVERAGE% coverage"
    cd ..
}

# Run frontend tests
run_frontend_tests() {
    log "Running frontend tests..."
    
    cd $FRONTEND_DIR
    
    # Install dependencies
    npm ci --silent
    
    # Run linting
    log "Running frontend linting..."
    npm run lint 2>&1 | tee ../test-results/frontend/lint.log
    
    # Run TypeScript check
    log "Running TypeScript check..."
    npm run type-check 2>&1 | tee ../test-results/frontend/typecheck.log
    
    # Run unit tests
    log "Running frontend unit tests..."
    npm run test -- --coverage --watchAll=false --coverageReporters=json-summary --coverageReporters=lcov 2>&1 | tee ../test-results/frontend/unit.log
    
    # Check coverage threshold
    if [ -f "./coverage/coverage-summary.json" ]; then
        COVERAGE=$(node -e "console.log(JSON.parse(require('fs').readFileSync('./coverage/coverage-summary.json')).total.lines.pct)")
        if (( $(echo "$COVERAGE < $COVERAGE_THRESHOLD" | bc -l) )); then
            error "Frontend coverage ($COVERAGE%) is below threshold ($COVERAGE_THRESHOLD%)"
            exit 1
        fi
        success "Frontend tests completed with $COVERAGE% coverage"
    else
        warning "Frontend coverage report not found"
    fi
    
    cd ..
}

# Run E2E tests
run_e2e_tests() {
    log "Running E2E tests..."
    
    cd $FRONTEND_DIR
    
    # Check if Android emulator is available
    if [ -n "$ANDROID_HOME" ] && command -v adb &> /dev/null; then
        log "Running Android E2E tests..."
        
        # Start emulator if not running
        if ! adb devices | grep -q "emulator"; then
            log "Starting Android emulator..."
            $ANDROID_HOME/emulator/emulator -avd Pixel_7_API_33 -no-window -no-audio &
            sleep 30
        fi
        
        # Build and test Android
        npm run detox:android:build:debug
        npm run detox:android:test:debug 2>&1 | tee ../test-results/e2e/android.log
        
        success "Android E2E tests completed"
    else
        warning "Android SDK not available - skipping Android E2E tests"
    fi
    
    # Check if iOS simulator is available (macOS only)
    if [[ "$OSTYPE" == "darwin"* ]] && command -v xcrun &> /dev/null; then
        log "Running iOS E2E tests..."
        
        # Build and test iOS
        npm run detox:ios:build:debug
        npm run detox:ios:test:debug 2>&1 | tee ../test-results/e2e/ios.log
        
        success "iOS E2E tests completed"
    else
        warning "iOS simulator not available - skipping iOS E2E tests"
    fi
    
    cd ..
}

# Run performance tests
run_performance_tests() {
    log "Running performance tests..."
    
    cd $BACKEND_DIR
    
    # Start the server in background
    npm start &
    SERVER_PID=$!
    sleep 10
    
    # Run load tests
    log "Running load tests..."
    if command -v artillery &> /dev/null; then
        artillery run ../tests/performance/load-test.yml --output ../test-results/performance/load-test.json
        artillery report ../test-results/performance/load-test.json --output ../test-results/performance/load-test.html
    else
        warning "Artillery not installed - skipping load tests"
    fi
    
    # Run API performance tests
    log "Running API performance tests..."
    npm run test:performance 2>&1 | tee ../test-results/performance/api.log
    
    # Stop the server
    kill $SERVER_PID
    
    success "Performance tests completed"
    cd ..
}

# Run security tests
run_security_tests() {
    log "Running security tests..."
    
    cd $BACKEND_DIR
    
    # Run npm audit
    log "Running npm security audit..."
    npm audit --audit-level=moderate 2>&1 | tee ../test-results/backend/security-audit.log
    
    # Run OWASP dependency check if available
    if command -v dependency-check &> /dev/null; then
        log "Running OWASP dependency check..."
        dependency-check --project "KryptoPesa Backend" --scan . --format JSON --out ../test-results/backend/owasp-report.json
    fi
    
    # Run penetration tests
    log "Running penetration tests..."
    npm start &
    SERVER_PID=$!
    sleep 10
    
    curl -X POST http://localhost:3000/security/pentest -H "Content-Type: application/json" > ../test-results/backend/pentest-results.json
    
    kill $SERVER_PID
    
    success "Security tests completed"
    cd ..
}

# Generate comprehensive report
generate_report() {
    log "Generating comprehensive test report..."
    
    # Create HTML report
    cat > test-results/index.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>KryptoPesa Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #1E3A8A; color: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 KryptoPesa Test Report</h1>
        <p>Generated on $(date)</p>
    </div>
    
    <div class="section">
        <h2>📊 Test Summary</h2>
        <div class="metric">
            <strong>Backend Coverage:</strong> 
            <span id="backend-coverage">Calculating...</span>
        </div>
        <div class="metric">
            <strong>Frontend Coverage:</strong> 
            <span id="frontend-coverage">Calculating...</span>
        </div>
        <div class="metric">
            <strong>E2E Tests:</strong> 
            <span id="e2e-status">Completed</span>
        </div>
        <div class="metric">
            <strong>Performance:</strong> 
            <span id="performance-status">Passed</span>
        </div>
    </div>
    
    <div class="section">
        <h2>📁 Detailed Reports</h2>
        <ul>
            <li><a href="backend/coverage/lcov-report/index.html">Backend Coverage Report</a></li>
            <li><a href="frontend/coverage/lcov-report/index.html">Frontend Coverage Report</a></li>
            <li><a href="performance/load-test.html">Load Test Report</a></li>
            <li><a href="backend/pentest-results.json">Security Test Results</a></li>
        </ul>
    </div>
</body>
</html>
EOF
    
    success "Test report generated at test-results/index.html"
}

# Cleanup
cleanup() {
    log "Cleaning up test environment..."
    
    # Stop test services
    if command -v docker &> /dev/null; then
        docker-compose -f docker-compose.test.yml down
    fi
    
    # Kill any remaining processes
    pkill -f "node.*test" || true
    
    success "Cleanup completed"
}

# Main execution
main() {
    log "🧪 Starting comprehensive KryptoPesa test suite..."
    
    # Trap cleanup on exit
    trap cleanup EXIT
    
    check_prerequisites
    setup_test_environment
    
    # Run test suites
    run_backend_tests
    run_frontend_tests
    run_e2e_tests
    run_performance_tests
    run_security_tests
    
    generate_report
    
    success "🎉 All tests completed successfully!"
    log "📊 View the comprehensive report at: test-results/index.html"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-only)
            BACKEND_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        --e2e-only)
            E2E_ONLY=true
            shift
            ;;
        --skip-e2e)
            SKIP_E2E=true
            shift
            ;;
        --coverage-threshold)
            COVERAGE_THRESHOLD="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --backend-only          Run only backend tests"
            echo "  --frontend-only         Run only frontend tests"
            echo "  --e2e-only             Run only E2E tests"
            echo "  --skip-e2e             Skip E2E tests"
            echo "  --coverage-threshold N  Set coverage threshold (default: 90)"
            echo "  --help                 Show this help message"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run based on options
if [[ "$BACKEND_ONLY" == "true" ]]; then
    check_prerequisites
    setup_test_environment
    run_backend_tests
elif [[ "$FRONTEND_ONLY" == "true" ]]; then
    check_prerequisites
    setup_test_environment
    run_frontend_tests
elif [[ "$E2E_ONLY" == "true" ]]; then
    check_prerequisites
    setup_test_environment
    run_e2e_tests
else
    main
fi
