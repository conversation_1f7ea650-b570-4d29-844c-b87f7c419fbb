const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../utils/logger');
const chatService = require('./chatService');

// Rate limiting for WebSocket events
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_MESSAGES_PER_WINDOW = 30;

const checkRateLimit = (userId, eventType) => {
  const key = `${userId}:${eventType}`;
  const now = Date.now();

  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  const limit = rateLimitMap.get(key);

  if (now > limit.resetTime) {
    // Reset the limit
    limit.count = 1;
    limit.resetTime = now + RATE_LIMIT_WINDOW;
    return true;
  }

  if (limit.count >= MAX_MESSAGES_PER_WINDOW) {
    return false;
  }

  limit.count++;
  return true;
};

const socketHandler = (io) => {
  // Initialize chat service with Socket.IO
  chatService.initialize(io);

  // Middleware for socket authentication
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }
      
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId);
      
      if (!user) {
        return next(new Error('User not found'));
      }
      
      socket.userId = user._id.toString();
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    logger.info(`User connected: ${socket.user.username} (${socket.userId})`);
    
    // Join user to their personal room
    socket.join(`user:${socket.userId}`);
    
    // Handle joining trade rooms
    socket.on('join_trade', (tradeId) => {
      socket.join(`trade:${tradeId}`);
      logger.info(`User ${socket.user.username} joined trade room: ${tradeId}`);
    });
    
    // Handle leaving trade rooms
    socket.on('leave_trade', (tradeId) => {
      socket.leave(`trade:${tradeId}`);
      logger.info(`User ${socket.user.username} left trade room: ${tradeId}`);
    });
    
    // Handle chat messages with rate limiting
    socket.on('send_message', (data) => {
      // Check rate limit
      if (!checkRateLimit(socket.userId, 'send_message')) {
        socket.emit('rate_limit_exceeded', {
          message: 'Too many messages. Please slow down.',
          retryAfter: 60
        });
        return;
      }

      const { tradeId, message } = data;

      // Validate input
      if (!tradeId || !message || typeof message !== 'string' || message.trim().length === 0) {
        socket.emit('error', { message: 'Invalid message data' });
        return;
      }

      // Sanitize message (basic)
      const sanitizedMessage = message.trim().substring(0, 1000); // Limit message length

      // Broadcast to trade room
      socket.to(`trade:${tradeId}`).emit('new_message', {
        tradeId,
        message: sanitizedMessage,
        sender: {
          id: socket.userId,
          username: socket.user.username
        },
        timestamp: new Date()
      });
    });
    
    // Handle typing indicators
    socket.on('typing_start', (tradeId) => {
      socket.to(`trade:${tradeId}`).emit('user_typing', {
        userId: socket.userId,
        username: socket.user.username
      });
    });
    
    socket.on('typing_stop', (tradeId) => {
      socket.to(`trade:${tradeId}`).emit('user_stopped_typing', {
        userId: socket.userId
      });
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`User disconnected: ${socket.user.username} (${socket.userId})`);
    });
  });
};

module.exports = socketHandler;
