import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import {
  tradingService,
  userService,
  walletService,
  EAST_AFRICAN_CONFIG,
  CreateOfferData,
  EastAfricanCurrency,
  CryptoCurrency,
  PaymentMethod,
  EastAfricanCountry,
  eastAfricanHelpers
} from '../../services';

// East African focused options
const CRYPTO_OPTIONS = Object.keys(EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES) as CryptoCurrency[];
const FIAT_OPTIONS = Object.keys(EAST_AFRICAN_CONFIG.CURRENCIES) as EastAfricanCurrency[];
const COUNTRY_OPTIONS = Object.keys(EAST_AFRICAN_CONFIG.COUNTRIES) as EastAfricanCountry[];
const PAYMENT_METHOD_OPTIONS = Object.keys(EAST_AFRICAN_CONFIG.PAYMENT_METHODS) as PaymentMethod[];

export default function CreateOfferScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();

  // State management
  const [userProfile, setUserProfile] = useState(null);
  const [walletBalances, setWalletBalances] = useState([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state with East African defaults
  const [offerType, setOfferType] = useState<'buy' | 'sell'>('sell');
  const [cryptoCurrency, setCryptoCurrency] = useState<CryptoCurrency>('USDT');
  const [fiatCurrency, setFiatCurrency] = useState<EastAfricanCurrency>('KES');
  const [country, setCountry] = useState<EastAfricanCountry>('KE');
  const [city, setCity] = useState('');
  const [priceType, setPriceType] = useState<'fixed' | 'market' | 'margin'>('market');
  const [fixedPrice, setFixedPrice] = useState('');
  const [marginPercentage, setMarginPercentage] = useState('0');
  const [minAmount, setMinAmount] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  const [availableAmount, setAvailableAmount] = useState('');
  const [selectedPaymentMethods, setSelectedPaymentMethods] = useState<PaymentMethod[]>([]);
  const [terms, setTerms] = useState('');
  const [timeLimit, setTimeLimit] = useState('30'); // minutes
  const [minimumReputation, setMinimumReputation] = useState('0');

  // Modal states
  const [cryptoModalVisible, setCryptoModalVisible] = useState(false);
  const [fiatModalVisible, setFiatModalVisible] = useState(false);
  const [countryModalVisible, setCountryModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);

  // Load user data and wallet balances
  const loadUserData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [profile, balances] = await Promise.all([
        userService.getProfile(),
        walletService.getWalletBalances(),
      ]);

      setUserProfile(profile);
      setWalletBalances(balances);

      // Set defaults from user profile
      setFiatCurrency(profile.preferences.currency);
      setCountry(profile.profile.location.country);
      setCity(profile.profile.location.city || '');

      // Set available payment methods for user's country
      const availableMethods = eastAfricanHelpers.getPaymentMethodsByCountry(profile.profile.location.country);
      if (availableMethods.length > 0) {
        setSelectedPaymentMethods([availableMethods[0]]);
      }
    } catch (err) {
      console.error('Failed to load user data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load user data');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  const handlePaymentMethodToggle = (method: PaymentMethod) => {
    setSelectedPaymentMethods(prev =>
      prev.includes(method)
        ? prev.filter(m => m !== method)
        : [...prev, method]
    );
  };

  // Get available wallet balance for selected crypto
  const getAvailableBalance = () => {
    const wallet = walletBalances.find(w => w.currency === cryptoCurrency);
    return wallet ? wallet.balance : 0;
  };

  // Validate payment method selection (no API integration needed)
  const validatePaymentMethods = () => {
    for (const method of selectedPaymentMethods) {
      if (method === 'mpesa' || method === 'airtel_money') {
        Alert.alert('Payment Method Notice', `Ensure you have a ${eastAfricanHelpers.getPaymentMethodName(method)} account. KryptoPesa does not process payments - you'll handle transactions externally.`);
      }
    }
    return true;
  };

  const validateForm = () => {
    // Basic validation
    if (priceType === 'fixed' && (!fixedPrice || parseFloat(fixedPrice) <= 0)) {
      Alert.alert('Invalid Price', 'Please enter a valid fixed price');
      return false;
    }

    if (priceType === 'margin' && (!marginPercentage || Math.abs(parseFloat(marginPercentage)) > 50)) {
      Alert.alert('Invalid Margin', 'Please enter a margin between -50% and +50%');
      return false;
    }

    if (!minAmount || parseFloat(minAmount) <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid minimum amount');
      return false;
    }

    if (!maxAmount || parseFloat(maxAmount) <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid maximum amount');
      return false;
    }

    if (!availableAmount || parseFloat(availableAmount) <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid available amount');
      return false;
    }

    if (parseFloat(minAmount) >= parseFloat(maxAmount)) {
      Alert.alert('Invalid Range', 'Maximum amount must be greater than minimum amount');
      return false;
    }

    if (parseFloat(availableAmount) < parseFloat(maxAmount)) {
      Alert.alert('Insufficient Balance', 'Available amount cannot be less than maximum amount');
      return false;
    }

    // Check wallet balance for sell offers
    if (offerType === 'sell') {
      const balance = getAvailableBalance();
      if (balance < parseFloat(availableAmount)) {
        Alert.alert('Insufficient Balance', `You only have ${balance} ${cryptoCurrency} available`);
        return false;
      }
    }

    if (selectedPaymentMethods.length === 0) {
      Alert.alert('Payment Methods Required', 'Please select at least one payment method');
      return false;
    }

    if (!validatePaymentMethods()) {
      return false;
    }

    return true;
  };

  const handleCreateOffer = async () => {
    if (!validateForm()) return;

    try {
      setCreating(true);

      const offerData: CreateOfferData = {
        type: offerType,
        cryptocurrency: {
          type: cryptoCurrency,
          minAmount: parseFloat(minAmount),
          maxAmount: parseFloat(maxAmount),
          availableAmount: parseFloat(availableAmount),
        },
        fiat: {
          currency: fiatCurrency,
          priceType,
          ...(priceType === 'fixed' && { fixedPrice: parseFloat(fixedPrice) }),
          ...(priceType === 'margin' && { marginPercentage: parseFloat(marginPercentage) }),
        },
        paymentMethods: selectedPaymentMethods,
        location: {
          country,
          ...(city && { city }),
        },
        terms: {
          timeLimit: parseInt(timeLimit),
          ...(terms && { instructions: terms }),
          ...(minimumReputation && { minimumReputation: parseFloat(minimumReputation) }),
        },
      };

      const newOffer = await tradingService.createOffer(offerData);

      Alert.alert(
        'Offer Created Successfully!',
        `Your ${offerType} offer for ${cryptoCurrency} has been created and is now live on KryptoPesa.`,
        [
          {
            text: 'View Offer',
            onPress: () => {
              navigation.goBack();
              navigation.navigate('OfferDetail', { offerId: newOffer._id });
            },
          },
          {
            text: 'Create Another',
            onPress: () => {
              // Reset form
              setMinAmount('');
              setMaxAmount('');
              setAvailableAmount('');
              setFixedPrice('');
              setMarginPercentage('0');
              setTerms('');
            },
          },
          {
            text: 'Done',
            onPress: () => navigation.goBack(),
            style: 'cancel',
          },
        ]
      );
    } catch (err) {
      console.error('Failed to create offer:', err);
      Alert.alert(
        'Failed to Create Offer',
        err instanceof Error ? err.message : 'Please try again later.'
      );
    } finally {
      setCreating(false);
    }
  };

  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Offer</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading your profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Offer</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load profile</Text>
          <Text style={styles.errorSubText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadUserData}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create KryptoPesa Offer</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Offer Type */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Offer Type</Text>
          <View style={styles.offerTypeContainer}>
            <TouchableOpacity
              style={[
                styles.offerTypeButton,
                offerType === 'buy' && styles.offerTypeButtonActive
              ]}
              onPress={() => setOfferType('buy')}
            >
              <Text style={[
                styles.offerTypeText,
                offerType === 'buy' && styles.offerTypeTextActive
              ]}>
                Buy {cryptoCurrency}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.offerTypeButton,
                offerType === 'sell' && styles.offerTypeButtonActive
              ]}
              onPress={() => setOfferType('sell')}
            >
              <Text style={[
                styles.offerTypeText,
                offerType === 'sell' && styles.offerTypeTextActive
              ]}>
                Sell {cryptoCurrency}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Currency Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Currency Pair</Text>
          
          <View style={styles.currencyRow}>
            <View style={styles.currencyItem}>
              <Text style={styles.inputLabel}>Cryptocurrency</Text>
              <TouchableOpacity
                style={styles.dropdownButton}
                onPress={() => setCryptoModalVisible(true)}
              >
                <View style={styles.currencyDisplay}>
                  <View style={styles.cryptoIcon}>
                    <Text style={styles.cryptoIconText}>
                      {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[cryptoCurrency]?.symbol || cryptoCurrency}
                    </Text>
                  </View>
                  <Text style={styles.dropdownText}>
                    {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[cryptoCurrency]?.name || cryptoCurrency}
                  </Text>
                </View>
                <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>
              {offerType === 'sell' && (
                <Text style={styles.balanceText}>
                  Available: {getAvailableBalance().toFixed(6)} {cryptoCurrency}
                </Text>
              )}
            </View>

            <View style={styles.currencyItem}>
              <Text style={styles.inputLabel}>East African Currency</Text>
              <TouchableOpacity
                style={styles.dropdownButton}
                onPress={() => setFiatModalVisible(true)}
              >
                <View style={styles.currencyDisplay}>
                  <Text style={styles.flagText}>
                    {EAST_AFRICAN_CONFIG.COUNTRIES[country]?.flag || '🌍'}
                  </Text>
                  <Text style={styles.dropdownText}>
                    {EAST_AFRICAN_CONFIG.CURRENCIES[fiatCurrency]?.symbol} {fiatCurrency}
                  </Text>
                </View>
                <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Location */}
          <View style={styles.locationContainer}>
            <Text style={styles.inputLabel}>Location</Text>
            <View style={styles.locationRow}>
              <TouchableOpacity
                style={[styles.dropdownButton, styles.countryButton]}
                onPress={() => setCountryModalVisible(true)}
              >
                <View style={styles.currencyDisplay}>
                  <Text style={styles.flagText}>
                    {EAST_AFRICAN_CONFIG.COUNTRIES[country]?.flag}
                  </Text>
                  <Text style={styles.dropdownText}>
                    {EAST_AFRICAN_CONFIG.COUNTRIES[country]?.name}
                  </Text>
                </View>
                <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>

              <TextInput
                style={[styles.textInput, styles.cityInput]}
                placeholder="City (optional)"
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={city}
                onChangeText={setCity}
              />
            </View>
          </View>
          </View>
        </View>
        
        {/* Price Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Price Settings</Text>
          
          <View style={styles.priceTypeContainer}>
            <TouchableOpacity
              style={[
                styles.priceTypeButton,
                priceType === 'market' && styles.priceTypeButtonActive
              ]}
              onPress={() => setPriceType('market')}
            >
              <Text style={[
                styles.priceTypeText,
                priceType === 'market' && styles.priceTypeTextActive
              ]}>
                Market Price
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.priceTypeButton,
                priceType === 'fixed' && styles.priceTypeButtonActive
              ]}
              onPress={() => setPriceType('fixed')}
            >
              <Text style={[
                styles.priceTypeText,
                priceType === 'fixed' && styles.priceTypeTextActive
              ]}>
                Fixed Price
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.priceTypeButton,
                priceType === 'margin' && styles.priceTypeButtonActive
              ]}
              onPress={() => setPriceType('margin')}
            >
              <Text style={[
                styles.priceTypeText,
                priceType === 'margin' && styles.priceTypeTextActive
              ]}>
                Margin
              </Text>
            </TouchableOpacity>
          </View>
          
          {priceType === 'market' ? (
            <View style={styles.marketPriceContainer}>
              <View style={styles.marketPriceInfo}>
                <Ionicons name="trending-up" size={20} color={theme.colors.success} />
                <Text style={styles.marketPriceText}>
                  Price will follow current market rates automatically
                </Text>
              </View>
              <Text style={styles.marketPriceHint}>
                Your offer will always show competitive market pricing
              </Text>
            </View>
          ) : priceType === 'fixed' ? (
            <View>
              <Text style={styles.inputLabel}>Price per {cryptoCurrency}</Text>
              <View style={styles.priceInputContainer}>
                <TextInput
                  style={styles.priceInput}
                  placeholder="0.00"
                  placeholderTextColor={theme.colors.inputPlaceholder}
                  value={fixedPrice}
                  onChangeText={setFixedPrice}
                  keyboardType="numeric"
                />
                <Text style={styles.priceCurrency}>
                  {EAST_AFRICAN_CONFIG.CURRENCIES[fiatCurrency]?.symbol} {fiatCurrency}
                </Text>
              </View>
            </View>
          ) : (
            <View>
              <Text style={styles.inputLabel}>Margin from Market Price</Text>
              <View style={styles.priceInputContainer}>
                <TextInput
                  style={styles.priceInput}
                  placeholder="0.0"
                  placeholderTextColor={theme.colors.inputPlaceholder}
                  value={marginPercentage}
                  onChangeText={setMarginPercentage}
                  keyboardType="numeric"
                />
                <Text style={styles.priceCurrency}>%</Text>
              </View>
              <Text style={styles.marginHint}>
                Positive for above market (e.g., +2%), negative for below market (e.g., -1%)
              </Text>
            </View>
          )}
        </View>
        
        {/* Amount Limits */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Amount Limits</Text>
          
          <View style={styles.amountContainer}>
            <View style={styles.amountRow}>
              <View style={styles.amountItem}>
                <Text style={styles.inputLabel}>
                  Minimum ({EAST_AFRICAN_CONFIG.CURRENCIES[fiatCurrency]?.symbol} {fiatCurrency})
                </Text>
                <TextInput
                  style={styles.input}
                  placeholder="1000"
                  placeholderTextColor={theme.colors.inputPlaceholder}
                  value={minAmount}
                  onChangeText={setMinAmount}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.amountItem}>
                <Text style={styles.inputLabel}>
                  Maximum ({EAST_AFRICAN_CONFIG.CURRENCIES[fiatCurrency]?.symbol} {fiatCurrency})
                </Text>
                <TextInput
                  style={styles.input}
                  placeholder="100000"
                  placeholderTextColor={theme.colors.inputPlaceholder}
                  value={maxAmount}
                  onChangeText={setMaxAmount}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.availableAmountContainer}>
              <Text style={styles.inputLabel}>
                Available Amount ({cryptoCurrency})
              </Text>
              <TextInput
                style={styles.input}
                placeholder="100"
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={availableAmount}
                onChangeText={setAvailableAmount}
                keyboardType="numeric"
              />
              <Text style={styles.availableHint}>
                Amount you're willing to {offerType === 'buy' ? 'buy' : 'sell'} in this offer
              </Text>
            </View>
          </View>
        </View>
        
        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Accepted Payment Methods</Text>
          <Text style={styles.paymentMethodDescription}>
            Select payment methods you accept. Payments are handled externally - KryptoPesa only holds crypto in escrow.
          </Text>
          <TouchableOpacity
            style={styles.paymentMethodsButton}
            onPress={() => setPaymentModalVisible(true)}
          >
            <Text style={styles.paymentMethodsText}>
              {selectedPaymentMethods.length > 0
                ? `${selectedPaymentMethods.length} method(s) selected`
                : 'Select payment methods'
              }
            </Text>
            <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          
          {selectedPaymentMethods.length > 0 && (
            <View style={styles.selectedPaymentMethods}>
              {selectedPaymentMethods.map((method, index) => (
                <View key={index} style={styles.paymentMethodTag}>
                  <Text style={styles.paymentMethodIcon}>
                    {EAST_AFRICAN_CONFIG.PAYMENT_METHODS[method]?.icon}
                  </Text>
                  <Text style={styles.paymentMethodTagText}>
                    {eastAfricanHelpers.getPaymentMethodName(method)}
                  </Text>
                </View>
              ))}
            </View>
          )}

          <Text style={styles.paymentMethodHint}>
            Available in {EAST_AFRICAN_CONFIG.COUNTRIES[country]?.name}: {' '}
            {eastAfricanHelpers.getPaymentMethodsByCountry(country)
              .map(method => eastAfricanHelpers.getPaymentMethodName(method))
              .join(', ')}
          </Text>
        </View>
        
        {/* Trading Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trading Terms</Text>
          <TextInput
            style={styles.termsInput}
            placeholder="Enter your trading terms and conditions..."
            placeholderTextColor={theme.colors.inputPlaceholder}
            value={terms}
            onChangeText={setTerms}
            multiline
            numberOfLines={4}
          />
        </View>
        
        {/* Additional Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trading Requirements</Text>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Payment Time Limit</Text>
              <Text style={styles.settingDescription}>
                Time limit for payment completion
              </Text>
            </View>
            <View style={styles.timeLimitContainer}>
              <TextInput
                style={styles.timeLimitInput}
                value={timeLimit}
                onChangeText={setTimeLimit}
                keyboardType="numeric"
                placeholderTextColor={theme.colors.inputPlaceholder}
              />
              <Text style={styles.timeLimitUnit}>min</Text>
            </View>
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Minimum Reputation</Text>
              <Text style={styles.settingDescription}>
                Minimum trader reputation score required
              </Text>
            </View>
            <View style={styles.timeLimitContainer}>
              <TextInput
                style={styles.timeLimitInput}
                value={minimumReputation}
                onChangeText={setMinimumReputation}
                keyboardType="numeric"
                placeholder="0"
                placeholderTextColor={theme.colors.inputPlaceholder}
              />
              <Text style={styles.timeLimitUnit}>%</Text>
            </View>
          </View>
        </View>
        
        {/* Create Button */}
        <TouchableOpacity
          style={[styles.createButton, creating && styles.createButtonDisabled]}
          onPress={handleCreateOffer}
          disabled={creating}
        >
          {creating ? (
            <View style={styles.createButtonContent}>
              <ActivityIndicator size="small" color="#FFFFFF" />
              <Text style={styles.createButtonText}>Creating Offer...</Text>
            </View>
          ) : (
            <Text style={styles.createButtonText}>Create KryptoPesa Offer</Text>
          )}
        </TouchableOpacity>
      </ScrollView>

      {/* Cryptocurrency Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={cryptoModalVisible}
        onRequestClose={() => setCryptoModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Cryptocurrency</Text>
              <TouchableOpacity onPress={() => setCryptoModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {CRYPTO_OPTIONS.map((crypto, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    cryptoCurrency === crypto && styles.modalOptionSelected
                  ]}
                  onPress={() => {
                    setCryptoCurrency(crypto);
                    setCryptoModalVisible(false);
                  }}
                >
                  <View style={styles.modalOptionContent}>
                    <View style={styles.cryptoIcon}>
                      <Text style={styles.cryptoIconText}>
                        {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[crypto]?.symbol || crypto}
                      </Text>
                    </View>
                    <View style={styles.modalOptionInfo}>
                      <Text style={[
                        styles.modalOptionText,
                        cryptoCurrency === crypto && styles.modalOptionTextSelected
                      ]}>
                        {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[crypto]?.name || crypto}
                      </Text>
                      <Text style={styles.modalOptionSubText}>
                        {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[crypto]?.network || 'Network'}
                      </Text>
                    </View>
                  </View>
                  {cryptoCurrency === crypto && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Fiat Currency Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={fiatModalVisible}
        onRequestClose={() => setFiatModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select East African Currency</Text>
              <TouchableOpacity onPress={() => setFiatModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {FIAT_OPTIONS.map((currency, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    fiatCurrency === currency && styles.modalOptionSelected
                  ]}
                  onPress={() => {
                    setFiatCurrency(currency);
                    setFiatModalVisible(false);
                  }}
                >
                  <View style={styles.modalOptionContent}>
                    <Text style={styles.flagText}>
                      {EAST_AFRICAN_CONFIG.COUNTRIES[EAST_AFRICAN_CONFIG.CURRENCIES[currency]?.country || 'KE']?.flag || '🌍'}
                    </Text>
                    <View style={styles.modalOptionInfo}>
                      <Text style={[
                        styles.modalOptionText,
                        fiatCurrency === currency && styles.modalOptionTextSelected
                      ]}>
                        {EAST_AFRICAN_CONFIG.CURRENCIES[currency]?.name || currency}
                      </Text>
                      <Text style={styles.modalOptionSubText}>
                        {EAST_AFRICAN_CONFIG.CURRENCIES[currency]?.symbol} {currency}
                      </Text>
                    </View>
                  </View>
                  {fiatCurrency === currency && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Payment Methods Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={paymentModalVisible}
        onRequestClose={() => setPaymentModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>External Payment Methods</Text>
              <TouchableOpacity onPress={() => setPaymentModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalNotice}>
              <Ionicons name="information-circle" size={20} color={theme.colors.info} />
              <Text style={styles.modalNoticeText}>
                Select payment methods you accept for external transactions. KryptoPesa only holds crypto in escrow.
              </Text>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {eastAfricanHelpers.getPaymentMethodsByCountry(country).map((method, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    selectedPaymentMethods.includes(method) && styles.modalOptionSelected
                  ]}
                  onPress={() => handlePaymentMethodToggle(method)}
                >
                  <View style={styles.modalOptionContent}>
                    <Text style={styles.paymentMethodIcon}>
                      {EAST_AFRICAN_CONFIG.PAYMENT_METHODS[method]?.icon}
                    </Text>
                    <View style={styles.modalOptionInfo}>
                      <Text style={[
                        styles.modalOptionText,
                        selectedPaymentMethods.includes(method) && styles.modalOptionTextSelected
                      ]}>
                        {eastAfricanHelpers.getPaymentMethodName(method)}
                      </Text>
                      <Text style={styles.modalOptionSubText}>
                        Available in {EAST_AFRICAN_CONFIG.COUNTRIES[country]?.name}
                      </Text>
                    </View>
                  </View>
                  {selectedPaymentMethods.includes(method) && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalDoneButton}
                onPress={() => setPaymentModalVisible(false)}
              >
                <Text style={styles.modalDoneButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Country Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={countryModalVisible}
        onRequestClose={() => setCountryModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Country</Text>
              <TouchableOpacity onPress={() => setCountryModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {COUNTRY_OPTIONS.map((countryCode, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    country === countryCode && styles.modalOptionSelected
                  ]}
                  onPress={() => {
                    setCountry(countryCode);
                    // Update fiat currency to match country's default
                    const countryCurrency = Object.keys(EAST_AFRICAN_CONFIG.CURRENCIES).find(
                      curr => EAST_AFRICAN_CONFIG.CURRENCIES[curr as EastAfricanCurrency]?.country === countryCode
                    ) as EastAfricanCurrency;
                    if (countryCurrency) {
                      setFiatCurrency(countryCurrency);
                    }
                    // Reset payment methods for new country
                    const availableMethods = eastAfricanHelpers.getPaymentMethodsByCountry(countryCode);
                    setSelectedPaymentMethods(availableMethods.slice(0, 1));
                    setCountryModalVisible(false);
                  }}
                >
                  <View style={styles.modalOptionContent}>
                    <Text style={styles.flagText}>
                      {EAST_AFRICAN_CONFIG.COUNTRIES[countryCode]?.flag}
                    </Text>
                    <View style={styles.modalOptionInfo}>
                      <Text style={[
                        styles.modalOptionText,
                        country === countryCode && styles.modalOptionTextSelected
                      ]}>
                        {EAST_AFRICAN_CONFIG.COUNTRIES[countryCode]?.name}
                      </Text>
                    </View>
                  </View>
                  {country === countryCode && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  offerTypeContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  offerTypeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  offerTypeButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  offerTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  offerTypeTextActive: {
    color: 'white',
  },
  currencyRow: {
    flexDirection: 'row',
    gap: 12,
  },
  currencyItem: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  dropdownText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  priceTypeContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  priceTypeButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  priceTypeButtonActive: {
    backgroundColor: theme.colors.primaryLight + '40',
    borderColor: theme.colors.primary,
  },
  priceTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  priceTypeTextActive: {
    color: theme.colors.primary,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    paddingHorizontal: 16,
  },
  priceInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.inputText,
  },
  priceCurrency: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  marginHint: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  amountRow: {
    flexDirection: 'row',
    gap: 12,
  },
  amountItem: {
    flex: 1,
  },
  input: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    fontSize: 16,
    color: theme.colors.inputText,
  },
  paymentMethodsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  paymentMethodsText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  selectedPaymentMethods: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  paymentMethodTag: {
    backgroundColor: theme.colors.primaryLight + '40',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.primaryLight,
  },
  paymentMethodTagText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  termsInput: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    fontSize: 16,
    color: theme.colors.inputText,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  timeLimitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeLimitInput: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    fontSize: 16,
    color: theme.colors.inputText,
    width: 60,
    textAlign: 'center',
  },
  timeLimitUnit: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 32,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalScrollView: {
    maxHeight: 400,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalOptionSelected: {
    backgroundColor: theme.colors.primaryLight + '40',
  },
  modalOptionText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  modalOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  modalFooter: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  modalDoneButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalDoneButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  // New styles for East African features
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 12,
    textAlign: 'center',
  },
  errorSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  currencyDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cryptoIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  cryptoIconText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  flagText: {
    fontSize: 20,
    marginRight: 8,
  },
  balanceText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  locationContainer: {
    marginTop: 16,
  },
  locationRow: {
    flexDirection: 'row',
    gap: 12,
  },
  countryButton: {
    flex: 1,
  },
  cityInput: {
    flex: 1,
  },
  marketPriceContainer: {
    backgroundColor: theme.colors.success + '20',
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  marketPriceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  marketPriceText: {
    fontSize: 16,
    color: theme.colors.success,
    marginLeft: 8,
    fontWeight: '500',
  },
  marketPriceHint: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  amountContainer: {
    gap: 16,
  },
  availableAmountContainer: {
    marginTop: 8,
  },
  availableHint: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  paymentMethodIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  paymentMethodDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 12,
    lineHeight: 16,
    fontStyle: 'italic',
  },
  paymentMethodHint: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 8,
    fontStyle: 'italic',
  },
  createButtonDisabled: {
    opacity: 0.6,
  },
  createButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  modalOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  modalOptionInfo: {
    flex: 1,
  },
  modalOptionSubText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  modalNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.info + '20',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  modalNoticeText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
});
