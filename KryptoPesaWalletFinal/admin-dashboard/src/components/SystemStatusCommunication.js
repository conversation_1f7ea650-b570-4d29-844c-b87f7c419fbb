/**
 * System Status Communication Component
 * Manages graceful degradation communication and system status broadcasting
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Grid,
  Box,
  Chip,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Divider,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Notifications,
  Warning,
  Error,
  CheckCircle,
  Settings,
  Send,
  History,
  ExpandMore,
  Refresh,
  NotificationImportant,
  Campaign,
  Timeline
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { format } from 'date-fns';
import websocketService from '../services/websocketService';

const SystemStatusCommunication = () => {
  const dispatch = useDispatch();
  const [systemStatus, setSystemStatus] = useState(null);
  const [activeAlerts, setActiveAlerts] = useState([]);
  const [degradationHistory, setDegradationHistory] = useState([]);
  const [communicationConfig, setCommunicationConfig] = useState({});
  const [statistics, setStatistics] = useState({});
  
  // Dialog states
  const [broadcastDialog, setBroadcastDialog] = useState(false);
  const [configDialog, setConfigDialog] = useState(false);
  const [historyDialog, setHistoryDialog] = useState(false);
  
  // Form states
  const [broadcastForm, setBroadcastForm] = useState({
    message: '',
    type: 'info',
    priority: 'normal',
    targetUsers: []
  });
  
  const [configForm, setConfigForm] = useState({});
  
  // Loading states
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch system status data
  const fetchSystemStatus = async () => {
    try {
      setRefreshing(true);
      
      const [statusResponse, alertsResponse, historyResponse, configResponse, statsResponse] = await Promise.all([
        fetch('/api/system/status/detailed', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/system/alerts', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/system/status/history?limit=20', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/system/communication/config', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/system/statistics', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        })
      ]);

      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        setSystemStatus(statusData.data);
      }

      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setActiveAlerts(alertsData.data.alerts);
      }

      if (historyResponse.ok) {
        const historyData = await historyResponse.json();
        setDegradationHistory(historyData.data.history);
      }

      if (configResponse.ok) {
        const configData = await configResponse.json();
        setCommunicationConfig(configData.data);
        setConfigForm(configData.data);
      }

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStatistics(statsData.data);
      }

    } catch (error) {
      console.error('Failed to fetch system status:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle alert acknowledgment
  const acknowledgeAlert = async (alertId) => {
    try {
      const response = await fetch(`/api/system/alerts/${alertId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ notes: 'Acknowledged via admin dashboard' })
      });

      if (response.ok) {
        fetchSystemStatus(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to acknowledge alert:', error);
    }
  };

  // Handle broadcast message
  const handleBroadcast = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/system/broadcast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(broadcastForm)
      });

      if (response.ok) {
        setBroadcastDialog(false);
        setBroadcastForm({ message: '', type: 'info', priority: 'normal', targetUsers: [] });
        // Show success message
      }
    } catch (error) {
      console.error('Failed to broadcast message:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle configuration update
  const handleConfigUpdate = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/system/communication/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(configForm)
      });

      if (response.ok) {
        setConfigDialog(false);
        fetchSystemStatus(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to update configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get status color
  const getStatusColor = (level) => {
    switch (level) {
      case 'none': return 'success';
      case 'partial': return 'warning';
      case 'severe': return 'error';
      default: return 'default';
    }
  };

  // Get status icon
  const getStatusIcon = (level) => {
    switch (level) {
      case 'none': return <CheckCircle />;
      case 'partial': return <Warning />;
      case 'severe': return <Error />;
      default: return <CheckCircle />;
    }
  };

  useEffect(() => {
    fetchSystemStatus();
    
    // Set up real-time updates
    const handleSystemStatusUpdate = (data) => {
      setSystemStatus(prev => ({ ...prev, ...data }));
    };

    websocketService.on('system_status_update', handleSystemStatusUpdate);
    
    // Refresh every 30 seconds
    const interval = setInterval(fetchSystemStatus, 30000);

    return () => {
      websocketService.off('system_status_update', handleSystemStatusUpdate);
      clearInterval(interval);
    };
  }, []);

  if (!systemStatus) {
    return (
      <Card>
        <CardContent>
          <Typography>Loading system status...</Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Grid container spacing={3}>
      {/* Current System Status */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardHeader
            title="System Status"
            action={
              <Tooltip title="Refresh">
                <IconButton onClick={fetchSystemStatus} disabled={refreshing}>
                  <Refresh />
                </IconButton>
              </Tooltip>
            }
          />
          <CardContent>
            <Box display="flex" alignItems="center" mb={2}>
              {getStatusIcon(systemStatus.degradationLevel)}
              <Box ml={1}>
                <Chip
                  label={systemStatus.degradationLevel.toUpperCase()}
                  color={getStatusColor(systemStatus.degradationLevel)}
                  variant="filled"
                />
              </Box>
            </Box>
            
            <Typography variant="body2" color="textSecondary" gutterBottom>
              {systemStatus.statusMessage}
            </Typography>
            
            <Typography variant="caption" display="block">
              Last updated: {format(new Date(systemStatus.lastHealthCheck), 'PPpp')}
            </Typography>

            {/* System Statistics */}
            {statistics && (
              <Box mt={2}>
                <Typography variant="subtitle2" gutterBottom>Statistics (24h)</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      Uptime: {statistics.uptimePercentage?.toFixed(2)}%
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      Degradations: {statistics.degradationFrequency}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Active Alerts */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardHeader
            title={
              <Badge badgeContent={activeAlerts.length} color="error">
                Active Alerts
              </Badge>
            }
          />
          <CardContent>
            {activeAlerts.length === 0 ? (
              <Typography color="textSecondary">No active alerts</Typography>
            ) : (
              <List dense>
                {activeAlerts.map((alert) => (
                  <ListItem key={alert.id}>
                    <ListItemIcon>
                      <NotificationImportant color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary={alert.message}
                      secondary={format(new Date(alert.timestamp), 'PPpp')}
                    />
                    <ListItemSecondaryAction>
                      <Button
                        size="small"
                        onClick={() => acknowledgeAlert(alert.id)}
                        disabled={alert.acknowledged}
                      >
                        {alert.acknowledged ? 'Acknowledged' : 'Acknowledge'}
                      </Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Communication Controls */}
      <Grid item xs={12}>
        <Card>
          <CardHeader title="Communication Controls" />
          <CardContent>
            <Grid container spacing={2}>
              <Grid item>
                <Button
                  variant="contained"
                  startIcon={<Campaign />}
                  onClick={() => setBroadcastDialog(true)}
                >
                  Broadcast Message
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<Settings />}
                  onClick={() => setConfigDialog(true)}
                >
                  Configure
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<History />}
                  onClick={() => setHistoryDialog(true)}
                >
                  View History
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Broadcast Dialog */}
      <Dialog open={broadcastDialog} onClose={() => setBroadcastDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Broadcast System Message</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Message"
            value={broadcastForm.message}
            onChange={(e) => setBroadcastForm({ ...broadcastForm, message: e.target.value })}
            margin="normal"
          />
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={broadcastForm.type}
                  onChange={(e) => setBroadcastForm({ ...broadcastForm, type: e.target.value })}
                >
                  <MenuItem value="info">Info</MenuItem>
                  <MenuItem value="warning">Warning</MenuItem>
                  <MenuItem value="maintenance">Maintenance</MenuItem>
                  <MenuItem value="emergency">Emergency</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={broadcastForm.priority}
                  onChange={(e) => setBroadcastForm({ ...broadcastForm, priority: e.target.value })}
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="normal">Normal</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="urgent">Urgent</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBroadcastDialog(false)}>Cancel</Button>
          <Button onClick={handleBroadcast} variant="contained" disabled={loading || !broadcastForm.message}>
            Broadcast
          </Button>
        </DialogActions>
      </Dialog>

      {/* Configuration Dialog */}
      <Dialog open={configDialog} onClose={() => setConfigDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Communication Configuration</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={configForm.broadcastStatusChanges || false}
                  onChange={(e) => setConfigForm({ ...configForm, broadcastStatusChanges: e.target.checked })}
                />
              }
              label="Broadcast Status Changes"
            />
          </Box>

          <Box sx={{ mt: 1 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={configForm.notifyAdmins || false}
                  onChange={(e) => setConfigForm({ ...configForm, notifyAdmins: e.target.checked })}
                />
              }
              label="Notify Admins"
            />
          </Box>

          <Box sx={{ mt: 1 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={configForm.notifyUsers || false}
                  onChange={(e) => setConfigForm({ ...configForm, notifyUsers: e.target.checked })}
                />
              }
              label="Notify Users"
            />
          </Box>

          <Typography variant="subtitle2" sx={{ mt: 3, mb: 1 }}>
            Alert Thresholds (milliseconds)
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Partial Degradation"
                type="number"
                value={configForm.alertThresholds?.partial || 30000}
                onChange={(e) => setConfigForm({
                  ...configForm,
                  alertThresholds: {
                    ...configForm.alertThresholds,
                    partial: parseInt(e.target.value)
                  }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Severe Degradation"
                type="number"
                value={configForm.alertThresholds?.severe || 10000}
                onChange={(e) => setConfigForm({
                  ...configForm,
                  alertThresholds: {
                    ...configForm.alertThresholds,
                    severe: parseInt(e.target.value)
                  }
                })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfigDialog(false)}>Cancel</Button>
          <Button onClick={handleConfigUpdate} variant="contained" disabled={loading}>
            Save Configuration
          </Button>
        </DialogActions>
      </Dialog>

      {/* History Dialog */}
      <Dialog open={historyDialog} onClose={() => setHistoryDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Degradation History</DialogTitle>
        <DialogContent>
          {degradationHistory.length === 0 ? (
            <Typography color="textSecondary">No degradation events recorded</Typography>
          ) : (
            <List>
              {degradationHistory.map((event, index) => (
                <React.Fragment key={index}>
                  <ListItem>
                    <ListItemIcon>
                      <Timeline />
                    </ListItemIcon>
                    <ListItemText
                      primary={`${event.previousLevel.toUpperCase()} → ${event.newLevel.toUpperCase()}`}
                      secondary={
                        <Box>
                          <Typography variant="body2">
                            {format(new Date(event.timestamp), 'PPpp')}
                          </Typography>
                          {event.affectedFeatures && (
                            <Typography variant="caption" display="block">
                              Affected: {[
                                ...event.affectedFeatures.disabled,
                                ...event.affectedFeatures.limited
                              ].join(', ') || 'None'}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                    <Chip
                      size="small"
                      label={event.newLevel}
                      color={getStatusColor(event.newLevel)}
                    />
                  </ListItem>
                  {index < degradationHistory.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setHistoryDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
};

export default SystemStatusCommunication;
