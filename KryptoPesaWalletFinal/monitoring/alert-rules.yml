# KryptoPesa Production Alert Rules
# Comprehensive alerting for enterprise-grade monitoring

groups:
  # Critical System Alerts
  - name: kryptopesa.critical
    interval: 30s
    rules:
      # API Availability
      - alert: APIDown
        expr: up{job="kryptopesa-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: api
        annotations:
          summary: "KryptoPesa API is down"
          description: "The KryptoPesa backend API has been down for more than 1 minute"
          runbook_url: "https://docs.kryptopesa.com/runbooks/api-down"

      # Database Connectivity
      - alert: DatabaseDown
        expr: up{job="mongodb"} == 0
        for: 2m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "MongoDB database is unreachable"
          description: "MongoDB has been unreachable for more than 2 minutes"
          runbook_url: "https://docs.kryptopesa.com/runbooks/database-down"

      # High Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 3m
        labels:
          severity: critical
          service: api
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          runbook_url: "https://docs.kryptopesa.com/runbooks/high-error-rate"

      # Response Time Degradation
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1.0
        for: 5m
        labels:
          severity: critical
          service: api
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for the last 5 minutes"
          runbook_url: "https://docs.kryptopesa.com/runbooks/high-response-time"

  # Trading System Alerts
  - name: kryptopesa.trading
    interval: 30s
    rules:
      # Failed Trades
      - alert: HighTradeFailureRate
        expr: rate(kryptopesa_trades_failed_total[5m]) / rate(kryptopesa_trades_total[5m]) > 0.02
        for: 2m
        labels:
          severity: warning
          service: trading
        annotations:
          summary: "High trade failure rate"
          description: "Trade failure rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # Blockchain Connection Issues
      - alert: BlockchainConnectionDown
        expr: kryptopesa_blockchain_connection_status == 0
        for: 1m
        labels:
          severity: critical
          service: blockchain
        annotations:
          summary: "Blockchain connection lost"
          description: "Connection to blockchain network has been lost"
          runbook_url: "https://docs.kryptopesa.com/runbooks/blockchain-connection"

      # Escrow Contract Issues
      - alert: EscrowContractError
        expr: rate(kryptopesa_escrow_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          service: escrow
        annotations:
          summary: "Escrow contract errors detected"
          description: "Escrow contract error rate: {{ $value }} errors/second"

  # Security Alerts
  - name: kryptopesa.security
    interval: 15s
    rules:
      # Rate Limiting Violations
      - alert: HighRateLimitViolations
        expr: rate(kryptopesa_rate_limit_violations_total[1m]) > 10
        for: 1m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High rate limit violations"
          description: "Rate limit violations: {{ $value }} violations/second"

      # Suspicious Login Attempts
      - alert: SuspiciousLoginActivity
        expr: rate(kryptopesa_failed_logins_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Suspicious login activity detected"
          description: "Failed login rate: {{ $value }} attempts/second"

      # Security Validation Failures
      - alert: SecurityValidationFailures
        expr: rate(kryptopesa_security_validation_failures_total[5m]) > 1
        for: 1m
        labels:
          severity: critical
          service: security
        annotations:
          summary: "Security validation failures"
          description: "Security validation failure rate: {{ $value }} failures/second"

  # Infrastructure Alerts
  - name: kryptopesa.infrastructure
    interval: 60s
    rules:
      # High CPU Usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      # High Memory Usage
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      # Disk Space Low
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }}"

  # Business Metrics Alerts
  - name: kryptopesa.business
    interval: 60s
    rules:
      # Low Trading Volume
      - alert: LowTradingVolume
        expr: rate(kryptopesa_trade_volume_total[1h]) < 1000
        for: 30m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Low trading volume"
          description: "Trading volume is {{ $value }} for the last hour"

      # High Dispute Rate
      - alert: HighDisputeRate
        expr: rate(kryptopesa_disputes_total[1h]) / rate(kryptopesa_trades_total[1h]) > 0.05
        for: 15m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "High dispute rate"
          description: "Dispute rate is {{ $value | humanizePercentage }} for the last hour"

      # User Registration Drop
      - alert: UserRegistrationDrop
        expr: rate(kryptopesa_user_registrations_total[1h]) < 10
        for: 2h
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Low user registration rate"
          description: "User registration rate is {{ $value }} registrations/hour"

  # WebSocket Alerts
  - name: kryptopesa.websocket
    interval: 30s
    rules:
      # WebSocket Connection Issues
      - alert: WebSocketConnectionDrop
        expr: kryptopesa_websocket_connections < 100
        for: 5m
        labels:
          severity: warning
          service: websocket
        annotations:
          summary: "Low WebSocket connections"
          description: "WebSocket connections dropped to {{ $value }}"

      # High WebSocket Error Rate
      - alert: HighWebSocketErrorRate
        expr: rate(kryptopesa_websocket_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: websocket
        annotations:
          summary: "High WebSocket error rate"
          description: "WebSocket error rate: {{ $value }} errors/second"
