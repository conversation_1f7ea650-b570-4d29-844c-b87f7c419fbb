# KryptoPesa Production Deployment Pipeline
# Enterprise-grade CI/CD with zero-downtime blue-green deployment

name: Production Deployment

on:
  push:
    branches: [main]
    tags: ["v*"]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: kryptopesa
  NODE_VERSION: "18.x"
  DOCKER_BUILDKIT: 1

jobs:
  # Security and Quality Gates
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: "fs"
          scan-ref: "."
          format: "sarif"
          output: "trivy-results.sarif"

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: "trivy-results.sarif"

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # Code Quality Assessment
  code-quality:
    name: Code Quality & Testing
    runs-on: ubuntu-latest
    strategy:
      matrix:
        component: [backend, mobile, admin-dashboard]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: ${{ matrix.component }}/package-lock.json

      - name: Install dependencies
        working-directory: ${{ matrix.component }}
        run: npm ci

      - name: Run ESLint
        working-directory: ${{ matrix.component }}
        run: npm run lint

      - name: Run unit tests
        working-directory: ${{ matrix.component }}
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ${{ matrix.component }}/coverage/lcov.info
          flags: ${{ matrix.component }}

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          projectBaseDir: ${{ matrix.component }}

  # Build and Package
  build:
    name: Build & Package
    runs-on: ubuntu-latest
    needs: [security-scan, code-quality]
    outputs:
      backend-image: ${{ steps.backend-meta.outputs.tags }}
      admin-image: ${{ steps.admin-meta.outputs.tags }}
      mobile-bundle: ${{ steps.mobile-build.outputs.bundle-path }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Backend Build
      - name: Extract backend metadata
        id: backend-meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/backend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,prefix={{branch}}-

      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.backend-meta.outputs.tags }}
          labels: ${{ steps.backend-meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            NODE_ENV=production
            BUILD_DATE=${{ github.event.head_commit.timestamp }}
            VCS_REF=${{ github.sha }}

      # Admin Dashboard Build
      - name: Extract admin metadata
        id: admin-meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/admin
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,prefix={{branch}}-

      - name: Build and push admin image
        uses: docker/build-push-action@v5
        with:
          context: ./admin-dashboard
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.admin-meta.outputs.tags }}
          labels: ${{ steps.admin-meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # Mobile App Build
      - name: Setup Node.js for mobile
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: mobile/package-lock.json

      - name: Install mobile dependencies
        working-directory: mobile
        run: npm ci

      - name: Build mobile bundle
        id: mobile-build
        working-directory: mobile
        run: |
          npm run build:production
          echo "bundle-path=mobile/dist" >> $GITHUB_OUTPUT

      - name: Upload mobile artifacts
        uses: actions/upload-artifact@v3
        with:
          name: mobile-bundle
          path: mobile/dist/
          retention-days: 30

  # Integration Testing
  integration-tests:
    name: Integration Testing
    runs-on: ubuntu-latest
    needs: [build]
    services:
      mongodb:
        image: mongo:6.0
        env:
          MONGO_INITDB_ROOT_USERNAME: test
          MONGO_INITDB_ROOT_PASSWORD: test
        ports:
          - 27017:27017
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: backend/package-lock.json

      - name: Install backend dependencies
        working-directory: backend
        run: npm ci

      - name: Wait for services
        run: |
          timeout 60 bash -c 'until nc -z localhost 27017; do sleep 1; done'
          timeout 60 bash -c 'until nc -z localhost 6379; do sleep 1; done'

      - name: Run integration tests
        working-directory: backend
        env:
          NODE_ENV: test
          MONGODB_URI: ***************************************************
          REDIS_URL: redis://localhost:6379
        run: npm run test:integration

      - name: Run API tests
        working-directory: backend
        env:
          NODE_ENV: test
          MONGODB_URI: ***************************************************
          REDIS_URL: redis://localhost:6379
        run: npm run test:api

  # Staging Deployment
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, integration-tests]
    if: github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://staging.kryptopesa.com

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Deploy to EKS staging
        run: |
          aws eks update-kubeconfig --name kryptopesa-staging
          kubectl set image deployment/backend backend=${{ needs.build.outputs.backend-image }}
          kubectl set image deployment/admin admin=${{ needs.build.outputs.admin-image }}
          kubectl rollout status deployment/backend
          kubectl rollout status deployment/admin

      - name: Run smoke tests
        run: |
          curl -f https://staging-api.kryptopesa.com/health || exit 1
          curl -f https://staging-admin.kryptopesa.com/health || exit 1

      - name: Notify staging deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: "#deployments"
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # Production Deployment (Blue-Green)
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, integration-tests, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v')
    environment:
      name: production
      url: https://api.kryptopesa.com

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: us-east-1

      - name: Blue-Green Deployment
        run: |
          # Update kubeconfig
          aws eks update-kubeconfig --name kryptopesa-production

          # Deploy to green environment
          kubectl apply -f k8s/production/green/
          kubectl set image deployment/backend-green backend=${{ needs.build.outputs.backend-image }}
          kubectl set image deployment/admin-green admin=${{ needs.build.outputs.admin-image }}

          # Wait for green deployment
          kubectl rollout status deployment/backend-green
          kubectl rollout status deployment/admin-green

          # Health check green environment
          kubectl port-forward service/backend-green 8080:3000 &
          sleep 10
          curl -f http://localhost:8080/health || exit 1

          # Switch traffic to green
          kubectl patch service backend -p '{"spec":{"selector":{"version":"green"}}}'
          kubectl patch service admin -p '{"spec":{"selector":{"version":"green"}}}'

          # Wait and verify
          sleep 30
          curl -f https://api.kryptopesa.com/health || exit 1

          # Scale down blue environment
          kubectl scale deployment backend-blue --replicas=0
          kubectl scale deployment admin-blue --replicas=0

      - name: Database migration
        run: |
          kubectl run migration-job --image=${{ needs.build.outputs.backend-image }} \
            --restart=Never --rm -i -- npm run migrate:production

      - name: Post-deployment verification
        run: |
          # Run production health checks
          curl -f https://api.kryptopesa.com/health/detailed || exit 1
          curl -f https://admin.kryptopesa.com/health || exit 1

          # Verify critical endpoints
          curl -f https://api.kryptopesa.com/api/offers?limit=1 || exit 1
          curl -f https://api.kryptopesa.com/api/trades?limit=1 || exit 1

      - name: Notify production deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: "#production"
          webhook_url: ${{ secrets.SLACK_WEBHOOK_PROD }}
          text: |
            🚀 Production deployment successful!
            Version: ${{ github.ref_name }}
            Backend: ${{ needs.build.outputs.backend-image }}
            Admin: ${{ needs.build.outputs.admin-image }}

  # Rollback capability
  rollback-production:
    name: Rollback Production
    runs-on: ubuntu-latest
    if: failure() && startsWith(github.ref, 'refs/tags/v')
    needs: [deploy-production]
    environment:
      name: production

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: us-east-1

      - name: Rollback to blue environment
        run: |
          aws eks update-kubeconfig --name kryptopesa-production

          # Switch traffic back to blue
          kubectl patch service backend -p '{"spec":{"selector":{"version":"blue"}}}'
          kubectl patch service admin -p '{"spec":{"selector":{"version":"blue"}}}'

          # Scale up blue environment
          kubectl scale deployment backend-blue --replicas=3
          kubectl scale deployment admin-blue --replicas=2

          # Wait for blue to be ready
          kubectl rollout status deployment/backend-blue
          kubectl rollout status deployment/admin-blue

          # Verify rollback
          curl -f https://api.kryptopesa.com/health || exit 1

      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: "warning"
          channel: "#production"
          webhook_url: ${{ secrets.SLACK_WEBHOOK_PROD }}
          text: |
            ⚠️ Production rollback executed!
            Reason: Deployment failure
            Status: Traffic switched back to previous version

  # Performance Testing
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm install -g artillery

      - name: Run load tests
        run: |
          artillery run scripts/load-test-config.yml \
            --target https://staging-api.kryptopesa.com \
            --output performance-report.json

      - name: Generate performance report
        run: |
          artillery report performance-report.json \
            --output performance-report.html

      - name: Upload performance report
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: performance-report.html
