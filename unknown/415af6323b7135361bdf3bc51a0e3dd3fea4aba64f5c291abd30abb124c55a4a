#!/usr/bin/env node

/**
 * Remove or replace console statements for production
 * This script processes all JavaScript files and handles console statements appropriately
 */

const fs = require('fs').promises;
const path = require('path');

class ConsoleStatementProcessor {
  constructor() {
    this.processedFiles = 0;
    this.modifiedFiles = 0;
    this.totalReplacements = 0;
  }

  async processDirectory(dirPath) {
    // console.log(`🔍 Processing directory: ${dirPath}`);
    
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
          await this.processDirectory(fullPath);
        } else if (entry.isFile() && this.shouldProcessFile(entry.name)) {
          await this.processFile(fullPath);
        }
      }
    } catch (error) {
      // console.error(`Error processing directory ${dirPath}:`, error.message);
    }
  }

  shouldSkipDirectory(dirName) {
    const skipDirs = [
      'node_modules',
      '.git',
      'build',
      'dist',
      'coverage',
      '__tests__',
      'test',
      'tests'
    ];
    return skipDirs.includes(dirName) || dirName.startsWith('.');
  }

  shouldProcessFile(fileName) {
    return fileName.endsWith('.js') || fileName.endsWith('.jsx') || 
           fileName.endsWith('.ts') || fileName.endsWith('.tsx');
  }

  async processFile(filePath) {
    try {
      this.processedFiles++;
      const content = await fs.readFile(filePath, 'utf8');
      const modifiedContent = this.processConsoleStatements(content, filePath);
      
      if (content !== modifiedContent) {
        await fs.writeFile(filePath, modifiedContent);
        this.modifiedFiles++;
        // console.log(`  ✅ Modified: ${path.relative(process.cwd(), filePath)}`);
      }
    } catch (error) {
      // console.error(`Error processing file ${filePath}:`, error.message);
    }
  }

  processConsoleStatements(content, filePath) {
    let modified = content;
    let replacements = 0;

    // Different strategies based on file type and location
    const isTestFile = this.isTestFile(filePath);
    const isProductionCode = this.isProductionCode(filePath);

    if (isTestFile) {
      // In test files, keep console statements but add conditional checks
      modified = this.addConditionalLogging(modified);
    } else if (isProductionCode) {
      // In production code, replace with proper logging
      const result = this.replaceWithProperLogging(modified);
      modified = result.content;
      replacements = result.replacements;
    } else {
      // In development/utility files, comment out console statements
      const result = this.commentOutConsoleStatements(modified);
      modified = result.content;
      replacements = result.replacements;
    }

    this.totalReplacements += replacements;
    return modified;
  }

  isTestFile(filePath) {
    return filePath.includes('test') || 
           filePath.includes('spec') || 
           filePath.includes('__tests__');
  }

  isProductionCode(filePath) {
    return filePath.includes('/src/') && 
           !filePath.includes('test') && 
           !filePath.includes('spec') &&
           !filePath.includes('scripts/');
  }

  addConditionalLogging(content) {
    // Add conditional checks for test console statements
    return content.replace(
      /(\s*)(console\.(log|warn|error|debug|info))\s*\(/g,
      '$1if (process.env.NODE_ENV !== \'test\' || process.env.VERBOSE_TESTS) $2('
    );
  }

  replaceWithProperLogging(content) {
    let replacements = 0;
    
    // Check if logger is already imported
    const hasLoggerImport = /require\(['"].*logger['"]|import.*logger/i.test(content);
    
    let modified = content;
    
    // Add logger import if not present
    if (!hasLoggerImport && /console\.(log|warn|error|debug|info)/.test(content)) {
      const importLine = "const logger = require('./utils/logger');\n";
      
      // Find the best place to insert the import
      const lines = modified.split('\n');
      let insertIndex = 0;
      
      // Look for existing requires/imports
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('require(') || lines[i].includes('import ')) {
          insertIndex = i + 1;
        } else if (lines[i].trim() === '' || lines[i].startsWith('//') || lines[i].startsWith('/*')) {
          continue;
        } else {
          break;
        }
      }
      
      lines.splice(insertIndex, 0, importLine);
      modified = lines.join('\n');
    }
    
    // Replace console statements with logger calls
    const replacements_map = {
      'console.log': 'logger.info',
      'console.info': 'logger.info',
      'console.warn': 'logger.warn',
      'console.error': 'logger.error',
      'console.debug': 'logger.debug'
    };
    
    for (const [consoleMethod, loggerMethod] of Object.entries(replacements_map)) {
      const regex = new RegExp(`\\b${consoleMethod.replace('.', '\\.')}\\b`, 'g');
      const matches = modified.match(regex);
      if (matches) {
        replacements += matches.length;
        modified = modified.replace(regex, loggerMethod);
      }
    }
    
    return { content: modified, replacements };
  }

  commentOutConsoleStatements(content) {
    let replacements = 0;
    
    const lines = content.split('\n');
    const modifiedLines = lines.map(line => {
      if (/^\s*console\.(log|warn|error|debug|info)/.test(line)) {
        replacements++;
        return line.replace(/^(\s*)/, '$1// ');
      }
      return line;
    });
    
    return { 
      content: modifiedLines.join('\n'), 
      replacements 
    };
  }

  async generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        processedFiles: this.processedFiles,
        modifiedFiles: this.modifiedFiles,
        totalReplacements: this.totalReplacements
      },
      recommendations: []
    };

    if (this.totalReplacements > 0) {
      report.recommendations.push(
        'Console statements have been processed for production readiness'
      );
    }

    if (this.modifiedFiles === 0) {
      report.recommendations.push(
        'No console statements found that needed modification'
      );
    }

    // Save report
    const reportPath = path.join(process.cwd(), 'reports', `console-cleanup-${Date.now()}.json`);
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    // console.log('\n📋 Console Statement Cleanup Report');
    // console.log('=====================================');
    // console.log(`Files processed: ${this.processedFiles}`);
    // console.log(`Files modified: ${this.modifiedFiles}`);
    // console.log(`Total replacements: ${this.totalReplacements}`);
    // console.log(`Report saved: ${reportPath}`);

    return report;
  }
}

async function main() {
  // console.log('🧹 Starting Console Statement Cleanup');
  // console.log('=====================================');

  const processor = new ConsoleStatementProcessor();
  
  // Process main directories
  const directories = [
    'backend/src',
    'admin-dashboard/src',
    'scripts'
  ];

  for (const dir of directories) {
    if (await fs.access(dir).then(() => true).catch(() => false)) {
      await processor.processDirectory(dir);
    } else {
      // console.log(`⚠️  Directory not found: ${dir}`);
    }
  }

  await processor.generateReport();
  // console.log('\n✅ Console statement cleanup completed');
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    // console.error('❌ Console cleanup failed:', error);
    process.exit(1);
  });
}

module.exports = ConsoleStatementProcessor;
