/**
 * Transaction Monitoring Service
 * Persists pending transaction state in database to prevent loss on process restart
 * Provides comprehensive transaction lifecycle management and recovery
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');

// Transaction State Schema for persistence
const transactionStateSchema = new mongoose.Schema({
  transactionId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['trade_creation', 'escrow_creation', 'escrow_release', 'dispute_creation', 'balance_update', 'wallet_transaction', 'blockchain_transaction']
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'in_progress', 'completed', 'failed', 'rolled_back', 'timeout'],
    default: 'pending'
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  relatedEntities: {
    tradeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Trade' },
    disputeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Dispute' },
    walletId: { type: mongoose.Schema.Types.ObjectId, ref: 'Wallet' },
    offerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Offer' }
  },
  blockchainData: {
    txHash: String,
    network: String,
    blockNumber: Number,
    confirmations: Number,
    gasUsed: String,
    gasPrice: String,
    fee: String,
    fromAddress: String,
    toAddress: String,
    contractAddress: String,
    tokenAddress: String,
    amount: String,
    symbol: String
  },
  operationData: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  retryCount: {
    type: Number,
    default: 0
  },
  maxRetries: {
    type: Number,
    default: 3
  },
  lastRetryAt: Date,
  nextRetryAt: Date,
  startedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: Date,
  errorDetails: {
    message: String,
    stack: String,
    code: String,
    lastOccurred: Date
  },
  metadata: {
    sessionId: String,
    clientInfo: String,
    priority: {
      type: String,
      enum: ['low', 'normal', 'high', 'critical'],
      default: 'normal'
    }
  }
}, {
  timestamps: true,
  collection: 'transaction_states'
});

// Indexes for performance
transactionStateSchema.index({ status: 1, nextRetryAt: 1 });
transactionStateSchema.index({ type: 1, status: 1 });
transactionStateSchema.index({ userId: 1, status: 1 });
transactionStateSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7 * 24 * 60 * 60 }); // 7 days TTL

const TransactionState = mongoose.model('TransactionState', transactionStateSchema);

class TransactionMonitoringService {
  constructor() {
    this.monitoringInterval = null;
    this.recoveryInterval = null;
    this.isRunning = false;
    
    // Configuration
    this.config = {
      monitoringIntervalMs: 30000, // 30 seconds
      recoveryIntervalMs: 60000,   // 1 minute
      maxRetries: 3,
      retryDelayMs: 5000,          // 5 seconds
      timeoutMs: 300000,           // 5 minutes
      batchSize: 50
    };
    
    this.initialize();
  }

  /**
   * Initialize transaction monitoring service
   */
  async initialize() {
    try {
      logger.info('🔄 Initializing Transaction Monitoring Service...');
      
      // Recover any pending transactions from previous session
      await this.recoverPendingTransactions();
      
      // Start monitoring intervals
      this.startMonitoring();
      
      logger.info('✅ Transaction Monitoring Service initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Transaction Monitoring Service initialization failed:', error);
      return false;
    }
  }

  /**
   * Start a new monitored transaction
   */
  async startTransaction(transactionId, type, userId, operationData, options = {}) {
    try {
      const transactionState = new TransactionState({
        transactionId,
        type,
        userId,
        operationData,
        status: 'pending',
        maxRetries: options.maxRetries || this.config.maxRetries,
        metadata: {
          sessionId: options.sessionId,
          clientInfo: options.clientInfo,
          priority: options.priority || 'normal'
        },
        relatedEntities: options.relatedEntities || {},
        blockchainData: options.blockchainData || {}
      });

      await transactionState.save();
      
      logger.info(`📝 Transaction monitoring started: ${transactionId}`, {
        type,
        userId,
        priority: options.priority
      });
      
      return transactionState;
    } catch (error) {
      logger.error(`❌ Failed to start transaction monitoring: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Update transaction status
   */
  async updateTransactionStatus(transactionId, status, errorDetails = null, completionData = null) {
    try {
      const updateData = {
        status,
        ...(status === 'completed' && { completedAt: new Date() }),
        ...(errorDetails && { errorDetails: { ...errorDetails, lastOccurred: new Date() } }),
        ...(completionData && { operationData: { ...completionData } })
      };

      const transactionState = await TransactionState.findOneAndUpdate(
        { transactionId },
        updateData,
        { new: true }
      );

      if (!transactionState) {
        logger.warn(`⚠️ Transaction state not found for update: ${transactionId}`);
        return null;
      }

      logger.info(`📊 Transaction status updated: ${transactionId} -> ${status}`);
      return transactionState;
    } catch (error) {
      logger.error(`❌ Failed to update transaction status: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Mark transaction as in progress
   */
  async markInProgress(transactionId) {
    return await this.updateTransactionStatus(transactionId, 'in_progress');
  }

  /**
   * Mark transaction as completed
   */
  async markCompleted(transactionId, completionData = null) {
    return await this.updateTransactionStatus(transactionId, 'completed', null, completionData);
  }

  /**
   * Mark transaction as failed
   */
  async markFailed(transactionId, error) {
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      code: error.code || 'UNKNOWN'
    };
    
    return await this.updateTransactionStatus(transactionId, 'failed', errorDetails);
  }

  /**
   * Schedule transaction retry
   */
  async scheduleRetry(transactionId, delayMs = null) {
    try {
      const transactionState = await TransactionState.findOne({ transactionId });
      if (!transactionState) {
        throw new AppError('Transaction state not found', 404);
      }

      if (transactionState.retryCount >= transactionState.maxRetries) {
        await this.updateTransactionStatus(transactionId, 'failed', {
          message: 'Maximum retry attempts exceeded',
          code: 'MAX_RETRIES_EXCEEDED'
        });
        return false;
      }

      const retryDelay = delayMs || this.config.retryDelayMs * Math.pow(2, transactionState.retryCount);
      const nextRetryAt = new Date(Date.now() + retryDelay);

      await TransactionState.findOneAndUpdate(
        { transactionId },
        {
          $inc: { retryCount: 1 },
          lastRetryAt: new Date(),
          nextRetryAt,
          status: 'pending'
        }
      );

      logger.info(`🔄 Transaction retry scheduled: ${transactionId} (attempt ${transactionState.retryCount + 1}/${transactionState.maxRetries})`);
      return true;
    } catch (error) {
      logger.error(`❌ Failed to schedule retry for transaction: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Get transaction state
   */
  async getTransactionState(transactionId) {
    try {
      return await TransactionState.findOne({ transactionId })
        .populate('userId', 'email username')
        .populate('relatedEntities.tradeId')
        .populate('relatedEntities.disputeId')
        .populate('relatedEntities.walletId');
    } catch (error) {
      logger.error(`❌ Failed to get transaction state: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Get pending transactions for retry
   */
  async getPendingTransactions(limit = null) {
    try {
      const query = TransactionState.find({
        status: 'pending',
        $or: [
          { nextRetryAt: { $lte: new Date() } },
          { nextRetryAt: { $exists: false } }
        ]
      }).sort({ 'metadata.priority': -1, createdAt: 1 });

      if (limit) {
        query.limit(limit);
      }

      return await query.exec();
    } catch (error) {
      logger.error('❌ Failed to get pending transactions', error);
      throw error;
    }
  }

  /**
   * Start monitoring intervals
   */
  startMonitoring() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;

    // Monitor for timeout transactions
    this.monitoringInterval = setInterval(() => {
      this.checkTimeoutTransactions();
    }, this.config.monitoringIntervalMs);

    // Recovery interval for failed transactions
    this.recoveryInterval = setInterval(() => {
      this.processRetryQueue();
    }, this.config.recoveryIntervalMs);

    logger.info('🔄 Transaction monitoring intervals started');
  }

  /**
   * Stop monitoring intervals
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.recoveryInterval) {
      clearInterval(this.recoveryInterval);
      this.recoveryInterval = null;
    }

    this.isRunning = false;
    logger.info('⏹️ Transaction monitoring intervals stopped');
  }

  /**
   * Check for timeout transactions
   */
  async checkTimeoutTransactions() {
    try {
      const timeoutThreshold = new Date(Date.now() - this.config.timeoutMs);

      const timedOutTransactions = await TransactionState.find({
        status: 'in_progress',
        startedAt: { $lt: timeoutThreshold }
      });

      for (const transaction of timedOutTransactions) {
        logger.warn(`⏰ Transaction timeout detected: ${transaction.transactionId}`);

        await this.updateTransactionStatus(transaction.transactionId, 'timeout', {
          message: 'Transaction timed out',
          code: 'TRANSACTION_TIMEOUT'
        });

        // Schedule retry if retries available
        if (transaction.retryCount < transaction.maxRetries) {
          await this.scheduleRetry(transaction.transactionId);
        }
      }
    } catch (error) {
      logger.error('❌ Error checking timeout transactions:', error);
    }
  }

  /**
   * Process retry queue
   */
  async processRetryQueue() {
    try {
      const pendingTransactions = await this.getPendingTransactions(this.config.batchSize);

      if (pendingTransactions.length === 0) {
        return;
      }

      logger.info(`🔄 Processing ${pendingTransactions.length} pending transactions`);

      for (const transaction of pendingTransactions) {
        try {
          await this.retryTransaction(transaction);
        } catch (error) {
          logger.error(`❌ Failed to retry transaction ${transaction.transactionId}:`, error);
          await this.markFailed(transaction.transactionId, error);
        }
      }
    } catch (error) {
      logger.error('❌ Error processing retry queue:', error);
    }
  }

  /**
   * Retry a specific transaction
   */
  async retryTransaction(transactionState) {
    try {
      logger.info(`🔄 Retrying transaction: ${transactionState.transactionId} (type: ${transactionState.type})`);

      await this.markInProgress(transactionState.transactionId);

      const { dataConsistencyService } = require('./dataConsistency');

      let result;

      switch (transactionState.type) {
        case 'trade_creation':
          result = await dataConsistencyService.executeAtomicTradeCreation(
            transactionState.operationData.tradeData,
            transactionState.operationData.offerData,
            transactionState.userId
          );
          break;

        case 'escrow_creation':
          result = await dataConsistencyService.executeAtomicEscrowCreation(
            transactionState.operationData.tradeId,
            transactionState.operationData.escrowData,
            transactionState.userId
          );
          break;

        case 'escrow_release':
          result = await dataConsistencyService.executeAtomicEscrowRelease(
            transactionState.operationData.tradeId,
            transactionState.operationData.releaseData,
            transactionState.userId
          );
          break;

        case 'dispute_creation':
          result = await dataConsistencyService.executeAtomicDisputeCreation(
            transactionState.operationData.tradeId,
            transactionState.operationData.disputeData,
            transactionState.userId
          );
          break;

        case 'balance_update':
          result = await dataConsistencyService.executeAtomicBalanceUpdate(
            transactionState.userId,
            transactionState.operationData.balanceUpdates,
            transactionState.operationData.transactionData
          );
          break;

        case 'blockchain_transaction':
          result = await this.processBlockchainTransaction(transactionState);
          break;

        default:
          throw new AppError(`Unknown transaction type: ${transactionState.type}`, 400);
      }

      await this.markCompleted(transactionState.transactionId, result);
      logger.info(`✅ Transaction retry successful: ${transactionState.transactionId}`);

      return result;
    } catch (error) {
      logger.error(`❌ Transaction retry failed: ${transactionState.transactionId}`, error);

      // Schedule another retry if possible
      const canRetry = await this.scheduleRetry(transactionState.transactionId);
      if (!canRetry) {
        await this.markFailed(transactionState.transactionId, error);
      }

      throw error;
    }
  }

  /**
   * Process blockchain transaction monitoring
   */
  async processBlockchainTransaction(transactionState) {
    try {
      const { txHash, network } = transactionState.blockchainData;

      // Import blockchain services dynamically to avoid circular dependencies
      const ethereumService = require('./blockchain/ethereumService');
      const bitcoinService = require('./blockchain/bitcoinService');

      let txStatus;

      if (network === 'bitcoin') {
        txStatus = await bitcoinService.getTransaction(txHash);
      } else {
        txStatus = await ethereumService.waitForTransaction(txHash, network, 1);
      }

      // Update blockchain data with latest status
      const updatedBlockchainData = {
        ...transactionState.blockchainData,
        blockNumber: txStatus.blockNumber,
        confirmations: txStatus.confirmations,
        gasUsed: txStatus.gasUsed
      };

      // Check confirmation thresholds
      const confirmationThresholds = {
        bitcoin: 1,
        ethereum: 12,
        polygon: 20
      };

      const requiredConfirmations = confirmationThresholds[network] || 12;

      if (txStatus.status === 'success' && txStatus.confirmations >= requiredConfirmations) {
        // Transaction confirmed - update wallet and complete monitoring
        await this.updateWalletTransactionStatus(
          transactionState.userId,
          txHash,
          'confirmed',
          txStatus.confirmations,
          txStatus.blockNumber
        );

        return {
          status: 'completed',
          blockchainData: updatedBlockchainData,
          message: 'Blockchain transaction confirmed'
        };
      } else if (txStatus.status === 'failed') {
        // Transaction failed
        await this.updateWalletTransactionStatus(
          transactionState.userId,
          txHash,
          'failed',
          0
        );

        return {
          status: 'failed',
          blockchainData: updatedBlockchainData,
          message: 'Blockchain transaction failed'
        };
      } else {
        // Still pending - update data and continue monitoring
        await this.updateTransactionStatus(
          transactionState.transactionId,
          'in_progress',
          null,
          { blockchainData: updatedBlockchainData }
        );

        return {
          status: 'pending',
          blockchainData: updatedBlockchainData,
          message: 'Blockchain transaction still pending'
        };
      }

    } catch (error) {
      logger.error(`Error processing blockchain transaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update wallet transaction status
   */
  async updateWalletTransactionStatus(userId, txHash, status, confirmations, blockNumber = null) {
    try {
      const Wallet = require('../models/Wallet');
      const wallet = await Wallet.findOne({ user: userId });

      if (wallet) {
        await wallet.updateTransactionStatus(txHash, status, confirmations, blockNumber);
        logger.info(`Wallet transaction status updated: ${txHash} -> ${status}`);
      }
    } catch (error) {
      logger.error(`Error updating wallet transaction status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Start monitoring blockchain transaction
   */
  async startBlockchainTransactionMonitoring(txHash, network, userId, transactionData, options = {}) {
    try {
      const transactionId = `blockchain_${txHash}`;

      const operationData = {
        txHash,
        network,
        userId,
        transactionData
      };

      const blockchainData = {
        txHash,
        network,
        fromAddress: transactionData.from,
        toAddress: transactionData.to,
        amount: transactionData.amount,
        symbol: transactionData.symbol,
        gasPrice: transactionData.gasPrice,
        fee: transactionData.fee,
        confirmations: 0
      };

      const transactionState = await this.startTransaction(
        transactionId,
        'blockchain_transaction',
        userId,
        operationData,
        {
          ...options,
          priority: 'high', // Blockchain transactions are high priority
          relatedEntities: {
            walletId: transactionData.walletId,
            tradeId: transactionData.relatedTrade
          },
          blockchainData
        }
      );

      logger.info(`Started persistent monitoring for blockchain transaction: ${txHash}`);
      return transactionState;

    } catch (error) {
      logger.error(`Error starting blockchain transaction monitoring: ${error.message}`);
      throw error;
    }
  }

  /**
   * Recover pending transactions from previous session
   */
  async recoverPendingTransactions() {
    try {
      const pendingTransactions = await TransactionState.find({
        status: { $in: ['pending', 'in_progress'] }
      });

      if (pendingTransactions.length === 0) {
        logger.info('📋 No pending transactions to recover');
        return;
      }

      logger.info(`🔄 Recovering ${pendingTransactions.length} pending transactions from previous session`);

      for (const transaction of pendingTransactions) {
        // Reset in_progress transactions to pending for retry
        if (transaction.status === 'in_progress') {
          await this.updateTransactionStatus(transaction.transactionId, 'pending');
        }

        // Schedule immediate retry for high priority transactions
        if (transaction.metadata.priority === 'critical' || transaction.metadata.priority === 'high') {
          await this.scheduleRetry(transaction.transactionId, 1000); // 1 second delay
        }
      }

      logger.info('✅ Pending transaction recovery completed');
    } catch (error) {
      logger.error('❌ Error recovering pending transactions:', error);
    }
  }

  /**
   * Get monitoring statistics
   */
  async getMonitoringStats() {
    try {
      const stats = await TransactionState.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const typeStats = await TransactionState.aggregate([
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 },
            avgRetries: { $avg: '$retryCount' }
          }
        }
      ]);

      const recentFailures = await TransactionState.find({
        status: 'failed',
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
      }).count();

      return {
        statusDistribution: stats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {}),
        typeDistribution: typeStats,
        recentFailures,
        isMonitoring: this.isRunning,
        config: this.config
      };
    } catch (error) {
      logger.error('❌ Error getting monitoring stats:', error);
      throw error;
    }
  }

  /**
   * Cleanup old completed transactions
   */
  async cleanupOldTransactions(olderThanDays = 7) {
    try {
      const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

      const result = await TransactionState.deleteMany({
        status: { $in: ['completed', 'failed'] },
        createdAt: { $lt: cutoffDate }
      });

      logger.info(`🧹 Cleaned up ${result.deletedCount} old transaction records`);
      return result.deletedCount;
    } catch (error) {
      logger.error('❌ Error cleaning up old transactions:', error);
      throw error;
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    try {
      logger.info('🔄 Shutting down Transaction Monitoring Service...');

      this.stopMonitoring();

      // Wait for any ongoing operations to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      logger.info('✅ Transaction Monitoring Service shutdown completed');
    } catch (error) {
      logger.error('❌ Error during Transaction Monitoring Service shutdown:', error);
    }
  }
}

// Export singleton instance
const transactionMonitoringService = new TransactionMonitoringService();
module.exports = { transactionMonitoringService, TransactionMonitoringService, TransactionState };
