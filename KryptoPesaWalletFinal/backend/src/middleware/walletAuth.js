const { ethers } = require('ethers');
const WalletUser = require('../models/WalletUser');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');

/**
 * Wallet-based authentication middleware
 * Replaces traditional JWT authentication with wallet signature verification
 */

/**
 * Authenticate user via wallet signature
 * Expects headers:
 * - x-wallet-address: The wallet address
 * - x-signature: Message signature
 * - x-timestamp: Request timestamp
 * - x-message: The signed message
 */
const authenticateWallet = async (req, res, next) => {
  try {
    const walletAddress = req.headers['x-wallet-address'];
    const signature = req.headers['x-signature'];
    const timestamp = req.headers['x-timestamp'];
    const message = req.headers['x-message'] ? req.headers['x-message'].replace(/\\n/g, '\n') : null; // Decode newlines from HTTP header

    console.log('🔐 [WALLET AUTH] Request received:', {
      method: req.method,
      path: req.path,
      originalUrl: req.originalUrl,
      walletAddress,
      timestamp,
      message: message ? message.substring(0, 100) + '...' : null,
      signature: signature ? signature.substring(0, 20) + '...' : null
    });

    // Validate required headers
    if (!walletAddress || !signature || !timestamp || !message) {
      console.error('❌ [WALLET AUTH] Missing headers:', {
        hasWalletAddress: !!walletAddress,
        hasSignature: !!signature,
        hasTimestamp: !!timestamp,
        hasMessage: !!message
      });
      throw new AppError('Missing authentication headers', 401);
    }

    // Validate wallet address format
    if (!ethers.utils.isAddress(walletAddress)) {
      throw new AppError('Invalid wallet address format', 401);
    }

    // Check timestamp to prevent replay attacks (5 minute window)
    const now = Date.now();
    const requestTime = parseInt(timestamp);
    const timeDiff = Math.abs(now - requestTime);
    
    if (timeDiff > 5 * 60 * 1000) { // 5 minutes
      throw new AppError('Request timestamp expired', 401);
    }

    // Verify the message format (should include timestamp and action)
    // Extract the path that matches what the mobile app sends
    let actionPath = req.originalUrl.replace(/^\/api/, '') || '/';

    // Apply the same path mapping logic as the mobile app
    if (actionPath.startsWith('/wallet-auth/')) {
      // /wallet-auth/* routes use the full path after /wallet-auth
      actionPath = actionPath.replace('/wallet-auth', '');
    } else if (actionPath === '/wallet') {
      // /wallet route maps to / in the wallet router (matches mobile app logic)
      actionPath = '/';
    } else if (actionPath.startsWith('/wallet/')) {
      // /wallet/* routes use the path after /wallet
      actionPath = actionPath.replace('/wallet', '');
    }

    const expectedMessage = `KryptoPesa Authentication\nTimestamp: ${timestamp}\nAction: ${req.method} ${actionPath}`;

    console.log('🔍 [WALLET AUTH] Message verification:', {
      receivedMessage: message,
      expectedMessage,
      originalUrl: req.originalUrl,
      actionPath,
      matches: message === expectedMessage
    });

    if (message !== expectedMessage) {
      console.error('❌ [WALLET AUTH] Message format mismatch:', {
        received: message,
        expected: expectedMessage
      });
      throw new AppError('Invalid message format', 401);
    }

    // Verify signature
    let recoveredAddress;
    try {
      recoveredAddress = ethers.utils.verifyMessage(message, signature);
    } catch (error) {
      throw new AppError('Invalid signature', 401);
    }

    // Check if recovered address matches provided wallet address
    if (recoveredAddress.toLowerCase() !== walletAddress.toLowerCase()) {
      throw new AppError('Signature verification failed', 401);
    }

    // Find or create user
    let user = await WalletUser.findByWalletAddress(walletAddress);
    
    if (!user) {
      // Auto-create user on first authentication
      user = await WalletUser.createFromWallet(walletAddress, recoveredAddress, {
        lastLoginIP: req.ip,
        deviceInfo: {
          userAgent: req.headers['user-agent'],
          timestamp: new Date()
        }
      });
      
      logger.info(`New wallet user created: ${walletAddress}`);
    } else {
      // Update last active and login info
      user.lastActive = new Date();
      user.lastLoginIP = req.ip;
      await user.save();
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new AppError(`Account is ${user.status}`, 403);
    }

    // Check if account is locked
    if (user.isLocked) {
      if (user.lockExpiresAt && user.lockExpiresAt < new Date()) {
        // Auto-unlock expired locks
        user.isLocked = false;
        user.lockReason = null;
        user.lockExpiresAt = null;
        await user.save();
      } else {
        throw new AppError('Account is temporarily locked', 423);
      }
    }

    // Attach user to request
    req.user = user;
    req.walletAddress = walletAddress;

    logger.debug(`Wallet authentication successful: ${walletAddress}`);
    next();

  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        code: 'WALLET_AUTH_FAILED'
      });
    }

    logger.error('Wallet authentication error:', error);
    return res.status(401).json({
      success: false,
      message: 'Authentication failed',
      code: 'WALLET_AUTH_ERROR'
    });
  }
};

/**
 * Optional authentication - doesn't fail if no auth provided
 * Useful for public endpoints that can show different data for authenticated users
 */
const optionalWalletAuth = async (req, res, next) => {
  const walletAddress = req.headers['x-wallet-address'];
  
  if (!walletAddress) {
    // No authentication provided, continue without user
    return next();
  }

  // Try to authenticate, but don't fail if it doesn't work
  try {
    await authenticateWallet(req, res, () => {
      // Authentication successful
      next();
    });
  } catch (error) {
    // Authentication failed, but continue without user
    logger.debug('Optional wallet auth failed, continuing without user');
    next();
  }
};

/**
 * Authorization middleware for different user roles/levels
 */
const authorize = (...allowedLevels) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Check verification level
    const userLevel = req.user.verification.level;
    
    if (!allowedLevels.includes(userLevel)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient verification level',
        code: 'INSUFFICIENT_VERIFICATION',
        required: Math.min(...allowedLevels),
        current: userLevel
      });
    }

    next();
  };
};

/**
 * Admin authorization (for admin users)
 * In wallet-based system, admins are identified by specific wallet addresses
 */
const authorizeAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  // Check if wallet address is in admin list
  const adminWallets = (process.env.ADMIN_WALLETS || '').split(',').map(addr => addr.toLowerCase());
  
  if (!adminWallets.includes(req.user.walletAddress)) {
    return res.status(403).json({
      success: false,
      message: 'Admin access required',
      code: 'ADMIN_REQUIRED'
    });
  }

  next();
};

/**
 * Rate limiting by wallet address
 */
const rateLimitByWallet = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();

  return (req, res, next) => {
    const walletAddress = req.walletAddress || req.headers['x-wallet-address'];
    
    if (!walletAddress) {
      return next();
    }

    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    for (const [addr, timestamps] of requests.entries()) {
      const validTimestamps = timestamps.filter(ts => ts > windowStart);
      if (validTimestamps.length === 0) {
        requests.delete(addr);
      } else {
        requests.set(addr, validTimestamps);
      }
    }

    // Check current wallet
    const walletRequests = requests.get(walletAddress) || [];
    const recentRequests = walletRequests.filter(ts => ts > windowStart);

    if (recentRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'Too many requests',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((recentRequests[0] + windowMs - now) / 1000)
      });
    }

    // Add current request
    recentRequests.push(now);
    requests.set(walletAddress, recentRequests);

    next();
  };
};

/**
 * Generate authentication message for client
 */
const generateAuthMessage = (timestamp, method, path) => {
  return `KryptoPesa Authentication\nTimestamp: ${timestamp}\nAction: ${method} ${path}`;
};

/**
 * Middleware to validate wallet ownership for specific operations
 * Ensures user can only access their own resources
 */
const validateWalletOwnership = (walletAddressParam = 'walletAddress') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const targetWallet = req.params[walletAddressParam];
    
    if (!targetWallet) {
      return res.status(400).json({
        success: false,
        message: 'Wallet address parameter required',
        code: 'MISSING_WALLET_PARAM'
      });
    }

    if (targetWallet.toLowerCase() !== req.user.walletAddress.toLowerCase()) {
      return res.status(403).json({
        success: false,
        message: 'Can only access your own wallet resources',
        code: 'WALLET_OWNERSHIP_REQUIRED'
      });
    }

    next();
  };
};

module.exports = {
  authenticateWallet,
  optionalWalletAuth,
  authorize,
  authorizeAdmin,
  rateLimitByWallet,
  generateAuthMessage,
  validateWalletOwnership
};
