# Prometheus Configuration for KryptoPesa Monitoring
# Comprehensive monitoring for high-availability production deployment

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'kryptopesa-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load alerting rules
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s

  # API Server instances
  - job_name: 'kryptopesa-api'
    static_configs:
      - targets:
        - 'api-1:3001'
        - 'api-2:3002'
        - 'api-3:3003'
        - 'api-4:3004'
    scrape_interval: 15s
    metrics_path: '/metrics'
    scrape_timeout: 10s
    params:
      format: ['prometheus']

  # Nginx Load Balancer
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    scrape_interval: 30s
    metrics_path: '/nginx_status'

  # PostgreSQL Database
  - job_name: 'postgres-primary'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # MongoDB
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb-exporter:9216']
    scrape_interval: 30s

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets:
        - 'node-exporter-1:9100'
        - 'node-exporter-2:9100'
        - 'node-exporter-3:9100'
        - 'node-exporter-4:9100'
    scrape_interval: 30s

  # Docker container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Elasticsearch
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']
    scrape_interval: 30s

  # Custom application metrics
  - job_name: 'kryptopesa-business-metrics'
    static_configs:
      - targets:
        - 'api-1:3001'
        - 'api-2:3002'
        - 'api-3:3003'
        - 'api-4:3004'
    scrape_interval: 15s
    metrics_path: '/metrics/business'
    params:
      format: ['prometheus']

  # External service monitoring
  - job_name: 'external-apis'
    static_configs:
      - targets:
        - 'blockchain-api-monitor:8080'
        - 'payment-api-monitor:8080'
    scrape_interval: 60s

  # SSL certificate monitoring
  - job_name: 'ssl-certificates'
    static_configs:
      - targets: ['ssl-exporter:9219']
    scrape_interval: 3600s  # Check every hour

  # Blackbox monitoring for external endpoints
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://api.kryptopesa.com/health
        - https://app.kryptopesa.com
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# Remote write configuration for long-term storage
remote_write:
  - url: "http://thanos-receive:19291/api/v1/receive"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Storage configuration
storage:
  tsdb:
    retention.time: 15d
    retention.size: 50GB
    wal-compression: true
