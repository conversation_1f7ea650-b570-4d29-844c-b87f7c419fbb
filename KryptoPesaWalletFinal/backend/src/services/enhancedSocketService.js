const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../utils/logger');
const chatScalabilityService = require('./chatScalabilityService');
const socketRedisAdapter = require('./socketRedisAdapter');
const realTimeScalabilityService = require('./realTimeScalabilityService');
const connectionPoolManager = require('./connectionPoolManager');
const notificationService = require('./notificationService');
const rateLimitingService = require('./rateLimitingService');

class EnhancedSocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // socketId -> userId
    this.userSockets = new Map(); // userId -> Set of socketIds
    this.rateLimitMap = new Map(); // userId -> { count, resetTime }
    this.isInitialized = false;
    
    // Rate limiting configuration
    this.RATE_LIMIT_WINDOW = 60000; // 1 minute
    this.MAX_MESSAGES_PER_WINDOW = 30;
    this.MAX_TYPING_EVENTS_PER_WINDOW = 60;
    
    // Performance metrics
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      messagesProcessed: 0,
      rateLimitViolations: 0,
      averageResponseTime: 0,
      errorCount: 0
    };
  }

  /**
   * Initialize the enhanced socket service
   */
  async initialize(io) {
    try {
      this.io = io;

      // Initialize Redis adapter for multi-server scaling
      try {
        const adapter = await socketRedisAdapter.initialize();
        io.adapter(adapter);

        // Subscribe to cross-server messages
        await socketRedisAdapter.subscribeToRoomMessages(io);
        await socketRedisAdapter.subscribeToUserMessages(io);
        await socketRedisAdapter.subscribeToSystemBroadcasts(io);

        logger.info('Socket.IO Redis adapter configured for multi-server scaling');
      } catch (error) {
        logger.warn('Redis adapter initialization failed, running in single-server mode:', error.message);
      }

      // Initialize real-time scalability service
      await realTimeScalabilityService.initialize();

      // Initialize connection pool manager
      await connectionPoolManager.initialize();

      // Initialize notification service
      await notificationService.initialize();

      // Set up Socket.IO middleware and event handlers
      this.setupMiddleware();
      this.setupEventHandlers();

      this.isInitialized = true;
      logger.info('Enhanced Socket Service initialized successfully with real-time scalability');
    } catch (error) {
      logger.error('Failed to initialize Enhanced Socket Service:', error);
      throw error;
    }
  }

  /**
   * Setup Socket.IO middleware
   */
  setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }
        
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.userId).select('username firstName lastName avatar isOnline');
        
        if (!user) {
          return next(new Error('User not found'));
        }
        
        socket.userId = user._id.toString();
        socket.user = user;
        next();
      } catch (error) {
        next(new Error('Authentication failed'));
      }
    });

    // Rate limiting middleware
    this.io.use((socket, next) => {
      socket.use((packet, next) => {
        const [event] = packet;
        
        if (this.checkRateLimit(socket.userId, event)) {
          next();
        } else {
          socket.emit('rate_limit_exceeded', {
            message: 'Too many requests. Please slow down.',
            retryAfter: 60
          });
          this.metrics.rateLimitViolations++;
        }
      });
      next();
    });
  }

  /**
   * Setup Socket.IO event handlers
   */
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });
  }

  /**
   * Handle new socket connection
   */
  async handleConnection(socket) {
    const startTime = Date.now();

    try {
      const userId = socket.userId;

      // Track connection locally
      this.connectedUsers.set(socket.id, userId);

      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, new Set());
      }
      this.userSockets.get(userId).add(socket.id);

      // Register with connection pool manager
      await connectionPoolManager.registerConnection(socket.id, userId, {
        username: socket.user.username,
        userAgent: socket.handshake.headers['user-agent'],
        ip: socket.handshake.address
      });

      // Register with real-time scalability service
      realTimeScalabilityService.registerUserConnection(userId, socket.id);

      // Update metrics
      this.metrics.totalConnections++;
      this.metrics.activeConnections = this.connectedUsers.size;

      // Join user to personal room
      socket.join(`user:${userId}`);

      // Notify chat scalability service
      await chatScalabilityService.handleUserConnection(userId, socket.id);

      // Publish user online event
      await realTimeScalabilityService.publishEvent('presence', 'user_online', {
        userId,
        userInfo: {
          username: socket.user.username,
          lastSeen: new Date()
        }
      });

      logger.info(`User connected: ${socket.user.username} (${userId}) - Socket: ${socket.id}`);

      // Set up event handlers for this socket
      this.setupSocketEventHandlers(socket);

      // Update response time metric
      this.updateResponseTime(Date.now() - startTime);

    } catch (error) {
      logger.error(`Failed to handle connection: ${error.message}`);
      this.metrics.errorCount++;
      socket.disconnect();
    }
  }

  /**
   * Setup event handlers for individual socket
   */
  setupSocketEventHandlers(socket) {
    const userId = socket.userId;

    // Join trade room
    socket.on('join_trade', async (data) => {
      try {
        const { tradeId } = data;

        if (!tradeId) {
          socket.emit('error', { message: 'Trade ID required' });
          return;
        }

        const roomId = `trade:${tradeId}`;

        // Join room locally
        socket.join(roomId);

        // Register with connection pool manager
        await connectionPoolManager.joinRoom(socket.id, roomId);

        // Subscribe with real-time scalability service
        realTimeScalabilityService.subscribeToRoom(userId, roomId);

        // Notify chat scalability service
        await chatScalabilityService.joinTradeRoom(userId, tradeId);

        socket.emit('joined_trade', { tradeId });
        logger.info(`User ${userId} joined trade room: ${tradeId}`);
      } catch (error) {
        logger.error(`Failed to join trade room: ${error.message}`);
        socket.emit('error', { message: 'Failed to join trade room' });
      }
    });

    // Leave trade room
    socket.on('leave_trade', async (data) => {
      try {
        const { tradeId } = data;

        if (!tradeId) {
          socket.emit('error', { message: 'Trade ID required' });
          return;
        }

        const roomId = `trade:${tradeId}`;

        // Leave room locally
        socket.leave(roomId);

        // Unregister from connection pool manager
        await connectionPoolManager.leaveRoom(socket.id, roomId);

        // Unsubscribe from real-time scalability service
        realTimeScalabilityService.unsubscribeFromRoom(userId, roomId);

        // Notify chat scalability service
        await chatScalabilityService.leaveTradeRoom(userId, tradeId);

        socket.emit('left_trade', { tradeId });
        logger.info(`User ${userId} left trade room: ${tradeId}`);
      } catch (error) {
        logger.error(`Failed to leave trade room: ${error.message}`);
      }
    });

    // Send message
    socket.on('send_message', async (data) => {
      const messageStartTime = Date.now();
      
      try {
        const { tradeId, content, type = 'text', attachmentData } = data;
        
        if (!tradeId || !content || content.trim().length === 0) {
          socket.emit('message_error', { message: 'Invalid message data' });
          return;
        }

        // Create message using scalability service
        const message = await chatScalabilityService.createMessage(
          tradeId, 
          userId, 
          content, 
          type, 
          attachmentData
        );

        // Broadcast message to room participants
        await chatScalabilityService.broadcastMessage(tradeId, message, userId);
        
        // Send confirmation to sender
        socket.emit('message_sent', {
          messageId: message._id,
          tradeId,
          timestamp: message.timestamp
        });

        this.metrics.messagesProcessed++;
        this.updateResponseTime(Date.now() - messageStartTime);
        
      } catch (error) {
        logger.error(`Failed to send message: ${error.message}`);
        socket.emit('message_error', { message: 'Failed to send message' });
        this.metrics.errorCount++;
      }
    });

    // Typing indicators
    socket.on('typing_start', (data) => {
      try {
        const { tradeId } = data;
        
        if (!tradeId) {
          return;
        }

        chatScalabilityService.handleTypingStart(userId, tradeId, socket.user.username);
      } catch (error) {
        logger.error(`Failed to handle typing start: ${error.message}`);
      }
    });

    socket.on('typing_stop', (data) => {
      try {
        const { tradeId } = data;
        
        if (!tradeId) {
          return;
        }

        chatScalabilityService.handleTypingStop(userId, tradeId);
      } catch (error) {
        logger.error(`Failed to handle typing stop: ${error.message}`);
      }
    });

    // Mark messages as read
    socket.on('mark_read', async (data) => {
      try {
        const { tradeId, messageIds } = data;
        
        if (!tradeId) {
          socket.emit('error', { message: 'Trade ID required' });
          return;
        }

        const readCount = await chatScalabilityService.markMessagesAsRead(tradeId, userId, messageIds);
        
        socket.emit('messages_marked_read', {
          tradeId,
          readCount
        });
      } catch (error) {
        logger.error(`Failed to mark messages as read: ${error.message}`);
        socket.emit('error', { message: 'Failed to mark messages as read' });
      }
    });

    // Get unread count
    socket.on('get_unread_count', async (data) => {
      try {
        const { tradeId } = data;
        
        if (!tradeId) {
          socket.emit('error', { message: 'Trade ID required' });
          return;
        }

        const unreadCount = await chatScalabilityService.getUnreadCount(tradeId, userId);
        
        socket.emit('unread_count', {
          tradeId,
          count: unreadCount
        });
      } catch (error) {
        logger.error(`Failed to get unread count: ${error.message}`);
        socket.emit('error', { message: 'Failed to get unread count' });
      }
    });

    // Handle disconnection
    socket.on('disconnect', async () => {
      await this.handleDisconnection(socket);
    });
  }

  /**
   * Handle socket disconnection
   */
  async handleDisconnection(socket) {
    try {
      const userId = socket.userId;

      // Remove from local tracking
      this.connectedUsers.delete(socket.id);

      // Unregister from connection pool manager
      await connectionPoolManager.unregisterConnection(socket.id);

      // Unregister from real-time scalability service
      realTimeScalabilityService.unregisterUserConnection(userId);

      const userSocketSet = this.userSockets.get(userId);
      if (userSocketSet) {
        userSocketSet.delete(socket.id);
        if (userSocketSet.size === 0) {
          this.userSockets.delete(userId);

          // User has no more connections on this server, handle full disconnection
          await chatScalabilityService.handleUserDisconnection(userId);

          // Publish user offline event
          await realTimeScalabilityService.publishEvent('presence', 'user_offline', {
            userId
          });
        }
      }

      // Update metrics
      this.metrics.activeConnections = this.connectedUsers.size;

      logger.info(`User disconnected: ${socket.user?.username} (${userId}) - Socket: ${socket.id}`);
    } catch (error) {
      logger.error(`Failed to handle disconnection: ${error.message}`);
    }
  }

  /**
   * Check rate limiting for user actions
   */
  checkRateLimit(userId, event) {
    const now = Date.now();
    const userLimits = this.rateLimitMap.get(userId) || { count: 0, resetTime: now + this.RATE_LIMIT_WINDOW };

    // Reset counter if window expired
    if (now > userLimits.resetTime) {
      userLimits.count = 0;
      userLimits.resetTime = now + this.RATE_LIMIT_WINDOW;
    }

    // Different limits for different events
    let maxEvents = this.MAX_MESSAGES_PER_WINDOW;
    if (event === 'typing_start' || event === 'typing_stop') {
      maxEvents = this.MAX_TYPING_EVENTS_PER_WINDOW;
    }

    if (userLimits.count >= maxEvents) {
      return false;
    }

    userLimits.count++;
    this.rateLimitMap.set(userId, userLimits);
    return true;
  }

  /**
   * Update average response time metric
   */
  updateResponseTime(responseTime) {
    this.metrics.averageResponseTime =
      (this.metrics.averageResponseTime + responseTime) / 2;
  }

  /**
   * Broadcast message to specific trade room
   */
  async broadcastToTrade(tradeId, event, data) {
    try {
      const roomId = `trade:${tradeId}`;

      // Emit to local room
      this.io.to(roomId).emit(event, data);

      // Also broadcast via Redis for multi-server setup
      await socketRedisAdapter.publishToRoom(roomId, event, data);

      logger.debug(`Broadcasted ${event} to trade ${tradeId}`);
    } catch (error) {
      logger.error(`Failed to broadcast to trade ${tradeId}:`, error);
    }
  }

  /**
   * Send message to specific user
   */
  async sendToUser(userId, event, data) {
    try {
      const userRoom = `user:${userId}`;

      // Emit to local user room
      this.io.to(userRoom).emit(event, data);

      // Also broadcast via Redis for multi-server setup
      await socketRedisAdapter.publishToUser(userId, event, data);

      logger.debug(`Sent ${event} to user ${userId}`);
    } catch (error) {
      logger.error(`Failed to send to user ${userId}:`, error);
    }
  }

  /**
   * Broadcast system message to all connected users
   */
  async broadcastSystemMessage(event, data) {
    try {
      // Emit to all local connections
      this.io.emit(event, data);

      // Also broadcast via Redis for multi-server setup
      await socketRedisAdapter.broadcastSystemMessage(event, data);

      logger.info(`Broadcasted system message: ${event}`);
    } catch (error) {
      logger.error('Failed to broadcast system message:', error);
    }
  }

  /**
   * Get service metrics
   */
  getMetrics() {
    const chatMetrics = chatScalabilityService.getMetrics();
    const realTimeMetrics = realTimeScalabilityService.getMetrics();
    const connectionPoolMetrics = connectionPoolManager.getMetrics();
    const notificationMetrics = notificationService.getMetrics();

    return {
      socket: {
        ...this.metrics,
        connectedUsers: this.connectedUsers.size,
        uniqueUsers: this.userSockets.size,
        rateLimitMapSize: this.rateLimitMap.size
      },
      chat: chatMetrics,
      realTime: realTimeMetrics,
      connectionPool: connectionPoolMetrics,
      notifications: notificationMetrics,
      redis: {
        adapterInitialized: socketRedisAdapter.isInitialized
      }
    };
  }

  /**
   * Get online users in specific trade
   */
  getTradeParticipants(tradeId) {
    const roomId = `trade:${tradeId}`;
    const room = this.io.sockets.adapter.rooms.get(roomId);

    if (!room) {
      return [];
    }

    const participants = [];
    for (const socketId of room) {
      const userId = this.connectedUsers.get(socketId);
      if (userId) {
        participants.push(userId);
      }
    }

    return [...new Set(participants)]; // Remove duplicates
  }

  /**
   * Check if user is online
   */
  isUserOnline(userId) {
    return this.userSockets.has(userId);
  }

  /**
   * Get total online users count
   */
  getOnlineUsersCount() {
    return this.userSockets.size;
  }

  /**
   * Cleanup rate limiting data
   */
  cleanupRateLimits() {
    const now = Date.now();

    for (const [userId, limits] of this.rateLimitMap.entries()) {
      if (now > limits.resetTime) {
        this.rateLimitMap.delete(userId);
      }
    }
  }

  /**
   * Health check for the socket service
   */
  async healthCheck() {
    try {
      const redisHealth = await socketRedisAdapter.healthCheck();

      return {
        healthy: this.isInitialized,
        activeConnections: this.metrics.activeConnections,
        totalConnections: this.metrics.totalConnections,
        messagesProcessed: this.metrics.messagesProcessed,
        errorRate: this.metrics.errorCount / Math.max(this.metrics.messagesProcessed, 1),
        averageResponseTime: this.metrics.averageResponseTime,
        redis: redisHealth
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      };
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    try {
      logger.info('Shutting down Enhanced Socket Service...');

      // Notify all connected users
      await this.broadcastSystemMessage('server_shutdown', {
        message: 'Server is shutting down. Please reconnect in a moment.',
        timestamp: new Date().toISOString()
      });

      // Wait a moment for messages to be sent
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Close all socket connections
      this.io.close();

      // Cleanup Redis adapter
      await socketRedisAdapter.cleanup();

      // Clear internal state
      this.connectedUsers.clear();
      this.userSockets.clear();
      this.rateLimitMap.clear();

      this.isInitialized = false;
      logger.info('Enhanced Socket Service shutdown complete');
    } catch (error) {
      logger.error('Error during socket service shutdown:', error);
    }
  }
}

// Create singleton instance
const enhancedSocketService = new EnhancedSocketService();

// Schedule periodic cleanup (every 5 minutes)
setInterval(() => {
  enhancedSocketService.cleanupRateLimits();
}, 5 * 60 * 1000);

module.exports = enhancedSocketService;
