#!/bin/bash

# KryptoPesa Production Deployment Script
# Deploys the complete KryptoPesa platform to production environment

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${1:-production}"
BACKUP_BEFORE_DEPLOY="${2:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking deployment prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if environment file exists
    if [[ ! -f "$PROJECT_ROOT/.env.production" ]]; then
        log_error "Production environment file (.env.production) not found"
        exit 1
    fi
    
    # Check if SSL certificates exist (for production)
    if [[ "$DEPLOYMENT_ENV" == "production" ]]; then
        if [[ ! -d "$PROJECT_ROOT/nginx/ssl" ]]; then
            log_warning "SSL certificates directory not found. HTTPS will not be available."
        fi
    fi
    
    log_success "Prerequisites check completed"
}

# Backup current deployment
backup_current_deployment() {
    if [[ "$BACKUP_BEFORE_DEPLOY" == "true" ]]; then
        log_info "Creating backup of current deployment..."
        
        BACKUP_DIR="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # Backup database
        if docker ps | grep -q kryptopesa-mongodb; then
            log_info "Backing up MongoDB database..."
            docker exec kryptopesa-mongodb mongodump --out /tmp/backup
            docker cp kryptopesa-mongodb:/tmp/backup "$BACKUP_DIR/mongodb"
        fi
        
        # Backup Redis data
        if docker ps | grep -q kryptopesa-redis; then
            log_info "Backing up Redis data..."
            docker exec kryptopesa-redis redis-cli BGSAVE
            docker cp kryptopesa-redis:/data/dump.rdb "$BACKUP_DIR/redis_dump.rdb"
        fi
        
        # Backup application logs
        if [[ -d "$PROJECT_ROOT/backend/logs" ]]; then
            cp -r "$PROJECT_ROOT/backend/logs" "$BACKUP_DIR/"
        fi
        
        log_success "Backup completed: $BACKUP_DIR"
    fi
}

# Build application images
build_images() {
    log_info "Building application images..."
    
    cd "$PROJECT_ROOT"
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t kryptopesa-backend:latest -f backend/Dockerfile.production backend/
    
    # Build admin dashboard image
    log_info "Building admin dashboard image..."
    docker build -t kryptopesa-admin:latest -f admin-dashboard/Dockerfile.production admin-dashboard/
    
    log_success "Images built successfully"
}

# Deploy services
deploy_services() {
    log_info "Deploying services..."
    
    cd "$PROJECT_ROOT"
    
    # Load environment variables
    export $(cat .env.production | grep -v '^#' | xargs)
    
    # Deploy with Docker Compose
    docker-compose -f docker-compose.production.yml down --remove-orphans
    docker-compose -f docker-compose.production.yml up -d
    
    log_success "Services deployed"
}

# Health checks
perform_health_checks() {
    log_info "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check backend health
    log_info "Checking backend health..."
    for i in {1..10}; do
        if curl -f http://localhost:3000/health &> /dev/null; then
            log_success "Backend is healthy"
            break
        fi
        
        if [[ $i -eq 10 ]]; then
            log_error "Backend health check failed"
            return 1
        fi
        
        log_info "Waiting for backend to be ready... (attempt $i/10)"
        sleep 10
    done
    
    # Check admin dashboard health
    log_info "Checking admin dashboard health..."
    for i in {1..5}; do
        if curl -f http://localhost:3001 &> /dev/null; then
            log_success "Admin dashboard is healthy"
            break
        fi
        
        if [[ $i -eq 5 ]]; then
            log_error "Admin dashboard health check failed"
            return 1
        fi
        
        log_info "Waiting for admin dashboard to be ready... (attempt $i/5)"
        sleep 10
    done
    
    # Check database connectivity
    log_info "Checking database connectivity..."
    if docker exec kryptopesa-backend node -e "
        const mongoose = require('mongoose');
        mongoose.connect(process.env.MONGODB_URI)
            .then(() => { console.log('Database connected'); process.exit(0); })
            .catch(() => { console.log('Database connection failed'); process.exit(1); });
    "; then
        log_success "Database connectivity verified"
    else
        log_error "Database connectivity check failed"
        return 1
    fi
    
    log_success "All health checks passed"
}

# Setup monitoring
setup_monitoring() {
    log_info "Setting up monitoring and logging..."
    
    # Wait for Prometheus to be ready
    log_info "Waiting for Prometheus to be ready..."
    for i in {1..5}; do
        if curl -f http://localhost:9090/-/ready &> /dev/null; then
            log_success "Prometheus is ready"
            break
        fi
        sleep 10
    done
    
    # Wait for Grafana to be ready
    log_info "Waiting for Grafana to be ready..."
    for i in {1..5}; do
        if curl -f http://localhost:3002/api/health &> /dev/null; then
            log_success "Grafana is ready"
            break
        fi
        sleep 10
    done
    
    # Wait for Elasticsearch to be ready
    log_info "Waiting for Elasticsearch to be ready..."
    for i in {1..10}; do
        if curl -f http://localhost:9200/_cluster/health &> /dev/null; then
            log_success "Elasticsearch is ready"
            break
        fi
        sleep 15
    done
    
    log_success "Monitoring setup completed"
}

# Post-deployment tasks
post_deployment_tasks() {
    log_info "Running post-deployment tasks..."
    
    # Run database migrations if needed
    log_info "Running database migrations..."
    docker exec kryptopesa-backend npm run migrate || log_warning "Migration command not found or failed"
    
    # Seed initial data if needed
    log_info "Seeding initial data..."
    docker exec kryptopesa-backend npm run seed:production || log_warning "Seed command not found or failed"
    
    # Clear application caches
    log_info "Clearing application caches..."
    docker exec kryptopesa-redis redis-cli FLUSHDB || log_warning "Cache clear failed"
    
    # Generate API documentation
    log_info "Generating API documentation..."
    docker exec kryptopesa-backend npm run docs:generate || log_warning "Documentation generation failed"
    
    log_success "Post-deployment tasks completed"
}

# Cleanup old images and containers
cleanup() {
    log_info "Cleaning up old images and containers..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    log_success "Cleanup completed"
}

# Display deployment summary
display_summary() {
    log_success "🎉 KryptoPesa deployment completed successfully!"
    echo ""
    echo "=== Deployment Summary ==="
    echo "Environment: $DEPLOYMENT_ENV"
    echo "Deployment Time: $(date)"
    echo ""
    echo "=== Service URLs ==="
    echo "Backend API: http://localhost:3000"
    echo "Admin Dashboard: http://localhost:3001"
    echo "Prometheus: http://localhost:9090"
    echo "Grafana: http://localhost:3002"
    echo "Kibana: http://localhost:5601"
    echo ""
    echo "=== Health Check ==="
    echo "Backend Health: http://localhost:3000/health"
    echo "API Documentation: http://localhost:3000/docs"
    echo ""
    echo "=== Monitoring ==="
    echo "System Metrics: http://localhost:3002 (Grafana)"
    echo "Application Logs: http://localhost:5601 (Kibana)"
    echo ""
    log_info "Deployment logs are available in the container logs"
    log_info "Use 'docker-compose -f docker-compose.production.yml logs -f' to follow logs"
}

# Rollback function
rollback() {
    log_warning "Rolling back deployment..."
    
    cd "$PROJECT_ROOT"
    docker-compose -f docker-compose.production.yml down
    
    # Restore from latest backup if available
    LATEST_BACKUP=$(ls -t backups/ | head -n1)
    if [[ -n "$LATEST_BACKUP" ]]; then
        log_info "Restoring from backup: $LATEST_BACKUP"
        # Add restore logic here
    fi
    
    log_success "Rollback completed"
}

# Main deployment function
main() {
    log_info "🚀 Starting KryptoPesa production deployment..."
    echo "Environment: $DEPLOYMENT_ENV"
    echo "Backup before deploy: $BACKUP_BEFORE_DEPLOY"
    echo ""
    
    # Trap errors and rollback
    trap 'log_error "Deployment failed! Rolling back..."; rollback; exit 1' ERR
    
    check_prerequisites
    backup_current_deployment
    build_images
    deploy_services
    perform_health_checks
    setup_monitoring
    post_deployment_tasks
    cleanup
    display_summary
    
    log_success "🎉 Deployment completed successfully!"
}

# Run main function
main "$@"
