const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const sharp = require('sharp');

describe('File Security System', () => {
  let testImagePath, testMaliciousPath, testLargePath;
  const testUserId = '507f1f77bcf86cd799439011';

  beforeAll(async () => {
    // Create test files
    await createTestFiles();
  });

  afterAll(async () => {
    // Cleanup test files
    await cleanupTestFiles();
  });

  describe('File Security Utilities', () => {
    test('should create test files successfully', async () => {
      expect(testImagePath).toBeDefined();
      expect(testMaliciousPath).toBeDefined();
      expect(testLargePath).toBeDefined();

      // Verify test image exists and is valid
      const imageStats = await fs.stat(testImagePath);
      expect(imageStats.isFile()).toBe(true);
      expect(imageStats.size).toBeGreaterThan(0);
    });

    test('should generate file checksums', async () => {
      const fileBuffer = await fs.readFile(testImagePath);

      const sha256Hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      const md5Hash = crypto.createHash('md5').update(fileBuffer).digest('hex');

      expect(sha256Hash).toMatch(/^[a-f0-9]{64}$/);
      expect(md5Hash).toMatch(/^[a-f0-9]{32}$/);
    });

    test('should detect file types correctly', async () => {
      const imageBuffer = await fs.readFile(testImagePath);

      // Check JPEG signature
      expect(imageBuffer[0]).toBe(0xFF);
      expect(imageBuffer[1]).toBe(0xD8);
      expect(imageBuffer[2]).toBe(0xFF);
    });

    test('should process images with Sharp', async () => {
      const processedImage = await sharp(testImagePath)
        .resize(100, 100)
        .jpeg({ quality: 80 })
        .toBuffer();

      expect(processedImage).toBeInstanceOf(Buffer);
      expect(processedImage.length).toBeGreaterThan(0);
    });

    test('should detect executable file signatures', async () => {
      const maliciousBuffer = await fs.readFile(testMaliciousPath);

      // Check PE signature (MZ header)
      expect(maliciousBuffer[0]).toBe(0x4D); // M
      expect(maliciousBuffer[1]).toBe(0x5A); // Z
    });
  });





  // Helper functions
  async function createTestFiles() {
    const testDir = path.join(__dirname, 'test-files');
    await fs.mkdir(testDir, { recursive: true });

    // Create valid test image
    const testImage = await sharp({
      create: {
        width: 200,
        height: 200,
        channels: 3,
        background: { r: 0, g: 255, b: 0 }
      }
    })
    .jpeg()
    .toBuffer();

    testImagePath = path.join(testDir, 'test-image.jpg');
    await fs.writeFile(testImagePath, testImage);

    // Create malicious file (fake executable)
    testMaliciousPath = path.join(testDir, 'malicious.exe');
    await fs.writeFile(testMaliciousPath, Buffer.from([0x4D, 0x5A, 0x90, 0x00]));

    // Create oversized file
    testLargePath = path.join(testDir, 'large-image.jpg');
    const largeImage = await sharp({
      create: {
        width: 5000,
        height: 5000,
        channels: 3,
        background: { r: 0, g: 0, b: 255 }
      }
    })
    .jpeg({ quality: 100 })
    .toBuffer();

    await fs.writeFile(testLargePath, largeImage);
  }

  async function cleanupTestFiles() {
    const testDir = path.join(__dirname, 'test-files');
    try {
      await fs.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Failed to cleanup test files:', error.message);
    }
  }
});
