const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const responseTime = require('response-time');
const { redis } = require('../config/database');

/**
 * Performance Optimization Middleware for High-Concurrency API
 * Implements caching, compression, rate limiting, and monitoring
 */

// Response Compression Middleware
const compressionMiddleware = compression({
  level: 6, // Balanced compression level
  threshold: 1024, // Only compress responses > 1KB
  filter: (req, res) => {
    // Don't compress if client doesn't support it
    if (req.headers['x-no-compression']) {
      return false;
    }
    
    // Compress all responses by default
    return compression.filter(req, res);
  }
});

// Security Headers Middleware
const securityMiddleware = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "https:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// Rate Limiting Configuration
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    
    // Use Redis for distributed rate limiting
    store: redis ? {
      incr: async (key) => {
        const current = await redis.incr(key);
        if (current === 1) {
          await redis.expire(key, Math.ceil(windowMs / 1000));
        }
        return { totalHits: current, resetTime: new Date(Date.now() + windowMs) };
      },
      decrement: async (key) => {
        const current = await redis.decr(key);
        return { totalHits: Math.max(0, current) };
      },
      resetKey: async (key) => {
        await redis.del(key);
      }
    } : undefined,
    
    // Skip rate limiting for health checks
    skip: (req) => {
      return req.path === '/health' || req.path === '/metrics';
    }
  });
};

// Different rate limits for different endpoints
const rateLimits = {
  // General API rate limit
  general: createRateLimit(
    15 * 60 * 1000, // 15 minutes
    1000, // 1000 requests per window
    'Too many requests, please try again later'
  ),
  
  // Authentication endpoints (stricter)
  auth: createRateLimit(
    15 * 60 * 1000, // 15 minutes
    10, // 10 attempts per window
    'Too many authentication attempts, please try again later'
  ),
  
  // Trading endpoints (moderate)
  trading: createRateLimit(
    1 * 60 * 1000, // 1 minute
    100, // 100 requests per minute
    'Trading rate limit exceeded, please slow down'
  ),
  
  // Wallet endpoints (moderate)
  wallet: createRateLimit(
    1 * 60 * 1000, // 1 minute
    50, // 50 requests per minute
    'Wallet rate limit exceeded, please slow down'
  )
};

// Slow Down Middleware (progressive delays)
const slowDownMiddleware = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 500, // Allow 500 requests per window at full speed
  delayMs: 100, // Add 100ms delay per request after delayAfter
  maxDelayMs: 5000, // Maximum delay of 5 seconds
  
  // Use Redis for distributed slow down
  store: redis ? {
    incr: async (key) => {
      const current = await redis.incr(key);
      if (current === 1) {
        await redis.expire(key, Math.ceil(15 * 60));
      }
      return { totalHits: current, resetTime: new Date(Date.now() + 15 * 60 * 1000) };
    },
    decrement: async (key) => {
      const current = await redis.decr(key);
      return { totalHits: Math.max(0, current) };
    },
    resetKey: async (key) => {
      await redis.del(key);
    }
  } : undefined
});

// Response Time Monitoring
const responseTimeMiddleware = responseTime((req, res, time) => {
  // Log slow requests
  if (time > 1000) {
    console.warn(`Slow request: ${req.method} ${req.path} - ${time.toFixed(2)}ms`);
  }
  
  // Add response time header
  res.setHeader('X-Response-Time', `${time.toFixed(2)}ms`);
  
  // Store metrics in Redis for monitoring
  if (redis) {
    const key = `metrics:response_time:${req.method}:${req.route?.path || req.path}`;
    redis.lpush(key, time).catch(err => console.error('Failed to store metrics:', err));
    redis.ltrim(key, 0, 99).catch(err => console.error('Failed to trim metrics:', err)); // Keep last 100 measurements
    redis.expire(key, 3600).catch(err => console.error('Failed to set metrics expiry:', err)); // 1 hour expiry
  }
});

// Cache Control Middleware
const cacheControlMiddleware = (req, res, next) => {
  // Set cache headers based on endpoint type
  if (req.path.startsWith('/api/public/')) {
    // Public data can be cached longer
    res.setHeader('Cache-Control', 'public, max-age=300'); // 5 minutes
  } else if (req.path.startsWith('/api/trading/offers')) {
    // Trading offers change frequently
    res.setHeader('Cache-Control', 'public, max-age=30'); // 30 seconds
  } else if (req.path.startsWith('/api/wallet/balance')) {
    // Wallet balances should not be cached
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  } else {
    // Default: short cache for API responses
    res.setHeader('Cache-Control', 'public, max-age=60'); // 1 minute
  }
  
  next();
};

// Request Size Limiting
const requestSizeLimitMiddleware = (req, res, next) => {
  const maxSize = 10 * 1024 * 1024; // 10MB limit
  
  if (req.headers['content-length'] && parseInt(req.headers['content-length']) > maxSize) {
    return res.status(413).json({
      error: 'Request entity too large',
      maxSize: '10MB'
    });
  }
  
  next();
};

// API Versioning Middleware
const apiVersioningMiddleware = (req, res, next) => {
  // Extract version from header or URL
  const version = req.headers['api-version'] || req.params.version || 'v1';
  
  // Validate version
  const supportedVersions = ['v1', 'v2'];
  if (!supportedVersions.includes(version)) {
    return res.status(400).json({
      error: 'Unsupported API version',
      supportedVersions
    });
  }
  
  req.apiVersion = version;
  res.setHeader('API-Version', version);
  
  next();
};

// Request ID Middleware for Tracing
const requestIdMiddleware = (req, res, next) => {
  const requestId = req.headers['x-request-id'] || 
                   `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  req.requestId = requestId;
  res.setHeader('X-Request-ID', requestId);
  
  next();
};

// Performance Metrics Collection
const metricsMiddleware = (req, res, next) => {
  const startTime = Date.now();
  
  // Override res.end to capture metrics
  const originalEnd = res.end;
  res.end = function(...args) {
    const duration = Date.now() - startTime;
    
    // Collect metrics
    const metrics = {
      method: req.method,
      path: req.route?.path || req.path,
      statusCode: res.statusCode,
      duration,
      timestamp: new Date().toISOString(),
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      requestId: req.requestId
    };
    
    // Store metrics asynchronously
    if (redis) {
      const metricsKey = `metrics:requests:${new Date().toISOString().split('T')[0]}`;
      redis.lpush(metricsKey, JSON.stringify(metrics))
        .catch(err => console.error('Failed to store request metrics:', err));
      redis.expire(metricsKey, 7 * 24 * 3600) // Keep for 7 days
        .catch(err => console.error('Failed to set metrics expiry:', err));
    }
    
    // Call original end method
    originalEnd.apply(this, args);
  };
  
  next();
};

// Error Handling Middleware
const errorHandlingMiddleware = (err, req, res, next) => {
  console.error(`Error in request ${req.requestId}:`, err);
  
  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const errorResponse = {
    error: 'Internal server error',
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  };
  
  if (isDevelopment) {
    errorResponse.details = err.message;
    errorResponse.stack = err.stack;
  }
  
  res.status(500).json(errorResponse);
};

// Apply rate limiting based on endpoint
const applyRateLimit = (req, res, next) => {
  if (req.path.startsWith('/api/auth/')) {
    return rateLimits.auth(req, res, next);
  } else if (req.path.startsWith('/api/trading/')) {
    return rateLimits.trading(req, res, next);
  } else if (req.path.startsWith('/api/wallet/')) {
    return rateLimits.wallet(req, res, next);
  } else {
    return rateLimits.general(req, res, next);
  }
};

module.exports = {
  // Individual middleware
  compression: compressionMiddleware,
  security: securityMiddleware,
  rateLimit: applyRateLimit,
  slowDown: slowDownMiddleware,
  responseTime: responseTimeMiddleware,
  cacheControl: cacheControlMiddleware,
  requestSizeLimit: requestSizeLimitMiddleware,
  apiVersioning: apiVersioningMiddleware,
  requestId: requestIdMiddleware,
  metrics: metricsMiddleware,
  errorHandling: errorHandlingMiddleware,
  
  // Combined middleware stack
  performanceStack: [
    requestIdMiddleware,
    securityMiddleware,
    compressionMiddleware,
    responseTimeMiddleware,
    requestSizeLimitMiddleware,
    cacheControlMiddleware,
    apiVersioningMiddleware,
    slowDownMiddleware,
    applyRateLimit,
    metricsMiddleware
  ],
  
  // Rate limit configurations
  rateLimits
};
