import { mutation, query } from "../_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";

// Helper function to hash passwords (in a real app, use bcrypt or similar)
// Note: In a production app, you'd use a proper password hashing library
function hashPassword(password: string): string {
  // This is a placeholder - in a real app, use a proper hashing function
  return password + "_hashed";
}

// Helper function to verify passwords
function verifyPassword(password: string, hashedPassword: string): boolean {
  // This is a placeholder - in a real app, use a proper verification function
  return hashedPassword === password + "_hashed";
}

// Register a new user
export const register = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    name: v.string(),
  },
  returns: v.object({
    success: v.boolean(),
    user: v.union(v.null(), v.object({
      _id: v.string(),
      email: v.string(),
      name: v.string(),
      profileComplete: v.boolean(),
      kycVerified: v.boolean(),
      twoFactorEnabled: v.boolean(),
    })),
    error: v.union(v.null(), v.string()),
  }),
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (existingUser) {
      return {
        success: false,
        user: null,
        error: "User with this email already exists",
      };
    }

    // Hash the password
    const hashedPassword = hashPassword(args.password);

    // Create the user
    const userId = await ctx.db.insert("users", {
      email: args.email,
      password: hashedPassword,
      name: args.name,
      profileComplete: false,
      kycVerified: false,
      twoFactorEnabled: false,
      paymentMethods: [],
      walletAddresses: [],
    });

    // Create initial wallets for the user (BTC, ETH)
    await ctx.db.insert("wallets", {
      userId,
      currency: "BTC",
      balance: 0,
      pendingBalance: 0,
    });

    await ctx.db.insert("wallets", {
      userId,
      currency: "ETH",
      balance: 0,
      pendingBalance: 0,
    });

    // Get the created user
    const user = await ctx.db.get(userId);

    if (!user) {
      return {
        success: false,
        user: null,
        error: "Failed to create user",
      };
    }

    // Return the user without the password
    return {
      success: true,
      user: {
        _id: userId,
        email: user.email,
        name: user.name,
        profileComplete: user.profileComplete,
        kycVerified: user.kycVerified,
        twoFactorEnabled: user.twoFactorEnabled,
      },
      error: null,
    };
  },
});

// Login a user
export const login = mutation({
  args: {
    email: v.string(),
    password: v.string(),
  },
  returns: v.object({
    success: v.boolean(),
    user: v.union(v.null(), v.object({
      _id: v.string(),
      email: v.string(),
      name: v.string(),
      profileComplete: v.boolean(),
      kycVerified: v.boolean(),
      twoFactorEnabled: v.boolean(),
    })),
    error: v.union(v.null(), v.string()),
  }),
  handler: async (ctx, args) => {
    // Find the user
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (!user) {
      return {
        success: false,
        user: null,
        error: "Invalid email or password",
      };
    }

    // Verify the password
    if (!verifyPassword(args.password, user.password)) {
      return {
        success: false,
        user: null,
        error: "Invalid email or password",
      };
    }

    // Return the user without the password
    return {
      success: true,
      user: {
        _id: user._id,
        email: user.email,
        name: user.name,
        profileComplete: user.profileComplete,
        kycVerified: user.kycVerified,
        twoFactorEnabled: user.twoFactorEnabled,
      },
      error: null,
    };
  },
});

// Logout a user (in a real app, this would invalidate tokens)
export const logout = mutation({
  args: {},
  returns: v.boolean(),
  handler: async (ctx, args) => {
    // In a real app, this would invalidate the user's session/token
    return true;
  },
});

// Get the current user
export const getCurrentUser = query({
  args: {},
  returns: v.union(v.null(), v.object({
    _id: v.string(),
    email: v.string(),
    name: v.string(),
    profileComplete: v.boolean(),
    kycVerified: v.boolean(),
    twoFactorEnabled: v.boolean(),
  })),
  handler: async (ctx, args) => {
    // In a real app, this would use the session/token to identify the user
    // For now, we'll return null to simulate no authenticated user
    return null;
  },
});

// Update user profile
export const updateUser = mutation({
  args: {
    name: v.optional(v.string()),
    profileComplete: v.optional(v.boolean()),
    kycVerified: v.optional(v.boolean()),
    twoFactorEnabled: v.optional(v.boolean()),
  },
  returns: v.object({
    success: v.boolean(),
    error: v.union(v.null(), v.string()),
  }),
  handler: async (ctx, args) => {
    // In a real app, this would get the user ID from the session/token
    // For now, we'll throw an error
    throw new ConvexError("Not implemented: No authentication system in place");
  },
});