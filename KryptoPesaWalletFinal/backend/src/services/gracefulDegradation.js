/**
 * Graceful Degradation Service
 * Provides fallback functionality when services are unavailable
 */

const logger = require('../utils/logger');
const { circuitBreakers } = require('../utils/circuitBreaker');
const { notificationService } = require('./notificationService');
const { realTimeScalabilityService } = require('./realTimeScalabilityService');

class GracefulDegradationService {
  constructor() {
    this.serviceStatus = {
      redis: 'healthy',
      blockchain: 'healthy',
      database: 'healthy',
      external: 'healthy'
    };

    this.fallbackData = new Map();
    this.degradationLevel = 'none'; // none, partial, severe
    this.lastHealthCheck = Date.now();
    this.previousDegradationLevel = 'none';

    // Communication settings
    this.communicationConfig = {
      broadcastStatusChanges: true,
      notifyAdmins: true,
      notifyUsers: true,
      alertThresholds: {
        partial: 30000,    // 30 seconds before notifying users
        severe: 10000      // 10 seconds before notifying users
      }
    };

    // Track degradation events
    this.degradationHistory = [];
    this.activeAlerts = new Map();
    this.lastNotificationTime = new Map();

    this.startHealthMonitoring();
  }

  /**
   * Check overall system health and adjust degradation level
   */
  async checkSystemHealth() {
    const healthChecks = {
      redis: await this.checkRedisHealth(),
      blockchain: await this.checkBlockchainHealth(),
      database: await this.checkDatabaseHealth(),
      external: await this.checkExternalServicesHealth()
    };

    // Update service status
    this.serviceStatus = healthChecks;
    
    // Determine degradation level
    const unhealthyServices = Object.values(healthChecks).filter(status => status !== 'healthy').length;
    
    if (unhealthyServices === 0) {
      this.degradationLevel = 'none';
    } else if (unhealthyServices <= 2) {
      this.degradationLevel = 'partial';
    } else {
      this.degradationLevel = 'severe';
    }

    this.lastHealthCheck = Date.now();

    // Check if degradation level changed
    if (this.degradationLevel !== this.previousDegradationLevel) {
      await this.handleDegradationLevelChange(this.previousDegradationLevel, this.degradationLevel);
      this.previousDegradationLevel = this.degradationLevel;
    }

    if (this.degradationLevel !== 'none') {
      logger.warn('System degradation detected:', {
        level: this.degradationLevel,
        unhealthyServices,
        serviceStatus: this.serviceStatus
      });
    }

    return {
      degradationLevel: this.degradationLevel,
      serviceStatus: this.serviceStatus,
      lastCheck: this.lastHealthCheck
    };
  }

  /**
   * Get cached data with fallback
   */
  async getCachedData(key, fetchFunction, ttl = 300000) {
    // Try Redis first
    if (this.serviceStatus.redis === 'healthy') {
      try {
        const { getCache } = require('../config/redis');
        const cached = await getCache(key);
        if (cached) {
          return JSON.parse(cached);
        }
      } catch (error) {
        logger.warn('Redis cache failed, using memory fallback:', error.message);
      }
    }

    // Use memory fallback
    const memoryData = this.fallbackData.get(key);
    if (memoryData && Date.now() - memoryData.timestamp < ttl) {
      logger.debug(`Using memory cache for key: ${key}`);
      return memoryData.data;
    }

    // Fetch fresh data
    try {
      const data = await fetchFunction();
      
      // Store in memory fallback
      this.fallbackData.set(key, {
        data,
        timestamp: Date.now()
      });

      // Try to store in Redis if available
      if (this.serviceStatus.redis === 'healthy') {
        try {
          const { setCache } = require('../config/redis');
          await setCache(key, data, ttl / 1000);
        } catch (error) {
          logger.debug('Failed to cache in Redis:', error.message);
        }
      }

      return data;
    } catch (error) {
      // Return stale data if available
      if (memoryData) {
        logger.warn(`Returning stale data for key ${key} due to fetch error:`, error.message);
        return memoryData.data;
      }
      throw error;
    }
  }

  /**
   * Execute database operation with fallback
   */
  async executeDatabaseOperation(operation, fallbackData = null) {
    if (this.serviceStatus.database === 'healthy') {
      try {
        return await operation();
      } catch (error) {
        logger.warn('Database operation failed, checking for fallback:', error.message);
        
        if (fallbackData) {
          logger.info('Using fallback data for database operation');
          return fallbackData;
        }
        throw error;
      }
    } else {
      if (fallbackData) {
        logger.warn('Database unhealthy, using fallback data');
        return fallbackData;
      }
      throw new Error('Database unavailable and no fallback data provided');
    }
  }

  /**
   * Execute blockchain operation with fallback
   */
  async executeBlockchainOperation(operation, fallbackResponse = null) {
    if (this.serviceStatus.blockchain === 'healthy') {
      try {
        return await circuitBreakers.blockchain.execute(operation);
      } catch (error) {
        logger.warn('Blockchain operation failed:', error.message);
        
        if (fallbackResponse) {
          logger.info('Using fallback response for blockchain operation');
          return fallbackResponse;
        }
        throw error;
      }
    } else {
      if (fallbackResponse) {
        logger.warn('Blockchain unhealthy, using fallback response');
        return fallbackResponse;
      }
      throw new Error('Blockchain unavailable and no fallback response provided');
    }
  }

  /**
   * Get degraded feature set based on current system health
   */
  getAvailableFeatures() {
    const features = {
      trading: true,
      walletOperations: true,
      realTimeUpdates: true,
      notifications: true,
      analytics: true,
      reporting: true
    };

    switch (this.degradationLevel) {
      case 'partial':
        features.realTimeUpdates = this.serviceStatus.redis === 'healthy';
        features.analytics = false; // Disable non-critical features
        break;
        
      case 'severe':
        features.realTimeUpdates = false;
        features.notifications = false;
        features.analytics = false;
        features.reporting = false;
        features.walletOperations = this.serviceStatus.blockchain === 'healthy';
        break;
    }

    return features;
  }

  /**
   * Get user-friendly status message
   */
  getStatusMessage() {
    switch (this.degradationLevel) {
      case 'none':
        return 'All systems operational';
      case 'partial':
        return 'Some features may be temporarily limited';
      case 'severe':
        return 'Limited functionality available - core trading features only';
      default:
        return 'System status unknown';
    }
  }

  /**
   * Health check methods
   */
  async checkRedisHealth() {
    try {
      const { getRedisClient } = require('../config/redis');
      const client = getRedisClient();
      
      if (!client || !client.isReady) {
        return 'unhealthy';
      }
      
      await client.ping();
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  async checkBlockchainHealth() {
    try {
      // Check circuit breaker state
      const blockchainCB = circuitBreakers.blockchain;
      if (blockchainCB.getState().state === 'OPEN') {
        return 'unhealthy';
      }
      
      // Simple health check - get latest block
      const ethereumService = require('./blockchain/ethereumService');
      const provider = ethereumService.getProvider('polygon');
      await provider.getBlockNumber();
      
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  async checkDatabaseHealth() {
    try {
      const mongoose = require('mongoose');
      
      if (mongoose.connection.readyState !== 1) {
        return 'unhealthy';
      }
      
      // Simple ping test
      await mongoose.connection.db.admin().ping();
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  async checkExternalServicesHealth() {
    // For now, assume external services are healthy
    // In production, implement actual health checks for external APIs
    return 'healthy';
  }

  /**
   * Start background health monitoring
   */
  startHealthMonitoring() {
    // Check health every 30 seconds
    setInterval(async () => {
      try {
        await this.checkSystemHealth();
      } catch (error) {
        logger.error('Health monitoring error:', error);
      }
    }, 30000);

    // Clean up old fallback data every 5 minutes
    setInterval(() => {
      this.cleanupFallbackData();
    }, 300000);

    logger.info('Graceful degradation health monitoring started');
  }

  /**
   * Clean up expired fallback data
   */
  cleanupFallbackData() {
    const now = Date.now();
    const maxAge = 3600000; // 1 hour
    
    for (const [key, data] of this.fallbackData.entries()) {
      if (now - data.timestamp > maxAge) {
        this.fallbackData.delete(key);
      }
    }
  }

  /**
   * Force degradation level (for testing)
   */
  forceDegradationLevel(level) {
    this.degradationLevel = level;
    logger.warn(`Degradation level manually set to: ${level}`);
  }

  /**
   * Handle degradation level changes and trigger communications
   */
  async handleDegradationLevelChange(previousLevel, newLevel) {
    try {
      const timestamp = new Date();

      // Record degradation event
      const degradationEvent = {
        timestamp,
        previousLevel,
        newLevel,
        serviceStatus: { ...this.serviceStatus },
        affectedFeatures: this.getAffectedFeatures(previousLevel, newLevel)
      };

      this.degradationHistory.push(degradationEvent);

      // Keep only last 100 events
      if (this.degradationHistory.length > 100) {
        this.degradationHistory.shift();
      }

      logger.info('Degradation level changed:', {
        from: previousLevel,
        to: newLevel,
        affectedFeatures: degradationEvent.affectedFeatures
      });

      // Broadcast status change
      if (this.communicationConfig.broadcastStatusChanges) {
        await this.broadcastStatusChange(degradationEvent);
      }

      // Send admin notifications
      if (this.communicationConfig.notifyAdmins) {
        await this.notifyAdmins(degradationEvent);
      }

      // Send user notifications (with throttling)
      if (this.communicationConfig.notifyUsers) {
        await this.notifyUsers(degradationEvent);
      }

      // Update active alerts
      await this.updateActiveAlerts(degradationEvent);

    } catch (error) {
      logger.error('Failed to handle degradation level change:', error);
    }
  }

  /**
   * Get features affected by degradation level change
   */
  getAffectedFeatures(previousLevel, newLevel) {
    const previousFeatures = this.getFeaturesForLevel(previousLevel);
    const newFeatures = this.getFeaturesForLevel(newLevel);

    const affected = {
      disabled: [],
      enabled: [],
      limited: []
    };

    for (const [feature, wasEnabled] of Object.entries(previousFeatures)) {
      const isEnabled = newFeatures[feature];

      if (wasEnabled && !isEnabled) {
        affected.disabled.push(feature);
      } else if (!wasEnabled && isEnabled) {
        affected.enabled.push(feature);
      } else if (wasEnabled && isEnabled && newLevel !== 'none') {
        affected.limited.push(feature);
      }
    }

    return affected;
  }

  /**
   * Get features for specific degradation level
   */
  getFeaturesForLevel(level) {
    const features = {
      trading: true,
      walletOperations: true,
      realTimeUpdates: true,
      notifications: true,
      analytics: true,
      reporting: true
    };

    switch (level) {
      case 'partial':
        features.realTimeUpdates = this.serviceStatus.redis === 'healthy';
        features.analytics = false;
        break;

      case 'severe':
        features.realTimeUpdates = false;
        features.notifications = false;
        features.analytics = false;
        features.reporting = false;
        features.walletOperations = this.serviceStatus.blockchain === 'healthy';
        break;
    }

    return features;
  }

  /**
   * Broadcast status change to all connected users
   */
  async broadcastStatusChange(degradationEvent) {
    try {
      const statusUpdate = {
        type: 'system_status_update',
        degradationLevel: degradationEvent.newLevel,
        previousLevel: degradationEvent.previousLevel,
        statusMessage: this.getStatusMessage(),
        availableFeatures: this.getAvailableFeatures(),
        affectedFeatures: degradationEvent.affectedFeatures,
        timestamp: degradationEvent.timestamp,
        severity: this.getDegradationSeverity(degradationEvent.newLevel)
      };

      // Broadcast via real-time service
      if (realTimeScalabilityService) {
        await realTimeScalabilityService.broadcast('system_status_update', statusUpdate);
      }

      logger.info('Broadcasted system status update to all users');
    } catch (error) {
      logger.error('Failed to broadcast status change:', error);
    }
  }

  /**
   * Send notifications to admin users
   */
  async notifyAdmins(degradationEvent) {
    try {
      const { newLevel, previousLevel, affectedFeatures } = degradationEvent;

      // Only notify for degradation (not recovery)
      if (this.getDegradationSeverity(newLevel) <= this.getDegradationSeverity(previousLevel)) {
        return;
      }

      const notification = {
        type: 'system_degradation_alert',
        title: `System Degradation Alert - ${newLevel.toUpperCase()}`,
        message: this.getAdminNotificationMessage(degradationEvent),
        priority: newLevel === 'severe' ? 'high' : 'medium',
        data: {
          degradationLevel: newLevel,
          previousLevel,
          affectedFeatures,
          serviceStatus: this.serviceStatus,
          timestamp: degradationEvent.timestamp
        }
      };

      // Send to all admin users via real-time
      if (realTimeScalabilityService) {
        await realTimeScalabilityService.publishEvent('admin', 'admin_alert', notification);
      }

      logger.info(`Sent degradation alert to admins: ${newLevel}`);
    } catch (error) {
      logger.error('Failed to notify admins:', error);
    }
  }

  /**
   * Send notifications to users (with throttling)
   */
  async notifyUsers(degradationEvent) {
    try {
      const { newLevel, affectedFeatures } = degradationEvent;

      // Check throttling
      const lastNotified = this.lastNotificationTime.get('users') || 0;
      const threshold = this.communicationConfig.alertThresholds[newLevel] || 60000;

      if (Date.now() - lastNotified < threshold) {
        logger.debug('User notification throttled');
        return;
      }

      // Only notify users for significant degradation
      if (newLevel === 'none') {
        return;
      }

      const notification = {
        type: 'system_status_notification',
        title: 'Service Status Update',
        message: this.getUserNotificationMessage(degradationEvent),
        priority: newLevel === 'severe' ? 'high' : 'normal',
        data: {
          degradationLevel: newLevel,
          affectedFeatures: affectedFeatures.disabled,
          statusMessage: this.getStatusMessage(),
          timestamp: degradationEvent.timestamp
        }
      };

      // Broadcast to all users
      if (realTimeScalabilityService) {
        await realTimeScalabilityService.broadcast('system_notification', notification);
      }

      this.lastNotificationTime.set('users', Date.now());
      logger.info(`Sent degradation notification to users: ${newLevel}`);
    } catch (error) {
      logger.error('Failed to notify users:', error);
    }
  }

  /**
   * Get current system status
   */
  getSystemStatus() {
    return {
      degradationLevel: this.degradationLevel,
      serviceStatus: this.serviceStatus,
      availableFeatures: this.getAvailableFeatures(),
      statusMessage: this.getStatusMessage(),
      lastHealthCheck: this.lastHealthCheck,
      activeAlerts: Array.from(this.activeAlerts.values()),
      degradationHistory: this.degradationHistory.slice(-10) // Last 10 events
    };
  }

  /**
   * Update active alerts based on degradation event
   */
  async updateActiveAlerts(degradationEvent) {
    const { newLevel, timestamp } = degradationEvent;

    if (newLevel === 'none') {
      // Clear all alerts when system is healthy
      this.activeAlerts.clear();
    } else {
      // Add or update alert
      const alertId = `degradation_${newLevel}`;
      this.activeAlerts.set(alertId, {
        id: alertId,
        type: 'system_degradation',
        level: newLevel,
        message: this.getStatusMessage(),
        timestamp,
        acknowledged: false
      });
    }
  }

  /**
   * Get degradation severity as numeric value
   */
  getDegradationSeverity(level) {
    const severityMap = {
      'none': 0,
      'partial': 1,
      'severe': 2
    };
    return severityMap[level] || 0;
  }

  /**
   * Get admin notification message
   */
  getAdminNotificationMessage(degradationEvent) {
    const { newLevel, affectedFeatures, serviceStatus } = degradationEvent;

    let message = `System degradation level changed to ${newLevel.toUpperCase()}.`;

    if (affectedFeatures.disabled.length > 0) {
      message += ` Disabled features: ${affectedFeatures.disabled.join(', ')}.`;
    }

    const unhealthyServices = Object.entries(serviceStatus)
      .filter(([, status]) => status !== 'healthy')
      .map(([service]) => service);

    if (unhealthyServices.length > 0) {
      message += ` Affected services: ${unhealthyServices.join(', ')}.`;
    }

    return message;
  }

  /**
   * Get user notification message
   */
  getUserNotificationMessage(degradationEvent) {
    const { newLevel, affectedFeatures } = degradationEvent;

    let message = '';

    switch (newLevel) {
      case 'partial':
        message = 'Some features may be temporarily limited due to system maintenance.';
        break;
      case 'severe':
        message = 'Limited functionality available. Core trading features remain operational.';
        break;
      default:
        message = 'System status has been updated.';
    }

    if (affectedFeatures.disabled.length > 0) {
      const friendlyFeatures = affectedFeatures.disabled.map(feature => {
        const featureMap = {
          'realTimeUpdates': 'real-time updates',
          'notifications': 'notifications',
          'analytics': 'analytics',
          'reporting': 'reports',
          'walletOperations': 'wallet operations'
        };
        return featureMap[feature] || feature;
      });

      message += ` Temporarily unavailable: ${friendlyFeatures.join(', ')}.`;
    }

    return message;
  }

  /**
   * Acknowledge alert (for admin use)
   */
  acknowledgeAlert(alertId, adminId) {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedBy = adminId;
      alert.acknowledgedAt = new Date();

      logger.info(`Alert ${alertId} acknowledged by admin ${adminId}`);
      return true;
    }
    return false;
  }

  /**
   * Get communication configuration
   */
  getCommunicationConfig() {
    return { ...this.communicationConfig };
  }

  /**
   * Update communication configuration
   */
  updateCommunicationConfig(config) {
    this.communicationConfig = { ...this.communicationConfig, ...config };
    logger.info('Communication configuration updated:', config);
  }

  /**
   * Get degradation statistics
   */
  getDegradationStatistics() {
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    const recentEvents = this.degradationHistory.filter(
      event => event.timestamp.getTime() > last24Hours
    );

    const stats = {
      totalEvents: this.degradationHistory.length,
      recentEvents: recentEvents.length,
      currentLevel: this.degradationLevel,
      activeAlerts: this.activeAlerts.size,
      uptimePercentage: this.calculateUptimePercentage(),
      degradationFrequency: this.calculateDegradationFrequency()
    };

    return stats;
  }

  /**
   * Calculate system uptime percentage
   */
  calculateUptimePercentage() {
    if (this.degradationHistory.length === 0) {
      return 100;
    }

    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    let totalDowntime = 0;
    let lastEventTime = last24Hours;
    let wasHealthy = true;

    for (const event of this.degradationHistory) {
      const eventTime = event.timestamp.getTime();

      if (eventTime < last24Hours) continue;

      if (!wasHealthy) {
        totalDowntime += eventTime - lastEventTime;
      }

      wasHealthy = event.newLevel === 'none';
      lastEventTime = eventTime;
    }

    // Add current downtime if system is degraded
    if (!wasHealthy) {
      totalDowntime += now - lastEventTime;
    }

    const uptime = ((24 * 60 * 60 * 1000 - totalDowntime) / (24 * 60 * 60 * 1000)) * 100;
    return Math.max(0, Math.min(100, uptime));
  }

  /**
   * Calculate degradation frequency
   */
  calculateDegradationFrequency() {
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    const recentDegradations = this.degradationHistory.filter(
      event => event.timestamp.getTime() > last24Hours && event.newLevel !== 'none'
    );

    return recentDegradations.length;
  }

  /**
   * Force degradation level (for testing/admin override)
   */
  async forceDegradationLevel(level) {
    const previousLevel = this.degradationLevel;
    this.degradationLevel = level;

    logger.warn(`Degradation level manually forced to: ${level}`, {
      previousLevel,
      newLevel: level,
      forced: true
    });

    // Trigger communication if level changed
    if (previousLevel !== level) {
      await this.handleDegradationLevelChange(previousLevel, level);
      this.previousDegradationLevel = level;
    }
  }
}

// Create singleton instance
const gracefulDegradation = new GracefulDegradationService();

module.exports = {
  GracefulDegradationService,
  gracefulDegradation
};
