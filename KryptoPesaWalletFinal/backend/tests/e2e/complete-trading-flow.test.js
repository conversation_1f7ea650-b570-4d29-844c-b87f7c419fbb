/**
 * End-to-End Tests for Complete Trading Flow
 * Tests the entire user journey from registration to trade completion
 */

const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../src/server');
const User = require('../../src/models/User');
const Offer = require('../../src/models/Offer');
const Trade = require('../../src/models/Trade');
const Chat = require('../../src/models/Chat');

describe('Complete Trading Flow E2E Tests', () => {
  let sellerToken, buyerToken;
  let sellerId, buyerId;
  let offerId, tradeId;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/kryptopesa_test');
  });

  beforeEach(async () => {
    // Clean database
    await User.deleteMany({});
    await Offer.deleteMany({});
    await Trade.deleteMany({});
    await Chat.deleteMany({});
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('User Registration and Authentication Flow', () => {
    test('Should register seller and buyer successfully', async () => {
      // Register seller
      const sellerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testseller',
          password: 'TestPass123!',
          profile: {
            firstName: 'John',
            lastName: 'Seller',
            country: 'KE',
            phone: '+254700000001'
          }
        });

      expect(sellerResponse.status).toBe(201);
      expect(sellerResponse.body.success).toBe(true);
      sellerToken = sellerResponse.body.data.token;
      sellerId = sellerResponse.body.data.user._id;

      // Register buyer
      const buyerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testbuyer',
          password: 'TestPass123!',
          profile: {
            firstName: 'Jane',
            lastName: 'Buyer',
            country: 'KE',
            phone: '+254700000002'
          }
        });

      expect(buyerResponse.status).toBe(201);
      expect(buyerResponse.body.success).toBe(true);
      buyerToken = buyerResponse.body.data.token;
      buyerId = buyerResponse.body.data.user._id;
    });

    test('Should login users successfully', async () => {
      // Create users first
      await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testseller',
          password: 'TestPass123!',
          profile: { firstName: 'John', lastName: 'Seller', country: 'KE' }
        });

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'TestPass123!'
        });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.data.token).toBeDefined();
    });
  });

  describe('Offer Creation and Management Flow', () => {
    beforeEach(async () => {
      // Setup users
      const sellerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testseller',
          password: 'TestPass123!',
          profile: { firstName: 'John', lastName: 'Seller', country: 'KE' }
        });
      sellerToken = sellerResponse.body.data.token;
    });

    test('Should create sell offer successfully', async () => {
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          type: 'sell',
          cryptocurrency: {
            symbol: 'USDT',
            network: 'polygon',
            minAmount: 10,
            maxAmount: 1000,
            availableAmount: 500
          },
          fiat: {
            currency: 'KES',
            priceType: 'fixed',
            fixedPrice: 150
          },
          paymentMethods: [{
            method: 'mobile_money',
            details: {
              provider: 'M-Pesa',
              mobileNumber: '+254700000001'
            }
          }],
          terms: {
            timeLimit: 30,
            instructions: 'Please follow payment instructions carefully'
          }
        });

      expect(offerResponse.status).toBe(201);
      expect(offerResponse.body.success).toBe(true);
      offerId = offerResponse.body.data.offer.offerId;
    });

    test('Should retrieve offers with filters', async () => {
      // Create offer first
      await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          type: 'sell',
          cryptocurrency: { symbol: 'USDT', network: 'polygon', minAmount: 10, maxAmount: 1000, availableAmount: 500 },
          fiat: { currency: 'KES', priceType: 'fixed', fixedPrice: 150 },
          paymentMethods: [{ method: 'mobile_money', details: { provider: 'M-Pesa' } }],
          terms: { timeLimit: 30 }
        });

      const offersResponse = await request(app)
        .get('/api/offers?type=sell&cryptocurrency=USDT&fiatCurrency=KES')
        .expect(200);

      expect(offersResponse.body.success).toBe(true);
      expect(offersResponse.body.data.offers).toHaveLength(1);
    });
  });

  describe('Complete Trading Flow', () => {
    beforeEach(async () => {
      // Setup seller and buyer
      const sellerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testseller',
          password: 'TestPass123!',
          profile: { firstName: 'John', lastName: 'Seller', country: 'KE' }
        });
      sellerToken = sellerResponse.body.data.token;

      const buyerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testbuyer',
          password: 'TestPass123!',
          profile: { firstName: 'Jane', lastName: 'Buyer', country: 'KE' }
        });
      buyerToken = buyerResponse.body.data.token;

      // Create offer
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          type: 'sell',
          cryptocurrency: { symbol: 'USDT', network: 'polygon', minAmount: 10, maxAmount: 1000, availableAmount: 500 },
          fiat: { currency: 'KES', priceType: 'fixed', fixedPrice: 150 },
          paymentMethods: [{ method: 'mobile_money', details: { provider: 'M-Pesa', mobileNumber: '+254700000001' } }],
          terms: { timeLimit: 30 }
        });
      offerId = offerResponse.body.data.offer.offerId;
    });

    test('Should complete full trading workflow', async () => {
      // Step 1: Buyer creates trade from offer
      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          offerId: offerId,
          amount: 100,
          paymentMethod: 'mobile_money'
        });

      expect(tradeResponse.status).toBe(201);
      expect(tradeResponse.body.success).toBe(true);
      tradeId = tradeResponse.body.data.trade.tradeId;

      // Step 2: Verify trade details
      const tradeDetailsResponse = await request(app)
        .get(`/api/trades/${tradeId}`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .expect(200);

      expect(tradeDetailsResponse.body.data.trade.status).toBe('created');

      // Step 3: Seller funds the trade (simulated)
      const fundResponse = await request(app)
        .post(`/api/trades/${tradeId}/fund`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          transactionHash: '0x1234567890abcdef'
        });

      expect(fundResponse.status).toBe(200);

      // Step 4: Buyer marks payment as sent
      const paymentResponse = await request(app)
        .post(`/api/trades/${tradeId}/payment-sent`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          paymentReference: 'MPESA123456',
          notes: 'Payment sent via M-Pesa'
        });

      expect(paymentResponse.status).toBe(200);

      // Step 5: Seller confirms payment received
      const confirmResponse = await request(app)
        .post(`/api/trades/${tradeId}/payment-received`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          confirmed: true
        });

      expect(confirmResponse.status).toBe(200);

      // Step 6: Verify trade completion
      const finalTradeResponse = await request(app)
        .get(`/api/trades/${tradeId}`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .expect(200);

      expect(finalTradeResponse.body.data.trade.status).toBe('completed');
    });

    test('Should handle trade cancellation', async () => {
      // Create trade
      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          offerId: offerId,
          amount: 100,
          paymentMethod: 'mobile_money'
        });
      tradeId = tradeResponse.body.data.trade.tradeId;

      // Cancel trade
      const cancelResponse = await request(app)
        .post(`/api/trades/${tradeId}/cancel`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          reason: 'Changed my mind'
        });

      expect(cancelResponse.status).toBe(200);

      // Verify cancellation
      const tradeDetailsResponse = await request(app)
        .get(`/api/trades/${tradeId}`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .expect(200);

      expect(tradeDetailsResponse.body.data.trade.status).toBe('cancelled');
    });
  });

  describe('Chat and Communication Flow', () => {
    beforeEach(async () => {
      // Setup complete trading environment
      const sellerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testseller',
          password: 'TestPass123!',
          profile: { firstName: 'John', lastName: 'Seller', country: 'KE' }
        });
      sellerToken = sellerResponse.body.data.token;

      const buyerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testbuyer',
          password: 'TestPass123!',
          profile: { firstName: 'Jane', lastName: 'Buyer', country: 'KE' }
        });
      buyerToken = buyerResponse.body.data.token;

      // Create offer and trade
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          type: 'sell',
          cryptocurrency: { symbol: 'USDT', network: 'polygon', minAmount: 10, maxAmount: 1000, availableAmount: 500 },
          fiat: { currency: 'KES', priceType: 'fixed', fixedPrice: 150 },
          paymentMethods: [{ method: 'mobile_money', details: { provider: 'M-Pesa' } }],
          terms: { timeLimit: 30 }
        });

      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          offerId: offerResponse.body.data.offer.offerId,
          amount: 100,
          paymentMethod: 'mobile_money'
        });
      tradeId = tradeResponse.body.data.trade.tradeId;
    });

    test('Should send and receive chat messages', async () => {
      // Send message from buyer
      const messageResponse = await request(app)
        .post(`/api/chat/${tradeId}/messages`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          content: 'Hello, I am ready to proceed with the trade',
          type: 'text'
        });

      expect(messageResponse.status).toBe(201);

      // Get messages
      const messagesResponse = await request(app)
        .get(`/api/chat/${tradeId}/messages`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .expect(200);

      expect(messagesResponse.body.data.messages).toHaveLength(1);
      expect(messagesResponse.body.data.messages[0].content).toBe('Hello, I am ready to proceed with the trade');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('Should handle invalid trade creation', async () => {
      const buyerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testbuyer',
          password: 'TestPass123!',
          profile: { firstName: 'Jane', lastName: 'Buyer', country: 'KE' }
        });
      buyerToken = buyerResponse.body.data.token;

      // Try to create trade with invalid offer ID
      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          offerId: 'invalid-offer-id',
          amount: 100,
          paymentMethod: 'mobile_money'
        });

      expect(tradeResponse.status).toBe(404);
      expect(tradeResponse.body.success).toBe(false);
    });

    test('Should handle unauthorized access', async () => {
      const response = await request(app)
        .get('/api/trades')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });
});
