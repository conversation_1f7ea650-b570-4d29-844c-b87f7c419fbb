{"dashboard": {"id": null, "title": "KryptoPesa Production Dashboard", "tags": ["krypt<PERSON><PERSON>", "production", "monitoring"], "timezone": "Africa/Nairobi", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up{job=\"kryptopesa-backend\"}", "legendFormat": "API Status"}, {"expr": "up{job=\"mongodb\"}", "legendFormat": "Database Status"}, {"expr": "up{job=\"redis\"}", "legendFormat": "<PERSON>ache <PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "API Response Times", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "99th percentile"}], "yAxes": [{"label": "Response Time (seconds)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{method}} {{endpoint}}"}], "yAxes": [{"label": "Requests per second", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{status=~\"4..\"}[5m])", "legendFormat": "4xx errors"}, {"expr": "rate(http_requests_total{status=~\"5..\"}[5m])", "legendFormat": "5xx errors"}], "yAxes": [{"label": "Errors per second", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Trading Metrics", "type": "stat", "targets": [{"expr": "kryptopesa_active_trades", "legendFormat": "Active Trades"}, {"expr": "rate(kryptopesa_trades_total[1h]) * 3600", "legendFormat": "Trades/Hour"}, {"expr": "kryptopesa_trade_volume_usd", "legendFormat": "Volume (USD)"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "User Activity", "type": "graph", "targets": [{"expr": "kryptopesa_active_users", "legendFormat": "Active Users"}, {"expr": "kryptopesa_websocket_connections", "legendFormat": "WebSocket Connections"}], "yAxes": [{"label": "Count", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Database Performance", "type": "graph", "targets": [{"expr": "mongodb_op_latencies_histogram_micros{type=\"command\"}", "legendFormat": "Command Latency"}, {"expr": "mongodb_connections{state=\"current\"}", "legendFormat": "Active Connections"}], "yAxes": [{"label": "Latency (μs) / Connections", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "Security Metrics", "type": "graph", "targets": [{"expr": "rate(kryptopesa_rate_limit_violations_total[5m])", "legendFormat": "Rate Limit Violations"}, {"expr": "rate(kryptopesa_failed_logins_total[5m])", "legendFormat": "Failed <PERSON><PERSON>"}, {"expr": "rate(kryptopesa_security_validation_failures_total[5m])", "legendFormat": "Security Failures"}], "yAxes": [{"label": "Events per second", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "Infrastructure Metrics", "type": "graph", "targets": [{"expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "CPU Usage %"}, {"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "Memory Usage %"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 27, "version": 1}}