import React, { useState, useEffect } from 'react';
import {
const logger = require('./utils/logger');

  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Avatar,
  Tooltip,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Visibility as ViewIcon,
  Block as BlockIcon,
  CheckCircle as VerifyIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Star as StarIcon
} from '@mui/icons-material';
import axios from 'axios';

const UsersPage = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalUsers, setTotalUsers] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [verifiedFilter, setVerifiedFilter] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [userDetailOpen, setUserDetailOpen] = useState(false);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionType, setActionType] = useState('');
  const [actionReason, setActionReason] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Fetch users data
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        role: roleFilter || undefined,
        verified: verifiedFilter || undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      const response = await axios.get('/admin/users', { params });

      if (response.data.success) {
        setUsers(response.data.data.users);
        setTotalUsers(response.data.data.pagination.totalUsers);
      }
    } catch (error) {
      // logger.error('Error fetching users:', error);
      setError('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page, rowsPerPage, searchTerm, statusFilter, roleFilter, verifiedFilter]);

  // Handle search
  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    switch (filterType) {
      case 'status':
        setStatusFilter(value);
        break;
      case 'role':
        setRoleFilter(value);
        break;
      case 'verified':
        setVerifiedFilter(value);
        break;
    }
    setPage(0);
  };

  // Handle pagination
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle user actions
  const handleUserAction = async (user, action) => {
    setSelectedUser(user);
    setActionType(action);
    setActionDialogOpen(true);
  };

  const executeUserAction = async () => {
    try {
      let endpoint = '';
      let data = {};

      switch (actionType) {
        case 'suspend':
          endpoint = `/admin/users/${selectedUser._id}/status`;
          data = { status: 'suspended', reason: actionReason };
          break;
        case 'ban':
          endpoint = `/admin/users/${selectedUser._id}/status`;
          data = { status: 'banned', reason: actionReason };
          break;
        case 'activate':
          endpoint = `/admin/users/${selectedUser._id}/status`;
          data = { status: 'active', reason: actionReason };
          break;
        case 'verify':
          endpoint = `/admin/users/${selectedUser._id}/verify`;
          data = {
            documentType: 'national_id', // Default, should be from form
            approved: true,
            notes: actionReason
          };
          break;
      }

      const response = await axios.put(endpoint, data);

      if (response.data.success) {
        setSnackbar({
          open: true,
          message: `User ${actionType} successful`,
          severity: 'success'
        });
        fetchUsers(); // Refresh the list
      }
    } catch (error) {
      // logger.error('Error executing user action:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || `Failed to ${actionType} user`,
        severity: 'error'
      });
    } finally {
      setActionDialogOpen(false);
      setActionReason('');
    }
  };

  // Get status chip color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'suspended': return 'warning';
      case 'banned': return 'error';
      case 'pending': return 'default';
      default: return 'default';
    }
  };

  // Get role chip color
  const getRoleColor = (role) => {
    switch (role) {
      case 'admin': return 'error';
      case 'moderator': return 'warning';
      case 'user': return 'primary';
      default: return 'default';
    }
  };

  if (loading && users.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading users...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        User Management
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters and Search */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search users..."
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="suspended">Suspended</MenuItem>
                <MenuItem value="banned">Banned</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Role</InputLabel>
              <Select
                value={roleFilter}
                label="Role"
                onChange={(e) => handleFilterChange('role', e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="user">User</MenuItem>
                <MenuItem value="moderator">Moderator</MenuItem>
                <MenuItem value="admin">Admin</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Verified</InputLabel>
              <Select
                value={verifiedFilter}
                label="Verified"
                onChange={(e) => handleFilterChange('verified', e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="true">Verified</MenuItem>
                <MenuItem value="false">Unverified</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterIcon />}
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('');
                setRoleFilter('');
                setVerifiedFilter('');
                setPage(0);
              }}
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Users Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>User</TableCell>
              <TableCell>Contact</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Reputation</TableCell>
              <TableCell>Verified</TableCell>
              <TableCell>Joined</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user._id} hover>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar>
                      {user.profile?.avatar ? (
                        <img src={user.profile.avatar} alt={user.username} />
                      ) : (
                        <PersonIcon />
                      )}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2">
                        {user.profile?.firstName} {user.profile?.lastName}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        @{user.username}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>

                <TableCell>
                  <Box>
                    <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                      <EmailIcon fontSize="small" color="action" />
                      <Typography variant="body2">{user.email}</Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                      <PhoneIcon fontSize="small" color="action" />
                      <Typography variant="body2">{user.phone}</Typography>
                    </Box>
                  </Box>
                </TableCell>

                <TableCell>
                  <Chip
                    label={user.status}
                    color={getStatusColor(user.status)}
                    size="small"
                  />
                </TableCell>

                <TableCell>
                  <Chip
                    label={user.role}
                    color={getRoleColor(user.role)}
                    size="small"
                  />
                </TableCell>

                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    <StarIcon fontSize="small" color="warning" />
                    <Typography variant="body2">
                      {user.reputation?.score || 0}/100
                    </Typography>
                  </Box>
                  <Typography variant="caption" color="textSecondary">
                    {user.reputation?.totalTrades || 0} trades
                  </Typography>
                </TableCell>

                <TableCell>
                  <Box>
                    <Chip
                      label={user.verification?.identity?.verified ? 'ID Verified' : 'Unverified'}
                      color={user.verification?.identity?.verified ? 'success' : 'default'}
                      size="small"
                      sx={{ mb: 0.5 }}
                    />
                    {user.verification?.email?.verified && (
                      <Chip
                        label="Email"
                        color="info"
                        size="small"
                        sx={{ mr: 0.5 }}
                      />
                    )}
                    {user.verification?.phone?.verified && (
                      <Chip
                        label="Phone"
                        color="info"
                        size="small"
                      />
                    )}
                  </Box>
                </TableCell>

                <TableCell>
                  <Typography variant="body2">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Last active: {new Date(user.lastActive).toLocaleDateString()}
                  </Typography>
                </TableCell>

                <TableCell>
                  <Box display="flex" gap={1}>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSelectedUser(user);
                          setUserDetailOpen(true);
                        }}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>

                    {user.status === 'active' && (
                      <Tooltip title="Suspend User">
                        <IconButton
                          size="small"
                          color="warning"
                          onClick={() => handleUserAction(user, 'suspend')}
                        >
                          <BlockIcon />
                        </IconButton>
                      </Tooltip>
                    )}

                    {user.status !== 'banned' && (
                      <Tooltip title="Ban User">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleUserAction(user, 'ban')}
                        >
                          <BlockIcon />
                        </IconButton>
                      </Tooltip>
                    )}

                    {['suspended', 'banned'].includes(user.status) && (
                      <Tooltip title="Activate User">
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => handleUserAction(user, 'activate')}
                        >
                          <VerifyIcon />
                        </IconButton>
                      </Tooltip>
                    )}

                    {!user.verification?.identity?.verified && (
                      <Tooltip title="Verify Identity">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleUserAction(user, 'verify')}
                        >
                          <VerifyIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={totalUsers}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* User Detail Dialog */}
      <Dialog
        open={userDetailOpen}
        onClose={() => setUserDetailOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          User Details: {selectedUser?.username}
        </DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Profile Information
                    </Typography>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Name</Typography>
                      <Typography variant="body1">
                        {selectedUser.profile?.firstName} {selectedUser.profile?.lastName}
                      </Typography>
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Username</Typography>
                      <Typography variant="body1">@{selectedUser.username}</Typography>
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Email</Typography>
                      <Typography variant="body1">{selectedUser.email}</Typography>
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Phone</Typography>
                      <Typography variant="body1">{selectedUser.phone}</Typography>
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Location</Typography>
                      <Typography variant="body1">
                        {selectedUser.profile?.location?.city}, {selectedUser.profile?.location?.country}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Account Status
                    </Typography>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Status</Typography>
                      <Chip
                        label={selectedUser.status}
                        color={getStatusColor(selectedUser.status)}
                        size="small"
                      />
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Role</Typography>
                      <Chip
                        label={selectedUser.role}
                        color={getRoleColor(selectedUser.role)}
                        size="small"
                      />
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Reputation Score</Typography>
                      <Typography variant="body1">
                        {selectedUser.reputation?.score || 0}/100
                      </Typography>
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Total Trades</Typography>
                      <Typography variant="body1">
                        {selectedUser.reputation?.totalTrades || 0}
                      </Typography>
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Joined Date</Typography>
                      <Typography variant="body1">
                        {new Date(selectedUser.createdAt).toLocaleDateString()}
                      </Typography>
                    </Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary">Last Active</Typography>
                      <Typography variant="body1">
                        {new Date(selectedUser.lastActive).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Verification Status
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <Typography variant="body2" color="textSecondary">Email</Typography>
                          <Chip
                            label={selectedUser.verification?.email?.verified ? 'Verified' : 'Unverified'}
                            color={selectedUser.verification?.email?.verified ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <Typography variant="body2" color="textSecondary">Phone</Typography>
                          <Chip
                            label={selectedUser.verification?.phone?.verified ? 'Verified' : 'Unverified'}
                            color={selectedUser.verification?.phone?.verified ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <Typography variant="body2" color="textSecondary">Identity</Typography>
                          <Chip
                            label={selectedUser.verification?.identity?.verified ? 'Verified' : 'Unverified'}
                            color={selectedUser.verification?.identity?.verified ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUserDetailOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Action Confirmation Dialog */}
      <Dialog
        open={actionDialogOpen}
        onClose={() => setActionDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Confirm {actionType} User
        </DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Are you sure you want to {actionType} user "{selectedUser?.username}"?
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Reason (required)"
            value={actionReason}
            onChange={(e) => setActionReason(e.target.value)}
            margin="normal"
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setActionDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={executeUserAction}
            color="primary"
            variant="contained"
            disabled={!actionReason.trim()}
          >
            Confirm {actionType}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UsersPage;
