const mongoose = require('mongoose');

// Optimized Chat schema without embedded messages for better scalability
const chatSchema = new mongoose.Schema({
  trade: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trade',
    required: true,
    unique: true
  },
  participants: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['seller', 'buyer', 'admin'],
      required: true
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    lastSeen: {
      type: Date,
      default: Date.now
    },
    unreadCount: {
      type: Number,
      default: 0
    }
  }],
  status: {
    type: String,
    enum: ['active', 'archived', 'locked'],
    default: 'active'
  },
  metadata: {
    totalMessages: {
      type: Number,
      default: 0
    },
    lastActivity: {
      type: Date,
      default: Date.now
    },
    lastMessageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Message'
    },
    lastMessagePreview: {
      type: String,
      maxlength: 100
    },
    autoMessages: {
      welcomeSent: {
        type: Boolean,
        default: false
      },
      remindersSent: {
        type: Number,
        default: 0
      }
    }
  },
  settings: {
    allowAttachments: {
      type: Boolean,
      default: true
    },
    maxAttachmentSize: {
      type: Number,
      default: 10485760 // 10MB
    },
    autoArchiveAfterDays: {
      type: Number,
      default: 90
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Optimized indexes for scalability
chatSchema.index({ trade: 1 }, { unique: true });
chatSchema.index({ 'participants.user': 1 });
chatSchema.index({ status: 1, 'metadata.lastActivity': -1 });
chatSchema.index({ createdAt: -1 });
chatSchema.index({ updatedAt: -1 });

// Virtual for getting participant by user ID
chatSchema.virtual('getParticipant').get(function() {
  return (userId) => {
    return this.participants.find(p => p.user.toString() === userId.toString());
  };
});

// Method to update participant's last seen and unread count
chatSchema.methods.updateParticipantActivity = function(userId, unreadCount = null) {
  const participant = this.participants.find(p => p.user.toString() === userId.toString());
  if (participant) {
    participant.lastSeen = new Date();
    if (unreadCount !== null) {
      participant.unreadCount = unreadCount;
    }
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to increment unread count for other participants
chatSchema.methods.incrementUnreadForOthers = function(senderId) {
  let updated = false;
  this.participants.forEach(participant => {
    if (participant.user.toString() !== senderId.toString()) {
      participant.unreadCount += 1;
      updated = true;
    }
  });

  if (updated) {
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to reset unread count for user
chatSchema.methods.resetUnreadCount = function(userId) {
  const participant = this.participants.find(p => p.user.toString() === userId.toString());
  if (participant && participant.unreadCount > 0) {
    participant.unreadCount = 0;
    participant.lastSeen = new Date();
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to update metadata after new message
chatSchema.methods.updateAfterMessage = function(messageId, messagePreview) {
  this.metadata.totalMessages += 1;
  this.metadata.lastActivity = new Date();
  this.metadata.lastMessageId = messageId;
  this.metadata.lastMessagePreview = messagePreview ? messagePreview.substring(0, 100) : '';
  return this.save();
};

// Method to check if user is participant
chatSchema.methods.isParticipant = function(userId) {
  return this.participants.some(p => p.user.toString() === userId.toString());
};

// Method to get participant role
chatSchema.methods.getParticipantRole = function(userId) {
  const participant = this.participants.find(p => p.user.toString() === userId.toString());
  return participant ? participant.role : null;
};

// Method to get unread count for user
chatSchema.methods.getUnreadCount = function(userId) {
  const participant = this.participants.find(p => p.user.toString() === userId.toString());
  return participant ? participant.unreadCount : 0;
};

// Method to add participant (for admin joining)
chatSchema.methods.addParticipant = function(userId, role = 'admin') {
  if (!this.isParticipant(userId)) {
    this.participants.push({
      user: userId,
      role,
      joinedAt: new Date(),
      lastSeen: new Date(),
      unreadCount: 0
    });
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to archive chat
chatSchema.methods.archive = function() {
  this.status = 'archived';
  return this.save();
};

// Method to lock chat
chatSchema.methods.lock = function() {
  this.status = 'locked';
  return this.save();
};

// Static method to create chat for trade (with optional session support for transactions)
chatSchema.statics.createForTrade = function(tradeId, sellerId, buyerId, options = {}) {
  return this.create([{
    trade: tradeId,
    participants: [
      { user: sellerId, role: 'seller', unreadCount: 0 },
      { user: buyerId, role: 'buyer', unreadCount: 0 }
    ],
    status: 'active'
  }], options).then(chats => chats[0]); // Return first chat from array
};

// Static method to find chat by trade ID
chatSchema.statics.findByTrade = function(tradeId) {
  return this.findOne({ trade: tradeId }).populate('participants.user', 'username firstName lastName avatar isOnline');
};

// Static method to get user's active chats
chatSchema.statics.getUserChats = function(userId, options = {}) {
  const { limit = 20, offset = 0, status = 'active' } = options;

  return this.find({
    'participants.user': userId,
    status
  })
  .populate('participants.user', 'username firstName lastName avatar isOnline')
  .populate('trade', 'tradeId amount cryptocurrency status')
  .sort({ 'metadata.lastActivity': -1 })
  .limit(limit)
  .skip(offset)
  .lean();
};

// Static method to get chat statistics
chatSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalMessages: { $sum: '$metadata.totalMessages' }
      }
    }
  ]);
};

module.exports = mongoose.model('Chat', chatSchema);
