const rateLimitingService = require('../services/rateLimitingService');
const logger = require('../utils/logger');

/**
 * Production Rate Limiting Middleware
 * Integrates with rate limiting service for comprehensive protection
 */

// Main rate limiting middleware factory
const createProductionRateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100,
    type = 'general',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    enableBlacklist = true,
    enableWhitelist = true,
    enableMetrics = true
  } = options;

  return async (req, res, next) => {
    try {
      const ip = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent') || 'unknown';
      const endpoint = req.originalUrl;
      const method = req.method;
      const userId = req.user ? req.user._id.toString() : null;

      // Check blacklist first
      if (enableBlacklist && await rateLimitingService.isBlacklisted(ip)) {
        if (enableMetrics) {
          rateLimitingService.trackRequest(ip, userAgent, endpoint, true);
        }
        
        logger.warn('Request blocked - IP blacklisted', {
          ip, userAgent, endpoint, method, userId,
          timestamp: new Date().toISOString()
        });
        
        return res.status(429).json({
          success: false,
          error: 'Access denied',
          message: 'Your IP address has been temporarily blocked due to suspicious activity.',
          type: 'blacklisted',
          timestamp: new Date().toISOString()
        });
      }

      // Check whitelist
      if (enableWhitelist && await rateLimitingService.isWhitelisted(ip)) {
        if (enableMetrics) {
          rateLimitingService.trackRequest(ip, userAgent, endpoint, false);
        }
        return next(); // Skip rate limiting for whitelisted IPs
      }

      // Generate rate limit key
      const key = generateRateLimitKey(ip, userId, type);
      
      // Check current rate limit status
      const rateLimitStatus = await checkRateLimit(key, windowMs, max);
      
      // Track metrics
      if (enableMetrics) {
        rateLimitingService.trackRequest(ip, userAgent, endpoint, rateLimitStatus.blocked);
      }

      if (rateLimitStatus.blocked) {
        // Log rate limit violation
        logger.warn('Rate limit exceeded', {
          type, ip, userAgent, endpoint, method, userId,
          currentCount: rateLimitStatus.current,
          limit: max,
          windowMs,
          resetTime: rateLimitStatus.resetTime,
          timestamp: new Date().toISOString()
        });

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': max,
          'X-RateLimit-Remaining': Math.max(0, max - rateLimitStatus.current),
          'X-RateLimit-Reset': rateLimitStatus.resetTime,
          'X-RateLimit-Type': type,
          'Retry-After': Math.ceil(windowMs / 1000)
        });

        return res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          message: `Too many ${type} requests. Please try again later.`,
          retryAfter: Math.ceil(windowMs / 1000),
          limit: max,
          current: rateLimitStatus.current,
          resetTime: rateLimitStatus.resetTime,
          type,
          timestamp: new Date().toISOString()
        });
      }

      // Set success headers
      res.set({
        'X-RateLimit-Limit': max,
        'X-RateLimit-Remaining': Math.max(0, max - rateLimitStatus.current),
        'X-RateLimit-Reset': rateLimitStatus.resetTime,
        'X-RateLimit-Type': type
      });

      next();
    } catch (error) {
      logger.error('Rate limiting middleware error:', error);
      // Continue on error to avoid blocking legitimate requests
      next();
    }
  };
};

// Generate consistent rate limit key
const generateRateLimitKey = (ip, userId, type) => {
  const baseKey = ip;
  const userKey = userId || 'anonymous';
  return `rl:prod:${type}:${baseKey}:${userKey}`;
};

// Check rate limit using Redis
const checkRateLimit = async (key, windowMs, max) => {
  try {
    const redisClient = rateLimitingService.redisClient;
    if (!redisClient || !redisClient.isReady) {
      // Fallback to allowing request if Redis is unavailable
      return { blocked: false, current: 0, resetTime: new Date(Date.now() + windowMs) };
    }

    // Use Redis pipeline for atomic operations
    const pipeline = redisClient.pipeline();
    pipeline.incr(key);
    pipeline.expire(key, Math.ceil(windowMs / 1000));
    
    const results = await pipeline.exec();
    const current = results[0][1];
    const resetTime = new Date(Date.now() + windowMs);

    return {
      blocked: current > max,
      current,
      resetTime
    };
  } catch (error) {
    logger.error('Rate limit check error:', error);
    // Fallback to allowing request on error
    return { blocked: false, current: 0, resetTime: new Date(Date.now() + windowMs) };
  }
};

// Predefined production rate limiters
const productionRateLimiters = {
  // Authentication - very strict
  auth: createProductionRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_AUTH_MAX) || 5,
    type: 'auth',
    skipSuccessfulRequests: true,
    skipFailedRequests: false
  }),

  // Login - extremely strict
  login: createProductionRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_LOGIN_MAX) || 3,
    type: 'login',
    skipSuccessfulRequests: true,
    skipFailedRequests: false
  }),

  // Password reset - strict
  passwordReset: createProductionRateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: parseInt(process.env.RATE_LIMIT_PASSWORD_RESET_MAX) || 3,
    type: 'passwordReset',
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  }),

  // Trading - moderate
  trading: createProductionRateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_TRADING_MAX) || 10,
    type: 'trading',
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  }),

  // Trade creation - stricter
  tradeCreation: createProductionRateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: parseInt(process.env.RATE_LIMIT_TRADE_CREATION_MAX) || 5,
    type: 'tradeCreation',
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  }),

  // Wallet operations - strict
  wallet: createProductionRateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: parseInt(process.env.RATE_LIMIT_WALLET_MAX) || 20,
    type: 'wallet',
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  }),

  // Wallet transactions - very strict
  walletTransaction: createProductionRateLimit({
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: parseInt(process.env.RATE_LIMIT_WALLET_TX_MAX) || 5,
    type: 'walletTransaction',
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  }),

  // Chat - moderate
  chat: createProductionRateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_CHAT_MAX) || 30,
    type: 'chat',
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  }),

  // File uploads - strict
  fileUpload: createProductionRateLimit({
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: parseInt(process.env.RATE_LIMIT_FILE_UPLOAD_MAX) || 10,
    type: 'fileUpload',
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  }),

  // Admin operations - strict
  admin: createProductionRateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: parseInt(process.env.RATE_LIMIT_ADMIN_MAX) || 50,
    type: 'admin',
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  }),

  // General API - lenient
  general: createProductionRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_GENERAL_MAX) || 1000,
    type: 'general',
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  }),

  // Public endpoints - very lenient
  public: createProductionRateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_PUBLIC_MAX) || 100,
    type: 'public',
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  }),

  // Health checks - very lenient
  health: createProductionRateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_HEALTH_MAX) || 200,
    type: 'health',
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  })
};

// Smart rate limiter that applies appropriate limits based on endpoint
const smartRateLimit = (req, res, next) => {
  const path = req.path.toLowerCase();
  const method = req.method.toLowerCase();

  // Determine appropriate rate limiter based on endpoint
  if (path.includes('/auth/login')) {
    return productionRateLimiters.login(req, res, next);
  } else if (path.includes('/auth/forgot-password') || path.includes('/auth/reset-password')) {
    return productionRateLimiters.passwordReset(req, res, next);
  } else if (path.startsWith('/api/auth/')) {
    return productionRateLimiters.auth(req, res, next);
  } else if (path.includes('/trades') && method === 'post') {
    return productionRateLimiters.tradeCreation(req, res, next);
  } else if (path.startsWith('/api/trading/') || path.startsWith('/api/trades/') || path.startsWith('/api/offers/')) {
    return productionRateLimiters.trading(req, res, next);
  } else if (path.includes('/wallet/send') || path.includes('/wallet/receive')) {
    return productionRateLimiters.walletTransaction(req, res, next);
  } else if (path.startsWith('/api/wallet/')) {
    return productionRateLimiters.wallet(req, res, next);
  } else if (path.startsWith('/api/chat/')) {
    return productionRateLimiters.chat(req, res, next);
  } else if (path.includes('/upload') || method === 'post' && path.includes('/files/')) {
    return productionRateLimiters.fileUpload(req, res, next);
  } else if (path.startsWith('/api/admin/')) {
    return productionRateLimiters.admin(req, res, next);
  } else if (path.startsWith('/health') || path.startsWith('/metrics')) {
    return productionRateLimiters.health(req, res, next);
  } else if (path.startsWith('/api/')) {
    return productionRateLimiters.general(req, res, next);
  } else {
    return productionRateLimiters.public(req, res, next);
  }
};

module.exports = {
  createProductionRateLimit,
  productionRateLimiters,
  smartRateLimit,
  rateLimitingService
};
