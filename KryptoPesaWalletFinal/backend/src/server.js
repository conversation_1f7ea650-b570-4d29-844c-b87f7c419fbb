// Load environment variables first
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const { createServer } = require('http');
const { Server } = require('socket.io');

const { connectDB } = require('./config/database');
const { connectRedis } = require('./config/redis');
const logger = require('./utils/logger');
const { errorHandler } = require('./middleware/errorHandler');
const chatService = require('./services/chatService');
const { keyManagementService } = require('./services/keyManagement');

// Performance optimization services
const advancedIndexing = require('./services/advancedIndexing');
const connectionPoolOptimizer = require('./services/connectionPoolOptimizer');
const loadBalancingService = require('./services/loadBalancingService');

// Advanced security services
const advancedSecurity = require('./middleware/advancedSecurity');
const { inputValidationService } = require('./services/inputValidationService');
const penetrationTestingService = require('./services/penetrationTestingService');

// Enhanced error handling
const enhancedErrorHandler = require('./middleware/enhancedErrorHandler');

// Import routes - Non-custodial wallet focused
const walletAuthRoutes = require('./routes/walletAuth'); // Wallet-based authentication
const userRoutes = require('./routes/user'); // Updated for wallet users
const walletRoutes = require('./routes/wallet'); // Non-custodial wallet operations
const tradeRoutes = require('./routes/trade'); // P2P trading
const offerRoutes = require('./routes/offer'); // Trading offers
const tradingRoutes = require('./routes/trading'); // Trading operations
const chatRoutes = require('./routes/chat'); // Trading communication
const notificationRoutes = require('./routes/notifications'); // User notifications
// const adminRoutes = require('./routes/admin'); // Admin operations - temporarily disabled during transformation
// const performanceRoutes = require('./routes/performance'); // Performance monitoring - temporarily disabled
// const dataConsistencyRoutes = require('./routes/dataConsistency'); // Data integrity - temporarily disabled
const healthRoutes = require('./routes/health'); // Health checks
const metricsRoutes = require('./routes/metrics'); // System metrics
// const keyManagementRoutes = require('./routes/keyManagement'); // Key management - temporarily disabled
// const systemStatusRoutes = require('./routes/systemStatus'); // System status - temporarily disabled

// Import socket handlers

// const socketHandler = require('./services/socketService');

// Import security middleware

// Import production rate limiting
const {
  smartRateLimit,
  rateLimitingService
} = require('./middleware/productionRateLimit');


const {
  securityValidation,
  validateTradeData,
  validateUserData
} = require('./middleware/securityValidation');

// Import monitoring middleware
const {
  requestMonitoring,
  errorMonitoring
} = require('./middleware/performanceMonitoring');

// Import comprehensive monitoring system
const ComprehensiveMonitoring = require('../monitoring/comprehensive-monitoring');

// Import response optimization middleware
const {
  responseFilterMiddleware,
  mobileOptimizeMiddleware
} = require('./utils/responseFilter');



// Validate environment variables before starting
const { validateEnvironment, getEnvSummary } = require('./utils/validateEnv');
validateEnvironment();

// Log environment summary
logger.info('Environment configuration:', getEnvSummary());

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production'
      ? ['https://kryptopesa.com', 'https://admin.kryptopesa.com']
      : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://localhost:19006', 'http://***************:8000', 'http://***************:8081', 'http://***************:3000', 'capacitor://localhost', 'ionic://localhost'],
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    credentials: true
  }
});

// Connect to databases
connectDB();
// Connect to Redis
connectRedis();

// Initialize services
require('./services/fileCleanupService');

// Initialize data consistency services
const { dataConsistencyService } = require('./services/dataConsistency');
const { transactionMonitoringService } = require('./services/transactionMonitoring');
require('./services/performanceOptimization');
require('./services/backgroundJobService');

// Initialize performance optimization for 50K+ users
(async () => {
  try {
    await advancedIndexing.initializePerformanceIndexes();
    logger.info('Advanced indexing initialized for high performance');
  } catch (error) {
    logger.error('Error initializing advanced indexing:', error);
  }
})();

// Initialize comprehensive monitoring system
const comprehensiveMonitoring = new ComprehensiveMonitoring({
  environment: process.env.NODE_ENV || 'development',
  serviceName: 'KryptoPesa-Backend',
  version: '1.0.0'
});

// Initialize monitoring integration service
const { monitoringIntegrationService } = require('./services/monitoringIntegrationService');
monitoringIntegrationService.initialize(comprehensiveMonitoring);

// Initialize business flow monitoring service
const { businessFlowMonitoringService } = require('./services/businessFlowMonitoring');
businessFlowMonitoringService.initialize();

// Initialize performance monitoring service
const { performanceMonitoringService } = require('./services/performanceMonitoringService');
performanceMonitoringService.initialize();

// Initialize circuit breaker monitoring service
const { circuitBreakerMonitoringService } = require('./services/circuitBreakerMonitoring');
circuitBreakerMonitoringService.initialize();

// EARLY DEBUG MIDDLEWARE - MOVED TO TOP FOR DEBUGGING
app.use((req, res, next) => {
  if (req.url.startsWith('/api/wallet')) {
    console.log('🔍 [SERVER] EARLY DEBUG - Incoming request:', {
      method: req.method,
      url: req.url,
      headers: {
        'x-wallet-address': req.headers['x-wallet-address'],
        'x-signature': req.headers['x-signature'] ? req.headers['x-signature'].substring(0, 10) + '...' : null,
        'x-timestamp': req.headers['x-timestamp'],
        'x-message': req.headers['x-message'] ? req.headers['x-message'].substring(0, 10) + '...' : null,
        'content-type': req.headers['content-type'],
        'origin': req.headers['origin'],
        'user-agent': req.headers['user-agent']
      }
    });
  }
  next();
});



// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://kryptopesa.com', 'https://admin.kryptopesa.com']
    : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://localhost:19006', 'http://***************:8000', 'http://***************:8081', 'http://***************:3000', 'http://*************:8081', 'http://*************:3000', 'capacitor://localhost', 'ionic://localhost'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    // Wallet authentication headers
    'x-wallet-address',
    'x-signature',
    'x-timestamp',
    'x-message'
  ],
  maxAge: 86400 // 24 hours
}));

// Debug middleware for wallet requests
app.use((req, res, next) => {
  if (req.url.startsWith('/api/wallet')) {
    console.log('🔍 [SERVER] Incoming wallet request:', {
      method: req.method,
      url: req.url,
      headers: {
        'x-wallet-address': req.headers['x-wallet-address'],
        'x-signature': req.headers['x-signature'] ? req.headers['x-signature'].substring(0, 20) + '...' : null,
        'x-timestamp': req.headers['x-timestamp'],
        'x-message': req.headers['x-message'] ? req.headers['x-message'].substring(0, 50) + '...' : null,
        'content-type': req.headers['content-type'],
        'origin': req.headers['origin']
      }
    });
  }
  next();
});

// Production rate limiting (smart rate limiting based on endpoints)
// TEMPORARILY DISABLED FOR DEBUGGING
// app.use(smartRateLimit);

// Security validation middleware
// TEMPORARILY DISABLED FOR DEBUGGING
// app.use(securityValidation);

// Advanced security hardening middleware
// TEMPORARILY DISABLED FOR DEBUGGING
// app.use(advancedSecurity.getIPBlockingMiddleware());
// app.use(advancedSecurity.getAdvancedRateLimit());
// app.use(advancedSecurity.getBruteForceProtection());
// app.use(advancedSecurity.getInputSanitization());
// app.use(advancedSecurity.getSecurityAuditMiddleware());

// Performance monitoring middleware
app.use(requestMonitoring());

// Comprehensive monitoring HTTP middleware
app.use(comprehensiveMonitoring.getHTTPMetricsMiddleware());

// Response optimization middleware
app.use(responseFilterMiddleware);
app.use(mobileOptimizeMiddleware);

// Body parsing middleware with size limits
app.use(compression());

// Load balancing and performance tracking middleware
app.use(loadBalancingService.trackRequest());

app.use(express.json({
  limit: '1mb',
  verify: (req, res, buf) => {
    // Store raw body for webhook verification if needed
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '1mb' }));

// Logging
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
}

// Secure file serving with authentication
const secureFileRoutes = require('./routes/files');
app.use('/api/files', secureFileRoutes);

// Health and monitoring routes
app.use('/health', healthRoutes);
app.use('/metrics', metricsRoutes);

// Load balancer health check endpoint
app.get('/health/load-balancer', loadBalancingService.getHealthCheckHandler());

// Security monitoring endpoints
app.get('/security/metrics', (req, res) => {
  res.json(advancedSecurity.getSecurityMetrics());
});

app.post('/security/pentest', async (req, res) => {
  try {
    const results = await penetrationTestingService.runComprehensivePenTest();
    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    logger.error('Error running penetration test:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to run penetration test'
    });
  }
});

// Enhanced error handling metrics endpoint
app.get('/errors/metrics', (req, res) => {
  res.json(enhancedErrorHandler.getErrorMetrics());
});

// Comprehensive monitoring endpoints
app.get('/metrics/prometheus', comprehensiveMonitoring.getMetricsEndpoint());
app.get('/health/comprehensive', comprehensiveMonitoring.getHealthEndpoint());

// Business flow monitoring endpoints
app.get('/api/monitoring/business-flows', (req, res) => {
  try {
    const metrics = businessFlowMonitoringService.getFlowMetrics();
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    logger.error('Error getting business flow metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get business flow metrics'
    });
  }
});

// Business flow event recording endpoint
app.post('/api/monitoring/business-flows/event', (req, res) => {
  try {
    const { flowType, eventType, eventData } = req.body;
    businessFlowMonitoringService.recordBusinessFlowEvent(flowType, eventType, eventData);
    res.json({
      success: true,
      message: 'Business flow event recorded'
    });
  } catch (error) {
    logger.error('Error recording business flow event:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record business flow event'
    });
  }
});

// Performance monitoring endpoints
app.get('/api/monitoring/performance', (req, res) => {
  try {
    const metrics = performanceMonitoringService.getPerformanceMetrics();
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    logger.error('Error getting performance metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get performance metrics'
    });
  }
});

app.get('/api/monitoring/performance/alerts', (req, res) => {
  try {
    const metrics = performanceMonitoringService.getPerformanceMetrics();

    const alerts = [];

    // Check response time alerts
    if (metrics.responseTime.average > 1000) {
      alerts.push({
        type: 'HIGH_AVERAGE_RESPONSE_TIME',
        severity: metrics.responseTime.average > 5000 ? 'critical' : 'warning',
        message: `Average response time is ${metrics.responseTime.average.toFixed(2)}ms`,
        threshold: 1000,
        current: metrics.responseTime.average
      });
    }

    // Check error rate alerts
    if (metrics.errors.rate > 0.02) {
      alerts.push({
        type: 'HIGH_ERROR_RATE',
        severity: metrics.errors.rate > 0.05 ? 'critical' : 'warning',
        message: `Error rate is ${(metrics.errors.rate * 100).toFixed(2)}%`,
        threshold: 0.02,
        current: metrics.errors.rate
      });
    }

    res.json({
      success: true,
      data: {
        alerts,
        alertCount: alerts.length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error getting performance alerts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get performance alerts'
    });
  }
});

// Circuit breaker monitoring endpoints
app.get('/api/monitoring/circuit-breakers', (req, res) => {
  try {
    const metrics = circuitBreakerMonitoringService.getCircuitBreakerMetrics();
    res.json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting circuit breaker metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get circuit breaker metrics'
    });
  }
});

app.get('/api/monitoring/circuit-breakers/alerts', (req, res) => {
  try {
    const metrics = circuitBreakerMonitoringService.getCircuitBreakerMetrics();
    const alerts = [];

    // Generate alerts based on current state
    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers || {})) {
      if (cbMetrics.healthScore < 50) {
        alerts.push({
          type: 'CIRCUIT_BREAKER_UNHEALTHY',
          circuitBreaker: name,
          severity: cbMetrics.healthScore < 25 ? 'critical' : 'high',
          message: `Circuit breaker ${name} health score is ${cbMetrics.healthScore}`,
          healthScore: cbMetrics.healthScore
        });
      }

      if (cbMetrics.currentState === 'OPEN') {
        alerts.push({
          type: 'CIRCUIT_BREAKER_OPEN',
          circuitBreaker: name,
          severity: 'high',
          message: `Circuit breaker ${name} is in OPEN state`,
          state: cbMetrics.currentState
        });
      }
    }

    res.json({
      success: true,
      data: {
        alerts,
        alertCount: alerts.length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error getting circuit breaker alerts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get circuit breaker alerts'
    });
  }
});

// API routes - Non-custodial wallet focused
app.use('/api/wallet-auth', walletAuthRoutes); // Wallet-based authentication (PRIMARY)
app.use('/api/users', userRoutes); // Wallet user management
app.use('/api/wallet', walletRoutes); // Non-custodial wallet operations
app.use('/api/trades', validateTradeData, tradeRoutes); // P2P trading
app.use('/api/offers', validateTradeData, offerRoutes); // Trading offers
app.use('/api/trading', validateTradeData, tradingRoutes); // Trading operations
app.use('/api/chat', chatRoutes); // Trading communication
app.use('/api/notifications', notificationRoutes); // User notifications
// app.use('/api/admin', adminRoutes); // Admin operations - temporarily disabled during non-custodial transformation
// app.use('/api/performance', performanceRoutes); // Performance monitoring - temporarily disabled
// app.use('/api/data-consistency', dataConsistencyRoutes); // Data integrity - temporarily disabled
// app.use('/api/key-management', keyManagementRoutes); // Key management - temporarily disabled
// app.use('/api/system', systemStatusRoutes); // System status - temporarily disabled
// Note: KYC removed - not needed for non-custodial wallet platform

// Enhanced Socket.io setup with scalability features
const enhancedSocketService = require('./services/enhancedSocketService');

// Initialize enhanced socket service
enhancedSocketService.initialize(io).then(() => {
  logger.info('Enhanced Socket Service initialized successfully');
}).catch((error) => {
  logger.error('Failed to initialize Enhanced Socket Service:', error);
});

// Make socket service available to routes
app.set('socketService', enhancedSocketService);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Error monitoring middleware
app.use(errorMonitoring());

// Error handling middleware (enhanced with user-friendly messages)
app.use(enhancedErrorHandler.getEnhancedErrorMiddleware());

const PORT = process.env.PORT || 3000;

server.listen(PORT, '0.0.0.0', async () => {
  logger.info(`🚀 KryptoPesa API server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV}`);
  logger.info(`Server accessible at: http://0.0.0.0:${PORT}`);

  // Initialize rate limiting service
  try {
    const initialized = await rateLimitingService.initialize();
    if (initialized) {
      logger.info('✅ Rate limiting service initialized successfully');
    } else {
      logger.warn('⚠️ Rate limiting service initialization failed - using fallback mode');
    }
  } catch (error) {
    logger.error('❌ Rate limiting service initialization error:', error);
  }

  // Initialize service layer performance optimization
  try {
    const { serviceLayerPerformance } = require('./services/serviceLayerPerformance');
    const initialized = await serviceLayerPerformance.initialize();
    if (initialized) {
      logger.info('✅ Service layer performance optimization initialized successfully');

      // Optimize database indexes on startup
      await serviceLayerPerformance.optimizeIndexes();
      logger.info('✅ Database indexes optimized');
    } else {
      logger.warn('⚠️ Service layer performance optimization initialization failed');
    }
  } catch (error) {
    logger.error('❌ Service layer performance optimization initialization error:', error);
  }

  // Initialize enhanced key management system
  try {
    const result = await keyManagementService.initialize();
    if (result.success) {
      logger.info('✅ Enhanced key management system initialized successfully');
      if (result.fallbackMode) {
        logger.warn('⚠️ Key management running in fallback mode (AWS unavailable)');
      }
    } else {
      logger.error('❌ Key management system initialization failed');
    }
  } catch (error) {
    logger.error('❌ Key management system initialization error:', error);
  }
});

// Graceful shutdown
// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`${signal} received, shutting down gracefully`);

  try {
    // Shutdown rate limiting service
    rateLimitingService.stopMonitoring();
    logger.info('Rate limiting service stopped');

    // Shutdown service layer performance optimization
    const { serviceLayerPerformance } = require('./services/serviceLayerPerformance');
    await serviceLayerPerformance.shutdown();
    logger.info('Service layer performance optimization stopped');

    // Shutdown background job service
    const { backgroundJobService } = require('./services/backgroundJobService');
    await backgroundJobService.shutdown();

    // Shutdown data consistency services
    await dataConsistencyService.shutdown();
    await transactionMonitoringService.shutdown();
    logger.info('Data consistency services shutdown completed');

    // Shutdown key management system
    keyManagementService.cleanup();
    logger.info('Key management system shutdown completed');

    // Shutdown business flow monitoring service
    await businessFlowMonitoringService.shutdown();
    logger.info('Business flow monitoring service shutdown completed');

    // Shutdown performance monitoring service
    await performanceMonitoringService.shutdown();
    logger.info('Performance monitoring service shutdown completed');

    // Shutdown circuit breaker monitoring service
    await circuitBreakerMonitoringService.shutdown();
    logger.info('Circuit breaker monitoring service shutdown completed');

    // Shutdown monitoring integration service
    await monitoringIntegrationService.shutdown();
    logger.info('Monitoring integration service shutdown completed');

    // Shutdown comprehensive monitoring system
    await comprehensiveMonitoring.shutdown();
    logger.info('Comprehensive monitoring system shutdown completed');

    // Close server
    server.close(() => {
      logger.info('HTTP server closed');

      // Close database connections
      require('mongoose').connection.close(() => {
        logger.info('Database connection closed');
        process.exit(0);
      });
    });
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

module.exports = { app, server, io };
