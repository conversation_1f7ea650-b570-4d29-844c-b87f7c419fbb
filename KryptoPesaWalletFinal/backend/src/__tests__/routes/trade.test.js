const request = require('supertest');
const express = require('express');
const tradeRoutes = require('../../routes/trade');
const { verifyToken } = require('../../routes/auth');

// Create test app
const app = express();
app.use(express.json());
app.use('/trade', verifyToken, tradeRoutes);

describe('Trade Routes', () => {
  let seller, buyer, offer, authTokenSeller, authTokenBuyer;

  beforeEach(async () => {
    // Create test users
    seller = await createTestUser({
      username: 'seller',
      email: '<EMAIL>'
    });
    
    buyer = await createTestUser({
      username: 'buyer',
      email: '<EMAIL>'
    });

    // Create test offer
    offer = await createTestOffer(seller._id);

    // Generate auth tokens
    authTokenSeller = generateAuthToken(seller._id);
    authTokenBuyer = generateAuthToken(buyer._id);
  });

  describe('POST /trade/create', () => {
    it('should create a new trade successfully', async () => {
      const tradeData = {
        offerId: offer._id.toString(),
        cryptocurrency: {
          symbol: 'USDT',
          amount: '50',
          network: 'TRC20'
        },
        fiat: {
          currency: 'KES',
          amount: 6500
        }
      };

      const response = await request(app)
        .post('/trade/create')
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .send(tradeData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trade.tradeId).toBeDefined();
      expect(response.body.data.trade.seller.toString()).toBe(seller._id.toString());
      expect(response.body.data.trade.buyer.toString()).toBe(buyer._id.toString());
      expect(response.body.data.trade.status).toBe('created');
    });

    it('should return 400 for invalid offer ID', async () => {
      const tradeData = {
        offerId: 'invalid-offer-id',
        cryptocurrency: {
          symbol: 'USDT',
          amount: '50',
          network: 'TRC20'
        },
        fiat: {
          currency: 'KES',
          amount: 6500
        }
      };

      const response = await request(app)
        .post('/trade/create')
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .send(tradeData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should return 404 for non-existent offer', async () => {
      const tradeData = {
        offerId: '507f1f77bcf86cd799439011', // Valid ObjectId but doesn't exist
        cryptocurrency: {
          symbol: 'USDT',
          amount: '50',
          network: 'TRC20'
        },
        fiat: {
          currency: 'KES',
          amount: 6500
        }
      };

      const response = await request(app)
        .post('/trade/create')
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .send(tradeData)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Offer not found');
    });

    it('should return 400 if user tries to trade with their own offer', async () => {
      const tradeData = {
        offerId: offer._id.toString(),
        cryptocurrency: {
          symbol: 'USDT',
          amount: '50',
          network: 'TRC20'
        },
        fiat: {
          currency: 'KES',
          amount: 6500
        }
      };

      const response = await request(app)
        .post('/trade/create')
        .set('Authorization', `Bearer ${authTokenSeller}`) // Seller trying to trade with own offer
        .send(tradeData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('cannot trade with your own offer');
    });

    it('should return 400 for amount below minimum', async () => {
      const tradeData = {
        offerId: offer._id.toString(),
        cryptocurrency: {
          symbol: 'USDT',
          amount: '5', // Below minimum
          network: 'TRC20'
        },
        fiat: {
          currency: 'KES',
          amount: 650
        }
      };

      const response = await request(app)
        .post('/trade/create')
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .send(tradeData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('below minimum');
    });

    it('should return 400 for amount above maximum', async () => {
      const tradeData = {
        offerId: offer._id.toString(),
        cryptocurrency: {
          symbol: 'USDT',
          amount: '200', // Above maximum
          network: 'TRC20'
        },
        fiat: {
          currency: 'KES',
          amount: 26000
        }
      };

      const response = await request(app)
        .post('/trade/create')
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .send(tradeData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('above maximum');
    });
  });

  describe('GET /trade/:tradeId', () => {
    let trade;

    beforeEach(async () => {
      trade = await createTestTrade(seller._id, buyer._id, offer._id);
    });

    it('should get trade details for seller', async () => {
      const response = await request(app)
        .get(`/trade/${trade.tradeId}`)
        .set('Authorization', `Bearer ${authTokenSeller}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trade.tradeId).toBe(trade.tradeId);
      expect(response.body.data.trade.seller._id).toBe(seller._id.toString());
    });

    it('should get trade details for buyer', async () => {
      const response = await request(app)
        .get(`/trade/${trade.tradeId}`)
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trade.tradeId).toBe(trade.tradeId);
      expect(response.body.data.trade.buyer._id).toBe(buyer._id.toString());
    });

    it('should return 403 for unauthorized user', async () => {
      const otherUser = await createTestUser({
        username: 'other',
        email: '<EMAIL>'
      });
      const otherToken = generateAuthToken(otherUser._id);

      const response = await request(app)
        .get(`/trade/${trade.tradeId}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('not authorized');
    });

    it('should return 404 for non-existent trade', async () => {
      const response = await request(app)
        .get('/trade/NONEXISTENT123')
        .set('Authorization', `Bearer ${authTokenSeller}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Trade not found');
    });
  });

  describe('PUT /trade/:tradeId/fund', () => {
    let trade;

    beforeEach(async () => {
      trade = await createTestTrade(seller._id, buyer._id, offer._id);
    });

    it('should fund trade successfully', async () => {
      const fundData = {
        transactionHash: '0x1234567890abcdef',
        escrowAddress: '0xabcdef1234567890'
      };

      const response = await request(app)
        .put(`/trade/${trade.tradeId}/fund`)
        .set('Authorization', `Bearer ${authTokenSeller}`)
        .send(fundData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trade.status).toBe('funded');
      expect(response.body.data.trade.escrow.transactionHash).toBe(fundData.transactionHash);
    });

    it('should return 403 if not seller', async () => {
      const fundData = {
        transactionHash: '0x1234567890abcdef',
        escrowAddress: '0xabcdef1234567890'
      };

      const response = await request(app)
        .put(`/trade/${trade.tradeId}/fund`)
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .send(fundData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Only seller can fund');
    });

    it('should return 400 if trade already funded', async () => {
      // Fund the trade first
      trade.status = 'funded';
      await trade.save();

      const fundData = {
        transactionHash: '0x1234567890abcdef',
        escrowAddress: '0xabcdef1234567890'
      };

      const response = await request(app)
        .put(`/trade/${trade.tradeId}/fund`)
        .set('Authorization', `Bearer ${authTokenSeller}`)
        .send(fundData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('already funded');
    });
  });

  describe('PUT /trade/:tradeId/payment-sent', () => {
    let trade;

    beforeEach(async () => {
      trade = await createTestTrade(seller._id, buyer._id, offer._id, {
        status: 'funded'
      });
    });

    it('should mark payment as sent successfully', async () => {
      const paymentData = {
        paymentMethod: 'M-Pesa',
        transactionId: 'MP123456789',
        notes: 'Payment sent via M-Pesa'
      };

      const response = await request(app)
        .put(`/trade/${trade.tradeId}/payment-sent`)
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .send(paymentData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trade.status).toBe('payment_sent');
      expect(response.body.data.trade.payment.method).toBe(paymentData.paymentMethod);
    });

    it('should return 403 if not buyer', async () => {
      const paymentData = {
        paymentMethod: 'M-Pesa',
        transactionId: 'MP123456789'
      };

      const response = await request(app)
        .put(`/trade/${trade.tradeId}/payment-sent`)
        .set('Authorization', `Bearer ${authTokenSeller}`)
        .send(paymentData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Only buyer can mark payment');
    });

    it('should return 400 if trade not funded', async () => {
      trade.status = 'created';
      await trade.save();

      const paymentData = {
        paymentMethod: 'M-Pesa',
        transactionId: 'MP123456789'
      };

      const response = await request(app)
        .put(`/trade/${trade.tradeId}/payment-sent`)
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('must be funded');
    });
  });

  describe('PUT /trade/:tradeId/confirm-payment', () => {
    let trade;

    beforeEach(async () => {
      trade = await createTestTrade(seller._id, buyer._id, offer._id, {
        status: 'payment_sent'
      });
    });

    it('should confirm payment and complete trade', async () => {
      const response = await request(app)
        .put(`/trade/${trade.tradeId}/confirm-payment`)
        .set('Authorization', `Bearer ${authTokenSeller}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trade.status).toBe('completed');
      expect(response.body.data.trade.completedAt).toBeDefined();
    });

    it('should return 403 if not seller', async () => {
      const response = await request(app)
        .put(`/trade/${trade.tradeId}/confirm-payment`)
        .set('Authorization', `Bearer ${authTokenBuyer}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Only seller can confirm');
    });

    it('should return 400 if payment not sent', async () => {
      trade.status = 'funded';
      await trade.save();

      const response = await request(app)
        .put(`/trade/${trade.tradeId}/confirm-payment`)
        .set('Authorization', `Bearer ${authTokenSeller}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Payment must be sent');
    });
  });
});
