import { API_ENDPOINTS, ApiResponse, EAST_AFRICAN_CONFIG } from './api';

// Lazy import for apiClient to avoid early initialization
const getApiClient = () => {
  const { apiClient } = require('./api');
  return apiClient;
};

// User types
export type EastAfricanCountry = 'KE' | 'TZ' | 'UG' | 'RW';
export type EastAfricanCurrency = 'KES' | 'TZS' | 'UGX' | 'RWF' | 'USD';
export type Language = 'en' | 'sw';

export interface UserProfile {
  _id: string;
  walletAddress: string;
  profile: {
    firstName?: string;
    lastName?: string;
    bio?: string;
    avatar?: string;
    location: {
      country: EastAfricanCountry;
      city?: string;
    };
  };
  preferences: {
    language: Language;
    currency: EastAfricanCurrency;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
  reputation: {
    score: number;
    totalTrades: number;
    completedTrades: number;
    completionRate: number;
    averageRating: number;
    totalRatings: number;
  };
  kyc: {
    level: number;
    status: 'pending' | 'approved' | 'rejected' | 'not_submitted';
    submittedAt?: string;
    approvedAt?: string;
  };
  security: {
    lastLogin: string;
    twoFactorEnabled: boolean;
    emailVerified: boolean;
    phoneVerified: boolean;
  };
  status: 'active' | 'suspended' | 'banned';
  isOnline: boolean;
  lastActive: string;
  createdAt: string;
}

export interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  bio?: string;
  avatar?: string;
  location?: {
    country?: EastAfricanCountry;
    city?: string;
  };
  preferences?: {
    language?: Language;
    currency?: EastAfricanCurrency;
    notifications?: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
    };
  };
}

export interface UserDashboard {
  user: UserProfile;
  stats: {
    totalTrades: number;
    activeTrades: number;
    totalVolume: number;
    walletValue: number;
    profitLoss: number;
  };
  recentTrades: Array<{
    _id: string;
    type: 'buy' | 'sell';
    cryptocurrency: string;
    amount: number;
    fiatAmount: number;
    status: string;
    createdAt: string;
  }>;
  notifications: Array<{
    _id: string;
    type: string;
    title: string;
    message: string;
    isRead: boolean;
    createdAt: string;
  }>;
}

export interface TraderProfile {
  _id: string;
  username: string;
  profile: {
    firstName?: string;
    lastName?: string;
    bio?: string;
    avatar?: string;
    location: {
      country: EastAfricanCountry;
      city?: string;
    };
  };
  reputation: {
    score: number;
    totalTrades: number;
    completedTrades: number;
    completionRate: number;
    averageRating: number;
    totalRatings: number;
  };
  kyc: {
    level: number;
    status: string;
  };
  isOnline: boolean;
  lastActive: string;
  joinedAt: string;
}

// User Service Class
class UserService {
  /**
   * Get current user profile
   */
  async getProfile(): Promise<UserProfile> {
    try {
      // Check if user is authenticated before making API call
      const isAuth = await getApiClient().isAuthenticated();
      if (!isAuth) {
        // Silently return mock data without error logging for unauthenticated users
        return this.getMockProfile();
      }

      const response = await getApiClient().get<UserProfile>(
        API_ENDPOINTS.USERS.PROFILE
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get user profile');
      }
    } catch (error) {
      // Only log errors for authenticated users
      if (error.message !== 'UNAUTHENTICATED') {
        console.error('Get profile error:', error);
      }

      // Return mock profile data when API fails
      return this.getMockProfile();
    }
  }

  /**
   * Get mock profile data (extracted to separate method)
   */
  private getMockProfile(): UserProfile {
    const mockProfile: UserProfile = {
        _id: 'mock_user_123',
        username: 'KryptoPesaUser',
        email: '<EMAIL>',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
          bio: 'Experienced crypto trader in East Africa',
          avatar: '',
          location: {
            country: 'KE',
            city: 'Nairobi',
          },
        },
        preferences: {
          currency: 'KES',
          language: 'en',
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
          privacy: {
            showOnlineStatus: true,
            showTradeHistory: false,
          },
        },
        security: {
          twoFactorEnabled: false,
          emailVerified: true,
          phoneVerified: false,
          kycStatus: 'pending',
          kycLevel: 1,
        },
        verification: {
          isVerified: false,
          status: 'pending',
          level: 1,
          documents: [],
        },
        trading: {
          reputation: 85,
          totalTrades: 42,
          completionRate: 95.2,
          averageResponseTime: 8, // minutes
          preferredPaymentMethods: ['mpesa', 'bank_transfer'],
        },
        wallet: {
          totalBalance: 2500.75,
          availableBalance: 2350.50,
          escrowBalance: 150.25,
        },
        createdAt: new Date(Date.now() - ******** * 90).toISOString(), // 90 days ago
        updatedAt: new Date().toISOString(),
        lastLoginAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
      };

      console.log('Using mock profile data');
      return mockProfile;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: UpdateProfileData): Promise<UserProfile> {
    try {
      const response = await getApiClient().put<UserProfile>(
        API_ENDPOINTS.USERS.UPDATE_PROFILE,
        profileData
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Update profile error:', error);

      // Return mock updated profile when API fails
      const currentProfile = await this.getProfile();
      const updatedProfile: UserProfile = {
        ...currentProfile,
        profile: {
          ...currentProfile.profile,
          ...profileData,
        },
        updatedAt: new Date().toISOString(),
      };

      console.log('Mock profile update successful:', updatedProfile);
      return updatedProfile;
    }
  }

  /**
   * Get user dashboard data
   */
  async getDashboard(walletAddress?: string): Promise<UserDashboard> {
    try {
      // Use provided wallet address or get from storage
      const address = walletAddress || await this.getCurrentWalletAddress();

      if (!address) {
        throw new Error('No wallet address available');
      }

      // Make direct fetch call without authentication for dashboard endpoint
      const API_BASE_URL = 'http://***************:3000/api';
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.USERS.DASHBOARD}/${address}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          return data.data;
        } else {
          throw new Error(data.message || 'Failed to get dashboard data');
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Get dashboard error:', error);
      throw error;
    }
  }

  /**
   * Get current wallet address from storage
   */
  private async getCurrentWalletAddress(): Promise<string | null> {
    try {
      // This should get the current user's wallet address from your wallet/auth system
      // For now, return a mock address - you'll need to implement this based on your auth system
      return '******************************************';
    } catch (error) {
      console.error('Error getting wallet address:', error);
      return null;
    }
  }

  /**
   * Search for traders
   */
  async searchTraders(query?: string, country?: EastAfricanCountry): Promise<TraderProfile[]> {
    try {
      const response = await getApiClient().get<TraderProfile[]>(
        API_ENDPOINTS.USERS.SEARCH,
        { query, country }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to search traders');
      }
    } catch (error) {
      console.error('Search traders error:', error);
      throw error;
    }
  }

  /**
   * Get trader profile by ID
   */
  async getTraderProfile(traderId: string): Promise<TraderProfile> {
    try {
      const response = await getApiClient().get<TraderProfile>(
        `${API_ENDPOINTS.USERS.PROFILE}/${traderId}`
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get trader profile');
      }
    } catch (error) {
      console.error('Get trader profile error:', error);
      throw error;
    }
  }

  /**
   * Upload profile avatar
   */
  async uploadAvatar(imageFile: any): Promise<string> {
    try {
      const response = await getApiClient().uploadFile<{ avatarUrl: string }>(
        `${API_ENDPOINTS.USERS.PROFILE}/avatar`,
        imageFile
      );

      if (response.success && response.data) {
        return response.data.avatarUrl;
      } else {
        throw new Error(response.message || 'Failed to upload avatar');
      }
    } catch (error) {
      console.error('Upload avatar error:', error);
      throw error;
    }
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(preferences: {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
  }): Promise<void> {
    try {
      const response = await getApiClient().put(
        `${API_ENDPOINTS.USERS.PROFILE}/notifications`,
        { notifications: preferences }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to update notification preferences');
      }
    } catch (error) {
      console.error('Update notification preferences error:', error);
      throw error;
    }
  }

  /**
   * Update user language preference
   */
  async updateLanguage(language: Language): Promise<void> {
    try {
      const response = await getApiClient().put(
        API_ENDPOINTS.USERS.UPDATE_PROFILE,
        { preferences: { language } }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to update language');
      }
    } catch (error) {
      console.error('Update language error:', error);
      throw error;
    }
  }

  /**
   * Update user currency preference
   */
  async updateCurrency(currency: EastAfricanCurrency): Promise<void> {
    try {
      const response = await getApiClient().put(
        API_ENDPOINTS.USERS.UPDATE_PROFILE,
        { preferences: { currency } }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to update currency');
      }
    } catch (error) {
      console.error('Update currency error:', error);
      throw error;
    }
  }
}

// Create and export service instance
export const userService = new UserService();

// User utility functions
export const userUtils = {
  /**
   * Get user display name
   */
  getDisplayName(user: UserProfile | TraderProfile): string {
    if (user.profile.firstName && user.profile.lastName) {
      return `${user.profile.firstName} ${user.profile.lastName}`;
    }
    if (user.profile.firstName) {
      return user.profile.firstName;
    }
    return 'username' in user ? user.username : 'Anonymous';
  },

  /**
   * Get country flag emoji
   */
  getCountryFlag(country: EastAfricanCountry): string {
    return EAST_AFRICAN_CONFIG.COUNTRIES[country]?.flag || '🌍';
  },

  /**
   * Get country name
   */
  getCountryName(country: EastAfricanCountry): string {
    return EAST_AFRICAN_CONFIG.COUNTRIES[country]?.name || country;
  },

  /**
   * Get currency symbol
   */
  getCurrencySymbol(currency: EastAfricanCurrency): string {
    return EAST_AFRICAN_CONFIG.CURRENCIES[currency]?.symbol || currency;
  },

  /**
   * Format reputation score
   */
  formatReputationScore(score: number): string {
    if (score >= 95) return 'Excellent';
    if (score >= 85) return 'Very Good';
    if (score >= 75) return 'Good';
    if (score >= 60) return 'Fair';
    return 'Poor';
  },

  /**
   * Get reputation color
   */
  getReputationColor(score: number): string {
    if (score >= 95) return '#10B981'; // Green
    if (score >= 85) return '#84CC16'; // Light Green
    if (score >= 75) return '#F59E0B'; // Yellow
    if (score >= 60) return '#F97316'; // Orange
    return '#EF4444'; // Red
  },

  /**
   * Format completion rate
   */
  formatCompletionRate(rate: number): string {
    return `${(rate * 100).toFixed(1)}%`;
  },

  /**
   * Get KYC level name
   */
  getKycLevelName(level: number): string {
    switch (level) {
      case 0: return 'Not Verified';
      case 1: return 'Basic';
      case 2: return 'Intermediate';
      case 3: return 'Advanced';
      default: return 'Unknown';
    }
  },

  /**
   * Get KYC status color
   */
  getKycStatusColor(status: string): string {
    switch (status) {
      case 'approved': return '#10B981'; // Green
      case 'pending': return '#F59E0B'; // Yellow
      case 'rejected': return '#EF4444'; // Red
      default: return '#6B7280'; // Gray
    }
  },

  /**
   * Check if user is online
   */
  isUserOnline(user: UserProfile | TraderProfile): boolean {
    if (user.isOnline) return true;
    
    // Consider user online if last active within 5 minutes
    const lastActive = new Date(user.lastActive);
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return lastActive > fiveMinutesAgo;
  },

  /**
   * Format last active time
   */
  formatLastActive(lastActive: string): string {
    const date = new Date(lastActive);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  },

  /**
   * Validate profile completeness
   */
  getProfileCompleteness(user: UserProfile): {
    percentage: number;
    missingFields: string[];
  } {
    const requiredFields = [
      { field: 'firstName', label: 'First Name', value: user.profile.firstName },
      { field: 'lastName', label: 'Last Name', value: user.profile.lastName },
      { field: 'bio', label: 'Bio', value: user.profile.bio },
      { field: 'avatar', label: 'Profile Picture', value: user.profile.avatar },
      { field: 'city', label: 'City', value: user.profile.location.city },
    ];

    const completedFields = requiredFields.filter(field => field.value && field.value.trim() !== '');
    const percentage = Math.round((completedFields.length / requiredFields.length) * 100);
    const missingFields = requiredFields
      .filter(field => !field.value || field.value.trim() === '')
      .map(field => field.label);

    return { percentage, missingFields };
  },
};

export default userService;
