import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import {
  websocketService,
  EAST_AFRICAN_CONFIG,
  eastAfricanHelpers
} from '../../services';

// Notification type (temporary until notificationService is implemented)
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'trade' | 'payment' | 'security' | 'system' | 'chat';
  priority: 'low' | 'medium' | 'high';
  read: boolean;
  timestamp: string;
  data?: any;
}

// Mock notifications data - in a real app this would come from Convex
const mockNotifications = [
  {
    id: '1',
    type: 'trade_request',
    title: 'New Trade Request',
    message: '<PERSON> wants to buy 0.005 BTC from you',
    timestamp: '2024-01-15T14:30:00Z',
    isRead: false,
    relatedId: 'trade_123',
    icon: 'swap-horizontal',
    iconColor: 'primary',
  },
  {
    id: '2',
    type: 'payment_received',
    title: 'Payment Received',
    message: 'Payment of $250 has been confirmed for trade #456',
    timestamp: '2024-01-15T13:15:00Z',
    isRead: false,
    relatedId: 'trade_456',
    icon: 'checkmark-circle',
    iconColor: 'success',
  },
  {
    id: '3',
    type: 'trade_completed',
    title: 'Trade Completed',
    message: 'Your trade with Alice Johnson has been completed successfully',
    timestamp: '2024-01-15T12:00:00Z',
    isRead: true,
    relatedId: 'trade_789',
    icon: 'trophy',
    iconColor: 'warning',
  },
  {
    id: '4',
    type: 'security_alert',
    title: 'Security Alert',
    message: 'New login detected from Chrome on Windows',
    timestamp: '2024-01-15T10:45:00Z',
    isRead: true,
    relatedId: null,
    icon: 'shield-checkmark',
    iconColor: 'error',
  },
  {
    id: '5',
    type: 'price_alert',
    title: 'Price Alert',
    message: 'BTC has reached your target price of $45,000',
    timestamp: '2024-01-15T09:30:00Z',
    isRead: true,
    relatedId: null,
    icon: 'trending-up',
    iconColor: 'info',
  },
  {
    id: '6',
    type: 'system_update',
    title: 'System Update',
    message: 'New features available! Check out the latest improvements',
    timestamp: '2024-01-14T18:00:00Z',
    isRead: true,
    relatedId: null,
    icon: 'information-circle',
    iconColor: 'textSecondary',
  },
];

export default function NotificationCenterScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();

  // State management
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load notifications
  const loadNotifications = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      // TODO: Replace with actual notificationService when implemented
      const notificationData = mockNotifications;
      setNotifications(notificationData);
    } catch (err) {
      console.error('Failed to load notifications:', err);
      setError(err instanceof Error ? err.message : 'Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadNotifications();

    // Set up WebSocket listeners for real-time notifications
    const unsubscribeNotification = websocketService.onNotification((notification) => {
      setNotifications(prev => [notification, ...prev]);
    });

    return () => {
      unsubscribeNotification();
    };
  }, []);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadNotifications(false);
    setRefreshing(false);
  };
  
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return notificationTime.toLocaleDateString();
  };
  
  const handleNotificationPress = async (notification: Notification) => {
    try {
      // Mark as read
      // TODO: Replace with actual notificationService when implemented
      // await notificationService.markAsRead(notification._id);
      setNotifications(prev =>
        prev.map(n => n._id === notification._id ? { ...n, isRead: true } : n)
      );

      // Navigate based on notification type
      switch (notification.type) {
        case 'trade_request':
        case 'trade_completed':
        case 'payment_received':
        case 'payment_sent':
        case 'trade_update':
          if (notification.relatedId) {
            navigation.navigate('Trade', { tradeId: notification.relatedId });
          }
          break;
        case 'message_received':
        case 'chat_message':
          if (notification.relatedId) {
            navigation.navigate('IndividualChat', { conversationId: notification.relatedId });
          }
          break;
        case 'kyc_approved':
        case 'kyc_rejected':
        case 'kyc_update':
          navigation.navigate('KYCVerification');
          break;
        case 'offer_update':
          if (notification.relatedId) {
            navigation.navigate('OfferDetail', { offerId: notification.relatedId });
          }
          break;
        case 'security_alert':
          navigation.navigate('Settings');
          break;
        default:
          // For system notifications or others without specific navigation
          break;
      }
    } catch (err) {
      console.error('Failed to mark notification as read:', err);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      // TODO: Replace with actual notificationService when implemented
      // await notificationService.markAllAsRead();
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
    } catch (err) {
      console.error('Failed to mark all as read:', err);
      Alert.alert('Error', 'Failed to mark all notifications as read');
    }
  };

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Replace with actual notificationService when implemented
              // await notificationService.clearAll();
              setNotifications([]);
            } catch (err) {
              console.error('Failed to clear notifications:', err);
              Alert.alert('Error', 'Failed to clear notifications');
            }
          },
        },
      ]
    );
  };

  const renderNotification = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[styles.notificationItem, !item.isRead && styles.unreadNotification]}
      onPress={() => handleNotificationPress(item)}
    >
      <View style={[
        styles.iconContainer,
        {
          backgroundColor: theme.colors[item.iconColor as keyof typeof theme.colors] + (item.isRead ? '15' : '20'),
          opacity: item.isRead ? 0.6 : 1
        }
      ]}>
        <Ionicons
          name={item.icon}
          size={20}
          color={theme.colors[item.iconColor as keyof typeof theme.colors]}
          style={{ opacity: item.isRead ? 0.6 : 1 }}
        />
      </View>

      <View style={styles.notificationContent}>
        <View style={styles.notificationHeader}>
          <Text style={[
            styles.notificationTitle,
            !item.isRead && styles.unreadTitle,
            item.isRead && styles.readTitle
          ]}>
            {item.title}
          </Text>
          <Text style={[
            styles.notificationTime,
            item.isRead && styles.readTime
          ]}>
            {formatTimeAgo(item.timestamp)}
          </Text>
        </View>

        <Text style={[
          styles.notificationMessage,
          item.isRead && styles.readMessage
        ]} numberOfLines={2}>
          {item.message}
        </Text>

        {!item.isRead && <View style={styles.unreadDot} />}
      </View>
    </TouchableOpacity>
  );

  const styles = createStyles(theme);

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading notifications...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load notifications</Text>
          <Text style={styles.errorSubText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadNotifications()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>KryptoPesa Notifications</Text>
          {unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadBadgeText}>{unreadCount}</Text>
            </View>
          )}
        </View>
        <TouchableOpacity onPress={handleMarkAllAsRead}>
          <Ionicons name="checkmark-done" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
      
      {notifications.length === 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="notifications-outline" size={64} color={theme.colors.border} />
          <Text style={styles.emptyStateTitle}>No Notifications</Text>
          <Text style={styles.emptyStateMessage}>
            You're all caught up! New notifications will appear here.
          </Text>
        </View>
      ) : (
        <>
          {unreadCount > 0 && (
            <View style={styles.actionBar}>
              <TouchableOpacity style={styles.actionButton} onPress={handleMarkAllAsRead}>
                <Ionicons name="checkmark-done-outline" size={16} color={theme.colors.primary} />
                <Text style={styles.actionButtonText}>Mark all as read</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionButton} onPress={handleClearAll}>
                <Ionicons name="trash-outline" size={16} color={theme.colors.error} />
                <Text style={[styles.actionButtonText, { color: theme.colors.error }]}>Clear all</Text>
              </TouchableOpacity>
            </View>
          )}
          
          <FlatList
            data={notifications}
            renderItem={renderNotification}
            keyExtractor={(item) => item.id}
            style={styles.notificationsList}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </>
      )}
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  unreadBadge: {
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  unreadBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateMessage: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  notificationsList: {
    flex: 1,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: theme.colors.background,
    alignItems: 'flex-start',
    gap: 12,
  },
  unreadNotification: {
    backgroundColor: theme.colors.surface,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notificationContent: {
    flex: 1,
    position: 'relative',
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    flex: 1,
    marginRight: 8,
  },
  unreadTitle: {
    fontWeight: '600',
  },
  readTitle: {
    color: theme.colors.textTertiary,
  },
  notificationTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  readTime: {
    color: theme.colors.textTertiary,
  },
  notificationMessage: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  readMessage: {
    color: theme.colors.textTertiary,
  },
  unreadDot: {
    position: 'absolute',
    top: 0,
    right: -8,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.primary,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginLeft: 68,
  },
});
