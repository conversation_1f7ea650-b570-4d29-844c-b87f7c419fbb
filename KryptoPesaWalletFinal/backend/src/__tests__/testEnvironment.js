/**
 * Test Environment Configuration
 * Centralized test environment setup and utilities
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

class TestEnvironment {
  constructor() {
    this.mongoServer = null;
    this.isConnected = false;
    this.activeTimers = new Set();
    this.activeIntervals = new Set();
    this.originalSetTimeout = global.setTimeout;
    this.originalSetInterval = global.setInterval;
    this.originalClearTimeout = global.clearTimeout;
    this.originalClearInterval = global.clearInterval;
  }

  // Override timer functions to track them
  setupTimerTracking() {
    global.setTimeout = (fn, delay, ...args) => {
      const id = this.originalSetTimeout(fn, delay, ...args);
      this.activeTimers.add(id);
      return id;
    };

    global.setInterval = (fn, delay, ...args) => {
      const id = this.originalSetInterval(fn, delay, ...args);
      this.activeIntervals.add(id);
      return id;
    };

    global.clearTimeout = (id) => {
      this.activeTimers.delete(id);
      return this.originalClearTimeout(id);
    };

    global.clearInterval = (id) => {
      this.activeIntervals.delete(id);
      return this.originalClearInterval(id);
    };
  }

  // Clean up all tracked timers
  cleanupTimers() {
    this.activeTimers.forEach(id => {
      this.originalClearTimeout(id);
    });
    this.activeIntervals.forEach(id => {
      this.originalClearInterval(id);
    });
    this.activeTimers.clear();
    this.activeIntervals.clear();

    // Stop circuit breaker monitoring
    try {
      const { stopAllCircuitBreakers } = require('../utils/circuitBreaker');
      stopAllCircuitBreakers();
    } catch (error) {
      // Ignore if circuit breaker module not available
    }
  }

  // Restore original timer functions
  restoreTimers() {
    global.setTimeout = this.originalSetTimeout;
    global.setInterval = this.originalSetInterval;
    global.clearTimeout = this.originalClearTimeout;
    global.clearInterval = this.originalClearInterval;
  }

  // Setup test environment variables
  setupTestEnvironment() {
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-purposes-only';
    process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters-long';
    process.env.REDIS_URL = 'redis://localhost:6379';
    process.env.ETHEREUM_RPC_URL = 'https://mainnet.infura.io/v3/test-key';
    process.env.BITCOIN_RPC_URL = 'https://blockstream.info/api';
    process.env.CLOUDINARY_CLOUD_NAME = 'test-cloud';
    process.env.CLOUDINARY_API_KEY = 'test-api-key';
    process.env.CLOUDINARY_API_SECRET = 'test-api-secret';
    process.env.FIREBASE_PROJECT_ID = 'test-project';
    process.env.FIREBASE_PRIVATE_KEY = 'test-private-key';
    process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>';
    process.env.DISCORD_WEBHOOK_URL = 'https://discord.com/api/webhooks/test';
    process.env.RATE_LIMIT_WINDOW_MS = '900000';
    process.env.RATE_LIMIT_MAX_REQUESTS = '100';
    process.env.BCRYPT_ROUNDS = '10';
    process.env.SESSION_SECRET = 'test-session-secret-key';
    process.env.CORS_ORIGIN = 'http://localhost:3000';
    process.env.PORT = '3001';
  }

  // Setup database connection with replica set support
  async setupDatabase() {
    if (this.isConnected) {
      return;
    }

    try {
      // Create MongoDB Memory Server without replica set to avoid timeout issues
      this.mongoServer = await MongoMemoryServer.create({
        instance: {
          port: undefined, // Let MongoDB Memory Server choose a port
          dbName: 'test-kryptopesa'
        }
      });

      const mongoUri = this.mongoServer.getUri();

      await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        serverSelectionTimeoutMS: 10000, // Reduce timeout to 10 seconds
        connectTimeoutMS: 10000,
      });

      this.isConnected = true;
      console.log('Test database connected successfully');
    } catch (error) {
      console.error('Failed to setup test database:', error);
      throw error;
    }
  }

  // Clean database collections
  async cleanDatabase() {
    if (!this.isConnected || mongoose.connection.readyState !== 1) {
      return;
    }

    try {
      const collections = mongoose.connection.collections;
      const cleanupPromises = Object.keys(collections).map(async (key) => {
        const collection = collections[key];
        await collection.deleteMany({});
      });
      
      await Promise.all(cleanupPromises);
    } catch (error) {
      console.error('Failed to clean database:', error);
    }
  }

  // Teardown database connection
  async teardownDatabase() {
    if (!this.isConnected) {
      return;
    }

    try {
      if (mongoose.connection.readyState === 1) {
        await mongoose.connection.dropDatabase();
        await mongoose.connection.close();
      }
      
      if (this.mongoServer) {
        await this.mongoServer.stop();
      }
      
      this.isConnected = false;
      console.log('Test database disconnected');
    } catch (error) {
      console.error('Failed to teardown database:', error);
    }
  }

  // Create test user
  async createTestUser(userData = {}) {
    const User = require('../models/User');
    const defaultUser = {
      username: 'testuser',
      email: '<EMAIL>',
      phone: '+254700000000',
      password: 'password123',
      profile: {
        firstName: 'Test',
        lastName: 'User',
        location: {
          country: 'KE',
          city: 'Nairobi'
        }
      },
      isVerified: true,
      ...userData
    };

    return await User.create(defaultUser);
  }

  // Create test wallet
  async createTestWallet(userId, walletData = {}) {
    const Wallet = require('../models/Wallet');

    // Generate unique address based on userId
    const uniqueAddress = `0x${userId.toString().padEnd(40, '0')}`;

    const defaultWallet = {
      user: userId,
      addresses: {
        ethereum: {
          address: uniqueAddress,
          publicKey: 'test-public-key'
        }
      },
      balances: [
        { symbol: 'BTC', network: 'bitcoin', balance: '0' },
        { symbol: 'ETH', network: 'ethereum', balance: '0' },
        { symbol: 'USDT', network: 'polygon', balance: '0' }
      ],
      ...walletData
    };

    return await Wallet.create(defaultWallet);
  }

  // Mock external services
  mockExternalServices() {
    // Mock Redis
    jest.mock('redis', () => ({
      createClient: jest.fn(() => ({
        connect: jest.fn(),
        disconnect: jest.fn(),
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
        exists: jest.fn(),
        expire: jest.fn(),
        incr: jest.fn(),
        decr: jest.fn(),
        hget: jest.fn(),
        hset: jest.fn(),
        hdel: jest.fn(),
        hgetall: jest.fn(),
        on: jest.fn(),
        off: jest.fn()
      }))
    }));

    // Mock Firebase Admin
    jest.mock('firebase-admin', () => ({
      initializeApp: jest.fn(),
      messaging: jest.fn(() => ({
        send: jest.fn(),
        sendMulticast: jest.fn()
      }))
    }));

    // Mock Cloudinary
    jest.mock('cloudinary', () => ({
      v2: {
        config: jest.fn(),
        uploader: {
          upload: jest.fn(),
          destroy: jest.fn()
        }
      }
    }));
  }

  // Complete setup
  async setup() {
    this.setupTestEnvironment();
    this.setupTimerTracking();
    this.mockExternalServices();
    await this.setupDatabase();
  }

  // Complete cleanup
  async cleanup() {
    this.cleanupTimers();
    await this.cleanDatabase();
  }

  // Complete teardown
  async teardown() {
    this.cleanupTimers();
    this.restoreTimers();
    await this.teardownDatabase();
  }
}

// Export singleton instance
const testEnvironment = new TestEnvironment();
module.exports = testEnvironment;
