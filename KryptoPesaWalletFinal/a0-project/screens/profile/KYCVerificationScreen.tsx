import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
  Modal,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useKYC, KYC_LEVELS } from '../../context/KYCContext';

// KYC verification levels and requirements
const KYC_LEVEL_INFO = {
  [KYC_LEVELS.BASIC]: {
    level: 1,
    name: 'Basic Verification',
    description: 'Personal information and phone verification',
    tradingLimit: '$1,000',
    requirements: ['Personal Information', 'Phone Verification'],
  },
  [KYC_LEVELS.INTERMEDIATE]: {
    level: 2,
    name: 'Intermediate Verification',
    description: 'Government ID verification',
    tradingLimit: '$10,000',
    requirements: ['Basic Verification', 'Government ID', 'Selfie Verification'],
  },
  [KYC_LEVELS.ADVANCED]: {
    level: 3,
    name: 'Advanced Verification',
    description: 'Address verification and enhanced due diligence',
    tradingLimit: 'Unlimited',
    requirements: ['Intermediate Verification', 'Proof of Address', 'Enhanced Due Diligence'],
  },
};

// Mock KYC data - in real app this would come from backend
const mockKYCData = {
  currentLevel: 0,
  status: 'not_started', // 'not_started', 'in_progress', 'under_review', 'approved', 'rejected'
  personalInfo: {
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    nationality: '',
    phoneNumber: '',
    address: {
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
    },
  },
  documents: {
    governmentId: {
      type: '', // 'passport', 'drivers_license', 'national_id'
      frontImage: null,
      backImage: null,
      status: 'not_uploaded',
    },
    selfie: {
      image: null,
      status: 'not_uploaded',
    },
    proofOfAddress: {
      type: '', // 'utility_bill', 'bank_statement', 'rental_agreement'
      image: null,
      status: 'not_uploaded',
    },
  },
  rejectionReasons: [],
};

export default function KYCVerificationScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const {
    kycData,
    isLoading,
    updatePersonalInfo,
    updateDocument,
    submitKYCVerification
  } = useKYC();
  const [currentStep, setCurrentStep] = useState(1);
  const [documentModalVisible, setDocumentModalVisible] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState('');

  const styles = createStyles(theme);

  const handlePersonalInfoChange = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      updatePersonalInfo({
        [parent]: {
          ...kycData.personalInfo[parent],
          [child]: value,
        },
      });
    } else {
      updatePersonalInfo({
        [field]: value,
      });
    }
  };

  const handleDocumentUpload = (documentType: string, imageType: string = 'image') => {
    // In a real app, this would open image picker
    Alert.alert(
      'Document Upload',
      'This would open the camera or photo library to capture/select the document.',
      [
        { text: 'Camera', onPress: () => simulateDocumentUpload(documentType, imageType) },
        { text: 'Photo Library', onPress: () => simulateDocumentUpload(documentType, imageType) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const simulateDocumentUpload = (documentType: string, imageType: string) => {
    // Simulate document upload
    const mockImageUri = `https://api.a0.dev/assets/image?text=${documentType}&aspect=3:2&seed=${documentType}`;

    updateDocument(documentType, {
      [imageType]: mockImageUri,
      status: 'uploaded',
    });

    Alert.alert('Success', 'Document uploaded successfully!');
  };

  const validatePersonalInfo = () => {
    const { personalInfo } = kycData;
    const required = ['firstName', 'lastName', 'dateOfBirth', 'nationality', 'phoneNumber'];
    const addressRequired = ['street', 'city', 'state', 'postalCode', 'country'];

    for (const field of required) {
      if (!personalInfo[field]) {
        Alert.alert('Error', `Please fill in your ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
        return false;
      }
    }

    for (const field of addressRequired) {
      if (!personalInfo.address[field]) {
        Alert.alert('Error', `Please fill in your ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
        return false;
      }
    }

    return true;
  };

  const handleSubmitKYC = async () => {
    if (!validatePersonalInfo()) return;

    const success = await submitKYCVerification();
    if (success) {
      Alert.alert(
        'KYC Submitted',
        'Your verification documents have been submitted for review. You will be notified once the review is complete.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    }
  };

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <View style={[styles.progressFill, { width: `${(currentStep / 3) * 100}%` }]} />
      </View>
      <Text style={styles.progressText}>Step {currentStep} of 3</Text>
    </View>
  );

  const renderKYCLevels = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Verification Levels</Text>
      {Object.values(KYC_LEVEL_INFO).map((level) => (
        <View key={level.level} style={[
          styles.levelCard,
          kycData.currentLevel >= level.level && styles.levelCardCompleted
        ]}>
          <View style={styles.levelHeader}>
            <View style={styles.levelInfo}>
              <Text style={styles.levelName}>{level.name}</Text>
              <Text style={styles.levelDescription}>{level.description}</Text>
            </View>
            <View style={styles.levelStatus}>
              {kycData.currentLevel >= level.level ? (
                <Ionicons name="checkmark-circle" size={24} color={theme.colors.success} />
              ) : (
                <View style={styles.levelNumber}>
                  <Text style={styles.levelNumberText}>{level.level}</Text>
                </View>
              )}
            </View>
          </View>
          <View style={styles.levelDetails}>
            <Text style={styles.levelLimit}>Trading Limit: {level.tradingLimit}</Text>
            <View style={styles.requirementsList}>
              {level.requirements.map((req, index) => (
                <View key={index} style={styles.requirementItem}>
                  <Ionicons 
                    name={kycData.currentLevel >= level.level ? "checkmark" : "ellipse-outline"} 
                    size={16} 
                    color={kycData.currentLevel >= level.level ? theme.colors.success : theme.colors.textSecondary} 
                  />
                  <Text style={styles.requirementText}>{req}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderPersonalInfoForm = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Personal Information</Text>

      <View style={styles.formRow}>
        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>First Name *</Text>
          <TextInput
            style={styles.textInput}
            value={kycData.personalInfo.firstName}
            onChangeText={(value) => handlePersonalInfoChange('firstName', value)}
            placeholder="Enter your first name"
            placeholderTextColor={theme.colors.inputPlaceholder}
          />
        </View>
        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>Last Name *</Text>
          <TextInput
            style={styles.textInput}
            value={kycData.personalInfo.lastName}
            onChangeText={(value) => handlePersonalInfoChange('lastName', value)}
            placeholder="Enter your last name"
            placeholderTextColor={theme.colors.inputPlaceholder}
          />
        </View>
      </View>

      <View style={styles.formField}>
        <Text style={styles.fieldLabel}>Date of Birth *</Text>
        <TouchableOpacity style={styles.dateInput}>
          <Text style={[styles.dateText, !kycData.personalInfo.dateOfBirth && styles.placeholderText]}>
            {kycData.personalInfo.dateOfBirth || 'Select your date of birth'}
          </Text>
          <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      <View style={styles.formField}>
        <Text style={styles.fieldLabel}>Nationality *</Text>
        <TouchableOpacity style={styles.selectInput}>
          <Text style={[styles.selectText, !kycData.personalInfo.nationality && styles.placeholderText]}>
            {kycData.personalInfo.nationality || 'Select your nationality'}
          </Text>
          <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      <View style={styles.formField}>
        <Text style={styles.fieldLabel}>Phone Number *</Text>
        <TextInput
          style={styles.textInput}
          value={kycData.personalInfo.phoneNumber}
          onChangeText={(value) => handlePersonalInfoChange('phoneNumber', value)}
          placeholder="+****************"
          placeholderTextColor={theme.colors.inputPlaceholder}
          keyboardType="phone-pad"
        />
      </View>

      {/* Address Information */}
      <Text style={styles.subsectionTitle}>Address Information</Text>

      <View style={styles.formField}>
        <Text style={styles.fieldLabel}>Street Address *</Text>
        <TextInput
          style={styles.textInput}
          value={kycData.personalInfo.address.street}
          onChangeText={(value) => handlePersonalInfoChange('address.street', value)}
          placeholder="Enter your street address"
          placeholderTextColor={theme.colors.inputPlaceholder}
        />
      </View>

      <View style={styles.formRow}>
        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>City *</Text>
          <TextInput
            style={styles.textInput}
            value={kycData.personalInfo.address.city}
            onChangeText={(value) => handlePersonalInfoChange('address.city', value)}
            placeholder="City"
            placeholderTextColor={theme.colors.inputPlaceholder}
          />
        </View>
        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>State/Province *</Text>
          <TextInput
            style={styles.textInput}
            value={kycData.personalInfo.address.state}
            onChangeText={(value) => handlePersonalInfoChange('address.state', value)}
            placeholder="State"
            placeholderTextColor={theme.colors.inputPlaceholder}
          />
        </View>
      </View>

      <View style={styles.formRow}>
        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>Postal Code *</Text>
          <TextInput
            style={styles.textInput}
            value={kycData.personalInfo.address.postalCode}
            onChangeText={(value) => handlePersonalInfoChange('address.postalCode', value)}
            placeholder="12345"
            placeholderTextColor={theme.colors.inputPlaceholder}
          />
        </View>
        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>Country *</Text>
          <TouchableOpacity style={styles.selectInput}>
            <Text style={[styles.selectText, !kycData.personalInfo.address.country && styles.placeholderText]}>
              {kycData.personalInfo.address.country || 'Select country'}
            </Text>
            <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderDocumentUpload = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Document Verification</Text>
      <Text style={styles.sectionDescription}>
        Please upload clear photos of your documents. Make sure all information is visible and readable.
      </Text>

      {/* Government ID */}
      <View style={styles.documentSection}>
        <Text style={styles.documentTitle}>Government ID *</Text>
        <Text style={styles.documentDescription}>
          Upload a clear photo of your passport, driver's license, or national ID card.
        </Text>

        <View style={styles.documentTypeSelector}>
          {['passport', 'drivers_license', 'national_id'].map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.documentTypeButton,
                kycData.documents.governmentId.type === type && styles.documentTypeButtonActive
              ]}
              onPress={() => updateDocument('governmentId', { type })}
            >
              <Text style={[
                styles.documentTypeText,
                kycData.documents.governmentId.type === type && styles.documentTypeTextActive
              ]}>
                {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.documentUploadRow}>
          <TouchableOpacity
            style={styles.documentUploadCard}
            onPress={() => handleDocumentUpload('governmentId', 'frontImage')}
          >
            {kycData.documents.governmentId.frontImage ? (
              <Image source={{ uri: kycData.documents.governmentId.frontImage }} style={styles.documentImage} />
            ) : (
              <View style={styles.documentPlaceholder}>
                <Ionicons name="camera" size={32} color={theme.colors.textSecondary} />
                <Text style={styles.documentPlaceholderText}>Front Side</Text>
              </View>
            )}
          </TouchableOpacity>

          {kycData.documents.governmentId.type !== 'passport' && (
            <TouchableOpacity
              style={styles.documentUploadCard}
              onPress={() => handleDocumentUpload('governmentId', 'backImage')}
            >
              {kycData.documents.governmentId.backImage ? (
                <Image source={{ uri: kycData.documents.governmentId.backImage }} style={styles.documentImage} />
              ) : (
                <View style={styles.documentPlaceholder}>
                  <Ionicons name="camera" size={32} color={theme.colors.textSecondary} />
                  <Text style={styles.documentPlaceholderText}>Back Side</Text>
                </View>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Selfie Verification */}
      <View style={styles.documentSection}>
        <Text style={styles.documentTitle}>Selfie Verification *</Text>
        <Text style={styles.documentDescription}>
          Take a clear selfie holding your ID document next to your face.
        </Text>

        <TouchableOpacity
          style={styles.selfieUploadCard}
          onPress={() => handleDocumentUpload('selfie', 'image')}
        >
          {kycData.documents.selfie.image ? (
            <Image source={{ uri: kycData.documents.selfie.image }} style={styles.selfieImage} />
          ) : (
            <View style={styles.selfiePlaceholder}>
              <Ionicons name="person" size={48} color={theme.colors.textSecondary} />
              <Text style={styles.selfiePlaceholderText}>Take Selfie with ID</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Proof of Address */}
      <View style={styles.documentSection}>
        <Text style={styles.documentTitle}>Proof of Address</Text>
        <Text style={styles.documentDescription}>
          Upload a recent utility bill, bank statement, or rental agreement (not older than 3 months).
        </Text>

        <TouchableOpacity
          style={styles.documentUploadCard}
          onPress={() => handleDocumentUpload('proofOfAddress', 'image')}
        >
          {kycData.documents.proofOfAddress.image ? (
            <Image source={{ uri: kycData.documents.proofOfAddress.image }} style={styles.documentImage} />
          ) : (
            <View style={styles.documentPlaceholder}>
              <Ionicons name="document" size={32} color={theme.colors.textSecondary} />
              <Text style={styles.documentPlaceholderText}>Upload Document</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>KYC Verification</Text>
        <View style={styles.headerRight} />
      </View>

      {renderProgressBar()}

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {currentStep === 1 && renderKYCLevels()}
        {currentStep === 2 && renderPersonalInfoForm()}
        {currentStep === 3 && renderDocumentUpload()}

        <View style={styles.navigationButtons}>
          {currentStep > 1 && (
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => setCurrentStep(currentStep - 1)}
            >
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity 
            style={[styles.nextButton, currentStep === 1 && styles.fullWidthButton]}
            onPress={() => {
              if (currentStep < 3) {
                setCurrentStep(currentStep + 1);
              } else {
                handleSubmitKYC();
              }
            }}
            disabled={isLoading}
          >
            <Text style={styles.nextButtonText}>
              {isLoading ? 'Submitting...' : currentStep === 3 ? 'Submit for Review' : 'Continue'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 24,
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  levelCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  levelCardCompleted: {
    borderColor: theme.colors.success,
    backgroundColor: `${theme.colors.success}10`,
  },
  levelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  levelInfo: {
    flex: 1,
  },
  levelName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  levelDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  levelStatus: {
    marginLeft: 12,
  },
  levelNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  levelNumberText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  levelDetails: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 12,
  },
  levelLimit: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  requirementsList: {
    gap: 6,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  requirementText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  formRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  formField: {
    flex: 1,
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: theme.colors.inputBackground,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.inputText,
  },
  dateInput: {
    backgroundColor: theme.colors.inputBackground,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    color: theme.colors.inputText,
  },
  selectInput: {
    backgroundColor: theme.colors.inputBackground,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectText: {
    fontSize: 16,
    color: theme.colors.inputText,
  },
  placeholderText: {
    color: theme.colors.inputPlaceholder,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 24,
    marginBottom: 16,
  },
  sectionDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  documentSection: {
    marginBottom: 32,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  documentDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 16,
    lineHeight: 20,
  },
  documentTypeSelector: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  documentTypeButton: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  documentTypeButtonActive: {
    backgroundColor: theme.colors.primaryLight,
    borderColor: theme.colors.primary,
  },
  documentTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  documentTypeTextActive: {
    color: theme.colors.primary,
  },
  documentUploadRow: {
    flexDirection: 'row',
    gap: 12,
  },
  documentUploadCard: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.border,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
  },
  documentPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  documentPlaceholderText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  documentImage: {
    width: '100%',
    height: 100,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  selfieUploadCard: {
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.border,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 160,
  },
  selfiePlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  selfiePlaceholderText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
    textAlign: 'center',
  },
  selfieImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    resizeMode: 'cover',
  },
  navigationButtons: {
    flexDirection: 'row',
    gap: 12,
    paddingVertical: 24,
  },
  backButton: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  nextButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  fullWidthButton: {
    flex: 2,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});
