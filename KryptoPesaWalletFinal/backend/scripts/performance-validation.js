#!/usr/bin/env node

/**
 * Performance Validation Script for KryptoPesa
 * Validates system performance against production requirements
 */

const axios = require('axios');
const WebSocket = require('ws');
const { performance } = require('perf_hooks');
const fs = require('fs').promises;
const path = require('path');

class PerformanceValidator {
  constructor(config) {
    this.config = {
      baseUrl: config.baseUrl || 'https://api.kryptopesa.com',
      wsUrl: config.wsUrl || 'wss://api.kryptopesa.com/ws',
      concurrentUsers: config.concurrentUsers || 10000,
      testDuration: config.testDuration || 300000, // 5 minutes
      ...config
    };
    
    this.results = {
      timestamp: new Date().toISOString(),
      config: this.config,
      tests: {},
      summary: {},
      passed: false
    };
    
    this.requirements = {
      responseTime: {
        p95: 1000,  // 95th percentile under 1 second
        p99: 2000,  // 99th percentile under 2 seconds
        avg: 500    // Average under 500ms
      },
      throughput: {
        min: 1000,  // Minimum 1000 requests per second
        target: 5000 // Target 5000 requests per second
      },
      errorRate: {
        max: 0.01   // Maximum 1% error rate
      },
      availability: {
        min: 0.999  // Minimum 99.9% availability
      },
      concurrency: {
        target: 10000 // Support 10,000 concurrent users
      }
    };
  }

  async runValidation() {
    console.log('🚀 Starting KryptoPesa Performance Validation');
    console.log(`Target: ${this.config.concurrentUsers} concurrent users`);
    console.log(`Duration: ${this.config.testDuration / 1000} seconds`);
    console.log('=' * 60);

    try {
      // Pre-validation checks
      await this.preValidationChecks();
      
      // Core performance tests
      await this.validateResponseTimes();
      await this.validateThroughput();
      await this.validateConcurrency();
      await this.validateWebSocketPerformance();
      await this.validateDatabasePerformance();
      await this.validateCachePerformance();
      
      // System resource validation
      await this.validateSystemResources();
      
      // Generate final report
      await this.generateReport();
      
    } catch (error) {
      console.error('❌ Validation failed:', error.message);
      this.results.error = error.message;
    }
  }

  async preValidationChecks() {
    console.log('🔍 Running pre-validation checks...');
    
    const checks = [
      { name: 'API Health', url: '/health' },
      { name: 'Database Health', url: '/health/database' },
      { name: 'Cache Health', url: '/health/cache' },
      { name: 'WebSocket Health', url: '/health/websocket' }
    ];

    const results = [];
    
    for (const check of checks) {
      try {
        const start = performance.now();
        const response = await axios.get(`${this.config.baseUrl}${check.url}`, {
          timeout: 5000
        });
        const duration = performance.now() - start;
        
        results.push({
          name: check.name,
          status: response.status === 200 ? 'PASS' : 'FAIL',
          responseTime: Math.round(duration),
          healthy: response.data.healthy !== false
        });
        
        console.log(`  ✅ ${check.name}: ${Math.round(duration)}ms`);
      } catch (error) {
        results.push({
          name: check.name,
          status: 'FAIL',
          error: error.message
        });
        console.log(`  ❌ ${check.name}: ${error.message}`);
      }
    }
    
    this.results.tests.preValidation = results;
  }

  async validateResponseTimes() {
    console.log('⏱️  Validating response times...');
    
    const endpoints = [
      { path: '/api/auth/login', method: 'POST', category: 'auth' },
      { path: '/api/trading/offers', method: 'GET', category: 'trading' },
      { path: '/api/wallet/balances', method: 'GET', category: 'wallet' },
      { path: '/api/chat/conversations', method: 'GET', category: 'chat' }
    ];

    const measurements = [];
    const iterations = 100;

    for (const endpoint of endpoints) {
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        try {
          const start = performance.now();
          
          if (endpoint.method === 'GET') {
            await axios.get(`${this.config.baseUrl}${endpoint.path}`, {
              timeout: 10000,
              headers: { 'Authorization': 'Bearer test-token' }
            });
          } else {
            await axios.post(`${this.config.baseUrl}${endpoint.path}`, {
              email: '<EMAIL>',
              password: 'password'
            }, { timeout: 10000 });
          }
          
          const duration = performance.now() - start;
          times.push(duration);
        } catch (error) {
          // Count errors but continue testing
          times.push(10000); // Penalty for errors
        }
      }
      
      times.sort((a, b) => a - b);
      
      const stats = {
        endpoint: endpoint.path,
        category: endpoint.category,
        avg: times.reduce((a, b) => a + b, 0) / times.length,
        p50: times[Math.floor(times.length * 0.5)],
        p95: times[Math.floor(times.length * 0.95)],
        p99: times[Math.floor(times.length * 0.99)],
        min: times[0],
        max: times[times.length - 1]
      };
      
      measurements.push(stats);
      
      console.log(`  📊 ${endpoint.path}:`);
      console.log(`     Avg: ${Math.round(stats.avg)}ms | P95: ${Math.round(stats.p95)}ms | P99: ${Math.round(stats.p99)}ms`);
    }
    
    this.results.tests.responseTimes = measurements;
    
    // Validate against requirements
    const overallP95 = measurements.reduce((sum, m) => sum + m.p95, 0) / measurements.length;
    const overallP99 = measurements.reduce((sum, m) => sum + m.p99, 0) / measurements.length;
    const overallAvg = measurements.reduce((sum, m) => sum + m.avg, 0) / measurements.length;
    
    const passed = overallP95 <= this.requirements.responseTime.p95 &&
                   overallP99 <= this.requirements.responseTime.p99 &&
                   overallAvg <= this.requirements.responseTime.avg;
    
    console.log(`  ${passed ? '✅' : '❌'} Response Time Validation: ${passed ? 'PASSED' : 'FAILED'}`);
    
    this.results.tests.responseTimeValidation = {
      passed,
      measurements: { overallP95, overallP99, overallAvg },
      requirements: this.requirements.responseTime
    };
  }

  async validateThroughput() {
    console.log('🚄 Validating throughput...');
    
    const testDuration = 60000; // 1 minute
    const concurrency = 50;
    let requestCount = 0;
    let errorCount = 0;
    
    const startTime = performance.now();
    const promises = [];
    
    // Create concurrent request streams
    for (let i = 0; i < concurrency; i++) {
      promises.push(this.throughputWorker(testDuration, (success) => {
        requestCount++;
        if (!success) errorCount++;
      }));
    }
    
    await Promise.all(promises);
    
    const actualDuration = performance.now() - startTime;
    const throughput = (requestCount / actualDuration) * 1000; // requests per second
    const errorRate = errorCount / requestCount;
    
    const passed = throughput >= this.requirements.throughput.min &&
                   errorRate <= this.requirements.errorRate.max;
    
    console.log(`  📈 Throughput: ${Math.round(throughput)} req/s`);
    console.log(`  📉 Error Rate: ${(errorRate * 100).toFixed(2)}%`);
    console.log(`  ${passed ? '✅' : '❌'} Throughput Validation: ${passed ? 'PASSED' : 'FAILED'}`);
    
    this.results.tests.throughput = {
      passed,
      throughput,
      errorRate,
      requestCount,
      errorCount,
      requirements: this.requirements.throughput
    };
  }

  async throughputWorker(duration, callback) {
    const endTime = performance.now() + duration;
    
    while (performance.now() < endTime) {
      try {
        await axios.get(`${this.config.baseUrl}/api/public/stats`, {
          timeout: 5000
        });
        callback(true);
      } catch (error) {
        callback(false);
      }
      
      // Small delay to prevent overwhelming
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  async validateConcurrency() {
    console.log('👥 Validating concurrency...');
    
    const targetConcurrency = Math.min(this.config.concurrentUsers, 1000); // Limit for testing
    const promises = [];
    let successCount = 0;
    let errorCount = 0;
    
    const startTime = performance.now();
    
    // Create concurrent connections
    for (let i = 0; i < targetConcurrency; i++) {
      promises.push(
        axios.get(`${this.config.baseUrl}/api/public/stats`, {
          timeout: 10000
        }).then(() => {
          successCount++;
        }).catch(() => {
          errorCount++;
        })
      );
    }
    
    await Promise.all(promises);
    
    const duration = performance.now() - startTime;
    const successRate = successCount / targetConcurrency;
    
    const passed = successRate >= 0.95; // 95% success rate
    
    console.log(`  🔗 Concurrent Requests: ${targetConcurrency}`);
    console.log(`  ✅ Success Rate: ${(successRate * 100).toFixed(2)}%`);
    console.log(`  ⏱️  Total Duration: ${Math.round(duration)}ms`);
    console.log(`  ${passed ? '✅' : '❌'} Concurrency Validation: ${passed ? 'PASSED' : 'FAILED'}`);
    
    this.results.tests.concurrency = {
      passed,
      targetConcurrency,
      successCount,
      errorCount,
      successRate,
      duration
    };
  }

  async validateWebSocketPerformance() {
    console.log('🔌 Validating WebSocket performance...');
    
    const connectionCount = 100;
    const messageDuration = 30000; // 30 seconds
    const connections = [];
    let messagesReceived = 0;
    let connectionErrors = 0;
    
    const startTime = performance.now();
    
    // Create WebSocket connections
    for (let i = 0; i < connectionCount; i++) {
      try {
        const ws = new WebSocket(`${this.config.wsUrl}?test=true`);
        
        ws.on('open', () => {
          ws.send(JSON.stringify({ type: 'ping', id: i }));
        });
        
        ws.on('message', () => {
          messagesReceived++;
        });
        
        ws.on('error', () => {
          connectionErrors++;
        });
        
        connections.push(ws);
      } catch (error) {
        connectionErrors++;
      }
    }
    
    // Wait for test duration
    await new Promise(resolve => setTimeout(resolve, messageDuration));
    
    // Close connections
    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    });
    
    const duration = performance.now() - startTime;
    const successfulConnections = connectionCount - connectionErrors;
    const connectionSuccessRate = successfulConnections / connectionCount;
    
    const passed = connectionSuccessRate >= 0.95 && messagesReceived > 0;
    
    console.log(`  🔗 WebSocket Connections: ${connectionCount}`);
    console.log(`  ✅ Successful Connections: ${successfulConnections}`);
    console.log(`  📨 Messages Received: ${messagesReceived}`);
    console.log(`  ${passed ? '✅' : '❌'} WebSocket Validation: ${passed ? 'PASSED' : 'FAILED'}`);
    
    this.results.tests.websocket = {
      passed,
      connectionCount,
      successfulConnections,
      messagesReceived,
      connectionErrors,
      connectionSuccessRate
    };
  }

  async validateDatabasePerformance() {
    console.log('🗄️  Validating database performance...');
    
    try {
      const response = await axios.get(`${this.config.baseUrl}/api/admin/database/stats`, {
        timeout: 10000,
        headers: { 'Authorization': 'Bearer admin-token' }
      });
      
      const stats = response.data;
      const passed = stats.connectionPool?.active < stats.connectionPool?.max * 0.8 &&
                     stats.queryPerformance?.avgResponseTime < 100;
      
      console.log(`  📊 Active Connections: ${stats.connectionPool?.active || 'N/A'}`);
      console.log(`  ⏱️  Avg Query Time: ${stats.queryPerformance?.avgResponseTime || 'N/A'}ms`);
      console.log(`  ${passed ? '✅' : '❌'} Database Validation: ${passed ? 'PASSED' : 'FAILED'}`);
      
      this.results.tests.database = {
        passed,
        stats
      };
    } catch (error) {
      console.log(`  ❌ Database Validation: FAILED (${error.message})`);
      this.results.tests.database = {
        passed: false,
        error: error.message
      };
    }
  }

  async validateCachePerformance() {
    console.log('🚀 Validating cache performance...');
    
    try {
      const response = await axios.get(`${this.config.baseUrl}/api/admin/cache/stats`, {
        timeout: 10000,
        headers: { 'Authorization': 'Bearer admin-token' }
      });
      
      const stats = response.data;
      const hitRate = parseFloat(stats.hitRate?.replace('%', '') || '0');
      const passed = hitRate >= 80; // 80% hit rate minimum
      
      console.log(`  🎯 Cache Hit Rate: ${stats.hitRate || 'N/A'}`);
      console.log(`  📊 Total Entries: ${stats.totalEntries || 'N/A'}`);
      console.log(`  ${passed ? '✅' : '❌'} Cache Validation: ${passed ? 'PASSED' : 'FAILED'}`);
      
      this.results.tests.cache = {
        passed,
        stats
      };
    } catch (error) {
      console.log(`  ❌ Cache Validation: FAILED (${error.message})`);
      this.results.tests.cache = {
        passed: false,
        error: error.message
      };
    }
  }

  async validateSystemResources() {
    console.log('💻 Validating system resources...');
    
    try {
      const response = await axios.get(`${this.config.baseUrl}/api/admin/system/stats`, {
        timeout: 10000,
        headers: { 'Authorization': 'Bearer admin-token' }
      });
      
      const stats = response.data;
      const passed = stats.cpu?.usage < 80 && 
                     stats.memory?.usage < 85 &&
                     stats.disk?.usage < 90;
      
      console.log(`  🖥️  CPU Usage: ${stats.cpu?.usage || 'N/A'}%`);
      console.log(`  🧠 Memory Usage: ${stats.memory?.usage || 'N/A'}%`);
      console.log(`  💾 Disk Usage: ${stats.disk?.usage || 'N/A'}%`);
      console.log(`  ${passed ? '✅' : '❌'} System Resources: ${passed ? 'PASSED' : 'FAILED'}`);
      
      this.results.tests.systemResources = {
        passed,
        stats
      };
    } catch (error) {
      console.log(`  ❌ System Resources: FAILED (${error.message})`);
      this.results.tests.systemResources = {
        passed: false,
        error: error.message
      };
    }
  }

  async generateReport() {
    console.log('\n📋 Generating Performance Report...');
    
    // Calculate overall pass/fail
    const testResults = Object.values(this.results.tests);
    const passedTests = testResults.filter(test => test.passed !== false).length;
    const totalTests = testResults.length;
    const overallPassed = passedTests === totalTests;
    
    this.results.summary = {
      overallPassed,
      passedTests,
      totalTests,
      passRate: (passedTests / totalTests * 100).toFixed(2) + '%',
      completedAt: new Date().toISOString()
    };
    
    this.results.passed = overallPassed;
    
    // Save report to file
    const reportPath = path.join(__dirname, '../reports', `performance-report-${Date.now()}.json`);
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(this.results, null, 2));
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 PERFORMANCE VALIDATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`Overall Result: ${overallPassed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Tests Passed: ${passedTests}/${totalTests} (${this.results.summary.passRate})`);
    console.log(`Report saved: ${reportPath}`);
    console.log('='.repeat(60));
    
    if (!overallPassed) {
      console.log('\n❌ Failed Tests:');
      testResults.forEach(test => {
        if (test.passed === false) {
          console.log(`  - ${test.name || 'Unknown test'}: ${test.error || 'Performance requirements not met'}`);
        }
      });
    }
    
    return this.results;
  }
}

// CLI execution
if (require.main === module) {
  const config = {
    baseUrl: process.env.BASE_URL || 'http://localhost:3001',
    wsUrl: process.env.WS_URL || 'ws://localhost:3001/ws',
    concurrentUsers: parseInt(process.env.CONCURRENT_USERS) || 1000,
    testDuration: parseInt(process.env.TEST_DURATION) || 300000
  };
  
  const validator = new PerformanceValidator(config);
  
  validator.runValidation()
    .then(results => {
      process.exit(results.passed ? 0 : 1);
    })
    .catch(error => {
      console.error('Validation failed:', error);
      process.exit(1);
    });
}

module.exports = PerformanceValidator;
