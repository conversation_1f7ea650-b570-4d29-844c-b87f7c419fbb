{"version": 3, "file": "static/js/209.c7cd9cbd.chunk.js", "mappings": "+KAIA,SAAeA,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,8CACD,U,mWCFJ,SAAeF,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,0KACD,U,qDCkCJ,MAsYA,EAtYmBC,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACvB,MAAOC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAC9BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,OAC5BK,EAAMC,IAAWN,EAAAA,EAAAA,UAAS,IAC1BO,EAAaC,KAAkBR,EAAAA,EAAAA,UAAS,KACxCS,GAAaC,KAAkBV,EAAAA,EAAAA,UAAS,IACxCW,GAAYC,KAAiBZ,EAAAA,EAAAA,UAAS,KACtCa,GAAcC,KAAmBd,EAAAA,EAAAA,UAAS,KAC1Ce,GAAeC,KAAoBhB,EAAAA,EAAAA,UAAS,OAC5CiB,GAAiBC,KAAsBlB,EAAAA,EAAAA,WAAS,GAGjDmB,GAAcC,UAClB,IACElB,GAAW,GACX,MAAMmB,EAAS,CACbhB,KAAMA,EAAO,EACbiB,MAAOf,EACPgB,OAAQZ,SAAca,EACtBC,OAAQZ,SAAgBW,EACxBE,OAAQ,YACRC,UAAW,QAGPC,QAAiBC,EAAAA,EAAMC,IAAI,gBAAiB,CAAET,WAEhDO,EAASG,KAAKC,UAChBjC,EAAU6B,EAASG,KAAKA,KAAKjC,QAC7BY,GAAekB,EAASG,KAAKA,KAAKE,WAAWxB,aAEjD,CAAE,MAAON,GACP+B,QAAQ/B,MAAM,yBAA0BA,GACxCC,EAAS,yBACX,CAAC,QACCF,GAAW,EACb,IAGFiC,EAAAA,EAAAA,WAAU,KACRhB,MACC,CAACd,EAAME,EAAaI,GAAYE,KAEnC,MAcMuB,GAAoBhB,MAAOiB,EAAOC,KACtC,WACyBT,EAAAA,EAAMU,KAAK,iBAADC,OAAkBH,EAAMI,QAAO,cAAc,CAC5EH,SACAI,OAAO,SAADF,OAAWF,EAAM,cAGZP,KAAKC,SAChBb,IAEJ,CAAE,MAAOhB,GACP+B,QAAQ/B,MAAM,iCAAkCA,EAClD,GAGIwC,GAAkBlB,IACtB,OAAQA,GACN,IAAK,YAAa,MAAO,UACzB,IAAK,YAAa,MAAO,QACzB,IAAK,WAAY,MAAO,UACxB,IAAK,SAAU,MAAO,OACtB,IAAK,SAAU,MAAO,UACtB,QAAS,MAAO,YAIdmB,GAAiBnB,IACrB,OAAQA,GACN,IAAK,YAAa,OAAOvC,EAAAA,EAAAA,KAAC2D,EAAAA,EAAY,IACtC,IAAK,YAAa,OAAO3D,EAAAA,EAAAA,KAAC4D,EAAU,IACpC,IAAK,WAAY,OAAO5D,EAAAA,EAAAA,KAAC6D,EAAAA,EAAW,IACpC,QAAS,OAAO,OAIdC,GAAeA,CAACC,EAAQC,IACtB,GAANV,OAAUW,WAAWF,GAAQG,QAAQ,GAAE,KAAAZ,OAAIU,GAGvCG,GAAcC,GACX,IAAIC,KAAKD,GAAYE,mBAAmB,QAAS,CACtDC,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,YAIZ,OACEC,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,EACF9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,sBAKtCF,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAGC,GAAI,EAAEP,SAAA,EAChC9E,EAAAA,EAAAA,KAACkF,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAEX,UAC9B9E,EAAAA,EAAAA,KAAC0F,EAAAA,EAAI,CAAAZ,UACHF,EAAAA,EAAAA,MAACe,EAAAA,EAAW,CAAAb,SAAA,EACV9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACa,MAAM,gBAAgBX,cAAY,EAAAH,SAAC,kBAG/C9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,KAAIF,SACrBvD,GAAYsE,2BAKrB7F,EAAAA,EAAAA,KAACkF,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAEX,UAC9B9E,EAAAA,EAAAA,KAAC0F,EAAAA,EAAI,CAAAZ,UACHF,EAAAA,EAAAA,MAACe,EAAAA,EAAW,CAAAb,SAAA,EACV9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACa,MAAM,gBAAgBX,cAAY,EAAAH,SAAC,mBAG/C9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,KAAIF,SACrBlE,EAAOkF,OAAOC,GAAK,CAAC,UAAW,SAAU,gBAAgBC,SAASD,EAAExD,SAAS0D,iBAKtFjG,EAAAA,EAAAA,KAACkF,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAEX,UAC9B9E,EAAAA,EAAAA,KAAC0F,EAAAA,EAAI,CAAAZ,UACHF,EAAAA,EAAAA,MAACe,EAAAA,EAAW,CAAAb,SAAA,EACV9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACa,MAAM,gBAAgBX,cAAY,EAAAH,SAAC,qBAG/C9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,KAAIF,SACrBlE,EAAOkF,OAAOC,GACA,cAAbA,EAAExD,QACF,IAAI8B,KAAK0B,EAAEG,WAAWC,kBAAmB,IAAI9B,MAAO8B,gBACpDF,iBAKVjG,EAAAA,EAAAA,KAACkF,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAEX,UAC9B9E,EAAAA,EAAAA,KAAC0F,EAAAA,EAAI,CAAAZ,UACHF,EAAAA,EAAAA,MAACe,EAAAA,EAAW,CAAAb,SAAA,EACV9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACa,MAAM,gBAAgBX,cAAY,EAAAH,SAAC,cAG/C9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,KAAKY,MAAM,eAAcd,SAC1ClE,EAAOkF,OAAOC,GAAkB,aAAbA,EAAExD,QAAuB0D,oBAQvDjG,EAAAA,EAAAA,KAACoG,EAAAA,EAAK,CAACC,GAAI,CAAEC,EAAG,EAAGjB,GAAI,GAAIP,UACzBF,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAGmB,WAAW,SAAQzB,SAAA,EAC7C9E,EAAAA,EAAAA,KAACkF,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,UACvB9E,EAAAA,EAAAA,KAACwG,EAAAA,EAAS,CACRC,WAAS,EACTC,YAAY,mBACZC,MAAOlF,GACPmF,SAAWC,GAAMnF,GAAcmF,EAAEC,OAAOH,OACxCI,WAAY,CACVC,gBACEhH,EAAAA,EAAAA,KAACiH,EAAAA,EAAc,CAACC,SAAS,QAAOpC,UAC9B9E,EAAAA,EAAAA,KAACmH,EAAAA,EAAU,YAMrBnH,EAAAA,EAAAA,KAACkF,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,UACvBF,EAAAA,EAAAA,MAACwC,EAAAA,EAAW,CAACX,WAAS,EAAA3B,SAAA,EACpB9E,EAAAA,EAAAA,KAACqH,EAAAA,EAAU,CAAAvC,SAAC,YACZF,EAAAA,EAAAA,MAAC0C,EAAAA,EAAM,CACLX,MAAOhF,GACPiF,SAAWC,GAAMjF,GAAgBiF,EAAEC,OAAOH,OAC1CY,MAAM,SAAQzC,SAAA,EAEd9E,EAAAA,EAAAA,KAACwH,EAAAA,EAAQ,CAACb,MAAM,GAAE7B,SAAC,kBACnB9E,EAAAA,EAAAA,KAACwH,EAAAA,EAAQ,CAACb,MAAM,UAAS7B,SAAC,aAC1B9E,EAAAA,EAAAA,KAACwH,EAAAA,EAAQ,CAACb,MAAM,SAAQ7B,SAAC,YACzB9E,EAAAA,EAAAA,KAACwH,EAAAA,EAAQ,CAACb,MAAM,eAAc7B,SAAC,kBAC/B9E,EAAAA,EAAAA,KAACwH,EAAAA,EAAQ,CAACb,MAAM,YAAW7B,SAAC,eAC5B9E,EAAAA,EAAAA,KAACwH,EAAAA,EAAQ,CAACb,MAAM,WAAU7B,SAAC,cAC3B9E,EAAAA,EAAAA,KAACwH,EAAAA,EAAQ,CAACb,MAAM,YAAW7B,SAAC,uBAIlC9E,EAAAA,EAAAA,KAACkF,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,UACvB9E,EAAAA,EAAAA,KAACyH,EAAAA,EAAM,CACLzC,QAAQ,WACR0C,QAASA,KACPhG,GAAc,IACdE,GAAgB,KAChBkD,SACH,0BAQPF,EAAAA,EAAAA,MAAC+C,EAAAA,EAAc,CAACC,UAAWxB,EAAAA,EAAMtB,SAAA,EAC/BF,EAAAA,EAAAA,MAACiD,EAAAA,EAAK,CAAA/C,SAAA,EACJ9E,EAAAA,EAAAA,KAAC8H,EAAAA,EAAS,CAAAhD,UACRF,EAAAA,EAAAA,MAACmD,EAAAA,EAAQ,CAAAjD,SAAA,EACP9E,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,SAAC,cACX9E,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,SAAC,kBACX9E,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,SAAC,YACX9E,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,SAAC,YACX9E,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,SAAC,aACX9E,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,SAAC,aACX9E,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,SAAC,kBAGf9E,EAAAA,EAAAA,KAACiI,EAAAA,EAAS,CAAAnD,SACPlE,EAAOsH,IAAK/E,IAAK,IAAAgF,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAChB5D,EAAAA,EAAAA,MAACmD,EAAAA,EAAQ,CAAiBU,OAAK,EAAA3D,SAAA,EAC7B9E,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,UACR9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,QAAQ0D,WAAW,YAAW5D,SAC/C3B,EAAMI,aAGXvD,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,UACRF,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,EACFF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SAAA,EACzB9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,YAAgB,KAAc,QAAZqD,EAAAhF,EAAMwF,cAAM,IAAAR,OAAA,EAAZA,EAAcS,WAAY,cAEtDhE,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SAAA,EACzB9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,WAAe,KAAa,QAAXsD,EAAAjF,EAAM0F,aAAK,IAAAT,OAAA,EAAXA,EAAaQ,WAAY,mBAIxD5I,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,UACRF,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAAC,SAAA,EACF9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SACxBhB,GAAiC,QAArBuE,EAAClF,EAAM2F,sBAAc,IAAAT,OAAA,EAApBA,EAAsBtE,OAA4B,QAAtBuE,EAAEnF,EAAM2F,sBAAc,IAAAR,OAAA,EAApBA,EAAsBS,WAEpEnE,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAACC,QAAQ,UAAUY,MAAM,gBAAed,SAAA,CAAC,UAC/ChB,GAAuB,QAAXyE,EAACpF,EAAM6F,YAAI,IAAAT,OAAA,EAAVA,EAAYxE,OAAkB,QAAZyE,EAAErF,EAAM6F,YAAI,IAAAR,OAAA,EAAVA,EAAYxE,mBAItDhE,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,UACR9E,EAAAA,EAAAA,KAACiJ,EAAAA,EAAI,CACHC,KAAMxF,GAAcP,EAAMZ,QAC1BgF,MAAOpE,EAAMZ,OAAO4G,QAAQ,IAAK,KACjCvD,MAAOnC,GAAeN,EAAMZ,QAC5B6G,KAAK,aAGTpJ,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,UACR9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SACxBX,GAAWhB,EAAMkG,gBAGtBrJ,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,UACR9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,QAAOF,SACxBX,GAAWhB,EAAM+C,gBAGtBlG,EAAAA,EAAAA,KAACgI,EAAAA,EAAS,CAAAlD,UACRF,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACyE,QAAQ,OAAOC,IAAK,EAAEzE,SAAA,EACzB9E,EAAAA,EAAAA,KAACwJ,EAAAA,EAAO,CAACC,MAAM,eAAc3E,UAC3B9E,EAAAA,EAAAA,KAAC0J,EAAAA,EAAU,CACTN,KAAK,QACL1B,QAASA,IAtONvE,KACvBrB,GAAiBqB,GACjBnB,IAAmB,IAoOgB2H,CAAgBxG,GAAO2B,UAEtC9E,EAAAA,EAAAA,KAAC4J,EAAAA,EAAQ,QAIK,aAAjBzG,EAAMZ,SACLvC,EAAAA,EAAAA,KAACwJ,EAAAA,EAAO,CAACC,MAAM,iBAAgB3E,UAC7B9E,EAAAA,EAAAA,KAAC0J,EAAAA,EAAU,CACTN,KAAK,QACLxD,MAAM,UACN8B,QAASA,IAAMxE,GAAkBC,EAAO,kBAAkB2B,UAE1D9E,EAAAA,EAAAA,KAAC2D,EAAAA,EAAY,QAKlB,CAAC,UAAW,SAAU,gBAAgBqC,SAAS7C,EAAMZ,UACpDvC,EAAAA,EAAAA,KAACwJ,EAAAA,EAAO,CAACC,MAAM,eAAc3E,UAC3B9E,EAAAA,EAAAA,KAAC0J,EAAAA,EAAU,CACTN,KAAK,QACLxD,MAAM,QACN8B,QAASA,IAAMxE,GAAkBC,EAAO,UAAU2B,UAElD9E,EAAAA,EAAAA,KAAC6J,EAAAA,EAAS,eA1EP1G,EAAM2G,aAqF3B9J,EAAAA,EAAAA,KAAC+J,EAAAA,EAAe,CACdC,mBAAoB,CAAC,GAAI,GAAI,GAAI,KACjCpC,UAAU,MACVqC,MAAO1I,GACPF,YAAaA,EACbF,KAAMA,EACN+I,aAzRiBC,CAACC,EAAOC,KAC/BjJ,EAAQiJ,IAyRFC,oBAtRyBF,IAC/B9I,GAAeiJ,SAASH,EAAMtD,OAAOH,MAAO,KAC5CvF,EAAQ,UAyRNwD,EAAAA,EAAAA,MAAC4F,EAAAA,EAAM,CACLC,KAAM1I,GACN2I,QAASA,IAAM1I,IAAmB,GAClC2I,SAAS,KACTlE,WAAS,EAAA3B,SAAA,EAETF,EAAAA,EAAAA,MAACgG,EAAAA,EAAW,CAAA9F,SAAA,CAAC,mBACmB,OAAbjD,SAAa,IAAbA,QAAa,EAAbA,GAAe0B,YAElCvD,EAAAA,EAAAA,KAAC6K,EAAAA,EAAa,CAAA/F,SACXjD,KACC7B,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAAAC,UACFF,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAEN,SAAA,EACzBF,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,SAAA,EACvB9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,uBAGtCF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EAAC9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,YAAgB,IAAEjD,GAAcU,WACpDqC,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EAAC9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,aAAiB,IAAEX,GAAWtC,GAAcwH,eAChEzE,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EAAC9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,aAAiB,IAAEX,GAAWtC,GAAcqE,kBAElEtB,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAIE,GAAI,EAAEX,SAAA,EACvB9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,oBAGtCF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,YAAgB,IAAEhB,GAAyC,QAA7B3D,EAAC0B,GAAciH,sBAAc,IAAA3I,OAAA,EAA5BA,EAA8B4D,OAAoC,QAA9B3D,EAAEyB,GAAciH,sBAAc,IAAA1I,OAAA,EAA5BA,EAA8B2I,YAE7GnE,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,UAAc,IAAEhB,GAA+B,QAAnBzD,EAACwB,GAAcmH,YAAI,IAAA3I,OAAA,EAAlBA,EAAoB0D,OAA0B,QAApBzD,EAAEuB,GAAcmH,YAAI,IAAA1I,OAAA,EAAlBA,EAAoB0D,cAEvFY,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,UAAc,IAAoB,QAAnBvE,EAACsB,GAAcmH,YAAI,IAAAzI,OAAA,EAAlBA,EAAoBuK,oBAGhDlG,EAAAA,EAAAA,MAACM,EAAAA,GAAI,CAACI,MAAI,EAACC,GAAI,GAAGT,SAAA,EAChB9E,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAH,SAAC,kBAGtCF,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,YAAgB,IAAsB,QAArBtE,EAACqB,GAAc8G,cAAM,IAAAnI,OAAA,EAApBA,EAAsBoI,SAAS,KAAuB,QAArBnI,EAACoB,GAAc8G,cAAM,IAAAlI,OAAA,EAApBA,EAAsBsK,MAAM,QAE1FnG,EAAAA,EAAAA,MAACG,EAAAA,EAAU,CAAAD,SAAA,EACT9E,EAAAA,EAAAA,KAAA,UAAA8E,SAAQ,WAAe,IAAqB,QAApBpE,EAACmB,GAAcgH,aAAK,IAAAnI,OAAA,EAAnBA,EAAqBkI,SAAS,KAAsB,QAApBjI,EAACkB,GAAcgH,aAAK,IAAAlI,OAAA,EAAnBA,EAAqBoK,MAAM,kBAOjG/K,EAAAA,EAAAA,KAACgL,EAAAA,EAAa,CAAAlG,UACZ9E,EAAAA,EAAAA,KAACyH,EAAAA,EAAM,CAACC,QAASA,IAAM1F,IAAmB,GAAO8C,SAAC,kB", "sources": ["../node_modules/@mui/icons-material/esm/Warning.js", "../node_modules/@mui/icons-material/esm/Cancel.js", "pages/TradesPage.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M1 21h22L12 2zm12-3h-2v-2h2zm0-4h-2v-4h2z\"\n}), 'Warning');", "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z\"\n}), 'Cancel');", "import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  Chip,\n  IconButton,\n  Tooltip,\n  TextField,\n  InputAdornment,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Visibility as ViewIcon,\n  Block as BlockIcon,\n  CheckCircle as CompleteIcon,\n  Cancel as CancelIcon,\n  Warning as DisputeIcon,\n} from '@mui/icons-material';\nimport axios from 'axios';\n\nconst TradesPage = () => {\n  const [trades, setTrades] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  const [totalTrades, setTotalTrades] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [selectedTrade, setSelectedTrade] = useState(null);\n  const [tradeDetailOpen, setTradeDetailOpen] = useState(false);\n\n  // Fetch trades data\n  const fetchTrades = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: page + 1,\n        limit: rowsPerPage,\n        search: searchTerm || undefined,\n        status: statusFilter || undefined,\n        sortBy: 'createdAt',\n        sortOrder: 'desc'\n      };\n\n      const response = await axios.get('/admin/trades', { params });\n\n      if (response.data.success) {\n        setTrades(response.data.data.trades);\n        setTotalTrades(response.data.data.pagination.totalTrades);\n      }\n    } catch (error) {\n      console.error('Error fetching trades:', error);\n      setError('Failed to fetch trades');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchTrades();\n  }, [page, rowsPerPage, searchTerm, statusFilter]);\n\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  const handleChangeRowsPerPage = (event) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  const handleViewTrade = (trade) => {\n    setSelectedTrade(trade);\n    setTradeDetailOpen(true);\n  };\n\n  const handleTradeAction = async (trade, action) => {\n    try {\n      const response = await axios.post(`/admin/trades/${trade.tradeId}/intervene`, {\n        action,\n        reason: `Admin ${action} action`\n      });\n\n      if (response.data.success) {\n        fetchTrades(); // Refresh the list\n      }\n    } catch (error) {\n      console.error('Error performing trade action:', error);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed': return 'success';\n      case 'cancelled': return 'error';\n      case 'disputed': return 'warning';\n      case 'active': return 'info';\n      case 'funded': return 'primary';\n      default: return 'default';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'completed': return <CompleteIcon />;\n      case 'cancelled': return <CancelIcon />;\n      case 'disputed': return <DisputeIcon />;\n      default: return null;\n    }\n  };\n\n  const formatAmount = (amount, currency) => {\n    return `${parseFloat(amount).toFixed(2)} ${currency}`;\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Trade Management\n      </Typography>\n\n      {/* Summary Cards */}\n      <Grid container spacing={3} mb={3}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                Total Trades\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalTrades.toLocaleString()}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                Active Trades\n              </Typography>\n              <Typography variant=\"h4\">\n                {trades.filter(t => ['created', 'funded', 'payment_sent'].includes(t.status)).length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                Completed Today\n              </Typography>\n              <Typography variant=\"h4\">\n                {trades.filter(t =>\n                  t.status === 'completed' &&\n                  new Date(t.updatedAt).toDateString() === new Date().toDateString()\n                ).length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                Disputes\n              </Typography>\n              <Typography variant=\"h4\" color=\"warning.main\">\n                {trades.filter(t => t.status === 'disputed').length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Filters */}\n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={12} md={4}>\n            <TextField\n              fullWidth\n              placeholder=\"Search trades...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <FormControl fullWidth>\n              <InputLabel>Status</InputLabel>\n              <Select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                label=\"Status\"\n              >\n                <MenuItem value=\"\">All Statuses</MenuItem>\n                <MenuItem value=\"created\">Created</MenuItem>\n                <MenuItem value=\"funded\">Funded</MenuItem>\n                <MenuItem value=\"payment_sent\">Payment Sent</MenuItem>\n                <MenuItem value=\"completed\">Completed</MenuItem>\n                <MenuItem value=\"disputed\">Disputed</MenuItem>\n                <MenuItem value=\"cancelled\">Cancelled</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={2}>\n            <Button\n              variant=\"outlined\"\n              onClick={() => {\n                setSearchTerm('');\n                setStatusFilter('');\n              }}\n            >\n              Clear Filters\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Trades Table */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Trade ID</TableCell>\n              <TableCell>Participants</TableCell>\n              <TableCell>Amount</TableCell>\n              <TableCell>Status</TableCell>\n              <TableCell>Created</TableCell>\n              <TableCell>Updated</TableCell>\n              <TableCell>Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {trades.map((trade) => (\n              <TableRow key={trade._id} hover>\n                <TableCell>\n                  <Typography variant=\"body2\" fontFamily=\"monospace\">\n                    {trade.tradeId}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Box>\n                    <Typography variant=\"body2\">\n                      <strong>Seller:</strong> {trade.seller?.username || 'Unknown'}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      <strong>Buyer:</strong> {trade.buyer?.username || 'Unknown'}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Box>\n                    <Typography variant=\"body2\">\n                      {formatAmount(trade.cryptocurrency?.amount, trade.cryptocurrency?.symbol)}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"textSecondary\">\n                      ≈ {formatAmount(trade.fiat?.amount, trade.fiat?.currency)}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    icon={getStatusIcon(trade.status)}\n                    label={trade.status.replace('_', ' ')}\n                    color={getStatusColor(trade.status)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\">\n                    {formatDate(trade.createdAt)}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\">\n                    {formatDate(trade.updatedAt)}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Box display=\"flex\" gap={1}>\n                    <Tooltip title=\"View Details\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleViewTrade(trade)}\n                      >\n                        <ViewIcon />\n                      </IconButton>\n                    </Tooltip>\n\n                    {trade.status === 'disputed' && (\n                      <Tooltip title=\"Force Complete\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"success\"\n                          onClick={() => handleTradeAction(trade, 'force_complete')}\n                        >\n                          <CompleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n\n                    {['created', 'funded', 'payment_sent'].includes(trade.status) && (\n                      <Tooltip title=\"Cancel Trade\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleTradeAction(trade, 'cancel')}\n                        >\n                          <BlockIcon />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n\n        <TablePagination\n          rowsPerPageOptions={[10, 25, 50, 100]}\n          component=\"div\"\n          count={totalTrades}\n          rowsPerPage={rowsPerPage}\n          page={page}\n          onPageChange={handleChangePage}\n          onRowsPerPageChange={handleChangeRowsPerPage}\n        />\n      </TableContainer>\n\n      {/* Trade Detail Dialog */}\n      <Dialog\n        open={tradeDetailOpen}\n        onClose={() => setTradeDetailOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          Trade Details - {selectedTrade?.tradeId}\n        </DialogTitle>\n        <DialogContent>\n          {selectedTrade && (\n            <Box>\n              <Grid container spacing={2}>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Trade Information\n                  </Typography>\n                  <Typography><strong>Status:</strong> {selectedTrade.status}</Typography>\n                  <Typography><strong>Created:</strong> {formatDate(selectedTrade.createdAt)}</Typography>\n                  <Typography><strong>Updated:</strong> {formatDate(selectedTrade.updatedAt)}</Typography>\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Amount Details\n                  </Typography>\n                  <Typography>\n                    <strong>Crypto:</strong> {formatAmount(selectedTrade.cryptocurrency?.amount, selectedTrade.cryptocurrency?.symbol)}\n                  </Typography>\n                  <Typography>\n                    <strong>Fiat:</strong> {formatAmount(selectedTrade.fiat?.amount, selectedTrade.fiat?.currency)}\n                  </Typography>\n                  <Typography>\n                    <strong>Rate:</strong> {selectedTrade.fiat?.exchangeRate}\n                  </Typography>\n                </Grid>\n                <Grid item xs={12}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Participants\n                  </Typography>\n                  <Typography>\n                    <strong>Seller:</strong> {selectedTrade.seller?.username} ({selectedTrade.seller?.email})\n                  </Typography>\n                  <Typography>\n                    <strong>Buyer:</strong> {selectedTrade.buyer?.username} ({selectedTrade.buyer?.email})\n                  </Typography>\n                </Grid>\n              </Grid>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setTradeDetailOpen(false)}>\n            Close\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default TradesPage;\n"], "names": ["createSvgIcon", "_jsx", "d", "TradesPage", "_selectedTrade$crypto", "_selectedTrade$crypto2", "_selectedTrade$fiat", "_selectedTrade$fiat2", "_selectedTrade$fiat3", "_selectedTrade$seller", "_selectedTrade$seller2", "_selectedTrade$buyer", "_selectedTrade$buyer2", "trades", "setTrades", "useState", "loading", "setLoading", "error", "setError", "page", "setPage", "rowsPerPage", "setRowsPerPage", "totalTrades", "setTotalTrades", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "selected<PERSON><PERSON>", "setSelectedTrade", "tradeDetailOpen", "setTradeDetailOpen", "fetchTrades", "async", "params", "limit", "search", "undefined", "status", "sortBy", "sortOrder", "response", "axios", "get", "data", "success", "pagination", "console", "useEffect", "handleTradeAction", "trade", "action", "post", "concat", "tradeId", "reason", "getStatusColor", "getStatusIcon", "CompleteIcon", "CancelIcon", "DisputeIcon", "formatAmount", "amount", "currency", "parseFloat", "toFixed", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "_jsxs", "Box", "children", "Typography", "variant", "gutterBottom", "Grid", "container", "spacing", "mb", "item", "xs", "sm", "md", "Card", "<PERSON><PERSON><PERSON><PERSON>", "color", "toLocaleString", "filter", "t", "includes", "length", "updatedAt", "toDateString", "Paper", "sx", "p", "alignItems", "TextField", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "InputAdornment", "position", "SearchIcon", "FormControl", "InputLabel", "Select", "label", "MenuItem", "<PERSON><PERSON>", "onClick", "TableContainer", "component", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "map", "_trade$seller", "_trade$buyer", "_trade$cryptocurrency", "_trade$cryptocurrency2", "_trade$fiat", "_trade$fiat2", "hover", "fontFamily", "seller", "username", "buyer", "cryptocurrency", "symbol", "fiat", "Chip", "icon", "replace", "size", "createdAt", "display", "gap", "<PERSON><PERSON><PERSON>", "title", "IconButton", "handleViewTrade", "ViewIcon", "BlockIcon", "_id", "TablePagination", "rowsPerPageOptions", "count", "onPageChange", "handleChangePage", "event", "newPage", "onRowsPerPageChange", "parseInt", "Dialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exchangeRate", "email", "DialogActions"], "sourceRoot": ""}