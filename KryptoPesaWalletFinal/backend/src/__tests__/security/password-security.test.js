/**
 * Comprehensive Password Security Tests
 * Ensures passwords are never stored in plaintext and validates security measures
 */

const User = require('../../models/User');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');

describe('Password Security Comprehensive Tests', () => {
  describe('Password Hashing Verification', () => {
    it('should never store plaintext passwords in database', async () => {
      const testPasswords = [
        'SimplePassword123',
        'Complex!Password@2024#',
        'VeryLongPasswordWithManyCharacters123!@#',
        'Password1!',
        '12345678Aa!',
        'SpecialChars!@#$%^&*()_+-=[]{}|;:,.<>?'
      ];

      for (let i = 0; i < testPasswords.length; i++) {
        const password = testPasswords[i];
        const userData = {
          username: `testuser${i}${Date.now()}`.substring(0, 30), // Ensure max length
          email: `test${i}${Date.now()}@example.com`,
          password: password,
          phone: `+25470000${1000 + i}`,
          profile: {
            firstName: 'Test',
            lastName: 'User',
            location: {
              country: 'KE',
              city: 'Nairobi'
            }
          }
        };

        const user = new User(userData);
        await user.save();

        // Verify password is hashed
        expect(user.password).not.toBe(password);
        expect(user.password).toMatch(/^\$2[aby]\$\d+\$/);
        
        // Verify password can be verified
        const isValid = await bcrypt.compare(password, user.password);
        expect(isValid).toBe(true);

        // Clean up
        await User.deleteOne({ _id: user._id });
      }
    });

    it('should use consistent bcrypt rounds', async () => {
      const users = [];

      // Create multiple users
      for (let i = 0; i < 3; i++) {
        const userData = {
          username: `rounds${i}${Date.now()}`.substring(0, 30),
          email: `rounds${i}${Date.now()}@example.com`,
          password: `TestPassword${i}123!`,
          phone: `+25470000${2000 + i}`,
          profile: {
            firstName: 'Test',
            lastName: 'User',
            location: {
              country: 'KE',
              city: 'Nairobi'
            }
          }
        };

        const user = new User(userData);
        await user.save();
        users.push(user);

        // Extract and verify rounds - should be consistent across all users
        const hashParts = user.password.split('$');
        const rounds = parseInt(hashParts[2]);

        // Verify it's a valid bcrypt hash with reasonable rounds
        expect(rounds).toBeGreaterThanOrEqual(1);
        expect(rounds).toBeLessThanOrEqual(15);

        // All users should use the same rounds
        if (i > 0) {
          const previousHashParts = users[i-1].password.split('$');
          const previousRounds = parseInt(previousHashParts[2]);
          expect(rounds).toBe(previousRounds);
        }
      }

      // Clean up
      await User.deleteMany({ _id: { $in: users.map(u => u._id) } });
    });

    it('should generate unique hashes for same password', async () => {
      const password = 'SamePassword123!';
      const users = [];

      // Create multiple users with same password
      for (let i = 0; i < 3; i++) {
        const userData = {
          username: `same${i}${Date.now()}`.substring(0, 30),
          email: `samepass${i}${Date.now()}@example.com`,
          password: password,
          phone: `+25470000${3000 + i}`,
          profile: {
            firstName: 'Test',
            lastName: 'User',
            location: {
              country: 'KE',
              city: 'Nairobi'
            }
          }
        };

        const user = new User(userData);
        await user.save();
        users.push(user);
      }

      // All hashes should be different (due to salt)
      const hashes = users.map(u => u.password);
      const uniqueHashes = [...new Set(hashes)];
      expect(uniqueHashes.length).toBe(hashes.length);

      // But all should verify correctly
      for (const user of users) {
        const isValid = await bcrypt.compare(password, user.password);
        expect(isValid).toBe(true);
      }

      // Clean up
      await User.deleteMany({ _id: { $in: users.map(u => u._id) } });
    });
  });

  describe('Password Update Security', () => {
    it('should rehash password on every change', async () => {
      const userData = {
        username: `update${Date.now()}`.substring(0, 30),
        email: `update${Date.now()}@example.com`,
        password: 'InitialPassword123!',
        phone: `+254700003000`,
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);
      await user.save();
      const initialHash = user.password;

      // Update password multiple times
      const passwords = ['NewPassword123!', 'AnotherPassword456!', 'FinalPassword789!'];
      const hashes = [initialHash];

      for (const newPassword of passwords) {
        user.password = newPassword;
        await user.save();
        
        // Hash should be different
        expect(user.password).not.toBe(hashes[hashes.length - 1]);
        expect(user.password).toMatch(/^\$2[aby]\$\d+\$/);
        
        // Should verify correctly
        const isValid = await bcrypt.compare(newPassword, user.password);
        expect(isValid).toBe(true);
        
        hashes.push(user.password);
      }

      // All hashes should be unique
      const uniqueHashes = [...new Set(hashes)];
      expect(uniqueHashes.length).toBe(hashes.length);

      // Clean up
      await User.deleteOne({ _id: user._id });
    });

    it('should not rehash when password is not modified', async () => {
      const userData = {
        username: `norehash${Date.now()}`.substring(0, 30),
        email: `norehash${Date.now()}@example.com`,
        password: 'StablePassword123!',
        phone: `+254700004000`,
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);
      await user.save();
      const originalHash = user.password;

      // Update other fields
      user.profile.firstName = 'Updated';
      await user.save();
      expect(user.password).toBe(originalHash);

      user.email = `updated${Date.now()}@example.com`;
      await user.save();
      expect(user.password).toBe(originalHash);

      user.lastActive = new Date();
      await user.save();
      expect(user.password).toBe(originalHash);

      // Clean up
      await User.deleteOne({ _id: user._id });
    });
  });

  describe('Password Comparison Security', () => {
    let testUser;

    beforeEach(async () => {
      const userData = {
        username: `comp${Date.now()}`.substring(0, 30),
        email: `comp${Date.now()}@example.com`,
        password: 'TestPassword123!',
        phone: `+254700005000`,
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      testUser = new User(userData);
      await testUser.save();
    });

    afterEach(async () => {
      if (testUser) {
        await User.deleteOne({ _id: testUser._id });
      }
    });

    it('should handle malicious comparison attempts', async () => {
      const maliciousInputs = [
        null,
        undefined,
        '',
        ' ',
        '\n',
        '\t',
        '{}',
        '[]',
        'function(){return true}',
        '<script>alert("xss")</script>',
        'SELECT * FROM users',
        '1\' OR \'1\'=\'1',
        Buffer.from('test'),
        { password: 'test' },
        ['password'],
        true,
        false,
        0,
        1,
        NaN,
        Infinity
      ];

      for (const input of maliciousInputs) {
        try {
          const result = await testUser.comparePassword(input);
          expect(result).toBe(false);
        } catch (error) {
          // bcrypt may throw errors for invalid inputs, which is acceptable
          expect(error).toBeDefined();
        }
      }
    });

    it('should be timing-attack resistant', async () => {
      const correctPassword = 'TestPassword123!';
      const wrongPasswords = [
        'WrongPassword123!',
        'TestPassword123',
        'testpassword123!',
        'TESTPASSWORD123!',
        'TestPassword124!',
        'TestPassword123!!',
        ''
      ];

      // Measure timing for correct password
      const correctTimes = [];
      for (let i = 0; i < 5; i++) {
        const start = process.hrtime.bigint();
        await testUser.comparePassword(correctPassword);
        const end = process.hrtime.bigint();
        correctTimes.push(Number(end - start));
      }

      // Measure timing for wrong passwords
      const wrongTimes = [];
      for (const wrongPassword of wrongPasswords) {
        const start = process.hrtime.bigint();
        await testUser.comparePassword(wrongPassword);
        const end = process.hrtime.bigint();
        wrongTimes.push(Number(end - start));
      }

      // Times should be relatively consistent (within reasonable variance)
      // This is a basic check - bcrypt should handle timing attacks
      const avgCorrect = correctTimes.reduce((a, b) => a + b) / correctTimes.length;
      const avgWrong = wrongTimes.reduce((a, b) => a + b) / wrongTimes.length;
      
      // The difference should not be extreme (bcrypt makes both operations similar)
      const timeDifference = Math.abs(avgCorrect - avgWrong);
      const maxExpectedDifference = Math.max(avgCorrect, avgWrong) * 0.5; // 50% variance allowed
      
      expect(timeDifference).toBeLessThan(maxExpectedDifference);
    });
  });

  describe('Database Security', () => {
    it('should never expose passwords in queries', async () => {
      const userData = {
        username: `query${Date.now()}`.substring(0, 30),
        email: `query${Date.now()}@example.com`,
        password: 'QueryTestPassword123!',
        phone: `+254700006000`,
        profile: {
          firstName: 'Test',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = new User(userData);
      await user.save();

      // Query without selecting password
      const userWithoutPassword = await User.findById(user._id);
      expect(userWithoutPassword.password).toBeUndefined();

      // Query with explicit password selection should work
      const userWithPassword = await User.findById(user._id).select('+password');
      expect(userWithPassword.password).toBeDefined();
      expect(userWithPassword.password).toMatch(/^\$2[aby]\$\d+\$/);

      // toJSON should not include password
      const jsonUser = userWithoutPassword.toJSON();
      expect(jsonUser.password).toBeUndefined();

      // Clean up
      await User.deleteOne({ _id: user._id });
    });
  });
});
