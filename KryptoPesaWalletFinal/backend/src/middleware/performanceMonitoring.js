const logger = require('../utils/logger');
const { performanceMonitoringService } = require('../services/performanceMonitoringService');

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byMethod: {},
        byRoute: {},
        byStatusCode: {}
      },
      responseTime: {
        total: 0,
        average: 0,
        min: Infinity,
        max: 0,
        p95: 0,
        p99: 0,
        samples: []
      },
      errors: {
        total: 0,
        byType: {},
        recent: []
      },
      memory: {
        samples: [],
        maxHeapUsed: 0,
        maxRSS: 0
      },
      activeConnections: 0
    };

    this.startTime = Date.now();
    this.sampleWindow = 1000; // Keep last 1000 samples for percentile calculations
    this.errorWindow = 100; // Keep last 100 errors

    // Start periodic memory monitoring
    this.startMemoryMonitoring();
  }

  // Middleware for request monitoring
  requestMonitoring() {
    return (req, res, next) => {
      const startTime = process.hrtime.bigint();
      const startMemory = process.memoryUsage();

      // Increment active connections
      this.metrics.activeConnections++;

      // Track request
      this.metrics.requests.total++;
      this.incrementCounter(this.metrics.requests.byMethod, req.method);
      this.incrementCounter(this.metrics.requests.byRoute, req.route?.path || req.path);

      // Override res.end to capture response metrics
      const originalEnd = res.end;
      res.end = (...args) => {
        const endTime = process.hrtime.bigint();
        const responseTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds
        const endMemory = process.memoryUsage();

        // Record response time
        this.recordResponseTime(responseTime);

        // Record status code
        this.incrementCounter(this.metrics.requests.byStatusCode, res.statusCode);

        // Track success/failure
        if (res.statusCode >= 200 && res.statusCode < 400) {
          this.metrics.requests.successful++;
        } else {
          this.metrics.requests.failed++;
        }

        // Record metrics in performance monitoring service
        performanceMonitoringService.recordRequest(
          req.method,
          req.route?.path || req.path,
          res.statusCode,
          responseTime
        );

        // Log performance metrics
        this.logRequestMetrics(req, res, responseTime, startMemory, endMemory);

        // Decrement active connections
        this.metrics.activeConnections--;

        // Call original end
        originalEnd.apply(res, args);
      };

      next();
    };
  }

  // Error monitoring middleware
  errorMonitoring() {
    return (error, req, res, next) => {
      this.recordError(error, req);
      next(error);
    };
  }

  recordResponseTime(responseTime) {
    this.metrics.responseTime.total += responseTime;
    this.metrics.responseTime.min = Math.min(this.metrics.responseTime.min, responseTime);
    this.metrics.responseTime.max = Math.max(this.metrics.responseTime.max, responseTime);

    // Add to samples for percentile calculation
    this.metrics.responseTime.samples.push(responseTime);
    if (this.metrics.responseTime.samples.length > this.sampleWindow) {
      this.metrics.responseTime.samples.shift();
    }

    // Calculate average
    this.metrics.responseTime.average = 
      this.metrics.responseTime.total / this.metrics.requests.total;

    // Calculate percentiles
    this.calculatePercentiles();
  }

  calculatePercentiles() {
    const samples = [...this.metrics.responseTime.samples].sort((a, b) => a - b);
    const length = samples.length;

    if (length > 0) {
      this.metrics.responseTime.p95 = samples[Math.floor(length * 0.95)];
      this.metrics.responseTime.p99 = samples[Math.floor(length * 0.99)];
    }
  }

  recordError(error, req) {
    this.metrics.errors.total++;

    const errorType = error.constructor.name;
    this.incrementCounter(this.metrics.errors.byType, errorType);

    // Add to recent errors
    const errorRecord = {
      timestamp: new Date().toISOString(),
      type: errorType,
      message: error.message,
      stack: error.stack,
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    };

    this.metrics.errors.recent.push(errorRecord);
    if (this.metrics.errors.recent.length > this.errorWindow) {
      this.metrics.errors.recent.shift();
    }

    // Record error in performance monitoring service
    performanceMonitoringService.recordError(error, req.method, req.route?.path || req.path);

    // Log error
    logger.error('Application error recorded', errorRecord);
  }

  logRequestMetrics(req, res, responseTime, startMemory, endMemory) {
    const memoryDelta = {
      rss: endMemory.rss - startMemory.rss,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed
    };

    const logData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime.toFixed(2)}ms`,
      memoryDelta: {
        rss: `${(memoryDelta.rss / 1024 / 1024).toFixed(2)}MB`,
        heapUsed: `${(memoryDelta.heapUsed / 1024 / 1024).toFixed(2)}MB`
      },
      userAgent: req.get('User-Agent'),
      ip: req.ip
    };

    // Log slow requests
    if (responseTime > 1000) {
      logger.warn('Slow request detected', logData);
    } else if (responseTime > 5000) {
      logger.error('Very slow request detected', logData);
    } else {
      logger.info('Request completed', logData);
    }
  }

  startMemoryMonitoring() {
    setInterval(() => {
      const memUsage = process.memoryUsage();
      
      this.metrics.memory.maxHeapUsed = Math.max(
        this.metrics.memory.maxHeapUsed, 
        memUsage.heapUsed
      );
      
      this.metrics.memory.maxRSS = Math.max(
        this.metrics.memory.maxRSS, 
        memUsage.rss
      );

      // Keep memory samples
      this.metrics.memory.samples.push({
        timestamp: Date.now(),
        ...memUsage
      });

      // Keep only last hour of samples (assuming 30s intervals)
      if (this.metrics.memory.samples.length > 120) {
        this.metrics.memory.samples.shift();
      }

      // Log memory warnings
      const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
      const rssMB = memUsage.rss / 1024 / 1024;

      if (heapUsedMB > 500) {
        logger.warn('High heap usage detected', {
          heapUsed: `${heapUsedMB.toFixed(2)}MB`,
          rss: `${rssMB.toFixed(2)}MB`
        });
      }

      if (rssMB > 1000) {
        logger.error('Very high memory usage detected', {
          heapUsed: `${heapUsedMB.toFixed(2)}MB`,
          rss: `${rssMB.toFixed(2)}MB`
        });
      }
    }, 30000); // Every 30 seconds
  }

  incrementCounter(counter, key) {
    counter[key] = (counter[key] || 0) + 1;
  }

  getMetrics() {
    const uptime = Date.now() - this.startTime;
    const currentMemory = process.memoryUsage();

    return {
      uptime: uptime,
      requests: {
        ...this.metrics.requests,
        requestsPerSecond: this.metrics.requests.total / (uptime / 1000)
      },
      responseTime: {
        ...this.metrics.responseTime,
        samples: undefined // Don't include raw samples in response
      },
      errors: {
        total: this.metrics.errors.total,
        byType: this.metrics.errors.byType,
        errorRate: this.metrics.errors.total / this.metrics.requests.total,
        recentErrors: this.metrics.errors.recent.slice(-10) // Last 10 errors
      },
      memory: {
        current: {
          rss: `${(currentMemory.rss / 1024 / 1024).toFixed(2)}MB`,
          heapTotal: `${(currentMemory.heapTotal / 1024 / 1024).toFixed(2)}MB`,
          heapUsed: `${(currentMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`,
          external: `${(currentMemory.external / 1024 / 1024).toFixed(2)}MB`
        },
        peak: {
          heapUsed: `${(this.metrics.memory.maxHeapUsed / 1024 / 1024).toFixed(2)}MB`,
          rss: `${(this.metrics.memory.maxRSS / 1024 / 1024).toFixed(2)}MB`
        }
      },
      activeConnections: this.metrics.activeConnections,
      timestamp: new Date().toISOString()
    };
  }

  getHealthScore() {
    const metrics = this.getMetrics();
    let score = 100;

    // Deduct points for high error rate
    if (metrics.errors.errorRate > 0.05) score -= 20; // > 5% error rate
    if (metrics.errors.errorRate > 0.1) score -= 30; // > 10% error rate

    // Deduct points for slow response times
    if (metrics.responseTime.average > 1000) score -= 15; // > 1s average
    if (metrics.responseTime.p95 > 2000) score -= 20; // > 2s p95

    // Deduct points for high memory usage
    const heapUsedMB = parseFloat(metrics.memory.current.heapUsed);
    if (heapUsedMB > 300) score -= 10;
    if (heapUsedMB > 500) score -= 20;

    return Math.max(0, score);
  }

  reset() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byMethod: {},
        byRoute: {},
        byStatusCode: {}
      },
      responseTime: {
        total: 0,
        average: 0,
        min: Infinity,
        max: 0,
        p95: 0,
        p99: 0,
        samples: []
      },
      errors: {
        total: 0,
        byType: {},
        recent: []
      },
      memory: {
        samples: [],
        maxHeapUsed: 0,
        maxRSS: 0
      },
      activeConnections: 0
    };
    this.startTime = Date.now();
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = {
  performanceMonitor,
  requestMonitoring: () => performanceMonitor.requestMonitoring(),
  errorMonitoring: () => performanceMonitor.errorMonitoring()
};
