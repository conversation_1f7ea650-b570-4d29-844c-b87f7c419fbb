
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">10.22% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>1200/11740</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.7% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>313/4666</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.68% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>149/1716</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">10.3% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>1182/11468</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="config"><a href="config/index.html">config</a></td>
	<td data-value="6.37" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.37" class="pct low">6.37%</td>
	<td data-value="345" class="abs low">22/345</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="188" class="abs low">0/188</td>
	<td data-value="2.59" class="pct low">2.59%</td>
	<td data-value="77" class="abs low">2/77</td>
	<td data-value="6.48" class="pct low">6.48%</td>
	<td data-value="339" class="abs low">22/339</td>
	</tr>

<tr>
	<td class="file low" data-value="middleware"><a href="middleware/index.html">middleware</a></td>
	<td data-value="9.86" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.86" class="pct low">9.86%</td>
	<td data-value="912" class="abs low">90/912</td>
	<td data-value="5.3" class="pct low">5.3%</td>
	<td data-value="566" class="abs low">30/566</td>
	<td data-value="7.09" class="pct low">7.09%</td>
	<td data-value="141" class="abs low">10/141</td>
	<td data-value="10.38" class="pct low">10.38%</td>
	<td data-value="857" class="abs low">89/857</td>
	</tr>

<tr>
	<td class="file low" data-value="models"><a href="models/index.html">models</a></td>
	<td data-value="37.32" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 37%"></div><div class="cover-empty" style="width: 63%"></div></div>
	</td>
	<td data-value="37.32" class="pct low">37.32%</td>
	<td data-value="635" class="abs low">237/635</td>
	<td data-value="12.37" class="pct low">12.37%</td>
	<td data-value="299" class="abs low">37/299</td>
	<td data-value="18.48" class="pct low">18.48%</td>
	<td data-value="119" class="abs low">22/119</td>
	<td data-value="38.18" class="pct low">38.18%</td>
	<td data-value="618" class="abs low">236/618</td>
	</tr>

<tr>
	<td class="file low" data-value="routes"><a href="routes/index.html">routes</a></td>
	<td data-value="2.08" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 2%"></div><div class="cover-empty" style="width: 98%"></div></div>
	</td>
	<td data-value="2.08" class="pct low">2.08%</td>
	<td data-value="2442" class="abs low">51/2442</td>
	<td data-value="1.28" class="pct low">1.28%</td>
	<td data-value="936" class="abs low">12/936</td>
	<td data-value="1.27" class="pct low">1.27%</td>
	<td data-value="235" class="abs low">3/235</td>
	<td data-value="2.12" class="pct low">2.12%</td>
	<td data-value="2398" class="abs low">51/2398</td>
	</tr>

<tr>
	<td class="file low" data-value="scripts"><a href="scripts/index.html">scripts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="171" class="abs low">0/171</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="62" class="abs low">0/62</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="31" class="abs low">0/31</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="163" class="abs low">0/163</td>
	</tr>

<tr>
	<td class="file low" data-value="services"><a href="services/index.html">services</a></td>
	<td data-value="11.54" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.54" class="pct low">11.54%</td>
	<td data-value="5430" class="abs low">627/5430</td>
	<td data-value="7.32" class="pct low">7.32%</td>
	<td data-value="2184" class="abs low">160/2184</td>
	<td data-value="10.59" class="pct low">10.59%</td>
	<td data-value="755" class="abs low">80/755</td>
	<td data-value="11.57" class="pct low">11.57%</td>
	<td data-value="5341" class="abs low">618/5341</td>
	</tr>

<tr>
	<td class="file low" data-value="services/blockchain"><a href="services/blockchain/index.html">services/blockchain</a></td>
	<td data-value="13.76" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 13%"></div><div class="cover-empty" style="width: 87%"></div></div>
	</td>
	<td data-value="13.76" class="pct low">13.76%</td>
	<td data-value="247" class="abs low">34/247</td>
	<td data-value="12" class="pct low">12%</td>
	<td data-value="75" class="abs low">9/75</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="56" class="abs low">8/56</td>
	<td data-value="13.8" class="pct low">13.8%</td>
	<td data-value="239" class="abs low">33/239</td>
	</tr>

<tr>
	<td class="file low" data-value="services/keyManagement"><a href="services/keyManagement/index.html">services/keyManagement</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="463" class="abs low">0/463</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="133" class="abs low">0/133</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="86" class="abs low">0/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="460" class="abs low">0/460</td>
	</tr>

<tr>
	<td class="file low" data-value="tests"><a href="tests/index.html">tests</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="745" class="abs low">0/745</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="145" class="abs low">0/145</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="723" class="abs low">0/723</td>
	</tr>

<tr>
	<td class="file low" data-value="utils"><a href="utils/index.html">utils</a></td>
	<td data-value="39.71" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 39%"></div><div class="cover-empty" style="width: 61%"></div></div>
	</td>
	<td data-value="39.71" class="pct low">39.71%</td>
	<td data-value="350" class="abs low">139/350</td>
	<td data-value="30.23" class="pct low">30.23%</td>
	<td data-value="215" class="abs low">65/215</td>
	<td data-value="33.8" class="pct low">33.8%</td>
	<td data-value="71" class="abs low">24/71</td>
	<td data-value="40.3" class="pct low">40.3%</td>
	<td data-value="330" class="abs low">133/330</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-08T14:32:16.455Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    