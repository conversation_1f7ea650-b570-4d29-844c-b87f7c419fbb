const express = require('express');
const { body, validationResult } = require('express-validator');
const { ethers } = require('ethers');
const WalletUser = require('../models/WalletUser');
const { authenticateWallet, generateAuthMessage } = require('../middleware/walletAuth');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   POST /api/wallet-auth/challenge
 * @desc    Generate authentication challenge for wallet
 * @access  Public
 */
router.post('/challenge', [
  body('walletAddress')
    .notEmpty()
    .withMessage('Wallet address is required')
    .custom((value) => {
      if (!ethers.utils.isAddress(value)) {
        throw new Error('Invalid wallet address format');
      }
      return true;
    }),
  body('action')
    .notEmpty()
    .withMessage('Action is required')
    .isIn(['login', 'register', 'transaction'])
    .withMessage('Invalid action type')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { walletAddress, action } = req.body;
    const timestamp = Date.now();
    
    // Generate challenge message
    const message = `KryptoPesa Authentication\nTimestamp: ${timestamp}\nAction: ${action}\nWallet: ${walletAddress}`;
    
    // Store challenge temporarily (in production, use Redis)
    // For now, we'll include it in the response
    
    res.json({
      success: true,
      data: {
        message,
        timestamp,
        walletAddress: walletAddress.toLowerCase(),
        expiresIn: 300 // 5 minutes
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/wallet-auth/verify
 * @desc    Verify wallet signature and authenticate user
 * @access  Public
 */
router.post('/verify', [
  body('walletAddress')
    .notEmpty()
    .withMessage('Wallet address is required')
    .custom((value) => {
      if (!ethers.utils.isAddress(value)) {
        throw new Error('Invalid wallet address format');
      }
      return true;
    }),
  body('signature')
    .notEmpty()
    .withMessage('Signature is required'),
  body('message')
    .notEmpty()
    .withMessage('Message is required'),
  body('timestamp')
    .isNumeric()
    .withMessage('Timestamp must be numeric')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { walletAddress, signature, message, timestamp } = req.body;

    // Verify timestamp (5 minute window)
    const now = Date.now();
    const timeDiff = Math.abs(now - parseInt(timestamp));
    
    if (timeDiff > 5 * 60 * 1000) {
      throw new AppError('Authentication challenge expired', 401);
    }

    // Verify signature
    let recoveredAddress;
    try {
      recoveredAddress = ethers.utils.verifyMessage(message, signature);
    } catch (error) {
      throw new AppError('Invalid signature', 401);
    }

    if (recoveredAddress.toLowerCase() !== walletAddress.toLowerCase()) {
      throw new AppError('Signature verification failed', 401);
    }

    // Find or create user
    let user = await WalletUser.findByWalletAddress(walletAddress);
    let isNewUser = false;

    if (!user) {
      // Create new user
      user = await WalletUser.createFromWallet(walletAddress, recoveredAddress, {
        lastLoginIP: req.ip,
        deviceInfo: {
          userAgent: req.headers['user-agent'],
          timestamp: new Date()
        }
      });
      isNewUser = true;
      
      logger.info(`New wallet user created: ${walletAddress}`);
    } else {
      // Update existing user
      user.lastActive = new Date();
      user.lastLoginIP = req.ip;
      await user.save();
    }

    // Check account status
    if (user.status !== 'active') {
      throw new AppError(`Account is ${user.status}`, 403);
    }

    if (user.isLocked) {
      throw new AppError('Account is temporarily locked', 423);
    }

    // Generate session token (simple timestamp-based for demo)
    const sessionToken = Buffer.from(`${walletAddress}:${Date.now()}`).toString('base64');

    res.json({
      success: true,
      message: isNewUser ? 'Wallet registered successfully' : 'Authentication successful',
      data: {
        user: user.toPublicJSON(),
        sessionToken,
        isNewUser,
        expiresIn: 24 * 60 * 60 // 24 hours
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/wallet-auth/profile
 * @desc    Get current user profile
 * @access  Private (Wallet Auth)
 */
router.get('/profile', authenticateWallet, async (req, res, next) => {
  try {
    res.json({
      success: true,
      data: {
        user: req.user.toPublicJSON()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   PUT /api/wallet-auth/profile
 * @desc    Update user profile
 * @access  Private (Wallet Auth)
 */
router.put('/profile', authenticateWallet, [
  body('displayName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .trim()
    .withMessage('Display name must be 1-50 characters'),
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .trim()
    .withMessage('Bio must be max 500 characters'),
  body('avatar')
    .optional()
    .isURL()
    .withMessage('Avatar must be a valid URL'),
  body('location.country')
    .optional()
    .isLength({ min: 2, max: 2 })
    .withMessage('Country must be 2-letter ISO code'),
  body('location.city')
    .optional()
    .isLength({ min: 1, max: 100 })
    .trim()
    .withMessage('City must be 1-100 characters'),
  body('preferences.language')
    .optional()
    .isIn(['en', 'sw'])
    .withMessage('Language must be en or sw'),
  body('preferences.currency')
    .optional()
    .isIn(['KES', 'TZS', 'UGX', 'RWF', 'USD'])
    .withMessage('Invalid currency')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const updateData = req.body;
    const user = req.user;

    // Update profile fields
    if (updateData.displayName !== undefined) {
      user.profile.displayName = updateData.displayName;
    }
    if (updateData.bio !== undefined) {
      user.profile.bio = updateData.bio;
    }
    if (updateData.avatar !== undefined) {
      user.profile.avatar = updateData.avatar;
    }
    if (updateData.location) {
      if (updateData.location.country !== undefined) {
        user.profile.location.country = updateData.location.country;
      }
      if (updateData.location.city !== undefined) {
        user.profile.location.city = updateData.location.city;
      }
    }

    // Update preferences
    if (updateData.preferences) {
      if (updateData.preferences.language !== undefined) {
        user.preferences.language = updateData.preferences.language;
      }
      if (updateData.preferences.currency !== undefined) {
        user.preferences.currency = updateData.preferences.currency;
      }
      if (updateData.preferences.notifications) {
        Object.assign(user.preferences.notifications, updateData.preferences.notifications);
      }
      if (updateData.preferences.trading) {
        Object.assign(user.preferences.trading, updateData.preferences.trading);
      }
    }

    await user.save();

    logger.info(`Profile updated for wallet: ${user.walletAddress}`);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: user.toPublicJSON()
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/wallet-auth/logout
 * @desc    Logout user (invalidate session)
 * @access  Private (Wallet Auth)
 */
router.post('/logout', authenticateWallet, async (req, res, next) => {
  try {
    // In a stateless system, logout is mainly client-side
    // But we can update last active time
    req.user.lastActive = new Date();
    await req.user.save();

    logger.info(`User logged out: ${req.user.walletAddress}`);

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/wallet-auth/status
 * @desc    Check authentication status
 * @access  Private (Wallet Auth)
 */
router.get('/status', authenticateWallet, async (req, res, next) => {
  try {
    res.json({
      success: true,
      data: {
        authenticated: true,
        walletAddress: req.user.walletAddress,
        status: req.user.status,
        verificationLevel: req.user.verification.level,
        lastActive: req.user.lastActive
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
