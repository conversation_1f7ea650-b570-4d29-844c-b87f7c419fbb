{"dashboard": {"id": "kryptopesa-production-dashboard", "title": "KryptoPesa Production Monitoring Dashboard", "tags": ["krypt<PERSON><PERSON>", "production", "monitoring"], "timezone": "UTC", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Overview", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up{job=\"kryptopesa-api\"}", "legendFormat": "API Instances Up", "refId": "A"}, {"expr": "rate(http_requests_total[5m])", "legendFormat": "Requests/sec", "refId": "B"}, {"expr": "active_users_total", "legendFormat": "Active Users", "refId": "C"}, {"expr": "rate(trading_volume_total[1h])", "legendFormat": "Trading Volume/hour", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 2, "title": "API Response Times", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "99th percentile", "refId": "C"}], "yAxes": [{"label": "Response Time (seconds)", "min": 0, "max": null}], "alert": {"conditions": [{"evaluator": {"params": [2], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["B", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "5m", "frequency": "10s", "handler": 1, "name": "High API Response Time", "noDataState": "no_data", "notifications": []}}, {"id": 3, "title": "Error Rate", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "rate(http_requests_total{status_code=~\"5..\"}[5m]) / rate(http_requests_total[5m])", "legendFormat": "5xx Error Rate", "refId": "A"}, {"expr": "rate(http_requests_total{status_code=~\"4..\"}[5m]) / rate(http_requests_total[5m])", "legendFormat": "4xx Error Rate", "refId": "B"}], "yAxes": [{"label": "Error Rate", "min": 0, "max": 1}], "alert": {"conditions": [{"evaluator": {"params": [0.05], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "3m", "frequency": "10s", "handler": 1, "name": "High Error Rate", "noDataState": "no_data", "notifications": []}}, {"id": 4, "title": "System Resources", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 17}, "targets": [{"expr": "system_cpu_usage_percent", "legendFormat": "CPU Usage %", "refId": "A"}, {"expr": "(system_memory_usage_bytes / system_memory_total_bytes) * 100", "legendFormat": "Memory Usage %", "refId": "B"}, {"expr": "system_disk_usage_percent", "legendFormat": "Disk Usage %", "refId": "C"}], "yAxes": [{"label": "Usage Percentage", "min": 0, "max": 100}]}, {"id": 5, "title": "Database Performance", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 12, "y": 17}, "targets": [{"expr": "database_connections_active", "legendFormat": "Active Connections", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile query time", "refId": "B"}], "yAxes": [{"label": "Connections / Query Time (s)", "min": 0}]}, {"id": 6, "title": "Trading Metrics", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 26}, "targets": [{"expr": "rate(trading_volume_total[1h])", "legendFormat": "Trading Volume/hour - {{cryptocurrency}}", "refId": "A"}, {"expr": "rate(escrow_transactions_total{status=\"completed\"}[1h])", "legendFormat": "Completed Escrows/hour", "refId": "B"}, {"expr": "rate(escrow_transactions_total{status=\"failed\"}[1h])", "legendFormat": "Failed Escrows/hour", "refId": "C"}]}, {"id": 7, "title": "Security Events", "type": "graph", "gridPos": {"h": 9, "w": 12, "x": 12, "y": 26}, "targets": [{"expr": "rate(failed_logins_total[5m])", "legendFormat": "Failed Logins/min", "refId": "A"}, {"expr": "rate(security_events_total[5m])", "legendFormat": "Security Events/min - {{event_type}}", "refId": "B"}], "alert": {"conditions": [{"evaluator": {"params": [10], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "2m", "frequency": "10s", "handler": 1, "name": "High Failed Login Rate", "noDataState": "no_data", "notifications": []}}, {"id": 8, "title": "<PERSON><PERSON>", "type": "stat", "gridPos": {"h": 6, "w": 8, "x": 0, "y": 35}, "targets": [{"expr": "(rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))) * 100", "legendFormat": "Cache Hit Rate %", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}, "unit": "percent"}}}, {"id": 9, "title": "WebSocket Connections", "type": "stat", "gridPos": {"h": 6, "w": 8, "x": 8, "y": 35}, "targets": [{"expr": "websocket_connections_active", "legendFormat": "Active WebSocket Connections", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 10, "title": "Mobile App Metrics", "type": "stat", "gridPos": {"h": 6, "w": 8, "x": 16, "y": 35}, "targets": [{"expr": "mobile_app_sessions_active", "legendFormat": "Active Mobile Sessions", "refId": "A"}, {"expr": "rate(mobile_app_crashes_total[1h])", "legendFormat": "App Crashes/hour", "refId": "B"}]}, {"id": 11, "title": "Top API Endpoints by Request Count", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "targets": [{"expr": "topk(10, sum by (route) (rate(http_requests_total[5m])))", "legendFormat": "{{route}}", "refId": "A", "format": "table"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"route": "Endpoint", "Value": "Requests/sec"}}}]}, {"id": 12, "title": "Recent Alerts", "type": "logs", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "targets": [{"expr": "{job=\"alertmanager\"}", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}, {"id": 13, "title": "Business KPIs", "type": "stat", "gridPos": {"h": 6, "w": 24, "x": 0, "y": 49}, "targets": [{"expr": "sum(rate(trading_volume_total[24h]))", "legendFormat": "24h Trading Volume", "refId": "A"}, {"expr": "count(active_users_total)", "legendFormat": "Daily Active Users", "refId": "B"}, {"expr": "sum(rate(wallet_transactions_total{status=\"completed\"}[24h]))", "legendFormat": "24h Completed Transactions", "refId": "C"}, {"expr": "sum(rate(commission_earned_total[24h]))", "legendFormat": "24h Commission Earned", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}]}}}}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"kryptopesa-api\"}, instance)", "refresh": 1, "includeAll": true, "multi": true}, {"name": "cryptocurrency", "type": "query", "query": "label_values(trading_volume_total, cryptocurrency)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "enable": true, "expr": "deployment_timestamp", "iconColor": "blue", "titleFormat": "Deployment: {{version}}"}, {"name": "<PERSON><PERSON><PERSON>", "datasource": "prometheus", "enable": true, "expr": "ALERTS{alertstate=\"firing\"}", "iconColor": "red", "titleFormat": "Alert: {{alertname}}"}]}}, "alerting": {"rules": [{"alert": "HighResponseTime", "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2", "for": "5m", "labels": {"severity": "warning", "service": "kryptopesa-api"}, "annotations": {"summary": "High API response time detected", "description": "95th percentile response time is {{ $value }}s"}}, {"alert": "HighErrorRate", "expr": "rate(http_requests_total{status_code=~\"5..\"}[5m]) / rate(http_requests_total[5m]) > 0.05", "for": "3m", "labels": {"severity": "critical", "service": "kryptopesa-api"}, "annotations": {"summary": "High error rate detected", "description": "Error rate is {{ $value | humanizePercentage }}"}}, {"alert": "DatabaseConnectionsHigh", "expr": "database_connections_active > 40", "for": "5m", "labels": {"severity": "warning", "service": "database"}, "annotations": {"summary": "High database connection count", "description": "Active database connections: {{ $value }}/50"}}, {"alert": "LowCacheHitRate", "expr": "(rate(cache_hits_total[10m]) / (rate(cache_hits_total[10m]) + rate(cache_misses_total[10m]))) * 100 < 70", "for": "10m", "labels": {"severity": "warning", "service": "cache"}, "annotations": {"summary": "Low cache hit rate", "description": "Cache hit rate is {{ $value }}%"}}, {"alert": "HighFailedLoginRate", "expr": "rate(failed_logins_total[5m]) > 10", "for": "2m", "labels": {"severity": "critical", "service": "security"}, "annotations": {"summary": "High failed login rate - possible attack", "description": "{{ $value }} failed login attempts per second"}}, {"alert": "EscrowTransactionFailures", "expr": "rate(escrow_transactions_total{status=\"failed\"}[10m]) > 0.1", "for": "5m", "labels": {"severity": "critical", "service": "trading"}, "annotations": {"summary": "High escrow transaction failure rate", "description": "{{ $value }} escrow transactions failing per second"}}]}, "notifications": [{"name": "email-alerts", "type": "email", "settings": {"addresses": "<EMAIL>;<EMAIL>", "subject": "KryptoPesa Production Alert: {{ .GroupLabels.alertname }}", "body": "Alert: {{ .GroupLabels.alertname }}\nSeverity: {{ .GroupLabels.severity }}\nDescription: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}"}}, {"name": "slack-alerts", "type": "slack", "settings": {"url": "${SLACK_WEBHOOK_URL}", "channel": "#production-alerts", "username": "KryptoPesa Monitoring", "title": "Production Alert: {{ .GroupLabels.alertname }}", "text": "{{ range .Alerts }}{{ .Annotations.description }}{{ end }}"}}, {"name": "discord-alerts", "type": "discord", "settings": {"url": "${DISCORD_WEBHOOK_URL}", "title": "🚨 KryptoPesa Production Alert", "message": "**Alert:** {{ .GroupLabels.alertname }}\n**Severity:** {{ .GroupLabels.severity }}\n**Description:** {{ range .Alerts }}{{ .Annotations.description }}{{ end }}"}}]}