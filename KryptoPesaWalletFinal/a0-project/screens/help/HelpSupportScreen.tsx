import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import {
  supportService,
  userService,
  EAST_AFRICAN_CONFIG,
  SupportTicket,
  eastAfricanHelpers
} from '../../services';

const EAST_AFRICAN_FAQ = [
  {
    id: '1',
    question: 'How do I start trading on KryptoPesa?',
    answer: 'Complete your KYC verification with East African documents (National ID/Passport), set up your external payment methods (M-Pesa, Airtel Money, Bank Transfer), and browse offers. KryptoPesa only holds crypto in escrow - you handle fiat payments externally.',
  },
  {
    id: '2',
    question: 'What payment methods work in East Africa?',
    answer: 'KryptoPesa supports M-Pesa (Kenya), Airtel Money (Kenya, Tanzania, Uganda, Rwanda), bank transfers, and cash meetups. You coordinate payments externally while KryptoPesa secures the crypto in escrow.',
  },
  {
    id: '3',
    question: 'How does the escrow system work?',
    answer: 'KryptoPesa holds cryptocurrency in escrow while you handle fiat payments externally. Once you confirm payment receipt/sending, the crypto is released. We never touch your M-Pesa or bank accounts.',
  },
  {
    id: '4',
    question: 'What are the trading limits and fees?',
    answer: 'Trading limits depend on your KYC level: Basic (100,000 KES), Intermediate (1M KES), Advanced (Unlimited). Platform fee is 1% of crypto amount. No hidden fees.',
  },
  {
    id: '5',
    question: 'Which East African countries are supported?',
    answer: 'KryptoPesa operates in Kenya (KES), Tanzania (TZS), Uganda (UGX), and Rwanda (RWF) with full local currency support and regional payment methods.',
  },
  {
    id: '6',
    question: 'How do I verify my account with East African documents?',
    answer: 'Upload your National ID or Passport, take a selfie with your ID, and provide proof of address (utility bill). We accept documents from Kenya, Tanzania, Uganda, and Rwanda.',
  },
  {
    id: '7',
    question: 'Is KryptoPesa safe for crypto trading?',
    answer: 'Yes! KryptoPesa uses military-grade encryption, multi-signature wallets, and escrow-only architecture. We never hold your fiat money - only crypto in secure escrow until trade completion.',
  },
  {
    id: '8',
    question: 'How do I handle M-Pesa payments?',
    answer: 'You coordinate M-Pesa payments directly with your trading partner outside KryptoPesa. Upload payment confirmation screenshots to the trade chat for verification.',
  },
];

export default function HelpSupportScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();

  // State management
  const [faqData, setFaqData] = useState(EAST_AFRICAN_FAQ);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // UI state
  const [expandedFaq, setExpandedFaq] = useState<string | null>(null);
  const [supportMessage, setSupportMessage] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('general');

  const supportCategories = [
    { id: 'general', label: 'General Question', icon: 'help-circle' },
    { id: 'trading', label: 'Trading Issue', icon: 'swap-horizontal' },
    { id: 'payment', label: 'M-Pesa/Airtel Money Issue', icon: 'card' },
    { id: 'security', label: 'Security Concern', icon: 'shield' },
    { id: 'kyc', label: 'KYC Verification', icon: 'person' },
    { id: 'technical', label: 'Technical Issue', icon: 'bug' },
  ];

  const contactMethods = [
    {
      id: 'email',
      title: 'Email Support',
      description: 'Get help via email within 24 hours',
      icon: 'mail',
      action: () => Linking.openURL('mailto:<EMAIL>'),
    },
    {
      id: 'whatsapp',
      title: 'WhatsApp Support',
      description: 'Chat with us on WhatsApp',
      icon: 'logo-whatsapp',
      action: () => Linking.openURL('https://wa.me/254700000000'),
    },
    {
      id: 'telegram',
      title: 'Telegram Support',
      description: 'Join our Telegram support group',
      icon: 'send',
      action: () => Linking.openURL('https://t.me/kryptopesa_support'),
    },
    {
      id: 'phone',
      title: 'Phone Support',
      description: 'Call us for urgent issues',
      icon: 'call',
      action: () => Linking.openURL('tel:+254700000000'),
    },
  ];

  // Load support data
  const loadSupportData = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      const [profile, faqList] = await Promise.all([
        userService.getProfile(),
        supportService.getFAQ(),
      ]);

      setUserProfile(profile);
      if (faqList.length > 0) {
        setFaqData(faqList);
      }
    } catch (err) {
      console.error('Failed to load support data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load support data');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadSupportData();
  }, []);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadSupportData(false);
    setRefreshing(false);
  };
  
  const handleFaqToggle = (faqId: string) => {
    setExpandedFaq(expandedFaq === faqId ? null : faqId);
  };
  
  const handleSubmitSupport = async () => {
    if (!supportMessage.trim()) {
      Alert.alert('Error', 'Please enter your message');
      return;
    }

    try {
      setSubmitting(true);

      const ticketData = {
        category: selectedCategory,
        message: supportMessage.trim(),
        userInfo: {
          userId: userProfile?._id,
          username: userProfile?.username,
          country: userProfile?.location?.country,
        },
      };

      const ticket = await supportService.createSupportTicket(ticketData);

      Alert.alert(
        'Support Request Submitted',
        `Thank you for contacting KryptoPesa support. Your ticket #${ticket.ticketNumber} has been created. We will respond within 24 hours.`,
        [
          {
            text: 'OK',
            onPress: () => {
              setSupportMessage('');
              setSelectedCategory('general');
            },
          },
        ]
      );
    } catch (err) {
      console.error('Failed to submit support ticket:', err);
      Alert.alert('Error', 'Failed to submit support request. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };
  
  const renderFaqItem = (item: any) => (
    <View key={item.id} style={styles.faqItem}>
      <TouchableOpacity
        style={styles.faqQuestion}
        onPress={() => handleFaqToggle(item.id)}
      >
        <Text style={styles.faqQuestionText}>{item.question}</Text>
        <Ionicons
          name={expandedFaq === item.id ? 'chevron-up' : 'chevron-down'}
          size={20}
          color={theme.colors.textSecondary}
        />
      </TouchableOpacity>
      
      {expandedFaq === item.id && (
        <View style={styles.faqAnswer}>
          <Text style={styles.faqAnswerText}>{item.answer}</Text>
        </View>
      )}
    </View>
  );
  
  const renderContactMethod = (method: any) => (
    <TouchableOpacity
      key={method.id}
      style={styles.contactMethod}
      onPress={method.action}
    >
      <View style={styles.contactIcon}>
        <Ionicons name={method.icon} size={24} color={theme.colors.primary} />
      </View>
      <View style={styles.contactContent}>
        <Text style={styles.contactTitle}>{method.title}</Text>
        <Text style={styles.contactDescription}>{method.description}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );

  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Help & Support</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading support information...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>KryptoPesa Help & Support</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Help</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.quickAction}>
              <Ionicons name="document-text" size={24} color={theme.colors.primary} />
              <Text style={styles.quickActionText}>User Guide</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickAction}>
              <Ionicons name="play-circle" size={24} color={theme.colors.primary} />
              <Text style={styles.quickActionText}>Video Tutorials</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickAction}>
              <Ionicons name="shield-checkmark" size={24} color={theme.colors.primary} />
              <Text style={styles.quickActionText}>Safety Tips</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* FAQ Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          <View style={styles.faqContainer}>
            {faqData.map(renderFaqItem)}
          </View>
        </View>
        
        {/* Contact Support */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Support</Text>
          <View style={styles.contactMethods}>
            {contactMethods.map(renderContactMethod)}
          </View>
        </View>
        
        {/* Submit Support Request */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Submit Support Request</Text>
          
          <View style={styles.supportForm}>
            <Text style={styles.inputLabel}>Category</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
              {supportCategories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category.id && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory(category.id)}
                >
                  <Ionicons
                    name={category.icon}
                    size={16}
                    color={selectedCategory === category.id ? 'white' : theme.colors.textSecondary}
                  />
                  <Text style={[
                    styles.categoryButtonText,
                    selectedCategory === category.id && styles.categoryButtonTextActive
                  ]}>
                    {category.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            
            <Text style={styles.inputLabel}>Message</Text>
            <TextInput
              style={styles.messageInput}
              placeholder="Describe your issue or question..."
              value={supportMessage}
              onChangeText={setSupportMessage}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
            />
            
            <TouchableOpacity
              style={[
                styles.submitButton,
                !supportMessage.trim() && styles.submitButtonDisabled
              ]}
              onPress={handleSubmitSupport}
              disabled={!supportMessage.trim()}
            >
              <Text style={styles.submitButtonText}>Submit Request</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Additional Resources */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Additional Resources</Text>
          
          <TouchableOpacity style={styles.resourceItem}>
            <Ionicons name="globe" size={20} color={theme.colors.textSecondary} />
            <Text style={styles.resourceText}>Visit our website</Text>
            <Ionicons name="open-outline" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.resourceItem}>
            <Ionicons name="logo-twitter" size={20} color={theme.colors.textSecondary} />
            <Text style={styles.resourceText}>Follow us on Twitter</Text>
            <Ionicons name="open-outline" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.resourceItem}>
            <Ionicons name="people" size={20} color={theme.colors.textSecondary} />
            <Text style={styles.resourceText}>Join our community</Text>
            <Ionicons name="open-outline" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickAction: {
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: theme.colors.backgroundSecondary,
    flex: 1,
    marginHorizontal: 4,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
    marginTop: 8,
    textAlign: 'center',
  },
  faqContainer: {
    gap: 8,
  },
  faqItem: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    overflow: 'hidden',
  },
  faqQuestion: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: theme.colors.backgroundSecondary,
  },
  faqQuestionText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    flex: 1,
    marginRight: 12,
  },
  faqAnswer: {
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  faqAnswerText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  contactMethods: {
    gap: 12,
  },
  contactMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primaryLight + '40',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  contactContent: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  contactDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  supportForm: {
    gap: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  categoryScroll: {
    marginBottom: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  categoryButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  categoryButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  categoryButtonTextActive: {
    color: 'white',
  },
  messageInput: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    fontSize: 16,
    color: theme.colors.text,
    textAlignVertical: 'top',
    minHeight: 120,
  },
  submitButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: theme.colors.border,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  resourceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  resourceText: {
    fontSize: 14,
    color: theme.colors.text,
    flex: 1,
    marginLeft: 12,
  },
});
