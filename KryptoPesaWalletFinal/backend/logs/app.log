{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: REDIS_URL appears to be using a default/weak value\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: AWS_KMS_KEY_ID is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: AWS_REGION is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: SENTRY_DSN is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: FIREBASE_PRIVATE_KEY is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: FIREBASE_CLIENT_EMAIL is recommended for production deployment\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: MONGODB_URI appears to contain development URL in production\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: REDIS_URL appears to contain development URL in production\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment validation completed successfully\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"hasAWS":false,"hasBlockchain":true,"hasDatabase":true,"hasFirebase":false,"hasMonitoring":false,"hasRedis":true,"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment configuration:\u001b[39m","nodeEnv":"production","port":"3000","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mKryptoPesa API server running on port 3000\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: production\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis Client Connected\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB Connected: localhost\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis Client Ready\u001b[39m","timestamp":"2025-07-03 20:20:43:2043"}
{"endpoint":"/health","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mSuspicious user agent detected:\u001b[39m","timestamp":"2025-07-03 20:21:15:2115","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"\u001b[32minfo\u001b[39m","memoryDelta":{"heapUsed":"0.23MB","rss":"3.98MB"},"message":"\u001b[32mRequest completed\u001b[39m","method":"GET","responseTime":"7.02ms","statusCode":503,"timestamp":"2025-07-03 20:21:15:2115","url":"/","userAgent":"curl/8.7.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [03/Jul/2025:17:21:15 +0000] \"GET /health HTTP/1.1\" 503 590 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-07-03 20:21:15:2115"}
{"endpoint":"/metrics","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mSuspicious user agent detected:\u001b[39m","timestamp":"2025-07-03 20:21:43:2143","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"\u001b[32minfo\u001b[39m","memoryDelta":{"heapUsed":"0.07MB","rss":"3.34MB"},"message":"\u001b[32mRequest completed\u001b[39m","method":"GET","responseTime":"1.34ms","statusCode":200,"timestamp":"2025-07-03 20:21:43:2143","url":"/","userAgent":"curl/8.7.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [03/Jul/2025:17:21:43 +0000] \"GET /metrics HTTP/1.1\" 200 570 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-07-03 20:21:43:2143"}
{"endpoint":"/metrics/prometheus","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mSuspicious user agent detected:\u001b[39m","timestamp":"2025-07-03 20:22:01:221","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"\u001b[32minfo\u001b[39m","memoryDelta":{"heapUsed":"0.06MB","rss":"2.70MB"},"message":"\u001b[32mRequest completed\u001b[39m","method":"GET","responseTime":"1.45ms","statusCode":200,"timestamp":"2025-07-03 20:22:01:221","url":"/prometheus","userAgent":"curl/8.7.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [03/Jul/2025:17:22:01 +0000] \"GET /metrics/prometheus HTTP/1.1\" 200 1329 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-07-03 20:22:01:221"}
{"endpoint":"/health","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mSuspicious user agent detected:\u001b[39m","timestamp":"2025-07-03 20:35:57:3557","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"\u001b[32minfo\u001b[39m","memoryDelta":{"heapUsed":"0.14MB","rss":"5.59MB"},"message":"\u001b[32mRequest completed\u001b[39m","method":"GET","responseTime":"5.04ms","statusCode":503,"timestamp":"2025-07-03 20:35:57:3557","url":"/","userAgent":"curl/8.7.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [03/Jul/2025:17:35:57 +0000] \"GET /health HTTP/1.1\" 503 593 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-07-03 20:35:57:3557"}
