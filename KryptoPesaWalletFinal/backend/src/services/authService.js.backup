const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

class AuthService {
  constructor() {
    this.jwtConfig = {
      accessTokenSecret: process.env.JWT_SECRET,
      refreshTokenSecret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET + '_refresh',
      accessTokenExpiry: process.env.JWT_EXPIRES_IN || '15m',
      refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
      algorithm: 'HS256',
      issuer: 'kryptopesa.com',
      audience: 'kryptopesa-users'
    };
    
    this.redisClient = getRedisClient();
  }

  /**
   * Generate access and refresh tokens
   */
  async generateTokens(userId, sessionId = null) {
    try {
      const payload = {
        userId: userId.toString(),
        sessionId: sessionId || crypto.randomUUID(),
        type: 'access'
      };

      const refreshPayload = {
        userId: userId.toString(),
        sessionId: payload.sessionId,
        type: 'refresh'
      };

      const accessToken = jwt.sign(payload, this.jwtConfig.accessTokenSecret, {
        expiresIn: this.jwtConfig.accessTokenExpiry,
        algorithm: this.jwtConfig.algorithm,
        issuer: this.jwtConfig.issuer,
        audience: this.jwtConfig.audience
      });

      const refreshToken = jwt.sign(refreshPayload, this.jwtConfig.refreshTokenSecret, {
        expiresIn: this.jwtConfig.refreshTokenExpiry,
        algorithm: this.jwtConfig.algorithm,
        issuer: this.jwtConfig.issuer,
        audience: this.jwtConfig.audience
      });

      // Store refresh token in Redis with expiration
      if (this.redisClient && this.redisClient.isReady) {
        const refreshTokenKey = `refresh_token:${userId}:${payload.sessionId}`;
        const refreshTokenHash = crypto.createHash('sha256').update(refreshToken).digest('hex');
        
        await this.redisClient.setEx(
          refreshTokenKey,
          7 * 24 * 60 * 60, // 7 days in seconds
          refreshTokenHash
        );

        // Store session info
        const sessionKey = `session:${userId}:${payload.sessionId}`;
        await this.redisClient.setEx(
          sessionKey,
          7 * 24 * 60 * 60,
          JSON.stringify({
            userId: userId.toString(),
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString()
          })
        );
      }

      return {
        accessToken,
        refreshToken,
        sessionId: payload.sessionId,
        expiresIn: this.jwtConfig.accessTokenExpiry
      };
    } catch (error) {
      logger.error('Token generation failed:', error);
      throw new AppError('Token generation failed', 500);
    }
  }

  /**
   * Verify access token
   */
  async verifyAccessToken(token) {
    try {
      const decoded = jwt.verify(token, this.jwtConfig.accessTokenSecret, {
        algorithms: [this.jwtConfig.algorithm],
        issuer: this.jwtConfig.issuer,
        audience: this.jwtConfig.audience
      });

      if (decoded.type !== 'access') {
        throw new AppError('Invalid token type', 401);
      }

      // Check if session is still valid
      if (this.redisClient && this.redisClient.isReady) {
        const sessionKey = `session:${decoded.userId}:${decoded.sessionId}`;
        const sessionData = await this.redisClient.get(sessionKey);
        
        if (!sessionData) {
          throw new AppError('Session expired or invalid', 401);
        }

        // Update last activity
        const session = JSON.parse(sessionData);
        session.lastActivity = new Date().toISOString();
        await this.redisClient.setEx(sessionKey, 7 * 24 * 60 * 60, JSON.stringify(session));
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AppError('Invalid token', 401);
      }
      if (error instanceof jwt.TokenExpiredError) {
        throw new AppError('Token expired', 401);
      }
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken) {
    try {
      const decoded = jwt.verify(refreshToken, this.jwtConfig.refreshTokenSecret, {
        algorithms: [this.jwtConfig.algorithm],
        issuer: this.jwtConfig.issuer,
        audience: this.jwtConfig.audience
      });

      if (decoded.type !== 'refresh') {
        throw new AppError('Invalid token type', 401);
      }

      // Verify refresh token exists in Redis
      if (this.redisClient && this.redisClient.isReady) {
        const refreshTokenKey = `refresh_token:${decoded.userId}:${decoded.sessionId}`;
        const storedTokenHash = await this.redisClient.get(refreshTokenKey);
        const providedTokenHash = crypto.createHash('sha256').update(refreshToken).digest('hex');

        if (!storedTokenHash || storedTokenHash !== providedTokenHash) {
          throw new AppError('Invalid refresh token', 401);
        }
      }

      // Generate new access token with same session ID
      const tokens = await this.generateTokens(decoded.userId, decoded.sessionId);
      
      return {
        accessToken: tokens.accessToken,
        expiresIn: tokens.expiresIn
      };
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AppError('Invalid refresh token', 401);
      }
      if (error instanceof jwt.TokenExpiredError) {
        throw new AppError('Refresh token expired', 401);
      }
      throw error;
    }
  }

  /**
   * Revoke refresh token (logout)
   */
  async revokeRefreshToken(userId, sessionId) {
    try {
      if (this.redisClient && this.redisClient.isReady) {
        const refreshTokenKey = `refresh_token:${userId}:${sessionId}`;
        const sessionKey = `session:${userId}:${sessionId}`;
        
        await Promise.all([
          this.redisClient.del(refreshTokenKey),
          this.redisClient.del(sessionKey)
        ]);
      }
      
      logger.info(`Session revoked for user ${userId}, session ${sessionId}`);
    } catch (error) {
      logger.error('Token revocation failed:', error);
      throw new AppError('Logout failed', 500);
    }
  }

  /**
   * Revoke all user sessions
   */
  async revokeAllUserSessions(userId) {
    try {
      if (this.redisClient && this.redisClient.isReady) {
        const pattern = `*:${userId}:*`;
        // Note: In production, use SCAN instead of KEYS for better performance
        const keys = await this.redisClient.keys(pattern);
        
        if (keys.length > 0) {
          await this.redisClient.del(keys);
        }
      }
      
      logger.info(`All sessions revoked for user ${userId}`);
    } catch (error) {
      logger.error('All sessions revocation failed:', error);
      throw new AppError('Session revocation failed', 500);
    }
  }

  /**
   * Generate 2FA secret and QR code
   */
  async generate2FASecret(userId, userEmail) {
    try {
      const secret = speakeasy.generateSecret({
        name: `KryptoPesa (${userEmail})`,
        issuer: 'KryptoPesa',
        length: 32
      });

      // Generate QR code
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

      return {
        secret: secret.base32,
        qrCode: qrCodeUrl,
        manualEntryKey: secret.base32
      };
    } catch (error) {
      logger.error('2FA secret generation failed:', error);
      throw new AppError('2FA setup failed', 500);
    }
  }

  /**
   * Verify 2FA token
   */
  verify2FAToken(secret, token) {
    try {
      return speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token,
        window: 2 // Allow 2 time steps (60 seconds) tolerance
      });
    } catch (error) {
      logger.error('2FA verification failed:', error);
      return false;
    }
  }

  /**
   * Enhanced password validation
   */
  validatePassword(password) {
    const errors = [];
    
    if (password.length < 12) {
      errors.push('Password must be at least 12 characters long');
    }
    
    if (password.length > 128) {
      errors.push('Password must not exceed 128 characters');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    // Check for common patterns
    const commonPatterns = [
      /(.)\1{3,}/, // Repeated characters
      /123456|654321|qwerty|password|admin/i, // Common sequences
    ];
    
    for (const pattern of commonPatterns) {
      if (pattern.test(password)) {
        errors.push('Password contains common patterns and is not secure');
        break;
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Hash password with enhanced security
   */
  async hashPassword(password) {
    try {
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
      return await bcrypt.hash(password, saltRounds);
    } catch (error) {
      logger.error('Password hashing failed:', error);
      throw new AppError('Password processing failed', 500);
    }
  }

  /**
   * Get active sessions for user
   */
  async getUserSessions(userId) {
    try {
      if (!this.redisClient || !this.redisClient.isReady) {
        return [];
      }

      const pattern = `session:${userId}:*`;
      const keys = await this.redisClient.keys(pattern);
      
      const sessions = [];
      for (const key of keys) {
        const sessionData = await this.redisClient.get(key);
        if (sessionData) {
          const session = JSON.parse(sessionData);
          const sessionId = key.split(':')[2];
          sessions.push({
            sessionId,
            ...session
          });
        }
      }
      
      return sessions;
    } catch (error) {
      logger.error('Failed to get user sessions:', error);
      return [];
    }
  }
}

module.exports = new AuthService();
