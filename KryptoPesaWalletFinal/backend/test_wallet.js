const { ethers } = require('ethers');
const bip39 = require('bip39');
const crypto = require('crypto');

async function testWalletCreation() {
  try {
    console.log('Testing wallet creation...');
    
    // Generate mnemonic
    const mnemonic = bip39.generateMnemonic(256);
    console.log('Generated mnemonic:', mnemonic);
    
    // Test Ethereum wallet creation
    console.log('Creating Ethereum wallet...');
    const ethWallet = ethers.Wallet.fromMnemonic(mnemonic);
    console.log('Ethereum address:', ethWallet.address);
    console.log('Ethereum public key:', ethWallet.publicKey);
    
    // Test Bitcoin wallet creation
    console.log('Creating Bitcoin wallet...');
    const bitcoin = require('bitcoinjs-lib');
    const BIP32Factory = require('bip32').default;
    const ecc = require('tiny-secp256k1');
    const bip32 = BIP32Factory(ecc);

    const seed = bip39.mnemonicToSeedSync(mnemonic);
    const network = bitcoin.networks.testnet;
    const root = bip32.fromSeed(seed, network);
    const derivationPath = "m/44'/1'/0'/0/0";
    const child = root.derivePath(derivationPath);
    
    const { address } = bitcoin.payments.p2wpkh({
      pubkey: Buffer.from(child.publicKey),
      network
    });
    
    console.log('Bitcoin address:', address);
    console.log('Bitcoin public key:', child.publicKey.toString('hex'));
    
    // Test mnemonic hash
    const mnemonicHash = crypto.createHash('sha256').update(mnemonic).digest('hex');
    console.log('Mnemonic hash:', mnemonicHash);
    
    console.log('✅ Wallet creation test successful!');
    
  } catch (error) {
    console.error('❌ Wallet creation test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testWalletCreation();
