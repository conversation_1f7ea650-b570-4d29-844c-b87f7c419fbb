/**
 * Integration Tests for Wallet Authentication API
 * Tests the complete wallet-based authentication flow
 */

const request = require('supertest');
const { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } = require('@jest/globals');
const mongoose = require('mongoose');
const { ethers } = require('ethers');
const app = require('../../../src/server').app;
const User = require('../../../src/models/User');
const { connectDB } = require('../../../src/config/database');

describe('Wallet Authentication API Integration Tests', () => {
  let testWallet;
  let testUser;
  let authToken;

  beforeAll(async () => {
    // Connect to test database
    process.env.NODE_ENV = 'test';
    process.env.MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/kryptopesa_test';
    
    await connectDB();
    
    // Create test wallet
    testWallet = ethers.Wallet.createRandom();
  });

  afterAll(async () => {
    // Clean up test database
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear users collection before each test
    await User.deleteMany({});
  });

  afterEach(async () => {
    // Clean up after each test
    await User.deleteMany({});
  });

  describe('POST /api/auth/wallet-login', () => {
    it('should create new user and authenticate with valid wallet signature', async () => {
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message,
          timestamp: Date.now()
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.walletAddress).toBe(testWallet.address.toLowerCase());

      // Verify user was created in database
      const user = await User.findOne({ walletAddress: testWallet.address.toLowerCase() });
      expect(user).toBeTruthy();
      expect(user.isActive).toBe(true);

      authToken = response.body.data.token;
    });

    it('should authenticate existing user with valid signature', async () => {
      // Create existing user
      const existingUser = new User({
        walletAddress: testWallet.address.toLowerCase(),
        profile: {
          firstName: 'Test',
          lastName: 'User'
        },
        isActive: true
      });
      await existingUser.save();

      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message,
          timestamp: Date.now()
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.profile.firstName).toBe('Test');
    });

    it('should reject invalid signature', async () => {
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const invalidSignature = '0x' + '0'.repeat(130); // Invalid signature

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature: invalidSignature,
          message,
          timestamp: Date.now()
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('AUTH_INVALID_SIGNATURE');
    });

    it('should reject expired timestamp', async () => {
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);
      const expiredTimestamp = Date.now() - (10 * 60 * 1000); // 10 minutes ago

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message,
          timestamp: expiredTimestamp
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('AUTH_TOKEN_EXPIRED');
    });

    it('should reject invalid wallet address format', async () => {
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: 'invalid-address',
          signature,
          message,
          timestamp: Date.now()
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('VALIDATION_ERROR');
    });

    it('should handle rate limiting', async () => {
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);

      // Make multiple rapid requests
      const requests = Array(10).fill().map(() =>
        request(app)
          .post('/api/auth/wallet-login')
          .send({
            walletAddress: testWallet.address,
            signature,
            message,
            timestamp: Date.now()
          })
      );

      const responses = await Promise.allSettled(requests);
      
      // At least one should be rate limited
      const rateLimited = responses.some(r => 
        r.status === 'fulfilled' && r.value.status === 429
      );
      
      expect(rateLimited).toBe(true);
    });
  });

  describe('POST /api/auth/verify', () => {
    beforeEach(async () => {
      // Create authenticated user for verification tests
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message,
          timestamp: Date.now()
        });

      authToken = response.body.data.token;
      testUser = response.body.data.user;
    });

    it('should verify valid token and return user data', async () => {
      const response = await request(app)
        .post('/api/auth/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.walletAddress).toBe(testWallet.address.toLowerCase());
      expect(response.body.data.user.isActive).toBe(true);
    });

    it('should reject request without token', async () => {
      const response = await request(app)
        .post('/api/auth/verify')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('AUTHENTICATION_ERROR');
    });

    it('should reject invalid token', async () => {
      const response = await request(app)
        .post('/api/auth/verify')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('AUTH_TOKEN_INVALID');
    });

    it('should reject token for deactivated user', async () => {
      // Deactivate user
      await User.findByIdAndUpdate(testUser._id, { isActive: false });

      const response = await request(app)
        .post('/api/auth/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('AUTH_USER_DEACTIVATED');
    });
  });

  describe('POST /api/auth/refresh', () => {
    beforeEach(async () => {
      // Create authenticated user
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message,
          timestamp: Date.now()
        });

      authToken = response.body.data.token;
    });

    it('should refresh valid token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.token).not.toBe(authToken); // New token should be different
    });

    it('should reject refresh without token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('AUTHENTICATION_ERROR');
    });
  });

  describe('Security Features', () => {
    it('should prevent replay attacks with same signature', async () => {
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);
      const timestamp = Date.now();

      // First request should succeed
      const response1 = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message,
          timestamp
        })
        .expect(200);

      expect(response1.body.success).toBe(true);

      // Second request with same signature should fail
      const response2 = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message,
          timestamp
        })
        .expect(401);

      expect(response2.body.success).toBe(false);
      expect(response2.body.error.code).toBe('AUTH_REPLAY_ATTACK');
    });

    it('should validate message format', async () => {
      const invalidMessage = 'Invalid message format';
      const signature = await testWallet.signMessage(invalidMessage);

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message: invalidMessage,
          timestamp: Date.now()
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('VALIDATION_ERROR');
    });

    it('should handle concurrent login attempts', async () => {
      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);

      // Make concurrent requests
      const requests = Array(5).fill().map(() =>
        request(app)
          .post('/api/auth/wallet-login')
          .send({
            walletAddress: testWallet.address,
            signature,
            message,
            timestamp: Date.now()
          })
      );

      const responses = await Promise.allSettled(requests);
      
      // Only one should succeed, others should fail due to replay protection
      const successful = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      );
      
      expect(successful).toHaveLength(1);
    });
  });

  describe('Error Handling', () => {
    it('should return user-friendly error messages', async () => {
      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: 'invalid',
          signature: 'invalid',
          message: 'invalid',
          timestamp: 'invalid'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toHaveProperty('message');
      expect(response.body.error).toHaveProperty('type');
      expect(response.body.error).toHaveProperty('timestamp');
    });

    it('should handle database connection errors gracefully', async () => {
      // Temporarily close database connection
      await mongoose.connection.close();

      const message = `Login to KryptoPesa at ${Date.now()}`;
      const signature = await testWallet.signMessage(message);

      const response = await request(app)
        .post('/api/auth/wallet-login')
        .send({
          walletAddress: testWallet.address,
          signature,
          message,
          timestamp: Date.now()
        })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('DATABASE_ERROR');

      // Reconnect for other tests
      await connectDB();
    });
  });
});
