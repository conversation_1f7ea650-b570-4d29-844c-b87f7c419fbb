import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

export default function ImportWalletScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [mnemonic, setMnemonic] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  const validateMnemonic = (phrase: string): boolean => {
    const words = phrase.trim().toLowerCase().split(/\s+/);
    
    // Check if it's 12 or 24 words
    if (words.length !== 12 && words.length !== 24) {
      return false;
    }

    // Basic validation - in a real app, you'd use a proper BIP39 word list
    const validWords = words.every(word => word.length >= 3 && word.length <= 8);
    return validWords;
  };

  const handleImport = async () => {
    if (!mnemonic.trim()) {
      Alert.alert('Error', 'Please enter your recovery phrase');
      return;
    }

    setIsValidating(true);

    try {
      if (!validateMnemonic(mnemonic)) {
        Alert.alert(
          'Invalid Recovery Phrase',
          'Please check your recovery phrase and try again. It should be 12 or 24 words.'
        );
        return;
      }

      // Convert to array for consistency with create wallet flow
      const mnemonicArray = mnemonic.trim().toLowerCase().split(/\s+/);
      
      Alert.alert(
        'Wallet Import Successful',
        'Your wallet has been imported successfully. Please set a PIN to secure it.',
        [
          {
            text: 'Set PIN',
            onPress: () => navigation.navigate('SetPIN', { mnemonic: mnemonicArray })
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to import wallet. Please try again.');
    } finally {
      setIsValidating(false);
    }
  };

  const handlePasteFromClipboard = async () => {
    try {
      // In a real app, you'd use Clipboard.getString()
      Alert.alert('Info', 'Clipboard functionality would be implemented here');
    } catch (error) {
      Alert.alert('Error', 'Failed to read from clipboard');
    }
  };

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Import Wallet</Text>
        <View style={styles.headerRight} />
      </View>

      <KeyboardAvoidingView 
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.content}>
          <View style={styles.titleContainer}>
            <Ionicons name="download" size={48} color={theme.colors.primary} />
            <Text style={styles.title}>Import Your Wallet</Text>
            <Text style={styles.subtitle}>
              Enter your 12 or 24-word recovery phrase to restore your existing wallet.
            </Text>
          </View>

          <View style={styles.warningBox}>
            <Ionicons name="shield-checkmark" size={24} color={theme.colors.success} />
            <View style={styles.warningContent}>
              <Text style={styles.warningTitle}>Your Keys, Your Crypto</Text>
              <Text style={styles.warningText}>
                KryptoPesa never stores your recovery phrase. It stays secure on your device only.
              </Text>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <View style={styles.inputHeader}>
              <Text style={styles.inputLabel}>Recovery Phrase</Text>
              <TouchableOpacity 
                style={styles.pasteButton}
                onPress={handlePasteFromClipboard}
              >
                <Ionicons name="clipboard" size={16} color={theme.colors.primary} />
                <Text style={styles.pasteText}>Paste</Text>
              </TouchableOpacity>
            </View>
            
            <TextInput
              style={styles.mnemonicInput}
              placeholder="Enter your recovery phrase (12 or 24 words)"
              placeholderTextColor={theme.colors.inputPlaceholder}
              value={mnemonic}
              onChangeText={setMnemonic}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
              autoCapitalize="none"
              autoCorrect={false}
              spellCheck={false}
            />
            
            <Text style={styles.inputHint}>
              Separate each word with a space. Words should be lowercase.
            </Text>
          </View>

          <View style={styles.securityTips}>
            <Text style={styles.tipsTitle}>Security Tips:</Text>
            <View style={styles.tip}>
              <Ionicons name="eye-off" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.tipText}>Make sure no one can see your screen</Text>
            </View>
            <View style={styles.tip}>
              <Ionicons name="trash" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.tipText}>Clear clipboard after importing</Text>
            </View>
            <View style={styles.tip}>
              <Ionicons name="lock-closed" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.tipText}>Use a secure network connection</Text>
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={[
              styles.importButton,
              (!mnemonic.trim() || isValidating) && styles.disabledButton
            ]}
            onPress={handleImport}
            disabled={!mnemonic.trim() || isValidating}
          >
            <Text style={styles.importButtonText}>
              {isValidating ? 'Validating...' : 'Import Wallet'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.createNewButton}
            onPress={() => navigation.navigate('CreateWallet')}
          >
            <Text style={styles.createNewText}>
              Don't have a wallet? Create new wallet
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 40,
  },
  keyboardAvoid: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  warningBox: {
    flexDirection: 'row',
    backgroundColor: theme.colors.successLight,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  warningContent: {
    flex: 1,
    marginLeft: 12,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  warningText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  pasteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  pasteText: {
    fontSize: 14,
    color: theme.colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  mnemonicInput: {
    backgroundColor: theme.colors.inputBackground,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: theme.colors.inputText,
    minHeight: 120,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  inputHint: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
  },
  securityTips: {
    backgroundColor: theme.colors.cardBackground,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 8,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  importButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginBottom: 16,
  },
  disabledButton: {
    backgroundColor: theme.colors.disabled,
  },
  importButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  createNewButton: {
    alignItems: 'center',
    padding: 12,
  },
  createNewText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '500',
  },
});
