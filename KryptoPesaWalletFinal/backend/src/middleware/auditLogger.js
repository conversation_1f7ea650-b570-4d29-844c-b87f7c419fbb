const logger = require('../utils/logger');
const AuditLog = require('../models/AuditLog');

/**
 * Audit logging middleware for tracking user actions and system events
 */
const auditLogger = (action, resource) => {
  return async (req, res, next) => {
    const startTime = Date.now();
    
    // Store original res.json to capture response
    const originalJson = res.json;
    let responseData = null;
    let statusCode = null;

    res.json = function(data) {
      responseData = data;
      statusCode = res.statusCode;
      return originalJson.call(this, data);
    };

    // Continue with the request
    next();

    // Log after response is sent
    res.on('finish', async () => {
      try {
        const duration = Date.now() - startTime;
        const userId = req.user?._id;
        const userAgent = req.get('User-Agent');
        const ip = req.ip || req.connection.remoteAddress;

        // Create audit log entry
        const auditEntry = {
          userId,
          action,
          resource,
          resourceId: req.params.id || req.params.tradeId || req.params.offerId || req.params.disputeId,
          method: req.method,
          endpoint: req.originalUrl,
          statusCode,
          duration,
          ip,
          userAgent,
          requestData: {
            params: req.params,
            query: req.query,
            body: sanitizeRequestBody(req.body)
          },
          responseData: sanitizeResponseData(responseData),
          timestamp: new Date()
        };

        // Save to database
        await AuditLog.create(auditEntry);

        // Log to file for critical actions
        if (isCriticalAction(action)) {
          logger.warn('Critical action performed', {
            userId,
            action,
            resource,
            statusCode,
            ip,
            userAgent
          });
        }

      } catch (error) {
        logger.error('Failed to create audit log entry', {
          error: error.message,
          action,
          resource,
          userId: req.user?._id
        });
      }
    });
  };
};

/**
 * Sanitize request body to remove sensitive information
 */
const sanitizeRequestBody = (body) => {
  if (!body || typeof body !== 'object') return body;

  const sanitized = { ...body };
  
  // Remove sensitive fields
  const sensitiveFields = [
    'password',
    'confirmPassword',
    'mnemonic',
    'privateKey',
    'token',
    'refreshToken',
    'otp',
    'pin'
  ];

  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });

  return sanitized;
};

/**
 * Sanitize response data to remove sensitive information
 */
const sanitizeResponseData = (data) => {
  if (!data || typeof data !== 'object') return data;

  const sanitized = { ...data };

  // Remove sensitive response fields
  if (sanitized.data) {
    if (sanitized.data.token) sanitized.data.token = '[REDACTED]';
    if (sanitized.data.refreshToken) sanitized.data.refreshToken = '[REDACTED]';
    if (sanitized.data.mnemonic) sanitized.data.mnemonic = '[REDACTED]';
    if (sanitized.data.privateKey) sanitized.data.privateKey = '[REDACTED]';
  }

  return sanitized;
};

/**
 * Check if an action is considered critical and requires special logging
 */
const isCriticalAction = (action) => {
  const criticalActions = [
    'user_login',
    'user_register',
    'wallet_create',
    'wallet_import',
    'trade_create',
    'trade_fund',
    'trade_complete',
    'trade_cancel',
    'dispute_create',
    'dispute_resolve',
    'admin_action',
    'password_change',
    'email_change',
    'kyc_submit',
    'kyc_approve',
    'kyc_reject'
  ];

  return criticalActions.includes(action);
};

/**
 * Create audit log entry manually (for use outside middleware)
 */
const createAuditLog = async (data) => {
  try {
    await AuditLog.create({
      ...data,
      timestamp: new Date()
    });
  } catch (error) {
    logger.error('Failed to create manual audit log entry', {
      error: error.message,
      data
    });
  }
};

/**
 * Get audit logs with filtering and pagination
 */
const getAuditLogs = async (filters = {}, options = {}) => {
  try {
    const {
      userId,
      action,
      resource,
      startDate,
      endDate,
      page = 1,
      limit = 50
    } = filters;

    const query = {};

    if (userId) query.userId = userId;
    if (action) query.action = action;
    if (resource) query.resource = resource;
    
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    const skip = (page - 1) * limit;

    const [logs, total] = await Promise.all([
      AuditLog.find(query)
        .populate('userId', 'username email')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      AuditLog.countDocuments(query)
    ]);

    return {
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };

  } catch (error) {
    logger.error('Failed to fetch audit logs', {
      error: error.message,
      filters
    });
    throw error;
  }
};

module.exports = {
  auditLogger,
  createAuditLog,
  getAuditLogs,
  isCriticalAction
};
