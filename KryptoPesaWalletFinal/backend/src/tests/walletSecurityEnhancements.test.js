const request = require('supertest');
const app = require('../app');
const User = require('../models/User');
const Wallet = require('../models/Wallet');
const AuditLog = require('../models/AuditLog');
const { connectDB, disconnectDB, clearDB } = require('./helpers/database');
const walletService = require('../services/walletService');
const { getRedisClient } = require('../config/redis');

describe('Wallet Security Enhancements', () => {
  let testUser;
  let authToken;
  let redisClient;

  beforeAll(async () => {
    await connectDB();
    redisClient = await getRedisClient();
  });

  afterAll(async () => {
    if (redisClient) {
      await redisClient.quit();
    }
    await disconnectDB();
  });

  beforeEach(async () => {
    await clearDB();
    
    // Create test user
    testUser = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      isVerified: true
    });

    // Get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });

    authToken = loginResponse.body.token;
  });

  afterEach(async () => {
    await clearDB();
  });

  describe('Enhanced Mnemonic Security', () => {
    test('should audit mnemonic access during wallet creation', async () => {
      const response = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.mnemonic).toBeDefined();
      expect(response.body.data.securityWarning).toContain('CRITICAL');

      // Check audit logs
      const auditLogs = await AuditLog.find({ 
        userId: testUser._id,
        action: { $regex: /^mnemonic_/ }
      });

      expect(auditLogs.length).toBeGreaterThan(0);
      
      const creationAttempt = auditLogs.find(log => log.action === 'mnemonic_wallet_creation_attempt');
      const generation = auditLogs.find(log => log.action === 'mnemonic_mnemonic_generation');
      const success = auditLogs.find(log => log.action === 'mnemonic_wallet_creation_success');

      expect(creationAttempt).toBeDefined();
      expect(generation).toBeDefined();
      expect(success).toBeDefined();
    });

    test('should validate mnemonic strength correctly', async () => {
      const testCases = [
        {
          mnemonic: 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about',
          expected: { valid: true, strength: 'medium', wordCount: 12 }
        },
        {
          mnemonic: 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon art',
          expected: { valid: true, strength: 'very_strong', wordCount: 24 }
        },
        {
          mnemonic: 'invalid mnemonic phrase',
          expected: { valid: false, reason: 'invalid_format' }
        },
        {
          mnemonic: 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon extra',
          expected: { valid: false, reason: 'invalid_word_count' }
        }
      ];

      for (const testCase of testCases) {
        const result = walletService.validateMnemonicStrength(testCase.mnemonic);
        expect(result.valid).toBe(testCase.expected.valid);
        
        if (testCase.expected.valid) {
          expect(result.strength).toBe(testCase.expected.strength);
          expect(result.wordCount).toBe(testCase.expected.wordCount);
        } else {
          expect(result.reason).toBe(testCase.expected.reason);
        }
      }
    });

    test('should securely verify mnemonic with audit trail', async () => {
      // Create wallet first
      const createResponse = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(201);

      const mnemonic = createResponse.body.data.mnemonic;

      // Test correct mnemonic verification
      const correctResult = await walletService.verifyMnemonic(testUser._id, mnemonic, {
        ip: '127.0.0.1',
        userAgent: 'test-agent'
      });
      expect(correctResult).toBe(true);

      // Test incorrect mnemonic verification
      const incorrectResult = await walletService.verifyMnemonic(testUser._id, 'wrong mnemonic phrase', {
        ip: '127.0.0.1',
        userAgent: 'test-agent'
      });
      expect(incorrectResult).toBe(false);

      // Check audit logs
      const verificationLogs = await AuditLog.find({ 
        userId: testUser._id,
        action: { $regex: /^mnemonic_mnemonic_verification/ }
      });

      expect(verificationLogs.length).toBeGreaterThanOrEqual(4); // 2 attempts + 2 results
    });
  });

  describe('Balance Update Throttling', () => {
    beforeEach(async () => {
      // Create wallet for testing
      await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(201);
    });

    test('should allow balance updates within limits', async () => {
      const response = await request(app)
        .post('/api/wallet/balances/refresh')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('should throttle frequent balance updates', async () => {
      // First update should succeed
      await request(app)
        .post('/api/wallet/balances/refresh')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Immediate second update should be throttled
      const response = await request(app)
        .post('/api/wallet/balances/refresh')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(429);

      expect(response.body.error).toContain('limited');
    });

    test('should cache balance data during throttling', async () => {
      // First update to populate cache
      await request(app)
        .post('/api/wallet/balances/refresh')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Second update should return cached data
      const response = await request(app)
        .post('/api/wallet/balances/refresh')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.fromCache).toBe(true);
    });

    test('should track balance update statistics', async () => {
      const stats = await walletService.getRecentBalanceUpdates(testUser._id);
      expect(stats).toHaveProperty('count');
      expect(stats).toHaveProperty('limit');
    });
  });

  describe('Security Status and Monitoring', () => {
    beforeEach(async () => {
      // Create wallet for testing
      await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(201);
    });

    test('should calculate security score correctly', async () => {
      const wallet = await Wallet.findOne({ user: testUser._id });
      const score = walletService.calculateSecurityScore(wallet);
      
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
      expect(typeof score).toBe('number');
    });

    test('should provide security recommendations', async () => {
      const wallet = await Wallet.findOne({ user: testUser._id });
      const recommendations = walletService.getSecurityRecommendations(wallet);
      
      expect(Array.isArray(recommendations)).toBe(true);
      
      // Should recommend backup completion for new wallet
      const backupRecommendation = recommendations.find(r => r.type === 'backup');
      expect(backupRecommendation).toBeDefined();
      expect(backupRecommendation.priority).toBe('high');
    });

    test('should get comprehensive security status', async () => {
      const status = await walletService.getWalletSecurityStatus(testUser._id);
      
      expect(status).toHaveProperty('hasBackup');
      expect(status).toHaveProperty('mnemonicHashExists');
      expect(status).toHaveProperty('securityScore');
      expect(status).toHaveProperty('recommendations');
      expect(status).toHaveProperty('recentUpdateCount');
      
      expect(status.hasBackup).toBe(false); // New wallet
      expect(status.mnemonicHashExists).toBe(true);
      expect(Array.isArray(status.recommendations)).toBe(true);
    });

    test('should mark backup as completed with audit', async () => {
      const result = await walletService.markBackupCompleted(testUser._id, {
        ip: '127.0.0.1',
        userAgent: 'test-agent'
      });
      
      expect(result).toBe(true);
      
      // Check wallet was updated
      const wallet = await Wallet.findOne({ user: testUser._id });
      expect(wallet.security.backupCompleted).toBe(true);
      expect(wallet.security.backupDate).toBeDefined();
      
      // Check audit log
      const auditLog = await AuditLog.findOne({ 
        userId: testUser._id,
        action: 'mnemonic_backup_completed'
      });
      expect(auditLog).toBeDefined();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle Redis unavailability gracefully', async () => {
      // Temporarily disable Redis
      const originalRedisClient = walletService.redisClient;
      walletService.redisClient = null;
      
      try {
        const throttleCheck = await walletService.checkBalanceUpdateThrottling(testUser._id);
        expect(throttleCheck.allowed).toBe(true);
        expect(throttleCheck.reason).toBe('redis_unavailable');
      } finally {
        walletService.redisClient = originalRedisClient;
      }
    });

    test('should handle invalid user ID in security operations', async () => {
      const invalidUserId = '507f1f77bcf86cd799439011';
      
      await expect(walletService.getWalletSecurityStatus(invalidUserId))
        .rejects.toThrow('Wallet not found');
    });

    test('should validate mnemonic format edge cases', async () => {
      const edgeCases = [
        '',
        '   ',
        null,
        undefined,
        'single',
        'too many words '.repeat(30).trim()
      ];
      
      for (const testCase of edgeCases) {
        const result = walletService.validateMnemonicFormat(testCase);
        expect(result).toBe(false);
      }
    });
  });
});
