const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const { AppError } = require('./errorHandler');
const fileSecurityService = require('../services/fileSecurityService');
const logger = require('../utils/logger');

/**
 * Enhanced secure file upload middleware with comprehensive validation
 */
class SecureFileUploadMiddleware {
  constructor() {
    this.tempUploadDir = path.join(__dirname, '../../temp-uploads');
    this.initializeTempDirectory();
  }

  async initializeTempDirectory() {
    try {
      await fs.mkdir(this.tempUploadDir, { recursive: true });
    } catch (error) {
      logger.error('Failed to initialize temp upload directory:', error);
    }
  }

  /**
   * Create secure multer configuration
   */
  createSecureUpload(options = {}) {
    const {
      maxFiles = 5,
      maxFileSize = 10 * 1024 * 1024, // 10MB
      allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'application/pdf'],
      context = 'general'
    } = options;

    const storage = multer.diskStorage({
      destination: async (req, file, cb) => {
        try {
          // Create user-specific temp directory
          const userTempDir = path.join(this.tempUploadDir, req.user._id.toString());
          await fs.mkdir(userTempDir, { recursive: true });
          cb(null, userTempDir);
        } catch (error) {
          cb(error);
        }
      },
      filename: (req, file, cb) => {
        // Generate temporary filename with timestamp and random suffix
        const timestamp = Date.now();
        const randomSuffix = crypto.randomBytes(8).toString('hex');
        const ext = path.extname(file.originalname);
        cb(null, `temp_${timestamp}_${randomSuffix}${ext}`);
      }
    });

    const fileFilter = (req, file, cb) => {
      try {
        // Basic MIME type check
        if (!allowedMimeTypes.includes(file.mimetype)) {
          return cb(new AppError(`File type ${file.mimetype} is not allowed`, 400), false);
        }

        // Check filename for dangerous patterns
        if (this.isDangerousFilename(file.originalname)) {
          return cb(new AppError('Filename contains dangerous characters', 400), false);
        }

        // Check file extension
        const ext = path.extname(file.originalname).toLowerCase();
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.pdf'];
        if (!allowedExtensions.includes(ext)) {
          return cb(new AppError(`File extension ${ext} is not allowed`, 400), false);
        }

        cb(null, true);
      } catch (error) {
        cb(error, false);
      }
    };

    const upload = multer({
      storage,
      fileFilter,
      limits: {
        fileSize: maxFileSize,
        files: maxFiles,
        fieldSize: 1024 * 1024, // 1MB field size limit
        fieldNameSize: 100,
        headerPairs: 2000
      }
    });

    // Return middleware function that includes security processing
    return (req, res, next) => {
      const uploadHandler = upload.array('files', maxFiles);
      
      uploadHandler(req, res, async (err) => {
        if (err) {
          // Handle multer errors
          if (err instanceof multer.MulterError) {
            switch (err.code) {
              case 'LIMIT_FILE_SIZE':
                return next(new AppError(`File size exceeds limit of ${maxFileSize / (1024 * 1024)}MB`, 400));
              case 'LIMIT_FILE_COUNT':
                return next(new AppError(`Too many files. Maximum ${maxFiles} files allowed`, 400));
              case 'LIMIT_UNEXPECTED_FILE':
                return next(new AppError('Unexpected file field', 400));
              default:
                return next(new AppError(`Upload error: ${err.message}`, 400));
            }
          }
          return next(err);
        }

        // Process uploaded files through security service
        if (req.files && req.files.length > 0) {
          try {
            const secureFiles = [];
            
            for (const file of req.files) {
              logger.info(`Processing uploaded file: ${file.originalname}`, {
                userId: req.user._id,
                context,
                size: file.size,
                mimetype: file.mimetype
              });

              // Run comprehensive security validation
              const secureFile = await fileSecurityService.validateAndSecureFile(
                file.path,
                file.originalname,
                req.user._id,
                context
              );

              secureFiles.push(secureFile);
            }

            // Replace req.files with secure file information
            req.secureFiles = secureFiles;
            req.uploadContext = context;

            logger.info(`File upload security processing completed`, {
              userId: req.user._id,
              context,
              fileCount: secureFiles.length
            });

          } catch (error) {
            // Clean up any remaining temp files
            await this.cleanupTempFiles(req.files);
            return next(error);
          }
        }

        next();
      });
    };
  }

  /**
   * Middleware for payment proof uploads
   */
  paymentProofUpload() {
    return this.createSecureUpload({
      maxFiles: 5,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      context: 'payment-proofs'
    });
  }

  /**
   * Middleware for chat attachments
   */
  chatAttachmentUpload() {
    return this.createSecureUpload({
      maxFiles: 3,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      context: 'chat-attachments'
    });
  }

  /**
   * Middleware for document uploads
   */
  documentUpload() {
    return this.createSecureUpload({
      maxFiles: 1,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['application/pdf', 'image/jpeg', 'image/png'],
      context: 'documents'
    });
  }

  /**
   * Rate limiting for file uploads
   */
  uploadRateLimit() {
    const uploadAttempts = new Map();
    const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
    const MAX_UPLOADS_PER_WINDOW = 20;

    return (req, res, next) => {
      const userId = req.user._id.toString();
      const now = Date.now();
      
      // Clean up old entries
      for (const [key, data] of uploadAttempts.entries()) {
        if (now - data.firstAttempt > RATE_LIMIT_WINDOW) {
          uploadAttempts.delete(key);
        }
      }

      // Check current user's upload rate
      const userAttempts = uploadAttempts.get(userId);
      
      if (!userAttempts) {
        uploadAttempts.set(userId, {
          count: 1,
          firstAttempt: now
        });
      } else {
        if (now - userAttempts.firstAttempt > RATE_LIMIT_WINDOW) {
          // Reset window
          uploadAttempts.set(userId, {
            count: 1,
            firstAttempt: now
          });
        } else {
          userAttempts.count++;
          
          if (userAttempts.count > MAX_UPLOADS_PER_WINDOW) {
            logger.warn(`Upload rate limit exceeded`, {
              userId,
              attempts: userAttempts.count,
              ip: req.ip
            });
            
            return next(new AppError('Upload rate limit exceeded. Please try again later.', 429));
          }
        }
      }

      next();
    };
  }

  /**
   * Cleanup temporary files
   */
  async cleanupTempFiles(files) {
    if (!files || !Array.isArray(files)) return;

    for (const file of files) {
      try {
        await fs.unlink(file.path);
      } catch (error) {
        logger.error(`Failed to cleanup temp file: ${file.path}`, error);
      }
    }
  }

  /**
   * Check for dangerous filename patterns
   */
  isDangerousFilename(filename) {
    const dangerousPatterns = [
      /\.\./,  // Directory traversal
      /[<>:"|?*]/,  // Windows reserved characters
      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Windows reserved names
      /\.(exe|bat|cmd|scr|pif|com|vbs|js|jar|app|deb|rpm)$/i,  // Executable extensions
      /^\./,  // Hidden files
      /\s+$/,  // Trailing whitespace
      /[^\x20-\x7E]/,  // Non-printable characters
    ];

    return dangerousPatterns.some(pattern => pattern.test(filename));
  }

  /**
   * Middleware to validate file upload context
   */
  validateUploadContext(allowedContexts) {
    return (req, res, next) => {
      const { context } = req.body;
      
      if (!context || !allowedContexts.includes(context)) {
        return next(new AppError(`Invalid upload context. Allowed: ${allowedContexts.join(', ')}`, 400));
      }

      next();
    };
  }

  /**
   * Middleware to check user upload quota
   */
  checkUploadQuota() {
    return async (req, res, next) => {
      try {
        const userId = req.user._id.toString();
        const userRole = req.user.role;
        
        // Define quota limits by role
        const quotaLimits = {
          user: 100 * 1024 * 1024, // 100MB
          premium: 500 * 1024 * 1024, // 500MB
          admin: 1024 * 1024 * 1024, // 1GB
          moderator: 1024 * 1024 * 1024 // 1GB
        };

        const userQuota = quotaLimits[userRole] || quotaLimits.user;

        // Calculate current usage (this would typically be stored in database)
        // For now, we'll implement a basic Redis-based tracking
        const { getRedisClient } = require('../config/redis');
        const redisClient = getRedisClient();
        
        if (redisClient && redisClient.isReady) {
          const currentUsage = await redisClient.get(`user_quota:${userId}`) || 0;
          const uploadSize = req.files ? req.files.reduce((total, file) => total + file.size, 0) : 0;
          
          if (parseInt(currentUsage) + uploadSize > userQuota) {
            return next(new AppError('Upload quota exceeded', 413));
          }
        }

        next();
      } catch (error) {
        logger.error('Error checking upload quota:', error);
        next(error);
      }
    };
  }
}

// Export singleton instance
module.exports = new SecureFileUploadMiddleware();
