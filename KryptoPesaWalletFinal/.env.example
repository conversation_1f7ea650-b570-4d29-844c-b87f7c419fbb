# KryptoPesa Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/kryptopesa
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret

# Blockchain Configuration
# Polygon Network (Primary)
POLYGON_RPC_URL=https://polygon-rpc.com
POLYGON_CHAIN_ID=137
POLYGON_PRIVATE_KEY=your-polygon-private-key

# Ethereum Network (Fallback)
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-infura-key
ETHEREUM_CHAIN_ID=1
ETHEREUM_PRIVATE_KEY=your-ethereum-private-key

# Bitcoin Network
BITCOIN_NETWORK=mainnet
BITCOIN_RPC_URL=https://blockstream.info/api

# Smart Contract Addresses (Deploy and update these)
ESCROW_CONTRACT_ADDRESS=0x...
USDT_CONTRACT_ADDRESS=******************************************
USDC_CONTRACT_ADDRESS=******************************************

# External APIs
COINGECKO_API_KEY=your-coingecko-api-key
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key

# Push Notifications
FIREBASE_SERVER_KEY=your-firebase-server-key
FIREBASE_PROJECT_ID=your-firebase-project-id

# File Upload
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-key
CLOUDINARY_API_SECRET=your-cloudinary-secret

# Email Configuration (for admin notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Security
BCRYPT_ROUNDS=12
ENCRYPTION_KEY=your-32-character-encryption-key-change-this-in-production

# AWS Configuration (for enhanced key management)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_KMS_KEY_ID=your-aws-kms-key-id
AWS_SECRETS_MANAGER_REGION=us-east-1

# Key Management Configuration
KEY_ROTATION_INTERVAL_DAYS=30
KEY_ROTATION_WARNING_DAYS=7
KEY_ACCESS_LOG_RETENTION_DAYS=90
ENABLE_KEY_MONITORING=true
ENABLE_FALLBACK_STORAGE=true

# Rate Limiting Configuration (Production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Comprehensive Monitoring Configuration
EMAIL_ALERTS_ENABLED=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
ALERT_RECIPIENTS=<EMAIL>

# Discord Alerts
DISCORD_ALERTS_ENABLED=false
DISCORD_WEBHOOK_URL=your-discord-webhook-url

# Slack Alerts
SLACK_ALERTS_ENABLED=false
SLACK_WEBHOOK_URL=your-slack-webhook-url

# Metrics Authentication
METRICS_TOKEN=your-metrics-token

# Authentication Rate Limits
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_LOGIN_MAX=3
RATE_LIMIT_PASSWORD_RESET_MAX=3

# Trading Rate Limits
RATE_LIMIT_TRADING_MAX=10
RATE_LIMIT_TRADE_CREATION_MAX=5

# Wallet Rate Limits
RATE_LIMIT_WALLET_MAX=20
RATE_LIMIT_WALLET_TX_MAX=5

# Chat Rate Limits
RATE_LIMIT_CHAT_MAX=30

# File Upload Rate Limits
RATE_LIMIT_FILE_UPLOAD_MAX=10

# Admin Rate Limits
RATE_LIMIT_ADMIN_MAX=50

# General API Rate Limits
RATE_LIMIT_GENERAL_MAX=1000
RATE_LIMIT_PUBLIC_MAX=100
RATE_LIMIT_HEALTH_MAX=200

# Advanced Rate Limiting
RATE_LIMIT_STRICT_MAX=3
RATE_LIMIT_PROGRESSIVE_BASE=100
RATE_LIMIT_PROGRESSIVE_WINDOW=900000

# Rate Limiting Alerts
RATE_LIMIT_ALERT_THRESHOLD=100
RATE_LIMIT_SUSPICIOUS_THRESHOLD=50
RATE_LIMIT_BLOCK_RATE_THRESHOLD=0.1

# Internal Service Token (for bypassing rate limits)
INTERNAL_SERVICE_TOKEN=your-internal-service-token-change-in-production

# Commission Settings
PLATFORM_COMMISSION_RATE=0.005
MINIMUM_TRADE_AMOUNT=10
MAXIMUM_TRADE_AMOUNT=50000

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+254700000000

# Development Settings
DEBUG_MODE=true
LOG_LEVEL=debug
ENABLE_CORS=true
