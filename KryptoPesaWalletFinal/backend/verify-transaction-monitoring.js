/**
 * Verification script for Transaction Monitoring Persistence
 * Tests the blockchain transaction monitoring functionality
 */

const mongoose = require('mongoose');
const { transactionMonitoringService, TransactionState } = require('./src/services/transactionMonitoring');

async function verifyTransactionMonitoring() {
  try {
    console.log('🔍 Verifying Transaction Monitoring Persistence...\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa_test', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB');

    // Test 1: Verify service is loaded correctly
    console.log('\n📋 Test 1: Service Loading');
    console.log('✅ TransactionMonitoringService loaded successfully');
    console.log('✅ TransactionState model loaded successfully');

    // Test 2: Test blockchain transaction monitoring
    console.log('\n📋 Test 2: Blockchain Transaction Monitoring');
    
    const testUserId = new mongoose.Types.ObjectId();
    const txHash = '0x' + Math.random().toString(16).substr(2, 40);
    const network = 'polygon';
    
    const transactionData = {
      hash: txHash,
      type: 'send',
      symbol: 'USDT',
      amount: '100',
      from: '0x' + Math.random().toString(16).substr(2, 40),
      to: '0x' + Math.random().toString(16).substr(2, 40),
      network,
      status: 'pending',
      walletId: new mongoose.Types.ObjectId()
    };

    // Start blockchain transaction monitoring
    const transactionState = await transactionMonitoringService.startBlockchainTransactionMonitoring(
      txHash,
      network,
      testUserId,
      transactionData,
      { priority: 'high', sessionId: 'test_session', clientInfo: 'VerificationScript' }
    );

    console.log('✅ Blockchain transaction monitoring started');
    console.log(`   Transaction ID: ${transactionState.transactionId}`);
    console.log(`   Type: ${transactionState.type}`);
    console.log(`   Status: ${transactionState.status}`);
    console.log(`   Network: ${transactionState.blockchainData.network}`);
    console.log(`   TX Hash: ${transactionState.blockchainData.txHash}`);

    // Test 3: Verify persistence
    console.log('\n📋 Test 3: Transaction Persistence');
    
    const persistedState = await TransactionState.findOne({ 
      transactionId: transactionState.transactionId 
    });
    
    if (persistedState) {
      console.log('✅ Transaction state persisted to database');
      console.log(`   Persisted ID: ${persistedState.transactionId}`);
      console.log(`   Persisted Type: ${persistedState.type}`);
      console.log(`   Blockchain Data: ${JSON.stringify(persistedState.blockchainData, null, 2)}`);
    } else {
      console.log('❌ Transaction state not found in database');
      return false;
    }

    // Test 4: Update transaction status
    console.log('\n📋 Test 4: Transaction Status Update');
    
    await transactionMonitoringService.updateTransactionStatus(
      transactionState.transactionId,
      'completed',
      null,
      {
        blockchainData: {
          ...transactionState.blockchainData,
          blockNumber: 12345,
          confirmations: 15,
          gasUsed: '21000',
          status: 'confirmed'
        }
      }
    );

    const updatedState = await TransactionState.findOne({ 
      transactionId: transactionState.transactionId 
    });

    if (updatedState && updatedState.status === 'completed') {
      console.log('✅ Transaction status updated successfully');
      console.log(`   New Status: ${updatedState.status}`);
      console.log(`   Block Number: ${updatedState.blockchainData.blockNumber}`);
      console.log(`   Confirmations: ${updatedState.blockchainData.confirmations}`);
    } else {
      console.log('❌ Transaction status update failed');
      return false;
    }

    // Test 5: Recovery functionality
    console.log('\n📋 Test 5: Recovery Functionality');
    
    // Create a pending transaction to test recovery
    const recoveryTxHash = '0x' + Math.random().toString(16).substr(2, 40);
    await TransactionState.create({
      transactionId: `blockchain_${recoveryTxHash}`,
      type: 'blockchain_transaction',
      status: 'in_progress',
      userId: testUserId,
      operationData: { txHash: recoveryTxHash },
      blockchainData: {
        txHash: recoveryTxHash,
        network: 'ethereum',
        confirmations: 5
      },
      metadata: { priority: 'critical' }
    });

    // Test recovery
    await transactionMonitoringService.recoverPendingTransactions();
    
    const recoveredState = await TransactionState.findOne({ 
      transactionId: `blockchain_${recoveryTxHash}` 
    });

    if (recoveredState && recoveredState.status === 'pending') {
      console.log('✅ Transaction recovery working correctly');
      console.log(`   Recovered transaction reset to pending status`);
    } else {
      console.log('❌ Transaction recovery failed');
      return false;
    }

    // Test 6: Get pending transactions
    console.log('\n📋 Test 6: Pending Transactions Query');
    
    const pendingTransactions = await transactionMonitoringService.getPendingTransactions();
    console.log(`✅ Found ${pendingTransactions.length} pending transactions`);
    
    if (pendingTransactions.length > 0) {
      console.log(`   Sample pending transaction: ${pendingTransactions[0].transactionId}`);
    }

    // Cleanup test data
    await TransactionState.deleteMany({ userId: testUserId });
    console.log('\n🧹 Test data cleaned up');

    console.log('\n🎉 All Transaction Monitoring Persistence tests passed!');
    console.log('\n📊 Summary:');
    console.log('   ✅ Blockchain transaction monitoring - WORKING');
    console.log('   ✅ Transaction persistence - WORKING');
    console.log('   ✅ Status updates - WORKING');
    console.log('   ✅ Recovery functionality - WORKING');
    console.log('   ✅ Pending transaction queries - WORKING');

    return true;

  } catch (error) {
    console.error('\n❌ Verification failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  } finally {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run verification if this script is executed directly
if (require.main === module) {
  verifyTransactionMonitoring()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Verification script error:', error);
      process.exit(1);
    });
}

module.exports = { verifyTransactionMonitoring };
