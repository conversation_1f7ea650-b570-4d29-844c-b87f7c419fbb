# Nginx Configuration for KryptoPesa High-Performance Load Balancing
# Optimized for 10,000+ concurrent users

# Main context
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Worker connections optimization
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
    worker_rlimit_nofile 65535;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=trading:10m rate=200r/m;
    limit_req_zone $binary_remote_addr zone=wallet:10m rate=50r/m;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;
    
    # Upstream backend servers
    upstream kryptopesa_api {
        least_conn;
        
        # API server instances
        server 127.0.0.1:3001 max_fails=3 fail_timeout=30s weight=1;
        server 127.0.0.1:3002 max_fails=3 fail_timeout=30s weight=1;
        server 127.0.0.1:3003 max_fails=3 fail_timeout=30s weight=1;
        server 127.0.0.1:3004 max_fails=3 fail_timeout=30s weight=1;
        
        # Health check
        keepalive 32;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }
    
    # WebSocket upstream for real-time features
    upstream kryptopesa_websocket {
        ip_hash; # Sticky sessions for WebSocket
        
        server 127.0.0.1:3001;
        server 127.0.0.1:3002;
        server 127.0.0.1:3003;
        server 127.0.0.1:3004;
    }
    
    # Caching configuration
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:100m 
                     max_size=1g inactive=60m use_temp_path=off;
    
    # Log format for analytics
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Security headers map
    map $sent_http_content_type $expires {
        default                    off;
        text/html                  epoch;
        text/css                   max;
        application/javascript     max;
        ~image/                    1M;
    }
    
    # Main server configuration
    server {
        listen 80;
        listen [::]:80;
        server_name api.kryptopesa.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS server configuration
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name api.kryptopesa.com;
        
        # SSL configuration
        ssl_certificate /etc/ssl/certs/kryptopesa.crt;
        ssl_certificate_key /etc/ssl/private/kryptopesa.key;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;
        
        # Modern SSL configuration
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        
        # HSTS
        add_header Strict-Transport-Security "max-age=63072000" always;
        
        # Security headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        
        # Connection limits
        limit_conn perip 20;
        limit_conn perserver 1000;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # API endpoints with caching
        location /api/public/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://kryptopesa_api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Caching for public endpoints
            proxy_cache api_cache;
            proxy_cache_valid 200 5m;
            proxy_cache_valid 404 1m;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            proxy_cache_lock on;
            
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # Authentication endpoints (stricter rate limiting)
        location /api/auth/ {
            limit_req zone=auth burst=5 nodelay;
            
            proxy_pass http://kryptopesa_api;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # No caching for auth endpoints
            proxy_cache off;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
        
        # Trading endpoints
        location /api/trading/ {
            limit_req zone=trading burst=50 nodelay;
            
            proxy_pass http://kryptopesa_api;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Short caching for trading data
            proxy_cache api_cache;
            proxy_cache_valid 200 30s;
            proxy_cache_use_stale error timeout updating;
            
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # Wallet endpoints
        location /api/wallet/ {
            limit_req zone=wallet burst=20 nodelay;
            
            proxy_pass http://kryptopesa_api;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # No caching for wallet endpoints
            proxy_cache off;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
        
        # WebSocket endpoints for real-time features
        location /ws/ {
            proxy_pass http://kryptopesa_websocket;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket specific settings
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
            proxy_connect_timeout 10;
        }
        
        # Static file serving with caching
        location /static/ {
            root /var/www/kryptopesa;
            expires $expires;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options nosniff;
            
            # Gzip static files
            gzip_static on;
        }
        
        # Default API proxy
        location /api/ {
            limit_req zone=api burst=30 nodelay;
            
            proxy_pass http://kryptopesa_api;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Error handling
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        
        # Metrics endpoint for monitoring
        location /metrics {
            access_log off;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            
            proxy_pass http://kryptopesa_api;
            proxy_set_header Host $host;
        }
        
        # Block common attack patterns
        location ~* \.(php|asp|aspx|jsp)$ {
            return 444;
        }
        
        location ~* /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
    
    # Status server for monitoring
    server {
        listen 8080;
        server_name localhost;
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            deny all;
        }
        
        location /api_status {
            proxy_pass http://kryptopesa_api/health;
            access_log off;
        }
    }
}
