#!/bin/bash

# KryptoPesa System Testing Script
# This script runs comprehensive system tests including unit tests, integration tests, and system checks

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_DIR="backend"
MOBILE_DIR="mobile"
ADMIN_DIR="admin-dashboard"
CONTRACTS_DIR="smart-contracts"
API_URL="http://localhost:3000"
ADMIN_URL="http://localhost:3001"

# Test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✓${NC} $1"
    ((PASSED_TESTS++))
}

error() {
    echo -e "${RED}✗${NC} $1"
    ((FAILED_TESTS++))
}

warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Function to check if service is running
check_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1

    log "Checking if $name is running at $url..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url/health" > /dev/null 2>&1; then
            success "$name is running"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "$name is not responding after $max_attempts attempts"
            return 1
        fi
        
        sleep 2
        ((attempt++))
    done
}

# Function to run backend tests
run_backend_tests() {
    log "Running backend tests..."
    ((TOTAL_TESTS++))
    
    cd $BACKEND_DIR
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing backend dependencies..."
        npm install
    fi
    
    # Run unit tests
    log "Running backend unit tests..."
    if npm test; then
        success "Backend unit tests passed"
    else
        error "Backend unit tests failed"
        cd ..
        return 1
    fi
    
    # Run linting
    log "Running backend linting..."
    if npm run lint; then
        success "Backend linting passed"
    else
        warning "Backend linting issues found"
    fi
    
    cd ..
}

# Function to run mobile tests
run_mobile_tests() {
    log "Running mobile app tests..."
    ((TOTAL_TESTS++))
    
    cd $MOBILE_DIR
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing mobile dependencies..."
        npm install
    fi
    
    # Run unit tests
    log "Running mobile unit tests..."
    if npm test -- --watchAll=false; then
        success "Mobile unit tests passed"
    else
        error "Mobile unit tests failed"
        cd ..
        return 1
    fi
    
    # Run linting
    log "Running mobile linting..."
    if npm run lint; then
        success "Mobile linting passed"
    else
        warning "Mobile linting issues found"
    fi
    
    cd ..
}

# Function to run admin dashboard tests
run_admin_tests() {
    log "Running admin dashboard tests..."
    ((TOTAL_TESTS++))
    
    cd $ADMIN_DIR
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing admin dependencies..."
        npm install
    fi
    
    # Run tests
    log "Running admin dashboard tests..."
    if npm test -- --watchAll=false; then
        success "Admin dashboard tests passed"
    else
        error "Admin dashboard tests failed"
        cd ..
        return 1
    fi
    
    cd ..
}

# Function to run smart contract tests
run_contract_tests() {
    log "Running smart contract tests..."
    ((TOTAL_TESTS++))
    
    cd $CONTRACTS_DIR
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing contract dependencies..."
        npm install
    fi
    
    # Run contract tests
    log "Running smart contract tests..."
    if npm test; then
        success "Smart contract tests passed"
    else
        error "Smart contract tests failed"
        cd ..
        return 1
    fi
    
    cd ..
}

# Function to run API integration tests
run_api_tests() {
    log "Running API integration tests..."
    ((TOTAL_TESTS++))
    
    # Test health endpoints
    log "Testing health endpoints..."
    if curl -s "$API_URL/health" | grep -q "OK"; then
        success "Health endpoint responding"
    else
        error "Health endpoint not responding"
        return 1
    fi
    
    # Test metrics endpoint
    log "Testing metrics endpoint..."
    if curl -s "$API_URL/metrics" > /dev/null; then
        success "Metrics endpoint responding"
    else
        warning "Metrics endpoint not responding"
    fi
    
    # Test API routes (basic connectivity)
    local routes=("/api/auth/health" "/api/users/health" "/api/wallet/health")
    
    for route in "${routes[@]}"; do
        log "Testing route: $route"
        if curl -s "$API_URL$route" > /dev/null 2>&1; then
            success "Route $route is accessible"
        else
            warning "Route $route may not be accessible"
        fi
    done
}

# Function to run performance tests
run_performance_tests() {
    log "Running performance tests..."
    ((TOTAL_TESTS++))
    
    # Test API response times
    log "Testing API response times..."
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "$API_URL/health")
    local response_ms=$(echo "$response_time * 1000" | bc -l | cut -d. -f1)
    
    if [ "$response_ms" -lt 1000 ]; then
        success "API response time: ${response_ms}ms (Good)"
    elif [ "$response_ms" -lt 2000 ]; then
        warning "API response time: ${response_ms}ms (Acceptable)"
    else
        error "API response time: ${response_ms}ms (Too slow)"
    fi
    
    # Test concurrent requests
    log "Testing concurrent request handling..."
    local concurrent_test_result=0
    
    for i in {1..10}; do
        curl -s "$API_URL/health" > /dev/null &
    done
    wait
    
    if [ $? -eq 0 ]; then
        success "Concurrent request handling test passed"
    else
        error "Concurrent request handling test failed"
    fi
}

# Function to run security tests
run_security_tests() {
    log "Running security tests..."
    ((TOTAL_TESTS++))
    
    # Test rate limiting
    log "Testing rate limiting..."
    local rate_limit_test=0
    
    for i in {1..20}; do
        if ! curl -s "$API_URL/health" > /dev/null; then
            rate_limit_test=1
            break
        fi
    done
    
    if [ $rate_limit_test -eq 0 ]; then
        success "Rate limiting test completed"
    else
        warning "Rate limiting may be too aggressive"
    fi
    
    # Test CORS headers
    log "Testing CORS headers..."
    local cors_headers=$(curl -s -I "$API_URL/health" | grep -i "access-control")
    
    if [ -n "$cors_headers" ]; then
        success "CORS headers present"
    else
        warning "CORS headers not found"
    fi
    
    # Test security headers
    log "Testing security headers..."
    local security_headers=$(curl -s -I "$API_URL/health" | grep -i -E "(x-frame-options|x-content-type-options|strict-transport-security)")
    
    if [ -n "$security_headers" ]; then
        success "Security headers present"
    else
        warning "Some security headers may be missing"
    fi
}

# Function to check system resources
check_system_resources() {
    log "Checking system resources..."
    ((TOTAL_TESTS++))
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -lt 80 ]; then
        success "Disk usage: ${disk_usage}% (Good)"
    elif [ "$disk_usage" -lt 90 ]; then
        warning "Disk usage: ${disk_usage}% (Monitor)"
    else
        error "Disk usage: ${disk_usage}% (Critical)"
    fi
    
    # Check memory usage
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$memory_usage" -lt 80 ]; then
        success "Memory usage: ${memory_usage}% (Good)"
    elif [ "$memory_usage" -lt 90 ]; then
        warning "Memory usage: ${memory_usage}% (Monitor)"
    else
        error "Memory usage: ${memory_usage}% (Critical)"
    fi
}

# Function to generate test report
generate_report() {
    log "Generating test report..."
    
    local report_file="test-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "KryptoPesa System Test Report"
        echo "============================="
        echo "Date: $(date)"
        echo "Total Tests: $TOTAL_TESTS"
        echo "Passed: $PASSED_TESTS"
        echo "Failed: $FAILED_TESTS"
        echo ""
        
        if [ $FAILED_TESTS -eq 0 ]; then
            echo "✓ All tests passed!"
            echo "System is ready for production deployment."
        else
            echo "✗ Some tests failed."
            echo "Please review the failed tests before deployment."
        fi
        
        echo ""
        echo "System Health Score: $(( (PASSED_TESTS * 100) / TOTAL_TESTS ))%"
        
    } > "$report_file"
    
    log "Test report saved to: $report_file"
}

# Main execution
main() {
    log "Starting KryptoPesa System Testing..."
    log "======================================"
    
    # Check if we're in the right directory
    if [ ! -d "$BACKEND_DIR" ] || [ ! -d "$MOBILE_DIR" ]; then
        error "Please run this script from the KryptoPesa root directory"
        exit 1
    fi
    
    # Run all tests
    run_backend_tests
    run_mobile_tests
    run_admin_tests
    run_contract_tests
    
    # Check if services are running for integration tests
    if check_service "$API_URL" "Backend API"; then
        run_api_tests
        run_performance_tests
        run_security_tests
    else
        warning "Skipping integration tests - backend not running"
        warning "Start the backend with: cd backend && npm run dev"
    fi
    
    # System checks
    check_system_resources
    
    # Generate report
    generate_report
    
    # Final summary
    log "======================================"
    log "Test Summary:"
    log "Total Tests: $TOTAL_TESTS"
    log "Passed: $PASSED_TESTS"
    log "Failed: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        success "All tests passed! System is ready for production."
        exit 0
    else
        error "Some tests failed. Please review before deployment."
        exit 1
    fi
}

# Run main function
main "$@"
