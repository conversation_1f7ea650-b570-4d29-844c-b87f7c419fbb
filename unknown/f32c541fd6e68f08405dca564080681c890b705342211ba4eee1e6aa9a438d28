<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1976d2" />
    <title>KryptoPesa Admin - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .offline-container {
            background: white;
            border-radius: 16px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            margin: 1rem;
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: #f5f5f5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1976d2;
        }

        .offline-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: #666;
        }

        .offline-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #1976d2;
            color: white;
        }

        .btn-primary:hover {
            background: #1565c0;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f5f5f5;
            color: #666;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
            transform: translateY(-1px);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
            padding: 0.5rem 1rem;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            font-size: 0.9rem;
            color: #856404;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ffc107;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .features-list {
            text-align: left;
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .features-list h3 {
            margin-bottom: 1rem;
            color: #1976d2;
            font-size: 1.1rem;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .features-list li::before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
        }

        @media (max-width: 480px) {
            .offline-container {
                padding: 2rem 1.5rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📡
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry - you can still access some features of the KryptoPesa Admin Dashboard.
        </p>

        <div class="features-list">
            <h3>Available Offline:</h3>
            <ul>
                <li>View cached dashboard data</li>
                <li>Browse recent trade history</li>
                <li>Access user management tools</li>
                <li>Review system statistics</li>
            </ul>
        </div>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="tryReconnect()">
                🔄 Try Again
            </button>
            <a href="/admin/dashboard" class="btn btn-secondary">
                📊 Go to Dashboard
            </a>
        </div>

        <div class="status-indicator">
            <div class="status-dot"></div>
            <span id="connection-status">Checking connection...</span>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connection-status');
            
            if (navigator.onLine) {
                statusElement.textContent = 'Connection restored! Refreshing...';
                statusElement.parentElement.style.background = '#d4edda';
                statusElement.parentElement.style.borderColor = '#c3e6cb';
                statusElement.parentElement.style.color = '#155724';
                
                // Auto-refresh after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                statusElement.textContent = 'No internet connection detected';
            }
        }

        // Try to reconnect
        function tryReconnect() {
            const button = event.target;
            button.textContent = '🔄 Checking...';
            button.disabled = true;
            
            // Attempt to fetch a small resource
            fetch('/favicon.ico', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                button.textContent = '✅ Connected!';
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch(() => {
                button.textContent = '❌ Still Offline';
                setTimeout(() => {
                    button.textContent = '🔄 Try Again';
                    button.disabled = false;
                }, 2000);
            });
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(() => {
            if (!navigator.onLine) {
                // Try a lightweight request to check actual connectivity
                fetch('/favicon.ico', { 
                    method: 'HEAD',
                    cache: 'no-cache',
                    signal: AbortSignal.timeout(5000)
                })
                .then(() => {
                    if (!navigator.onLine) {
                        // Browser thinks we're offline but we can reach the server
                        window.location.reload();
                    }
                })
                .catch(() => {
                    // Still offline
                });
            }
        }, 30000); // Check every 30 seconds

        // Service Worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready');
                
                // Listen for service worker updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New content is available
                            if (confirm('New version available. Refresh to update?')) {
                                window.location.reload();
                            }
                        }
                    });
                });
            });
        }
    </script>
</body>
</html>
