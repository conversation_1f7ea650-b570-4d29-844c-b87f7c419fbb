/**
 * Comprehensive Backup and Disaster Recovery System for KryptoPesa
 * Enterprise-grade data protection and business continuity
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');
const AWS = require('aws-sdk');
const cron = require('node-cron');
const winston = require('winston');

class BackupSystem {
  constructor(config = {}) {
    this.config = {
      environment: process.env.NODE_ENV || 'development',
      backupRetention: {
        daily: 30,    // Keep daily backups for 30 days
        weekly: 12,   // Keep weekly backups for 12 weeks
        monthly: 12,  // Keep monthly backups for 12 months
        yearly: 7     // Keep yearly backups for 7 years
      },
      encryption: {
        algorithm: 'aes-256-gcm',
        keyDerivation: 'pbkdf2',
        iterations: 100000
      },
      storage: {
        local: {
          enabled: true,
          path: process.env.BACKUP_LOCAL_PATH || '/var/backups/kryptopesa'
        },
        s3: {
          enabled: process.env.AWS_S3_BACKUP_ENABLED === 'true',
          bucket: process.env.AWS_S3_BACKUP_BUCKET,
          region: process.env.AWS_REGION || 'us-east-1',
          storageClass: 'STANDARD_IA'
        },
        gcs: {
          enabled: process.env.GCS_BACKUP_ENABLED === 'true',
          bucket: process.env.GCS_BACKUP_BUCKET,
          projectId: process.env.GCS_PROJECT_ID
        }
      },
      databases: {
        postgresql: {
          enabled: true,
          host: process.env.DB_HOST || 'localhost',
          port: process.env.DB_PORT || 5432,
          database: process.env.DB_NAME || 'kryptopesa',
          username: process.env.DB_USER || 'postgres',
          password: process.env.DB_PASSWORD
        },
        mongodb: {
          enabled: true,
          uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa'
        },
        redis: {
          enabled: true,
          host: process.env.REDIS_HOST || 'localhost',
          port: process.env.REDIS_PORT || 6379,
          password: process.env.REDIS_PASSWORD
        }
      },
      ...config
    };

    this.initializeLogger();
    this.initializeStorageClients();
    this.scheduleBackups();
  }

  initializeLogger() {
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      defaultMeta: { service: 'backup-system' },
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({
          filename: path.join(__dirname, '../logs/backup.log'),
          maxsize: 50 * 1024 * 1024,
          maxFiles: 5
        })
      ]
    });
  }

  initializeStorageClients() {
    // AWS S3 client
    if (this.config.storage.s3.enabled) {
      this.s3Client = new AWS.S3({
        region: this.config.storage.s3.region,
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      });
    }

    // Google Cloud Storage client would be initialized here
    if (this.config.storage.gcs.enabled) {
      // const { Storage } = require('@google-cloud/storage');
      // this.gcsClient = new Storage({ projectId: this.config.storage.gcs.projectId });
    }
  }

  scheduleBackups() {
    // Daily backup at 2 AM
    cron.schedule('0 2 * * *', () => {
      this.performFullBackup('daily');
    });

    // Weekly backup on Sunday at 3 AM
    cron.schedule('0 3 * * 0', () => {
      this.performFullBackup('weekly');
    });

    // Monthly backup on 1st day at 4 AM
    cron.schedule('0 4 1 * *', () => {
      this.performFullBackup('monthly');
    });

    // Yearly backup on January 1st at 5 AM
    cron.schedule('0 5 1 1 *', () => {
      this.performFullBackup('yearly');
    });

    // Cleanup old backups daily at 6 AM
    cron.schedule('0 6 * * *', () => {
      this.cleanupOldBackups();
    });

    this.logger.info('Backup schedules initialized');
  }

  async performFullBackup(type = 'manual') {
    const backupId = this.generateBackupId(type);
    const startTime = Date.now();

    this.logger.info('Starting full backup', { backupId, type });

    try {
      const backupManifest = {
        id: backupId,
        type,
        timestamp: new Date().toISOString(),
        environment: this.config.environment,
        components: {},
        metadata: {
          version: '1.0.0',
          encryption: this.config.encryption.algorithm
        }
      };

      // Create backup directory
      const backupDir = await this.createBackupDirectory(backupId);

      // Backup databases
      if (this.config.databases.postgresql.enabled) {
        backupManifest.components.postgresql = await this.backupPostgreSQL(backupDir);
      }

      if (this.config.databases.mongodb.enabled) {
        backupManifest.components.mongodb = await this.backupMongoDB(backupDir);
      }

      if (this.config.databases.redis.enabled) {
        backupManifest.components.redis = await this.backupRedis(backupDir);
      }

      // Backup application files
      backupManifest.components.application = await this.backupApplicationFiles(backupDir);

      // Backup configuration files
      backupManifest.components.configuration = await this.backupConfigurationFiles(backupDir);

      // Create backup manifest
      await this.createBackupManifest(backupDir, backupManifest);

      // Encrypt backup
      const encryptedBackup = await this.encryptBackup(backupDir);

      // Upload to remote storage
      await this.uploadBackup(encryptedBackup, backupManifest);

      // Verify backup integrity
      await this.verifyBackupIntegrity(encryptedBackup, backupManifest);

      const duration = Date.now() - startTime;
      this.logger.info('Full backup completed successfully', {
        backupId,
        type,
        duration: `${duration}ms`,
        size: await this.getBackupSize(encryptedBackup)
      });

      return {
        success: true,
        backupId,
        manifest: backupManifest,
        duration
      };

    } catch (error) {
      this.logger.error('Full backup failed', {
        backupId,
        type,
        error: error.message,
        stack: error.stack
      });

      throw error;
    }
  }

  generateBackupId(type) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const randomSuffix = crypto.randomBytes(4).toString('hex');
    return `${type}-${timestamp}-${randomSuffix}`;
  }

  async createBackupDirectory(backupId) {
    const backupDir = path.join(this.config.storage.local.path, backupId);
    await fs.mkdir(backupDir, { recursive: true });
    return backupDir;
  }

  async backupPostgreSQL(backupDir) {
    this.logger.info('Starting PostgreSQL backup');

    const dumpFile = path.join(backupDir, 'postgresql.sql');
    const config = this.config.databases.postgresql;

    try {
      const pgDumpCommand = [
        'pg_dump',
        `--host=${config.host}`,
        `--port=${config.port}`,
        `--username=${config.username}`,
        `--dbname=${config.database}`,
        '--verbose',
        '--clean',
        '--if-exists',
        '--create',
        '--format=custom',
        `--file=${dumpFile}`
      ].join(' ');

      // Set password via environment variable
      const env = { ...process.env, PGPASSWORD: config.password };

      execSync(pgDumpCommand, { env, stdio: 'pipe' });

      const stats = await fs.stat(dumpFile);

      this.logger.info('PostgreSQL backup completed', {
        file: dumpFile,
        size: stats.size
      });

      return {
        type: 'postgresql',
        file: 'postgresql.sql',
        size: stats.size,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('PostgreSQL backup failed', { error: error.message });
      throw error;
    }
  }

  async backupMongoDB(backupDir) {
    this.logger.info('Starting MongoDB backup');

    const dumpDir = path.join(backupDir, 'mongodb');
    await fs.mkdir(dumpDir, { recursive: true });

    try {
      const mongoDumpCommand = [
        'mongodump',
        `--uri="${this.config.databases.mongodb.uri}"`,
        `--out=${dumpDir}`,
        '--gzip'
      ].join(' ');

      execSync(mongoDumpCommand, { stdio: 'pipe' });

      const size = await this.getDirectorySize(dumpDir);

      this.logger.info('MongoDB backup completed', {
        directory: dumpDir,
        size
      });

      return {
        type: 'mongodb',
        directory: 'mongodb',
        size,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('MongoDB backup failed', { error: error.message });
      throw error;
    }
  }

  async backupRedis(backupDir) {
    this.logger.info('Starting Redis backup');

    const dumpFile = path.join(backupDir, 'redis.rdb');
    const config = this.config.databases.redis;

    try {
      // Use redis-cli to create backup
      const redisCommand = [
        'redis-cli',
        `--rdb=${dumpFile}`,
        `-h ${config.host}`,
        `-p ${config.port}`
      ];

      if (config.password) {
        redisCommand.push(`-a ${config.password}`);
      }

      execSync(redisCommand.join(' '), { stdio: 'pipe' });

      const stats = await fs.stat(dumpFile);

      this.logger.info('Redis backup completed', {
        file: dumpFile,
        size: stats.size
      });

      return {
        type: 'redis',
        file: 'redis.rdb',
        size: stats.size,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('Redis backup failed', { error: error.message });
      throw error;
    }
  }

  async backupApplicationFiles(backupDir) {
    this.logger.info('Starting application files backup');

    const appBackupDir = path.join(backupDir, 'application');
    await fs.mkdir(appBackupDir, { recursive: true });

    try {
      // Backup critical application directories
      const criticalPaths = [
        'uploads',
        'certificates',
        'keys',
        'logs'
      ];

      const backupInfo = [];

      for (const criticalPath of criticalPaths) {
        const sourcePath = path.join(__dirname, '..', criticalPath);
        const targetPath = path.join(appBackupDir, criticalPath);

        try {
          await fs.access(sourcePath);
          await this.copyDirectory(sourcePath, targetPath);

          const size = await this.getDirectorySize(targetPath);
          backupInfo.push({
            path: criticalPath,
            size,
            status: 'success'
          });

        } catch (error) {
          backupInfo.push({
            path: criticalPath,
            status: 'skipped',
            reason: 'not found'
          });
        }
      }

      this.logger.info('Application files backup completed', { backupInfo });

      return {
        type: 'application',
        directory: 'application',
        files: backupInfo,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('Application files backup failed', { error: error.message });
      throw error;
    }
  }

  async backupConfigurationFiles(backupDir) {
    this.logger.info('Starting configuration files backup');

    const configBackupDir = path.join(backupDir, 'configuration');
    await fs.mkdir(configBackupDir, { recursive: true });

    try {
      // Backup configuration files (excluding sensitive data)
      const configFiles = [
        'package.json',
        'package-lock.json',
        'docker-compose.yml',
        'nginx.conf'
      ];

      const backupInfo = [];

      for (const configFile of configFiles) {
        const sourcePath = path.join(__dirname, '..', configFile);
        const targetPath = path.join(configBackupDir, configFile);

        try {
          await fs.access(sourcePath);
          await fs.copyFile(sourcePath, targetPath);

          const stats = await fs.stat(targetPath);
          backupInfo.push({
            file: configFile,
            size: stats.size,
            status: 'success'
          });

        } catch (error) {
          backupInfo.push({
            file: configFile,
            status: 'skipped',
            reason: 'not found'
          });
        }
      }

      this.logger.info('Configuration files backup completed', { backupInfo });

      return {
        type: 'configuration',
        directory: 'configuration',
        files: backupInfo,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('Configuration files backup failed', { error: error.message });
      throw error;
    }
  }

  async createBackupManifest(backupDir, manifest) {
    const manifestPath = path.join(backupDir, 'manifest.json');
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));

    this.logger.info('Backup manifest created', { manifestPath });
  }

  async encryptBackup(backupDir) {
    this.logger.info('Encrypting backup', { backupDir });

    const encryptedFile = `${backupDir}.encrypted`;
    const password = process.env.BACKUP_ENCRYPTION_PASSWORD || 'default-password';

    try {
      // Create tar archive
      const tarFile = `${backupDir}.tar.gz`;
      execSync(`tar -czf ${tarFile} -C ${path.dirname(backupDir)} ${path.basename(backupDir)}`);

      // Encrypt the archive
      const key = crypto.pbkdf2Sync(password, 'salt', this.config.encryption.iterations, 32, 'sha256');
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(this.config.encryption.algorithm, key);

      const input = await fs.readFile(tarFile);
      const encrypted = Buffer.concat([cipher.update(input), cipher.final()]);

      // Save encrypted file with IV
      const encryptedData = Buffer.concat([iv, encrypted]);
      await fs.writeFile(encryptedFile, encryptedData);

      // Cleanup temporary files
      await fs.unlink(tarFile);
      await fs.rmdir(backupDir, { recursive: true });

      this.logger.info('Backup encryption completed', { encryptedFile });

      return encryptedFile;

    } catch (error) {
      this.logger.error('Backup encryption failed', { error: error.message });
      throw error;
    }
  }

  async uploadBackup(encryptedBackup, manifest) {
    const uploadPromises = [];

    // Upload to S3
    if (this.config.storage.s3.enabled) {
      uploadPromises.push(this.uploadToS3(encryptedBackup, manifest));
    }

    // Upload to Google Cloud Storage
    if (this.config.storage.gcs.enabled) {
      uploadPromises.push(this.uploadToGCS(encryptedBackup, manifest));
    }

    if (uploadPromises.length > 0) {
      await Promise.all(uploadPromises);
      this.logger.info('Backup uploaded to remote storage');
    }
  }

  async uploadToS3(encryptedBackup, manifest) {
    const key = `backups/${manifest.type}/${path.basename(encryptedBackup)}`;
    const fileContent = await fs.readFile(encryptedBackup);

    const params = {
      Bucket: this.config.storage.s3.bucket,
      Key: key,
      Body: fileContent,
      StorageClass: this.config.storage.s3.storageClass,
      Metadata: {
        'backup-id': manifest.id,
        'backup-type': manifest.type,
        'timestamp': manifest.timestamp
      }
    };

    await this.s3Client.upload(params).promise();
    this.logger.info('Backup uploaded to S3', { bucket: this.config.storage.s3.bucket, key });
  }

  async uploadToGCS(encryptedBackup, manifest) {
    // Google Cloud Storage upload implementation
    this.logger.info('GCS upload not implemented yet');
  }

  async verifyBackupIntegrity(encryptedBackup, manifest) {
    try {
      // Calculate checksum
      const fileContent = await fs.readFile(encryptedBackup);
      const checksum = crypto.createHash('sha256').update(fileContent).digest('hex');

      // Store checksum in manifest
      manifest.checksum = checksum;
      manifest.verified = true;

      this.logger.info('Backup integrity verified', {
        backupId: manifest.id,
        checksum
      });

    } catch (error) {
      this.logger.error('Backup integrity verification failed', {
        backupId: manifest.id,
        error: error.message
      });
      throw error;
    }
  }

  async getBackupSize(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  async getDirectorySize(dirPath) {
    let totalSize = 0;

    try {
      const files = await fs.readdir(dirPath, { withFileTypes: true });

      for (const file of files) {
        const filePath = path.join(dirPath, file.name);

        if (file.isDirectory()) {
          totalSize += await this.getDirectorySize(filePath);
        } else {
          const stats = await fs.stat(filePath);
          totalSize += stats.size;
        }
      }
    } catch (error) {
      this.logger.error('Error calculating directory size', { dirPath, error: error.message });
    }

    return totalSize;
  }

  async copyDirectory(source, target) {
    await fs.mkdir(target, { recursive: true });

    const files = await fs.readdir(source, { withFileTypes: true });

    for (const file of files) {
      const sourcePath = path.join(source, file.name);
      const targetPath = path.join(target, file.name);

      if (file.isDirectory()) {
        await this.copyDirectory(sourcePath, targetPath);
      } else {
        await fs.copyFile(sourcePath, targetPath);
      }
    }
  }

  async cleanupOldBackups() {
    this.logger.info('Starting backup cleanup');

    try {
      // Cleanup local backups
      await this.cleanupLocalBackups();

      // Cleanup remote backups
      if (this.config.storage.s3.enabled) {
        await this.cleanupS3Backups();
      }

      this.logger.info('Backup cleanup completed');

    } catch (error) {
      this.logger.error('Backup cleanup failed', { error: error.message });
    }
  }

  async cleanupLocalBackups() {
    const backupPath = this.config.storage.local.path;

    try {
      const files = await fs.readdir(backupPath);
      const now = Date.now();

      for (const file of files) {
        const filePath = path.join(backupPath, file);
        const stats = await fs.stat(filePath);
        const ageInDays = (now - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);

        let shouldDelete = false;

        if (file.includes('daily-') && ageInDays > this.config.backupRetention.daily) {
          shouldDelete = true;
        } else if (file.includes('weekly-') && ageInDays > this.config.backupRetention.weekly * 7) {
          shouldDelete = true;
        } else if (file.includes('monthly-') && ageInDays > this.config.backupRetention.monthly * 30) {
          shouldDelete = true;
        } else if (file.includes('yearly-') && ageInDays > this.config.backupRetention.yearly * 365) {
          shouldDelete = true;
        }

        if (shouldDelete) {
          await fs.unlink(filePath);
          this.logger.info('Old backup deleted', { file, ageInDays });
        }
      }

    } catch (error) {
      this.logger.error('Local backup cleanup failed', { error: error.message });
    }
  }

  async cleanupS3Backups() {
    // S3 backup cleanup implementation
    this.logger.info('S3 backup cleanup not implemented yet');
  }
}

module.exports = BackupSystem;
