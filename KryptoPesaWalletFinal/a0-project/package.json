{"name": "a0-project", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "convex": "^1.14.2", "expo": "^52.0.46", "react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "^2.27.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-web": "~0.19.6", "sonner-native": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}}