/**
 * Performance Monitoring Service Tests
 * Comprehensive tests for performance monitoring functionality
 */

const { performanceMonitoringService } = require('../../src/services/performanceMonitoringService');
const { monitoringIntegrationService } = require('../../src/services/monitoringIntegrationService');

// Mock the monitoring integration service
jest.mock('../../src/services/monitoringIntegrationService', () => ({
  monitoringIntegrationService: {
    triggerAlert: jest.fn()
  }
}));

describe('Performance Monitoring Service', () => {
  beforeEach(() => {
    // Reset metrics before each test
    performanceMonitoringService.resetMetrics();
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // Shutdown service after tests
    await performanceMonitoringService.shutdown();
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      const result = await performanceMonitoringService.initialize();
      expect(result).toBe(true);
      expect(performanceMonitoringService.isInitialized).toBe(true);
    });

    test('should not initialize twice', async () => {
      await performanceMonitoringService.initialize();
      const result = await performanceMonitoringService.initialize();
      expect(result).toBe(true);
    });
  });

  describe('Request Recording', () => {
    test('should record successful request', () => {
      performanceMonitoringService.recordRequest('GET', '/api/users', 200, 150);
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.requests.total).toBe(1);
      expect(metrics.requests.successful).toBe(1);
      expect(metrics.requests.failed).toBe(0);
      expect(metrics.responseTime.current).toBe(150);
    });

    test('should record failed request', () => {
      performanceMonitoringService.recordRequest('POST', '/api/trades', 500, 300);
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.requests.total).toBe(1);
      expect(metrics.requests.successful).toBe(0);
      expect(metrics.requests.failed).toBe(1);
    });

    test('should track requests by method', () => {
      performanceMonitoringService.recordRequest('GET', '/api/users', 200, 100);
      performanceMonitoringService.recordRequest('POST', '/api/trades', 201, 200);
      performanceMonitoringService.recordRequest('GET', '/api/offers', 200, 150);
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.requests.byMethod.GET).toBe(2);
      expect(metrics.requests.byMethod.POST).toBe(1);
    });

    test('should track requests by route', () => {
      performanceMonitoringService.recordRequest('GET', '/api/users', 200, 100);
      performanceMonitoringService.recordRequest('GET', '/api/users', 200, 120);
      performanceMonitoringService.recordRequest('POST', '/api/trades', 201, 200);
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.requests.byRoute['/api/users']).toBe(2);
      expect(metrics.requests.byRoute['/api/trades']).toBe(1);
    });
  });

  describe('Response Time Monitoring', () => {
    test('should calculate response time percentiles', () => {
      // Record multiple response times
      const responseTimes = [100, 150, 200, 250, 300, 400, 500, 600, 800, 1000];
      responseTimes.forEach((time, index) => {
        performanceMonitoringService.recordRequest('GET', `/api/test${index}`, 200, time);
      });
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.responseTime.min).toBe(100);
      expect(metrics.responseTime.max).toBe(1000);
      expect(metrics.responseTime.average).toBeGreaterThan(0);
      expect(metrics.responseTime.p95).toBeGreaterThan(metrics.responseTime.p50);
    });

    test('should log slow requests', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      // Record a very slow request
      performanceMonitoringService.recordRequest('GET', '/api/slow', 200, 15000);
      
      // Should trigger slow request logging
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('Error Recording', () => {
    test('should record error metrics', () => {
      const error = new Error('Test error');
      performanceMonitoringService.recordError(error, 'POST', '/api/test');
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.errors.total).toBe(1);
      expect(metrics.errors.byType.Error).toBe(1);
      expect(metrics.errors.recentCount).toBe(1);
    });

    test('should identify critical errors', () => {
      const criticalError = new Error('Critical security breach');
      performanceMonitoringService.recordError(criticalError, 'POST', '/api/test');
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.errors.criticalErrors).toBe(1);
    });

    test('should track errors by type', () => {
      const error1 = new TypeError('Type error');
      const error2 = new ReferenceError('Reference error');
      const error3 = new TypeError('Another type error');
      
      performanceMonitoringService.recordError(error1, 'GET', '/api/test1');
      performanceMonitoringService.recordError(error2, 'GET', '/api/test2');
      performanceMonitoringService.recordError(error3, 'GET', '/api/test3');
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.errors.byType.TypeError).toBe(2);
      expect(metrics.errors.byType.ReferenceError).toBe(1);
    });
  });

  describe('Cache Monitoring', () => {
    test('should record cache hits and misses', () => {
      performanceMonitoringService.recordCacheHit();
      performanceMonitoringService.recordCacheHit();
      performanceMonitoringService.recordCacheMiss();
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.cache.hits).toBe(2);
      expect(metrics.cache.misses).toBe(1);
      expect(metrics.cache.hitRate).toBeCloseTo(0.67, 2);
    });
  });

  describe('System Metrics', () => {
    test('should update active connections', () => {
      performanceMonitoringService.updateActiveConnections(25);
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.system.activeConnections).toBe(25);
    });

    test('should include memory metrics', () => {
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.system.memory.heapUsed).toBeDefined();
      expect(metrics.system.memory.heapTotal).toBeDefined();
      expect(metrics.system.memory.heapUsagePercent).toBeDefined();
    });
  });

  describe('Alert Triggering', () => {
    beforeEach(async () => {
      await performanceMonitoringService.initialize();
    });

    test('should trigger alert for high response time', async () => {
      // Record requests with high response times
      for (let i = 0; i < 10; i++) {
        performanceMonitoringService.recordRequest('GET', '/api/test', 200, 2500);
      }
      
      // Manually trigger response time monitoring
      await performanceMonitoringService.monitorResponseTime();
      
      expect(monitoringIntegrationService.triggerAlert).toHaveBeenCalledWith(
        expect.stringContaining('RESPONSE_TIME'),
        expect.objectContaining({
          severity: expect.any(String),
          message: expect.stringContaining('response time')
        })
      );
    });

    test('should trigger alert for high error rate', async () => {
      // Record requests with high error rate
      for (let i = 0; i < 20; i++) {
        const statusCode = i < 15 ? 500 : 200; // 75% error rate
        performanceMonitoringService.recordRequest('GET', '/api/test', statusCode, 100);
      }
      
      // Manually trigger error rate monitoring
      await performanceMonitoringService.monitorErrorRate();
      
      expect(monitoringIntegrationService.triggerAlert).toHaveBeenCalledWith(
        expect.stringContaining('ERROR_RATE'),
        expect.objectContaining({
          severity: expect.any(String),
          message: expect.stringContaining('error rate')
        })
      );
    });
  });

  describe('Metrics Retrieval', () => {
    test('should return comprehensive metrics', () => {
      // Record some sample data
      performanceMonitoringService.recordRequest('GET', '/api/users', 200, 150);
      performanceMonitoringService.recordRequest('POST', '/api/trades', 201, 300);
      performanceMonitoringService.recordCacheHit();
      performanceMonitoringService.updateActiveConnections(10);
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      
      expect(metrics).toHaveProperty('requests');
      expect(metrics).toHaveProperty('responseTime');
      expect(metrics).toHaveProperty('errors');
      expect(metrics).toHaveProperty('system');
      expect(metrics).toHaveProperty('cache');
      expect(metrics).toHaveProperty('timestamp');
      expect(metrics.isInitialized).toBeDefined();
    });

    test('should calculate success rate correctly', () => {
      performanceMonitoringService.recordRequest('GET', '/api/test', 200, 100);
      performanceMonitoringService.recordRequest('GET', '/api/test', 200, 100);
      performanceMonitoringService.recordRequest('GET', '/api/test', 500, 100);
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.requests.successRate).toBeCloseTo(0.67, 2);
    });
  });

  describe('Service Lifecycle', () => {
    test('should shutdown gracefully', async () => {
      await performanceMonitoringService.initialize();
      await performanceMonitoringService.shutdown();
      
      expect(performanceMonitoringService.isInitialized).toBe(false);
    });

    test('should reset metrics', () => {
      performanceMonitoringService.recordRequest('GET', '/api/test', 200, 100);
      performanceMonitoringService.resetMetrics();
      
      const metrics = performanceMonitoringService.getPerformanceMetrics();
      expect(metrics.requests.total).toBe(0);
      expect(metrics.errors.total).toBe(0);
    });
  });
});
