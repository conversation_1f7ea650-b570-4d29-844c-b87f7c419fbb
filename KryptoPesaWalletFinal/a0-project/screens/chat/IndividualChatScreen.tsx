import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import {
  userService,
  tradingService,
  websocketService,
  EAST_AFRICAN_CONFIG,
  Trade,
  eastAfricanHelpers
} from '../../services';

// Chat message type (temporary until chatService is implemented)
interface ChatMessage {
  id: string;
  senderId: string;
  text: string;
  timestamp: string;
  status: 'sent' | 'delivered' | 'read';
  attachments?: string[];
}

// Mock messages for individual chat
const mockMessages = [
  {
    id: '1',
    senderId: '1', // current user
    text: "Hello, I'm interested in your BTC offer.",
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    status: 'read',
  },
  {
    id: '2',
    senderId: '2', // other trader
    text: 'Great! I can sell you 0.0025 BTC at the listed price of $67,500.',
    timestamp: new Date(Date.now() - 3500000).toISOString(),
    status: 'read',
  },
  {
    id: '3',
    senderId: '1',
    text: 'Perfect. How do we proceed with the trade?',
    timestamp: new Date(Date.now() - 3400000).toISOString(),
    status: 'read',
  },
  {
    id: '4',
    senderId: '2',
    text: "I'll initiate the trade now. You'll receive a notification to confirm.",
    timestamp: new Date(Date.now() - 3300000).toISOString(),
    status: 'read',
  },
  {
    id: '5',
    senderId: '1',
    text: 'Trade confirmed! Sending payment now.',
    timestamp: new Date(Date.now() - 3200000).toISOString(),
    status: 'read',
  },
  {
    id: '6',
    senderId: '2',
    text: 'Payment received! Releasing crypto from escrow.',
    timestamp: new Date(Date.now() - 3100000).toISOString(),
    status: 'read',
  },
  {
    id: '7',
    senderId: '1',
    text: "I've just sent the payment. Please check your account.",
    timestamp: new Date(Date.now() - 300000).toISOString(),
    status: 'delivered',
  },
];

const mockTrader = {
  id: '2',
  name: 'Alice Trader',
  avatar: null,
  isOnline: true,
  lastSeen: new Date(Date.now() - 300000).toISOString(),
  rating: 4.8,
  completedTrades: 42,
};

const mockTrade = {
  id: 'trade_1',
  type: 'buy',
  cryptoCurrency: 'BTC',
  fiatCurrency: 'USD',
  amount: '0.0025',
  price: '67500',
  status: 'payment_sent',
};

export default function IndividualChatScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const { theme } = useTheme();
  const flatListRef = useRef(null);

  // Route params
  const { traderId, tradeId, traderName, conversationId } = route.params || {};

  // State management
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [trade, setTrade] = useState<Trade | null>(null);
  const [otherTrader, setOtherTrader] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);

  // Load chat data
  const loadChatData = async (showLoading = true) => {
    if (!conversationId || !tradeId) {
      setError('Missing conversation or trade ID');
      setLoading(false);
      return;
    }

    try {
      if (showLoading) setLoading(true);
      setError(null);

      const [chatMessages, tradeData, profile] = await Promise.all([
        Promise.resolve(mockMessages), // TODO: Replace with chatService.getMessages(conversationId)
        tradingService.getTrade(tradeId),
        userService.getProfile(),
      ]);

      setMessages(chatMessages);
      setTrade(tradeData);
      setUserProfile(profile);

      // Determine other trader
      const otherUser = tradeData.buyer._id === profile._id ? tradeData.seller : tradeData.buyer;
      setOtherTrader(otherUser);

      // Join chat room for real-time updates
      websocketService.joinRoom('chat', conversationId);

      // Mark messages as read
      // TODO: Replace with chatService.markAsRead(conversationId)
      console.log('Messages marked as read for conversation:', conversationId);
    } catch (err) {
      console.error('Failed to load chat data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load chat');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadChatData();

    // Set up WebSocket listeners for real-time updates
    const unsubscribeChatMessage = websocketService.onChatMessage((data) => {
      if (data.chatId === conversationId) {
        setMessages(prev => [...prev, data.message]);
        // Auto-scroll to bottom for new messages
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    });

    const unsubscribeTradeUpdate = websocketService.onTradeUpdate((data) => {
      if (data.tradeId === tradeId) {
        setTrade(prev => prev ? { ...prev, status: data.status, timeline: data.timeline } : null);
      }
    });

    return () => {
      unsubscribeChatMessage();
      unsubscribeTradeUpdate();
      if (conversationId) {
        websocketService.leaveRoom('chat', conversationId);
      }
    };
  }, [conversationId, tradeId]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadChatData(false);
    setRefreshing(false);
  };

  const sendMessage = async () => {
    if (!message.trim() || !conversationId || sending) return;

    try {
      setSending(true);

      // Send message via WebSocket
      websocketService.sendChatMessage(conversationId, message.trim());
      setMessage('');
    } catch (err) {
      console.error('Failed to send message:', err);
      Alert.alert('Failed to Send', 'Please try again');
    } finally {
      setSending(false);
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const renderMessage = ({ item, index }: { item: ChatMessage, index: number }) => {
    const isCurrentUser = item.sender._id === userProfile?._id;
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const showDateSeparator = !previousMessage ||
      formatDate(item.timestamp) !== formatDate(previousMessage.timestamp);

    return (
      <View>
        {showDateSeparator && (
          <View style={styles.dateSeparator}>
            <Text style={styles.dateText}>{formatDate(item.timestamp)}</Text>
          </View>
        )}
        <View style={[
          styles.messageContainer,
          isCurrentUser ? styles.currentUserMessage : styles.otherUserMessage
        ]}>
          <View style={[
            styles.messageBubble,
            isCurrentUser ? styles.currentUserBubble : styles.otherUserBubble
          ]}>
            <Text style={[
              styles.messageText,
              isCurrentUser ? styles.currentUserText : styles.otherUserText
            ]}>
              {item.content}
            </Text>
            <View style={styles.messageFooter}>
              <Text style={[
                styles.timeText,
                isCurrentUser ? styles.currentUserTime : styles.otherUserTime
              ]}>
                {formatTime(item.timestamp)}
              </Text>
              {isCurrentUser && (
                <Ionicons 
                  name={
                    item.status === 'sending' ? 'time-outline' :
                    item.status === 'delivered' ? 'checkmark' :
                    item.status === 'read' ? 'checkmark-done' : 'time-outline'
                  }
                  size={14}
                  color={item.status === 'read' ? theme.colors.primary : theme.colors.textSecondary}
                  style={styles.statusIcon}
                />
              )}
            </View>
          </View>
        </View>
      </View>
    );
  };

  const getStatusText = () => {
    if (mockTrader.isOnline) {
      return 'Online';
    } else {
      const lastSeenDate = new Date(mockTrader.lastSeen);
      const now = new Date();
      const diffInMinutes = Math.floor((now - lastSeenDate) / (1000 * 60));
      
      if (diffInMinutes < 60) {
        return `Last seen ${diffInMinutes}m ago`;
      } else if (diffInMinutes < 1440) {
        return `Last seen ${Math.floor(diffInMinutes / 60)}h ago`;
      } else {
        return `Last seen ${Math.floor(diffInMinutes / 1440)}d ago`;
      }
    }
  };

  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Loading Chat...</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading conversation...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !trade || !otherTrader) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Chat Error</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load chat</Text>
          <Text style={styles.errorSubText}>{error || 'Chat not found'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadChatData()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.traderInfo}
          onPress={() => navigation.navigate('TraderProfile', { traderId })}
        >
          <View style={styles.traderAvatar}>
            <Text style={styles.avatarText}>
              {otherTrader.username.charAt(0).toUpperCase()}
            </Text>
            {otherTrader.isOnline && <View style={styles.onlineIndicator} />}
          </View>
          <View style={styles.traderDetails}>
            <Text style={styles.traderName}>@{otherTrader.username}</Text>
            <Text style={styles.statusText}>
              {trade.cryptocurrency.amount} {trade.cryptocurrency.type} • {eastAfricanHelpers.formatCurrencyAmount(trade.fiat.amount, trade.fiat.currency)}
            </Text>
          </View>
        </TouchableOpacity>

        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.headerButton}
            onPress={() => navigation.navigate('Trade', { tradeId })}
          >
            <Ionicons name="receipt-outline" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="call-outline" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Trade Context Banner */}
      <View style={styles.tradeContextBanner}>
        <View style={styles.tradeContextLeft}>
          <Ionicons name="swap-horizontal" size={16} color={theme.colors.textSecondary} />
          <Text style={styles.tradeContextText}>
            {mockTrade.type === 'buy' ? 'Buying' : 'Selling'} {mockTrade.amount} {mockTrade.cryptoCurrency} 
            for ${parseFloat(mockTrade.amount) * parseFloat(mockTrade.price)} {mockTrade.fiatCurrency}
          </Text>
        </View>
        <TouchableOpacity 
          onPress={() => navigation.navigate('Trade', { tradeId })}
        >
          <Text style={styles.viewTradeText}>View Trade</Text>
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item._id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContainer}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={() => (
          <View style={styles.emptyChat}>
            <Ionicons name="chatbubbles-outline" size={48} color={theme.colors.border} />
            <Text style={styles.emptyChatText}>No messages yet</Text>
            <Text style={styles.emptyChatSubText}>Start the conversation!</Text>
          </View>
        )}
      />

      {/* Input */}
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <View style={styles.inputRow}>
          <TouchableOpacity style={styles.attachButton}>
            <Ionicons name="attach" size={24} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          
          <TextInput
            style={styles.textInput}
            value={message}
            onChangeText={setMessage}
            placeholder="Type a message..."
            placeholderTextColor={theme.colors.inputPlaceholder}
            multiline
            maxLength={1000}
          />
          
          <TouchableOpacity
            style={[styles.sendButton, message.trim() ? styles.sendButtonActive : null]}
            onPress={sendMessage}
            disabled={!message.trim() || sending}
          >
            {sending ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Ionicons
                name="send"
                size={20}
                color={message.trim() ? theme.colors.textInverse : theme.colors.textTertiary}
              />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  traderInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  traderAvatar: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.success,
    borderWidth: 2,
    borderColor: theme.colors.surface,
  },
  traderDetails: {
    flex: 1,
  },
  traderName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  statusText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
  },
  tradeContextBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.warningLight,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.warning,
  },
  tradeContextLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tradeContextText: {
    fontSize: 14,
    color: theme.colors.warning,
    fontWeight: '500',
  },
  viewTradeText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primaryDark,
  },
  messagesList: {
    flex: 1,
  },
  messagesContainer: {
    paddingVertical: 16,
  },
  dateSeparator: {
    alignItems: 'center',
    marginVertical: 16,
  },
  dateText: {
    fontSize: 12,
    color: theme.colors.textTertiary,
    backgroundColor: theme.colors.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  messageContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  currentUserMessage: {
    alignItems: 'flex-end',
  },
  otherUserMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 18,
  },
  currentUserBubble: {
    backgroundColor: theme.colors.primary,
    borderBottomRightRadius: 4,
  },
  otherUserBubble: {
    backgroundColor: theme.colors.surface,
    borderBottomLeftRadius: 4,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  currentUserText: {
    color: 'white',
  },
  otherUserText: {
    color: theme.colors.text,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 4,
  },
  timeText: {
    fontSize: 12,
  },
  currentUserTime: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  otherUserTime: {
    color: theme.colors.textTertiary,
  },
  statusIcon: {
    marginLeft: 2,
  },
  inputContainer: {
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  attachButton: {
    padding: 8,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 100,
    color: theme.colors.inputText,
    backgroundColor: theme.colors.inputBackground,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  // New styles for East African features
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 12,
    textAlign: 'center',
  },
  errorSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  emptyChat: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyChatText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  emptyChatSubText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
});
