/**
 * Monitoring Integration Service
 * Connects business logic with comprehensive monitoring system
 */

const logger = require('../utils/logger');

class MonitoringIntegrationService {
  constructor() {
    this.comprehensiveMonitoring = null;
    this.isInitialized = false;
  }

  /**
   * Initialize with comprehensive monitoring instance
   */
  initialize(comprehensiveMonitoring) {
    this.comprehensiveMonitoring = comprehensiveMonitoring;
    this.isInitialized = true;
    logger.info('Monitoring integration service initialized');
  }

  /**
   * Record trade-related metrics
   */
  recordTrade(tradeData) {
    if (!this.isInitialized) return;

    try {
      const { status, cryptocurrency, amount, error } = tradeData;
      
      // Record trade creation/update
      this.comprehensiveMonitoring.recordTrade(status, cryptocurrency);
      
      // Record trade error if applicable
      if (error) {
        this.comprehensiveMonitoring.recordTradeError();
        logger.warn('Trade error recorded in monitoring', { 
          tradeId: tradeData.id, 
          error: error.message 
        });
      }

      // Log trade volume metrics
      if (amount && status === 'completed') {
        logger.info('Trade completed', {
          cryptocurrency,
          amount,
          status
        });
      }
    } catch (error) {
      logger.error('Failed to record trade metrics', { error: error.message });
    }
  }

  /**
   * Record escrow-related metrics
   */
  recordEscrow(escrowData) {
    if (!this.isInitialized) return;

    try {
      const { status, error } = escrowData;
      
      // Record escrow transaction
      this.comprehensiveMonitoring.recordEscrow(status);
      
      // Record escrow failure if applicable
      if (error || status === 'failed') {
        this.comprehensiveMonitoring.recordEscrowFailure();
        logger.warn('Escrow failure recorded in monitoring', { 
          escrowId: escrowData.id, 
          error: error?.message 
        });
      }
    } catch (error) {
      logger.error('Failed to record escrow metrics', { error: error.message });
    }
  }

  /**
   * Record wallet operation metrics
   */
  recordWalletOperation(operationData) {
    if (!this.isInitialized) return;

    try {
      const { operation, status, error } = operationData;
      
      // Record wallet operation
      this.comprehensiveMonitoring.recordWalletOperation(operation, status);
      
      // Log wallet operation details
      if (error) {
        logger.warn('Wallet operation error recorded', { 
          operation, 
          status, 
          error: error.message 
        });
      }
    } catch (error) {
      logger.error('Failed to record wallet operation metrics', { error: error.message });
    }
  }

  /**
   * Record security event metrics
   */
  recordSecurityEvent(eventData) {
    if (!this.isInitialized) return;

    try {
      const { eventType, severity, details } = eventData;
      
      // Record security event
      this.comprehensiveMonitoring.recordSecurityEvent(eventType, severity);
      
      // Log security event
      logger.security('Security event recorded in monitoring', {
        eventType,
        severity,
        details
      });
    } catch (error) {
      logger.error('Failed to record security event metrics', { error: error.message });
    }
  }

  /**
   * Update dispute metrics
   */
  async updateDisputeMetrics() {
    if (!this.isInitialized) return;

    try {
      const Dispute = require('../models/Dispute');
      
      // Count unresolved disputes
      const unresolvedCount = await Dispute.countDocuments({ 
        status: { $in: ['open', 'investigating'] } 
      });
      
      // Update monitoring metrics
      this.comprehensiveMonitoring.updateUnresolvedDisputes(unresolvedCount);
      
      logger.debug('Dispute metrics updated', { unresolvedCount });
    } catch (error) {
      logger.error('Failed to update dispute metrics', { error: error.message });
    }
  }

  /**
   * Record authentication events
   */
  recordAuthEvent(authData) {
    if (!this.isInitialized) return;

    try {
      const { event, success, userId, ip, userAgent } = authData;
      
      if (!success) {
        // Record failed authentication as security event
        this.recordSecurityEvent({
          eventType: 'failed_authentication',
          severity: 'medium',
          details: { event, userId, ip, userAgent }
        });
      }
      
      logger.audit('Authentication event recorded', {
        event,
        success,
        userId,
        ip
      });
    } catch (error) {
      logger.error('Failed to record auth event metrics', { error: error.message });
    }
  }

  /**
   * Record API performance metrics
   */
  recordAPIPerformance(performanceData) {
    if (!this.isInitialized) return;

    try {
      const { endpoint, method, responseTime, statusCode, error } = performanceData;
      
      // Log slow API responses
      if (responseTime > 2000) {
        logger.warn('Slow API response detected', {
          endpoint,
          method,
          responseTime,
          statusCode
        });
      }
      
      // Record API errors
      if (error || statusCode >= 500) {
        logger.error('API error recorded', {
          endpoint,
          method,
          statusCode,
          error: error?.message
        });
      }
    } catch (error) {
      logger.error('Failed to record API performance metrics', { error: error.message });
    }
  }

  /**
   * Trigger custom alert (enhanced for business flow monitoring)
   */
  async triggerAlert(alertData) {
    if (!this.isInitialized) return;

    try {
      const { type, message, severity, details, threshold, current } = alertData;

      // Enhanced alert data with business flow context
      const enhancedAlertData = {
        message,
        severity: severity || 'medium',
        details: {
          ...details,
          threshold,
          current,
          source: 'business_flow_monitoring',
          environment: process.env.NODE_ENV || 'development'
        },
        timestamp: new Date().toISOString()
      };

      // Send alert through comprehensive monitoring
      await this.comprehensiveMonitoring.sendAlert(type, enhancedAlertData);

      logger.warn('Business flow alert triggered', {
        type,
        message,
        severity,
        threshold,
        current,
        source: 'business_flow_monitoring'
      });
    } catch (error) {
      logger.error('Failed to trigger business flow alert', {
        error: error.message,
        alertType: alertData.type
      });
    }
  }

  /**
   * Get monitoring status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      comprehensiveMonitoring: !!this.comprehensiveMonitoring,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Shutdown monitoring integration
   */
  async shutdown() {
    try {
      logger.info('Shutting down monitoring integration service');
      this.isInitialized = false;
      this.comprehensiveMonitoring = null;
      logger.info('Monitoring integration service shutdown completed');
    } catch (error) {
      logger.error('Error during monitoring integration shutdown', { error: error.message });
    }
  }
}

// Create singleton instance
const monitoringIntegrationService = new MonitoringIntegrationService();

module.exports = {
  monitoringIntegrationService
};
