/**
 * System Status API Integration Tests
 * Tests for system status communication endpoints
 */

const request = require('supertest');
const app = require('../../src/server');
const { gracefulDegradationService } = require('../../src/services/gracefulDegradation');
const { generateToken } = require('../../src/utils/jwt');

// Mock dependencies
jest.mock('../../src/services/gracefulDegradation');
jest.mock('../../src/services/realTimeScalabilityService');
jest.mock('../../src/utils/logger');

describe('System Status API', () => {
  let adminToken;
  let userToken;

  beforeAll(() => {
    // Generate test tokens
    adminToken = generateToken({ _id: 'admin123', role: 'admin', username: 'testadmin' });
    userToken = generateToken({ _id: 'user123', role: 'user', username: 'testuser' });
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock default system status
    gracefulDegradationService.getSystemStatus.mockReturnValue({
      degradationLevel: 'none',
      serviceStatus: {
        redis: 'healthy',
        blockchain: 'healthy',
        database: 'healthy',
        external: 'healthy'
      },
      availableFeatures: {
        trading: true,
        walletOperations: true,
        realTimeUpdates: true,
        notifications: true,
        analytics: true,
        reporting: true
      },
      statusMessage: 'All systems operational',
      lastHealthCheck: new Date().toISOString(),
      activeAlerts: [],
      degradationHistory: []
    });
  });

  describe('GET /api/system/status', () => {
    test('should return public system status without authentication', async () => {
      const response = await request(app)
        .get('/api/system/status')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('degradationLevel');
      expect(response.body.data).toHaveProperty('statusMessage');
      expect(response.body.data).toHaveProperty('availableFeatures');
      expect(response.body.data).not.toHaveProperty('serviceStatus'); // Should be filtered out
      expect(response.body.data).not.toHaveProperty('activeAlerts'); // Should be filtered out
    });

    test('should return current degradation status', async () => {
      gracefulDegradationService.getSystemStatus.mockReturnValue({
        degradationLevel: 'partial',
        statusMessage: 'Some features may be temporarily limited',
        availableFeatures: { trading: true, analytics: false },
        lastHealthCheck: new Date().toISOString()
      });

      const response = await request(app)
        .get('/api/system/status')
        .expect(200);

      expect(response.body.data.degradationLevel).toBe('partial');
      expect(response.body.data.statusMessage).toContain('limited');
    });
  });

  describe('GET /api/system/status/detailed', () => {
    test('should require admin authentication', async () => {
      await request(app)
        .get('/api/system/status/detailed')
        .expect(401);

      await request(app)
        .get('/api/system/status/detailed')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });

    test('should return detailed status for admin', async () => {
      gracefulDegradationService.getDegradationStatistics.mockReturnValue({
        totalEvents: 5,
        recentEvents: 2,
        currentLevel: 'none',
        activeAlerts: 0,
        uptimePercentage: 99.5,
        degradationFrequency: 1
      });

      gracefulDegradationService.getCommunicationConfig.mockReturnValue({
        broadcastStatusChanges: true,
        notifyAdmins: true,
        notifyUsers: true
      });

      const response = await request(app)
        .get('/api/system/status/detailed')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('degradationLevel');
      expect(response.body.data).toHaveProperty('serviceStatus');
      expect(response.body.data).toHaveProperty('statistics');
      expect(response.body.data).toHaveProperty('communicationConfig');
    });
  });

  describe('GET /api/system/status/history', () => {
    test('should require admin authentication', async () => {
      await request(app)
        .get('/api/system/status/history')
        .expect(401);
    });

    test('should return degradation history with default parameters', async () => {
      const mockHistory = [
        {
          timestamp: new Date(),
          previousLevel: 'none',
          newLevel: 'partial',
          affectedFeatures: { disabled: ['analytics'] }
        }
      ];

      gracefulDegradationService.getSystemStatus.mockReturnValue({
        degradationLevel: 'none',
        statusMessage: 'All systems operational',
        degradationHistory: mockHistory
      });

      const response = await request(app)
        .get('/api/system/status/history')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.history).toHaveLength(1);
      expect(response.body.data.timeRange).toBe('24 hours');
    });

    test('should validate query parameters', async () => {
      await request(app)
        .get('/api/system/status/history?limit=200')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);

      await request(app)
        .get('/api/system/status/history?hours=200')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);
    });
  });

  describe('GET /api/system/alerts', () => {
    test('should return active alerts for admin', async () => {
      const mockAlerts = [
        {
          id: 'alert1',
          type: 'system_degradation',
          level: 'partial',
          message: 'System degradation detected',
          timestamp: new Date(),
          acknowledged: false
        }
      ];

      gracefulDegradationService.getSystemStatus.mockReturnValue({
        activeAlerts: mockAlerts
      });

      const response = await request(app)
        .get('/api/system/alerts')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.alerts).toHaveLength(1);
      expect(response.body.data.count).toBe(1);
    });
  });

  describe('POST /api/system/alerts/:alertId/acknowledge', () => {
    test('should acknowledge alert', async () => {
      gracefulDegradationService.acknowledgeAlert.mockReturnValue(true);

      const response = await request(app)
        .post('/api/system/alerts/alert123/acknowledge')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ notes: 'Acknowledged by admin' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('acknowledged');
      expect(gracefulDegradationService.acknowledgeAlert).toHaveBeenCalledWith('alert123', 'admin123');
    });

    test('should return 404 for non-existent alert', async () => {
      gracefulDegradationService.acknowledgeAlert.mockReturnValue(false);

      await request(app)
        .post('/api/system/alerts/nonexistent/acknowledge')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    test('should validate notes length', async () => {
      const longNotes = 'a'.repeat(501);

      await request(app)
        .post('/api/system/alerts/alert123/acknowledge')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ notes: longNotes })
        .expect(400);
    });
  });

  describe('PUT /api/system/communication/config', () => {
    test('should require admin role', async () => {
      await request(app)
        .put('/api/system/communication/config')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });

    test('should update communication configuration', async () => {
      gracefulDegradationService.updateCommunicationConfig.mockImplementation(() => {});
      gracefulDegradationService.getCommunicationConfig.mockReturnValue({
        broadcastStatusChanges: false,
        notifyAdmins: true,
        notifyUsers: false
      });

      const config = {
        broadcastStatusChanges: false,
        notifyUsers: false
      };

      const response = await request(app)
        .put('/api/system/communication/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(config)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(gracefulDegradationService.updateCommunicationConfig).toHaveBeenCalledWith(config);
    });

    test('should validate configuration parameters', async () => {
      await request(app)
        .put('/api/system/communication/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ broadcastStatusChanges: 'invalid' })
        .expect(400);

      await request(app)
        .put('/api/system/communication/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ alertThresholds: { partial: 500 } }) // Too low
        .expect(400);
    });
  });

  describe('POST /api/system/force-degradation', () => {
    test('should force degradation level for admin', async () => {
      gracefulDegradationService.forceDegradationLevel.mockImplementation(() => {});

      const response = await request(app)
        .post('/api/system/force-degradation')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          level: 'partial',
          reason: 'Testing degradation communication'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('partial');
      expect(gracefulDegradationService.forceDegradationLevel).toHaveBeenCalledWith('partial');
    });

    test('should validate degradation level', async () => {
      await request(app)
        .post('/api/system/force-degradation')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          level: 'invalid',
          reason: 'Test'
        })
        .expect(400);
    });

    test('should require reason', async () => {
      await request(app)
        .post('/api/system/force-degradation')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ level: 'partial' })
        .expect(400);
    });
  });

  describe('POST /api/system/broadcast', () => {
    test('should broadcast system message', async () => {
      const mockRealTimeService = require('../../src/services/realTimeScalabilityService');
      mockRealTimeService.broadcast = jest.fn().mockResolvedValue();

      const response = await request(app)
        .post('/api/system/broadcast')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          message: 'System maintenance scheduled',
          type: 'maintenance',
          priority: 'high'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('broadcasted');
      expect(mockRealTimeService.broadcast).toHaveBeenCalledWith(
        'system_message',
        expect.objectContaining({
          type: 'admin_broadcast',
          message: 'System maintenance scheduled',
          messageType: 'maintenance',
          priority: 'high'
        })
      );
    });

    test('should validate message parameters', async () => {
      await request(app)
        .post('/api/system/broadcast')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ message: '' })
        .expect(400);

      await request(app)
        .post('/api/system/broadcast')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          message: 'Test',
          type: 'invalid'
        })
        .expect(400);

      await request(app)
        .post('/api/system/broadcast')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          message: 'Test',
          priority: 'invalid'
        })
        .expect(400);
    });
  });

  describe('GET /api/system/statistics', () => {
    test('should return degradation statistics for admin', async () => {
      const mockStats = {
        totalEvents: 10,
        recentEvents: 3,
        currentLevel: 'none',
        activeAlerts: 0,
        uptimePercentage: 99.8,
        degradationFrequency: 2
      };

      gracefulDegradationService.getDegradationStatistics.mockReturnValue(mockStats);

      const response = await request(app)
        .get('/api/system/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockStats);
    });

    test('should require admin authentication', async () => {
      await request(app)
        .get('/api/system/statistics')
        .expect(401);

      await request(app)
        .get('/api/system/statistics')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });
  });
});
