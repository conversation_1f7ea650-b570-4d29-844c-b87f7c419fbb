const logger = require('../utils/logger');
const { createError, ERROR_CODES } = require('../utils/errorCodes');
const { createAuditLog } = require('./auditLogger');
const { filterErrorForLogging, filterForAPI } = require('../utils/sensitiveDataFilter');

class AppError extends Error {
  constructor(message, statusCode, errorCode = null, details = null) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.errorCode = errorCode;
    this.details = details;
    this.timestamp = new Date().toISOString();

    Error.captureStackTrace(this, this.constructor);
  }

  // Create AppError from structured error code
  static fromErrorCode(errorCodeKey, customMessage = null, details = null) {
    const errorInfo = ERROR_CODES[errorCodeKey];
    if (!errorInfo) {
      throw new Error(`Unknown error code: ${errorCodeKey}`);
    }

    return new AppError(
      customMessage || errorInfo.message,
      errorInfo.httpStatus,
      errorInfo.code,
      details
    );
  }
}

const handleCastErrorDB = (err) => {
  const message = `Invalid ${err.path}: ${err.value}`;
  return AppError.fromErrorCode('VALIDATION_INVALID_FORMAT', message, {
    path: err.path,
    value: err.value
  });
};

const handleDuplicateFieldsDB = (err) => {
  const duplicateField = Object.keys(err.keyValue)[0];
  const value = err.keyValue[duplicateField];

  // Map to specific error codes based on field
  let errorCode = 'VALIDATION_FAILED';
  if (duplicateField === 'email') {
    errorCode = 'USER_EMAIL_EXISTS';
  } else if (duplicateField === 'username') {
    errorCode = 'USER_USERNAME_EXISTS';
  }

  return AppError.fromErrorCode(errorCode, null, {
    field: duplicateField,
    value
  });
};

const handleValidationErrorDB = (err) => {
  const errors = Object.values(err.errors).map(el => ({
    field: el.path,
    message: el.message,
    value: el.value
  }));

  return AppError.fromErrorCode('VALIDATION_FAILED',
    `Validation failed: ${errors.map(e => e.message).join(', ')}`,
    { validationErrors: errors }
  );
};

const handleJWTError = () =>
  AppError.fromErrorCode('AUTH_TOKEN_INVALID');

const handleJWTExpiredError = () =>
  AppError.fromErrorCode('AUTH_TOKEN_EXPIRED');

const sendErrorDev = (err, req, res) => {
  // Log error for development with sensitive data filtering
  const errorData = {
    error: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  };

  logger.error('Development Error:', filterErrorForLogging(errorData));

  // Filter response data for API
  const responseData = {
    success: false,
    status: err.status,
    error: {
      code: err.errorCode,
      message: err.message,
      details: err.details,
      timestamp: err.timestamp,
      stack: err.stack
    }
  };

  res.status(err.statusCode).json(filterForAPI(responseData));
};

const sendErrorProd = async (err, req, res) => {
  // Create audit log for errors
  try {
    await createAuditLog({
      userId: req.user?._id,
      action: 'system_error',
      resource: 'system',
      method: req.method,
      endpoint: req.originalUrl,
      statusCode: err.statusCode,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      success: false,
      error: {
        message: err.message,
        code: err.errorCode,
        stack: err.isOperational ? undefined : err.stack
      },
      severity: err.statusCode >= 500 ? 'high' : 'medium'
    });
  } catch (auditError) {
    logger.error('Failed to create audit log for error:', auditError);
  }

  // Operational, trusted error: send structured message to client
  if (err.isOperational) {
    res.status(err.statusCode).json({
      success: false,
      status: err.status,
      error: {
        code: err.errorCode,
        message: err.message,
        details: err.details,
        timestamp: err.timestamp
      }
    });
  } else {
    // Programming or other unknown error: don't leak error details
    const errorData = {
      error: err.message,
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userId: req.user?._id
    };

    logger.error('Unhandled Error:', filterErrorForLogging(errorData));

    const responseData = {
      success: false,
      status: 'error',
      error: {
        code: 'SYSTEM_003',
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      }
    };

    res.status(500).json(filterForAPI(responseData));
  }
};

const errorHandler = async (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, req, res);
  } else {
    let error = { ...err };
    error.message = err.message;

    if (error.name === 'CastError') error = handleCastErrorDB(error);
    if (error.code === 11000) error = handleDuplicateFieldsDB(error);
    if (error.name === 'ValidationError') error = handleValidationErrorDB(error);
    if (error.name === 'JsonWebTokenError') error = handleJWTError();
    if (error.name === 'TokenExpiredError') error = handleJWTExpiredError();

    await sendErrorProd(error, req, res);
  }
};

module.exports = { errorHandler, AppError };
