/**
 * Key Management Admin Routes
 * Provides admin endpoints for key management monitoring and operations
 */

const express = require('express');
const { keyManagementService } = require('../services/keyManagement');
const { authenticate, authorize } = require('../middleware/authMiddleware');
// Validation helper function
const validateRequest = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};
const logger = require('../utils/logger');
const { body, param, query } = require('express-validator');

const router = express.Router();

// Apply authentication and admin role requirement to all routes
router.use(authenticate);
router.use(authorize(['admin', 'super_admin']));

/**
 * GET /api/key-management/status
 * Get comprehensive security status for all keys
 */
router.get('/status', async (req, res) => {
  try {
    const status = await keyManagementService.getSecurityStatus();
    
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('❌ Failed to get key management status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve key management status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * POST /api/key-management/audit
 * Perform comprehensive security audit
 */
router.post('/audit', async (req, res) => {
  try {
    const auditResults = await keyManagementService.performSecurityAudit();
    
    res.json({
      success: true,
      data: auditResults
    });
  } catch (error) {
    logger.error('❌ Failed to perform security audit:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform security audit',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * POST /api/key-management/rotate/:network
 * Force key rotation for specific network
 */
router.post('/rotate/:network', [
  param('network').isIn(['ethereum', 'polygon', 'bitcoin']).withMessage('Invalid network'),
  body('reason').optional().isString().withMessage('Reason must be a string'),
  validateRequest
], async (req, res) => {
  try {
    const { network } = req.params;
    const { reason = 'manual_admin_rotation' } = req.body;
    
    await keyManagementService.rotateNetworkKey(network, reason);
    
    res.json({
      success: true,
      message: `Key rotation initiated for ${network}`,
      data: {
        network,
        reason,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error(`❌ Failed to rotate key for ${req.params.network}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to rotate key for ${req.params.network}`,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * POST /api/key-management/compromise/:network
 * Mark key as compromised
 */
router.post('/compromise/:network', [
  param('network').isIn(['ethereum', 'polygon', 'bitcoin']).withMessage('Invalid network'),
  body('reason').isString().notEmpty().withMessage('Reason is required'),
  validateRequest
], async (req, res) => {
  try {
    const { network } = req.params;
    const { reason } = req.body;
    const userId = req.user.id;
    
    await keyManagementService.markKeyCompromised(network, reason, userId);
    
    res.json({
      success: true,
      message: `Key marked as compromised for ${network}`,
      data: {
        network,
        reason,
        markedBy: userId,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error(`❌ Failed to mark key as compromised for ${req.params.network}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to mark key as compromised for ${req.params.network}`,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/key-management/metrics
 * Get real-time key management metrics
 */
router.get('/metrics', [
  query('hours').optional().isInt({ min: 1, max: 168 }).withMessage('Hours must be between 1 and 168'),
  validateRequest
], async (req, res) => {
  try {
    const hours = parseInt(req.query.hours) || 24;
    
    const metrics = {
      timestamp: new Date().toISOString(),
      timeWindow: `${hours} hours`,
      keyMetrics: await keyManagementService.monitoringService.getKeyMetrics(),
      securityMetrics: keyManagementService.monitoringService.getSecurityMetrics(),
      recentAlerts: keyManagementService.monitoringService.getRecentAlerts(hours),
      performanceMetrics: keyManagementService.monitoringService.metrics.performanceMetrics
    };
    
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    logger.error('❌ Failed to get key management metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve key management metrics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/key-management/alerts
 * Get security alerts with filtering
 */
router.get('/alerts', [
  query('severity').optional().isIn(['low', 'medium', 'high', 'critical']).withMessage('Invalid severity level'),
  query('type').optional().isString().withMessage('Type must be a string'),
  query('hours').optional().isInt({ min: 1, max: 720 }).withMessage('Hours must be between 1 and 720'),
  validateRequest
], async (req, res) => {
  try {
    const { severity, type, hours = 24 } = req.query;
    
    let alerts = keyManagementService.monitoringService.getRecentAlerts(parseInt(hours));
    
    // Apply filters
    if (severity) {
      alerts = alerts.filter(alert => alert.details.severity === severity);
    }
    
    if (type) {
      alerts = alerts.filter(alert => alert.type.includes(type));
    }
    
    res.json({
      success: true,
      data: {
        alerts,
        totalCount: alerts.length,
        filters: { severity, type, hours: parseInt(hours) }
      }
    });
  } catch (error) {
    logger.error('❌ Failed to get security alerts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve security alerts',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/key-management/health
 * Get key management system health check
 */
router.get('/health', async (req, res) => {
  try {
    const health = {
      timestamp: new Date().toISOString(),
      system: {
        initialized: keyManagementService.isInitialized,
        fallbackMode: keyManagementService.secureKeyService.fallbackMode,
        awsConnectivity: !keyManagementService.secureKeyService.fallbackMode
      },
      services: {
        secureKeyService: keyManagementService.secureKeyService ? 'operational' : 'unavailable',
        monitoringService: keyManagementService.monitoringService ? 'operational' : 'unavailable'
      },
      lastCheck: new Date().toISOString()
    };
    
    const statusCode = health.system.initialized ? 200 : 503;
    
    res.status(statusCode).json({
      success: health.system.initialized,
      data: health
    });
  } catch (error) {
    logger.error('❌ Failed to get key management health:', error);
    res.status(503).json({
      success: false,
      message: 'Key management system health check failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * POST /api/key-management/test-encryption
 * Test encryption/decryption functionality (admin only)
 */
router.post('/test-encryption', [
  body('testData').isString().notEmpty().withMessage('Test data is required'),
  validateRequest
], async (req, res) => {
  try {
    const { testData } = req.body;
    const { SecurityConfig } = require('../config/security');
    const securityConfig = new SecurityConfig();
    
    // Test encryption/decryption
    const encrypted = securityConfig.encrypt(testData);
    const decrypted = securityConfig.decrypt(encrypted);
    
    const success = decrypted === testData;
    
    res.json({
      success,
      data: {
        testPassed: success,
        encryptionWorking: !!encrypted.encrypted,
        decryptionWorking: success,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('❌ Encryption test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Encryption test failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/key-management/recommendations
 * Get security recommendations
 */
router.get('/recommendations', async (req, res) => {
  try {
    const status = await keyManagementService.getSecurityStatus();
    
    res.json({
      success: true,
      data: {
        recommendations: status.recommendations,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('❌ Failed to get security recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve security recommendations',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
