const logger = require('./utils/logger');

/**
 * Security Utilities for Admin Dashboard
 * Implements security headers, CSP, and protection mechanisms
 */

// Content Security Policy configuration
export const CSP_CONFIG = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-inline'", // Required for React development
    "'unsafe-eval'", // Required for React development
    'https://apis.google.com',
    'https://www.google-analytics.com'
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'", // Required for Material-UI
    'https://fonts.googleapis.com'
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
    'data:'
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https:',
    'http:'
  ],
  'connect-src': [
    "'self'",
    process.env.REACT_APP_API_URL || 'http://localhost:3000',
    process.env.REACT_APP_WS_URL || 'ws://localhost:3000',
    'https://api.kryptopesa.com',
    'wss://api.kryptopesa.com'
  ],
  'frame-src': ["'none'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
  'upgrade-insecure-requests': []
};

// Generate CSP header string
export const generateCSPHeader = () => {
  return Object.entries(CSP_CONFIG)
    .map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive;
      }
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');
};

// Security headers configuration
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  'Content-Security-Policy': generateCSPHeader()
};

// Input sanitization utilities
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

// Validate admin session
export const validateAdminSession = () => {
  const token = localStorage.getItem('admin_token');
  if (!token) return false;

  try {
    // Basic JWT validation (decode without verification for client-side check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const now = Date.now() / 1000;
    
    // Check if token is expired
    if (payload.exp && payload.exp < now) {
      localStorage.removeItem('admin_token');
      return false;
    }

    // Check if user has admin role
    if (!payload.role || !['admin', 'moderator'].includes(payload.role)) {
      localStorage.removeItem('admin_token');
      return false;
    }

    return true;
  } catch (error) {
    localStorage.removeItem('admin_token');
    return false;
  }
};

// Rate limiting for admin actions
class AdminRateLimiter {
  constructor() {
    this.attempts = new Map();
    this.maxAttempts = 10;
    this.windowMs = 15 * 60 * 1000; // 15 minutes
  }

  isAllowed(action, userId) {
    const key = `${action}_${userId}`;
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (validAttempts.length >= this.maxAttempts) {
      return false;
    }

    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    
    return true;
  }

  reset(action, userId) {
    const key = `${action}_${userId}`;
    this.attempts.delete(key);
  }
}

export const adminRateLimiter = new AdminRateLimiter();

// Secure data handling
export const secureDataHandler = {
  // Mask sensitive data for display
  maskSensitiveData: (data, fields = ['password', 'token', 'secret', 'key']) => {
    if (!data || typeof data !== 'object') return data;
    
    const masked = { ...data };
    
    fields.forEach(field => {
      if (masked[field]) {
        masked[field] = '***MASKED***';
      }
    });

    return masked;
  },

  // Validate data before sending to API
  validateApiData: (data) => {
    if (!data || typeof data !== 'object') return data;
    
    const validated = {};
    
    Object.keys(data).forEach(key => {
      const value = data[key];
      
      // Skip functions and undefined values
      if (typeof value === 'function' || value === undefined) {
        return;
      }
      
      // Sanitize string values
      if (typeof value === 'string') {
        validated[key] = sanitizeInput(value);
      } else {
        validated[key] = value;
      }
    });

    return validated;
  },

  // Log security events
  logSecurityEvent: (event, details = {}) => {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details: secureDataHandler.maskSensitiveData(details),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // In production, send to security monitoring service
    if (process.env.NODE_ENV === 'production') {
      // logger.warn('Security Event:', logEntry);
      // TODO: Send to security monitoring service
    } else {
      // logger.info('Security Event:', logEntry);
    }
  }
};

// Session management
export const sessionManager = {
  // Check session validity
  isSessionValid: () => {
    return validateAdminSession();
  },

  // Refresh session
  refreshSession: async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.token) {
          localStorage.setItem('admin_token', data.token);
          return true;
        }
      }
      
      return false;
    } catch (error) {
      // logger.error('Session refresh failed:', error);
      return false;
    }
  },

  // Auto-refresh session before expiry
  startAutoRefresh: () => {
    const interval = 10 * 60 * 1000; // 10 minutes
    
    setInterval(async () => {
      if (sessionManager.isSessionValid()) {
        await sessionManager.refreshSession();
      }
    }, interval);
  },

  // Clear session
  clearSession: () => {
    localStorage.removeItem('admin_token');
    sessionStorage.clear();
    
    // Clear any cached data
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name);
        });
      });
    }
  }
};

// Initialize security measures
export const initializeSecurity = () => {
  // Start session auto-refresh
  sessionManager.startAutoRefresh();

  // Add security event listeners
  window.addEventListener('beforeunload', () => {
    secureDataHandler.logSecurityEvent('session_end');
  });

  // Monitor for suspicious activity
  let clickCount = 0;
  let lastClickTime = 0;
  
  document.addEventListener('click', () => {
    const now = Date.now();
    
    if (now - lastClickTime < 100) {
      clickCount++;
      
      if (clickCount > 10) {
        secureDataHandler.logSecurityEvent('suspicious_activity', {
          type: 'rapid_clicking',
          count: clickCount
        });
        clickCount = 0;
      }
    } else {
      clickCount = 0;
    }
    
    lastClickTime = now;
  });

  // Monitor for console access (potential developer tools usage)
  let devtools = {
    open: false,
    orientation: null
  };

  const threshold = 160;

  setInterval(() => {
    if (window.outerHeight - window.innerHeight > threshold || 
        window.outerWidth - window.innerWidth > threshold) {
      if (!devtools.open) {
        devtools.open = true;
        secureDataHandler.logSecurityEvent('devtools_opened');
      }
    } else {
      devtools.open = false;
    }
  }, 500);

  // logger.info('Admin Dashboard Security Initialized');
};

// Export security utilities
export default {
  CSP_CONFIG,
  SECURITY_HEADERS,
  generateCSPHeader,
  sanitizeInput,
  validateAdminSession,
  adminRateLimiter,
  secureDataHandler,
  sessionManager,
  initializeSecurity
};
