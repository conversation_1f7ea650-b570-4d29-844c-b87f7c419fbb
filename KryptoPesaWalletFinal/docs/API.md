# KryptoPesa API Documentation

## Base URL
- Development: `http://localhost:3000/api`
- Production: `https://api.kryptopesa.com/api`

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format
All API responses follow this format:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Response data
  "error": {} // Error details (if any)
}
```

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "phone": "+************",
  "password": "securepassword",
  "firstName": "John",
  "lastName": "Doe",
  "country": "KE",
  "city": "Nairobi"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "user_id",
      "username": "john_doe",
      "email": "<EMAIL>",
      "profile": {
        "firstName": "<PERSON>",
        "lastName": "Doe"
      }
    },
    "token": "jwt_token"
  }
}
```

### POST /auth/login
Authenticate user and get access token.

**Request Body:**
```json
{
  "identifier": "<EMAIL>", // email, username, or phone
  "password": "securepassword"
}
```

### GET /auth/me
Get current user information (requires authentication).

## Wallet Endpoints

### POST /wallet/create
Create a new wallet for the authenticated user.

**Response:**
```json
{
  "success": true,
  "data": {
    "addresses": {
      "ethereum": "0x...",
      "bitcoin": "bc1..."
    },
    "mnemonic": "word1 word2 ... word12"
  }
}
```

### POST /wallet/import
Import existing wallet using mnemonic phrase.

**Request Body:**
```json
{
  "mnemonic": "word1 word2 ... word12"
}
```

### GET /wallet
Get wallet information and balances.

### POST /wallet/balances/refresh
Refresh wallet balances from blockchain.

### GET /wallet/transactions
Get transaction history.

**Query Parameters:**
- `limit` (optional): Number of transactions to return (default: 50)
- `offset` (optional): Number of transactions to skip (default: 0)

## Trading Endpoints

### GET /offers
Get available trading offers.

**Query Parameters:**
- `type`: "buy" or "sell"
- `cryptocurrency`: "USDT", "USDC", "BTC", etc.
- `fiatCurrency`: "KES", "TZS", "UGX", "RWF"
- `country`: "KE", "TZ", "UG", "RW"
- `paymentMethod`: "bank_transfer", "mobile_money", etc.
- `limit`: Number of offers to return
- `offset`: Number of offers to skip

**Response:**
```json
{
  "success": true,
  "data": {
    "offers": [
      {
        "offerId": "OFF-123",
        "creator": {
          "username": "trader1",
          "reputation": {
            "score": 95,
            "completedTrades": 150
          }
        },
        "type": "sell",
        "cryptocurrency": {
          "symbol": "USDT",
          "minAmount": "100",
          "maxAmount": "5000",
          "availableAmount": "2500"
        },
        "fiat": {
          "currency": "KES",
          "effectivePrice": 145.50
        },
        "paymentMethods": [
          {
            "method": "mobile_money",
            "details": {
              "provider": "M-Pesa"
            }
          }
        ]
      }
    ]
  }
}
```

### POST /offers
Create a new trading offer.

**Request Body:**
```json
{
  "type": "sell",
  "cryptocurrency": {
    "symbol": "USDT",
    "minAmount": "100",
    "maxAmount": "5000",
    "availableAmount": "2500"
  },
  "fiat": {
    "currency": "KES",
    "priceType": "margin",
    "marginPercentage": 2.5
  },
  "paymentMethods": [
    {
      "method": "mobile_money",
      "details": {
        "provider": "M-Pesa",
        "mobileNumber": "+************"
      }
    }
  ],
  "terms": {
    "timeLimit": 30,
    "instructions": "Payment instructions here"
  }
}
```

### POST /offers/{offerId}/respond
Respond to a trading offer to initiate a trade.

**Request Body:**
```json
{
  "amount": "1000" // Amount in cryptocurrency
}
```

### GET /trades/active
Get active trades for the authenticated user.

### GET /trades/{tradeId}
Get specific trade details.

### POST /trades/{tradeId}/confirm-payment
Confirm payment sent or received.

**Request Body:**
```json
{
  "type": "sent" // or "received"
}
```

### POST /trades/{tradeId}/dispute
Create a dispute for a trade.

**Request Body:**
```json
{
  "reason": "Payment not received after 2 hours"
}
```

## Chat Endpoints

### GET /chat/{tradeId}/messages
Get chat messages for a trade.

**Query Parameters:**
- `limit`: Number of messages to return
- `offset`: Number of messages to skip

### POST /chat/{tradeId}/messages
Send a message in trade chat.

**Request Body:**
```json
{
  "content": "Hello, I have sent the payment",
  "type": "text"
}
```

### POST /chat/{tradeId}/read
Mark messages as read.

**Request Body:**
```json
{
  "messageIds": ["msg1", "msg2"]
}
```

## User Endpoints

### GET /users/{userId}
Get user profile information.

### PUT /users/profile
Update user profile.

### GET /users/{userId}/reputation
Get user reputation and trading statistics.

## Admin Endpoints (Admin/Moderator only)

### GET /admin/users
Get all users with pagination and filters.

### PUT /admin/users/{userId}/status
Update user account status.

### GET /admin/trades
Get all trades with filters.

### GET /admin/disputes
Get all disputes.

### POST /admin/disputes/{disputeId}/resolve
Resolve a dispute.

**Request Body:**
```json
{
  "decision": "favor_buyer",
  "reasoning": "Evidence shows payment was made",
  "actionTaken": "release_escrow"
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 422 | Unprocessable Entity - Validation failed |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |

## Rate Limiting
- 100 requests per 15 minutes per IP address
- Higher limits for authenticated users
- Special limits for trading endpoints

## WebSocket Events

### Connection
```javascript
const socket = io('wss://api.kryptopesa.com', {
  auth: {
    token: 'jwt_token'
  }
});
```

### Events
- `join_trade` - Join a trade room
- `leave_trade` - Leave a trade room
- `send_message` - Send chat message
- `new_message` - Receive chat message
- `trade_update` - Trade status update
- `typing_start` - User started typing
- `typing_stop` - User stopped typing
