import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

// Mock data for dropdowns
const cryptoCurrencies = ['BTC', 'ETH', 'USDT', 'LTC', 'BCH'];
const fiatCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];
const paymentMethods = [
  'Bank Transfer',
  'PayPal',
  'Wise',
  'Revolut',
  'Cash App',
  'Venmo',
  'Zelle',
  'Western Union'
];

export default function CreateOfferScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  // Form state
  const [offerType, setOfferType] = useState('sell'); // 'buy' or 'sell'
  const [cryptoCurrency, setCryptoCurrency] = useState('BTC');
  const [fiatCurrency, setFiatCurrency] = useState('USD');
  const [priceType, setPriceType] = useState('fixed'); // 'fixed' or 'margin'
  const [fixedPrice, setFixedPrice] = useState('');
  const [marginPercentage, setMarginPercentage] = useState('');
  const [minAmount, setMinAmount] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  const [selectedPaymentMethods, setSelectedPaymentMethods] = useState([]);
  const [terms, setTerms] = useState('');
  const [autoReply, setAutoReply] = useState(true);
  const [timeLimit, setTimeLimit] = useState('30'); // minutes
  
  // Modal states
  const [cryptoModalVisible, setCryptoModalVisible] = useState(false);
  const [fiatModalVisible, setFiatModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  
  const handlePaymentMethodToggle = (method: string) => {
    setSelectedPaymentMethods(prev => 
      prev.includes(method) 
        ? prev.filter(m => m !== method)
        : [...prev, method]
    );
  };
  
  const validateForm = () => {
    if (priceType === 'fixed' && (!fixedPrice || parseFloat(fixedPrice) <= 0)) {
      Alert.alert('Error', 'Please enter a valid fixed price');
      return false;
    }
    
    if (priceType === 'margin' && (!marginPercentage || parseFloat(marginPercentage) === 0)) {
      Alert.alert('Error', 'Please enter a valid margin percentage');
      return false;
    }
    
    if (!minAmount || parseFloat(minAmount) <= 0) {
      Alert.alert('Error', 'Please enter a valid minimum amount');
      return false;
    }
    
    if (!maxAmount || parseFloat(maxAmount) <= 0) {
      Alert.alert('Error', 'Please enter a valid maximum amount');
      return false;
    }
    
    if (parseFloat(minAmount) >= parseFloat(maxAmount)) {
      Alert.alert('Error', 'Maximum amount must be greater than minimum amount');
      return false;
    }
    
    if (selectedPaymentMethods.length === 0) {
      Alert.alert('Error', 'Please select at least one payment method');
      return false;
    }
    
    return true;
  };
  
  const handleCreateOffer = () => {
    if (!validateForm()) return;
    
    // In a real app, we would call a Convex mutation
    // const result = await createOfferMutation({ ... });
    
    // For now, just show success and navigate back
    Alert.alert(
      'Success',
      'Your offer has been created successfully!',
      [
        {
          text: 'OK',
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Offer</Text>
        <View style={{ width: 24 }} />
      </View>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Offer Type */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Offer Type</Text>
          <View style={styles.offerTypeContainer}>
            <TouchableOpacity
              style={[
                styles.offerTypeButton,
                offerType === 'buy' && styles.offerTypeButtonActive
              ]}
              onPress={() => setOfferType('buy')}
            >
              <Text style={[
                styles.offerTypeText,
                offerType === 'buy' && styles.offerTypeTextActive
              ]}>
                Buy {cryptoCurrency}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.offerTypeButton,
                offerType === 'sell' && styles.offerTypeButtonActive
              ]}
              onPress={() => setOfferType('sell')}
            >
              <Text style={[
                styles.offerTypeText,
                offerType === 'sell' && styles.offerTypeTextActive
              ]}>
                Sell {cryptoCurrency}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Currency Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Currency Pair</Text>
          
          <View style={styles.currencyRow}>
            <View style={styles.currencyItem}>
              <Text style={styles.inputLabel}>Cryptocurrency</Text>
              <TouchableOpacity
                style={styles.dropdownButton}
                onPress={() => setCryptoModalVisible(true)}
              >
                <Text style={styles.dropdownText}>{cryptoCurrency}</Text>
                <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.currencyItem}>
              <Text style={styles.inputLabel}>Fiat Currency</Text>
              <TouchableOpacity
                style={styles.dropdownButton}
                onPress={() => setFiatModalVisible(true)}
              >
                <Text style={styles.dropdownText}>{fiatCurrency}</Text>
                <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        
        {/* Price Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Price Settings</Text>
          
          <View style={styles.priceTypeContainer}>
            <TouchableOpacity
              style={[
                styles.priceTypeButton,
                priceType === 'fixed' && styles.priceTypeButtonActive
              ]}
              onPress={() => setPriceType('fixed')}
            >
              <Text style={[
                styles.priceTypeText,
                priceType === 'fixed' && styles.priceTypeTextActive
              ]}>
                Fixed Price
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.priceTypeButton,
                priceType === 'margin' && styles.priceTypeButtonActive
              ]}
              onPress={() => setPriceType('margin')}
            >
              <Text style={[
                styles.priceTypeText,
                priceType === 'margin' && styles.priceTypeTextActive
              ]}>
                Market Margin
              </Text>
            </TouchableOpacity>
          </View>
          
          {priceType === 'fixed' ? (
            <View>
              <Text style={styles.inputLabel}>Price per {cryptoCurrency}</Text>
              <View style={styles.priceInputContainer}>
                <TextInput
                  style={styles.priceInput}
                  placeholder="0.00"
                  placeholderTextColor={theme.colors.inputPlaceholder}
                  value={fixedPrice}
                  onChangeText={setFixedPrice}
                  keyboardType="numeric"
                />
                <Text style={styles.priceCurrency}>{fiatCurrency}</Text>
              </View>
            </View>
          ) : (
            <View>
              <Text style={styles.inputLabel}>Margin from Market Price</Text>
              <View style={styles.priceInputContainer}>
                <TextInput
                  style={styles.priceInput}
                  placeholder="0.0"
                  placeholderTextColor={theme.colors.inputPlaceholder}
                  value={marginPercentage}
                  onChangeText={setMarginPercentage}
                  keyboardType="numeric"
                />
                <Text style={styles.priceCurrency}>%</Text>
              </View>
              <Text style={styles.marginHint}>
                Positive for above market, negative for below market
              </Text>
            </View>
          )}
        </View>
        
        {/* Amount Limits */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Amount Limits</Text>
          
          <View style={styles.amountRow}>
            <View style={styles.amountItem}>
              <Text style={styles.inputLabel}>Minimum ({fiatCurrency})</Text>
              <TextInput
                style={styles.input}
                placeholder="100"
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={minAmount}
                onChangeText={setMinAmount}
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.amountItem}>
              <Text style={styles.inputLabel}>Maximum ({fiatCurrency})</Text>
              <TextInput
                style={styles.input}
                placeholder="10000"
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={maxAmount}
                onChangeText={setMaxAmount}
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>
        
        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Methods</Text>
          <TouchableOpacity
            style={styles.paymentMethodsButton}
            onPress={() => setPaymentModalVisible(true)}
          >
            <Text style={styles.paymentMethodsText}>
              {selectedPaymentMethods.length > 0 
                ? `${selectedPaymentMethods.length} method(s) selected`
                : 'Select payment methods'
              }
            </Text>
            <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          
          {selectedPaymentMethods.length > 0 && (
            <View style={styles.selectedPaymentMethods}>
              {selectedPaymentMethods.map((method, index) => (
                <View key={index} style={styles.paymentMethodTag}>
                  <Text style={styles.paymentMethodTagText}>{method}</Text>
                </View>
              ))}
            </View>
          )}
        </View>
        
        {/* Trading Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trading Terms</Text>
          <TextInput
            style={styles.termsInput}
            placeholder="Enter your trading terms and conditions..."
            placeholderTextColor={theme.colors.inputPlaceholder}
            value={terms}
            onChangeText={setTerms}
            multiline
            numberOfLines={4}
          />
        </View>
        
        {/* Additional Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Additional Settings</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Auto Reply</Text>
              <Text style={styles.settingDescription}>
                Automatically reply to trade requests
              </Text>
            </View>
            <Switch
              value={autoReply}
              onValueChange={setAutoReply}
              trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
              thumbColor={autoReply ? theme.colors.primary : theme.colors.surface}
            />
          </View>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Payment Time Limit</Text>
              <Text style={styles.settingDescription}>
                Time limit for payment completion
              </Text>
            </View>
            <View style={styles.timeLimitContainer}>
              <TextInput
                style={styles.timeLimitInput}
                value={timeLimit}
                onChangeText={setTimeLimit}
                keyboardType="numeric"
                placeholderTextColor={theme.colors.inputPlaceholder}
              />
              <Text style={styles.timeLimitUnit}>min</Text>
            </View>
          </View>
        </View>
        
        {/* Create Button */}
        <TouchableOpacity style={styles.createButton} onPress={handleCreateOffer}>
          <Text style={styles.createButtonText}>Create Offer</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Cryptocurrency Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={cryptoModalVisible}
        onRequestClose={() => setCryptoModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Cryptocurrency</Text>
              <TouchableOpacity onPress={() => setCryptoModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {cryptoCurrencies.map((crypto, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    cryptoCurrency === crypto && styles.modalOptionSelected
                  ]}
                  onPress={() => {
                    setCryptoCurrency(crypto);
                    setCryptoModalVisible(false);
                  }}
                >
                  <Text style={[
                    styles.modalOptionText,
                    cryptoCurrency === crypto && styles.modalOptionTextSelected
                  ]}>
                    {crypto}
                  </Text>
                  {cryptoCurrency === crypto && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Fiat Currency Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={fiatModalVisible}
        onRequestClose={() => setFiatModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Fiat Currency</Text>
              <TouchableOpacity onPress={() => setFiatModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {fiatCurrencies.map((fiat, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    fiatCurrency === fiat && styles.modalOptionSelected
                  ]}
                  onPress={() => {
                    setFiatCurrency(fiat);
                    setFiatModalVisible(false);
                  }}
                >
                  <Text style={[
                    styles.modalOptionText,
                    fiatCurrency === fiat && styles.modalOptionTextSelected
                  ]}>
                    {fiat}
                  </Text>
                  {fiatCurrency === fiat && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Payment Methods Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={paymentModalVisible}
        onRequestClose={() => setPaymentModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Payment Methods</Text>
              <TouchableOpacity onPress={() => setPaymentModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {paymentMethods.map((method, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    selectedPaymentMethods.includes(method) && styles.modalOptionSelected
                  ]}
                  onPress={() => handlePaymentMethodToggle(method)}
                >
                  <Text style={[
                    styles.modalOptionText,
                    selectedPaymentMethods.includes(method) && styles.modalOptionTextSelected
                  ]}>
                    {method}
                  </Text>
                  {selectedPaymentMethods.includes(method) && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalDoneButton}
                onPress={() => setPaymentModalVisible(false)}
              >
                <Text style={styles.modalDoneButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  offerTypeContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  offerTypeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  offerTypeButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  offerTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  offerTypeTextActive: {
    color: 'white',
  },
  currencyRow: {
    flexDirection: 'row',
    gap: 12,
  },
  currencyItem: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  dropdownText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  priceTypeContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  priceTypeButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  priceTypeButtonActive: {
    backgroundColor: theme.colors.primaryLight + '40',
    borderColor: theme.colors.primary,
  },
  priceTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  priceTypeTextActive: {
    color: theme.colors.primary,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    paddingHorizontal: 16,
  },
  priceInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.inputText,
  },
  priceCurrency: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  marginHint: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  amountRow: {
    flexDirection: 'row',
    gap: 12,
  },
  amountItem: {
    flex: 1,
  },
  input: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    fontSize: 16,
    color: theme.colors.inputText,
  },
  paymentMethodsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  paymentMethodsText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  selectedPaymentMethods: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  paymentMethodTag: {
    backgroundColor: theme.colors.primaryLight + '40',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.primaryLight,
  },
  paymentMethodTagText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  termsInput: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    fontSize: 16,
    color: theme.colors.inputText,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  timeLimitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeLimitInput: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    fontSize: 16,
    color: theme.colors.inputText,
    width: 60,
    textAlign: 'center',
  },
  timeLimitUnit: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 32,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalScrollView: {
    maxHeight: 400,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalOptionSelected: {
    backgroundColor: theme.colors.primaryLight + '40',
  },
  modalOptionText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  modalOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  modalFooter: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  modalDoneButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalDoneButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});
