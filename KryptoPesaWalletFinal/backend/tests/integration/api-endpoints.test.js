/**
 * API Integration Tests
 * Tests all API endpoints for functionality and error handling
 */

const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../src/server');
const User = require('../../src/models/User');

describe('API Endpoints Integration Tests', () => {
  let authToken;
  let userId;

  beforeAll(async () => {
    await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/kryptopesa_test');
  });

  beforeEach(async () => {
    await User.deleteMany({});
    
    // Create test user
    const userResponse = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        username: 'testuser',
        password: 'TestPass123!',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          country: 'KE'
        }
      });
    
    authToken = userResponse.body.data.token;
    userId = userResponse.body.data.user._id;
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('Authentication Endpoints', () => {
    test('POST /api/auth/register - Should register new user', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'newuser',
          password: 'NewPass123!',
          profile: {
            firstName: 'New',
            lastName: 'User',
            country: 'KE'
          }
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.token).toBeDefined();
    });

    test('POST /api/auth/login - Should login existing user', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'TestPass123!'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
    });

    test('GET /api/auth/me - Should get current user', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
    });

    test('POST /api/auth/logout - Should logout user', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('User Management Endpoints', () => {
    test('GET /api/users/profile - Should get user profile', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.profile.firstName).toBe('Test');
    });

    test('PUT /api/users/profile - Should update user profile', async () => {
      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          profile: {
            firstName: 'Updated',
            lastName: 'User',
            bio: 'Updated bio'
          }
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.profile.firstName).toBe('Updated');
    });

    test('GET /api/users/reputation - Should get user reputation', async () => {
      const response = await request(app)
        .get('/api/users/reputation')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.reputation).toBeDefined();
    });
  });

  describe('Offer Endpoints', () => {
    test('POST /api/offers - Should create new offer', async () => {
      const response = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'sell',
          cryptocurrency: {
            symbol: 'USDT',
            network: 'polygon',
            minAmount: 10,
            maxAmount: 1000,
            availableAmount: 500
          },
          fiat: {
            currency: 'KES',
            priceType: 'fixed',
            fixedPrice: 150
          },
          paymentMethods: [{
            method: 'mobile_money',
            details: {
              provider: 'M-Pesa',
              mobileNumber: '+254700000001'
            }
          }],
          terms: {
            timeLimit: 30,
            instructions: 'Test instructions'
          }
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.offer.type).toBe('sell');
    });

    test('GET /api/offers - Should get offers list', async () => {
      const response = await request(app)
        .get('/api/offers');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.offers).toBeDefined();
    });

    test('GET /api/offers with filters - Should filter offers', async () => {
      const response = await request(app)
        .get('/api/offers?type=sell&cryptocurrency=USDT&fiatCurrency=KES');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('Trade Endpoints', () => {
    let offerId;

    beforeEach(async () => {
      // Create an offer first
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'sell',
          cryptocurrency: {
            symbol: 'USDT',
            network: 'polygon',
            minAmount: 10,
            maxAmount: 1000,
            availableAmount: 500
          },
          fiat: {
            currency: 'KES',
            priceType: 'fixed',
            fixedPrice: 150
          },
          paymentMethods: [{
            method: 'mobile_money',
            details: { provider: 'M-Pesa' }
          }],
          terms: { timeLimit: 30 }
        });
      
      offerId = offerResponse.body.data.offer.offerId;
    });

    test('GET /api/trades - Should get user trades', async () => {
      const response = await request(app)
        .get('/api/trades')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.trades).toBeDefined();
    });
  });

  describe('Wallet Endpoints', () => {
    test('GET /api/wallet/balance - Should get wallet balance', async () => {
      const response = await request(app)
        .get('/api/wallet/balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.balances).toBeDefined();
    });

    test('GET /api/wallet/transactions - Should get transaction history', async () => {
      const response = await request(app)
        .get('/api/wallet/transactions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.transactions).toBeDefined();
    });

    test('POST /api/wallet/import - Should validate wallet import', async () => {
      const response = await request(app)
        .post('/api/wallet/import')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'mnemonic',
          data: 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about'
        });

      // Should validate the mnemonic format
      expect(response.status).toBe(400); // Invalid mnemonic
    });
  });

  describe('Health and Monitoring Endpoints', () => {
    test('GET /health - Should return health status', async () => {
      const response = await request(app)
        .get('/health');

      expect(response.status).toBe(200);
      expect(response.body.status).toBeDefined();
    });

    test('GET /health/detailed - Should return detailed health', async () => {
      const response = await request(app)
        .get('/health/detailed');

      expect(response.status).toBe(200);
      expect(response.body.services).toBeDefined();
    });

    test('GET /metrics - Should return metrics (with auth)', async () => {
      const response = await request(app)
        .get('/metrics')
        .set('Authorization', 'Bearer test-metrics-token');

      // Should require proper metrics authentication
      expect([200, 401, 403]).toContain(response.status);
    });
  });

  describe('Error Handling', () => {
    test('Should handle 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/api/non-existent-route');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('Should handle unauthorized requests', async () => {
      const response = await request(app)
        .get('/api/users/profile');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    test('Should handle invalid JSON', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('invalid json');

      expect(response.status).toBe(400);
    });

    test('Should handle validation errors', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: 'invalid-email',
          password: '123' // Too short
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('Rate Limiting', () => {
    test('Should enforce rate limits on auth endpoints', async () => {
      const promises = [];
      
      // Make multiple rapid requests
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/api/auth/login')
            .send({
              identifier: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }

      const responses = await Promise.all(promises);
      
      // Should eventually hit rate limit
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Security Headers', () => {
    test('Should include security headers', async () => {
      const response = await request(app)
        .get('/health');

      expect(response.headers['x-content-type-options']).toBeDefined();
      expect(response.headers['x-frame-options']).toBeDefined();
    });
  });
});
