const mongoose = require('mongoose');

const disputeSchema = new mongoose.Schema({
  disputeId: {
    type: String,
    required: true,
    unique: true
  },
  trade: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trade',
    required: true,
    unique: true
  },
  initiator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  respondent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  category: {
    type: String,
    enum: [
      'payment_not_received',
      'payment_not_sent',
      'wrong_amount',
      'fake_payment_proof',
      'account_issues',
      'communication_issues',
      'other'
    ],
    required: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  evidence: [{
    type: {
      type: String,
      enum: ['image', 'document', 'screenshot', 'video'],
      required: true
    },
    url: {
      type: String,
      required: true
    },
    filename: String,
    description: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  status: {
    type: String,
    enum: ['open', 'under_review', 'awaiting_response', 'resolved', 'closed'],
    default: 'open'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  resolution: {
    decision: {
      type: String,
      enum: ['favor_initiator', 'favor_respondent', 'partial_refund', 'no_action'],
    },
    reasoning: String,
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedAt: Date,
    actionTaken: {
      type: String,
      enum: ['release_escrow', 'refund_seller', 'partial_release', 'manual_intervention']
    },
    transactionHash: String
  },
  communication: [{
    from: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    to: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    message: {
      type: String,
      required: true,
      maxlength: 1000
    },
    type: {
      type: String,
      enum: ['admin_note', 'user_response', 'system_message'],
      default: 'user_response'
    },
    attachments: [{
      url: String,
      filename: String
    }],
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  timeline: [{
    action: {
      type: String,
      required: true
    },
    actor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    details: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  metadata: {
    tradeValue: {
      crypto: String,
      fiat: Number,
      currency: String
    },
    escalationLevel: {
      type: Number,
      default: 1,
      min: 1,
      max: 3
    },
    responseDeadline: Date,
    autoCloseAt: Date,
    tags: [String]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
disputeSchema.index({ disputeId: 1 });
disputeSchema.index({ trade: 1 });
disputeSchema.index({ initiator: 1 });
disputeSchema.index({ status: 1, priority: 1 });
disputeSchema.index({ assignedTo: 1, status: 1 });
disputeSchema.index({ createdAt: -1 });
disputeSchema.index({ 'resolution.resolvedAt': -1 });

// Virtual for time since creation
disputeSchema.virtual('age').get(function() {
  return Date.now() - this.createdAt;
});

// Virtual for is overdue
disputeSchema.virtual('isOverdue').get(function() {
  if (this.metadata.responseDeadline) {
    return Date.now() > this.metadata.responseDeadline;
  }
  return false;
});

// Pre-save middleware to generate dispute ID
disputeSchema.pre('save', function(next) {
  if (!this.disputeId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.disputeId = `DSP-${timestamp}-${random}`.toUpperCase();
  }
  next();
});

// Method to add communication
disputeSchema.methods.addCommunication = function(fromUserId, message, type = 'user_response', toUserId = null, attachments = []) {
  this.communication.push({
    from: fromUserId,
    to: toUserId,
    message,
    type,
    attachments,
    timestamp: new Date()
  });
  
  this.addTimelineEntry(`${type}_added`, fromUserId);
  
  return this.save();
};

// Method to add timeline entry (with optional session support for transactions)
disputeSchema.methods.addTimelineEntry = function(action, actor, details = '', options = {}) {
  this.timeline.push({
    action,
    actor,
    details,
    timestamp: new Date()
  });

  // Support session for transactions - return save promise instead of this
  return this.save(options);
};

// Method to update status
disputeSchema.methods.updateStatus = function(newStatus, actor, details = '') {
  const oldStatus = this.status;
  this.status = newStatus;
  
  this.addTimelineEntry(`status_changed_from_${oldStatus}_to_${newStatus}`, actor, details);
  
  // Set deadlines based on status
  if (newStatus === 'awaiting_response') {
    this.metadata.responseDeadline = new Date(Date.now() + 48 * 60 * 60 * 1000); // 48 hours
  } else if (newStatus === 'resolved') {
    this.resolution.resolvedAt = new Date();
    this.resolution.resolvedBy = actor;
  }
  
  return this.save();
};

// Method to assign to admin
disputeSchema.methods.assignTo = function(adminId, assignedBy) {
  this.assignedTo = adminId;
  this.addTimelineEntry('assigned', assignedBy, `Assigned to admin ${adminId}`);
  
  return this.save();
};

// Method to escalate
disputeSchema.methods.escalate = function(reason, escalatedBy) {
  if (this.metadata.escalationLevel < 3) {
    this.metadata.escalationLevel += 1;
    this.priority = this.metadata.escalationLevel === 3 ? 'urgent' : 'high';
    
    this.addTimelineEntry('escalated', escalatedBy, reason);
    
    return this.save();
  }
  
  throw new Error('Dispute already at maximum escalation level');
};

// Method to resolve dispute
disputeSchema.methods.resolve = function(decision, reasoning, actionTaken, resolvedBy, transactionHash = null) {
  this.resolution = {
    decision,
    reasoning,
    resolvedBy,
    resolvedAt: new Date(),
    actionTaken,
    transactionHash
  };
  
  this.status = 'resolved';
  this.addTimelineEntry('resolved', resolvedBy, `Decision: ${decision}`);
  
  return this.save();
};

// Static method to get dispute statistics
disputeSchema.statics.getStats = function(timeframe = 30) {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: null,
        totalDisputes: { $sum: 1 },
        resolvedDisputes: {
          $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] }
        },
        averageResolutionTime: {
          $avg: {
            $cond: [
              { $eq: ['$status', 'resolved'] },
              { $subtract: ['$resolution.resolvedAt', '$createdAt'] },
              null
            ]
          }
        },
        categoryBreakdown: {
          $push: '$category'
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Dispute', disputeSchema);
