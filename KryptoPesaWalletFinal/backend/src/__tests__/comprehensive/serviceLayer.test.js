/**
 * Comprehensive Service Layer Testing
 * Tests all service methods including error and edge cases
 */

const testEnvironment = require('../testEnvironment');

// Import models
const User = require('../../models/User');

// Import existing services
const walletService = require('../../services/walletService');
const escrowService = require('../../services/escrowService');
const chatService = require('../../services/chatService');
const notificationService = require('../../services/notificationService');
const priceService = require('../../services/priceService');
const tradingService = require('../../services/tradingService');
const authService = require('../../services/authService');
const transactionService = require('../../services/transactionService');

const bcrypt = require('bcryptjs');

describe('Comprehensive Service Layer Tests', () => {
  let testUser1, testUser2, testWallet1, testWallet2;

  beforeAll(async () => {
    await testEnvironment.setup();
  }, 60000);

  beforeEach(async () => {
    await testEnvironment.cleanDatabase();
    
    // Create test users
    testUser1 = await testEnvironment.createTestUser({
      email: '<EMAIL>',
      username: 'testuser1',
      phone: '+254700000001'
    });

    testUser2 = await testEnvironment.createTestUser({
      email: '<EMAIL>',
      username: 'testuser2',
      phone: '+254700000002'
    });

    // Create test wallets
    testWallet1 = await testEnvironment.createTestWallet(testUser1._id);
    testWallet2 = await testEnvironment.createTestWallet(testUser2._id);
  });

  afterEach(async () => {
    await testEnvironment.cleanup();
  });

  afterAll(async () => {
    await testEnvironment.teardown();
  });

  describe('User Model Tests', () => {
    test('should create user with validation', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'newuser',
        phone: '+254700000004',
        password: 'password123',
        profile: {
          firstName: 'New',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      const user = await User.create(userData);
      expect(user).toBeDefined();
      expect(user.email).toBe(userData.email);
      expect(user.password).not.toBe(userData.password); // Should be hashed
      expect(user.profile.firstName).toBe(userData.profile.firstName);
    });

    test('should handle duplicate email error', async () => {
      const userData = {
        email: testUser1.email,
        username: 'duplicate',
        phone: '+254700000005',
        password: 'password123',
        profile: {
          firstName: 'Duplicate',
          lastName: 'User',
          location: {
            country: 'KE',
            city: 'Nairobi'
          }
        }
      };

      await expect(User.create(userData)).rejects.toThrow();
    });

    test('should authenticate user correctly', async () => {
      const user = await User.findOne({ email: testUser1.email }).select('+password');
      expect(user).toBeDefined();

      const isMatch = await user.comparePassword('password123');
      expect(isMatch).toBe(true);
    });

    test('should fail authentication with wrong password', async () => {
      const user = await User.findOne({ email: testUser1.email }).select('+password');
      expect(user).toBeDefined();

      const isMatch = await user.comparePassword('wrongpassword');
      expect(isMatch).toBe(false);
    });

    test('should update user profile', async () => {
      const updateData = { 'profile.firstName': 'UpdatedName' };
      const updatedUser = await User.findByIdAndUpdate(testUser1._id, updateData, { new: true });

      expect(updatedUser).toBeDefined();
      expect(updatedUser.profile.firstName).toBe('UpdatedName');
    });

    test('should handle invalid user ID', async () => {
      const invalidId = '507f1f77bcf86cd799439011';
      const user = await User.findById(invalidId);

      expect(user).toBeNull();
    });
  });

  describe('Wallet Service Tests', () => {
    test('should create wallet with mnemonic', async () => {
      const newUser = await testEnvironment.createTestUser({
        email: '<EMAIL>',
        username: 'walletuser',
        phone: '+254700000003'
      });

      const result = await walletService.createWallet(newUser._id);
      expect(result.wallet).toBeDefined();
      expect(result.addresses.ethereum).toBeDefined();
      expect(result.mnemonic).toBeDefined();
      expect(result.mnemonic.split(' ')).toHaveLength(24); // 24-word mnemonic
    });

    test('should get wallet for user', async () => {
      const wallet = await walletService.getWallet(testUser1._id);
      expect(wallet).toBeDefined();
      expect(wallet.user._id.toString()).toBe(testUser1._id.toString());
      expect(wallet.balances).toBeDefined();
      expect(Array.isArray(wallet.balances)).toBe(true);
    });

    test('should get transaction history', async () => {
      const history = await walletService.getTransactionHistory(testUser1._id, 10, 0);
      expect(history).toBeDefined();
      expect(history.transactions).toBeDefined();
      expect(Array.isArray(history.transactions)).toBe(true);
      expect(history.total).toBeDefined();
    });

    test('should get wallet statistics', async () => {
      const stats = await walletService.getWalletStats(testUser1._id);
      expect(stats).toBeDefined();
      expect(stats.totalValue).toBeDefined();
      expect(typeof stats.totalValue).toBe('number');
      expect(stats.totalTransactions).toBeDefined();
    });

    test('should get wallet security status', async () => {
      const securityStatus = await walletService.getWalletSecurityStatus(testUser1._id);
      expect(securityStatus).toBeDefined();
      expect(securityStatus.hasBackup).toBeDefined();
      expect(typeof securityStatus.hasBackup).toBe('boolean');
      expect(securityStatus.securityScore).toBeDefined();
    });
  });

  describe('Trade Service Tests', () => {
    let testOffer;

    beforeEach(async () => {
      // Create test offer
      const Offer = require('../../models/Offer');
      testOffer = await Offer.create({
        offerId: `offer_${Date.now()}`,
        creator: testUser1._id,
        type: 'sell',
        cryptocurrency: {
          symbol: 'BTC',
          network: 'bitcoin',
          minAmount: '0.001',
          maxAmount: '1.0',
          availableAmount: '0.5'
        },
        fiat: {
          currency: 'KES',
          priceType: 'fixed',
          fixedPrice: 45000
        },
        paymentMethods: [{
          method: 'mobile_money',
          details: {
            provider: 'M-Pesa',
            instructions: 'Send to **********'
          },
          isPreferred: true
        }],
        location: {
          country: 'KE'
        },
        status: 'active'
      });
    });

    test('should validate trading service methods exist', async () => {
      // Test trading service methods exist
      expect(typeof tradingService.acceptTrade).toBe('function');
      expect(typeof tradingService.getOffers).toBe('function');
      expect(typeof tradingService.getActiveTrades).toBe('function');
      expect(typeof tradingService.cancelTrade).toBe('function');
    });

    test('should handle trading service errors gracefully', async () => {
      // Test with invalid user ID for getActiveTrades
      const invalidUserId = '507f1f77bcf86cd799439011';
      const trades = await tradingService.getActiveTrades(invalidUserId);
      expect(Array.isArray(trades)).toBe(true);
      expect(trades.length).toBe(0);
    });

    test('should get active trades for user', async () => {
      const trades = await tradingService.getActiveTrades(testUser1._id);
      expect(Array.isArray(trades)).toBe(true);
    });

    test('should get offers with filters', async () => {
      const offers = await tradingService.getOffers({
        cryptocurrency: 'BTC',
        type: 'sell',
        limit: 10
      });
      expect(Array.isArray(offers)).toBe(true);
    });

    test('should validate trade parameters', async () => {
      // Test with invalid offer ID
      const invalidTradeData = {
        offerId: 'invalid-id',
        amount: 0.05,
        paymentMethod: 'M-Pesa',
        message: 'Test trade'
      };

      await expect(tradingService.acceptTrade(testUser2._id, invalidTradeData)).rejects.toThrow();
    });
  });

  describe('Escrow Service Tests', () => {
    test('should validate escrow parameters', async () => {
      // Test with invalid trade ID
      const invalidTradeId = '507f1f77bcf86cd799439011';

      await expect(escrowService.createEscrow(
        invalidTradeId,
        testUser2._id, // buyer
        testUser1._id, // seller
        0.05,
        'BTC',
        'bitcoin'
      )).rejects.toThrow();
    });

    test('should validate release escrow parameters', async () => {
      const invalidTradeId = '507f1f77bcf86cd799439011';

      await expect(escrowService.releaseEscrow(invalidTradeId, testUser2._id)).rejects.toThrow();
    });

    test('should validate cancel escrow parameters', async () => {
      const invalidTradeId = '507f1f77bcf86cd799439011';

      await expect(escrowService.cancelEscrow(invalidTradeId, testUser1._id, 'Test cancellation')).rejects.toThrow();
    });
  });

  describe('Price Service Tests', () => {
    test('should validate price service methods exist', async () => {
      // Test price service methods exist
      expect(typeof priceService.getPrices).toBe('function');
      expect(typeof priceService.getPrice).toBe('function');
      expect(typeof priceService.getHistoricalPrices).toBe('function');
    });

    test('should handle price service configuration', async () => {
      // Test that price service is properly configured
      expect(priceService.cryptoMapping).toBeDefined();
      expect(priceService.fiatMapping).toBeDefined();
      expect(typeof priceService.cryptoMapping).toBe('object');
    });
  });

  describe('Notification Service Tests', () => {
    test('should send notification', async () => {
      const result = await notificationService.sendNotification(testUser1._id, {
        type: 'trade_update',
        title: 'Test Notification',
        message: 'This is a test notification'
      });
      expect(result).toBeDefined();
    });

    test('should send trade notification', async () => {
      const result = await notificationService.sendTradeNotification('trade123', testUser1._id, 'trade_created', {
        cryptoCurrency: 'BTC'
      });
      expect(result).toBeDefined();
    });
  });

  describe('Transaction Service Tests', () => {
    test('should validate transaction service methods exist', async () => {
      // Test transaction service methods exist
      expect(typeof transactionService.sendTransaction).toBe('function');
      expect(typeof transactionService.updateTransactionStatus).toBe('function');
      expect(typeof transactionService.getTransactionHistory).toBe('function');
      expect(typeof transactionService.estimateTransactionFee).toBe('function');
    });

    test('should get transaction history', async () => {
      const history = await transactionService.getTransactionHistory(testUser1._id, 10, 0);
      expect(history).toBeDefined();
      expect(history.transactions).toBeDefined();
      expect(Array.isArray(history.transactions)).toBe(true);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle wallet service errors gracefully', async () => {
      // Test with invalid user ID
      const invalidId = '507f1f77bcf86cd799439011';

      await expect(walletService.getWallet(invalidId)).rejects.toThrow();
    });

    test('should validate input parameters', async () => {
      // Test User model validation
      const invalidUserData = {
        email: 'invalid-email',
        username: '',
        password: '123', // Too short
        phone: 'invalid-phone',
        profile: {
          firstName: '',
          lastName: '',
          location: {
            country: 'INVALID',
            city: ''
          }
        }
      };

      await expect(User.create(invalidUserData)).rejects.toThrow();
    });

    test('should handle concurrent wallet operations safely', async () => {
      // Create multiple concurrent wallet operations
      const promises = Array.from({ length: 3 }, async (_, i) => {
        const user = await testEnvironment.createTestUser({
          email: `concurrent${i}@test.com`,
          username: `concurrent${i}`,
          phone: `+25470000010${i}` // Use different phone numbers
        });
        return walletService.createWallet(user._id);
      });

      const results = await Promise.all(promises);

      expect(results.length).toBe(3);
      results.forEach(result => {
        expect(result.wallet).toBeDefined();
        expect(result.addresses).toBeDefined();
      });
    });
  });
});
