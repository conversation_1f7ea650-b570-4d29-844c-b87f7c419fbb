#!/bin/bash

# KryptoPesa System Stop Script
# Stops all running components gracefully

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to kill process by PID file
kill_by_pidfile() {
    local pidfile=$1
    local service_name=$2
    
    if [ -f "$pidfile" ]; then
        local pid=$(cat "$pidfile")
        if kill -0 "$pid" 2>/dev/null; then
            print_status "Stopping $service_name (PID: $pid)..."
            kill "$pid"
            sleep 2
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "Force killing $service_name..."
                kill -9 "$pid" 2>/dev/null || true
            fi
            
            print_success "$service_name stopped"
        else
            print_warning "$service_name was not running"
        fi
        rm -f "$pidfile"
    else
        print_warning "No PID file found for $service_name"
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local service_name=$2
    
    local pids=$(lsof -ti :$port 2>/dev/null || true)
    if [ -n "$pids" ]; then
        print_status "Stopping $service_name on port $port..."
        echo "$pids" | xargs kill 2>/dev/null || true
        sleep 2
        
        # Force kill if still running
        local remaining_pids=$(lsof -ti :$port 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            print_warning "Force killing $service_name..."
            echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
        fi
        
        print_success "$service_name stopped"
    else
        print_warning "$service_name was not running on port $port"
    fi
}

# Stop backend server
stop_backend() {
    print_status "Stopping backend server..."
    kill_by_pidfile "logs/backend.pid" "Backend Server"
    kill_port 3000 "Backend Server"
}

# Stop admin dashboard
stop_admin_dashboard() {
    print_status "Stopping admin dashboard..."
    kill_by_pidfile "logs/admin-dashboard.pid" "Admin Dashboard"
    kill_port 3001 "Admin Dashboard"
}

# Stop MongoDB (Docker)
stop_mongodb() {
    print_status "Stopping MongoDB..."
    
    if command -v docker >/dev/null 2>&1; then
        if docker ps | grep -q "kryptopesa-mongodb"; then
            docker stop kryptopesa-mongodb >/dev/null 2>&1 || true
            docker rm kryptopesa-mongodb >/dev/null 2>&1 || true
            print_success "MongoDB container stopped"
        else
            print_warning "MongoDB container was not running"
        fi
    else
        # Stop local MongoDB
        local mongo_pids=$(pgrep mongod || true)
        if [ -n "$mongo_pids" ]; then
            print_status "Stopping local MongoDB..."
            echo "$mongo_pids" | xargs kill 2>/dev/null || true
            print_success "MongoDB stopped"
        else
            print_warning "MongoDB was not running"
        fi
    fi
}

# Stop Redis (Docker)
stop_redis() {
    print_status "Stopping Redis..."
    
    if command -v docker >/dev/null 2>&1; then
        if docker ps | grep -q "kryptopesa-redis"; then
            docker stop kryptopesa-redis >/dev/null 2>&1 || true
            docker rm kryptopesa-redis >/dev/null 2>&1 || true
            print_success "Redis container stopped"
        else
            print_warning "Redis container was not running"
        fi
    else
        # Stop local Redis
        local redis_pids=$(pgrep redis-server || true)
        if [ -n "$redis_pids" ]; then
            print_status "Stopping local Redis..."
            echo "$redis_pids" | xargs kill 2>/dev/null || true
            print_success "Redis stopped"
        else
            print_warning "Redis was not running"
        fi
    fi
}

# Stop React Native Metro bundler
stop_metro() {
    print_status "Stopping React Native Metro bundler..."
    kill_port 8081 "Metro Bundler"
}

# Clean up any remaining processes
cleanup_processes() {
    print_status "Cleaning up remaining processes..."
    
    # Kill any remaining Node.js processes related to KryptoPesa
    local node_pids=$(pgrep -f "kryptopesa\|admin-dashboard\|mobile" || true)
    if [ -n "$node_pids" ]; then
        print_warning "Killing remaining KryptoPesa processes..."
        echo "$node_pids" | xargs kill 2>/dev/null || true
        sleep 1
        
        # Force kill if still running
        local remaining_pids=$(pgrep -f "kryptopesa\|admin-dashboard\|mobile" || true)
        if [ -n "$remaining_pids" ]; then
            echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
        fi
    fi
}

# Main execution
main() {
    print_status "🛑 Stopping KryptoPesa System..."
    echo "=================================================="
    
    # Change to project directory
    cd "$(dirname "$0")/.."
    
    # Stop all services
    stop_backend
    stop_admin_dashboard
    stop_metro
    stop_mongodb
    stop_redis
    
    # Clean up processes
    cleanup_processes
    
    # Clean up log files (optional)
    if [ "$1" = "--clean-logs" ]; then
        print_status "Cleaning up log files..."
        rm -rf logs/*.log
        print_success "Log files cleaned"
    fi
    
    echo "=================================================="
    print_success "🎉 KryptoPesa System Stopped Successfully!"
    echo ""
    print_status "💡 Tips:"
    echo "  • Use --clean-logs flag to remove log files"
    echo "  • Docker containers and volumes are preserved"
    echo "  • To restart: ./scripts/start-system.sh"
}

# Run main function
main "$@"
