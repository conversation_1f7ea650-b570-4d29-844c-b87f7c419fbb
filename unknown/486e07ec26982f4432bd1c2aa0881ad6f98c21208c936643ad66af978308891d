const logger = require('./utils/logger');

/**
 * Crash Reporting Service
 * Comprehensive error tracking and reporting for production
 */

class CrashReportingService {
  constructor() {
    this.isInitialized = false;
    this.errorQueue = [];
    this.maxQueueSize = 50;
    this.reportingEndpoint = '/api/admin/crash-reports';
    this.sessionId = this.generateSessionId();
    this.userId = null;
    this.deviceInfo = this.collectDeviceInfo();
    this.errorCounts = new Map();
    this.maxErrorsPerType = 10; // Prevent spam
  }

  // Initialize crash reporting
  initialize(userId = null) {
    if (this.isInitialized) return;

    this.userId = userId;
    this.setupGlobalErrorHandlers();
    this.setupUnhandledRejectionHandler();
    this.setupConsoleErrorCapture();
    this.startPeriodicReporting();
    
    this.isInitialized = true;
    // logger.info('Crash reporting service initialized');
  }

  // Setup global error handlers
  setupGlobalErrorHandlers() {
    window.addEventListener('error', (event) => {
      this.captureError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href
      });
    });
  }

  // Setup unhandled promise rejection handler
  setupUnhandledRejectionHandler() {
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        type: 'unhandled_promise_rejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack,
        reason: event.reason,
        timestamp: Date.now(),
        url: window.location.href
      });
    });
  }

  // Setup console error capture
  setupConsoleErrorCapture() {
    const originalConsoleError = // logger.error;
    // logger.error = (...args) => {
      // Call original // logger.error
      originalConsoleError.apply(console, args);
      
      // Capture the error
      this.captureError({
        type: 'console_error',
        message: args.join(' '),
        arguments: args.map(arg => this.serializeArgument(arg)),
        timestamp: Date.now(),
        url: window.location.href
      });
    };
  }

  // Capture and queue error
  captureError(errorData) {
    try {
      // Enhance error data
      const enhancedError = {
        ...errorData,
        sessionId: this.sessionId,
        userId: this.userId,
        deviceInfo: this.deviceInfo,
        breadcrumbs: this.getBreadcrumbs(),
        userAgent: navigator.userAgent,
        timestamp: errorData.timestamp || Date.now(),
        errorId: this.generateErrorId()
      };

      // Check if we should throttle this error type
      const errorKey = this.getErrorKey(enhancedError);
      const currentCount = this.errorCounts.get(errorKey) || 0;
      
      if (currentCount >= this.maxErrorsPerType) {
        // logger.warn(`Error throttled: ${errorKey}`);
        return;
      }

      this.errorCounts.set(errorKey, currentCount + 1);

      // Add to queue
      this.errorQueue.push(enhancedError);

      // Trim queue if too large
      if (this.errorQueue.length > this.maxQueueSize) {
        this.errorQueue.shift();
      }

      // Report critical errors immediately
      if (this.isCriticalError(enhancedError)) {
        this.reportImmediately(enhancedError);
      }

      // logger.warn('Error captured:', enhancedError);
    } catch (captureError) {
      // logger.error('Failed to capture error:', captureError);
    }
  }

  // Capture React component errors
  captureReactError(error, errorInfo, componentName = 'Unknown') {
    this.captureError({
      type: 'react_error',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      componentName,
      timestamp: Date.now(),
      url: window.location.href
    });
  }

  // Capture API errors
  captureApiError(error, requestInfo = {}) {
    this.captureError({
      type: 'api_error',
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url || requestInfo.url,
      method: error.config?.method || requestInfo.method,
      data: error.response?.data,
      timestamp: Date.now()
    });
  }

  // Capture user feedback
  captureUserFeedback(feedback, context = {}) {
    this.captureError({
      type: 'user_feedback',
      message: feedback.message,
      category: feedback.category,
      severity: feedback.severity,
      context,
      timestamp: Date.now(),
      url: window.location.href
    });
  }

  // Report errors to server
  async reportErrors(errors = null) {
    if (!errors) {
      errors = [...this.errorQueue];
      this.errorQueue = [];
    }

    if (errors.length === 0) return;

    try {
      const response = await fetch(this.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify({
          errors,
          metadata: {
            reportedAt: Date.now(),
            sessionId: this.sessionId,
            userId: this.userId,
            deviceInfo: this.deviceInfo
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to report errors: ${response.status}`);
      }

      // logger.info(`Reported ${errors.length} errors successfully`);
    } catch (reportError) {
      // logger.error('Failed to report errors:', reportError);
      // Re-queue errors for retry
      this.errorQueue.unshift(...errors);
    }
  }

  // Report critical errors immediately
  async reportImmediately(error) {
    try {
      await this.reportErrors([error]);
    } catch (reportError) {
      // logger.error('Failed to report critical error immediately:', reportError);
    }
  }

  // Start periodic reporting
  startPeriodicReporting() {
    // Report errors every 2 minutes
    setInterval(() => {
      if (this.errorQueue.length > 0) {
        this.reportErrors();
      }
    }, 2 * 60 * 1000);

    // Report on page unload
    window.addEventListener('beforeunload', () => {
      if (this.errorQueue.length > 0) {
        // Use sendBeacon for reliable reporting on page unload
        const data = JSON.stringify({
          errors: this.errorQueue,
          metadata: {
            reportedAt: Date.now(),
            sessionId: this.sessionId,
            userId: this.userId,
            deviceInfo: this.deviceInfo
          }
        });

        navigator.sendBeacon(this.reportingEndpoint, data);
      }
    });
  }

  // Helper methods
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  collectDeviceInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      memory: performance.memory ? {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      } : null,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null
    };
  }

  getBreadcrumbs() {
    // Return recent navigation history
    return {
      currentUrl: window.location.href,
      referrer: document.referrer,
      timestamp: Date.now()
    };
  }

  getErrorKey(error) {
    return `${error.type}_${error.message?.substring(0, 100)}`;
  }

  isCriticalError(error) {
    const criticalTypes = ['react_error', 'api_error'];
    const criticalPatterns = [
      /network error/i,
      /unauthorized/i,
      /forbidden/i,
      /internal server error/i,
      /cannot read property/i,
      /undefined is not a function/i
    ];

    return criticalTypes.includes(error.type) ||
           criticalPatterns.some(pattern => pattern.test(error.message || ''));
  }

  serializeArgument(arg) {
    try {
      if (typeof arg === 'object' && arg !== null) {
        return JSON.stringify(arg, null, 2);
      }
      return String(arg);
    } catch (error) {
      return '[Unserializable Object]';
    }
  }

  // Public API
  setUserId(userId) {
    this.userId = userId;
  }

  addBreadcrumb(breadcrumb) {
    // Could be enhanced to maintain a breadcrumb trail
    // logger.info('Breadcrumb:', breadcrumb);
  }

  setContext(key, value) {
    this.deviceInfo.context = this.deviceInfo.context || {};
    this.deviceInfo.context[key] = value;
  }

  getErrorStats() {
    return {
      queueSize: this.errorQueue.length,
      errorCounts: Object.fromEntries(this.errorCounts),
      sessionId: this.sessionId,
      isInitialized: this.isInitialized
    };
  }

  clearErrors() {
    this.errorQueue = [];
    this.errorCounts.clear();
  }
}

// Create singleton instance
const crashReporting = new CrashReportingService();

// React hook for crash reporting
export const useCrashReporting = () => {
  const reportError = (error, context = {}) => {
    crashReporting.captureError({
      type: 'manual_report',
      message: error.message || error,
      stack: error.stack,
      context,
      timestamp: Date.now(),
      url: window.location.href
    });
  };

  const reportUserFeedback = (feedback) => {
    crashReporting.captureUserFeedback(feedback);
  };

  return {
    reportError,
    reportUserFeedback,
    getStats: () => crashReporting.getErrorStats()
  };
};

export default crashReporting;
