const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate } = require('../middleware/authMiddleware');
const { authenticateWallet } = require('../middleware/walletAuth');
const walletService = require('../services/walletService');
const transactionService = require('../services/transactionService');
const priceService = require('../services/priceService');
const { ethers } = require('ethers');
const logger = require('../utils/logger');

const router = express.Router();

console.log('🔧 [WALLET ROUTES] Loading wallet routes module...');

// All wallet routes require wallet authentication
router.use(authenticateWallet);

console.log('🔧 [WALLET ROUTES] Wallet authentication middleware applied');

// Create new wallet
router.post('/create', async (req, res, next) => {
  try {
    const auditContext = {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: '/api/wallet/create'
    };

    const result = await walletService.createWallet(req.user._id, null, auditContext);

    res.status(201).json({
      success: true,
      message: 'Wallet created successfully',
      data: {
        addresses: result.addresses,
        mnemonic: result.mnemonic, // Only returned once during creation
        securityWarning: result.securityWarning
      }
    });

  } catch (error) {
    // If user already has a wallet, return a more user-friendly error
    if (error.message === 'User already has a wallet') {
      return res.status(400).json({
        success: false,
        message: 'You already have a wallet. Please use the existing wallet or import a new one.',
        code: 'WALLET_EXISTS'
      });
    }
    next(error);
  }
});

// Import existing wallet
router.post('/import', [
  body('mnemonic')
    .notEmpty()
    .withMessage('Mnemonic phrase is required')
    .custom((value) => {
      if (!ethers.utils.isValidMnemonic(value)) {
        throw new Error('Invalid mnemonic phrase');
      }
      return true;
    })
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { mnemonic } = req.body;

    const auditContext = {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: '/api/wallet/import'
    };

    const result = await walletService.importWallet(req.user._id, mnemonic, auditContext);

    res.status(201).json({
      success: true,
      message: 'Wallet imported successfully',
      data: {
        addresses: result.addresses,
        securityWarning: result.securityWarning
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get wallet info for wallet-authenticated users
router.get('/', async (req, res, next) => {
  console.log('🎯 [WALLET GET] Route handler reached!', {
    method: req.method,
    path: req.path,
    walletAddress: req.user?.walletAddress,
    timestamp: new Date().toISOString()
  });

  // IMMEDIATELY set cache-busting headers to prevent 304 responses
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0, private',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Last-Modified': new Date().toUTCString(),
    'ETag': `"wallet-${Date.now()}-${Math.random()}"`,
    'Vary': '*'
  });

  console.log('🔍 [WALLET GET] Request received:', {
    url: req.originalUrl,
    query: req.query,
    walletAddress: req.user.walletAddress,
    userType: 'WalletUser',
    headers: {
      'if-none-match': req.headers['if-none-match'],
      'if-modified-since': req.headers['if-modified-since'],
      'cache-control': req.headers['cache-control']
    },
    timestamp: new Date().toISOString()
  });

  try {
    // For wallet-authenticated users, return wallet data based on their wallet address
    // This is a simplified response that matches what the mobile app expects
    const walletAddress = req.user.walletAddress;

    // Create mock wallet data structure that matches mobile app expectations
    const walletData = {
      wallets: [
        {
          currency: 'MATIC',
          name: 'Polygon',
          balance: 0.0,
          pendingBalance: 0.0,
          usdValue: 0.0,
          address: walletAddress,
          change: 0.0
        },
        {
          currency: 'USDT',
          name: 'Tether USD',
          balance: 0.0,
          pendingBalance: 0.0,
          usdValue: 0.0,
          address: walletAddress,
          change: 0.0
        },
        {
          currency: 'USDC',
          name: 'USD Coin',
          balance: 0.0,
          pendingBalance: 0.0,
          usdValue: 0.0,
          address: walletAddress,
          change: 0.0
        }
      ],
      totalUsdValue: 0.0
    };

    console.log('✅ [WALLET GET] Response prepared for wallet user:', {
      walletAddress,
      walletsCount: walletData.wallets.length,
      totalUsdValue: walletData.totalUsdValue,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      data: walletData
    });

  } catch (error) {
    console.error('❌ [WALLET GET] Error:', error);
    next(error);
  }
});

// Update balances with throttling
router.post('/balances/refresh', async (req, res, next) => {
  try {
    const forceUpdate = req.body.force === true;
    const result = await walletService.updateBalances(req.user._id, forceUpdate);

    // Handle cached responses
    if (result.fromCache) {
      res.json({
        success: true,
        message: 'Balances retrieved from cache (throttled)',
        data: {
          balances: result.balances,
          fromCache: true,
          cacheAge: result.cacheAge,
          lastUpdated: result.lastUpdated
        }
      });
    } else {
      res.json({
        success: true,
        message: 'Balances updated successfully',
        data: {
          balances: result.balances,
          fromCache: false,
          lastUpdated: new Date().toISOString()
        }
      });
    }

  } catch (error) {
    // Handle throttling errors specifically
    if (error.statusCode === 429) {
      return res.status(429).json({
        success: false,
        error: error.message,
        code: 'RATE_LIMITED',
        retryAfter: error.retryAfter || 30
      });
    }
    next(error);
  }
});

// Get wallet security status
router.get('/security/status', async (req, res, next) => {
  try {
    const status = await walletService.getWalletSecurityStatus(req.user._id);

    res.json({
      success: true,
      message: 'Security status retrieved successfully',
      data: status
    });

  } catch (error) {
    next(error);
  }
});

// Verify mnemonic phrase
router.post('/security/verify-mnemonic', [
  body('mnemonic')
    .notEmpty()
    .withMessage('Mnemonic phrase is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { mnemonic } = req.body;
    const auditContext = {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: '/api/wallet/security/verify-mnemonic'
    };

    const isValid = await walletService.verifyMnemonic(req.user._id, mnemonic, auditContext);

    res.json({
      success: true,
      message: isValid ? 'Mnemonic verified successfully' : 'Mnemonic verification failed',
      data: { isValid }
    });

  } catch (error) {
    next(error);
  }
});

// Validate mnemonic strength
router.post('/security/validate-mnemonic', [
  body('mnemonic')
    .notEmpty()
    .withMessage('Mnemonic phrase is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { mnemonic } = req.body;
    const validation = walletService.validateMnemonicStrength(mnemonic);

    res.json({
      success: true,
      message: 'Mnemonic validation completed',
      data: validation
    });

  } catch (error) {
    next(error);
  }
});

// Mark backup as completed
router.post('/security/backup-completed', async (req, res, next) => {
  try {
    const auditContext = {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: '/api/wallet/security/backup-completed'
    };

    const result = await walletService.markBackupCompleted(req.user._id, auditContext);

    res.json({
      success: true,
      message: 'Backup marked as completed successfully',
      data: { completed: result }
    });

  } catch (error) {
    next(error);
  }
});

// Get balance update statistics
router.get('/security/balance-stats', async (req, res, next) => {
  try {
    const stats = await walletService.getRecentBalanceUpdates(req.user._id);

    res.json({
      success: true,
      message: 'Balance update statistics retrieved successfully',
      data: stats
    });

  } catch (error) {
    next(error);
  }
});

// Get transaction history
router.get('/transactions', async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const result = await walletService.getTransactionHistory(req.user._id, limit, offset);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    next(error);
  }
});

// Verify mnemonic
router.post('/verify-mnemonic', [
  body('mnemonic')
    .notEmpty()
    .withMessage('Mnemonic phrase is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { mnemonic } = req.body;
    const isValid = await walletService.verifyMnemonic(req.user._id, mnemonic);

    res.json({
      success: true,
      data: { isValid }
    });

  } catch (error) {
    next(error);
  }
});

// Mark backup as completed
router.post('/backup/complete', async (req, res, next) => {
  try {
    await walletService.markBackupCompleted(req.user._id);

    res.json({
      success: true,
      message: 'Backup marked as completed'
    });

  } catch (error) {
    next(error);
  }
});

// Get wallet statistics
router.get('/stats', async (req, res, next) => {
  try {
    const stats = await walletService.getWalletStats(req.user._id);

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    next(error);
  }
});

// Send transaction
router.post('/send', [
  body('toAddress').notEmpty().withMessage('Recipient address is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
  body('symbol').isIn(['BTC', 'ETH', 'MATIC', 'USDT', 'USDC', 'DAI']).withMessage('Invalid cryptocurrency'),
  body('network').isIn(['bitcoin', 'ethereum', 'polygon']).withMessage('Invalid network')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await transactionService.sendTransaction(req.user._id, req.body);

    res.json({
      success: true,
      message: 'Transaction sent successfully',
      data: result
    });

  } catch (error) {
    next(error);
  }
});

// Estimate transaction fee
router.post('/estimate-fee', [
  body('toAddress').notEmpty().withMessage('Recipient address is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
  body('symbol').isIn(['BTC', 'ETH', 'MATIC', 'USDT', 'USDC', 'DAI']).withMessage('Invalid cryptocurrency'),
  body('network').isIn(['bitcoin', 'ethereum', 'polygon']).withMessage('Invalid network')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const estimate = await transactionService.estimateTransactionFee(req.user._id, req.body);

    res.json({
      success: true,
      data: { estimate }
    });

  } catch (error) {
    next(error);
  }
});

// Get current prices
router.get('/prices', async (req, res, next) => {
  try {
    const { cryptos, currencies } = req.query;
    const cryptoArray = cryptos ? cryptos.split(',') : ['BTC', 'ETH', 'MATIC', 'USDT', 'USDC'];
    const currencyArray = currencies ? currencies.split(',') : ['USD'];

    const prices = await priceService.getPrices(cryptoArray, currencyArray);

    res.json({
      success: true,
      data: { prices }
    });

  } catch (error) {
    next(error);
  }
});

// Get portfolio value
router.get('/portfolio-value', async (req, res, next) => {
  try {
    const { currency = 'USD' } = req.query;
    const wallet = await walletService.getWallet(req.user._id);

    const portfolioValue = await priceService.getPortfolioValue(wallet.balances, currency);

    res.json({
      success: true,
      data: { portfolioValue }
    });

  } catch (error) {
    next(error);
  }
});

// Get transaction history
router.get('/transactions', async (req, res, next) => {
  try {
    const { limit = 50, offset = 0 } = req.query;
    const transactions = await transactionService.getTransactionHistory(
      req.user._id,
      parseInt(limit),
      parseInt(offset)
    );

    res.json({
      success: true,
      data: transactions
    });

  } catch (error) {
    next(error);
  }
});

// Verify mnemonic
router.post('/verify-mnemonic', [
  body('mnemonic').notEmpty().withMessage('Mnemonic is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { mnemonic } = req.body;
    const isValid = await walletService.verifyMnemonic(mnemonic);

    res.json({
      success: true,
      data: { isValid }
    });

  } catch (error) {
    next(error);
  }
});

// Mark backup as completed
router.post('/backup/complete', async (req, res, next) => {
  try {
    const success = await walletService.markBackupCompleted(req.user._id);

    res.json({
      success,
      message: success ? 'Backup marked as completed' : 'Failed to mark backup as completed'
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;