const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../src/server');
const User = require('../../src/models/User');

describe('API Endpoint Validation Tests', () => {
  let server;
  let authToken;
  let userId;

  beforeAll(async () => {
    // Connect to test database
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/kryptopesa_test';
    await mongoose.connect(mongoUri);
    
    // Start server
    server = app.listen(0);
  });

  afterAll(async () => {
    // Clean up
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    if (server) {
      server.close();
    }
  });

  beforeEach(async () => {
    // Clean collections and setup test user
    await User.deleteMany({});
    await setupTestUser();
  });

  describe('Authentication Endpoints', () => {
    describe('POST /api/auth/register', () => {
      test('Should validate required fields', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({})
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toBeDefined();
      });

      test('Should validate email format', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            username: 'testuser',
            email: 'invalid-email',
            password: 'password123',
            firstName: 'Test',
            lastName: 'User',
            phone: '+254700000001',
            country: 'KE',
            city: 'Nairobi'
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors.some(e => e.path === 'email')).toBe(true);
      });

      test('Should validate password length', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            username: 'testuser',
            email: '<EMAIL>',
            password: '123', // Too short
            firstName: 'Test',
            lastName: 'User',
            phone: '+254700000001',
            country: 'KE',
            city: 'Nairobi'
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors.some(e => e.path === 'password')).toBe(true);
      });

      test('Should validate country code', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'password123',
            firstName: 'Test',
            lastName: 'User',
            phone: '+254700000001',
            country: 'INVALID', // Invalid country
            city: 'Nairobi'
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors.some(e => e.path === 'country')).toBe(true);
      });
    });

    describe('POST /api/auth/login', () => {
      test('Should validate required fields', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({})
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toBeDefined();
      });

      test('Should return error for invalid credentials', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            identifier: '<EMAIL>',
            password: 'wrongpassword'
          })
          .expect(401);

        expect(response.body.success).toBe(false);
        expect(response.body.error.code).toBe('AUTH_001');
      });
    });
  });

  describe('Offer Endpoints', () => {
    describe('POST /api/offers', () => {
      test('Should require authentication', async () => {
        const response = await request(app)
          .post('/api/offers')
          .send({})
          .expect(401);

        expect(response.body.success).toBe(false);
        expect(response.body.error.code).toBe('AUTH_003');
      });

      test('Should validate offer data structure', async () => {
        const response = await request(app)
          .post('/api/offers')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            type: 'invalid_type', // Invalid type
            cryptocurrency: {},
            fiat: {}
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toBeDefined();
      });

      test('Should validate amount ranges', async () => {
        const response = await request(app)
          .post('/api/offers')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            type: 'sell',
            cryptocurrency: {
              symbol: 'USDT',
              network: 'TRC20',
              minAmount: '1000', // Min > Max
              maxAmount: '100',
              availableAmount: '100'
            },
            fiat: {
              currency: 'KES',
              priceType: 'fixed',
              fixedPrice: '150'
            },
            paymentMethods: [
              {
                method: 'mobile_money',
                details: {
                  provider: 'M-Pesa',
                  mobileNumber: '+254700000001'
                }
              }
            ]
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.message).toContain('Minimum amount must be less than maximum amount');
      });

      test('Should validate payment method details', async () => {
        const response = await request(app)
          .post('/api/offers')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            type: 'sell',
            cryptocurrency: {
              symbol: 'USDT',
              network: 'TRC20',
              minAmount: '10',
              maxAmount: '1000',
              availableAmount: '1000'
            },
            fiat: {
              currency: 'KES',
              priceType: 'fixed',
              fixedPrice: '150'
            },
            paymentMethods: [
              {
                method: 'mobile_money',
                details: {
                  provider: 'Invalid Provider', // Invalid provider
                  mobileNumber: '+254700000001'
                }
              }
            ]
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.message).toContain('Payment method validation failed');
      });
    });

    describe('GET /api/offers', () => {
      test('Should return paginated results', async () => {
        const response = await request(app)
          .get('/api/offers')
          .query({ limit: 10, page: 1 })
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.pagination).toBeDefined();
        expect(response.body.data.pagination.limit).toBe(10);
        expect(response.body.data.pagination.page).toBe(1);
      });

      test('Should validate query parameters', async () => {
        const response = await request(app)
          .get('/api/offers')
          .query({ 
            limit: 'invalid', // Invalid limit
            minAmount: 'not_a_number' // Invalid amount
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toBeDefined();
      });
    });
  });

  describe('Trade Endpoints', () => {
    describe('POST /api/trades', () => {
      test('Should require authentication', async () => {
        const response = await request(app)
          .post('/api/trades')
          .send({})
          .expect(401);

        expect(response.body.success).toBe(false);
        expect(response.body.error.code).toBe('AUTH_003');
      });

      test('Should validate trade amount', async () => {
        const response = await request(app)
          .post('/api/trades')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            offerId: 'invalid_offer_id',
            amount: 'invalid_amount', // Invalid amount
            paymentMethod: {
              method: 'mobile_money',
              details: {}
            }
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toBeDefined();
      });
    });

    describe('GET /api/trades', () => {
      test('Should require authentication', async () => {
        const response = await request(app)
          .get('/api/trades')
          .expect(401);

        expect(response.body.success).toBe(false);
        expect(response.body.error.code).toBe('AUTH_003');
      });

      test('Should return user trades only', async () => {
        const response = await request(app)
          .get('/api/trades')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.trades).toBeDefined();
        expect(Array.isArray(response.body.data.trades)).toBe(true);
      });
    });
  });

  describe('User Endpoints', () => {
    describe('GET /api/users/profile', () => {
      test('Should require authentication', async () => {
        const response = await request(app)
          .get('/api/users/profile')
          .expect(401);

        expect(response.body.success).toBe(false);
        expect(response.body.error.code).toBe('AUTH_003');
      });

      test('Should return user profile', async () => {
        const response = await request(app)
          .get('/api/users/profile')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.user).toBeDefined();
        expect(response.body.data.user.email).toBe('<EMAIL>');
      });
    });

    describe('PUT /api/users/profile', () => {
      test('Should validate profile update data', async () => {
        const response = await request(app)
          .put('/api/users/profile')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            email: 'invalid-email', // Invalid email
            firstName: '', // Empty required field
            country: 'INVALID' // Invalid country
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toBeDefined();
      });
    });
  });

  describe('File Upload Endpoints', () => {
    describe('POST /api/users/upload/payment-proof', () => {
      test('Should require authentication', async () => {
        const response = await request(app)
          .post('/api/users/upload/payment-proof')
          .expect(401);

        expect(response.body.success).toBe(false);
        expect(response.body.error.code).toBe('AUTH_003');
      });

      test('Should validate trade ID', async () => {
        const response = await request(app)
          .post('/api/users/upload/payment-proof')
          .set('Authorization', `Bearer ${authToken}`)
          .send({}) // Missing tradeId
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.message).toContain('Trade ID is required');
      });
    });
  });

  describe('Rate Limiting', () => {
    test('Should enforce rate limits on auth endpoints', async () => {
      // Make multiple rapid requests to trigger rate limiting
      const promises = Array(20).fill().map(() => 
        request(app)
          .post('/api/auth/login')
          .send({
            identifier: '<EMAIL>',
            password: 'wrongpassword'
          })
      );

      const responses = await Promise.all(promises);
      
      // At least one should be rate limited
      const rateLimited = responses.some(res => res.status === 429);
      expect(rateLimited).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('Should return structured error responses', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
      expect(response.body.error.code).toBeDefined();
      expect(response.body.error.message).toBeDefined();
      expect(response.body.error.timestamp).toBeDefined();
    });

    test('Should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  // Helper function to setup test user
  async function setupTestUser() {
    // Register test user
    const registerResponse = await request(app)
      .post('/api/auth/register')
      .send({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        phone: '+254700000001',
        country: 'KE',
        city: 'Nairobi'
      });

    userId = registerResponse.body.data.user._id;

    // Login to get token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        identifier: '<EMAIL>',
        password: 'password123'
      });

    authToken = loginResponse.body.data.token;
  }
});
