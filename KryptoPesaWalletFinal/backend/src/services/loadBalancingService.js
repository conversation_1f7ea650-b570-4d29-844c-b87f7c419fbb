/**
 * Load Balancing Service for 50K+ Daily Users
 * Prepares the application for horizontal scaling and load distribution
 */

const os = require('os');
const cluster = require('cluster');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

class LoadBalancingService {
  constructor() {
    this.instanceId = process.env.INSTANCE_ID || `instance-${Date.now()}`;
    this.redisClient = getRedisClient();
    this.healthMetrics = {
      cpuUsage: 0,
      memoryUsage: 0,
      activeConnections: 0,
      requestsPerSecond: 0,
      responseTime: 0,
      errorRate: 0,
      lastHealthCheck: new Date()
    };
    
    this.requestCounter = 0;
    this.errorCounter = 0;
    this.responseTimes = [];
    
    this.initializeLoadBalancing();
  }

  /**
   * Initialize load balancing features
   */
  initializeLoadBalancing() {
    // Register this instance
    this.registerInstance();
    
    // Start health monitoring
    this.startHealthMonitoring();
    
    // Setup graceful shutdown
    this.setupGracefulShutdown();
    
    // Initialize session affinity if needed
    this.initializeSessionAffinity();
    
    logger.info(`Load balancing service initialized for instance: ${this.instanceId}`);
  }

  /**
   * Register this instance with the load balancer
   */
  async registerInstance() {
    try {
      const instanceInfo = {
        id: this.instanceId,
        host: os.hostname(),
        port: process.env.PORT || 3000,
        pid: process.pid,
        startTime: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        nodeVersion: process.version,
        platform: os.platform(),
        arch: os.arch(),
        cpus: os.cpus().length,
        memory: os.totalmem()
      };

      // Register in Redis for service discovery
      await this.redisClient.hset(
        'load_balancer:instances',
        this.instanceId,
        JSON.stringify(instanceInfo)
      );

      // Set TTL for automatic cleanup if instance goes down
      await this.redisClient.expire(`load_balancer:instances:${this.instanceId}`, 60);

      logger.info('Instance registered with load balancer', instanceInfo);
    } catch (error) {
      logger.error('Error registering instance:', error);
    }
  }

  /**
   * Start health monitoring for load balancer
   */
  startHealthMonitoring() {
    // Update health metrics every 10 seconds
    setInterval(() => {
      this.updateHealthMetrics();
      this.reportHealthToLoadBalancer();
    }, 10000);

    // Reset counters every minute
    setInterval(() => {
      this.resetCounters();
    }, 60000);
  }

  /**
   * Update health metrics
   */
  updateHealthMetrics() {
    // CPU usage
    const cpuUsage = process.cpuUsage();
    this.healthMetrics.cpuUsage = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds

    // Memory usage
    const memUsage = process.memoryUsage();
    this.healthMetrics.memoryUsage = (memUsage.heapUsed / memUsage.heapTotal) * 100;

    // Calculate requests per second
    this.healthMetrics.requestsPerSecond = this.requestCounter;

    // Calculate average response time
    if (this.responseTimes.length > 0) {
      this.healthMetrics.responseTime = this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length;
    }

    // Calculate error rate
    this.healthMetrics.errorRate = this.requestCounter > 0 ? 
      (this.errorCounter / this.requestCounter) * 100 : 0;

    this.healthMetrics.lastHealthCheck = new Date();
  }

  /**
   * Report health status to load balancer
   */
  async reportHealthToLoadBalancer() {
    try {
      const healthReport = {
        instanceId: this.instanceId,
        timestamp: new Date().toISOString(),
        metrics: this.healthMetrics,
        status: this.calculateHealthStatus(),
        load: this.calculateLoadScore()
      };

      // Store in Redis for load balancer to read
      await this.redisClient.setex(
        `load_balancer:health:${this.instanceId}`,
        30, // 30 seconds TTL
        JSON.stringify(healthReport)
      );

      // Also store in time series for monitoring
      await this.redisClient.lpush(
        `load_balancer:metrics:${this.instanceId}`,
        JSON.stringify(healthReport)
      );

      // Keep only last 100 entries
      await this.redisClient.ltrim(`load_balancer:metrics:${this.instanceId}`, 0, 99);

    } catch (error) {
      logger.error('Error reporting health to load balancer:', error);
    }
  }

  /**
   * Calculate overall health status
   */
  calculateHealthStatus() {
    const { cpuUsage, memoryUsage, errorRate, responseTime } = this.healthMetrics;

    // Define thresholds
    const thresholds = {
      cpu: { warning: 70, critical: 90 },
      memory: { warning: 80, critical: 95 },
      errorRate: { warning: 5, critical: 10 },
      responseTime: { warning: 1000, critical: 3000 }
    };

    // Check critical conditions
    if (cpuUsage > thresholds.cpu.critical ||
        memoryUsage > thresholds.memory.critical ||
        errorRate > thresholds.errorRate.critical ||
        responseTime > thresholds.responseTime.critical) {
      return 'critical';
    }

    // Check warning conditions
    if (cpuUsage > thresholds.cpu.warning ||
        memoryUsage > thresholds.memory.warning ||
        errorRate > thresholds.errorRate.warning ||
        responseTime > thresholds.responseTime.warning) {
      return 'warning';
    }

    return 'healthy';
  }

  /**
   * Calculate load score for load balancing decisions
   */
  calculateLoadScore() {
    const { cpuUsage, memoryUsage, activeConnections, responseTime } = this.healthMetrics;
    
    // Weighted load score (0-100, lower is better)
    const cpuScore = Math.min(cpuUsage, 100);
    const memoryScore = memoryUsage;
    const connectionScore = Math.min((activeConnections / 1000) * 100, 100);
    const responseScore = Math.min((responseTime / 1000) * 10, 100);

    return (cpuScore * 0.3 + memoryScore * 0.3 + connectionScore * 0.2 + responseScore * 0.2);
  }

  /**
   * Middleware to track requests for load balancing
   */
  trackRequest() {
    return (req, res, next) => {
      const startTime = Date.now();
      this.requestCounter++;

      // Track response time
      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        this.responseTimes.push(responseTime);
        
        // Keep only last 100 response times
        if (this.responseTimes.length > 100) {
          this.responseTimes.shift();
        }

        // Track errors
        if (res.statusCode >= 400) {
          this.errorCounter++;
        }
      });

      next();
    };
  }

  /**
   * Health check endpoint for load balancer
   */
  getHealthCheckHandler() {
    return (req, res) => {
      const status = this.calculateHealthStatus();
      const statusCode = status === 'healthy' ? 200 : status === 'warning' ? 200 : 503;

      res.status(statusCode).json({
        status,
        instanceId: this.instanceId,
        timestamp: new Date().toISOString(),
        metrics: this.healthMetrics,
        load: this.calculateLoadScore(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0'
      });
    };
  }

  /**
   * Initialize session affinity for sticky sessions
   */
  initializeSessionAffinity() {
    // This would be used if sticky sessions are needed
    // For stateless applications, this might not be necessary
    logger.info('Session affinity initialized (stateless mode)');
  }

  /**
   * Setup graceful shutdown for load balancer
   */
  setupGracefulShutdown() {
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);

      try {
        // Mark instance as shutting down
        await this.markInstanceShuttingDown();

        // Wait for existing connections to finish
        await this.waitForConnectionsDrain();

        // Cleanup resources
        await this.cleanup();

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }

  /**
   * Mark instance as shutting down
   */
  async markInstanceShuttingDown() {
    try {
      await this.redisClient.hset(
        'load_balancer:instances',
        this.instanceId,
        JSON.stringify({ status: 'shutting_down', timestamp: new Date().toISOString() })
      );
      
      logger.info('Instance marked as shutting down');
    } catch (error) {
      logger.error('Error marking instance as shutting down:', error);
    }
  }

  /**
   * Wait for connections to drain
   */
  async waitForConnectionsDrain(maxWaitTime = 30000) {
    const startTime = Date.now();
    
    return new Promise((resolve) => {
      const checkConnections = () => {
        const elapsed = Date.now() - startTime;
        
        if (this.healthMetrics.activeConnections === 0 || elapsed >= maxWaitTime) {
          resolve();
        } else {
          setTimeout(checkConnections, 1000);
        }
      };
      
      checkConnections();
    });
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      // Remove instance from load balancer registry
      await this.redisClient.hdel('load_balancer:instances', this.instanceId);
      await this.redisClient.del(`load_balancer:health:${this.instanceId}`);
      await this.redisClient.del(`load_balancer:metrics:${this.instanceId}`);
      
      logger.info('Load balancer cleanup completed');
    } catch (error) {
      logger.error('Error during cleanup:', error);
    }
  }

  /**
   * Reset counters for next measurement period
   */
  resetCounters() {
    this.requestCounter = 0;
    this.errorCounter = 0;
    this.responseTimes = [];
  }

  /**
   * Get load balancing status
   */
  getLoadBalancingStatus() {
    return {
      instanceId: this.instanceId,
      health: this.calculateHealthStatus(),
      load: this.calculateLoadScore(),
      metrics: this.healthMetrics,
      uptime: process.uptime(),
      pid: process.pid
    };
  }

  /**
   * Get all registered instances (for monitoring)
   */
  async getAllInstances() {
    try {
      const instances = await this.redisClient.hgetall('load_balancer:instances');
      const result = {};
      
      for (const [id, data] of Object.entries(instances)) {
        result[id] = JSON.parse(data);
      }
      
      return result;
    } catch (error) {
      logger.error('Error getting all instances:', error);
      return {};
    }
  }
}

module.exports = new LoadBalancingService();
