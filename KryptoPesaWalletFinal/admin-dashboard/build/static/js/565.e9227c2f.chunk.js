"use strict";(self.webpackChunkkryptopesa_admin_dashboard=self.webpackChunkkryptopesa_admin_dashboard||[]).push([[565],{2796:(e,t,s)=>{s.d(t,{A:()=>n});var i=s(9662),o=s(579);const n=(0,i.A)((0,o.jsx)("path",{d:"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"}),"TrendingUp")},3565:(e,t,s)=>{s.r(t),s.d(t,{default:()=>C});var i=s(2555),o=s(5043),n=s(2110),l=s(6494),r=s(6446),c=s(5865),d=s(1637),a=s(8903),u=s(3336),h=s(9650),v=s(1806),x=s(4882),m=s(8076),A=s(39),j=s(3460),p=s(3845),y=s(2796),g=s(4389),f=s(5155),b=s(1973),k=s(6201),w=s(7111);const S=s(9722).A.create({baseURL:"https://api.kryptopesa.com/api",timeout:1e4,headers:{"Content-Type":"application/json"}});S.interceptors.request.use(e=>{const t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),S.interceptors.response.use(e=>e,e=>{var t;return 401===(null===(t=e.response)||void 0===t?void 0:t.status)&&(localStorage.removeItem("admin_token"),window.location.href="/login"),Promise.reject(e)});const T={getStats:()=>S.get("/admin/dashboard/stats"),getRealtimeMetrics:()=>S.get("/metrics/realtime"),getApplicationMetrics:()=>S.get("/metrics/application")};const _=new class{constructor(){this.socket=null,this.listeners=new Map,this.reconnectAttempts=0,this.maxReconnectAttempts=5}connect(){const e=localStorage.getItem("admin_token");if(!e)return void console.error("No admin token available for WebSocket connection");try{this.socket=new WebSocket("".concat("wss://api.kryptopesa.com","?token=").concat(e)),this.socket.onopen=()=>{console.log("Admin WebSocket connected"),this.reconnectAttempts=0,this.emit("connected")},this.socket.onmessage=e=>{try{const t=JSON.parse(e.data);this.emit(t.type,t.payload)}catch(t){console.error("Error parsing WebSocket message:",t)}},this.socket.onclose=()=>{console.log("Admin WebSocket disconnected"),this.emit("disconnected"),this.attemptReconnect()},this.socket.onerror=e=>{console.error("Admin WebSocket error:",e),this.emit("error",e)}}catch(t){console.error("Error creating WebSocket connection:",t)}}disconnect(){this.socket&&(this.socket.close(),this.socket=null)}attemptReconnect(){if(this.reconnectAttempts<this.maxReconnectAttempts){this.reconnectAttempts++;const e=1e3*Math.pow(2,this.reconnectAttempts);setTimeout(()=>{console.log("Attempting to reconnect WebSocket (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")")),this.connect()},e)}}on(e,t){this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t)}off(e,t){if(this.listeners.has(e)){const s=this.listeners.get(e),i=s.indexOf(t);i>-1&&s.splice(i,1)}}emit(e,t){this.listeners.has(e)&&this.listeners.get(e).forEach(e=>{try{e(t)}catch(s){console.error("Error in WebSocket event callback:",s)}})}send(e){this.socket&&this.socket.readyState===WebSocket.OPEN?this.socket.send(JSON.stringify(e)):console.warn("WebSocket not connected, cannot send data")}};var I=s(579);const D=o.memo(e=>{let{title:t,value:s,icon:i,color:o="primary",growth:d}=e;return(0,I.jsx)(n.A,{children:(0,I.jsx)(l.A,{children:(0,I.jsxs)(r.A,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[(0,I.jsxs)(r.A,{children:[(0,I.jsx)(c.A,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:t}),(0,I.jsx)(c.A,{variant:"h4",component:"div",children:s}),void 0!==d&&(0,I.jsxs)(r.A,{display:"flex",alignItems:"center",mt:1,children:[(0,I.jsxs)(r.A,{display:"flex",alignItems:"center",children:[d>0&&(0,I.jsx)(y.A,{color:"success",fontSize:"small"}),d<0&&(0,I.jsx)(g.A,{color:"error",fontSize:"small"}),(0,I.jsxs)(c.A,{variant:"body2",sx:{ml:.5,color:d>0?"success.main":d<0?"error.main":"text.secondary"},children:[d>0?"+":"",d,"%"]})]}),(0,I.jsx)(c.A,{variant:"body2",color:"textSecondary",sx:{ml:1},children:"vs last week"})]})]}),(0,I.jsx)(r.A,{sx:{backgroundColor:"".concat(o,".light"),borderRadius:"50%",p:1,display:"flex",alignItems:"center",justifyContent:"center"},children:i})]})})})}),C=()=>{var e,t,s,n,l,g,S,C,R,W,O,z,B,L,N,U,E,F;const[M,V]=(0,o.useState)(null),[P,J]=(0,o.useState)(!0),[Q,q]=(0,o.useState)(null),G=(0,o.useCallback)(async()=>{try{J(!0);const e=await T.getStats();e.data.success&&V(e.data.data)}catch(Q){0,q("Failed to fetch dashboard statistics")}finally{J(!1)}},[]);(0,o.useEffect)(()=>{G();const e=e=>{V(t=>(0,i.A)((0,i.A)({},t),e))};_.on("dashboard_stats_update",e),_.on("trade_update",G),_.on("user_update",G);const t=setInterval(G,3e4);return()=>{clearInterval(t),_.off("dashboard_stats_update",e),_.off("trade_update",G),_.off("user_update",G)}},[G]);const H=(0,o.useCallback)(e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):(null===e||void 0===e?void 0:e.toLocaleString())||"0",[]),K=(0,o.useCallback)(e=>"$".concat(H(e)),[H]),$=(0,o.useMemo)(()=>{var e,t,s,i,o,n,l,r,c,d,a,u,h,v,x,m;return M?{totalUsers:H(null===M||void 0===M||null===(e=M.statistics)||void 0===e||null===(t=e.users)||void 0===t?void 0:t.total),activeTrades:H(null===M||void 0===M||null===(s=M.statistics)||void 0===s||null===(i=s.trades)||void 0===i?void 0:i.active),pendingDisputes:H(null===M||void 0===M||null===(o=M.statistics)||void 0===o||null===(n=o.disputes)||void 0===n?void 0:n.pending),totalVolume:K(null===M||void 0===M||null===(l=M.statistics)||void 0===l||null===(r=l.trades)||void 0===r?void 0:r.volume),totalOffers:H(null===M||void 0===M||null===(c=M.statistics)||void 0===c||null===(d=c.offers)||void 0===d?void 0:d.total),completedTrades:H(null===M||void 0===M||null===(a=M.statistics)||void 0===a||null===(u=a.trades)||void 0===u?void 0:u.completed),weeklyNewUsers:H(null===M||void 0===M||null===(h=M.statistics)||void 0===h||null===(v=h.users)||void 0===v?void 0:v.weeklyNew),activeOffers:H(null===M||void 0===M||null===(x=M.statistics)||void 0===x||null===(m=x.offers)||void 0===m?void 0:m.active)}:null},[M,H,K]);return P?(0,I.jsx)(r.A,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",children:(0,I.jsx)(d.A,{})}):Q?(0,I.jsxs)(r.A,{children:[(0,I.jsx)(c.A,{variant:"h4",gutterBottom:!0,children:"Dashboard Overview"}),(0,I.jsx)(c.A,{color:"error",children:Q})]}):(0,I.jsxs)(r.A,{children:[(0,I.jsx)(c.A,{variant:"h4",gutterBottom:!0,children:"Dashboard Overview"}),(0,I.jsxs)(a.Ay,{container:!0,spacing:3,children:[(0,I.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,I.jsx)(D,{title:"Total Users",value:null===$||void 0===$?void 0:$.totalUsers,growth:null===M||void 0===M||null===(e=M.statistics)||void 0===e||null===(t=e.users)||void 0===t?void 0:t.growth,icon:(0,I.jsx)(f.A,{}),color:"primary"})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,I.jsx)(D,{title:"Active Trades",value:null===$||void 0===$?void 0:$.activeTrades,growth:null===M||void 0===M||null===(s=M.statistics)||void 0===s||null===(n=s.trades)||void 0===n?void 0:n.growth,icon:(0,I.jsx)(b.A,{}),color:"success"})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,I.jsx)(D,{title:"Pending Disputes",value:null===$||void 0===$?void 0:$.pendingDisputes,icon:(0,I.jsx)(k.A,{}),color:"warning"})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,I.jsx)(D,{title:"Total Volume",value:null===$||void 0===$?void 0:$.totalVolume,growth:null===M||void 0===M||null===(l=M.statistics)||void 0===l||null===(g=l.trades)||void 0===g?void 0:g.volumeGrowth,icon:(0,I.jsx)(y.A,{}),color:"info"})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,I.jsx)(D,{title:"Total Offers",value:H(null===M||void 0===M||null===(S=M.statistics)||void 0===S||null===(C=S.offers)||void 0===C?void 0:C.total),icon:(0,I.jsx)(w.A,{}),color:"secondary"})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,I.jsx)(D,{title:"Completed Trades",value:H(null===M||void 0===M||null===(R=M.statistics)||void 0===R||null===(W=R.trades)||void 0===W?void 0:W.completed),icon:(0,I.jsx)(b.A,{}),color:"success"})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,I.jsx)(D,{title:"New Users (Week)",value:H(null===M||void 0===M||null===(O=M.statistics)||void 0===O||null===(z=O.users)||void 0===z?void 0:z.weeklyNew),icon:(0,I.jsx)(f.A,{}),color:"primary"})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,I.jsx)(D,{title:"Active Offers",value:H(null===M||void 0===M||null===(B=M.statistics)||void 0===B||null===(L=B.offers)||void 0===L?void 0:L.active),icon:(0,I.jsx)(w.A,{}),color:"secondary"})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,md:8,children:(0,I.jsxs)(u.A,{sx:{p:2},children:[(0,I.jsx)(c.A,{variant:"h6",gutterBottom:!0,children:"Recent Activity"}),(0,I.jsx)(c.A,{color:"textSecondary",children:"Recent activity data will be displayed here..."})]})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,md:4,children:(0,I.jsxs)(u.A,{sx:{p:2},children:[(0,I.jsx)(c.A,{variant:"h6",gutterBottom:!0,children:"Quick Actions"}),(0,I.jsx)(c.A,{color:"textSecondary",children:"Quick action buttons will be displayed here..."})]})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,children:(0,I.jsxs)(u.A,{sx:{p:2},children:[(0,I.jsx)(c.A,{variant:"h6",gutterBottom:!0,children:"Trading Volume Chart"}),(0,I.jsx)(c.A,{color:"textSecondary",children:"Trading volume chart will be displayed here..."})]})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,lg:6,children:(0,I.jsxs)(u.A,{sx:{p:2},children:[(0,I.jsx)(c.A,{variant:"h6",gutterBottom:!0,children:"Recent Trades"}),(0,I.jsx)(h.A,{children:(0,I.jsxs)(v.A,{size:"small",children:[(0,I.jsx)(x.A,{children:(0,I.jsxs)(m.A,{children:[(0,I.jsx)(A.A,{children:"Trade ID"}),(0,I.jsx)(A.A,{children:"Amount"}),(0,I.jsx)(A.A,{children:"Status"}),(0,I.jsx)(A.A,{children:"Time"})]})}),(0,I.jsx)(j.A,{children:null===M||void 0===M||null===(N=M.recentActivity)||void 0===N||null===(U=N.trades)||void 0===U?void 0:U.slice(0,5).map(e=>{var t,s,i;return(0,I.jsxs)(m.A,{children:[(0,I.jsx)(A.A,{children:(0,I.jsxs)(c.A,{variant:"body2",fontFamily:"monospace",children:[null===(t=e.tradeId)||void 0===t?void 0:t.substring(0,8),"..."]})}),(0,I.jsx)(A.A,{children:(0,I.jsxs)(c.A,{variant:"body2",children:[null===(s=e.cryptocurrency)||void 0===s?void 0:s.amount," ",null===(i=e.cryptocurrency)||void 0===i?void 0:i.symbol]})}),(0,I.jsx)(A.A,{children:(0,I.jsx)(p.A,{label:e.status,size:"small",color:"completed"===e.status?"success":"disputed"===e.status?"warning":"cancelled"===e.status?"error":"default"})}),(0,I.jsx)(A.A,{children:(0,I.jsx)(c.A,{variant:"body2",children:new Date(e.createdAt).toLocaleDateString()})})]},e._id)})})]})})]})}),(0,I.jsx)(a.Ay,{item:!0,xs:12,lg:6,children:(0,I.jsxs)(u.A,{sx:{p:2},children:[(0,I.jsx)(c.A,{variant:"h6",gutterBottom:!0,children:"Recent Disputes"}),(0,I.jsx)(h.A,{children:(0,I.jsxs)(v.A,{size:"small",children:[(0,I.jsx)(x.A,{children:(0,I.jsxs)(m.A,{children:[(0,I.jsx)(A.A,{children:"Dispute ID"}),(0,I.jsx)(A.A,{children:"Trade"}),(0,I.jsx)(A.A,{children:"Status"}),(0,I.jsx)(A.A,{children:"Time"})]})}),(0,I.jsx)(j.A,{children:null===M||void 0===M||null===(E=M.recentActivity)||void 0===E||null===(F=E.disputes)||void 0===F?void 0:F.slice(0,5).map(e=>{var t,s,i;return(0,I.jsxs)(m.A,{children:[(0,I.jsx)(A.A,{children:(0,I.jsxs)(c.A,{variant:"body2",fontFamily:"monospace",children:[null===(t=e.disputeId)||void 0===t?void 0:t.substring(0,8),"..."]})}),(0,I.jsx)(A.A,{children:(0,I.jsxs)(c.A,{variant:"body2",fontFamily:"monospace",children:[null===(s=e.trade)||void 0===s||null===(i=s.tradeId)||void 0===i?void 0:i.substring(0,8),"..."]})}),(0,I.jsx)(A.A,{children:(0,I.jsx)(p.A,{label:e.status,size:"small",color:"resolved"===e.status?"success":"pending"===e.status?"warning":"default"})}),(0,I.jsx)(A.A,{children:(0,I.jsx)(c.A,{variant:"body2",children:new Date(e.createdAt).toLocaleDateString()})})]},e._id)})})]})})]})})]})]})}},4389:(e,t,s)=>{s.d(t,{A:()=>n});var i=s(9662),o=s(579);const n=(0,i.A)((0,o.jsx)("path",{d:"m16 18 2.29-2.29-4.88-4.88-4 4L2 7.41 3.41 6l6 6 4-4 6.3 6.29L22 12v6z"}),"TrendingDown")}}]);
//# sourceMappingURL=565.e9227c2f.chunk.js.map