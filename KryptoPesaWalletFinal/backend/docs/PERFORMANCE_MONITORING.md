# Performance Monitoring System

## Overview

KryptoPesa's Performance Monitoring System provides comprehensive real-time monitoring of application performance with automated alerting for slow requests and high error rates. The system is designed for enterprise-grade production environments supporting 50,000+ daily users.

## Architecture

### Core Components

1. **Performance Monitoring Service** (`src/services/performanceMonitoringService.js`)
   - Centralized performance metrics collection and analysis
   - Real-time alerting for performance thresholds
   - Integration with business flow monitoring

2. **Performance Monitoring Middleware** (`src/middleware/performanceMonitoring.js`)
   - Request/response time tracking
   - Error recording and classification
   - Automatic integration with all API endpoints

3. **Enhanced Metrics Endpoints** (`src/routes/metrics.js`)
   - RESTful APIs for metrics retrieval
   - Performance alerts endpoint
   - Prometheus-compatible metrics export

## Key Features

### 🚀 Real-Time Performance Tracking
- **Response Time Monitoring**: Average, P50, P95, P99 percentiles
- **Request Volume Tracking**: Total, successful, failed requests by method/route
- **Error Rate Analysis**: Real-time error rate calculation with trend analysis
- **System Resource Monitoring**: Memory usage, CPU utilization, active connections

### 🔔 Intelligent Alerting
- **Slow Request Detection**: Configurable thresholds for response time alerts
- **High Error Rate Alerts**: Automatic detection of error rate spikes
- **Memory Usage Warnings**: Proactive memory exhaustion prevention
- **Critical Error Identification**: Special handling for security and payment errors

### 📊 Comprehensive Metrics
- **Performance Percentiles**: P50, P95, P99 response time analysis
- **Cache Performance**: Hit/miss rates and optimization recommendations
- **Database Metrics**: Query performance and connection pool monitoring
- **Business Impact Analysis**: Performance correlation with business flows

## Configuration

### Alert Thresholds

```javascript
alertThresholds: {
  responseTime: {
    average: 1000,      // 1 second average
    p95: 2000,          // 2 seconds 95th percentile
    p99: 5000,          // 5 seconds 99th percentile
    critical: 10000     // 10 seconds critical
  },
  errorRate: {
    warning: 0.02,      // 2% warning
    critical: 0.05,     // 5% critical
    severe: 0.10        // 10% severe
  },
  memory: {
    warning: 0.80,      // 80% of heap limit
    critical: 0.90,     // 90% of heap limit
    severe: 0.95        // 95% of heap limit
  }
}
```

### Monitoring Intervals

```javascript
monitoringIntervals: {
  responseTime: 30000,    // 30 seconds
  errorRate: 60000,       // 1 minute
  systemMetrics: 30000,   // 30 seconds
  databaseMetrics: 60000, // 1 minute
  cacheMetrics: 30000,    // 30 seconds
  alertCheck: 60000       // 1 minute
}
```

## API Endpoints

### Performance Metrics
```
GET /api/metrics/performance
```
Returns comprehensive performance metrics including response times, error rates, and system resources.

### Performance Alerts
```
GET /api/metrics/performance/alerts
```
Returns current performance alerts with severity levels and recommendations.

### Real-Time Monitoring
```
GET /api/monitoring/performance
```
Live performance metrics for dashboard integration.

### Prometheus Metrics
```
GET /metrics/prometheus
```
Prometheus-compatible metrics for external monitoring systems.

## Integration

### Automatic Request Tracking
All API requests are automatically tracked through the performance monitoring middleware:

```javascript
// Automatic integration - no code changes required
app.use(performanceMonitoring());
```

### Manual Event Recording
```javascript
// Record custom performance events
performanceMonitoringService.recordRequest('POST', '/api/trades', 201, 250);

// Record errors with context
performanceMonitoringService.recordError(error, 'POST', '/api/payments');

// Update cache metrics
performanceMonitoringService.recordCacheHit();
performanceMonitoringService.recordCacheMiss();
```

## Alert Types

### Response Time Alerts
- **HIGH_AVERAGE_RESPONSE_TIME**: Average response time exceeds threshold
- **HIGH_P95_RESPONSE_TIME**: 95th percentile response time exceeds threshold
- **HIGH_P99_RESPONSE_TIME**: 99th percentile response time exceeds threshold

### Error Rate Alerts
- **HIGH_ERROR_RATE**: Error rate exceeds warning threshold (2%)
- **CRITICAL_ERROR_RATE**: Error rate exceeds critical threshold (5%)
- **SEVERE_ERROR_RATE**: Error rate exceeds severe threshold (10%)
- **CRITICAL_ERRORS_DETECTED**: Critical errors (security, payment) detected

### System Resource Alerts
- **HIGH_MEMORY_USAGE**: Memory usage exceeds warning threshold (80%)
- **CRITICAL_MEMORY_USAGE**: Memory usage exceeds critical threshold (90%)
- **SEVERE_MEMORY_USAGE**: Memory usage exceeds severe threshold (95%)

### Cache Performance Alerts
- **LOW_CACHE_HIT_RATE**: Cache hit rate below warning threshold (70%)
- **CRITICAL_CACHE_HIT_RATE**: Cache hit rate below critical threshold (50%)

## Production Deployment

### Environment Variables
```bash
# Performance monitoring configuration
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_ALERT_COOLDOWN=300000  # 5 minutes
PERFORMANCE_SAMPLE_WINDOW=1000     # Keep last 1000 samples
```

### Monitoring Integration
The system integrates with:
- **Prometheus**: Metrics scraping and long-term storage
- **Grafana**: Performance dashboards and visualization
- **Discord/Slack**: Real-time alert notifications
- **Email**: Critical alert notifications

### Health Checks
```bash
# Check performance monitoring status
curl http://localhost:3001/api/monitoring/performance

# Check current alerts
curl http://localhost:3001/api/metrics/performance/alerts
```

## Testing

### Unit Tests
```bash
npm test tests/services/performanceMonitoringService.test.js
```

### Load Testing Integration
The performance monitoring system automatically tracks metrics during load testing:

```bash
# Run load tests with performance monitoring
npm run test:load
```

## Troubleshooting

### Common Issues

1. **High Memory Usage Alerts**
   - Check for memory leaks in application code
   - Review cache size and eviction policies
   - Monitor garbage collection patterns

2. **Slow Response Time Alerts**
   - Analyze database query performance
   - Check external API response times
   - Review cache hit rates

3. **High Error Rate Alerts**
   - Check application logs for error patterns
   - Verify database connectivity
   - Review recent deployments

### Performance Optimization

1. **Response Time Optimization**
   - Implement database query optimization
   - Add caching for frequently accessed data
   - Use connection pooling for external services

2. **Error Rate Reduction**
   - Implement circuit breakers for external services
   - Add retry logic with exponential backoff
   - Improve input validation and error handling

## Metrics Dashboard

The performance monitoring system provides data for comprehensive dashboards showing:

- **Real-time Performance**: Live response times and error rates
- **Historical Trends**: Performance trends over time
- **Alert History**: Past alerts and resolution times
- **System Health**: Overall system performance score
- **Business Impact**: Performance correlation with business metrics

## Security Considerations

- **Sensitive Data**: Performance logs exclude sensitive user data
- **Access Control**: Metrics endpoints require admin authentication
- **Alert Security**: Alerts include security-relevant performance issues
- **Audit Trail**: All performance events are logged for compliance

This performance monitoring system ensures KryptoPesa maintains enterprise-grade performance standards while providing early warning of potential issues before they impact users.
