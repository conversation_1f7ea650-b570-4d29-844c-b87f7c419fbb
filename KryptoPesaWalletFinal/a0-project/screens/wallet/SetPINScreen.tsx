import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Vibration,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import * as LocalAuthentication from 'expo-local-authentication';

interface RouteParams {
  mnemonic: string[];
}

export default function SetPINScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { mnemonic } = route.params as RouteParams;
  
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [isConfirming, setIsConfirming] = useState(false);
  const [biometricsAvailable, setBiometricsAvailable] = useState(false);
  const [enableBiometrics, setEnableBiometrics] = useState(false);

  React.useEffect(() => {
    checkBiometrics();
  }, []);

  const checkBiometrics = async () => {
    const hasHardware = await LocalAuthentication.hasHardwareAsync();
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    setBiometricsAvailable(hasHardware && isEnrolled);
  };

  const handleNumberPress = (number: string) => {
    if (isConfirming) {
      if (confirmPin.length < 6) {
        setConfirmPin(confirmPin + number);
      }
    } else {
      if (pin.length < 6) {
        setPin(pin + number);
      }
    }
  };

  const handleBackspace = () => {
    if (isConfirming) {
      setConfirmPin(confirmPin.slice(0, -1));
    } else {
      setPin(pin.slice(0, -1));
    }
  };

  const handlePinComplete = () => {
    if (!isConfirming) {
      if (pin.length === 6) {
        setIsConfirming(true);
      }
    } else {
      if (confirmPin.length === 6) {
        if (pin === confirmPin) {
          handleWalletSetupComplete();
        } else {
          Vibration.vibrate(500);
          Alert.alert(
            'PIN Mismatch',
            'The PINs you entered don\'t match. Please try again.',
            [{ text: 'OK', onPress: resetPins }]
          );
        }
      }
    }
  };

  const resetPins = () => {
    setPin('');
    setConfirmPin('');
    setIsConfirming(false);
  };

  const handleWalletSetupComplete = async () => {
    try {
      // Here you would save the wallet data securely
      // For now, we'll just navigate to the main app
      
      Alert.alert(
        'Wallet Created Successfully!',
        'Your KryptoPesa wallet has been created. You can now start trading cryptocurrencies.',
        [
          {
            text: 'Start Trading',
            onPress: () => navigation.navigate('Dashboard')
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create wallet. Please try again.');
    }
  };

  React.useEffect(() => {
    if ((isConfirming && confirmPin.length === 6) || (!isConfirming && pin.length === 6)) {
      handlePinComplete();
    }
  }, [pin, confirmPin]);

  const currentPin = isConfirming ? confirmPin : pin;
  const styles = createStyles(theme);

  const renderPinDots = () => {
    return (
      <View style={styles.pinDotsContainer}>
        {[...Array(6)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              index < currentPin.length && styles.filledPinDot
            ]}
          />
        ))}
      </View>
    );
  };

  const renderNumberPad = () => {
    const numbers = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['', '0', 'backspace']
    ];

    return (
      <View style={styles.numberPad}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numberRow}>
            {row.map((item, itemIndex) => {
              if (item === '') {
                return <View key={itemIndex} style={styles.numberButton} />;
              }
              
              if (item === 'backspace') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={styles.numberButton}
                    onPress={handleBackspace}
                  >
                    <Ionicons name="backspace" size={24} color={theme.colors.text} />
                  </TouchableOpacity>
                );
              }

              return (
                <TouchableOpacity
                  key={itemIndex}
                  style={styles.numberButton}
                  onPress={() => handleNumberPress(item)}
                >
                  <Text style={styles.numberText}>{item}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Set PIN</Text>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.content}>
        <View style={styles.stepIndicator}>
          <View style={[styles.step, styles.completedStep]}>
            <Ionicons name="checkmark" size={16} color="white" />
          </View>
          <View style={[styles.stepLine, styles.completedLine]} />
          <View style={[styles.step, styles.completedStep]}>
            <Ionicons name="checkmark" size={16} color="white" />
          </View>
          <View style={[styles.stepLine, styles.completedLine]} />
          <View style={[styles.step, styles.activeStep]}>
            <Text style={styles.stepNumber}>3</Text>
          </View>
        </View>

        <View style={styles.titleContainer}>
          <Ionicons name="lock-closed" size={48} color={theme.colors.primary} />
          <Text style={styles.title}>
            {isConfirming ? 'Confirm Your PIN' : 'Create a PIN'}
          </Text>
          <Text style={styles.subtitle}>
            {isConfirming 
              ? 'Enter your PIN again to confirm'
              : 'Create a 6-digit PIN to secure your wallet'
            }
          </Text>
        </View>

        {renderPinDots()}
        {renderNumberPad()}

        {biometricsAvailable && !isConfirming && (
          <View style={styles.biometricsContainer}>
            <TouchableOpacity
              style={styles.biometricsOption}
              onPress={() => setEnableBiometrics(!enableBiometrics)}
            >
              <Ionicons 
                name={enableBiometrics ? "checkbox" : "square-outline"} 
                size={24} 
                color={theme.colors.primary} 
              />
              <Text style={styles.biometricsText}>
                Enable biometric unlock (fingerprint/face)
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
  },
  step: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeStep: {
    backgroundColor: theme.colors.primary,
  },
  completedStep: {
    backgroundColor: theme.colors.success,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  stepLine: {
    width: 40,
    height: 2,
    backgroundColor: theme.colors.border,
    marginHorizontal: 8,
  },
  completedLine: {
    backgroundColor: theme.colors.success,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  pinDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 48,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: theme.colors.border,
    marginHorizontal: 8,
  },
  filledPinDot: {
    backgroundColor: theme.colors.primary,
  },
  numberPad: {
    alignItems: 'center',
  },
  numberRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  numberButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.cardBackground,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  numberText: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.colors.text,
  },
  biometricsContainer: {
    marginTop: 32,
    alignItems: 'center',
  },
  biometricsOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  biometricsText: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 12,
  },
});
