# Prometheus Configuration for KryptoPesa Production Monitoring
# Comprehensive metrics collection for enterprise-grade observability

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'kryptopesa-production'
    region: 'east-africa'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load alerting rules
rule_files:
  - "alert_rules.yml"
  - "kryptopesa_rules.yml"

# Scrape configurations
scrape_configs:
  # KryptoPesa Backend API
  - job_name: 'kryptopesa-backend'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    params:
      format: ['prometheus']

  # MongoDB Metrics
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb-exporter:9216']
    scrape_interval: 30s

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s

  # Node.js Application Metrics
  - job_name: 'nodejs-app'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics/nodejs'
    scrape_interval: 15s

  # System Metrics (Node Exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # Docker Container Metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

  # Nginx Load Balancer Metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s

  # Blockchain Network Metrics
  - job_name: 'blockchain-monitor'
    static_configs:
      - targets: ['blockchain-monitor:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Custom KryptoPesa Business Metrics
  - job_name: 'kryptopesa-business'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics/business'
    scrape_interval: 30s
    params:
      module: ['business_metrics']

  # WebSocket Connection Metrics
  - job_name: 'websocket-metrics'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics/websocket'
    scrape_interval: 10s

  # Mobile App Performance Metrics (via backend proxy)
  - job_name: 'mobile-performance'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics/mobile'
    scrape_interval: 60s

  # External Service Health Checks
  - job_name: 'external-services'
    static_configs:
      - targets: ['health-checker:8080']
    metrics_path: '/metrics/external'
    scrape_interval: 60s

  # Security Metrics
  - job_name: 'security-metrics'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics/security'
    scrape_interval: 30s

# Remote write configuration for long-term storage
remote_write:
  - url: "https://prometheus-remote-write.kryptopesa.com/api/v1/write"
    basic_auth:
      username: "kryptopesa"
      password_file: "/etc/prometheus/remote_write_password"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Remote read configuration for historical data
remote_read:
  - url: "https://prometheus-remote-read.kryptopesa.com/api/v1/read"
    basic_auth:
      username: "kryptopesa"
      password_file: "/etc/prometheus/remote_read_password"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true
