/**
 * Comprehensive Sensitive Data Filter
 * Ensures sensitive data is never exposed in logs, API responses, or error messages
 */

const crypto = require('crypto');

// Define sensitive field patterns
const SENSITIVE_FIELDS = [
  // Authentication & Security
  'password',
  'confirmPassword',
  'currentPassword',
  'newPassword',
  'oldPassword',
  'twoFactorSecret',
  'passwordResetToken',
  'passwordResetExpires',
  'refreshToken',
  'accessToken',
  'token',
  'jwt',
  'otp',
  'pin',
  'mnemonic',
  'privateKey',
  'publicKey',
  'encryptedMnemonic',
  'mnemonicHash',
  
  // Personal Information
  'ssn',
  'socialSecurityNumber',
  'nationalId',
  'passportNumber',
  'drivingLicense',
  'documentNumber',
  
  // Financial Information
  'accountnumber',
  'routingnumber',
  'cardnumber',
  'cvv',
  'expirydate',
  'bankaccount',
  'iban',
  'swift',
  
  // System Information
  'ipaddress',
  'useragent',
  'sessionid',
  'deviceid',
  'fingerprint'
];

// Define sensitive patterns (regex)
const SENSITIVE_PATTERNS = [
  /password/i,
  /secret/i,
  /token/i,
  /key/i,
  /mnemonic/i,
  /private/i,
  /auth/i,
  /credential/i,
  /session/i,
  /sensitive/i
];

/**
 * Check if a field name is sensitive
 */
const isSensitiveField = (fieldName) => {
  if (!fieldName || typeof fieldName !== 'string') return false;
  
  // Check exact matches
  if (SENSITIVE_FIELDS.includes(fieldName.toLowerCase())) return true;
  
  // Check pattern matches
  return SENSITIVE_PATTERNS.some(pattern => pattern.test(fieldName));
};

/**
 * Mask sensitive string values
 */
const maskSensitiveValue = (value, showLength = 4) => {
  if (!value || typeof value !== 'string') return value;

  if (value.length <= showLength) {
    return '*'.repeat(value.length);
  }

  const visiblePart = value.slice(-showLength);
  const maskedPart = '*'.repeat(Math.max(0, value.length - showLength));
  return maskedPart + visiblePart;
};

/**
 * Deep filter sensitive data from objects
 */
const filterSensitiveData = (obj, options = {}) => {
  const {
    maskValues = true,
    removeFields = false,
    customSensitiveFields = [],
    preserveStructure = true
  } = options;
  
  if (!obj || typeof obj !== 'object') return obj;
  
  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => filterSensitiveData(item, options));
  }
  
  // Handle dates and other objects that shouldn't be processed
  if (obj instanceof Date || obj instanceof RegExp) return obj;
  
  const filtered = preserveStructure ? {} : obj;
  const allSensitiveFields = [...SENSITIVE_FIELDS, ...customSensitiveFields];
  
  for (const [key, value] of Object.entries(obj)) {
    const isFieldSensitive = isSensitiveField(key) || allSensitiveFields.includes(key.toLowerCase());
    
    if (isFieldSensitive) {
      if (removeFields) {
        // Skip adding this field
        continue;
      } else if (maskValues && typeof value === 'string' && !key.toLowerCase().includes('key') && !key.toLowerCase().includes('secret') && !key.toLowerCase().includes('sensitive')) {
        // Mask values for certain fields, but use [REDACTED] for keys, secrets, and sensitive data
        filtered[key] = maskSensitiveValue(value);
      } else {
        filtered[key] = '[REDACTED]';
      }
    } else if (value && typeof value === 'object') {
      // Recursively filter nested objects
      filtered[key] = filterSensitiveData(value, options);
    } else {
      filtered[key] = value;
    }
  }
  
  return filtered;
};

/**
 * Filter sensitive data for logging
 */
const filterForLogging = (data) => {
  return filterSensitiveData(data, {
    maskValues: false,
    removeFields: false,
    preserveStructure: true
  });
};

/**
 * Filter sensitive data for API responses
 */
const filterForAPI = (data) => {
  return filterSensitiveData(data, {
    maskValues: true,
    removeFields: false,
    preserveStructure: true
  });
};

/**
 * Filter sensitive data for audit logs
 */
const filterForAudit = (data) => {
  return filterSensitiveData(data, {
    maskValues: true,
    removeFields: false,
    preserveStructure: true,
    customSensitiveFields: ['ipAddress', 'userAgent']
  });
};

/**
 * Completely remove sensitive fields
 */
const removeSensitiveFields = (data, customFields = []) => {
  return filterSensitiveData(data, {
    removeFields: true,
    customSensitiveFields: customFields
  });
};

/**
 * Filter error objects for safe logging
 */
const filterErrorForLogging = (error) => {
  if (!error) return error;
  
  const safeError = {
    name: error.name,
    message: error.message,
    code: error.code,
    statusCode: error.statusCode,
    timestamp: error.timestamp || new Date().toISOString()
  };
  
  // Only include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    safeError.stack = error.stack;
  }
  
  // Filter any additional properties
  Object.keys(error).forEach(key => {
    if (!safeError.hasOwnProperty(key)) {
      if (isSensitiveField(key)) {
        safeError[key] = '[REDACTED]';
      } else {
        safeError[key] = error[key];
      }
    }
  });

  return filterSensitiveData(safeError);
};

/**
 * Generate a hash for sensitive data (for verification without exposure)
 */
const hashSensitiveData = (data) => {
  if (!data) return null;
  return crypto.createHash('sha256').update(String(data)).digest('hex').substring(0, 16);
};

/**
 * Middleware to filter request/response data
 */
const createFilterMiddleware = (options = {}) => {
  return (req, res, next) => {
    // Filter request body
    if (req.body) {
      req.filteredBody = filterSensitiveData(req.body, options);
    }
    
    // Store original res.json to filter responses
    const originalJson = res.json;
    res.json = function(data) {
      const filteredData = filterSensitiveData(data, options);
      return originalJson.call(this, filteredData);
    };
    
    next();
  };
};

module.exports = {
  isSensitiveField,
  maskSensitiveValue,
  filterSensitiveData,
  filterForLogging,
  filterForAPI,
  filterForAudit,
  removeSensitiveFields,
  filterErrorForLogging,
  hashSensitiveData,
  createFilterMiddleware,
  SENSITIVE_FIELDS,
  SENSITIVE_PATTERNS
};
