# KryptoPesa Security Policies
# Enterprise-grade security configurations for production deployment

apiVersion: v1
kind: Namespace
metadata:
  name: kryptopesa-production
  labels:
    environment: production
    app: kryptopesa
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted

---
# Pod Security Policy
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: kryptopesa-psp
  namespace: kryptopesa-production
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: true
  seLinux:
    rule: 'RunAsAny'

---
# Network Policy - Backend Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-network-policy
  namespace: kryptopesa-production
spec:
  podSelector:
    matchLabels:
      app: backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from nginx ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  # Allow ingress from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080  # Metrics port
  egress:
  # Allow egress to MongoDB
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 27017
  # Allow egress to Redis
  - to:
    - namespaceSelector:
        matchLabels:
          name: cache
    ports:
    - protocol: TCP
      port: 6379
  # Allow egress to external APIs (HTTPS)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow DNS
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53

---
# Network Policy - Admin Dashboard Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: admin-network-policy
  namespace: kryptopesa-production
spec:
  podSelector:
    matchLabels:
      app: admin
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from nginx ingress controller only
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 80
  egress:
  # Allow egress to backend API
  - to:
    - podSelector:
        matchLabels:
          app: backend
    ports:
    - protocol: TCP
      port: 3000
  # Allow DNS
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53

---
# Network Policy - Database Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: database-network-policy
  namespace: database
spec:
  podSelector:
    matchLabels:
      app: mongodb
  policyTypes:
  - Ingress
  ingress:
  # Only allow connections from backend
  - from:
    - namespaceSelector:
        matchLabels:
          name: kryptopesa-production
      podSelector:
        matchLabels:
          app: backend
    ports:
    - protocol: TCP
      port: 27017

---
# Network Policy - Cache Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cache-network-policy
  namespace: cache
spec:
  podSelector:
    matchLabels:
      app: redis
  policyTypes:
  - Ingress
  ingress:
  # Only allow connections from backend
  - from:
    - namespaceSelector:
        matchLabels:
          name: kryptopesa-production
      podSelector:
        matchLabels:
          app: backend
    ports:
    - protocol: TCP
      port: 6379

---
# Service Account for Backend
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kryptopesa-backend
  namespace: kryptopesa-production
automountServiceAccountToken: false

---
# Service Account for Admin
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kryptopesa-admin
  namespace: kryptopesa-production
automountServiceAccountToken: false

---
# Role for Backend (minimal permissions)
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kryptopesa-backend-role
  namespace: kryptopesa-production
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get"]
  resourceNames: ["kryptopesa-secrets"]

---
# RoleBinding for Backend
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kryptopesa-backend-binding
  namespace: kryptopesa-production
subjects:
- kind: ServiceAccount
  name: kryptopesa-backend
  namespace: kryptopesa-production
roleRef:
  kind: Role
  name: kryptopesa-backend-role
  apiGroup: rbac.authorization.k8s.io

---
# Security Context Constraints
apiVersion: security.openshift.io/v1
kind: SecurityContextConstraints
metadata:
  name: kryptopesa-scc
allowHostDirVolumePlugin: false
allowHostIPC: false
allowHostNetwork: false
allowHostPID: false
allowHostPorts: false
allowPrivilegedContainer: false
allowedCapabilities: null
defaultAddCapabilities: null
requiredDropCapabilities:
- KILL
- MKNOD
- SETUID
- SETGID
fsGroup:
  type: MustRunAs
  ranges:
  - min: 1000
    max: 65535
readOnlyRootFilesystem: true
runAsUser:
  type: MustRunAsNonRoot
seLinuxContext:
  type: MustRunAs
supplementalGroups:
  type: MustRunAs
  ranges:
  - min: 1000
    max: 65535
volumes:
- configMap
- downwardAPI
- emptyDir
- persistentVolumeClaim
- projected
- secret

---
# Admission Controller Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: admission-controller-config
  namespace: kryptopesa-production
data:
  config.yaml: |
    # OPA Gatekeeper policies
    policies:
      - name: require-security-context
        enabled: true
        description: "Require security context for all pods"
      - name: disallow-privileged
        enabled: true
        description: "Disallow privileged containers"
      - name: require-resource-limits
        enabled: true
        description: "Require CPU and memory limits"
      - name: disallow-host-network
        enabled: true
        description: "Disallow host network access"
      - name: require-non-root
        enabled: true
        description: "Require non-root user"

---
# Image Security Policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: image-security-policy
  namespace: kryptopesa-production
data:
  policy.yaml: |
    # Container image security requirements
    imagePolicy:
      # Only allow images from trusted registries
      allowedRegistries:
        - "ghcr.io/kryptopesa"
        - "docker.io/library"
        - "gcr.io/distroless"
      
      # Require image scanning
      requireScanning: true
      maxCriticalVulnerabilities: 0
      maxHighVulnerabilities: 5
      
      # Require signed images
      requireSignature: true
      trustedSigners:
        - "<EMAIL>"

---
# Secrets Management Policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: secrets-policy
  namespace: kryptopesa-production
data:
  policy.yaml: |
    # Secrets management requirements
    secretsPolicy:
      # Encryption at rest
      encryptionAtRest: true
      encryptionProvider: "aes-gcm"
      
      # Key rotation
      keyRotationEnabled: true
      keyRotationInterval: "30d"
      
      # Access controls
      requireServiceAccount: true
      auditAccess: true
      
      # External secrets
      allowExternalSecrets: true
      externalProviders:
        - "aws-secrets-manager"
        - "azure-key-vault"
        - "hashicorp-vault"

---
# Compliance Monitoring
apiVersion: v1
kind: ConfigMap
metadata:
  name: compliance-config
  namespace: kryptopesa-production
data:
  compliance.yaml: |
    # Compliance frameworks
    frameworks:
      - name: "PCI-DSS"
        enabled: true
        version: "4.0"
      - name: "SOC2"
        enabled: true
        type: "Type II"
      - name: "GDPR"
        enabled: true
        region: "EU"
    
    # Audit requirements
    audit:
      enabled: true
      retention: "7y"
      encryption: true
      immutable: true
    
    # Monitoring
    monitoring:
      realtime: true
      alerting: true
      reporting: true
      frequency: "daily"
