const Message = require('../models/Message');
const Chat = require('../models/Chat');
const Trade = require('../models/Trade');
const User = require('../models/User');
const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');

class ChatScalabilityService {
  constructor() {
    this.redis = null;
    this.messageCache = new Map(); // Fallback in-memory cache
    this.connectionPool = new Map(); // Active connections
    this.roomSubscriptions = new Map(); // Room -> Set of userIds
    this.userRooms = new Map(); // userId -> Set of roomIds
    this.offlineMessageQueue = new Map(); // userId -> Array of messages
    this.typingTimeouts = new Map(); // roomId -> Map(userId -> timeout)
    
    // Performance metrics
    this.metrics = {
      messagesProcessed: 0,
      cacheHits: 0,
      cacheMisses: 0,
      activeConnections: 0,
      messagesSent: 0,
      messagesDelivered: 0,
      averageResponseTime: 0
    };
    
    this.initialize();
  }

  /**
   * Initialize the chat scalability service
   */
  async initialize() {
    try {
      this.redis = getRedisClient();
      logger.info('Chat Scalability Service initialized with Redis support');
    } catch (error) {
      logger.warn('Redis not available, using in-memory fallback for chat service');
    }
  }

  /**
   * Optimized message creation with caching and background processing
   */
  async createMessage(tradeId, senderId, content, type = 'text', attachmentData = null) {
    const startTime = Date.now();
    
    try {
      // Validate input
      if (!tradeId || !content || content.trim().length === 0) {
        throw new AppError('Invalid message data', 400);
      }

      // Sanitize content
      const sanitizedContent = content.trim().substring(0, 1000);

      // Create message document
      const messageData = {
        trade: tradeId,
        sender: senderId,
        message: sanitizedContent,
        type,
        timestamp: new Date(),
        isRead: false,
        attachmentUrl: attachmentData?.url || null,
        attachmentType: attachmentData?.type || null,
        attachmentSize: attachmentData?.size || null
      };

      // Save to database
      const message = new Message(messageData);
      await message.save();

      // Populate sender information
      await message.populate('sender', 'username firstName lastName avatar isOnline');

      // Cache the message for quick retrieval
      await this.cacheMessage(message);

      // Update chat metadata (background operation)
      setImmediate(() => {
        this.updateChatMetadata(tradeId, message._id);
      });

      // Update metrics
      this.metrics.messagesProcessed++;
      this.metrics.messagesSent++;
      this.metrics.averageResponseTime = (this.metrics.averageResponseTime + (Date.now() - startTime)) / 2;

      logger.info(`Message created for trade ${tradeId} by user ${senderId}`);
      
      return message;
    } catch (error) {
      logger.error(`Failed to create message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Optimized message retrieval with pagination and caching
   */
  async getMessages(tradeId, options = {}) {
    const {
      limit = 50,
      offset = 0,
      includeDeleted = false,
      messageType = null,
      userId = null
    } = options;

    try {
      // Check cache first
      const cacheKey = `messages:${tradeId}:${limit}:${offset}:${messageType || 'all'}`;
      const cachedMessages = await this.getCachedData(cacheKey);
      
      if (cachedMessages) {
        this.metrics.cacheHits++;
        return cachedMessages;
      }

      this.metrics.cacheMisses++;

      // Build optimized query
      const query = { trade: tradeId };
      
      if (!includeDeleted) {
        query.isDeleted = false;
      }
      
      if (messageType) {
        query.type = messageType;
      }

      // Use lean queries for better performance
      const messages = await Message.find(query)
        .populate('sender', 'username firstName lastName avatar isOnline')
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip(offset)
        .lean();

      // Reverse to show chronological order
      messages.reverse();

      // Mark messages as read if userId provided
      if (userId) {
        setImmediate(() => {
          this.markMessagesAsRead(tradeId, userId, messages.map(m => m._id));
        });
      }

      // Cache the result
      await this.setCachedData(cacheKey, messages, 300); // 5 minutes TTL

      return messages;
    } catch (error) {
      logger.error(`Failed to get messages for trade ${tradeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Optimized message broadcasting with Redis pub/sub
   */
  async broadcastMessage(tradeId, message, excludeUserId = null) {
    try {
      const roomId = `trade:${tradeId}`;
      const messageData = {
        id: message._id,
        tradeId,
        senderId: message.sender?._id,
        senderUsername: message.sender?.username,
        senderDisplayName: message.sender ? 
          `${message.sender.firstName || ''} ${message.sender.lastName || ''}`.trim() || message.sender.username :
          'System',
        message: message.message,
        type: message.type,
        timestamp: message.timestamp,
        attachmentUrl: message.attachmentUrl,
        attachmentType: message.attachmentType,
        hasAttachment: !!message.attachmentUrl,
        isSystem: !message.sender
      };

      // Get room participants
      const participants = this.roomSubscriptions.get(roomId) || new Set();
      
      // Broadcast to online users
      for (const userId of participants) {
        if (userId !== excludeUserId) {
          await this.sendMessageToUser(userId, 'new_message', messageData);
        }
      }

      // Queue for offline users
      await this.queueOfflineMessage(tradeId, messageData, excludeUserId);

      this.metrics.messagesDelivered++;
      
      logger.info(`Message broadcasted to room ${roomId}, ${participants.size} participants`);
    } catch (error) {
      logger.error(`Failed to broadcast message: ${error.message}`);
    }
  }

  /**
   * Handle user connection with optimized room management
   */
  async handleUserConnection(userId, socketId) {
    try {
      // Store connection
      this.connectionPool.set(userId, socketId);
      this.metrics.activeConnections = this.connectionPool.size;

      // Update user online status
      await User.findByIdAndUpdate(userId, {
        isOnline: true,
        lastSeen: new Date()
      });

      // Deliver queued offline messages
      await this.deliverOfflineMessages(userId);

      logger.info(`User ${userId} connected with socket ${socketId}`);
    } catch (error) {
      logger.error(`Failed to handle user connection: ${error.message}`);
    }
  }

  /**
   * Handle user disconnection with cleanup
   */
  async handleUserDisconnection(userId) {
    try {
      // Remove from connection pool
      this.connectionPool.delete(userId);
      this.metrics.activeConnections = this.connectionPool.size;

      // Clean up room subscriptions
      const userRooms = this.userRooms.get(userId) || new Set();
      for (const roomId of userRooms) {
        const roomUsers = this.roomSubscriptions.get(roomId);
        if (roomUsers) {
          roomUsers.delete(userId);
          if (roomUsers.size === 0) {
            this.roomSubscriptions.delete(roomId);
          }
        }
      }
      this.userRooms.delete(userId);

      // Update user offline status
      await User.findByIdAndUpdate(userId, {
        isOnline: false,
        lastSeen: new Date()
      });

      logger.info(`User ${userId} disconnected and cleaned up`);
    } catch (error) {
      logger.error(`Failed to handle user disconnection: ${error.message}`);
    }
  }

  /**
   * Join user to trade room with validation
   */
  async joinTradeRoom(userId, tradeId) {
    try {
      // Verify user is participant in trade
      const trade = await Trade.findOne({ tradeId }).lean();
      if (!trade) {
        throw new AppError('Trade not found', 404);
      }

      const isParticipant = trade.seller.toString() === userId || 
                           trade.buyer.toString() === userId;
      
      if (!isParticipant) {
        throw new AppError('Access denied - not a trade participant', 403);
      }

      const roomId = `trade:${tradeId}`;
      
      // Add user to room
      if (!this.roomSubscriptions.has(roomId)) {
        this.roomSubscriptions.set(roomId, new Set());
      }
      this.roomSubscriptions.get(roomId).add(userId);

      // Add room to user's rooms
      if (!this.userRooms.has(userId)) {
        this.userRooms.set(userId, new Set());
      }
      this.userRooms.get(userId).add(roomId);

      logger.info(`User ${userId} joined trade room ${roomId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to join trade room: ${error.message}`);
      throw error;
    }
  }

  /**
   * Leave trade room with cleanup
   */
  async leaveTradeRoom(userId, tradeId) {
    try {
      const roomId = `trade:${tradeId}`;

      // Remove user from room
      const roomUsers = this.roomSubscriptions.get(roomId);
      if (roomUsers) {
        roomUsers.delete(userId);
        if (roomUsers.size === 0) {
          this.roomSubscriptions.delete(roomId);
        }
      }

      // Remove room from user's rooms
      const userRooms = this.userRooms.get(userId);
      if (userRooms) {
        userRooms.delete(roomId);
        if (userRooms.size === 0) {
          this.userRooms.delete(userId);
        }
      }

      logger.info(`User ${userId} left trade room ${roomId}`);
    } catch (error) {
      logger.error(`Failed to leave trade room: ${error.message}`);
    }
  }

  /**
   * Handle typing indicators with auto-cleanup
   */
  handleTypingStart(userId, tradeId, username) {
    try {
      const roomId = `trade:${tradeId}`;

      // Clear existing timeout
      const roomTimeouts = this.typingTimeouts.get(roomId) || new Map();
      const existingTimeout = roomTimeouts.get(userId);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // Broadcast typing start to room participants
      const participants = this.roomSubscriptions.get(roomId) || new Set();
      for (const participantId of participants) {
        if (participantId !== userId) {
          this.sendMessageToUser(participantId, 'user_typing', {
            userId,
            username,
            tradeId,
            isTyping: true
          });
        }
      }

      // Set auto-stop timeout (3 seconds)
      const timeout = setTimeout(() => {
        this.handleTypingStop(userId, tradeId);
      }, 3000);

      roomTimeouts.set(userId, timeout);
      this.typingTimeouts.set(roomId, roomTimeouts);

    } catch (error) {
      logger.error(`Failed to handle typing start: ${error.message}`);
    }
  }

  /**
   * Handle typing stop
   */
  handleTypingStop(userId, tradeId) {
    try {
      const roomId = `trade:${tradeId}`;

      // Clear timeout
      const roomTimeouts = this.typingTimeouts.get(roomId);
      if (roomTimeouts) {
        const timeout = roomTimeouts.get(userId);
        if (timeout) {
          clearTimeout(timeout);
          roomTimeouts.delete(userId);
        }

        if (roomTimeouts.size === 0) {
          this.typingTimeouts.delete(roomId);
        }
      }

      // Broadcast typing stop to room participants
      const participants = this.roomSubscriptions.get(roomId) || new Set();
      for (const participantId of participants) {
        if (participantId !== userId) {
          this.sendMessageToUser(participantId, 'user_stopped_typing', {
            userId,
            tradeId
          });
        }
      }

    } catch (error) {
      logger.error(`Failed to handle typing stop: ${error.message}`);
    }
  }

  /**
   * Mark messages as read with batch processing
   */
  async markMessagesAsRead(tradeId, userId, messageIds = []) {
    try {
      const query = {
        trade: tradeId,
        sender: { $ne: userId },
        isRead: false
      };

      if (messageIds.length > 0) {
        query._id = { $in: messageIds };
      }

      // Batch update messages
      const result = await Message.updateMany(query, {
        isRead: true,
        readAt: new Date()
      });

      // Invalidate cache
      await this.invalidateMessageCache(tradeId);

      // Broadcast read status to other participants
      if (result.modifiedCount > 0) {
        const roomId = `trade:${tradeId}`;
        const participants = this.roomSubscriptions.get(roomId) || new Set();

        for (const participantId of participants) {
          if (participantId !== userId) {
            this.sendMessageToUser(participantId, 'messages_read', {
              tradeId,
              readByUserId: userId,
              messageCount: result.modifiedCount
            });
          }
        }
      }

      logger.info(`Marked ${result.modifiedCount} messages as read for user ${userId} in trade ${tradeId}`);
      return result.modifiedCount;
    } catch (error) {
      logger.error(`Failed to mark messages as read: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get unread message count for user
   */
  async getUnreadCount(tradeId, userId) {
    try {
      const cacheKey = `unread:${tradeId}:${userId}`;
      const cachedCount = await this.getCachedData(cacheKey);

      if (cachedCount !== null) {
        return cachedCount;
      }

      const count = await Message.countDocuments({
        trade: tradeId,
        sender: { $ne: userId },
        isRead: false,
        isDeleted: false
      });

      // Cache for 1 minute
      await this.setCachedData(cacheKey, count, 60);

      return count;
    } catch (error) {
      logger.error(`Failed to get unread count: ${error.message}`);
      return 0;
    }
  }

  /**
   * Cache message data
   */
  async cacheMessage(message) {
    try {
      const cacheKey = `message:${message._id}`;
      const messageData = message.toClientJSON ? message.toClientJSON() : message;

      await this.setCachedData(cacheKey, messageData, 3600); // 1 hour TTL
    } catch (error) {
      logger.error(`Failed to cache message: ${error.message}`);
    }
  }

  /**
   * Update chat metadata in background
   */
  async updateChatMetadata(tradeId, messageId) {
    try {
      // Update or create chat document with latest message info
      await Chat.findOneAndUpdate(
        { trade: tradeId },
        {
          $inc: { 'metadata.totalMessages': 1 },
          $set: {
            'metadata.lastActivity': new Date(),
            'metadata.lastMessageId': messageId
          }
        },
        { upsert: true }
      );

      // Invalidate related caches
      await this.invalidateMessageCache(tradeId);
    } catch (error) {
      logger.error(`Failed to update chat metadata: ${error.message}`);
    }
  }

  /**
   * Queue message for offline users
   */
  async queueOfflineMessage(tradeId, messageData, excludeUserId) {
    try {
      // Get trade participants
      const trade = await Trade.findOne({ tradeId }).lean();
      if (!trade) return;

      const participants = [trade.seller.toString(), trade.buyer.toString()];

      for (const userId of participants) {
        if (userId !== excludeUserId && !this.connectionPool.has(userId)) {
          // User is offline, queue the message
          if (!this.offlineMessageQueue.has(userId)) {
            this.offlineMessageQueue.set(userId, []);
          }

          this.offlineMessageQueue.get(userId).push({
            ...messageData,
            queuedAt: new Date()
          });

          // Limit queue size to prevent memory issues
          const queue = this.offlineMessageQueue.get(userId);
          if (queue.length > 100) {
            queue.splice(0, queue.length - 100);
          }
        }
      }
    } catch (error) {
      logger.error(`Failed to queue offline message: ${error.message}`);
    }
  }

  /**
   * Deliver queued offline messages to user
   */
  async deliverOfflineMessages(userId) {
    try {
      const queuedMessages = this.offlineMessageQueue.get(userId);
      if (!queuedMessages || queuedMessages.length === 0) {
        return;
      }

      // Send all queued messages
      for (const messageData of queuedMessages) {
        await this.sendMessageToUser(userId, 'offline_message', messageData);
      }

      // Clear the queue
      this.offlineMessageQueue.delete(userId);

      logger.info(`Delivered ${queuedMessages.length} offline messages to user ${userId}`);
    } catch (error) {
      logger.error(`Failed to deliver offline messages: ${error.message}`);
    }
  }

  /**
   * Send message to specific user
   */
  async sendMessageToUser(userId, event, data) {
    try {
      const socketId = this.connectionPool.get(userId);
      if (!socketId) {
        return false; // User not connected
      }

      // In a real implementation, this would use Socket.IO to emit to specific socket
      // For now, we'll use Redis pub/sub or direct socket emission
      if (this.redis) {
        await this.redis.publish(`user:${userId}`, JSON.stringify({ event, data }));
      }

      return true;
    } catch (error) {
      logger.error(`Failed to send message to user: ${error.message}`);
      return false;
    }
  }

  /**
   * Cache data with Redis or fallback to memory
   */
  async setCachedData(key, data, ttlSeconds = 3600) {
    try {
      if (this.redis) {
        await this.redis.setex(key, ttlSeconds, JSON.stringify(data));
      } else {
        // Fallback to memory cache with TTL
        this.messageCache.set(key, {
          data,
          expires: Date.now() + (ttlSeconds * 1000)
        });
      }
    } catch (error) {
      logger.error(`Failed to cache data: ${error.message}`);
    }
  }

  /**
   * Get cached data
   */
  async getCachedData(key) {
    try {
      if (this.redis) {
        const cached = await this.redis.get(key);
        return cached ? JSON.parse(cached) : null;
      } else {
        // Check memory cache
        const cached = this.messageCache.get(key);
        if (cached) {
          if (Date.now() < cached.expires) {
            return cached.data;
          } else {
            this.messageCache.delete(key);
          }
        }
        return null;
      }
    } catch (error) {
      logger.error(`Failed to get cached data: ${error.message}`);
      return null;
    }
  }

  /**
   * Invalidate message cache for trade
   */
  async invalidateMessageCache(tradeId) {
    try {
      if (this.redis) {
        const pattern = `messages:${tradeId}:*`;
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(keys);
        }

        // Also invalidate unread counts
        const unreadPattern = `unread:${tradeId}:*`;
        const unreadKeys = await this.redis.keys(unreadPattern);
        if (unreadKeys.length > 0) {
          await this.redis.del(unreadKeys);
        }
      } else {
        // Clear memory cache entries for this trade
        for (const key of this.messageCache.keys()) {
          if (key.includes(`messages:${tradeId}:`) || key.includes(`unread:${tradeId}:`)) {
            this.messageCache.delete(key);
          }
        }
      }
    } catch (error) {
      logger.error(`Failed to invalidate cache: ${error.message}`);
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      cacheHitRate: this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) || 0,
      activeRooms: this.roomSubscriptions.size,
      totalUserRooms: Array.from(this.userRooms.values()).reduce((sum, rooms) => sum + rooms.size, 0),
      offlineQueueSize: Array.from(this.offlineMessageQueue.values()).reduce((sum, queue) => sum + queue.length, 0)
    };
  }

  /**
   * Cleanup old data and optimize memory usage
   */
  async cleanup() {
    try {
      // Clean up expired memory cache entries
      const now = Date.now();
      for (const [key, cached] of this.messageCache.entries()) {
        if (now >= cached.expires) {
          this.messageCache.delete(key);
        }
      }

      // Clean up old offline messages (older than 24 hours)
      const cutoffTime = now - (24 * 60 * 60 * 1000);
      for (const [userId, queue] of this.offlineMessageQueue.entries()) {
        const filteredQueue = queue.filter(msg =>
          new Date(msg.queuedAt).getTime() > cutoffTime
        );

        if (filteredQueue.length === 0) {
          this.offlineMessageQueue.delete(userId);
        } else if (filteredQueue.length !== queue.length) {
          this.offlineMessageQueue.set(userId, filteredQueue);
        }
      }

      logger.info('Chat scalability service cleanup completed');
    } catch (error) {
      logger.error(`Failed to cleanup chat service: ${error.message}`);
    }
  }

  /**
   * Archive old messages for completed trades
   */
  async archiveOldMessages(daysOld = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      // Find completed trades older than cutoff
      const oldTrades = await Trade.find({
        status: { $in: ['completed', 'cancelled'] },
        updatedAt: { $lt: cutoffDate }
      }).select('_id').lean();

      if (oldTrades.length === 0) {
        return 0;
      }

      const tradeIds = oldTrades.map(t => t._id);

      // Archive messages (mark as archived instead of deleting)
      const result = await Message.updateMany(
        {
          trade: { $in: tradeIds },
          isDeleted: false
        },
        {
          isArchived: true,
          archivedAt: new Date()
        }
      );

      logger.info(`Archived ${result.modifiedCount} messages from ${oldTrades.length} old trades`);
      return result.modifiedCount;
    } catch (error) {
      logger.error(`Failed to archive old messages: ${error.message}`);
      return 0;
    }
  }
}

// Create singleton instance
const chatScalabilityService = new ChatScalabilityService();

// Schedule periodic cleanup (every hour)
setInterval(() => {
  chatScalabilityService.cleanup();
}, 60 * 60 * 1000);

module.exports = chatScalabilityService;
