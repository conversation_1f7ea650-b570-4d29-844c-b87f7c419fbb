import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import {
  tradingService,
  userService,
  EAST_AFRICAN_CONFIG,
  Offer,
  eastAfricanHelpers
} from '../../services';

// Mock offer data - in a real app this would come from Convex
const mockOffer = {
  id: '1',
  type: 'sell',
  cryptoCurrency: 'BTC',
  fiatCurrency: 'USD',
  price: 45250.00,
  minAmount: 100,
  maxAmount: 5000,
  paymentMethods: ['Bank Transfer', 'PayPal', 'Wise'],
  terms: 'Please make payment within 30 minutes. Provide transaction ID after payment. No third-party payments accepted.',
  timeLimit: 30,
  user: {
    id: 'user123',
    name: '<PERSON>',
    rating: 4.8,
    completedTrades: 127,
    responseTime: '5 min',
    lastSeen: '2 hours ago',
    verificationLevel: 'verified',
  },
  createdAt: '2024-01-15T10:30:00Z',
  isOnline: true,
};

export default function OfferDetailScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { offerId } = route.params || {};

  // State management
  const [offer, setOffer] = useState<Offer | null>(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [creating, setCreating] = useState(false);

  // Trade form state
  const [tradeAmount, setTradeAmount] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [paymentMethodModalVisible, setPaymentMethodModalVisible] = useState(false);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);

  // Load offer data
  const loadOfferData = async (showLoading = true) => {
    if (!offerId) {
      setError('Offer ID not provided');
      setLoading(false);
      return;
    }

    try {
      if (showLoading) setLoading(true);
      setError(null);

      const [offerData, profile] = await Promise.all([
        tradingService.getOffer(offerId),
        userService.getProfile(),
      ]);

      setOffer(offerData);
      setUserProfile(profile);

      // Set default payment method if user has preferences
      if (offerData.paymentMethods.length > 0) {
        setSelectedPaymentMethod(offerData.paymentMethods[0]);
      }
    } catch (err) {
      console.error('Failed to load offer:', err);
      setError(err instanceof Error ? err.message : 'Failed to load offer');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadOfferData();
  }, [offerId]);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadOfferData(false);
    setRefreshing(false);
  };
  
  const calculateCryptoAmount = () => {
    if (!tradeAmount || !offer?.fiat.exchangeRate) return '0';
    return (parseFloat(tradeAmount) / offer.fiat.exchangeRate).toFixed(8);
  };

  const getAvailableCryptoAmount = () => {
    if (!offer) return 0;
    return offer.cryptocurrency.availableAmount;
  };

  const getMaxFiatAmount = () => {
    if (!offer) return 0;
    const maxFromAvailable = offer.cryptocurrency.availableAmount * offer.fiat.exchangeRate;
    return Math.min(maxFromAvailable, offer.cryptocurrency.maxAmount);
  };

  const validateTrade = () => {
    if (!offer) return false;

    const amount = parseFloat(tradeAmount);

    if (!tradeAmount || isNaN(amount) || amount <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid amount');
      return false;
    }

    if (offer.cryptocurrency?.minAmount && amount < offer.cryptocurrency.minAmount) {
      Alert.alert('Amount Too Low', `Minimum amount is ${eastAfricanHelpers.formatCurrencyAmount(offer.cryptocurrency.minAmount, offer.fiat.currency)}`);
      return false;
    }

    const maxAmount = getMaxFiatAmount();
    if (amount > maxAmount) {
      Alert.alert('Amount Too High', `Maximum available amount is ${eastAfricanHelpers.formatCurrencyAmount(maxAmount, offer.fiat.currency)}`);
      return false;
    }

    if (!selectedPaymentMethod) {
      Alert.alert('Payment Method Required', 'Please select a payment method');
      return false;
    }

    // Check if user is trying to trade with themselves
    if (offer.creator._id === userProfile?._id) {
      Alert.alert('Invalid Trade', 'You cannot trade with your own offer');
      return false;
    }

    return true;
  };

  const handleStartTrade = () => {
    if (!validateTrade()) return;
    setConfirmModalVisible(true);
  };
  
  const confirmTrade = async () => {
    if (!offer || !userProfile) return;

    try {
      setCreating(true);

      const tradeData = {
        offerId: offer._id,
        fiatAmount: parseFloat(tradeAmount),
        paymentMethod: selectedPaymentMethod,
      };

      const newTrade = await tradingService.createTrade(tradeData);

      setConfirmModalVisible(false);

      Alert.alert(
        'Trade Created Successfully!',
        `Your ${offer.type === 'buy' ? 'sell' : 'buy'} trade for ${calculateCryptoAmount()} ${offer.cryptocurrency.type} has been created.`,
        [
          {
            text: 'View Trade',
            onPress: () => {
              navigation.navigate('Trade', { tradeId: newTrade._id });
            },
          },
          {
            text: 'Go to Trades',
            onPress: () => {
              navigation.navigate('ActiveTrades');
            },
            style: 'cancel',
          },
        ]
      );
    } catch (err) {
      console.error('Failed to create trade:', err);
      Alert.alert(
        'Failed to Create Trade',
        err instanceof Error ? err.message : 'Please try again later.'
      );
    } finally {
      setCreating(false);
    }
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Offer Details</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading offer details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !offer) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Offer Details</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load offer</Text>
          <Text style={styles.errorSubText}>{error || 'Offer not found'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadOfferData()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>KryptoPesa Offer</Text>
        <TouchableOpacity>
          <Ionicons name="share-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {/* Offer Type Badge */}
        <View style={styles.offerTypeBadge}>
          <Text style={[
            styles.offerTypeText,
            offer.type === 'buy' ? styles.buyText : styles.sellText
          ]}>
            {offer.type === 'buy' ? 'BUY' : 'SELL'} {offer.cryptocurrency.type}
          </Text>
          <View style={styles.locationBadge}>
            <Text style={styles.flagText}>
              {EAST_AFRICAN_CONFIG.COUNTRIES[offer.location.country]?.flag}
            </Text>
            <Text style={styles.locationText}>
              {offer.location.city ? `${offer.location.city}, ` : ''}{EAST_AFRICAN_CONFIG.COUNTRIES[offer.location.country]?.name}
            </Text>
          </View>
        </View>

        {/* Price Section */}
        <View style={styles.section}>
          <View style={styles.priceContainer}>
            <View style={styles.cryptoIconContainer}>
              <Text style={styles.cryptoIconText}>
                {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[offer.cryptocurrency.type]?.symbol || offer.cryptocurrency.type}
              </Text>
            </View>
            <View style={styles.priceInfo}>
              <Text style={styles.priceLabel}>Exchange Rate</Text>
              <Text style={styles.priceValue}>
                {eastAfricanHelpers.formatCurrencyAmount(offer.fiat.exchangeRate, offer.fiat.currency)}
              </Text>
              <Text style={styles.priceSubtext}>per {offer.cryptocurrency.type}</Text>
            </View>
          </View>
        </View>

        {/* Amount Limits */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trading Limits</Text>
          <View style={styles.limitsContainer}>
            <View style={styles.limitItem}>
              <Text style={styles.limitLabel}>Minimum</Text>
              <Text style={styles.limitValue}>
                {eastAfricanHelpers.formatCurrencyAmount(offer.cryptocurrency?.minAmount || 0, offer.fiat.currency)}
              </Text>
            </View>
            <View style={styles.limitSeparator} />
            <View style={styles.limitItem}>
              <Text style={styles.limitLabel}>Available</Text>
              <Text style={styles.limitValue}>
                {offer.cryptocurrency.availableAmount} {offer.cryptocurrency.type}
              </Text>
            </View>
            <View style={styles.limitSeparator} />
            <View style={styles.limitItem}>
              <Text style={styles.limitLabel}>Maximum</Text>
              <Text style={styles.limitValue}>
                {eastAfricanHelpers.formatCurrencyAmount(getMaxFiatAmount(), offer.fiat.currency)}
              </Text>
            </View>
          </View>
        </View>

        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>External Payment Methods</Text>
          <Text style={styles.paymentMethodsDescription}>
            Payments are handled externally. KryptoPesa only holds crypto in escrow.
          </Text>
          <View style={styles.paymentMethodsContainer}>
            {offer.paymentMethods.map((method, index) => (
              <View key={index} style={styles.paymentMethodTag}>
                <Text style={styles.paymentMethodIcon}>
                  {EAST_AFRICAN_CONFIG.PAYMENT_METHODS[method]?.icon}
                </Text>
                <Text style={styles.paymentMethodText}>
                  {eastAfricanHelpers.getPaymentMethodName(method)}
                </Text>
              </View>
            ))}
          </View>
        </View>
        
        {/* Trader Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trader Information</Text>
          <TouchableOpacity
            style={styles.traderContainer}
            onPress={() => navigation.navigate('TraderProfile', { traderId: offer.creator._id })}
          >
            <View style={styles.traderHeader}>
              <View style={styles.traderAvatar}>
                <Text style={styles.traderAvatarText}>
                  {offer.creator.username.charAt(0).toUpperCase()}
                </Text>
              </View>
              <View style={styles.traderInfo}>
                <View style={styles.traderNameContainer}>
                  <Text style={styles.traderName}>@{offer.creator.username}</Text>
                  {offer.creator.verification?.isVerified && (
                    <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
                  )}
                  {offer.creator.isOnline && (
                    <View style={styles.onlineIndicator} />
                  )}
                </View>
                <Text style={styles.traderJoined}>
                  Joined {formatDate(offer.creator.createdAt)}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Trading Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trading Terms</Text>
          {offer.terms.instructions ? (
            <Text style={styles.termsText}>{offer.terms.instructions}</Text>
          ) : (
            <Text style={styles.noTermsText}>No specific terms provided</Text>
          )}
          <View style={styles.termsDetails}>
            <View style={styles.termItem}>
              <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.termText}>
                Payment time limit: {offer.terms.timeLimit} minutes
              </Text>
            </View>
            {offer.terms.minimumReputation > 0 && (
              <View style={styles.termItem}>
                <Ionicons name="star-outline" size={16} color={theme.colors.textSecondary} />
                <Text style={styles.termText}>
                  Minimum reputation: {offer.terms.minimumReputation}%
                </Text>
              </View>
            )}
          </View>
        </View>
        
        {/* Trade Form */}
        {offer.creator._id !== userProfile?._id ? (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Start KryptoPesa Trade</Text>

            <View style={styles.tradeForm}>
              <Text style={styles.inputLabel}>
                Amount ({EAST_AFRICAN_CONFIG.CURRENCIES[offer.fiat.currency]?.symbol} {offer.fiat.currency})
              </Text>
              <TextInput
                style={styles.amountInput}
                placeholder={`${eastAfricanHelpers.formatCurrencyAmount(offer.cryptocurrency?.minAmount || 0, offer.fiat.currency)} - ${eastAfricanHelpers.formatCurrencyAmount(getMaxFiatAmount(), offer.fiat.currency)}`}
                value={tradeAmount}
                onChangeText={setTradeAmount}
                keyboardType="numeric"
                placeholderTextColor={theme.colors.inputPlaceholder}
              />

              {tradeAmount && (
                <View style={styles.conversionContainer}>
                  <Text style={styles.conversionText}>
                    You will {offer.type === 'buy' ? 'sell' : 'receive'}: {calculateCryptoAmount()} {offer.cryptocurrency.type}
                  </Text>
                  <Text style={styles.conversionSubText}>
                    Rate: {eastAfricanHelpers.formatCurrencyAmount(offer.fiat.exchangeRate, offer.fiat.currency)} per {offer.cryptocurrency.type}
                  </Text>
                </View>
              )}

              <Text style={styles.inputLabel}>External Payment Method</Text>
              <TouchableOpacity
                style={styles.paymentMethodSelector}
                onPress={() => setPaymentMethodModalVisible(true)}
              >
                <View style={styles.paymentMethodDisplay}>
                  {selectedPaymentMethod && (
                    <Text style={styles.paymentMethodIcon}>
                      {EAST_AFRICAN_CONFIG.PAYMENT_METHODS[selectedPaymentMethod]?.icon}
                    </Text>
                  )}
                  <Text style={[
                    styles.paymentMethodSelectorText,
                    !selectedPaymentMethod && styles.placeholderText
                  ]}>
                    {selectedPaymentMethod ? eastAfricanHelpers.getPaymentMethodName(selectedPaymentMethod) : 'Select payment method'}
                  </Text>
                </View>
                <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>

              <View style={styles.escrowNotice}>
                <Ionicons name="shield-checkmark" size={20} color={theme.colors.success} />
                <Text style={styles.escrowNoticeText}>
                  KryptoPesa holds crypto in escrow. You handle fiat payments externally.
                </Text>
              </View>

              <TouchableOpacity
                style={[
                  styles.startTradeButton,
                  (!tradeAmount || !selectedPaymentMethod || creating) && styles.disabledButton
                ]}
                onPress={handleStartTrade}
                disabled={!tradeAmount || !selectedPaymentMethod || creating}
              >
                {creating ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.startTradeButtonText}>
                    {offer.type === 'buy' ? 'Sell' : 'Buy'} {offer.cryptocurrency.type}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <View style={styles.section}>
            <View style={styles.ownOfferNotice}>
              <Ionicons name="information-circle" size={24} color={theme.colors.info} />
              <Text style={styles.ownOfferNoticeText}>
                This is your own offer. You cannot trade with yourself.
              </Text>
            </View>
          </View>
        )}

        <View style={styles.offerMeta}>
          <Text style={styles.offerMetaText}>
            Offer created: {formatDate(offer.createdAt)}
          </Text>
          <Text style={styles.offerMetaText}>
            Last updated: {formatDate(offer.updatedAt)}
          </Text>
        </View>
      </ScrollView>

      {/* Payment Method Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={paymentMethodModalVisible}
        onRequestClose={() => setPaymentMethodModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>External Payment Method</Text>
              <TouchableOpacity onPress={() => setPaymentMethodModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalNotice}>
              <Ionicons name="information-circle" size={20} color={theme.colors.info} />
              <Text style={styles.modalNoticeText}>
                Select how you'll handle payment externally. KryptoPesa only holds crypto in escrow.
              </Text>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {offer.paymentMethods.map((method, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    selectedPaymentMethod === method && styles.modalOptionSelected
                  ]}
                  onPress={() => {
                    setSelectedPaymentMethod(method);
                    setPaymentMethodModalVisible(false);
                  }}
                >
                  <View style={styles.modalOptionContent}>
                    <Text style={styles.paymentMethodIcon}>
                      {EAST_AFRICAN_CONFIG.PAYMENT_METHODS[method]?.icon}
                    </Text>
                    <View style={styles.modalOptionInfo}>
                      <Text style={[
                        styles.modalOptionText,
                        selectedPaymentMethod === method && styles.modalOptionTextSelected
                      ]}>
                        {eastAfricanHelpers.getPaymentMethodName(method)}
                      </Text>
                      <Text style={styles.modalOptionSubText}>
                        Available in {EAST_AFRICAN_CONFIG.COUNTRIES[offer.location.country]?.name}
                      </Text>
                    </View>
                  </View>
                  {selectedPaymentMethod === method && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Confirmation Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={confirmModalVisible}
        onRequestClose={() => setConfirmModalVisible(false)}
      >
        <View style={styles.confirmModalOverlay}>
          <View style={styles.confirmModalContent}>
            <View style={styles.confirmModalHeader}>
              <Ionicons name="shield-checkmark" size={48} color={theme.colors.success} />
              <Text style={styles.confirmModalTitle}>Confirm KryptoPesa Trade</Text>
            </View>

            <View style={styles.confirmModalBody}>
              <View style={styles.confirmRow}>
                <Text style={styles.confirmLabel}>You will {offer.type === 'buy' ? 'sell' : 'receive'}:</Text>
                <Text style={styles.confirmValue}>
                  {calculateCryptoAmount()} {offer.cryptocurrency.type}
                </Text>
              </View>

              <View style={styles.confirmRow}>
                <Text style={styles.confirmLabel}>For:</Text>
                <Text style={styles.confirmValue}>
                  {eastAfricanHelpers.formatCurrencyAmount(parseFloat(tradeAmount), offer.fiat.currency)}
                </Text>
              </View>

              <View style={styles.confirmRow}>
                <Text style={styles.confirmLabel}>External payment via:</Text>
                <View style={styles.confirmPaymentMethod}>
                  <Text style={styles.paymentMethodIcon}>
                    {EAST_AFRICAN_CONFIG.PAYMENT_METHODS[selectedPaymentMethod]?.icon}
                  </Text>
                  <Text style={styles.confirmValue}>
                    {eastAfricanHelpers.getPaymentMethodName(selectedPaymentMethod)}
                  </Text>
                </View>
              </View>

              <View style={styles.confirmRow}>
                <Text style={styles.confirmLabel}>Trading with:</Text>
                <Text style={styles.confirmValue}>@{offer.creator.username}</Text>
              </View>

              <View style={styles.escrowWarning}>
                <Ionicons name="information-circle" size={16} color={theme.colors.warning} />
                <Text style={styles.escrowWarningText}>
                  KryptoPesa will hold crypto in escrow. You'll handle fiat payment externally.
                </Text>
              </View>
            </View>

            <View style={styles.confirmModalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setConfirmModalVisible(false)}
                disabled={creating}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.confirmButton, creating && styles.disabledButton]}
                onPress={confirmTrade}
                disabled={creating}
              >
                {creating ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.confirmButtonText}>Create Trade</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  offerTypeBadge: {
    alignSelf: 'center',
    backgroundColor: theme.colors.primaryLight + '40',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 16,
    borderWidth: 1,
    borderColor: theme.colors.primaryLight,
  },
  offerTypeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  buyText: {
    color: theme.colors.success,
  },
  sellText: {
    color: theme.colors.error,
  },
  section: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  priceContainer: {
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.text,
  },
  priceSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  limitsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  limitItem: {
    flex: 1,
    alignItems: 'center',
  },
  limitSeparator: {
    width: 1,
    height: 40,
    backgroundColor: theme.colors.border,
    marginHorizontal: 16,
  },
  limitLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  limitValue: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  paymentMethodsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  paymentMethodTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primaryLight + '40',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.primaryLight,
    gap: 6,
  },
  paymentMethodText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  traderContainer: {
    gap: 12,
  },
  traderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  traderAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  traderAvatarText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  traderInfo: {
    flex: 1,
  },
  traderNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 4,
  },
  traderName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  onlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.success,
  },
  traderStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  lastSeen: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  termsText: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: 12,
  },
  timeLimitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: theme.colors.warning + '40',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  timeLimitText: {
    fontSize: 12,
    color: theme.colors.warning,
    fontWeight: '500',
  },
  tradeForm: {
    gap: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  amountInput: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    fontSize: 16,
    color: theme.colors.text,
  },
  conversionContainer: {
    backgroundColor: theme.colors.info + '20',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.info + '60',
  },
  conversionText: {
    fontSize: 14,
    color: theme.colors.info,
    fontWeight: '500',
    textAlign: 'center',
  },
  paymentMethodSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  paymentMethodSelectorText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  placeholderText: {
    color: theme.colors.textSecondary,
  },
  startTradeButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 8,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  disabledButton: {
    backgroundColor: theme.colors.border,
    shadowOpacity: 0,
    elevation: 0,
  },
  startTradeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  offerMeta: {
    alignItems: 'center',
    paddingVertical: 16,
    marginTop: 8,
  },
  offerMetaText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalScrollView: {
    maxHeight: 300,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalOptionSelected: {
    backgroundColor: theme.colors.primaryLight + '40',
  },
  modalOptionText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  modalOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  confirmModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  confirmModalContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  confirmModalHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  confirmModalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 12,
  },
  confirmModalBody: {
    gap: 12,
    marginBottom: 24,
  },
  confirmRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  confirmLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  confirmValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  confirmModalFooter: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: theme.colors.backgroundSecondary,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
  },
  confirmButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  // New styles for East African features
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 12,
    textAlign: 'center',
  },
  errorSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  locationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  flagText: {
    fontSize: 16,
    marginRight: 6,
  },
  locationText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  cryptoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cryptoIconText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  priceInfo: {
    flex: 1,
  },
  paymentMethodsDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 12,
    fontStyle: 'italic',
  },
  paymentMethodIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  traderJoined: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  termsDetails: {
    marginTop: 12,
  },
  termItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  termText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 8,
  },
  noTermsText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  conversionSubText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  paymentMethodDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  escrowNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.success + '20',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  escrowNoticeText: {
    fontSize: 12,
    color: theme.colors.success,
    marginLeft: 8,
    flex: 1,
  },
  ownOfferNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.info + '20',
    padding: 16,
    borderRadius: 8,
  },
  ownOfferNoticeText: {
    fontSize: 14,
    color: theme.colors.info,
    marginLeft: 12,
    flex: 1,
  },
  modalNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.info + '20',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  modalNoticeText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
  modalOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  modalOptionInfo: {
    flex: 1,
  },
  modalOptionSubText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  confirmPaymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  escrowWarning: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.warning + '20',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  escrowWarningText: {
    fontSize: 12,
    color: theme.colors.warning,
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
});
