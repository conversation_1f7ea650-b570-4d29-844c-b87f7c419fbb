import { authTokens, API_ENDPOINTS, ApiResponse } from './api';

// Lazy import for apiClient to avoid early initialization
const getApiClient = () => {
  const { apiClient } = require('./api');
  return apiClient;
};

// Types for wallet authentication
export interface WalletAuthRequest {
  walletAddress: string;
  signature: string;
  message: string;
  timestamp: number;
}

export interface WalletAuthResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  user: {
    id: string;
    walletAddress: string;
    profileComplete: boolean;
    kycLevel: number;
    reputation: number;
  };
}

export interface SignatureVerificationRequest {
  walletAddress: string;
  signature: string;
  message: string;
}

// Wallet Authentication Service
class WalletAuthService {
  /**
   * Authenticate user with wallet signature
   */
  async authenticateWithWallet(authData: WalletAuthRequest): Promise<WalletAuthResponse> {
    try {
      const response = await getApiClient().post<WalletAuthResponse>(
        API_ENDPOINTS.WALLET_AUTH.AUTHENTICATE,
        authData
      );

      if (response.success && response.data) {
        // Store tokens and wallet address
        await authTokens.setTokens(
          response.data.accessToken,
          response.data.refreshToken,
          authData.walletAddress
        );

        return response.data;
      } else {
        throw new Error(response.message || 'Authentication failed');
      }
    } catch (error) {
      console.error('Wallet authentication error:', error);
      throw error;
    }
  }

  /**
   * Verify wallet signature without full authentication
   */
  async verifySignature(verificationData: SignatureVerificationRequest): Promise<boolean> {
    try {
      const response = await getApiClient().post<{ valid: boolean }>(
        API_ENDPOINTS.WALLET_AUTH.VERIFY,
        verificationData
      );

      return response.success && response.data?.valid === true;
    } catch (error) {
      console.error('Signature verification error:', error);
      return false;
    }
  }

  /**
   * Refresh authentication tokens
   */
  async refreshTokens(): Promise<WalletAuthResponse> {
    try {
      const response = await getApiClient().post<WalletAuthResponse>(
        API_ENDPOINTS.WALLET_AUTH.REFRESH
      );

      if (response.success && response.data) {
        // Update stored tokens
        await authTokens.setTokens(
          response.data.accessToken,
          response.data.refreshToken
        );

        return response.data;
      } else {
        throw new Error(response.message || 'Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      // Clear invalid tokens
      await authTokens.clearTokens();
      throw error;
    }
  }

  /**
   * Logout user and clear tokens
   */
  async logout(): Promise<void> {
    try {
      // Clear local tokens
      await authTokens.clearTokens();
      
      // Note: Backend doesn't require explicit logout for wallet auth
      // as tokens are stateless JWTs
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear tokens even if API call fails
      await authTokens.clearTokens();
    }
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    return authTokens.isAuthenticated();
  }

  /**
   * Get current wallet address
   */
  async getCurrentWalletAddress(): Promise<string | null> {
    return authTokens.getWalletAddress();
  }

  /**
   * Generate authentication message for signing
   * This creates a standardized message that the user signs with their wallet
   */
  generateAuthMessage(walletAddress: string, timestamp: number): string {
    return `KryptoPesa Authentication\n\nWallet: ${walletAddress}\nTimestamp: ${timestamp}\n\nBy signing this message, you authenticate with KryptoPesa using your wallet.`;
  }

  /**
   * Generate current timestamp for authentication
   */
  generateTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * Validate authentication message format
   */
  validateAuthMessage(message: string, walletAddress: string, timestamp: number): boolean {
    const expectedMessage = this.generateAuthMessage(walletAddress, timestamp);
    return message === expectedMessage;
  }

  /**
   * Check if timestamp is within acceptable range (5 minutes)
   */
  isTimestampValid(timestamp: number): boolean {
    const now = Math.floor(Date.now() / 1000);
    const fiveMinutes = 5 * 60;
    return Math.abs(now - timestamp) <= fiveMinutes;
  }

  /**
   * Prepare authentication data for wallet signing
   */
  prepareAuthData(walletAddress: string): {
    message: string;
    timestamp: number;
  } {
    const timestamp = this.generateTimestamp();
    const message = this.generateAuthMessage(walletAddress, timestamp);
    
    return { message, timestamp };
  }

  /**
   * Complete authentication after wallet signing
   */
  async completeAuthentication(
    walletAddress: string,
    signature: string,
    message: string,
    timestamp: number
  ): Promise<WalletAuthResponse> {
    // Validate inputs
    if (!this.validateAuthMessage(message, walletAddress, timestamp)) {
      throw new Error('Invalid authentication message');
    }

    if (!this.isTimestampValid(timestamp)) {
      throw new Error('Authentication timestamp expired');
    }

    // Perform authentication
    return this.authenticateWithWallet({
      walletAddress,
      signature,
      message,
      timestamp,
    });
  }
}

// Create and export service instance
export const walletAuthService = new WalletAuthService();

// Helper functions for wallet integration
export const walletAuthHelpers = {
  /**
   * Format wallet address for display
   */
  formatWalletAddress(address: string): string {
    if (address.length <= 10) return address;
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  },

  /**
   * Validate Ethereum wallet address format
   */
  isValidEthereumAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  },

  /**
   * Validate Bitcoin wallet address format (basic)
   */
  isValidBitcoinAddress(address: string): boolean {
    return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) || 
           /^bc1[a-z0-9]{39,59}$/.test(address);
  },

  /**
   * Determine wallet type from address
   */
  getWalletType(address: string): 'ethereum' | 'bitcoin' | 'unknown' {
    if (this.isValidEthereumAddress(address)) return 'ethereum';
    if (this.isValidBitcoinAddress(address)) return 'bitcoin';
    return 'unknown';
  },

  /**
   * Generate QR code data for wallet connection
   */
  generateWalletConnectData(walletAddress: string): string {
    const timestamp = Math.floor(Date.now() / 1000);
    return JSON.stringify({
      action: 'kryptopesa_auth',
      walletAddress,
      timestamp,
      domain: 'kryptopesa.com',
    });
  },
};

export default walletAuthService;
