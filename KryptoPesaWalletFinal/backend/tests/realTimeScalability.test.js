const request = require('supertest');
const { createServer } = require('http');
const { Server } = require('socket.io');
const Client = require('socket.io-client');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');

const app = require('../src/app');
const User = require('../src/models/User');
const Trade = require('../src/models/Trade');
const realTimeScalabilityService = require('../src/services/realTimeScalabilityService');
const connectionPoolManager = require('../src/services/connectionPoolManager');
const notificationService = require('../src/services/notificationService');
const enhancedSocketService = require('../src/services/enhancedSocketService');

describe('Real-Time Features Scalability', () => {
  let server;
  let io;
  let clientSocket1;
  let clientSocket2;
  let testUser1;
  let testUser2;
  let testTrade;
  let authToken1;
  let authToken2;

  beforeAll(async () => {
    // Create test server
    server = createServer(app);
    io = new Server(server);
    
    // Initialize services
    await realTimeScalabilityService.initialize();
    await connectionPoolManager.initialize();
    await notificationService.initialize();
    await enhancedSocketService.initialize(io);
    
    server.listen(0);
    
    // Create test users
    testUser1 = await User.create({
      username: 'testuser1',
      email: '<EMAIL>',
      password: 'password123',
      isVerified: true
    });
    
    testUser2 = await User.create({
      username: 'testuser2',
      email: '<EMAIL>',
      password: 'password123',
      isVerified: true
    });
    
    // Create test trade
    testTrade = await Trade.create({
      buyer: testUser1._id,
      seller: testUser2._id,
      cryptoCurrency: 'BTC',
      amount: 0.1,
      price: 50000,
      status: 'active'
    });
    
    // Generate auth tokens
    authToken1 = jwt.sign({ userId: testUser1._id }, process.env.JWT_SECRET);
    authToken2 = jwt.sign({ userId: testUser2._id }, process.env.JWT_SECRET);
  });

  afterAll(async () => {
    if (clientSocket1) clientSocket1.close();
    if (clientSocket2) clientSocket2.close();
    if (server) server.close();
    
    await realTimeScalabilityService.shutdown();
    await connectionPoolManager.shutdown();
    
    // Cleanup test data
    await User.deleteMany({ _id: { $in: [testUser1._id, testUser2._id] } });
    await Trade.deleteMany({ _id: testTrade._id });
  });

  beforeEach(async () => {
    const port = server.address().port;
    
    // Create client connections
    clientSocket1 = new Client(`http://localhost:${port}`, {
      auth: { token: authToken1 }
    });
    
    clientSocket2 = new Client(`http://localhost:${port}`, {
      auth: { token: authToken2 }
    });
    
    // Wait for connections
    await Promise.all([
      new Promise(resolve => clientSocket1.on('connect', resolve)),
      new Promise(resolve => clientSocket2.on('connect', resolve))
    ]);
  });

  afterEach(() => {
    if (clientSocket1) {
      clientSocket1.removeAllListeners();
      clientSocket1.close();
    }
    if (clientSocket2) {
      clientSocket2.removeAllListeners();
      clientSocket2.close();
    }
  });

  describe('Connection Pool Management', () => {
    test('should register user connections', async () => {
      const metrics = connectionPoolManager.getMetrics();
      expect(metrics.localConnections).toBeGreaterThan(0);
      expect(metrics.userConnections).toBeGreaterThan(0);
    });

    test('should track user presence', async () => {
      const isUser1Online = await connectionPoolManager.isUserOnline(testUser1._id);
      const isUser2Online = await connectionPoolManager.isUserOnline(testUser2._id);
      
      expect(isUser1Online).toBe(true);
      expect(isUser2Online).toBe(true);
    });

    test('should handle room subscriptions', (done) => {
      clientSocket1.emit('join_trade', { tradeId: testTrade._id });
      
      clientSocket1.on('joined_trade', (data) => {
        expect(data.tradeId).toBe(testTrade._id.toString());
        done();
      });
    });
  });

  describe('Real-Time Event Broadcasting', () => {
    test('should broadcast trade status updates', (done) => {
      const tradeId = testTrade._id.toString();
      
      // Both users join trade room
      clientSocket1.emit('join_trade', { tradeId });
      clientSocket2.emit('join_trade', { tradeId });
      
      let receivedCount = 0;
      const expectedData = {
        tradeId,
        status: 'payment_sent',
        data: { amount: 0.1 }
      };
      
      const checkComplete = () => {
        receivedCount++;
        if (receivedCount === 2) done();
      };
      
      clientSocket1.on('trade_status_update', (data) => {
        expect(data.tradeId).toBe(expectedData.tradeId);
        expect(data.status).toBe(expectedData.status);
        checkComplete();
      });
      
      clientSocket2.on('trade_status_update', (data) => {
        expect(data.tradeId).toBe(expectedData.tradeId);
        expect(data.status).toBe(expectedData.status);
        checkComplete();
      });
      
      // Simulate trade status update
      setTimeout(() => {
        realTimeScalabilityService.publishEvent('trading', 'trade_status_update', expectedData);
      }, 100);
    });

    test('should handle wallet balance updates', (done) => {
      const expectedData = {
        userId: testUser1._id.toString(),
        balances: { BTC: 1.5, ETH: 10.0 },
        currency: 'BTC'
      };
      
      clientSocket1.on('balance_update', (data) => {
        expect(data.balances).toEqual(expectedData.balances);
        expect(data.currency).toBe(expectedData.currency);
        done();
      });
      
      // Simulate balance update
      setTimeout(() => {
        realTimeScalabilityService.publishEvent('wallet', 'balance_update', expectedData);
      }, 100);
    });

    test('should handle system notifications', (done) => {
      const expectedData = {
        message: 'System maintenance scheduled',
        type: 'maintenance',
        targetUsers: null
      };
      
      let receivedCount = 0;
      const checkComplete = () => {
        receivedCount++;
        if (receivedCount === 2) done();
      };
      
      clientSocket1.on('system_notification', (data) => {
        expect(data.message).toBe(expectedData.message);
        expect(data.type).toBe(expectedData.type);
        checkComplete();
      });
      
      clientSocket2.on('system_notification', (data) => {
        expect(data.message).toBe(expectedData.message);
        expect(data.type).toBe(expectedData.type);
        checkComplete();
      });
      
      // Simulate system notification
      setTimeout(() => {
        realTimeScalabilityService.publishEvent('system', 'system_notification', expectedData);
      }, 100);
    });
  });

  describe('Chat Message Broadcasting', () => {
    test('should broadcast new messages to trade participants', (done) => {
      const tradeId = testTrade._id.toString();
      
      // Both users join trade room
      clientSocket1.emit('join_trade', { tradeId });
      clientSocket2.emit('join_trade', { tradeId });
      
      clientSocket2.on('new_message', (data) => {
        expect(data.message.content).toBe('Hello from user 1');
        expect(data.message.sender).toBe(testUser1._id.toString());
        done();
      });
      
      // User 1 sends message after joining
      setTimeout(() => {
        clientSocket1.emit('send_message', {
          tradeId,
          content: 'Hello from user 1',
          type: 'text'
        });
      }, 200);
    });

    test('should handle typing indicators', (done) => {
      const tradeId = testTrade._id.toString();
      
      clientSocket1.emit('join_trade', { tradeId });
      clientSocket2.emit('join_trade', { tradeId });
      
      clientSocket2.on('typing_indicator', (data) => {
        expect(data.userId).toBe(testUser1._id.toString());
        expect(data.isTyping).toBe(true);
        done();
      });
      
      setTimeout(() => {
        clientSocket1.emit('typing_start', { tradeId });
      }, 200);
    });
  });

  describe('Notification Service Integration', () => {
    test('should send real-time notifications to online users', async () => {
      const notification = {
        title: 'Test Notification',
        body: 'This is a test notification',
        type: 'test'
      };
      
      const result = await notificationService.sendRealTimeNotification(
        testUser1._id,
        notification
      );
      
      expect(result).toBe(true);
    });

    test('should queue notifications for offline users', async () => {
      const offlineUserId = new mongoose.Types.ObjectId();
      const notification = {
        title: 'Offline Notification',
        body: 'This notification is for an offline user',
        type: 'test'
      };
      
      const result = await notificationService.sendRealTimeNotification(
        offlineUserId,
        notification
      );
      
      expect(result).toBe(false); // User is offline
    });

    test('should send trade notifications', async () => {
      const result = await notificationService.sendTradeNotification(
        testTrade._id,
        testUser1._id,
        'trade_accepted',
        { cryptoCurrency: 'BTC' }
      );
      
      expect(result).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle multiple concurrent connections', async () => {
      const connections = [];
      const numConnections = 10;
      
      // Create multiple connections
      for (let i = 0; i < numConnections; i++) {
        const client = new Client(`http://localhost:${server.address().port}`, {
          auth: { token: authToken1 }
        });
        connections.push(client);
      }
      
      // Wait for all connections
      await Promise.all(
        connections.map(client => 
          new Promise(resolve => client.on('connect', resolve))
        )
      );
      
      const metrics = connectionPoolManager.getMetrics();
      expect(metrics.localConnections).toBeGreaterThanOrEqual(numConnections);
      
      // Cleanup
      connections.forEach(client => client.close());
    });

    test('should maintain performance under load', async () => {
      const startTime = Date.now();
      const numEvents = 100;
      
      // Send multiple events rapidly
      for (let i = 0; i < numEvents; i++) {
        await realTimeScalabilityService.publishEvent('system', 'test_event', {
          eventNumber: i,
          timestamp: new Date()
        });
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should process 100 events in less than 1 second
      expect(duration).toBeLessThan(1000);
    });

    test('should provide comprehensive metrics', () => {
      const metrics = enhancedSocketService.getMetrics();
      
      expect(metrics).toHaveProperty('socket');
      expect(metrics).toHaveProperty('chat');
      expect(metrics).toHaveProperty('realTime');
      expect(metrics).toHaveProperty('connectionPool');
      expect(metrics).toHaveProperty('notifications');
      expect(metrics).toHaveProperty('redis');
      
      expect(metrics.socket.connectedUsers).toBeGreaterThan(0);
      expect(metrics.realTime.isInitialized).toBe(true);
      expect(metrics.connectionPool.serverId).toBeDefined();
    });
  });

  describe('Error Handling and Resilience', () => {
    test('should handle Redis connection failures gracefully', async () => {
      // This test would require mocking Redis failures
      // For now, we'll test that the service can initialize without Redis
      const testService = require('../src/services/realTimeScalabilityService');
      
      // The service should handle Redis unavailability
      expect(testService.isInitialized).toBeDefined();
    });

    test('should handle malformed event data', async () => {
      const invalidEvent = {
        event: 'invalid_event',
        payload: null,
        serverId: 'test',
        timestamp: 'invalid-date'
      };
      
      // Should not throw error
      expect(() => {
        realTimeScalabilityService.handleRedisMessage(
          'test-channel',
          JSON.stringify(invalidEvent)
        );
      }).not.toThrow();
    });

    test('should cleanup stale connections', async () => {
      const initialConnections = connectionPoolManager.getMetrics().localConnections;
      
      // Simulate stale connection cleanup
      await connectionPoolManager.cleanupStaleConnections();
      
      // Should not crash and metrics should be valid
      const finalMetrics = connectionPoolManager.getMetrics();
      expect(finalMetrics.localConnections).toBeGreaterThanOrEqual(0);
    });
  });
});
