# KryptoPesa Testing Guide

## Overview
This document outlines the comprehensive testing strategy for the KryptoPesa platform, including unit tests, integration tests, and end-to-end testing procedures.

## Testing Stack
- **Backend**: Jest, Supertest, MongoDB Memory Server
- **Smart Contracts**: Hardhat, Waffle, Chai
- **Mobile App**: Jest, React Native Testing Library, Detox
- **Admin Dashboard**: Jest, React Testing Library, Cypress

## Backend Testing

### Setup
```bash
cd backend
npm install
npm test
```

### Unit Tests
Location: `backend/src/**/*.test.js`

#### Example: User Model Test
```javascript
// backend/src/models/__tests__/User.test.js
const User = require('../User');
const mongoose = require('mongoose');

describe('User Model', () => {
  beforeAll(async () => {
    await mongoose.connect(process.env.MONGODB_TEST_URI);
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    await User.deleteMany({});
  });

  test('should create a valid user', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      phone: '+254700000000',
      password: 'password123',
      profile: {
        firstName: 'Test',
        lastName: 'User',
        location: {
          country: 'KE',
          city: 'Nairobi'
        }
      }
    };

    const user = new User(userData);
    const savedUser = await user.save();

    expect(savedUser._id).toBeDefined();
    expect(savedUser.username).toBe(userData.username);
    expect(savedUser.password).not.toBe(userData.password); // Should be hashed
  });

  test('should calculate reputation score correctly', () => {
    const user = new User({
      reputation: {
        completedTrades: 10,
        cancelledTrades: 1,
        disputedTrades: 0,
        averageRating: 4.5
      }
    });

    const score = user.calculateReputationScore();
    expect(score).toBeGreaterThan(80);
  });
});
```

### Integration Tests
```javascript
// backend/src/routes/__tests__/auth.test.js
const request = require('supertest');
const { app } = require('../../server');

describe('Auth Routes', () => {
  test('POST /api/auth/register', async () => {
    const userData = {
      username: 'newuser',
      email: '<EMAIL>',
      phone: '+254700000001',
      password: 'password123',
      firstName: 'New',
      lastName: 'User',
      country: 'KE',
      city: 'Nairobi'
    };

    const response = await request(app)
      .post('/api/auth/register')
      .send(userData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.user.username).toBe(userData.username);
    expect(response.body.data.token).toBeDefined();
  });

  test('POST /api/auth/login', async () => {
    // First create a user
    const user = await User.create({
      username: 'loginuser',
      email: '<EMAIL>',
      phone: '+254700000002',
      password: 'password123',
      profile: {
        firstName: 'Login',
        lastName: 'User',
        location: { country: 'KE', city: 'Nairobi' }
      }
    });

    const response = await request(app)
      .post('/api/auth/login')
      .send({
        identifier: '<EMAIL>',
        password: 'password123'
      })
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.token).toBeDefined();
  });
});
```

## Smart Contract Testing

### Setup
```bash
cd smart-contracts
npm install
npm test
```

### Contract Tests
```javascript
// smart-contracts/test/KryptoPesaEscrow.test.js
const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("KryptoPesaEscrow", function () {
  let escrow, token, owner, seller, buyer, feeCollector;

  beforeEach(async function () {
    [owner, seller, buyer, feeCollector] = await ethers.getSigners();

    // Deploy mock token
    const MockToken = await ethers.getContractFactory("MockERC20");
    token = await MockToken.deploy("Test USDT", "TUSDT", 6);

    // Deploy escrow
    const KryptoPesaEscrow = await ethers.getContractFactory("KryptoPesaEscrow");
    escrow = await KryptoPesaEscrow.deploy(feeCollector.address, owner.address);

    // Setup
    await escrow.authorizeToken(token.address, true);
    await token.mint(seller.address, ethers.utils.parseUnits("1000", 6));
    await token.connect(seller).approve(escrow.address, ethers.utils.parseUnits("1000", 6));
  });

  describe("Trade Creation", function () {
    it("Should create a trade successfully", async function () {
      const amount = ethers.utils.parseUnits("100", 6);
      
      const tx = await escrow.connect(seller).createTrade(
        buyer.address,
        token.address,
        amount,
        10000, // 100 USD
        "USD",
        "Bank Transfer",
        ethers.utils.keccak256(ethers.utils.toUtf8Bytes("payment-details"))
      );

      const receipt = await tx.wait();
      const event = receipt.events.find(e => e.event === "TradeCreated");
      
      expect(event.args.seller).to.equal(seller.address);
      expect(event.args.buyer).to.equal(buyer.address);
      expect(event.args.amount).to.equal(amount);
    });
  });

  describe("Trade Execution", function () {
    let tradeId;

    beforeEach(async function () {
      const amount = ethers.utils.parseUnits("100", 6);
      const tx = await escrow.connect(seller).createTrade(
        buyer.address,
        token.address,
        amount,
        10000,
        "USD",
        "Bank Transfer",
        ethers.utils.keccak256(ethers.utils.toUtf8Bytes("payment-details"))
      );
      const receipt = await tx.wait();
      tradeId = receipt.events.find(e => e.event === "TradeCreated").args.tradeId;
    });

    it("Should complete full trade flow", async function () {
      // Fund trade
      await escrow.connect(seller).fundTrade(tradeId);
      
      // Buyer confirms payment sent
      await escrow.connect(buyer).confirmPaymentSent(tradeId);
      
      // Seller confirms payment received
      const tx = await escrow.connect(seller).confirmPaymentReceived(tradeId);
      
      // Check trade completed
      const receipt = await tx.wait();
      const event = receipt.events.find(e => e.event === "TradeCompleted");
      expect(event).to.not.be.undefined;
    });
  });
});
```

## Mobile App Testing

### Setup
```bash
cd mobile
npm install
npm test
```

### Component Tests
```javascript
// mobile/src/components/__tests__/LoadingScreen.test.js
import React from 'react';
import { render } from '@testing-library/react-native';
import LoadingScreen from '../LoadingScreen';

describe('LoadingScreen', () => {
  it('renders correctly with default message', () => {
    const { getByText } = render(<LoadingScreen />);
    expect(getByText('Loading...')).toBeTruthy();
  });

  it('renders correctly with custom message', () => {
    const customMessage = 'Please wait...';
    const { getByText } = render(<LoadingScreen message={customMessage} />);
    expect(getByText(customMessage)).toBeTruthy();
  });
});
```

### Redux Tests
```javascript
// mobile/src/store/slices/__tests__/authSlice.test.js
import authReducer, { loginUser } from '../authSlice';

describe('authSlice', () => {
  const initialState = {
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
    error: null
  };

  it('should handle initial state', () => {
    expect(authReducer(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('should handle loginUser.pending', () => {
    const action = { type: loginUser.pending.type };
    const state = authReducer(initialState, action);
    expect(state.isLoading).toBe(true);
    expect(state.error).toBe(null);
  });

  it('should handle loginUser.fulfilled', () => {
    const mockUser = { id: '1', username: 'testuser' };
    const mockToken = 'mock-token';
    const action = {
      type: loginUser.fulfilled.type,
      payload: { user: mockUser, token: mockToken }
    };
    const state = authReducer(initialState, action);
    expect(state.isLoading).toBe(false);
    expect(state.user).toEqual(mockUser);
    expect(state.token).toBe(mockToken);
    expect(state.isAuthenticated).toBe(true);
  });
});
```

### E2E Tests with Detox
```javascript
// mobile/e2e/auth.e2e.js
describe('Authentication Flow', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should show login screen', async () => {
    await expect(element(by.text('Welcome Back'))).toBeVisible();
  });

  it('should login successfully', async () => {
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('password123');
    await element(by.id('login-button')).tap();
    
    await expect(element(by.text('Dashboard'))).toBeVisible();
  });
});
```

## Admin Dashboard Testing

### Setup
```bash
cd admin-dashboard
npm install
npm test
```

### Component Tests
```javascript
// admin-dashboard/src/components/__tests__/Sidebar.test.js
import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import Sidebar from '../Sidebar';
import { AuthProvider } from '../../services/AuthContext';

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={{}}>
        <AuthProvider>
          {component}
        </AuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Sidebar', () => {
  it('renders navigation items', () => {
    renderWithProviders(<Sidebar />);
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('Trades')).toBeInTheDocument();
    expect(screen.getByText('Disputes')).toBeInTheDocument();
  });
});
```

### E2E Tests with Cypress
```javascript
// admin-dashboard/cypress/e2e/disputes.cy.js
describe('Dispute Management', () => {
  beforeEach(() => {
    cy.login('<EMAIL>', 'adminpassword');
    cy.visit('/disputes');
  });

  it('should display disputes list', () => {
    cy.get('[data-testid="disputes-table"]').should('be.visible');
    cy.get('[data-testid="dispute-row"]').should('have.length.greaterThan', 0);
  });

  it('should resolve a dispute', () => {
    cy.get('[data-testid="resolve-button"]').first().click();
    cy.get('[data-testid="resolution-dialog"]').should('be.visible');
    
    cy.get('[data-testid="resolution-select"]').click();
    cy.get('[data-value="favor_initiator"]').click();
    
    cy.get('[data-testid="reasoning-input"]').type('Evidence supports the initiator');
    cy.get('[data-testid="submit-resolution"]').click();
    
    cy.get('[data-testid="success-message"]').should('contain', 'Dispute resolved');
  });
});
```

## Performance Testing

### Load Testing with Artillery
```yaml
# load-test.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 50
    - duration: 60
      arrivalRate: 100

scenarios:
  - name: "API Load Test"
    flow:
      - post:
          url: "/api/auth/login"
          json:
            identifier: "<EMAIL>"
            password: "password123"
          capture:
            - json: "$.data.token"
              as: "token"
      - get:
          url: "/api/offers"
          headers:
            Authorization: "Bearer {{ token }}"
```

Run with: `artillery run load-test.yml`

## Security Testing

### Penetration Testing Checklist
- [ ] SQL Injection testing
- [ ] XSS vulnerability testing
- [ ] CSRF protection testing
- [ ] Authentication bypass testing
- [ ] Authorization testing
- [ ] Input validation testing
- [ ] Rate limiting testing
- [ ] SSL/TLS configuration testing

### Smart Contract Security
```bash
# Install security tools
npm install -g mythril
npm install -g slither-analyzer

# Run security analysis
mythril analyze smart-contracts/contracts/KryptoPesaEscrow.sol
slither smart-contracts/contracts/
```

## Test Coverage

### Backend Coverage
```bash
cd backend
npm run test:coverage
```

Target: 90%+ code coverage

### Mobile App Coverage
```bash
cd mobile
npm run test:coverage
```

Target: 85%+ code coverage

## Continuous Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: cd backend && npm ci
      - run: cd backend && npm test
      
  contract-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: cd smart-contracts && npm ci
      - run: cd smart-contracts && npm test
      
  mobile-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: cd mobile && npm ci
      - run: cd mobile && npm test
```

## Manual Testing Procedures

### User Registration & Authentication
1. Register new user with valid data
2. Verify email validation
3. Test login with various credentials
4. Test password reset flow
5. Test account lockout after failed attempts

### Wallet Operations
1. Create new wallet
2. Import existing wallet with mnemonic
3. Test balance refresh
4. Test transaction history
5. Test backup verification

### Trading Flow
1. Create trading offer
2. Browse and filter offers
3. Respond to offer
4. Complete trade flow
5. Test dispute creation and resolution

### Admin Functions
1. User management
2. Trade monitoring
3. Dispute resolution
4. System analytics
5. Configuration management
