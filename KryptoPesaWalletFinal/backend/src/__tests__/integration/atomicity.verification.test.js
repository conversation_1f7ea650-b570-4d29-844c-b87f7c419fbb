/**
 * Atomicity Implementation Verification Tests
 * Verifies that all multi-document operations use atomic transactions
 */

const fs = require('fs');
const path = require('path');

describe('Atomicity Implementation Verification', () => {
  
  describe('EscrowService Atomicity', () => {
    test('should use atomic transactions for escrow creation', () => {
      const escrowServicePath = path.join(__dirname, '../../services/escrowService.js');
      const escrowServiceContent = fs.readFileSync(escrowServicePath, 'utf8');
      
      // Verify that createEscrow uses dataConsistencyService.executeAtomicEscrowCreation
      expect(escrowServiceContent).toMatch(/dataConsistencyService\.executeAtomicEscrowCreation/);
      expect(escrowServiceContent).toMatch(/require\(['"]\.\/dataConsistency['"]\)/);
      
      // Verify that direct trade.save() is replaced with atomic operation
      const createEscrowMatch = escrowServiceContent.match(/createEscrow[\s\S]*?(?=async|\})/);
      if (createEscrowMatch) {
        const createEscrowMethod = createEscrowMatch[0];
        // Should NOT contain direct trade.save() after escrow creation
        expect(createEscrowMethod).not.toMatch(/trade\.save\(\)/);
      }
    });

    test('should use atomic transactions for escrow release', () => {
      const escrowServicePath = path.join(__dirname, '../../services/escrowService.js');
      const escrowServiceContent = fs.readFileSync(escrowServicePath, 'utf8');
      
      // Verify that releaseEscrow uses dataConsistencyService.executeAtomicEscrowRelease
      expect(escrowServiceContent).toMatch(/dataConsistencyService\.executeAtomicEscrowRelease/);
      
      // Verify that direct trade.save() is replaced with atomic operation
      const releaseEscrowMatch = escrowServiceContent.match(/releaseEscrow[\s\S]*?(?=async|\})/);
      if (releaseEscrowMatch) {
        const releaseEscrowMethod = releaseEscrowMatch[0];
        // Should NOT contain direct trade.save() after escrow release
        expect(releaseEscrowMethod).not.toMatch(/trade\.save\(\)/);
      }
    });
  });

  describe('Trade Routes Atomicity', () => {
    test('should use atomic transactions for dispute creation', () => {
      const tradeRoutesPath = path.join(__dirname, '../../routes/trade.js');
      const tradeRoutesContent = fs.readFileSync(tradeRoutesPath, 'utf8');
      
      // Verify that dispute creation uses dataConsistencyService.executeAtomicDisputeCreation
      expect(tradeRoutesContent).toMatch(/dataConsistencyService\.executeAtomicDisputeCreation/);
      expect(tradeRoutesContent).toMatch(/require\(['"]\.\.\/services\/dataConsistency['"]\)/);
      
      // Verify that separate dispute.save() and trade.updateStatus() are replaced
      const disputeCreationMatch = tradeRoutesContent.match(/\/disputes[\s\S]*?(?=router\.|\})/);
      if (disputeCreationMatch) {
        const disputeCreationRoute = disputeCreationMatch[0];
        // Should NOT contain separate dispute.save() and trade.updateStatus()
        expect(disputeCreationRoute).not.toMatch(/dispute\.save\(\)[\s\S]*?trade\.updateStatus/);
      }
    });
  });

  describe('WalletService Atomicity', () => {
    test('should use atomic transactions for balance updates', () => {
      const walletServicePath = path.join(__dirname, '../../services/walletService.js');
      const walletServiceContent = fs.readFileSync(walletServicePath, 'utf8');
      
      // Verify that updateBalances uses dataConsistencyService.executeAtomicBalanceUpdate
      expect(walletServiceContent).toMatch(/dataConsistencyService\.executeAtomicBalanceUpdate/);
      expect(walletServiceContent).toMatch(/require\(['"]\.\/dataConsistency['"]\)/);
      
      // Verify that multiple separate wallet.updateBalance() calls are replaced
      const updateBalancesMatch = walletServiceContent.match(/updateBalances[\s\S]*?(?=async|\})/);
      if (updateBalancesMatch) {
        const updateBalancesMethod = updateBalancesMatch[0];
        // Should collect balance updates and use atomic operation
        expect(updateBalancesMethod).toMatch(/balanceUpdates/);
        expect(updateBalancesMethod).toMatch(/executeAtomicBalanceUpdate/);
      }
    });

    test('should use atomic transactions for transaction addition with balance updates', () => {
      const walletServicePath = path.join(__dirname, '../../services/walletService.js');
      const walletServiceContent = fs.readFileSync(walletServicePath, 'utf8');
      
      // Verify that addTransaction can use atomic operations when balance updates are included
      const addTransactionMatch = walletServiceContent.match(/addTransaction[\s\S]*?(?=async|\})/);
      if (addTransactionMatch) {
        const addTransactionMethod = addTransactionMatch[0];
        // Should check for balance updates and use atomic operation when needed
        expect(addTransactionMethod).toMatch(/balanceUpdate/);
        expect(addTransactionMethod).toMatch(/executeAtomicBalanceUpdate/);
      }
    });
  });

  describe('TradingService Atomicity', () => {
    test('should use atomic transactions for trade creation', () => {
      const tradingServicePath = path.join(__dirname, '../../services/tradingService.js');
      const tradingServiceContent = fs.readFileSync(tradingServicePath, 'utf8');
      
      // Verify that acceptOffer uses dataConsistencyService.executeAtomicTradeCreation
      expect(tradingServiceContent).toMatch(/dataConsistencyService\.executeAtomicTradeCreation/);
      expect(tradingServiceContent).toMatch(/require\(['"]\.\/dataConsistency['"]\)/);
      
      // Verify that separate trade.save() calls are replaced
      const acceptOfferMatch = tradingServiceContent.match(/acceptOffer[\s\S]*?(?=async|\})/);
      if (acceptOfferMatch) {
        const acceptOfferMethod = acceptOfferMatch[0];
        // Should NOT contain multiple trade.save() calls
        const tradeSaveCalls = (acceptOfferMethod.match(/trade\.save\(\)/g) || []).length;
        expect(tradeSaveCalls).toBeLessThanOrEqual(1); // At most one for population
      }
    });
  });

  describe('DataConsistencyService Integration', () => {
    test('should have all required atomic operation methods', () => {
      const dataConsistencyPath = path.join(__dirname, '../../services/dataConsistency.js');
      const dataConsistencyContent = fs.readFileSync(dataConsistencyPath, 'utf8');
      
      // Verify all atomic operation methods exist
      expect(dataConsistencyContent).toMatch(/executeAtomicTradeCreation/);
      expect(dataConsistencyContent).toMatch(/executeAtomicEscrowCreation/);
      expect(dataConsistencyContent).toMatch(/executeAtomicEscrowRelease/);
      expect(dataConsistencyContent).toMatch(/executeAtomicDisputeCreation/);
      expect(dataConsistencyContent).toMatch(/executeAtomicBalanceUpdate/);
    });

    test('should export dataConsistencyService instance', () => {
      const dataConsistencyPath = path.join(__dirname, '../../services/dataConsistency.js');
      const dataConsistencyContent = fs.readFileSync(dataConsistencyPath, 'utf8');
      
      // Verify that the service is properly exported
      expect(dataConsistencyContent).toMatch(/module\.exports.*dataConsistencyService/);
    });
  });

  describe('Model Transaction Support', () => {
    test('should verify models support session parameters', () => {
      const modelsDir = path.join(__dirname, '../../models');
      const modelFiles = ['Trade.js', 'Chat.js', 'Offer.js', 'Dispute.js', 'Wallet.js'];
      
      modelFiles.forEach(modelFile => {
        const modelPath = path.join(modelsDir, modelFile);
        if (fs.existsSync(modelPath)) {
          const modelContent = fs.readFileSync(modelPath, 'utf8');
          
          // Check for session parameter support in key methods
          const hasSessionSupport = 
            modelContent.includes('session') || 
            modelContent.includes('Session') ||
            modelContent.includes('transaction');
            
          // At minimum, models should be compatible with transactions
          expect(modelContent).toBeDefined();
        }
      });
    });
  });

  describe('Atomic Operation Coverage', () => {
    test('should verify no remaining non-atomic multi-document operations', () => {
      const servicePaths = [
        '../../services/escrowService.js',
        '../../services/walletService.js', 
        '../../services/tradingService.js',
        '../../routes/trade.js'
      ];

      servicePaths.forEach(servicePath => {
        const fullPath = path.join(__dirname, servicePath);
        if (fs.existsSync(fullPath)) {
          const content = fs.readFileSync(fullPath, 'utf8');
          
          // Look for patterns that might indicate non-atomic operations
          const suspiciousPatterns = [
            // Multiple saves without transaction context
            /await\s+\w+\.save\(\)[\s\S]*?await\s+\w+\.save\(\)/,
            // Save followed by updateStatus without atomic wrapper
            /\.save\(\)[\s\S]*?\.updateStatus\(/,
            // Multiple model updates without transaction
            /await\s+\w+\.update[\s\S]*?await\s+\w+\.update/
          ];

          suspiciousPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches) {
              // If we find suspicious patterns, they should be in test files or comments
              const isInComment = matches[0].includes('//') || matches[0].includes('/*');
              const isInTestContext = content.includes('jest') || content.includes('test') || content.includes('mock');
              
              if (!isInComment && !isInTestContext) {
                console.warn(`Potential non-atomic operation found in ${servicePath}:`, matches[0].substring(0, 100));
              }
            }
          });
        }
      });
    });
  });

  describe('Transaction Error Handling', () => {
    test('should verify atomic operations have proper error handling', () => {
      const dataConsistencyPath = path.join(__dirname, '../../services/dataConsistency.js');
      const dataConsistencyContent = fs.readFileSync(dataConsistencyPath, 'utf8');
      
      // Verify error handling patterns
      expect(dataConsistencyContent).toMatch(/try[\s\S]*?catch/);
      expect(dataConsistencyContent).toMatch(/session\.abortTransaction/);
      expect(dataConsistencyContent).toMatch(/session\.endSession/);
    });
  });

  describe('Performance Considerations', () => {
    test('should verify atomic operations use appropriate session management', () => {
      const dataConsistencyPath = path.join(__dirname, '../../services/dataConsistency.js');
      const dataConsistencyContent = fs.readFileSync(dataConsistencyPath, 'utf8');
      
      // Verify session lifecycle management
      expect(dataConsistencyContent).toMatch(/startSession/);
      expect(dataConsistencyContent).toMatch(/startTransaction/);
      expect(dataConsistencyContent).toMatch(/commitTransaction/);
      expect(dataConsistencyContent).toMatch(/endSession/);
    });
  });
});
