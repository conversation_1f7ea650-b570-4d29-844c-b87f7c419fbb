/**
 * Production Security Configuration for KryptoPesa
 * Enterprise-grade security settings and validation
 */

const crypto = require('crypto');

class SecurityConfig {
  constructor() {
    this.validateSecurityRequirements();
  }

  validateSecurityRequirements() {
    const errors = [];

    // Validate encryption key strength
    if (!process.env.ENCRYPTION_KEY || process.env.ENCRYPTION_KEY.length < 32) {
      errors.push('ENCRYPTION_KEY must be at least 32 characters long');
    }

    // Validate JWT secret strength
    if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
      errors.push('JWT_SECRET must be at least 32 characters long');
    }

    // Validate session secret strength
    if (!process.env.SESSION_SECRET || process.env.SESSION_SECRET.length < 32) {
      errors.push('SESSION_SECRET must be at least 32 characters long');
    }

    // Check for production-specific security requirements
    if (process.env.NODE_ENV === 'production') {
      if (!process.env.SSL_CERT_PATH || !process.env.SSL_KEY_PATH) {
        errors.push('SSL certificate paths must be configured for production');
      }

      if (!process.env.CORS_ORIGINS || process.env.CORS_ORIGINS.includes('*')) {
        errors.push('CORS_ORIGINS must be explicitly configured (no wildcards) for production');
      }
    }

    if (errors.length > 0) {
      throw new Error(`Security configuration errors: ${errors.join(', ')}`);
    }
  }

  getSecurityHeaders() {
    return {
      // Content Security Policy
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          scriptSrc: ["'self'"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "wss:", "https:"],
          mediaSrc: ["'self'"],
          objectSrc: ["'none'"],
          childSrc: ["'none'"],
          frameSrc: ["'none'"],
          workerSrc: ["'self'"],
          manifestSrc: ["'self'"],
          baseUri: ["'self'"],
          formAction: ["'self'"],
          frameAncestors: ["'none'"],
          upgradeInsecureRequests: []
        }
      },

      // HTTP Strict Transport Security
      hsts: {
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true
      },

      // Other security headers
      noSniff: true,
      frameguard: { action: 'deny' },
      xssFilter: true,
      referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
      crossOriginEmbedderPolicy: false,
      crossOriginOpenerPolicy: { policy: 'same-origin' },
      crossOriginResourcePolicy: { policy: 'cross-origin' },
      originAgentCluster: true,
      dnsPrefetchControl: { allow: false },
      ieNoOpen: true,
      permittedCrossDomainPolicies: false
    };
  }

  getRateLimitConfig() {
    return {
      // General API rate limit
      general: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 1000,
        message: {
          error: 'Too many requests from this IP, please try again later',
          retryAfter: '15 minutes'
        },
        standardHeaders: true,
        legacyHeaders: false
      },

      // Authentication endpoints (stricter)
      auth: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: parseInt(process.env.RATE_LIMIT_AUTH_MAX) || 10,
        message: {
          error: 'Too many authentication attempts, account temporarily locked',
          retryAfter: '15 minutes'
        },
        skipSuccessfulRequests: true,
        skipFailedRequests: false
      },

      // Password reset (very strict)
      passwordReset: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 3,
        message: {
          error: 'Too many password reset attempts, please try again later',
          retryAfter: '1 hour'
        }
      },

      // Trading endpoints
      trading: {
        windowMs: 1 * 60 * 1000, // 1 minute
        max: 100,
        message: {
          error: 'Trading rate limit exceeded, please slow down',
          retryAfter: '1 minute'
        }
      },

      // Wallet endpoints
      wallet: {
        windowMs: 1 * 60 * 1000, // 1 minute
        max: 50,
        message: {
          error: 'Wallet rate limit exceeded, please slow down',
          retryAfter: '1 minute'
        }
      }
    };
  }

  getCORSConfig() {
    const allowedOrigins = process.env.CORS_ORIGINS 
      ? process.env.CORS_ORIGINS.split(',').map(origin => origin.trim())
      : ['http://localhost:3000']; // Development fallback

    return {
      origin: function (origin, callback) {
        // Allow requests with no origin (mobile apps, etc.)
        if (!origin) return callback(null, true);
        
        if (allowedOrigins.indexOf(origin) !== -1) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-API-Key',
        'X-Request-ID'
      ],
      exposedHeaders: ['X-Request-ID', 'X-Response-Time'],
      maxAge: 86400 // 24 hours
    };
  }

  getEncryptionConfig() {
    return {
      algorithm: 'aes-256-gcm',
      keyDerivation: 'pbkdf2',
      iterations: 100000,
      keyLength: 32,
      ivLength: 16,
      tagLength: 16,
      saltLength: 32
    };
  }

  getPasswordConfig() {
    return {
      bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      maxLength: 128,
      preventCommonPasswords: true,
      passwordHistory: 5
    };
  }

  getJWTConfig() {
    return {
      secret: process.env.JWT_SECRET,
      algorithm: 'HS256',
      expiresIn: process.env.JWT_EXPIRES_IN || '15m',
      refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
      issuer: 'kryptopesa.com',
      audience: 'kryptopesa-users'
    };
  }

  getSessionConfig() {
    return {
      secret: process.env.SESSION_SECRET,
      name: 'kryptopesa.sid',
      resave: false,
      saveUninitialized: false,
      rolling: true,
      cookie: {
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: 'strict'
      }
    };
  }

  getCSRFConfig() {
    return {
      secret: process.env.CSRF_SECRET || process.env.SESSION_SECRET,
      cookie: {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      }
    };
  }

  // Security utility methods
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  hashPassword(password) {
    const bcrypt = require('bcrypt');
    return bcrypt.hash(password, this.getPasswordConfig().bcryptRounds);
  }

  verifyPassword(password, hash) {
    const bcrypt = require('bcrypt');
    return bcrypt.compare(password, hash);
  }

  encrypt(text, key = process.env.ENCRYPTION_KEY) {
    try {
      const algorithm = 'aes-256-cbc';
      const iv = crypto.randomBytes(16);
      const salt = crypto.randomBytes(32);

      const derivedKey = crypto.pbkdf2Sync(key || 'default-key', salt, 10000, 32, 'sha256');
      const cipher = crypto.createCipheriv(algorithm, derivedKey, iv);

      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return {
        encrypted,
        iv: iv.toString('hex'),
        salt: salt.toString('hex'),
        algorithm
      };
    } catch (error) {
      throw new Error('Encryption failed: ' + error.message);
    }
  }

  decrypt(encryptedData, key = process.env.ENCRYPTION_KEY) {
    try {
      const config = this.getEncryptionConfig();
      const { encrypted, iv, salt, tag, algorithm } = encryptedData;

      const derivedKey = crypto.pbkdf2Sync(key, Buffer.from(salt, 'hex'), config.iterations, config.keyLength, 'sha256');
      const decipher = crypto.createDecipherGCM(algorithm || config.algorithm, derivedKey, Buffer.from(iv, 'hex'));
      decipher.setAAD(Buffer.from('kryptopesa'));
      decipher.setAuthTag(Buffer.from(tag, 'hex'));

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      throw new Error('Decryption failed: ' + error.message);
    }
  }

  decrypt(encryptedData, key = process.env.ENCRYPTION_KEY) {
    try {
      const { encrypted, iv, salt, algorithm } = encryptedData;

      const derivedKey = crypto.pbkdf2Sync(key || 'default-key', Buffer.from(salt, 'hex'), 10000, 32, 'sha256');
      const decipher = crypto.createDecipheriv(algorithm || 'aes-256-cbc', derivedKey, Buffer.from(iv, 'hex'));

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      throw new Error('Decryption failed: ' + error.message);
    }
  }

  // Security validation methods
  validatePasswordStrength(password) {
    const config = this.getPasswordConfig();
    const errors = [];

    if (password.length < config.minLength) {
      errors.push(`Password must be at least ${config.minLength} characters long`);
    }

    if (password.length > config.maxLength) {
      errors.push(`Password must be no more than ${config.maxLength} characters long`);
    }

    if (config.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (config.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (config.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (config.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/vbscript:/gi, '')
      .replace(/onload\s*=/gi, '')
      .replace(/onerror\s*=/gi, '')
      .replace(/onclick\s*=/gi, '')
      .trim();
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidPhoneNumber(phone) {
    // East African phone number validation
    const phoneRegex = /^\+254[0-9]{9}$|^\+256[0-9]{9}$|^\+255[0-9]{9}$|^\+250[0-9]{9}$/;
    return phoneRegex.test(phone);
  }
}

module.exports = new SecurityConfig();
