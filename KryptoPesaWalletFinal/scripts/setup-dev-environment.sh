#!/bin/bash

# KryptoPesa Development Environment Setup Script
# This script sets up the complete local development environment

set -e  # Exit on any error

echo "🚀 Setting up KryptoPesa Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker Desktop."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose."
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node -v)"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Create environment file
create_env_file() {
    print_status "Creating environment file..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created .env file from .env.example"
        print_warning "Please update the .env file with your specific configuration"
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Backend dependencies
    print_status "Installing backend dependencies..."
    cd backend && npm install && cd ..
    
    # Mobile dependencies
    print_status "Installing mobile dependencies..."
    cd mobile && npm install && cd ..
    
    # Admin dashboard dependencies
    print_status "Installing admin dashboard dependencies..."
    cd admin-dashboard && npm install && cd ..
    
    # Smart contracts dependencies
    print_status "Installing smart contracts dependencies..."
    cd smart-contracts && npm install && cd ..
    
    print_success "All dependencies installed"
}

# Start Docker services
start_docker_services() {
    print_status "Starting Docker services..."
    
    # Stop any existing containers
    docker-compose down
    
    # Start database services first
    print_status "Starting MongoDB and Redis..."
    docker-compose up -d mongodb redis
    
    # Wait for databases to be ready
    print_status "Waiting for databases to be ready..."
    sleep 10
    
    # Check MongoDB connection
    until docker-compose exec mongodb mongosh --eval "print('MongoDB is ready')" &> /dev/null; do
        print_status "Waiting for MongoDB..."
        sleep 2
    done
    
    # Check Redis connection
    until docker-compose exec redis redis-cli ping &> /dev/null; do
        print_status "Waiting for Redis..."
        sleep 2
    done
    
    print_success "Database services are ready"
}

# Deploy smart contracts
deploy_smart_contracts() {
    print_status "Deploying smart contracts to local Hardhat network..."
    
    cd smart-contracts
    
    # Start Hardhat node in background
    print_status "Starting Hardhat node..."
    npx hardhat node --hostname 0.0.0.0 &
    HARDHAT_PID=$!
    
    # Wait for Hardhat node to start
    sleep 5
    
    # Deploy contracts
    print_status "Deploying contracts..."
    npx hardhat run scripts/deploy.js --network localhost
    
    # Save Hardhat PID for later cleanup
    echo $HARDHAT_PID > .hardhat.pid
    
    cd ..
    
    print_success "Smart contracts deployed"
}

# Start backend services
start_backend_services() {
    print_status "Starting backend services..."
    
    # Start backend API
    cd backend
    npm run dev &
    BACKEND_PID=$!
    echo $BACKEND_PID > .backend.pid
    cd ..
    
    # Wait for backend to start
    print_status "Waiting for backend API to start..."
    sleep 5
    
    # Check backend health
    until curl -f http://localhost:3000/health &> /dev/null; do
        print_status "Waiting for backend API..."
        sleep 2
    done
    
    print_success "Backend API is running"
}

# Start admin dashboard
start_admin_dashboard() {
    print_status "Starting admin dashboard..."
    
    cd admin-dashboard
    npm start &
    ADMIN_PID=$!
    echo $ADMIN_PID > .admin.pid
    cd ..
    
    print_success "Admin dashboard is starting (will be available at http://localhost:3001)"
}

# Build mobile app
build_mobile_app() {
    print_status "Preparing mobile app..."
    
    cd mobile
    
    # Create standalone build configuration
    print_status "Creating standalone build configuration..."
    
    # Create app.json for standalone build
    cat > app.json << EOF
{
  "expo": {
    "name": "KryptoPesa",
    "slug": "kryptopesa",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "light",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.kryptopesa.app"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#FFFFFF"
      },
      "package": "com.kryptopesa.app"
    },
    "web": {
      "favicon": "./assets/favicon.png"
    },
    "plugins": [
      "@react-native-firebase/app",
      "@react-native-firebase/messaging",
      [
        "expo-build-properties",
        {
          "android": {
            "enableProguardInReleaseBuilds": true
          }
        }
      ]
    ]
  }
}
EOF
    
    print_success "Mobile app configuration created"
    
    cd ..
}

# Create development scripts
create_dev_scripts() {
    print_status "Creating development scripts..."
    
    # Create start script
    cat > start-dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting KryptoPesa Development Environment..."

# Start Docker services
docker-compose up -d mongodb redis

# Start Hardhat node
cd smart-contracts && npx hardhat node --hostname 0.0.0.0 &
cd ..

# Start backend
cd backend && npm run dev &
cd ..

# Start admin dashboard
cd admin-dashboard && npm start &
cd ..

echo "✅ Development environment started!"
echo "📱 Mobile app: cd mobile && npm start"
echo "🌐 Admin dashboard: http://localhost:3001"
echo "🔗 Backend API: http://localhost:3000"
echo "⛓️ Hardhat node: http://localhost:8545"
EOF

    chmod +x start-dev.sh
    
    # Create stop script
    cat > stop-dev.sh << 'EOF'
#!/bin/bash
echo "🛑 Stopping KryptoPesa Development Environment..."

# Stop Docker services
docker-compose down

# Kill background processes
if [ -f smart-contracts/.hardhat.pid ]; then
    kill $(cat smart-contracts/.hardhat.pid) 2>/dev/null || true
    rm smart-contracts/.hardhat.pid
fi

if [ -f backend/.backend.pid ]; then
    kill $(cat backend/.backend.pid) 2>/dev/null || true
    rm backend/.backend.pid
fi

if [ -f admin-dashboard/.admin.pid ]; then
    kill $(cat admin-dashboard/.admin.pid) 2>/dev/null || true
    rm admin-dashboard/.admin.pid
fi

# Kill any remaining processes
pkill -f "hardhat node" || true
pkill -f "npm run dev" || true
pkill -f "npm start" || true

echo "✅ Development environment stopped!"
EOF

    chmod +x stop-dev.sh
    
    # Create test script
    cat > test-all.sh << 'EOF'
#!/bin/bash
echo "🧪 Running all tests..."

# Test smart contracts
echo "Testing smart contracts..."
cd smart-contracts && npm test && cd ..

# Test backend
echo "Testing backend..."
cd backend && npm test && cd ..

# Test mobile app
echo "Testing mobile app..."
cd mobile && npm test && cd ..

echo "✅ All tests completed!"
EOF

    chmod +x test-all.sh
    
    print_success "Development scripts created"
}

# Display final instructions
display_instructions() {
    print_success "🎉 KryptoPesa Development Environment Setup Complete!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Update the .env file with your configuration"
    echo "2. Start the development environment: ./start-dev.sh"
    echo "3. Start the mobile app: cd mobile && npm start"
    echo ""
    echo "🔗 Service URLs:"
    echo "• Backend API: http://localhost:3000"
    echo "• Admin Dashboard: http://localhost:3001"
    echo "• Hardhat Node: http://localhost:8545"
    echo "• MongoDB: mongodb://localhost:27017"
    echo "• Redis: redis://localhost:6379"
    echo ""
    echo "🛠️ Useful Commands:"
    echo "• Start all services: ./start-dev.sh"
    echo "• Stop all services: ./stop-dev.sh"
    echo "• Run all tests: ./test-all.sh"
    echo "• View logs: docker-compose logs -f"
    echo ""
    echo "📱 Mobile Development:"
    echo "• For Android: npm run android (in mobile directory)"
    echo "• For iOS: npm run ios (in mobile directory)"
    echo "• For web: npm run web (in mobile directory)"
    echo ""
    print_warning "Remember to configure Firebase for push notifications in production!"
}

# Main execution
main() {
    check_prerequisites
    create_env_file
    install_dependencies
    start_docker_services
    deploy_smart_contracts
    start_backend_services
    start_admin_dashboard
    build_mobile_app
    create_dev_scripts
    display_instructions
}

# Run main function
main
