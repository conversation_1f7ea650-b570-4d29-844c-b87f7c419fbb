import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Clipboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import {
  walletService,
  tradingService,
  userService,
  EAST_AFRICAN_CONFIG,
  Transaction,
  eastAfricanHelpers
} from '../../services';

export default function TransactionDetailScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { transactionId } = route.params || {};

  // State management
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [relatedTrade, setRelatedTrade] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load transaction data
  const loadTransactionData = async (showLoading = true) => {
    if (!transactionId) {
      setError('Transaction ID not provided');
      setLoading(false);
      return;
    }

    try {
      if (showLoading) setLoading(true);
      setError(null);

      const [txData, profile] = await Promise.all([
        walletService.getTransaction(transactionId),
        userService.getProfile(),
      ]);

      setTransaction(txData);
      setUserProfile(profile);

      // Load related trade if exists
      if (txData.relatedTradeId) {
        try {
          const tradeData = await tradingService.getTrade(txData.relatedTradeId);
          setRelatedTrade(tradeData);
        } catch (err) {
          console.log('No related trade found or access denied');
        }
      }
    } catch (err) {
      console.error('Failed to load transaction:', err);
      setError(err instanceof Error ? err.message : 'Failed to load transaction');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadTransactionData();
  }, [transactionId]);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadTransactionData(false);
    setRefreshing(false);
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'confirmed':
        return theme.colors.success;
      case 'pending':
      case 'unconfirmed':
        return theme.colors.warning;
      case 'failed':
      case 'rejected':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'confirmed':
        return 'checkmark-circle';
      case 'pending':
      case 'unconfirmed':
        return 'time';
      case 'failed':
      case 'rejected':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'send':
      case 'sent':
        return 'arrow-up';
      case 'receive':
      case 'received':
        return 'arrow-down';
      case 'trade':
        return 'swap-horizontal';
      case 'escrow':
        return 'shield';
      default:
        return 'help-circle';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'send':
      case 'sent':
        return theme.colors.error;
      case 'receive':
      case 'received':
        return theme.colors.success;
      case 'trade':
      case 'escrow':
        return theme.colors.primary;
      default:
        return theme.colors.textSecondary;
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await Clipboard.setString(text);
      Alert.alert('Copied', `${label} copied to clipboard`);
    } catch (err) {
      Alert.alert('Error', 'Failed to copy to clipboard');
    }
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  const formatAddress = (address: string) => {
    if (address.length <= 16) return address;
    return `${address.slice(0, 8)}...${address.slice(-8)}`;
  };
  
  const handleShare = async () => {
    try {
      await Share.share({
        message: `Transaction Details\n\nAmount: ${transaction.amount} ${transaction.currency}\nStatus: ${transaction.status}\nTx Hash: ${transaction.txHash}\n\nView on blockchain explorer for more details.`,
        title: 'Transaction Details',
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };
  
  const handleCopyHash = () => {
    // In a real app, we would copy to clipboard
    Alert.alert('Copied', 'Transaction hash copied to clipboard');
  };
  
  const handleViewOnExplorer = () => {
    Alert.alert(
      'View on Explorer',
      'This would open the transaction in a blockchain explorer',
      [{ text: 'OK' }]
    );
  };
  
  const handleViewTrade = () => {
    if (transaction.relatedTradeId) {
      navigation.navigate('Trade', { tradeId: transaction.relatedTradeId });
    }
  };

  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Transaction Details</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading transaction details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !transaction) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Transaction Details</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load transaction</Text>
          <Text style={styles.errorSubText}>{error || 'Transaction not found'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadTransactionData()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>KryptoPesa Transaction</Text>
        <TouchableOpacity onPress={handleShare}>
          <Ionicons name="share-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {/* Status Section */}
        <View style={styles.statusSection}>
          <View style={[styles.statusIcon, { backgroundColor: getStatusColor(transaction.status) + '20' }]}>
            <Ionicons
              name={getStatusIcon(transaction.status)}
              size={32}
              color={getStatusColor(transaction.status)}
            />
          </View>
          <Text style={[styles.statusText, { color: getStatusColor(transaction.status) }]}>
            {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
          </Text>
          <Text style={styles.statusDescription}>
            {transaction.description || `${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} Transaction`}
          </Text>
        </View>

        {/* Amount Section */}
        <View style={styles.section}>
          <View style={styles.amountContainer}>
            <View style={styles.amountHeader}>
              <View style={[styles.typeIcon, { backgroundColor: getTypeColor(transaction.type) + '20' }]}>
                <Ionicons
                  name={getTypeIcon(transaction.type)}
                  size={20}
                  color={getTypeColor(transaction.type)}
                />
              </View>
              <Text style={styles.amountLabel}>
                {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} Amount
              </Text>
            </View>
            <Text style={[
              styles.amountValue,
              { color: getTypeColor(transaction.type) }
            ]}>
              {transaction.type === 'receive' ? '+' : transaction.type === 'send' ? '-' : ''}{transaction.amount} {transaction.currency}
            </Text>
            {transaction.fiatAmount && transaction.fiatCurrency && (
              <Text style={styles.fiatAmount}>
                ≈ {eastAfricanHelpers.formatCurrencyAmount(transaction.fiatAmount, transaction.fiatCurrency)}
              </Text>
            )}
          </View>
        </View>
        
        {/* Transaction Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Transaction Details</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Transaction ID</Text>
            <TouchableOpacity
              style={styles.hashContainer}
              onPress={() => copyToClipboard(transaction._id, 'Transaction ID')}
            >
              <Text style={styles.hashText}>{formatAddress(transaction._id)}</Text>
              <Ionicons name="copy-outline" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Date & Time</Text>
            <Text style={styles.detailValue}>{formatDate(transaction.timestamp)}</Text>
          </View>

          {transaction.txHash && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Blockchain Hash</Text>
              <TouchableOpacity
                style={styles.hashContainer}
                onPress={() => copyToClipboard(transaction.txHash, 'Transaction Hash')}
              >
                <Text style={styles.hashText}>{formatAddress(transaction.txHash)}</Text>
                <Ionicons name="copy-outline" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>
          )}

          {transaction.fee && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Network Fee</Text>
              <Text style={styles.detailValue}>{transaction.fee} {transaction.currency}</Text>
            </View>
          )}

          {transaction.confirmations !== undefined && transaction.requiredConfirmations && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Confirmations</Text>
              <View style={styles.confirmationContainer}>
                <Text style={[
                  styles.confirmationText,
                  transaction.confirmations >= transaction.requiredConfirmations
                    ? styles.confirmedText
                    : styles.pendingText
                ]}>
                  {transaction.confirmations}/{transaction.requiredConfirmations}
                </Text>
                {transaction.confirmations >= transaction.requiredConfirmations && (
                  <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
                )}
              </View>
            </View>
          )}

          {transaction.description && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Description</Text>
              <Text style={styles.detailValue}>{transaction.description}</Text>
            </View>
          )}
        </View>
        
        {/* Address Details */}
        {(transaction.fromAddress || transaction.toAddress) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Address Details</Text>

            {transaction.fromAddress && (
              <View style={styles.addressContainer}>
                <Text style={styles.addressLabel}>From</Text>
                <TouchableOpacity
                  style={styles.addressRow}
                  onPress={() => copyToClipboard(transaction.fromAddress, 'From Address')}
                >
                  <Text style={styles.addressText}>{formatAddress(transaction.fromAddress)}</Text>
                  <Ionicons name="copy-outline" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            )}

            {transaction.fromAddress && transaction.toAddress && (
              <View style={styles.addressSeparator}>
                <Ionicons name="arrow-down" size={20} color={theme.colors.textSecondary} />
              </View>
            )}

            {transaction.toAddress && (
              <View style={styles.addressContainer}>
                <Text style={styles.addressLabel}>To</Text>
                <TouchableOpacity
                  style={styles.addressRow}
                  onPress={() => copyToClipboard(transaction.toAddress, 'To Address')}
                >
                  <Text style={styles.addressText}>{formatAddress(transaction.toAddress)}</Text>
                  <Ionicons name="copy-outline" size={16} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}

        {/* Blockchain Details */}
        {(transaction.blockHeight || transaction.blockHash) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Blockchain Details</Text>

            {transaction.blockHeight && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Block Height</Text>
                <Text style={styles.detailValue}>{transaction.blockHeight.toLocaleString()}</Text>
              </View>
            )}

            {transaction.blockHash && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Block Hash</Text>
                <TouchableOpacity
                  style={styles.hashContainer}
                  onPress={() => copyToClipboard(transaction.blockHash, 'Block Hash')}
                >
                  <Text style={styles.hashText}>{formatAddress(transaction.blockHash)}</Text>
                  <Ionicons name="copy-outline" size={16} color={theme.colors.primary} />
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}

        {/* Related Trade */}
        {relatedTrade && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Related KryptoPesa Trade</Text>
            <TouchableOpacity
              style={styles.relatedTradeContainer}
              onPress={() => navigation.navigate('Trade', { tradeId: relatedTrade._id })}
            >
              <View style={styles.relatedTradeInfo}>
                <View style={styles.relatedTradeHeader}>
                  <Text style={styles.relatedTradeType}>
                    {relatedTrade.type === 'buy' ? 'Buy' : 'Sell'} {relatedTrade.cryptocurrency.type}
                  </Text>
                  <Text style={styles.relatedTradeAmount}>
                    {relatedTrade.cryptocurrency.amount} {relatedTrade.cryptocurrency.type}
                  </Text>
                </View>
                <Text style={styles.relatedTradeValue}>
                  {eastAfricanHelpers.formatCurrencyAmount(relatedTrade.fiat.amount, relatedTrade.fiat.currency)}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
        )}
        
        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {transaction.txHash && (
            <TouchableOpacity style={styles.actionButton} onPress={handleViewOnExplorer}>
              <Ionicons name="globe-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.actionButtonText}>View on Explorer</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
            <Ionicons name="share-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.actionButtonText}>Share Transaction</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  statusSection: {
    alignItems: 'center',
    paddingVertical: 32,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    marginTop: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statusIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  section: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  amountContainer: {
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  amountValue: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  receivedAmount: {
    color: theme.colors.success,
  },
  sentAmount: {
    color: theme.colors.error,
  },
  fiatAmount: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'right',
    flex: 1,
  },
  hashContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
    justifyContent: 'flex-end',
  },
  hashText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  confirmationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    flex: 1,
    justifyContent: 'flex-end',
  },
  confirmationText: {
    fontSize: 14,
    fontWeight: '500',
  },
  confirmedText: {
    color: theme.colors.success,
  },
  pendingText: {
    color: theme.colors.warning,
  },
  addressContainer: {
    marginBottom: 16,
  },
  addressLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  addressText: {
    fontSize: 14,
    fontFamily: 'monospace',
    color: theme.colors.text,
    flex: 1,
  },
  addressSeparator: {
    alignItems: 'center',
    marginVertical: 8,
  },
  actionButtons: {
    gap: 12,
    marginTop: 16,
    marginBottom: 32,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: theme.colors.surface,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  // New styles for East African features
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 12,
    textAlign: 'center',
  },
  errorSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  amountHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  relatedTradeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  relatedTradeInfo: {
    flex: 1,
  },
  relatedTradeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  relatedTradeType: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  relatedTradeAmount: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  relatedTradeValue: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});
