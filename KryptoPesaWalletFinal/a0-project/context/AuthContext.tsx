import React, { createContext, useContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';

// Define the shape of our auth context
type AuthContextType = {
  user: any | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
};

// Define user types
type User = {
  _id: string;
  email: string;
  name: string;
  profileComplete: boolean;
  kycVerified: boolean;
  twoFactorEnabled: boolean;
};

type RegisterData = {
  email: string;
  password: string;
  name: string;
};

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  updateUser: async () => {},
});

// Create a provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Create mock functions that will be replaced with actual Convex functions when available
  const handleLogin = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      // Mock successful login for now
      const mockUser = {
        _id: 'mock-user-id',
        email,
        name: 'Mock User',
        profileComplete: false,
        kycVerified: false,
        twoFactorEnabled: false
      };
      setUser(mockUser);
      Alert.alert('Success', 'Login successful');
    } catch (error) {
      Alert.alert('Error', 'An error occurred during login');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleRegister = async (userData: RegisterData) => {
    try {
      setIsLoading(true);
      // Mock successful registration for now
      const mockUser = {
        _id: 'mock-user-id',
        email: userData.email,
        name: userData.name,
        profileComplete: false,
        kycVerified: false,
        twoFactorEnabled: false
      };
      setUser(mockUser);
      Alert.alert('Success', 'Registration successful');
    } catch (error) {
      Alert.alert('Error', 'An error occurred during registration');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleLogout = () => {
    setUser(null);
    Alert.alert('Success', 'Logged out successfully');
  };
  
  const handleUpdateUser = async (userData: Partial<User>) => {
    try {
      setUser(prev => prev ? { ...prev, ...userData } : null);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      Alert.alert('Error', 'An error occurred while updating profile');
      console.error(error);
    }
  };
  
  // Initialize auth state
  useEffect(() => {
    // Simulate checking auth state
    const checkAuth = async () => {
      try {
        // In a real app, this would check for stored tokens or session data
        // For now, just set loading to false after a delay
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error checking auth state:', error);
        setIsLoading(false);
      }
    };
    
    checkAuth();
  }, []);
  
  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login: handleLogin,
    register: handleRegister,
    logout: handleLogout,
    updateUser: handleUpdateUser,
  };
  
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Create a hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};