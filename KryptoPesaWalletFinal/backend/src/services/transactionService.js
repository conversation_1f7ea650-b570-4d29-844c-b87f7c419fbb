const { ethers } = require('ethers');
const Wallet = require('../models/Wallet');
const ethereumService = require('./blockchain/ethereumService');
const bitcoinService = require('./blockchain/bitcoinService');
const priceService = require('./priceService');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');
const { transactionMonitoringService } = require('./transactionMonitoring');

class TransactionService {
  constructor() {
    // Removed in-memory pendingTransactions - now using persistent transaction monitoring
    this.confirmationThresholds = {
      bitcoin: 1,
      ethereum: 12,
      polygon: 20
    };
  }

  /**
   * Send cryptocurrency transaction
   */
  async sendTransaction(userId, transactionData) {
    try {
      const { toAddress, amount, symbol, network, gasPrice, memo } = transactionData;
      
      // Get user wallet
      const wallet = await Wallet.findOne({ user: userId }).populate('user');
      if (!wallet) {
        throw new AppError('Wallet not found', 404);
      }

      // Validate transaction data
      await this.validateTransaction(wallet, transactionData);

      let txResult;
      
      if (network === 'bitcoin') {
        txResult = await this.sendBitcoinTransaction(wallet, transactionData);
      } else {
        txResult = await this.sendEthereumTransaction(wallet, transactionData);
      }

      // Add transaction to wallet
      const transaction = {
        hash: txResult.txHash,
        type: 'send',
        symbol,
        amount: amount.toString(),
        from: txResult.fromAddress,
        to: toAddress,
        network,
        gasUsed: txResult.gasUsed || 0,
        gasPrice: gasPrice || '0',
        fee: txResult.fee || '0',
        status: 'pending',
        confirmations: 0,
        memo: memo || '',
        timestamp: new Date()
      };

      await wallet.addTransaction(transaction);

      // Start persistent monitoring transaction
      await transactionMonitoringService.startBlockchainTransactionMonitoring(
        txResult.txHash,
        network,
        userId,
        {
          ...transaction,
          walletId: wallet._id
        },
        {
          priority: 'high',
          sessionId: `tx_${Date.now()}`,
          clientInfo: 'TransactionService'
        }
      );

      logger.info(`Transaction sent: ${txResult.txHash}`, {
        userId,
        symbol,
        amount,
        network,
        to: toAddress
      });

      return {
        txHash: txResult.txHash,
        transaction
      };

    } catch (error) {
      logger.error(`Error sending transaction: ${error.message}`, { userId, transactionData });
      throw error;
    }
  }

  /**
   * Send Bitcoin transaction
   */
  async sendBitcoinTransaction(wallet, { toAddress, amount, feeRate }) {
    try {
      const fromAddress = wallet.addresses.bitcoin.address;
      
      // Securely retrieve private key from key management service
      const keyManagement = require('./keyManagement/keyManager');
      const privateKey = await keyManagement.getPrivateKey(wallet._id, 'bitcoin');
      
      const txResult = await bitcoinService.createTransaction(
        fromAddress,
        toAddress,
        parseInt(amount),
        privateKey,
        feeRate
      );

      const txHash = await bitcoinService.broadcastTransaction(txResult.txHex);

      return {
        txHash,
        fromAddress,
        fee: txResult.fee.toString(),
        size: txResult.size
      };

    } catch (error) {
      logger.error(`Bitcoin transaction error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send Ethereum/Polygon transaction
   */
  async sendEthereumTransaction(wallet, { toAddress, amount, symbol, network, gasPrice, gasLimit }) {
    try {
      const fromAddress = wallet.addresses.ethereum.address;
      
      // Get secure wallet instance
      const secureWallet = await ethereumService.getWallet(network);
      if (!secureWallet) {
        throw new AppError('Wallet not configured for this network', 500);
      }

      let txResult;

      if (symbol === 'ETH' || symbol === 'MATIC') {
        // Native token transfer
        const tx = {
          to: toAddress,
          value: ethers.utils.parseEther(amount),
          gasPrice: gasPrice ? ethers.utils.parseUnits(gasPrice, 'gwei') : undefined,
          gasLimit: gasLimit || 21000
        };

        const txResponse = await secureWallet.sendTransaction(tx);
        const receipt = await txResponse.wait();

        txResult = {
          txHash: receipt.transactionHash,
          fromAddress,
          gasUsed: receipt.gasUsed.toString(),
          fee: receipt.gasUsed.mul(receipt.effectiveGasPrice).toString()
        };

      } else {
        // ERC20 token transfer
        const tokenAddress = this.getTokenAddress(symbol, network);
        if (!tokenAddress) {
          throw new AppError(`Token ${symbol} not supported on ${network}`, 400);
        }

        const tokenContract = ethereumService.getTokenContract(tokenAddress, network);
        const decimals = await tokenContract.decimals();
        const transferAmount = ethers.utils.parseUnits(amount, decimals);

        const tx = await tokenContract.transfer(toAddress, transferAmount, {
          gasPrice: gasPrice ? ethers.utils.parseUnits(gasPrice, 'gwei') : undefined,
          gasLimit: gasLimit || 100000
        });

        const receipt = await tx.wait();

        txResult = {
          txHash: receipt.transactionHash,
          fromAddress,
          gasUsed: receipt.gasUsed.toString(),
          fee: receipt.gasUsed.mul(receipt.effectiveGasPrice).toString()
        };
      }

      return txResult;

    } catch (error) {
      logger.error(`Ethereum transaction error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate transaction before sending
   */
  async validateTransaction(wallet, { toAddress, amount, symbol, network }) {
    // Validate address format
    if (network === 'bitcoin') {
      if (!bitcoinService.validateAddress(toAddress)) {
        throw new AppError('Invalid Bitcoin address', 400);
      }
    } else {
      if (!ethers.utils.isAddress(toAddress)) {
        throw new AppError('Invalid Ethereum address', 400);
      }
    }

    // Check balance
    const balance = wallet.balances.find(b => 
      b.symbol === symbol && b.network === network
    );

    if (!balance) {
      throw new AppError(`No ${symbol} balance found on ${network}`, 400);
    }

    const balanceAmount = parseFloat(balance.balance) / Math.pow(10, balance.decimals);
    const sendAmount = parseFloat(amount);

    if (sendAmount > balanceAmount) {
      throw new AppError('Insufficient balance', 400);
    }

    if (sendAmount <= 0) {
      throw new AppError('Amount must be greater than 0', 400);
    }

    // Check minimum transaction amounts
    const minAmounts = {
      BTC: 0.00001,
      ETH: 0.001,
      MATIC: 0.01,
      USDT: 1,
      USDC: 1
    };

    if (sendAmount < (minAmounts[symbol] || 0)) {
      throw new AppError(`Minimum ${symbol} transaction amount is ${minAmounts[symbol]}`, 400);
    }
  }

  /**
   * Get token contract address
   */
  getTokenAddress(symbol, network) {
    const tokenAddresses = {
      polygon: {
        USDT: process.env.POLYGON_USDT_ADDRESS || '******************************************',
        USDC: process.env.POLYGON_USDC_ADDRESS || '******************************************',
        DAI: process.env.POLYGON_DAI_ADDRESS || '******************************************'
      },
      ethereum: {
        USDT: process.env.ETHEREUM_USDT_ADDRESS || '******************************************',
        USDC: process.env.ETHEREUM_USDC_ADDRESS || '******************************************',
        DAI: process.env.ETHEREUM_DAI_ADDRESS || '******************************************'
      }
    };

    return tokenAddresses[network]?.[symbol];
  }

  /**
   * Estimate transaction fee
   */
  async estimateTransactionFee(userId, transactionData) {
    try {
      const { toAddress, amount, symbol, network } = transactionData;
      
      if (network === 'bitcoin') {
        const feeEstimate = await bitcoinService.estimateFee();
        return {
          network,
          symbol,
          estimatedFee: feeEstimate.feeRate * 250, // Rough estimation
          feeRate: feeEstimate.feeRate,
          estimatedMinutes: feeEstimate.estimatedMinutes
        };
      } else {
        const provider = ethereumService.getProvider(network);
        const gasPrice = await provider.getGasPrice();
        
        let gasLimit;
        if (symbol === 'ETH' || symbol === 'MATIC') {
          gasLimit = 21000;
        } else {
          gasLimit = 100000; // ERC20 transfer
        }

        const estimatedFee = gasPrice.mul(gasLimit);

        return {
          network,
          symbol,
          gasPrice: ethers.utils.formatUnits(gasPrice, 'gwei'),
          gasLimit,
          estimatedFee: ethers.utils.formatEther(estimatedFee),
          estimatedFeeUSD: await this.convertToUSD(estimatedFee, network === 'polygon' ? 'MATIC' : 'ETH')
        };
      }
    } catch (error) {
      logger.error(`Error estimating transaction fee: ${error.message}`);
      throw error;
    }
  }

  /**
   * Convert amount to USD
   */
  async convertToUSD(amount, symbol) {
    try {
      const price = await priceService.getPrice(symbol, 'USD');
      const amountFloat = parseFloat(ethers.utils.formatEther(amount));
      return (amountFloat * price).toFixed(2);
    } catch (error) {
      return '0.00';
    }
  }

  /**
   * Monitor transaction status (DEPRECATED - replaced by persistent monitoring)
   * This method is kept for backward compatibility but functionality moved to transactionMonitoringService
   */
  async monitorTransaction(txHash, network, userId) {
    // This method has been replaced by persistent transaction monitoring
    // All blockchain transaction monitoring is now handled by transactionMonitoringService
    // which provides database persistence and process restart recovery
    logger.info(`Transaction monitoring delegated to persistent service: ${txHash}`);
  }

  /**
   * Update transaction status in wallet
   */
  async updateTransactionStatus(txHash, userId, status, confirmations) {
    try {
      const wallet = await Wallet.findOne({ user: userId });
      if (wallet) {
        await wallet.updateTransactionStatus(txHash, status, confirmations);
        logger.info(`Transaction status updated: ${txHash} -> ${status}`);
      }
    } catch (error) {
      logger.error(`Error updating transaction status: ${error.message}`);
    }
  }

  /**
   * Get transaction history with enhanced details
   */
  async getTransactionHistory(userId, limit = 50, offset = 0) {
    try {
      const wallet = await Wallet.findOne({ user: userId });
      if (!wallet) {
        throw new AppError('Wallet not found', 404);
      }

      const transactions = wallet.transactions
        .slice(offset, offset + limit)
        .map(tx => ({
          ...tx.toObject(),
          amountUSD: null, // Would be calculated with historical prices
          feeUSD: null
        }));

      return {
        transactions,
        total: wallet.transactions.length,
        limit,
        offset
      };
    } catch (error) {
      logger.error(`Error getting transaction history: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get pending transactions (now uses persistent storage)
   */
  async getPendingTransactions() {
    try {
      // Get pending blockchain transactions from persistent storage
      const { TransactionState } = require('./transactionMonitoring');

      const pendingTransactions = await TransactionState.find({
        type: 'blockchain_transaction',
        status: { $in: ['pending', 'in_progress'] }
      }).select('transactionId blockchainData metadata createdAt');

      return pendingTransactions.map(tx => ({
        txHash: tx.blockchainData?.txHash || tx.transactionId.replace('blockchain_', ''),
        network: tx.blockchainData?.network,
        userId: tx.userId,
        startTime: tx.createdAt.getTime(),
        duration: Date.now() - tx.createdAt.getTime(),
        status: tx.status,
        priority: tx.metadata?.priority
      }));
    } catch (error) {
      logger.error(`Error getting pending transactions: ${error.message}`);
      return [];
    }
  }
}

module.exports = new TransactionService();
