const express = require('express');
const { body, validationResult, query } = require('express-validator');
const WalletUser = require('../models/WalletUser'); // Updated to use WalletUser
const Trade = require('../models/Trade');
const { authenticateWallet, validateWalletOwnership } = require('../middleware/walletAuth'); // Updated auth
const { AppError } = require('../middleware/errorHandler');
const secureFileUpload = require('../middleware/fileSecurityMiddleware');
const logger = require('../utils/logger');

const router = express.Router();

// Dashboard endpoint - Get user dashboard data
router.get('/dashboard/:walletAddress', async (req, res, next) => {
  try {
    const { walletAddress } = req.params;

    let user = await WalletUser.findByWalletAddress(walletAddress);

    // If user doesn't exist, return mock data for development
    if (!user) {
      return res.json({
        success: true,
        data: {
          user: {
            _id: 'mock_user_id',
            username: 'demo_trader',
            preferences: {
              currency: 'USD',
              language: 'en',
              country: 'US'
            }
          },
          stats: {
            walletValue: 4941.84,
            profitLoss: 84.32,
            profitLossPercentage: 1.7,
            activeTrades: 3,
            completedTrades: 47,
            pendingOffers: 2
          },
          recentTrades: [
            {
              _id: 'trade1',
              type: 'buy',
              cryptocurrency: { type: 'USDT', amount: 500 },
              fiat: { currency: 'USD', amount: 500 },
              status: 'completed',
              createdAt: new Date(Date.now() - 86400000), // 1 day ago
              counterparty: { username: 'seller123', profile: { avatar: null } }
            },
            {
              _id: 'trade2',
              type: 'sell',
              cryptocurrency: { type: 'BTC', amount: 0.01 },
              fiat: { currency: 'USD', amount: 650 },
              status: 'completed',
              createdAt: new Date(Date.now() - 172800000), // 2 days ago
              counterparty: { username: 'buyer456', profile: { avatar: null } }
            }
          ],
          notifications: []
        }
      });
    }

    // Get user's trade statistics
    const tradeStats = await Trade.aggregate([
      { $match: { $or: [{ buyer: user._id }, { seller: user._id }] } },
      {
        $group: {
          _id: null,
          totalTrades: { $sum: 1 },
          activeTrades: {
            $sum: {
              $cond: [{ $in: ['$status', ['created', 'funded', 'payment_sent']] }, 1, 0]
            }
          },
          completedTrades: {
            $sum: {
              $cond: [{ $eq: ['$status', 'completed'] }, 1, 0]
            }
          },
          totalVolume: { $sum: '$fiat.amount' }
        }
      }
    ]);

    const stats = tradeStats[0] || {
      totalTrades: 0,
      activeTrades: 0,
      completedTrades: 0,
      totalVolume: 0
    };

    // Get recent trades
    const recentTrades = await Trade.find({
      $or: [{ buyer: user._id }, { seller: user._id }]
    })
    .sort({ createdAt: -1 })
    .limit(5)
    .populate('buyer seller', 'username profile.avatar')
    .lean();

    res.json({
      success: true,
      data: {
        user: {
          _id: user._id,
          username: user.username,
          preferences: user.preferences || {
            currency: 'USD',
            language: 'en',
            country: 'US'
          }
        },
        stats: {
          walletValue: stats.totalVolume || 0,
          profitLoss: 0, // Calculate based on your business logic
          profitLossPercentage: 0,
          activeTrades: stats.activeTrades,
          completedTrades: stats.completedTrades,
          pendingOffers: 0 // Add offer counting logic if needed
        },
        recentTrades: recentTrades.map(trade => ({
          _id: trade._id,
          type: trade.buyer.toString() === user._id.toString() ? 'buy' : 'sell',
          cryptocurrency: trade.cryptocurrency,
          fiat: trade.fiat,
          status: trade.status,
          createdAt: trade.createdAt,
          counterparty: trade.buyer.toString() === user._id.toString() ? trade.seller : trade.buyer
        })),
        notifications: [] // Add notification logic if needed
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get user profile by wallet address (public)
router.get('/:walletAddress', async (req, res, next) => {
  try {
    const { walletAddress } = req.params;

    const user = await WalletUser.findByWalletAddress(walletAddress);

    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Get trade statistics with caching
    const { performanceOptimization } = require('../services/performanceOptimization');
    const cacheKey = `user:stats:${walletAddress}`;

    const tradeStats = await performanceOptimization.cacheQuery(
      cacheKey,
      () => Trade.getTradeStats(user._id),
      300 // Cache for 5 minutes
    );

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          profile: {
            firstName: user.profile.firstName,
            lastName: user.profile.lastName,
            avatar: user.profile.avatar,
            location: user.profile.location,
            bio: user.profile.bio
          },
          reputation: user.reputation,
          verification: {
            identity: user.verification.identity,
            phone: user.verification.phone,
            email: user.verification.email
          },
          joinedAt: user.createdAt,
          lastActive: user.lastActive
        },
        statistics: tradeStats[0] || {
          totalTrades: 0,
          totalVolume: 0,
          averageTradeSize: 0,
          currencies: []
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Update user profile (requires auth)
router.put('/profile', authenticateWallet, [
  body('firstName').optional().isLength({ min: 1, max: 50 }).trim(),
  body('lastName').optional().isLength({ min: 1, max: 50 }).trim(),
  body('bio').optional().isLength({ max: 500 }).trim(),
  body('avatar').optional().isURL(),
  body('location.city').optional().isLength({ min: 1, max: 100 }).trim(),
  body('preferences.language').optional().isIn(['en', 'sw']),
  body('preferences.currency').optional().isIn(['KES', 'TZS', 'UGX', 'RWF', 'USD']),
  body('preferences.notifications.email').optional().isBoolean(),
  body('preferences.notifications.sms').optional().isBoolean(),
  body('preferences.notifications.push').optional().isBoolean()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const updates = req.body;
    const user = req.user;

    // Update profile fields
    if (updates.firstName) user.profile.firstName = updates.firstName;
    if (updates.lastName) user.profile.lastName = updates.lastName;
    if (updates.bio !== undefined) user.profile.bio = updates.bio;
    if (updates.avatar) user.profile.avatar = updates.avatar;
    if (updates.location?.city) user.profile.location.city = updates.location.city;

    // Update preferences
    if (updates.preferences) {
      if (updates.preferences.language) user.preferences.language = updates.preferences.language;
      if (updates.preferences.currency) user.preferences.currency = updates.preferences.currency;

      if (updates.preferences.notifications) {
        Object.keys(updates.preferences.notifications).forEach(key => {
          if (typeof updates.preferences.notifications[key] === 'boolean') {
            user.preferences.notifications[key] = updates.preferences.notifications[key];
          }
        });
      }
    }

    await user.save();

    logger.info(`Profile updated for user: ${user.username}`);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user }
    });

  } catch (error) {
    next(error);
  }
});

// Get user reputation details
router.get('/:userId/reputation', async (req, res, next) => {
  try {
    const { userId } = req.params;

    const user = await WalletUser.findById(userId);
    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Get detailed trade statistics
    const completedTrades = await Trade.find({
      $or: [{ seller: userId }, { buyer: userId }],
      status: 'completed'
    }).populate('seller buyer', 'username');

    const disputedTrades = await Trade.find({
      $or: [{ seller: userId }, { buyer: userId }],
      status: 'disputed'
    }).populate('dispute');

    const cancelledTrades = await Trade.find({
      $or: [{ seller: userId }, { buyer: userId }],
      status: 'cancelled'
    });

    // Calculate reputation metrics
    const totalTrades = completedTrades.length;
    const totalDisputes = disputedTrades.length;
    const totalCancellations = cancelledTrades.length;

    const disputeRate = totalTrades > 0 ? (totalDisputes / totalTrades) * 100 : 0;
    const cancellationRate = totalTrades > 0 ? (totalCancellations / totalTrades) * 100 : 0;

    const totalVolume = completedTrades.reduce((sum, trade) => {
      return sum + parseFloat(trade.cryptocurrency.amount);
    }, 0);

    const averageTradeSize = totalTrades > 0 ? totalVolume / totalTrades : 0;

    // Get recent feedback (last 10 trades)
    const recentTrades = completedTrades
      .sort((a, b) => b.completedAt - a.completedAt)
      .slice(0, 10);

    res.json({
      success: true,
      data: {
        reputation: user.reputation,
        statistics: {
          totalTrades,
          totalDisputes,
          totalCancellations,
          disputeRate: Math.round(disputeRate * 100) / 100,
          cancellationRate: Math.round(cancellationRate * 100) / 100,
          totalVolume,
          averageTradeSize: Math.round(averageTradeSize * 100) / 100
        },
        recentTrades: recentTrades.map(trade => ({
          tradeId: trade.tradeId,
          amount: trade.cryptocurrency.amount,
          currency: trade.cryptocurrency.symbol,
          completedAt: trade.completedAt,
          counterparty: trade.seller._id.toString() === userId ? trade.buyer.username : trade.seller.username
        }))
      }
    });

  } catch (error) {
    next(error);
  }
});

// Update FCM token for push notifications
router.post('/fcm-token', authenticateWallet, [
  body('token').notEmpty().withMessage('FCM token is required'),
  body('deviceId').optional().isLength({ min: 1, max: 100 }),
  body('platform').optional().isIn(['ios', 'android'])
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { token, deviceId, platform } = req.body;
    const userId = req.user._id;

    // Update user's FCM token
    const user = await WalletUser.findByIdAndUpdate(
      userId,
      {
        $set: {
          'notifications.fcmToken': token,
          'notifications.deviceId': deviceId,
          'notifications.platform': platform,
          'notifications.updatedAt': new Date()
        }
      },
      { new: true }
    );

    if (!user) {
      throw new AppError('User not found', 404);
    }

    logger.info(`FCM token updated for user ${userId}`, {
      userId,
      deviceId,
      platform
    });

    res.json({
      success: true,
      message: 'FCM token updated successfully',
      data: {
        tokenUpdated: true,
        timestamp: new Date()
      }
    });

  } catch (error) {
    next(error);
  }
});

// Upload payment proof files with enhanced security
router.post('/upload/payment-proof',
  authenticateWallet,
  secureFileUpload.uploadRateLimit(),
  secureFileUpload.checkUploadQuota(),
  secureFileUpload.paymentProofUpload(),
  async (req, res, next) => {
  try {
    if (!req.secureFiles || req.secureFiles.length === 0) {
      throw new AppError('No files uploaded', 400);
    }

    const { tradeId } = req.body;
    if (!tradeId) {
      throw new AppError('Trade ID is required', 400);
    }

    // Verify user is participant in the trade
    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    // Process securely uploaded files
    const uploadedFiles = req.secureFiles.map(file => ({
      fileId: file.fileId,
      filename: file.filename,
      originalName: file.metadata.originalName,
      size: file.metadata.size,
      url: file.url,
      uploadedAt: file.metadata.uploadedAt,
      uploadedBy: req.user._id,
      integrity: file.metadata.integrity
    }));

    // Update trade with secure payment proof file IDs
    await Trade.findOneAndUpdate(
      { tradeId },
      {
        $push: {
          'payment.proofOfPayment': {
            $each: uploadedFiles.map(file => file.fileId)
          }
        }
      }
    );

    logger.info(`Secure payment proof uploaded for trade ${tradeId}`, {
      tradeId,
      userId: req.user._id,
      fileCount: uploadedFiles.length,
      fileIds: uploadedFiles.map(f => f.fileId)
    });

    res.json({
      success: true,
      message: 'Payment proof uploaded successfully',
      data: {
        files: uploadedFiles,
        tradeId
      }
    });

  } catch (error) {
    next(error);
  }
});

// Search users
router.get('/', [
  query('search').optional().isLength({ min: 2, max: 50 }),
  query('country').optional().isIn(['KE', 'TZ', 'UG', 'RW']),
  query('minReputation').optional().isInt({ min: 0, max: 100 }),
  query('verified').optional().isBoolean(),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  query('offset').optional().isInt({ min: 0 })
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      search,
      country,
      minReputation,
      verified,
      limit = 20,
      offset = 0
    } = req.query;

    // Build query
    const query = { status: 'active' };

    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { 'profile.firstName': { $regex: search, $options: 'i' } },
        { 'profile.lastName': { $regex: search, $options: 'i' } }
      ];
    }

    if (country) {
      query['profile.location.country'] = country;
    }

    if (minReputation) {
      query['reputation.score'] = { $gte: parseInt(minReputation) };
    }

    if (verified === 'true') {
      query['verification.identity.verified'] = true;
    }

    // Use optimized query with caching for user search
    const { performanceOptimization } = require('../services/performanceOptimization');
    const cacheKey = `users:search:${JSON.stringify(query)}:${limit}:${offset}`;

    const result = await performanceOptimization.cacheQuery(
      cacheKey,
      async () => {
        const [users, total] = await Promise.all([
          WalletUser.find(query)
            .select('username profile reputation verification createdAt lastActive')
            .sort({ 'reputation.score': -1, createdAt: -1 })
            .limit(parseInt(limit))
            .skip(parseInt(offset))
            .lean(),
          WalletUser.countDocuments(query)
        ]);

        return { users, total };
      },
      180 // Cache for 3 minutes
    );

    const { users, total } = result;

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          total,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: parseInt(offset) + parseInt(limit) < total
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;