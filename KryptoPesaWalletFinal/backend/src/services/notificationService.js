const admin = require('firebase-admin');
const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');
const realTimeScalabilityService = require('./realTimeScalabilityService');

/**
 * Comprehensive Notification Service
 * Handles push notifications, email notifications, and real-time notifications
 */
class NotificationService {
  constructor() {
    this.redisClient = null;
    this.isFirebaseInitialized = false;
    this.notificationQueue = [];
    this.processingQueue = false;
    this.metrics = {
      totalNotifications: 0,
      successfulDeliveries: 0,
      failedDeliveries: 0,
      averageDeliveryTime: 0,
      lastReset: new Date()
    };
    this.retryAttempts = new Map();
    this.maxRetries = 3;
    this.retryDelay = 5000; // 5 seconds
  }

  /**
   * Initialize notification service
   */
  async initialize() {
    try {
      this.redisClient = getRedisClient();
      
      // Initialize Firebase Admin SDK if credentials are available
      await this.initializeFirebase();
      
      // Start background processes
      this.startBackgroundProcesses();
      
      logger.info('Notification service initialized successfully');
      return true;
    } catch (error) {
      logger.error('Failed to initialize notification service:', error);
      return false;
    }
  }

  /**
   * Initialize Firebase Admin SDK
   */
  async initializeFirebase() {
    try {
      if (admin.apps.length === 0) {
        const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT_KEY;
        
        if (serviceAccount) {
          admin.initializeApp({
            credential: admin.credential.cert(JSON.parse(serviceAccount)),
            projectId: process.env.FIREBASE_PROJECT_ID
          });
          
          this.isFirebaseInitialized = true;
          logger.info('Firebase Admin SDK initialized for push notifications');
        } else {
          logger.warn('Firebase service account key not found, push notifications disabled');
        }
      } else {
        this.isFirebaseInitialized = true;
      }
    } catch (error) {
      logger.error('Failed to initialize Firebase:', error);
      this.isFirebaseInitialized = false;
    }
  }

  /**
   * Send push notification
   */
  async sendPushNotification(userId, notification) {
    try {
      if (!this.isFirebaseInitialized) {
        logger.warn('Firebase not initialized, cannot send push notification');
        return false;
      }

      // Get user's FCM token from database or Redis
      const fcmToken = await this.getUserFCMToken(userId);
      if (!fcmToken) {
        logger.warn(`No FCM token found for user ${userId}`);
        return false;
      }

      const message = {
        token: fcmToken,
        notification: {
          title: notification.title,
          body: notification.body,
          icon: notification.icon || '/icons/icon-192x192.png'
        },
        data: {
          type: notification.type || 'general',
          tradeId: notification.tradeId || '',
          userId: userId.toString(),
          timestamp: new Date().toISOString(),
          ...notification.data
        },
        android: {
          notification: {
            channelId: notification.channelId || 'default',
            priority: 'high',
            defaultSound: true,
            defaultVibrateTimings: true
          }
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: notification.badge || 1
            }
          }
        }
      };

      const response = await admin.messaging().send(message);
      logger.info(`Push notification sent successfully to user ${userId}:`, response);
      
      this.metrics.successfulDeliveries++;
      return true;
    } catch (error) {
      logger.error(`Failed to send push notification to user ${userId}:`, error);
      this.metrics.failedDeliveries++;
      
      // Handle specific Firebase errors
      if (error.code === 'messaging/registration-token-not-registered') {
        await this.removeInvalidFCMToken(userId);
      }
      
      return false;
    }
  }

  /**
   * Send real-time notification
   */
  async sendRealTimeNotification(userId, notification) {
    try {
      const isOnline = realTimeScalabilityService.isUserOnline(userId);
      
      if (isOnline) {
        // User is online, send real-time notification
        await realTimeScalabilityService.sendToUser(userId, 'notification', {
          ...notification,
          timestamp: new Date()
        });
        
        this.metrics.successfulDeliveries++;
        return true;
      } else {
        // User is offline, queue for push notification
        await this.queueNotification(userId, notification, 'push');
        return false;
      }
    } catch (error) {
      logger.error(`Failed to send real-time notification to user ${userId}:`, error);
      this.metrics.failedDeliveries++;
      return false;
    }
  }

  /**
   * Send comprehensive notification (real-time + push)
   */
  async sendNotification(userId, notification) {
    try {
      const startTime = Date.now();
      
      // Try real-time first
      const realTimeSent = await this.sendRealTimeNotification(userId, notification);
      
      // If user is offline or real-time failed, send push notification
      if (!realTimeSent) {
        await this.sendPushNotification(userId, notification);
      }
      
      // Update metrics
      const deliveryTime = Date.now() - startTime;
      this.updateDeliveryTimeMetrics(deliveryTime);
      this.metrics.totalNotifications++;
      
      return true;
    } catch (error) {
      logger.error(`Failed to send notification to user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Send trade notification
   */
  async sendTradeNotification(tradeId, userId, type, data = {}) {
    const notifications = {
      trade_created: {
        title: 'New Trade Created',
        body: `A new ${data.cryptoCurrency} trade has been created`,
        type: 'trade',
        channelId: 'trades'
      },
      trade_accepted: {
        title: 'Trade Accepted',
        body: 'Your trade has been accepted',
        type: 'trade',
        channelId: 'trades'
      },
      trade_payment_sent: {
        title: 'Payment Sent',
        body: 'Payment has been marked as sent',
        type: 'trade',
        channelId: 'trades'
      },
      trade_completed: {
        title: 'Trade Completed',
        body: 'Your trade has been completed successfully',
        type: 'trade',
        channelId: 'trades'
      },
      trade_disputed: {
        title: 'Trade Disputed',
        body: 'A dispute has been raised for your trade',
        type: 'dispute',
        channelId: 'disputes'
      },
      new_message: {
        title: 'New Message',
        body: `You have a new message in trade ${tradeId}`,
        type: 'chat',
        channelId: 'messages'
      }
    };

    const notification = notifications[type];
    if (!notification) {
      logger.warn(`Unknown trade notification type: ${type}`);
      return false;
    }

    return await this.sendNotification(userId, {
      ...notification,
      tradeId,
      data: { ...data, tradeId }
    });
  }

  /**
   * Send wallet notification
   */
  async sendWalletNotification(userId, type, data = {}) {
    const notifications = {
      balance_update: {
        title: 'Balance Updated',
        body: `Your ${data.currency} balance has been updated`,
        type: 'wallet',
        channelId: 'wallet'
      },
      transaction_confirmed: {
        title: 'Transaction Confirmed',
        body: `Your ${data.currency} transaction has been confirmed`,
        type: 'wallet',
        channelId: 'wallet'
      },
      transaction_failed: {
        title: 'Transaction Failed',
        body: `Your ${data.currency} transaction has failed`,
        type: 'wallet',
        channelId: 'wallet'
      },
      deposit_received: {
        title: 'Deposit Received',
        body: `You received ${data.amount} ${data.currency}`,
        type: 'wallet',
        channelId: 'wallet'
      }
    };

    const notification = notifications[type];
    if (!notification) {
      logger.warn(`Unknown wallet notification type: ${type}`);
      return false;
    }

    return await this.sendNotification(userId, {
      ...notification,
      data
    });
  }

  /**
   * Send system notification
   */
  async sendSystemNotification(userId, type, data = {}) {
    const notifications = {
      maintenance_mode: {
        title: 'Maintenance Mode',
        body: 'The system is entering maintenance mode',
        type: 'system',
        channelId: 'system'
      },
      security_alert: {
        title: 'Security Alert',
        body: 'Suspicious activity detected on your account',
        type: 'security',
        channelId: 'security'
      },
      account_verified: {
        title: 'Account Verified',
        body: 'Your account has been successfully verified',
        type: 'account',
        channelId: 'account'
      },
      kyc_approved: {
        title: 'KYC Approved',
        body: 'Your KYC verification has been approved',
        type: 'account',
        channelId: 'account'
      },
      kyc_rejected: {
        title: 'KYC Rejected',
        body: 'Your KYC verification has been rejected',
        type: 'account',
        channelId: 'account'
      }
    };

    const notification = notifications[type];
    if (!notification) {
      logger.warn(`Unknown system notification type: ${type}`);
      return false;
    }

    return await this.sendNotification(userId, {
      ...notification,
      data
    });
  }

  /**
   * Queue notification for later processing
   */
  async queueNotification(userId, notification, type = 'push') {
    try {
      const queueItem = {
        userId,
        notification,
        type,
        timestamp: new Date(),
        attempts: 0
      };

      this.notificationQueue.push(queueItem);
      
      // Process queue if not already processing
      if (!this.processingQueue) {
        this.processNotificationQueue();
      }
      
      return true;
    } catch (error) {
      logger.error('Failed to queue notification:', error);
      return false;
    }
  }

  /**
   * Process notification queue
   */
  async processNotificationQueue() {
    if (this.processingQueue || this.notificationQueue.length === 0) {
      return;
    }

    this.processingQueue = true;

    try {
      while (this.notificationQueue.length > 0) {
        const item = this.notificationQueue.shift();
        await this.processQueuedNotification(item);
      }
    } catch (error) {
      logger.error('Error processing notification queue:', error);
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * Process individual queued notification
   */
  async processQueuedNotification(item) {
    try {
      let success = false;

      if (item.type === 'push') {
        success = await this.sendPushNotification(item.userId, item.notification);
      } else if (item.type === 'realtime') {
        success = await this.sendRealTimeNotification(item.userId, item.notification);
      }

      if (!success) {
        item.attempts++;
        
        if (item.attempts < this.maxRetries) {
          // Retry after delay
          setTimeout(() => {
            this.notificationQueue.push(item);
            if (!this.processingQueue) {
              this.processNotificationQueue();
            }
          }, this.retryDelay * item.attempts);
        } else {
          logger.error(`Failed to deliver notification after ${this.maxRetries} attempts:`, item);
        }
      }
    } catch (error) {
      logger.error('Failed to process queued notification:', error);
    }
  }

  /**
   * Get user's FCM token
   */
  async getUserFCMToken(userId) {
    try {
      if (this.redisClient) {
        const token = await this.redisClient.get(`fcm_token:${userId}`);
        if (token) return token;
      }

      // Fallback to database query
      const User = require('../models/User');
      const user = await User.findById(userId).select('fcmToken');
      return user?.fcmToken || null;
    } catch (error) {
      logger.error(`Failed to get FCM token for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Store user's FCM token
   */
  async storeFCMToken(userId, token) {
    try {
      if (this.redisClient) {
        await this.redisClient.setex(`fcm_token:${userId}`, 86400 * 30, token); // 30 days
      }

      // Also store in database
      const User = require('../models/User');
      await User.findByIdAndUpdate(userId, { fcmToken: token });
      
      logger.info(`FCM token stored for user ${userId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to store FCM token for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Remove invalid FCM token
   */
  async removeInvalidFCMToken(userId) {
    try {
      if (this.redisClient) {
        await this.redisClient.del(`fcm_token:${userId}`);
      }

      const User = require('../models/User');
      await User.findByIdAndUpdate(userId, { $unset: { fcmToken: 1 } });
      
      logger.info(`Invalid FCM token removed for user ${userId}`);
    } catch (error) {
      logger.error(`Failed to remove FCM token for user ${userId}:`, error);
    }
  }

  /**
   * Start background processes
   */
  startBackgroundProcesses() {
    // Process notification queue every 5 seconds
    setInterval(() => {
      if (this.notificationQueue.length > 0) {
        this.processNotificationQueue();
      }
    }, 5000);

    // Calculate metrics every 30 seconds
    setInterval(() => {
      this.calculateMetrics();
    }, 30000);

    logger.info('Notification service background processes started');
  }

  /**
   * Calculate metrics
   */
  calculateMetrics() {
    const now = new Date();
    const timeDiff = (now - this.metrics.lastReset) / 1000;

    // Reset counters every 5 minutes
    if (timeDiff >= 300) {
      this.metrics.totalNotifications = 0;
      this.metrics.successfulDeliveries = 0;
      this.metrics.failedDeliveries = 0;
      this.metrics.lastReset = now;
    }
  }

  /**
   * Update delivery time metrics
   */
  updateDeliveryTimeMetrics(deliveryTime) {
    this.metrics.averageDeliveryTime = 
      (this.metrics.averageDeliveryTime + deliveryTime) / 2;
  }

  /**
   * Get service metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      queueSize: this.notificationQueue.length,
      isFirebaseInitialized: this.isFirebaseInitialized,
      successRate: this.metrics.totalNotifications > 0 
        ? (this.metrics.successfulDeliveries / this.metrics.totalNotifications) * 100 
        : 0
    };
  }
}

// Create singleton instance
const notificationService = new NotificationService();

module.exports = notificationService;
