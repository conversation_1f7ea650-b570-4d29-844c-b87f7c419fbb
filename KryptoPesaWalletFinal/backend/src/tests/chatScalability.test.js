const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../server');
const User = require('../models/User');
const Trade = require('../models/Trade');
const Chat = require('../models/Chat');
const Message = require('../models/Message');
const chatScalabilityService = require('../services/chatScalabilityService');
const { generateToken } = require('../utils/auth');

describe('Chat Scalability Features', () => {
  let testUsers = [];
  let testTrade;
  let authTokens = [];

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || process.env.MONGODB_URI);
    }

    // Clean up existing test data
    await User.deleteMany({ username: /^testuser/ });
    await Trade.deleteMany({});
    await Chat.deleteMany({});
    await Message.deleteMany({});
  });

  beforeEach(async () => {
    // Create test users
    const userData = [
      {
        username: 'testuser1',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User1'
      },
      {
        username: 'testuser2',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User2'
      }
    ];

    testUsers = await User.create(userData);
    authTokens = testUsers.map(user => generateToken(user._id));

    // Create test trade
    testTrade = await Trade.create({
      tradeId: 'TEST-TRADE-001',
      seller: testUsers[0]._id,
      buyer: testUsers[1]._id,
      amount: 100,
      cryptocurrency: 'BTC',
      fiatCurrency: 'USD',
      exchangeRate: 50000,
      status: 'created'
    });
  });

  afterEach(async () => {
    // Clean up test data
    await User.deleteMany({ username: /^testuser/ });
    await Trade.deleteMany({});
    await Chat.deleteMany({});
    await Message.deleteMany({});
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('Message Creation and Retrieval', () => {
    test('should create message using scalability service', async () => {
      const message = await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Test message content',
        'text'
      );

      expect(message).toBeDefined();
      expect(message.trade.toString()).toBe(testTrade._id.toString());
      expect(message.sender.toString()).toBe(testUsers[0]._id.toString());
      expect(message.message).toBe('Test message content');
      expect(message.type).toBe('text');
    });

    test('should retrieve messages with pagination', async () => {
      // Create multiple messages
      const messagePromises = [];
      for (let i = 0; i < 10; i++) {
        messagePromises.push(
          chatScalabilityService.createMessage(
            testTrade._id,
            testUsers[i % 2]._id,
            `Test message ${i}`,
            'text'
          )
        );
      }
      await Promise.all(messagePromises);

      // Retrieve with pagination
      const messages = await chatScalabilityService.getMessages(testTrade._id, {
        limit: 5,
        offset: 0
      });

      expect(messages).toHaveLength(5);
      expect(messages[0].message).toContain('Test message');
    });

    test('should handle message caching', async () => {
      // Create a message
      const message = await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Cached message test',
        'text'
      );

      // First retrieval (cache miss)
      const messages1 = await chatScalabilityService.getMessages(testTrade._id, {
        limit: 10,
        offset: 0
      });

      // Second retrieval (cache hit)
      const messages2 = await chatScalabilityService.getMessages(testTrade._id, {
        limit: 10,
        offset: 0
      });

      expect(messages1).toEqual(messages2);
      
      // Check metrics for cache hits
      const metrics = chatScalabilityService.getMetrics();
      expect(metrics.cacheHits).toBeGreaterThan(0);
    });
  });

  describe('Real-time Features', () => {
    test('should handle user connections', async () => {
      await chatScalabilityService.handleUserConnection(testUsers[0]._id, 'socket123');
      
      const metrics = chatScalabilityService.getMetrics();
      expect(metrics.activeConnections).toBe(1);
    });

    test('should handle user disconnections', async () => {
      await chatScalabilityService.handleUserConnection(testUsers[0]._id, 'socket123');
      await chatScalabilityService.handleUserDisconnection(testUsers[0]._id);
      
      const metrics = chatScalabilityService.getMetrics();
      expect(metrics.activeConnections).toBe(0);
    });

    test('should manage trade room subscriptions', async () => {
      await chatScalabilityService.handleUserConnection(testUsers[0]._id, 'socket123');
      await chatScalabilityService.joinTradeRoom(testUsers[0]._id, testTrade.tradeId);
      
      // Verify user is in room
      const success = await chatScalabilityService.joinTradeRoom(testUsers[0]._id, testTrade.tradeId);
      expect(success).toBe(true);
    });
  });

  describe('Message Read Status', () => {
    test('should mark messages as read', async () => {
      // Create messages
      const message1 = await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Message 1',
        'text'
      );
      
      const message2 = await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Message 2',
        'text'
      );

      // Mark as read
      const readCount = await chatScalabilityService.markMessagesAsRead(
        testTrade._id,
        testUsers[1]._id.toString(),
        [message1._id, message2._id]
      );

      expect(readCount).toBe(2);
    });

    test('should get unread count', async () => {
      // Create messages from user1
      await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Unread message 1',
        'text'
      );
      
      await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Unread message 2',
        'text'
      );

      // Get unread count for user2
      const unreadCount = await chatScalabilityService.getUnreadCount(
        testTrade._id,
        testUsers[1]._id.toString()
      );

      expect(unreadCount).toBe(2);
    });
  });

  describe('Performance Optimization', () => {
    test('should handle high message volume', async () => {
      const startTime = Date.now();
      
      // Create 100 messages rapidly
      const messagePromises = [];
      for (let i = 0; i < 100; i++) {
        messagePromises.push(
          chatScalabilityService.createMessage(
            testTrade._id,
            testUsers[i % 2]._id,
            `Performance test message ${i}`,
            'text'
          )
        );
      }
      
      await Promise.all(messagePromises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(5000); // 5 seconds
      
      // Verify all messages were created
      const messageCount = await Message.countDocuments({ trade: testTrade._id });
      expect(messageCount).toBe(100);
    });

    test('should maintain performance metrics', async () => {
      // Create some messages
      await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Metrics test message',
        'text'
      );

      const metrics = chatScalabilityService.getMetrics();
      
      expect(metrics).toHaveProperty('messagesProcessed');
      expect(metrics).toHaveProperty('cacheHits');
      expect(metrics).toHaveProperty('cacheMisses');
      expect(metrics).toHaveProperty('activeConnections');
      expect(metrics.messagesProcessed).toBeGreaterThan(0);
    });
  });

  describe('API Endpoints', () => {
    test('should get messages via API', async () => {
      // Create chat and messages
      await Chat.createForTrade(testTrade._id, testUsers[0]._id, testUsers[1]._id);
      
      await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'API test message',
        'text'
      );

      const response = await request(app)
        .get(`/api/chat/${testTrade.tradeId}/messages`)
        .set('Authorization', `Bearer ${authTokens[0]}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.messages).toBeDefined();
      expect(response.body.data.messages.length).toBeGreaterThan(0);
    });

    test('should send message via API', async () => {
      // Create chat
      await Chat.createForTrade(testTrade._id, testUsers[0]._id, testUsers[1]._id);

      const response = await request(app)
        .post(`/api/chat/${testTrade.tradeId}/messages`)
        .set('Authorization', `Bearer ${authTokens[0]}`)
        .send({
          content: 'API message test',
          type: 'text'
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.messageId).toBeDefined();
    });

    test('should mark messages as read via API', async () => {
      // Create chat and message
      await Chat.createForTrade(testTrade._id, testUsers[0]._id, testUsers[1]._id);
      
      const message = await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Read test message',
        'text'
      );

      const response = await request(app)
        .post(`/api/chat/${testTrade.tradeId}/read`)
        .set('Authorization', `Bearer ${authTokens[1]}`)
        .send({
          messageIds: [message._id]
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.readCount).toBe(1);
    });

    test('should get unread count via API', async () => {
      // Create chat and messages
      await Chat.createForTrade(testTrade._id, testUsers[0]._id, testUsers[1]._id);
      
      await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Unread API test 1',
        'text'
      );
      
      await chatScalabilityService.createMessage(
        testTrade._id,
        testUsers[0]._id,
        'Unread API test 2',
        'text'
      );

      const response = await request(app)
        .get('/api/chat/unread-count')
        .set('Authorization', `Bearer ${authTokens[1]}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.totalUnread).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid trade ID', async () => {
      await expect(
        chatScalabilityService.createMessage(
          new mongoose.Types.ObjectId(),
          testUsers[0]._id,
          'Invalid trade test',
          'text'
        )
      ).rejects.toThrow();
    });

    test('should handle empty message content', async () => {
      await expect(
        chatScalabilityService.createMessage(
          testTrade._id,
          testUsers[0]._id,
          '',
          'text'
        )
      ).rejects.toThrow();
    });

    test('should handle unauthorized access', async () => {
      const response = await request(app)
        .get(`/api/chat/${testTrade.tradeId}/messages`)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });
});
