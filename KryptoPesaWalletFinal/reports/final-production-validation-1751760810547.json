{"timestamp": "2025-07-06T00:10:59.045Z", "overallScore": 54, "readyForProduction": false, "categories": {"codeQuality": {"score": 96, "status": "passed", "issues": ["2 console statements found in production code"]}, "configuration": {"score": 0, "status": "failed", "issues": ["Missing required environment variable: NODE_ENV", "Missing required environment variable: JWT_SECRET", "Missing required environment variable: ENCRYPTION_KEY", "Missing required environment variable: SESSION_SECRET", "Missing required environment variable: MONGODB_URI", "Missing required environment variable: REDIS_URL", "9 hardcoded values found"]}, "security": {"score": 0, "status": "failed", "issues": ["209 exposed secrets found"]}, "performance": {"score": 100, "status": "passed", "issues": []}, "errorHandling": {"score": 100, "status": "passed", "issues": []}, "testing": {"score": 100, "status": "passed", "issues": []}, "documentation": {"score": 90, "status": "passed", "issues": ["Missing documentation: docs/API_DOCUMENTATION.md"]}, "dependencies": {"score": 100, "status": "passed", "issues": []}}, "criticalIssues": ["Critical environment variables missing", "Exposed secrets found - immediate action required"], "recommendations": [{"category": "Critical Issues", "priority": "critical", "recommendation": "Resolve 2 critical issues before production deployment", "issues": ["Critical environment variables missing", "Exposed secrets found - immediate action required"]}, {"category": "configuration", "priority": "high", "recommendation": "Improve configuration score from 0 to at least 90", "issues": ["Missing required environment variable: NODE_ENV", "Missing required environment variable: JWT_SECRET", "Missing required environment variable: ENCRYPTION_KEY", "Missing required environment variable: SESSION_SECRET", "Missing required environment variable: MONGODB_URI", "Missing required environment variable: REDIS_URL", "9 hardcoded values found"]}, {"category": "security", "priority": "high", "recommendation": "Improve security score from 0 to at least 90", "issues": ["209 exposed secrets found"]}], "summary": {"overallScore": 54, "readyForProduction": false, "criticalIssues": 2, "recommendations": 3, "categoriesScores": {"codeQuality": 96, "configuration": 0, "security": 0, "performance": 100, "errorHandling": 100, "testing": 100, "documentation": 90, "dependencies": 100}, "completedAt": "2025-07-06T00:13:30.547Z"}}