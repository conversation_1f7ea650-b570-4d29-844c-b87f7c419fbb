/**
 * E2E Business Flow Testing
 * Complex business scenarios and edge cases
 */

// Set up test environment variables before importing server
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long-for-testing';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-chars-long-for-testing';
process.env.SESSION_SECRET = 'test-session-secret-32-chars-long-for-testing';
process.env.MONGODB_URI = 'mongodb://localhost:27017/kryptopesa_test';
process.env.REDIS_URL = 'redis://localhost:6379';

const testEnvironment = require('../testEnvironment');
const request = require('supertest');
const { app, cleanupTestIntervals } = require('../testServer');

describe('E2E Business Flow Tests', () => {
  beforeAll(async () => {
    await testEnvironment.setup();
  }, 60000);

  beforeEach(async () => {
    await testEnvironment.cleanDatabase();
  });

  afterEach(async () => {
    await testEnvironment.cleanup();
  });

  afterAll(async () => {
    cleanupTestIntervals();
    await testEnvironment.teardown();
  });

  describe('Multi-User Trading Scenarios', () => {
    test('should handle multiple users accessing trading platform simultaneously', async () => {
      const users = [];
      
      // Create multiple users
      for (let i = 0; i < 3; i++) {
        const registerResponse = await request(app)
          .post('/api/auth/register')
          .send({
            email: `trader${i}@example.com`,
            username: `trader${i}`,
            phone: `+***********${i}`,
            password: 'Krypt0P3sa!2024#Secure',
            firstName: 'Trader',
            lastName: `User${i}`,
            country: 'KE',
            city: 'Nairobi'
          });

        expect(registerResponse.status).toBe(201);
        users.push({
          token: registerResponse.body.data.accessToken,
          id: registerResponse.body.data.user._id
        });
      }

      // All users access trading platform simultaneously
      const tradingRequests = users.map(user =>
        request(app)
          .get('/api/trading/offers')
          .set('Authorization', `Bearer ${user.token}`)
      );

      const responses = await Promise.all(tradingRequests);
      
      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });

    test('should handle wallet creation for multiple users', async () => {
      const users = [];
      
      // Create multiple users
      for (let i = 0; i < 3; i++) {
        const registerResponse = await request(app)
          .post('/api/auth/register')
          .send({
            email: `wallet${i}@example.com`,
            username: `wallet${i}`,
            phone: `+25470000020${i}`,
            password: 'Krypt0P3sa!2024#Secure',
            firstName: 'Wallet',
            lastName: `User${i}`,
            country: 'KE',
            city: 'Nairobi'
          });

        users.push(registerResponse.body.data.accessToken);
      }

      // All users create wallets simultaneously
      const walletRequests = users.map(token =>
        request(app)
          .post('/api/wallet/create')
          .set('Authorization', `Bearer ${token}`)
      );

      const responses = await Promise.all(walletRequests);
      
      // All should succeed with unique addresses
      const addresses = new Set();
      responses.forEach(response => {
        expect(response.status).toBe(201);
        expect(response.body.success).toBe(true);
        addresses.add(response.body.data.addresses.ethereum);
        addresses.add(response.body.data.addresses.bitcoin);
      });

      // All addresses should be unique
      expect(addresses.size).toBe(6); // 3 users × 2 addresses each
    });
  });

  describe('Complex Authentication Scenarios', () => {
    test('should handle user registration with edge case data', async () => {
      // Test with special characters in names
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'special_user_123',
          phone: '+254700000999',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: "O'Connor",
          lastName: 'Van Der Berg',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(registerResponse.status).toBe(201);
      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.user.profile.firstName).toBe("O'Connor");
      expect(registerResponse.body.data.user.profile.lastName).toBe('Van Der Berg');
    });

    test('should handle login with different identifier types', async () => {
      // Register user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'multilogin',
          phone: '+254700000888',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Multi',
          lastName: 'Login',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(registerResponse.status).toBe(201);

      // Test login with email
      const emailLoginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'Krypt0P3sa!2024#Secure'
        });

      expect(emailLoginResponse.status).toBe(200);
      expect(emailLoginResponse.body.success).toBe(true);

      // Test login with username
      const usernameLoginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: 'multilogin',
          password: 'Krypt0P3sa!2024#Secure'
        });

      expect(usernameLoginResponse.status).toBe(200);
      expect(usernameLoginResponse.body.success).toBe(true);
    });
  });

  describe('Data Integrity and Consistency', () => {
    test('should maintain data consistency across multiple operations', async () => {
      // Register user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'consistency',
          phone: '+254700000777',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Consistency',
          lastName: 'Test',
          country: 'KE',
          city: 'Nairobi'
        });

      const authToken = registerResponse.body.data.accessToken;

      // Create wallet
      const walletResponse = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`);

      expect(walletResponse.status).toBe(201);

      // Perform multiple operations
      const operations = [
        request(app).get('/api/trading/offers').set('Authorization', `Bearer ${authToken}`),
        request(app).get('/api/trading/trades/active').set('Authorization', `Bearer ${authToken}`),
        request(app).get('/api/wallet/transactions').set('Authorization', `Bearer ${authToken}`),
        request(app).get('/api/wallet/security/balance-stats').set('Authorization', `Bearer ${authToken}`)
      ];

      const responses = await Promise.all(operations);
      
      // All operations should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });

    test('should handle database constraints properly', async () => {
      // Register first user
      const firstUserResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'unique',
          phone: '+254700000666',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Unique',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(firstUserResponse.status).toBe(201);

      // Attempt to register with same email
      const duplicateEmailResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>', // Same email
          username: 'different',
          phone: '+254700000555',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Different',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(duplicateEmailResponse.status).toBe(400);
      expect(duplicateEmailResponse.body.success).toBe(false);

      // Attempt to register with same username
      const duplicateUsernameResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'unique', // Same username
          phone: '+254700000444',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Different',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(duplicateUsernameResponse.status).toBe(400);
      expect(duplicateUsernameResponse.body.success).toBe(false);
    });
  });

  describe('System Resilience and Recovery', () => {
    test('should handle malformed authentication tokens', async () => {
      // Test with malformed token
      const malformedTokenResponse = await request(app)
        .get('/api/trading/offers')
        .set('Authorization', 'Bearer malformed.token.here');

      expect(malformedTokenResponse.status).toBe(401);
      expect(malformedTokenResponse.body.success).toBe(false);

      // Test with expired-format token (but invalid)
      const expiredTokenResponse = await request(app)
        .get('/api/trading/offers')
        .set('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature');

      expect(expiredTokenResponse.status).toBe(401);
      expect(expiredTokenResponse.body.success).toBe(false);
    });

    test('should handle network interruption simulation', async () => {
      // Register user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'network',
          phone: '+254700000333',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Network',
          lastName: 'Test',
          country: 'KE',
          city: 'Nairobi'
        });

      const authToken = registerResponse.body.data.accessToken;

      // Simulate rapid requests (network recovery)
      const rapidRequests = [];
      for (let i = 0; i < 5; i++) {
        rapidRequests.push(
          request(app)
            .get('/api/trading/offers')
            .set('Authorization', `Bearer ${authToken}`)
        );
      }

      const responses = await Promise.all(rapidRequests);
      
      // All should succeed or be properly rate limited
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status);
      });
    });
  });

  describe('Cross-Feature Integration', () => {
    test('should handle complete user journey with all features', async () => {
      // Step 1: Register
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'complete',
          phone: '+254700000222',
          password: 'Krypt0P3sa!2024#Secure',
          firstName: 'Complete',
          lastName: 'Journey',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(registerResponse.status).toBe(201);
      const authToken = registerResponse.body.data.accessToken;

      // Step 2: Login (verify credentials work)
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'Krypt0P3sa!2024#Secure'
        });

      expect(loginResponse.status).toBe(200);

      // Step 3: Create wallet
      const walletResponse = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${authToken}`);

      expect(walletResponse.status).toBe(201);

      // Step 4: Access all major features
      const featureTests = [
        request(app).get('/api/trading/offers').set('Authorization', `Bearer ${authToken}`),
        request(app).get('/api/trading/trades/active').set('Authorization', `Bearer ${authToken}`),
        request(app).get('/api/wallet/transactions').set('Authorization', `Bearer ${authToken}`),
        request(app).get('/api/wallet/security/balance-stats').set('Authorization', `Bearer ${authToken}`)
      ];

      const featureResponses = await Promise.all(featureTests);
      
      // All features should be accessible
      featureResponses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });
  });
});
