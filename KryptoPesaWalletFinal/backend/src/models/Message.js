const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  trade: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trade',
    required: true,
    index: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null // null for system messages
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000,
    trim: true
  },
  type: {
    type: String,
    enum: ['text', 'image', 'file', 'system', 'payment_proof'],
    default: 'text'
  },
  attachmentUrl: {
    type: String,
    default: null
  },
  attachmentType: {
    type: String,
    enum: ['image', 'document', 'video'],
    default: null
  },
  attachmentSize: {
    type: Number,
    default: null
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  isRead: {
    type: Boolean,
    default: false
  },
  readAt: {
    type: Date,
    default: null
  },
  isEdited: {
    type: Boolean,
    default: false
  },
  editedAt: {
    type: Date,
    default: null
  },
  originalMessage: {
    type: String,
    default: null
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedAt: {
    type: Date,
    default: null
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
messageSchema.index({ trade: 1, timestamp: -1 });
messageSchema.index({ sender: 1, timestamp: -1 });
messageSchema.index({ trade: 1, isRead: 1 });
messageSchema.index({ trade: 1, type: 1 });

// Virtual for sender display name
messageSchema.virtual('senderDisplayName').get(function() {
  if (!this.sender) return 'System';
  if (this.sender.firstName && this.sender.lastName) {
    return `${this.sender.firstName} ${this.sender.lastName}`;
  }
  return this.sender.username;
});

// Virtual for attachment info
messageSchema.virtual('hasAttachment').get(function() {
  return !!this.attachmentUrl;
});

// Virtual for formatted timestamp
messageSchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toISOString();
});

// Pre-save middleware
messageSchema.pre('save', function(next) {
  // Validate attachment data
  if (this.attachmentUrl && !this.attachmentType) {
    const url = this.attachmentUrl.toLowerCase();
    if (url.includes('.jpg') || url.includes('.jpeg') || url.includes('.png') || url.includes('.gif')) {
      this.attachmentType = 'image';
    } else if (url.includes('.pdf') || url.includes('.doc') || url.includes('.txt')) {
      this.attachmentType = 'document';
    } else if (url.includes('.mp4') || url.includes('.mov') || url.includes('.avi')) {
      this.attachmentType = 'video';
    }
  }

  // Set type based on attachment
  if (this.attachmentUrl && this.type === 'text') {
    if (this.attachmentType === 'image') {
      this.type = 'image';
    } else {
      this.type = 'file';
    }
  }

  next();
});

// Static methods
messageSchema.statics.getTradeMessages = function(tradeId, options = {}) {
  const {
    limit = 50,
    offset = 0,
    includeDeleted = false,
    messageType = null
  } = options;

  const query = { trade: tradeId };
  
  if (!includeDeleted) {
    query.isDeleted = false;
  }
  
  if (messageType) {
    query.type = messageType;
  }

  return this.find(query)
    .populate('sender', 'username firstName lastName avatar isOnline')
    .sort({ timestamp: -1 })
    .limit(limit)
    .skip(offset);
};

messageSchema.statics.getUnreadCount = function(tradeId, userId) {
  return this.countDocuments({
    trade: tradeId,
    sender: { $ne: userId },
    isRead: false,
    isDeleted: false
  });
};

messageSchema.statics.markAsRead = function(tradeId, userId, messageIds = null) {
  const query = {
    trade: tradeId,
    sender: { $ne: userId },
    isRead: false,
    isDeleted: false
  };

  if (messageIds) {
    query._id = { $in: messageIds };
  }

  return this.updateMany(query, {
    isRead: true,
    readAt: new Date()
  });
};

messageSchema.statics.getMessageStats = function(tradeId) {
  return this.aggregate([
    { $match: { trade: mongoose.Types.ObjectId(tradeId), isDeleted: false } },
    {
      $group: {
        _id: null,
        totalMessages: { $sum: 1 },
        textMessages: {
          $sum: { $cond: [{ $eq: ['$type', 'text'] }, 1, 0] }
        },
        imageMessages: {
          $sum: { $cond: [{ $eq: ['$type', 'image'] }, 1, 0] }
        },
        systemMessages: {
          $sum: { $cond: [{ $eq: ['$type', 'system'] }, 1, 0] }
        },
        unreadMessages: {
          $sum: { $cond: [{ $eq: ['$isRead', false] }, 1, 0] }
        },
        firstMessage: { $min: '$timestamp' },
        lastMessage: { $max: '$timestamp' }
      }
    }
  ]);
};

// Instance methods
messageSchema.methods.markAsRead = function() {
  this.isRead = true;
  this.readAt = new Date();
  return this.save();
};

messageSchema.methods.softDelete = function() {
  this.isDeleted = true;
  this.deletedAt = new Date();
  return this.save();
};

messageSchema.methods.edit = function(newMessage) {
  this.originalMessage = this.message;
  this.message = newMessage;
  this.isEdited = true;
  this.editedAt = new Date();
  return this.save();
};

messageSchema.methods.toClientJSON = function() {
  return {
    id: this._id,
    tradeId: this.trade,
    senderId: this.sender?._id || null,
    senderUsername: this.sender?.username || 'System',
    senderDisplayName: this.senderDisplayName,
    message: this.message,
    type: this.type,
    timestamp: this.timestamp,
    isRead: this.isRead,
    readAt: this.readAt,
    attachmentUrl: this.attachmentUrl,
    attachmentType: this.attachmentType,
    attachmentSize: this.attachmentSize,
    hasAttachment: this.hasAttachment,
    isEdited: this.isEdited,
    editedAt: this.editedAt,
    isSystem: !this.sender,
    metadata: this.metadata
  };
};

// Middleware to clean up old messages (optional)
messageSchema.statics.cleanupOldMessages = function(daysOld = 90) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);
  
  return this.deleteMany({
    timestamp: { $lt: cutoffDate },
    isDeleted: true
  });
};

const Message = mongoose.model('Message', messageSchema);

module.exports = Message;
