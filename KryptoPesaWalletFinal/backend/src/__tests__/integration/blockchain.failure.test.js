/**
 * Blockchain Failure Integration Tests
 * Tests system behavior when blockchain operations fail
 */

const mongoose = require('mongoose');
const Trade = require('../../models/Trade');
const User = require('../../models/User');
const Offer = require('../../models/Offer');
const ethereumService = require('../../services/blockchain/ethereumService');
const { deadLetterQueue } = require('../../services/deadLetterQueue');
const { circuitBreakers } = require('../../utils/circuitBreaker');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock blockchain service
jest.mock('../../services/blockchain/ethereumService');

describe('Blockchain Failure Integration Tests', () => {
  let mongoServer;
  let seller, buyer, offer, trade;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
    
    // Initialize dead letter queue
    await deadLetterQueue.initialize();
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean collections
    await Trade.deleteMany({});
    await User.deleteMany({});
    await Offer.deleteMany({});

    // Reset circuit breaker
    circuitBreakers.blockchain.reset();

    // Create test data
    seller = await createTestUser({ username: 'seller', email: '<EMAIL>' });
    buyer = await createTestUser({ username: 'buyer', email: '<EMAIL>' });
    offer = await createTestOffer(seller._id);
    trade = await createTestTrade(seller._id, buyer._id, offer._id);

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('Trade Creation Blockchain Failures', () => {
    test('should handle blockchain network timeout during trade creation', async () => {
      // Mock blockchain timeout
      ethereumService.createTrade.mockRejectedValue(
        new Error('Operation timeout after 120000ms')
      );

      await expect(
        trade.createOnBlockchain({
          buyer: buyer.walletAddress,
          tokenAddress: '0x123',
          amount: '100',
          fiatAmount: 13000,
          fiatCurrency: 'KES',
          paymentMethod: 'M-Pesa',
          paymentHash: '0xabc'
        })
      ).rejects.toThrow('Operation timeout');

      // Verify trade status remains unchanged
      const updatedTrade = await Trade.findById(trade._id);
      expect(updatedTrade.status).toBe('created');
      expect(updatedTrade.blockchainTradeId).toBeUndefined();
    });

    test('should handle insufficient gas errors gracefully', async () => {
      ethereumService.createTrade.mockRejectedValue(
        new Error('gas required exceeds allowance or always failing transaction')
      );

      await expect(
        trade.createOnBlockchain({
          buyer: buyer.walletAddress,
          tokenAddress: '0x123',
          amount: '100'
        })
      ).rejects.toThrow('gas required exceeds allowance');

      // Verify error is logged and trade can be retried
      const updatedTrade = await Trade.findById(trade._id);
      expect(updatedTrade.status).toBe('created');
    });

    test('should handle nonce conflicts during concurrent transactions', async () => {
      ethereumService.createTrade.mockRejectedValue(
        new Error('nonce too low')
      );

      await expect(
        trade.createOnBlockchain({
          buyer: buyer.walletAddress,
          tokenAddress: '0x123',
          amount: '100'
        })
      ).rejects.toThrow('nonce too low');

      // This error should trigger retry mechanism
      expect(ethereumService.createTrade).toHaveBeenCalledTimes(1);
    });
  });

  describe('Trade Funding Blockchain Failures', () => {
    beforeEach(async () => {
      // Set trade to funded status for funding tests
      await trade.updateStatus('funded', seller._id, 'Ready for blockchain funding');
    });

    test('should handle blockchain funding failure with rollback', async () => {
      ethereumService.fundTrade.mockRejectedValue(
        new Error('execution reverted: insufficient balance')
      );

      await expect(
        trade.fundOnBlockchain(123)
      ).rejects.toThrow('insufficient balance');

      // Verify trade status is rolled back
      const updatedTrade = await Trade.findById(trade._id);
      expect(updatedTrade.status).toBe('funded'); // Should remain funded, not move to blockchain_funded
    });

    test('should handle network congestion during funding', async () => {
      ethereumService.fundTrade.mockRejectedValue(
        new Error('replacement transaction underpriced')
      );

      await expect(
        trade.fundOnBlockchain(123)
      ).rejects.toThrow('replacement transaction underpriced');

      // This should not trigger retries (non-retryable error)
      expect(ethereumService.fundTrade).toHaveBeenCalledTimes(1);
    });
  });

  describe('Circuit Breaker Integration', () => {
    test('should trip circuit breaker after multiple blockchain failures', async () => {
      // Configure circuit breaker for testing
      const blockchainCB = circuitBreakers.blockchain;
      
      // Mock multiple failures
      ethereumService.createTrade.mockRejectedValue(
        new Error('Network error')
      );

      // Trigger multiple failures to trip circuit breaker
      for (let i = 0; i < 6; i++) {
        try {
          await trade.createOnBlockchain({
            buyer: buyer.walletAddress,
            tokenAddress: '0x123',
            amount: '100'
          });
        } catch (error) {
          // Expected to fail
        }
      }

      // Circuit breaker should be open
      expect(blockchainCB.getState().state).toBe('OPEN');
    });

    test('should use fallback when circuit breaker is open', async () => {
      // Force circuit breaker open
      circuitBreakers.blockchain.forceOpen();

      // Attempt blockchain operation
      await expect(
        trade.createOnBlockchain({
          buyer: buyer.walletAddress,
          tokenAddress: '0x123',
          amount: '100'
        })
      ).rejects.toThrow('Circuit breaker blockchain is OPEN');

      // Verify blockchain service was not called
      expect(ethereumService.createTrade).not.toHaveBeenCalled();
    });
  });

  describe('Dead Letter Queue Integration', () => {
    test('should queue failed blockchain operations for retry', async () => {
      ethereumService.createTrade.mockRejectedValue(
        new Error('Temporary network error')
      );

      const operation = {
        type: 'blockchain_transaction',
        data: {
          tradeId: trade._id,
          operation: 'createTrade',
          params: {
            buyer: buyer.walletAddress,
            tokenAddress: '0x123',
            amount: '100'
          }
        },
        timestamp: new Date().toISOString()
      };

      // Add to dead letter queue
      await deadLetterQueue.addFailedOperation(
        'blockchain',
        operation,
        new Error('Temporary network error'),
        { tradeId: trade._id }
      );

      // Verify operation was queued
      const stats = await deadLetterQueue.getQueueStats();
      expect(stats.blockchain.pending).toBeGreaterThan(0);
    });

    test('should process queued blockchain operations when service recovers', async () => {
      // First, add a failed operation to queue
      const operation = {
        type: 'blockchain_transaction',
        data: {
          tradeId: trade._id,
          operation: 'createTrade'
        }
      };

      await deadLetterQueue.addFailedOperation(
        'blockchain',
        operation,
        new Error('Network error')
      );

      // Mock successful retry
      ethereumService.createTrade.mockResolvedValue({
        tradeId: 123,
        transactionHash: '0x123',
        blockNumber: 12345
      });

      // Process the queue
      await deadLetterQueue.processQueue('blockchain');

      // Verify the operation was retried
      expect(ethereumService.createTrade).toHaveBeenCalled();
    });
  });

  describe('Data Consistency During Failures', () => {
    test('should maintain database consistency when blockchain fails', async () => {
      const originalStatus = trade.status;
      
      ethereumService.createTrade.mockRejectedValue(
        new Error('Blockchain failure')
      );

      // Attempt operation that should fail
      try {
        await trade.createOnBlockchain({
          buyer: buyer.walletAddress,
          tokenAddress: '0x123',
          amount: '100'
        });
      } catch (error) {
        // Expected to fail
      }

      // Verify database state is unchanged
      const updatedTrade = await Trade.findById(trade._id);
      expect(updatedTrade.status).toBe(originalStatus);
      expect(updatedTrade.blockchainTradeId).toBeUndefined();
    });

    test('should handle partial blockchain state updates', async () => {
      // Mock blockchain service to succeed initially then fail
      ethereumService.createTrade
        .mockResolvedValueOnce({
          tradeId: 123,
          transactionHash: '0x123',
          blockNumber: 12345
        })
        .mockRejectedValue(new Error('Confirmation failed'));

      // First call should succeed
      const result = await trade.createOnBlockchain({
        buyer: buyer.walletAddress,
        tokenAddress: '0x123',
        amount: '100'
      });

      expect(result.tradeId).toBe(123);

      // Verify trade was updated with blockchain info
      const updatedTrade = await Trade.findById(trade._id);
      expect(updatedTrade.blockchainTradeId).toBe(123);
    });
  });

  describe('Recovery Mechanisms', () => {
    test('should recover from temporary blockchain outages', async () => {
      // Mock temporary failure followed by success
      ethereumService.createTrade
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockResolvedValue({
          tradeId: 123,
          transactionHash: '0x123',
          blockNumber: 12345
        });

      // Should eventually succeed with retry logic
      const result = await trade.createOnBlockchain({
        buyer: buyer.walletAddress,
        tokenAddress: '0x123',
        amount: '100'
      });

      expect(result.tradeId).toBe(123);
      expect(ethereumService.createTrade).toHaveBeenCalledTimes(3);
    });

    test('should handle blockchain service restart scenarios', async () => {
      // Simulate service restart by resetting circuit breaker
      circuitBreakers.blockchain.forceOpen();
      
      // Wait for circuit breaker to attempt recovery
      setTimeout(() => {
        circuitBreakers.blockchain.forceClose();
      }, 100);

      // Mock successful operation after restart
      ethereumService.createTrade.mockResolvedValue({
        tradeId: 123,
        transactionHash: '0x123',
        blockNumber: 12345
      });

      // Should work after circuit breaker closes
      await new Promise(resolve => setTimeout(resolve, 150));
      
      const result = await trade.createOnBlockchain({
        buyer: buyer.walletAddress,
        tokenAddress: '0x123',
        amount: '100'
      });

      expect(result.tradeId).toBe(123);
    });
  });

  // Helper functions
  async function createTestUser(userData = {}) {
    const defaultUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'hashedpassword',
      phone: '+254700000000',
      walletAddress: '0x' + Math.random().toString(16).substr(2, 40),
      profile: {
        firstName: 'Test',
        lastName: 'User',
        location: { country: 'KE', city: 'Nairobi' }
      },
      verification: {
        email: { verified: true },
        phone: { verified: true }
      }
    };
    
    const user = new User({ ...defaultUser, ...userData });
    await user.save();
    return user;
  }

  async function createTestOffer(userId, offerData = {}) {
    const defaultOffer = {
      creator: userId,
      type: 'sell',
      cryptocurrency: {
        symbol: 'USDT',
        amount: '1000',
        availableAmount: '1000',
        network: 'polygon'
      },
      fiat: {
        currency: 'KES',
        amount: 130000,
        paymentMethods: ['M-Pesa']
      },
      terms: {
        minAmount: 1000,
        maxAmount: 130000,
        timeLimit: 30
      },
      status: 'active'
    };
    
    const offer = new Offer({ ...defaultOffer, ...offerData });
    await offer.save();
    return offer;
  }

  async function createTestTrade(sellerId, buyerId, offerId, tradeData = {}) {
    const defaultTrade = {
      tradeId: `TRD${Date.now()}${Math.random().toString(36).substr(2, 5)}`,
      seller: sellerId,
      buyer: buyerId,
      offer: offerId,
      cryptocurrency: {
        symbol: 'USDT',
        amount: '100',
        network: 'polygon'
      },
      fiat: {
        currency: 'KES',
        amount: 13000
      },
      status: 'created',
      expiresAt: new Date(Date.now() + 30 * 60 * 1000)
    };
    
    const trade = new Trade({ ...defaultTrade, ...tradeData });
    
    // Add mock blockchain methods
    trade.createOnBlockchain = jest.fn().mockImplementation(async (params) => {
      return await ethereumService.createTrade(params);
    });
    
    trade.fundOnBlockchain = jest.fn().mockImplementation(async (tradeId) => {
      return await ethereumService.fundTrade(tradeId);
    });
    
    await trade.save();
    return trade;
  }
});
