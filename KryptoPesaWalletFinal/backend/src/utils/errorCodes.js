/**
 * Structured error codes for KryptoPesa platform
 * Format: CATEGORY_SPECIFIC_ERROR
 */

const ERROR_CODES = {
  // Authentication & Authorization (AUTH_*)
  AUTH_INVALID_CREDENTIALS: {
    code: 'AUTH_001',
    message: 'Invalid email or password',
    httpStatus: 401,
    category: 'authentication'
  },
  AUTH_TOKEN_EXPIRED: {
    code: 'AUTH_002',
    message: 'Authentication token has expired',
    httpStatus: 401,
    category: 'authentication'
  },
  AUTH_TOKEN_INVALID: {
    code: 'AUTH_003',
    message: 'Invalid authentication token',
    httpStatus: 401,
    category: 'authentication'
  },
  AUTH_ACCESS_DENIED: {
    code: 'AUTH_004',
    message: 'Access denied - insufficient permissions',
    httpStatus: 403,
    category: 'authorization'
  },
  AUTH_ACCOUNT_SUSPENDED: {
    code: 'AUTH_005',
    message: 'Account has been suspended',
    httpStatus: 403,
    category: 'authentication'
  },
  AUTH_EMAIL_NOT_VERIFIED: {
    code: 'AUTH_006',
    message: 'Email address not verified',
    httpStatus: 403,
    category: 'authentication'
  },

  // User Management (USER_*)
  USER_NOT_FOUND: {
    code: 'USER_001',
    message: 'User not found',
    httpStatus: 404,
    category: 'user'
  },
  USER_EMAIL_EXISTS: {
    code: 'USER_002',
    message: 'Email address already registered',
    httpStatus: 409,
    category: 'user'
  },
  USER_USERNAME_EXISTS: {
    code: 'USER_003',
    message: 'Username already taken',
    httpStatus: 409,
    category: 'user'
  },
  USER_PROFILE_INCOMPLETE: {
    code: 'USER_004',
    message: 'User profile is incomplete',
    httpStatus: 400,
    category: 'user'
  },
  USER_KYC_REQUIRED: {
    code: 'USER_005',
    message: 'KYC verification required for this action',
    httpStatus: 403,
    category: 'user'
  },

  // Wallet Management (WALLET_*)
  WALLET_NOT_FOUND: {
    code: 'WALLET_001',
    message: 'Wallet not found',
    httpStatus: 404,
    category: 'wallet'
  },
  WALLET_ALREADY_EXISTS: {
    code: 'WALLET_002',
    message: 'Wallet already exists for this user',
    httpStatus: 409,
    category: 'wallet'
  },
  WALLET_INVALID_MNEMONIC: {
    code: 'WALLET_003',
    message: 'Invalid mnemonic phrase',
    httpStatus: 400,
    category: 'wallet'
  },
  WALLET_INSUFFICIENT_BALANCE: {
    code: 'WALLET_004',
    message: 'Insufficient wallet balance',
    httpStatus: 400,
    category: 'wallet'
  },
  WALLET_TRANSACTION_FAILED: {
    code: 'WALLET_005',
    message: 'Wallet transaction failed',
    httpStatus: 500,
    category: 'wallet'
  },

  // Trading (TRADE_*)
  TRADE_NOT_FOUND: {
    code: 'TRADE_001',
    message: 'Trade not found',
    httpStatus: 404,
    category: 'trade'
  },
  TRADE_INVALID_STATUS: {
    code: 'TRADE_002',
    message: 'Invalid trade status for this operation',
    httpStatus: 400,
    category: 'trade'
  },
  TRADE_UNAUTHORIZED_PARTICIPANT: {
    code: 'TRADE_003',
    message: 'User is not a participant in this trade',
    httpStatus: 403,
    category: 'trade'
  },
  TRADE_AMOUNT_INVALID: {
    code: 'TRADE_004',
    message: 'Trade amount is invalid or out of range',
    httpStatus: 400,
    category: 'trade'
  },
  TRADE_ALREADY_FUNDED: {
    code: 'TRADE_005',
    message: 'Trade has already been funded',
    httpStatus: 409,
    category: 'trade'
  },
  TRADE_FUNDING_TIMEOUT: {
    code: 'TRADE_006',
    message: 'Trade funding timeout exceeded',
    httpStatus: 408,
    category: 'trade'
  },

  // Offers (OFFER_*)
  OFFER_NOT_FOUND: {
    code: 'OFFER_001',
    message: 'Offer not found',
    httpStatus: 404,
    category: 'offer'
  },
  OFFER_INACTIVE: {
    code: 'OFFER_002',
    message: 'Offer is not active',
    httpStatus: 400,
    category: 'offer'
  },
  OFFER_INSUFFICIENT_AMOUNT: {
    code: 'OFFER_003',
    message: 'Insufficient offer amount available',
    httpStatus: 400,
    category: 'offer'
  },
  OFFER_SELF_TRADE: {
    code: 'OFFER_004',
    message: 'Cannot trade with your own offer',
    httpStatus: 400,
    category: 'offer'
  },
  OFFER_EXPIRED: {
    code: 'OFFER_005',
    message: 'Offer has expired',
    httpStatus: 410,
    category: 'offer'
  },

  // Disputes (DISPUTE_*)
  DISPUTE_NOT_FOUND: {
    code: 'DISPUTE_001',
    message: 'Dispute not found',
    httpStatus: 404,
    category: 'dispute'
  },
  DISPUTE_ALREADY_EXISTS: {
    code: 'DISPUTE_002',
    message: 'Dispute already exists for this trade',
    httpStatus: 409,
    category: 'dispute'
  },
  DISPUTE_INVALID_STATUS: {
    code: 'DISPUTE_003',
    message: 'Invalid dispute status for this operation',
    httpStatus: 400,
    category: 'dispute'
  },
  DISPUTE_UNAUTHORIZED: {
    code: 'DISPUTE_004',
    message: 'Not authorized to access this dispute',
    httpStatus: 403,
    category: 'dispute'
  },

  // Payment (PAYMENT_*)
  PAYMENT_METHOD_INVALID: {
    code: 'PAYMENT_001',
    message: 'Invalid payment method',
    httpStatus: 400,
    category: 'payment'
  },
  PAYMENT_PROOF_REQUIRED: {
    code: 'PAYMENT_002',
    message: 'Payment proof is required',
    httpStatus: 400,
    category: 'payment'
  },
  PAYMENT_VERIFICATION_FAILED: {
    code: 'PAYMENT_003',
    message: 'Payment verification failed',
    httpStatus: 400,
    category: 'payment'
  },

  // Validation (VALIDATION_*)
  VALIDATION_FAILED: {
    code: 'VALIDATION_001',
    message: 'Input validation failed',
    httpStatus: 400,
    category: 'validation'
  },
  VALIDATION_REQUIRED_FIELD: {
    code: 'VALIDATION_002',
    message: 'Required field is missing',
    httpStatus: 400,
    category: 'validation'
  },
  VALIDATION_INVALID_FORMAT: {
    code: 'VALIDATION_003',
    message: 'Invalid data format',
    httpStatus: 400,
    category: 'validation'
  },

  // System (SYSTEM_*)
  SYSTEM_MAINTENANCE: {
    code: 'SYSTEM_001',
    message: 'System is under maintenance',
    httpStatus: 503,
    category: 'system'
  },
  SYSTEM_OVERLOADED: {
    code: 'SYSTEM_002',
    message: 'System is temporarily overloaded',
    httpStatus: 503,
    category: 'system'
  },
  SYSTEM_DATABASE_ERROR: {
    code: 'SYSTEM_003',
    message: 'Database operation failed',
    httpStatus: 500,
    category: 'system'
  },
  SYSTEM_EXTERNAL_SERVICE_ERROR: {
    code: 'SYSTEM_004',
    message: 'External service unavailable',
    httpStatus: 502,
    category: 'system'
  },

  // Rate Limiting (RATE_*)
  RATE_LIMIT_EXCEEDED: {
    code: 'RATE_001',
    message: 'Rate limit exceeded',
    httpStatus: 429,
    category: 'rate_limit'
  },

  // File Upload (FILE_*)
  FILE_TOO_LARGE: {
    code: 'FILE_001',
    message: 'File size exceeds maximum limit',
    httpStatus: 413,
    category: 'file'
  },
  FILE_INVALID_TYPE: {
    code: 'FILE_002',
    message: 'Invalid file type',
    httpStatus: 400,
    category: 'file'
  },
  FILE_UPLOAD_FAILED: {
    code: 'FILE_003',
    message: 'File upload failed',
    httpStatus: 500,
    category: 'file'
  }
};

/**
 * Create a structured error object
 */
const createError = (errorCode, customMessage = null, details = null) => {
  const errorInfo = ERROR_CODES[errorCode];
  
  if (!errorInfo) {
    throw new Error(`Unknown error code: ${errorCode}`);
  }

  return {
    code: errorInfo.code,
    message: customMessage || errorInfo.message,
    httpStatus: errorInfo.httpStatus,
    category: errorInfo.category,
    details,
    timestamp: new Date().toISOString()
  };
};

/**
 * Get error by code
 */
const getErrorByCode = (code) => {
  return Object.values(ERROR_CODES).find(error => error.code === code);
};

/**
 * Get errors by category
 */
const getErrorsByCategory = (category) => {
  return Object.values(ERROR_CODES).filter(error => error.category === category);
};

module.exports = {
  ERROR_CODES,
  createError,
  getErrorByCode,
  getErrorsByCategory
};
