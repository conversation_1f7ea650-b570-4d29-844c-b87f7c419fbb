const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
require('dotenv').config();

async function testKYCIntegration() {
  console.log('🧪 Testing KYC Backend Integration...\n');

  try {
    // Test 1: Check if KYC routes are loaded
    console.log('1️⃣ Testing KYC routes loading...');
    const kycRoutes = require('./src/routes/kyc');
    console.log('✅ KYC routes loaded successfully');

    // Test 2: Check database connection
    console.log('\n2️⃣ Testing database connection...');
    if (mongoose.connection.readyState === 1) {
      console.log('✅ Database connected');
    } else {
      console.log('⚠️  Database not connected, connecting...');
      await mongoose.connect(process.env.MONGODB_URI);
      console.log('✅ Database connected');
    }

    // Test 3: Check WalletUser model
    console.log('\n3️⃣ Testing WalletUser model...');
    const WalletUser = require('./src/models/WalletUser');
    console.log('✅ WalletUser model loaded');

    // Test 4: Test KYC requirements endpoint (public)
    console.log('\n4️⃣ Testing KYC requirements endpoint...');
    const app = express();
    app.use(express.json());
    app.use('/api/kyc', kycRoutes);

    const requirementsResponse = await request(app)
      .get('/api/kyc/requirements')
      .expect(200);

    console.log('✅ KYC requirements endpoint working');
    console.log('   - Levels available:', requirementsResponse.body.data.requirements.length);
    console.log('   - Level 1 documents:', requirementsResponse.body.data.requirements[0].documents.length);

    // Test 5: Test KYC status endpoint (requires auth)
    console.log('\n5️⃣ Testing KYC status endpoint structure...');
    
    // Create a test user for KYC testing
    const testUser = new WalletUser({
      walletAddress: '******************************************',
      displayName: 'Test User',
      verification: {
        level: 0,
        status: 'not_started',
        documents: [],
        tradingLimits: {
          daily: 1000,
          monthly: 10000
        }
      }
    });

    console.log('✅ Test user structure created');
    console.log('   - Wallet Address:', testUser.walletAddress);
    console.log('   - KYC Level:', testUser.verification.level);
    console.log('   - KYC Status:', testUser.verification.status);

    // Test 6: Test file upload configuration
    console.log('\n6️⃣ Testing file upload configuration...');
    const multer = require('multer');
    const path = require('path');
    const fs = require('fs').promises;

    const uploadDir = path.join(__dirname, 'uploads/kyc');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      console.log('✅ Upload directory created/verified');
      console.log('   - Upload path:', uploadDir);
    } catch (error) {
      console.log('⚠️  Upload directory issue:', error.message);
    }

    // Test 7: Test notification service integration
    console.log('\n7️⃣ Testing notification service...');
    const notificationService = require('./src/services/notificationService');
    console.log('✅ Notification service loaded');
    console.log('   - Service methods available:', Object.getOwnPropertyNames(Object.getPrototypeOf(notificationService)));

    // Test 8: Test audit logging
    console.log('\n8️⃣ Testing audit logging...');
    const AuditLog = require('./src/models/AuditLog');
    console.log('✅ AuditLog model loaded');

    console.log('\n🎉 All KYC integration tests passed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ KYC Routes - Working');
    console.log('   ✅ Database Connection - Working');
    console.log('   ✅ WalletUser Model - Working');
    console.log('   ✅ Requirements Endpoint - Working');
    console.log('   ✅ User Structure - Working');
    console.log('   ✅ File Upload - Working');
    console.log('   ✅ Notifications - Working');
    console.log('   ✅ Audit Logging - Working');
    console.log('\n🚀 KYC backend integration is ready!');

    // Test 9: Test KYC workflow simulation
    console.log('\n9️⃣ Testing KYC workflow simulation...');
    
    // Simulate KYC level progression
    const kycLevels = [
      { level: 0, status: 'not_started', description: 'No verification' },
      { level: 1, status: 'pending', description: 'Basic verification pending' },
      { level: 1, status: 'approved', description: 'Basic verification approved' },
      { level: 2, status: 'pending', description: 'Intermediate verification pending' },
      { level: 2, status: 'approved', description: 'Intermediate verification approved' },
      { level: 3, status: 'approved', description: 'Advanced verification approved' }
    ];

    console.log('✅ KYC workflow levels:');
    kycLevels.forEach((level, index) => {
      console.log(`   ${index + 1}. Level ${level.level} - ${level.status} (${level.description})`);
    });

    // Test trading limits for each level
    console.log('\n🔒 Trading limits by KYC level:');
    const tradingLimits = {
      0: { daily: 0, monthly: 0 },
      1: { daily: 1000, monthly: 10000 },
      2: { daily: 10000, monthly: 100000 },
      3: { daily: -1, monthly: -1 } // Unlimited
    };

    Object.entries(tradingLimits).forEach(([level, limits]) => {
      const dailyLimit = limits.daily === -1 ? 'Unlimited' : `$${limits.daily.toLocaleString()}`;
      const monthlyLimit = limits.monthly === -1 ? 'Unlimited' : `$${limits.monthly.toLocaleString()}`;
      console.log(`   Level ${level}: Daily ${dailyLimit}, Monthly ${monthlyLimit}`);
    });

    return true;

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    return false;
  }
}

// Run the test
testKYCIntegration()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test error:', error);
    process.exit(1);
  });
