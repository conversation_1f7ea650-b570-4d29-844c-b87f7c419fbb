{"timestamp": "2025-07-06T00:25:20.033Z", "overallScore": 65, "readyForProduction": false, "categories": {"codeQuality": {"score": 96, "status": "passed", "issues": ["2 console statements found in production code"]}, "configuration": {"score": 55, "status": "failed", "issues": ["9 hardcoded values found"]}, "security": {"score": 0, "status": "failed", "issues": ["33 exposed secrets found"]}, "performance": {"score": 100, "status": "passed", "issues": []}, "errorHandling": {"score": 100, "status": "passed", "issues": []}, "testing": {"score": 100, "status": "passed", "issues": []}, "documentation": {"score": 90, "status": "passed", "issues": ["Missing documentation: docs/API_DOCUMENTATION.md"]}, "dependencies": {"score": 100, "status": "passed", "issues": []}}, "criticalIssues": ["Exposed secrets found - immediate action required"], "recommendations": [{"category": "Critical Issues", "priority": "critical", "recommendation": "Resolve 1 critical issues before production deployment", "issues": ["Exposed secrets found - immediate action required"]}, {"category": "configuration", "priority": "high", "recommendation": "Improve configuration score from 55 to at least 90", "issues": ["9 hardcoded values found"]}, {"category": "security", "priority": "high", "recommendation": "Improve security score from 0 to at least 90", "issues": ["33 exposed secrets found"]}], "summary": {"overallScore": 65, "readyForProduction": false, "criticalIssues": 1, "recommendations": 3, "categoriesScores": {"codeQuality": 96, "configuration": 55, "security": 0, "performance": 100, "errorHandling": 100, "testing": 100, "documentation": 90, "dependencies": 100}, "completedAt": "2025-07-06T00:25:21.005Z"}}