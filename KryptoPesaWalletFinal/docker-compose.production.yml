version: "3.8"

services:
  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: kryptopesa-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
      - MONGODB_URI=${MONGODB_URI}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - LOG_LEVEL=info
      - METRICS_ENABLED=true
      - BLOCKCHAIN_NETWORK=mainnet
      - ETHEREUM_RPC_URL=${ETHEREUM_RPC_URL}
      - POLYGON_RPC_URL=${POLYG<PERSON>_RPC_URL}
      - PRIVATE_KEY=${BLOC<PERSON><PERSON>AIN_PRIVATE_KEY}
    ports:
      - "3000:3000"
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    depends_on:
      - mongodb
      - redis
    networks:
      - kryptopesa-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Admin Dashboard Service
  admin-dashboard:
    build:
      context: ./admin-dashboard
      dockerfile: Dockerfile.production
    container_name: kryptopesa-admin
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=${BACKEND_URL}
      - REACT_APP_WS_URL=${WEBSOCKET_URL}
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - kryptopesa-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: kryptopesa-mongodb
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
      - MONGO_INITDB_DATABASE=kryptopesa
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - kryptopesa-network
    command: mongod --auth --bind_ip_all

  # Redis Cache
  redis:
    image: redis:7.0-alpine
    container_name: kryptopesa-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - kryptopesa-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: kryptopesa-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
      - admin-dashboard
    networks:
      - kryptopesa-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: kryptopesa-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
    networks:
      - kryptopesa-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: kryptopesa-grafana
    restart: unless-stopped
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - kryptopesa-network

  # Log Aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: kryptopesa-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - kryptopesa-network

  # Log Processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: kryptopesa-logstash
    restart: unless-stopped
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./backend/logs:/var/log/app
    depends_on:
      - elasticsearch
    networks:
      - kryptopesa-network

  # Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: kryptopesa-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - kryptopesa-network

  # Backup Service
  backup:
    build:
      context: ./scripts/backup
      dockerfile: Dockerfile
    container_name: kryptopesa-backup
    restart: unless-stopped
    environment:
      - MONGODB_URI=${MONGODB_URI}
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}
      - S3_BUCKET=${BACKUP_S3_BUCKET}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
    volumes:
      - ./backups:/backups
    depends_on:
      - mongodb
    networks:
      - kryptopesa-network

  # SSL Certificate Management
  certbot:
    image: certbot/certbot
    container_name: kryptopesa-certbot
    volumes:
      - ./nginx/ssl:/etc/letsencrypt
      - ./nginx/webroot:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN_NAME}
    networks:
      - kryptopesa-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  kryptopesa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
