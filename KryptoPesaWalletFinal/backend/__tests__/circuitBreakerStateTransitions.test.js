/**
 * Circuit Breaker State Transition Tests
 * Comprehensive testing for all circuit breaker state transitions and edge cases
 */

// Using <PERSON><PERSON>'s built-in expect instead of Chai
const sinon = require('sinon');
const { CircuitBreaker, createCircuitBreaker } = require('../../src/utils/circuitBreaker');

describe('Circuit Breaker State Transitions', () => {
  let sandbox;
  let circuitBreaker;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    circuitBreaker = new CircuitBreaker({
      name: 'TestCircuitBreaker',
      failureThreshold: 3,
      timeout: 1000,
      monitoringPeriod: 100
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('CLOSED State Behavior', () => {
    test('should start in CLOSED state', () => {
      expect(circuitBreaker.getState().state).to.equal('CLOSED');
      expect(circuitBreaker.getState().failureCount).to.equal(0);
      expect(circuitBreaker.getState().successCount).to.equal(0);
    });

    test('should execute operations successfully in CLOSED state', async () => {
      const operation = sandbox.stub().resolves('success');
      
      const result = await circuitBreaker.execute(operation);
      
      expect(result).to.equal('success');
      expect(operation.calledOnce).to.be.true;
      expect(circuitBreaker.getState().state).to.equal('CLOSED');
    });

    test('should increment failure count on operation failure', async () => {
      const operation = sandbox.stub().rejects(new Error('Test failure'));
      
      try {
        await circuitBreaker.execute(operation);
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.equal('Test failure');
        expect(circuitBreaker.getState().failureCount).to.equal(1);
        expect(circuitBreaker.getState().state).to.equal('CLOSED');
      }
    });

    test('should transition to OPEN when failure threshold is reached', async () => {
      const operation = sandbox.stub().rejects(new Error('Test failure'));
      
      // Execute failures up to threshold
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(operation);
        } catch (error) {
          // Expected to fail
        }
      }
      
      expect(circuitBreaker.getState().state).to.equal('OPEN');
      expect(circuitBreaker.getState().failureCount).to.equal(3);
    });

    test('should reset failure count on successful operation', async () => {
      const failingOperation = sandbox.stub().rejects(new Error('Test failure'));
      const successOperation = sandbox.stub().resolves('success');
      
      // Add some failures
      try {
        await circuitBreaker.execute(failingOperation);
      } catch (error) {
        // Expected
      }
      
      expect(circuitBreaker.getState().failureCount).to.equal(1);
      
      // Execute successful operation
      await circuitBreaker.execute(successOperation);
      
      expect(circuitBreaker.getState().failureCount).to.equal(0);
      expect(circuitBreaker.getState().state).to.equal('CLOSED');
    });

    test('should ignore expected errors', async () => {
      const cbWithExpectedErrors = new CircuitBreaker({
        name: 'TestWithExpectedErrors',
        failureThreshold: 3,
        expectedErrors: ['ValidationError', /timeout/i]
      });

      const validationError = new Error('ValidationError: Invalid input');
      const timeoutError = new Error('Request timeout occurred');
      const unexpectedError = new Error('Unexpected error');

      // Expected errors should not increment failure count
      try {
        await cbWithExpectedErrors.execute(() => Promise.reject(validationError));
      } catch (error) {
        // Expected
      }
      
      try {
        await cbWithExpectedErrors.execute(() => Promise.reject(timeoutError));
      } catch (error) {
        // Expected
      }
      
      expect(cbWithExpectedErrors.getState().failureCount).to.equal(0);
      
      // Unexpected error should increment failure count
      try {
        await cbWithExpectedErrors.execute(() => Promise.reject(unexpectedError));
      } catch (error) {
        // Expected
      }
      
      expect(cbWithExpectedErrors.getState().failureCount).to.equal(1);
    });
  });

  describe('OPEN State Behavior', () => {
    beforeEach(() => {
      circuitBreaker.forceOpen();
    });

    test('should reject operations immediately in OPEN state', async () => {
      const operation = sandbox.stub().resolves('success');
      
      try {
        await circuitBreaker.execute(operation);
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('Circuit breaker TestCircuitBreaker is OPEN');
        expect(operation.called).to.be.false;
      }
    });

    test('should execute fallback function when provided', async () => {
      const operation = sandbox.stub().resolves('success');
      const fallback = sandbox.stub().resolves('fallback result');
      
      const result = await circuitBreaker.execute(operation, fallback);
      
      expect(result).to.equal('fallback result');
      expect(operation.called).to.be.false;
      expect(fallback.calledOnce).to.be.true;
    });

    test('should transition to HALF_OPEN after timeout', async () => {
      // Set next attempt to past time
      circuitBreaker.nextAttempt = Date.now() - 1000;
      
      const operation = sandbox.stub().resolves('success');
      
      const result = await circuitBreaker.execute(operation);
      
      expect(result).to.equal('success');
      expect(circuitBreaker.getState().state).to.equal('CLOSED'); // Should close immediately on success
    });

    test('should remain OPEN if timeout has not elapsed', async () => {
      // Set next attempt to future time
      circuitBreaker.nextAttempt = Date.now() + 10000;
      
      const operation = sandbox.stub().resolves('success');
      
      try {
        await circuitBreaker.execute(operation);
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('Circuit breaker TestCircuitBreaker is OPEN');
        expect(circuitBreaker.getState().state).to.equal('OPEN');
      }
    });
  });

  describe('HALF_OPEN State Behavior', () => {
    beforeEach(() => {
      circuitBreaker.state = 'HALF_OPEN';
      circuitBreaker.successCount = 0;
      circuitBreaker.failureCount = 0;
    });

    test('should execute operations in HALF_OPEN state', async () => {
      const operation = sandbox.stub().resolves('success');
      
      const result = await circuitBreaker.execute(operation);
      
      expect(result).to.equal('success');
      expect(operation.calledOnce).to.be.true;
      expect(circuitBreaker.getState().successCount).to.equal(1);
    });

    test('should transition to CLOSED after sufficient successes', async () => {
      const operation = sandbox.stub().resolves('success');
      const successesNeeded = Math.ceil(circuitBreaker.failureThreshold / 2);
      
      // Execute successful operations
      for (let i = 0; i < successesNeeded; i++) {
        await circuitBreaker.execute(operation);
      }
      
      expect(circuitBreaker.getState().state).to.equal('CLOSED');
      expect(circuitBreaker.getState().failureCount).to.equal(0);
      expect(circuitBreaker.getState().successCount).to.equal(0);
    });

    test('should transition to OPEN on any failure', async () => {
      const operation = sandbox.stub().rejects(new Error('Test failure'));
      
      try {
        await circuitBreaker.execute(operation);
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.equal('Test failure');
        expect(circuitBreaker.getState().state).to.equal('OPEN');
      }
    });

    test('should reset success count on failure', async () => {
      const successOperation = sandbox.stub().resolves('success');
      const failOperation = sandbox.stub().rejects(new Error('Test failure'));
      
      // Add some successes
      await circuitBreaker.execute(successOperation);
      expect(circuitBreaker.getState().successCount).to.equal(1);
      
      // Execute failure
      try {
        await circuitBreaker.execute(failOperation);
      } catch (error) {
        // Expected
      }
      
      expect(circuitBreaker.getState().successCount).to.equal(0);
      expect(circuitBreaker.getState().state).to.equal('OPEN');
    });
  });

  describe('Timeout Handling', () => {
    test('should timeout long-running operations', async () => {
      const longOperation = () => new Promise(resolve => setTimeout(resolve, 2000));
      
      const cbWithShortTimeout = new CircuitBreaker({
        name: 'ShortTimeout',
        timeout: 100
      });
      
      try {
        await cbWithShortTimeout.execute(longOperation);
        expect.fail('Should have timed out');
      } catch (error) {
        expect(error.message).to.include('Operation timed out');
        expect(cbWithShortTimeout.getState().metrics.totalTimeouts).to.equal(1);
      }
    });

    test('should not timeout fast operations', async () => {
      const fastOperation = () => Promise.resolve('fast result');
      
      const cbWithLongTimeout = new CircuitBreaker({
        name: 'LongTimeout',
        timeout: 5000
      });
      
      const result = await cbWithLongTimeout.execute(fastOperation);
      
      expect(result).to.equal('fast result');
      expect(cbWithLongTimeout.getState().metrics.totalTimeouts).to.equal(0);
    });
  });

  describe('Metrics Collection', () => {
    test('should track total requests', async () => {
      const operation = sandbox.stub().resolves('success');
      
      await circuitBreaker.execute(operation);
      await circuitBreaker.execute(operation);
      
      expect(circuitBreaker.getState().metrics.totalRequests).to.equal(2);
    });

    test('should track total successes and failures', async () => {
      const successOperation = sandbox.stub().resolves('success');
      const failOperation = sandbox.stub().rejects(new Error('failure'));
      
      await circuitBreaker.execute(successOperation);
      
      try {
        await circuitBreaker.execute(failOperation);
      } catch (error) {
        // Expected
      }
      
      expect(circuitBreaker.getState().metrics.totalSuccesses).to.equal(1);
      expect(circuitBreaker.getState().metrics.totalFailures).to.equal(1);
    });

    test('should calculate average response time', async () => {
      const fastOperation = () => new Promise(resolve => setTimeout(() => resolve('fast'), 10));
      const slowOperation = () => new Promise(resolve => setTimeout(() => resolve('slow'), 50));
      
      await circuitBreaker.execute(fastOperation);
      await circuitBreaker.execute(slowOperation);
      
      const avgResponseTime = circuitBreaker.getState().metrics.averageResponseTime;
      expect(avgResponseTime).to.be.greaterThan(0);
      expect(avgResponseTime).to.be.lessThan(100);
    });
  });

  describe('Manual Controls', () => {
    test('should force circuit breaker to OPEN state', () => {
      expect(circuitBreaker.getState().state).to.equal('CLOSED');
      
      circuitBreaker.forceOpen();
      
      expect(circuitBreaker.getState().state).to.equal('OPEN');
    });

    test('should force circuit breaker to CLOSED state', () => {
      circuitBreaker.forceOpen();
      expect(circuitBreaker.getState().state).to.equal('OPEN');
      
      circuitBreaker.forceClose();
      
      expect(circuitBreaker.getState().state).to.equal('CLOSED');
      expect(circuitBreaker.getState().failureCount).to.equal(0);
      expect(circuitBreaker.getState().successCount).to.equal(0);
    });
  });

  describe('Factory Function', () => {
    test('should create circuit breaker with default options', () => {
      const cb = createCircuitBreaker('TestFactory');
      
      expect(cb.name).to.equal('TestFactory');
      expect(cb.failureThreshold).to.equal(5);
      expect(cb.timeout).to.equal(60000);
    });

    test('should create circuit breaker with custom options', () => {
      const cb = createCircuitBreaker('TestCustom', {
        failureThreshold: 10,
        timeout: 30000
      });
      
      expect(cb.name).to.equal('TestCustom');
      expect(cb.failureThreshold).to.equal(10);
      expect(cb.timeout).to.equal(30000);
    });
  });

  describe('Auto-Reset Functionality', () => {
    test('should auto-reset after extended open period', (done) => {
      // Mock the monitoring period to be very short for testing
      const cbWithShortMonitoring = new CircuitBreaker({
        name: 'AutoResetTest',
        failureThreshold: 1,
        timeout: 100,
        monitoringPeriod: 50
      });

      cbWithShortMonitoring.forceOpen();
      
      // Set next attempt to past time to simulate extended open period
      cbWithShortMonitoring.nextAttempt = Date.now() - 300; // 300ms ago
      
      setTimeout(() => {
        expect(cbWithShortMonitoring.getState().state).to.equal('CLOSED');
        done();
      }, 200); // Wait for auto-reset
    });
  });
});
