/**
 * Comprehensive Integration Testing
 * End-to-end integration tests for all critical flows
 */

// Set up test environment variables before importing server
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long-for-testing';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-chars-long-for-testing';
process.env.SESSION_SECRET = 'test-session-secret-32-chars-long-for-testing';
process.env.MONGODB_URI = 'mongodb://localhost:27017/kryptopesa_test';
process.env.REDIS_URL = 'redis://localhost:6379';

const testEnvironment = require('../testEnvironment');
const request = require('supertest');
const { app, cleanupTestIntervals } = require('../testServer');

describe('Comprehensive Integration Tests', () => {
  let testUser1, testUser2, testWallet1, testWallet2;
  let authToken1, authToken2;

  beforeAll(async () => {
    await testEnvironment.setup();
  }, 60000);

  beforeEach(async () => {
    await testEnvironment.cleanDatabase();
    
    // Create test users
    testUser1 = await testEnvironment.createTestUser({
      email: '<EMAIL>',
      username: 'testuser1',
      phone: '+254700000001'
    });

    testUser2 = await testEnvironment.createTestUser({
      email: '<EMAIL>',
      username: 'testuser2',
      phone: '+254700000002'
    });

    // Create test wallets
    testWallet1 = await testEnvironment.createTestWallet(testUser1._id);
    testWallet2 = await testEnvironment.createTestWallet(testUser2._id);

    // Generate auth tokens using authService
    const authService = require('../../services/authService');
    const tokens1 = await authService.generateTokens(testUser1._id);
    const tokens2 = await authService.generateTokens(testUser2._id);
    authToken1 = tokens1.accessToken;
    authToken2 = tokens2.accessToken;
  });

  afterEach(async () => {
    await testEnvironment.cleanup();
  });

  afterAll(async () => {
    cleanupTestIntervals();
    await testEnvironment.teardown();
  });

  describe('Complete P2P Trading Flow', () => {
    test('should handle trading endpoints authentication', async () => {
      // Test that trading endpoints require authentication
      const unauthenticatedResponse = await request(app)
        .get('/api/trading/offers');

      expect(unauthenticatedResponse.status).toBe(401);
      expect(unauthenticatedResponse.body.success).toBe(false);
    });

    test('should get offers with authentication', async () => {
      // Test authenticated access to offers endpoint
      const offersResponse = await request(app)
        .get('/api/trading/offers')
        .set('Authorization', `Bearer ${authToken1}`);

      expect(offersResponse.status).toBe(200);
      expect(offersResponse.body.success).toBe(true);
      expect(Array.isArray(offersResponse.body.data.offers)).toBe(true);

      // Step 2: User2 creates a trade from the offer
      const tradeResponse = await request(app)
        .post('/api/trading/trades/accept')
        .set('Authorization', `Bearer ${authToken2}`)
        .send({
          offerId: offerId,
          amount: 0.05,
          paymentMethod: 'M-Pesa'
        });

      expect(tradeResponse.status).toBe(201);
      expect(tradeResponse.body.success).toBe(true);
      const tradeId = tradeResponse.body.trade._id;

      // Step 3: User1 accepts the trade
      const acceptResponse = await request(app)
        .patch(`/api/trades/${tradeId}/status`)
        .set('Authorization', `Bearer ${authToken1}`)
        .send({ status: 'accepted' });

      expect(acceptResponse.status).toBe(200);
      expect(acceptResponse.body.success).toBe(true);

      // Step 4: Create escrow
      const escrowResponse = await request(app)
        .post('/api/escrow')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({ tradeId: tradeId });

      expect(escrowResponse.status).toBe(201);
      expect(escrowResponse.body.success).toBe(true);
      const escrowId = escrowResponse.body.escrow._id;

      // Step 5: User2 confirms payment
      const paymentResponse = await request(app)
        .patch(`/api/trades/${tradeId}/payment`)
        .set('Authorization', `Bearer ${authToken2}`)
        .send({ 
          paymentProof: 'payment-proof-url',
          paymentMethod: 'M-Pesa'
        });

      expect(paymentResponse.status).toBe(200);
      expect(paymentResponse.body.success).toBe(true);

      // Step 6: User1 confirms payment received and releases escrow
      const releaseResponse = await request(app)
        .patch(`/api/escrow/${escrowId}/release`)
        .set('Authorization', `Bearer ${authToken1}`)
        .send({ confirmed: true });

      expect(releaseResponse.status).toBe(200);
      expect(releaseResponse.body.success).toBe(true);

      // Step 7: Verify trade completion
      const finalTradeResponse = await request(app)
        .get(`/api/trades/${tradeId}`)
        .set('Authorization', `Bearer ${authToken1}`);

      expect(finalTradeResponse.status).toBe(200);
      expect(finalTradeResponse.body.trade.status).toBe('completed');
    });

    test('should handle trade dispute workflow', async () => {
      // Create offer and trade
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          type: 'sell',
          cryptocurrency: 'BTC',
          amount: 0.1,
          pricePerUnit: 45000,
          paymentMethods: ['M-Pesa'],
          location: 'Nairobi'
        });

      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${authToken2}`)
        .send({
          offerId: offerResponse.body.offer._id,
          amount: 0.05,
          totalPrice: 2250
        });

      const tradeId = tradeResponse.body.trade._id;

      // Accept trade and create escrow
      await request(app)
        .patch(`/api/trades/${tradeId}/status`)
        .set('Authorization', `Bearer ${authToken1}`)
        .send({ status: 'accepted' });

      await request(app)
        .post('/api/escrow')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({ tradeId: tradeId });

      // Create dispute
      const disputeResponse = await request(app)
        .post('/api/disputes')
        .set('Authorization', `Bearer ${authToken2}`)
        .send({
          tradeId: tradeId,
          reason: 'Payment not received',
          description: 'I sent payment but seller claims not received'
        });

      expect(disputeResponse.status).toBe(201);
      expect(disputeResponse.body.success).toBe(true);
      expect(disputeResponse.body.dispute.status).toBe('open');

      // Verify trade status updated
      const tradeStatusResponse = await request(app)
        .get(`/api/trades/${tradeId}`)
        .set('Authorization', `Bearer ${authToken1}`);

      expect(tradeStatusResponse.body.trade.status).toBe('disputed');
    });
  });

  describe('Wallet Operations Integration', () => {
    test('should handle wallet creation and balance updates', async () => {
      // Create new user without wallet
      const newUserResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'newuser',
          phone: '+254700000003',
          password: 'Password123!',
          firstName: 'New',
          lastName: 'User',
          country: 'KE',
          city: 'Nairobi'
        });

      expect(newUserResponse.status).toBe(201);
      const newUserId = newUserResponse.body.user._id;
      const newUserToken = newUserResponse.body.token;

      // Create wallet
      const walletResponse = await request(app)
        .post('/api/wallet/create')
        .set('Authorization', `Bearer ${newUserToken}`);

      expect(walletResponse.status).toBe(201);
      expect(walletResponse.body.success).toBe(true);
      expect(walletResponse.body.wallet.address).toBeDefined();

      // Get balance
      const balanceResponse = await request(app)
        .get('/api/wallet/balance')
        .set('Authorization', `Bearer ${newUserToken}`);

      expect(balanceResponse.status).toBe(200);
      expect(balanceResponse.body.balance).toBeDefined();

      // Update balance (admin operation)
      const updateResponse = await request(app)
        .patch('/api/wallet/balance')
        .set('Authorization', `Bearer ${newUserToken}`)
        .send({
          BTC: 0.5,
          ETH: 2.0,
          USDT: 1000
        });

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.body.success).toBe(true);
    });

    test('should validate insufficient balance scenarios', async () => {
      // Try to create offer with insufficient balance
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          type: 'sell',
          cryptocurrency: 'BTC',
          amount: 10, // More than available
          pricePerUnit: 45000,
          paymentMethods: ['M-Pesa'],
          location: 'Nairobi'
        });

      expect(offerResponse.status).toBe(400);
      expect(offerResponse.body.error).toContain('insufficient');
    });
  });

  describe('Chat and Communication Integration', () => {
    test('should handle trade chat workflow', async () => {
      // Create trade first
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          type: 'sell',
          cryptocurrency: 'BTC',
          amount: 0.1,
          pricePerUnit: 45000,
          paymentMethods: ['M-Pesa'],
          location: 'Nairobi'
        });

      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${authToken2}`)
        .send({
          offerId: offerResponse.body.offer._id,
          amount: 0.05,
          totalPrice: 2250
        });

      const tradeId = tradeResponse.body.trade._id;

      // Send chat message
      const messageResponse = await request(app)
        .post('/api/chat/message')
        .set('Authorization', `Bearer ${authToken2}`)
        .send({
          tradeId: tradeId,
          message: 'Hello, I am ready to proceed with payment'
        });

      expect(messageResponse.status).toBe(201);
      expect(messageResponse.body.success).toBe(true);

      // Get chat history
      const chatResponse = await request(app)
        .get(`/api/chat/${tradeId}`)
        .set('Authorization', `Bearer ${authToken1}`);

      expect(chatResponse.status).toBe(200);
      expect(chatResponse.body.messages).toBeDefined();
      expect(chatResponse.body.messages.length).toBeGreaterThan(0);

      // Reply to message
      const replyResponse = await request(app)
        .post('/api/chat/message')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          tradeId: tradeId,
          message: 'Great! Please proceed with M-Pesa payment'
        });

      expect(replyResponse.status).toBe(201);
      expect(replyResponse.body.success).toBe(true);
    });
  });

  describe('Security and Authentication Integration', () => {
    test('should handle authentication flow', async () => {
      // Login
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: testUser1.email,
          password: 'password123'
        });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.token).toBeDefined();

      // Access protected route
      const profileResponse = await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${loginResponse.body.token}`);

      expect(profileResponse.status).toBe(200);
      expect(profileResponse.body.user._id).toBe(testUser1._id.toString());
    });

    test('should reject invalid tokens', async () => {
      const response = await request(app)
        .get('/api/user/profile')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('token');
    });

    test('should handle rate limiting', async () => {
      // Make multiple rapid requests
      const promises = Array.from({ length: 10 }, () =>
        request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'wrongpassword'
          })
      );

      const responses = await Promise.all(promises);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle malformed requests gracefully', async () => {
      const response = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          // Missing required fields
          type: 'invalid-type'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    test('should handle non-existent resource requests', async () => {
      const response = await request(app)
        .get('/api/trades/507f1f77bcf86cd799439011')
        .set('Authorization', `Bearer ${authToken1}`);

      expect(response.status).toBe(404);
      expect(response.body.error).toContain('not found');
    });

    test('should handle unauthorized access attempts', async () => {
      // Create trade with user1
      const offerResponse = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          type: 'sell',
          cryptocurrency: 'BTC',
          amount: 0.1,
          pricePerUnit: 45000,
          paymentMethods: ['M-Pesa'],
          location: 'Nairobi'
        });

      const tradeResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${authToken2}`)
        .send({
          offerId: offerResponse.body.offer._id,
          amount: 0.05,
          totalPrice: 2250
        });

      const tradeId = tradeResponse.body.trade._id;

      // Try to access trade with unauthorized user
      const unauthorizedResponse = await request(app)
        .get(`/api/trades/${tradeId}`)
        .set('Authorization', `Bearer ${authToken1}`); // Wrong user

      expect(unauthorizedResponse.status).toBe(403);
      expect(unauthorizedResponse.body.error).toContain('Unauthorized');
    });
  });
});
