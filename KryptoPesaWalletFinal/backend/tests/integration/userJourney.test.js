const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../src/server');
const User = require('../../src/models/User');
const Offer = require('../../src/models/Offer');
const Trade = require('../../src/models/Trade');

describe('Complete User Journey Integration Tests', () => {
  let server;
  let sellerToken, buyerToken;
  let sellerId, buyerId;
  let offerId, tradeId;

  beforeAll(async () => {
    // Connect to test database
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/kryptopesa_test';
    await mongoose.connect(mongoUri);
    
    // Start server
    server = app.listen(0);
  });

  afterAll(async () => {
    // Clean up
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    if (server) {
      server.close();
    }
  });

  beforeEach(async () => {
    // Clean collections before each test
    await User.deleteMany({});
    await Offer.deleteMany({});
    await Trade.deleteMany({});
  });

  describe('1. User Registration and Authentication', () => {
    test('Should register two users successfully', async () => {
      // Register seller
      const sellerData = {
        username: 'seller123',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Seller',
        phone: '+************',
        country: 'KE',
        city: 'Nairobi'
      };

      const sellerResponse = await request(app)
        .post('/api/auth/register')
        .send(sellerData)
        .expect(201);

      expect(sellerResponse.body.success).toBe(true);
      expect(sellerResponse.body.data.user.email).toBe(sellerData.email);
      sellerId = sellerResponse.body.data.user._id;

      // Register buyer
      const buyerData = {
        username: 'buyer123',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Buyer',
        phone: '+************',
        country: 'KE',
        city: 'Mombasa'
      };

      const buyerResponse = await request(app)
        .post('/api/auth/register')
        .send(buyerData)
        .expect(201);

      expect(buyerResponse.body.success).toBe(true);
      expect(buyerResponse.body.data.user.email).toBe(buyerData.email);
      buyerId = buyerResponse.body.data.user._id;
    });

    test('Should login both users successfully', async () => {
      // First register users
      await request(app)
        .post('/api/auth/register')
        .send({
          username: 'seller123',
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'John',
          lastName: 'Seller',
          phone: '+************',
          country: 'KE',
          city: 'Nairobi'
        });

      await request(app)
        .post('/api/auth/register')
        .send({
          username: 'buyer123',
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Jane',
          lastName: 'Buyer',
          phone: '+************',
          country: 'KE',
          city: 'Mombasa'
        });

      // Login seller
      const sellerLogin = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'password123'
        })
        .expect(200);

      expect(sellerLogin.body.success).toBe(true);
      expect(sellerLogin.body.data.token).toBeDefined();
      sellerToken = sellerLogin.body.data.token;

      // Login buyer
      const buyerLogin = await request(app)
        .post('/api/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'password123'
        })
        .expect(200);

      expect(buyerLogin.body.success).toBe(true);
      expect(buyerLogin.body.data.token).toBeDefined();
      buyerToken = buyerLogin.body.data.token;
    });
  });

  describe('2. Offer Creation and Management', () => {
    beforeEach(async () => {
      // Setup users and login
      await setupUsersAndLogin();
    });

    test('Should create a sell offer successfully', async () => {
      const offerData = {
        type: 'sell',
        cryptocurrency: {
          symbol: 'USDT',
          network: 'TRC20',
          minAmount: '10',
          maxAmount: '1000',
          availableAmount: '1000'
        },
        fiat: {
          currency: 'KES',
          priceType: 'fixed',
          fixedPrice: '150',
          marginPercentage: 0
        },
        paymentMethods: [
          {
            method: 'mobile_money',
            details: {
              provider: 'M-Pesa',
              mobileNumber: '+************',
              accountName: 'John Seller'
            }
          }
        ],
        terms: {
          instructions: 'Please send payment to the provided M-Pesa number',
          autoReply: 'Thank you for your trade request',
          timeLimit: 30
        },
        location: {
          country: 'KE',
          city: 'Nairobi'
        }
      };

      const response = await request(app)
        .post('/api/offers')
        .set('Authorization', `Bearer ${sellerToken}`)
        .send(offerData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.offer.type).toBe('sell');
      expect(response.body.data.offer.status).toBe('active');
      offerId = response.body.data.offer.offerId;
    });

    test('Should fetch offers with filters', async () => {
      // Create offer first
      await createTestOffer();

      const response = await request(app)
        .get('/api/offers')
        .query({
          type: 'sell',
          cryptocurrency: 'USDT',
          fiatCurrency: 'KES',
          country: 'KE'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.offers).toHaveLength(1);
      expect(response.body.data.offers[0].type).toBe('sell');
    });
  });

  describe('3. Trade Creation and Execution', () => {
    beforeEach(async () => {
      await setupUsersAndLogin();
      await createTestOffer();
    });

    test('Should create trade from offer successfully', async () => {
      const tradeData = {
        offerId,
        amount: '100',
        paymentMethod: {
          method: 'mobile_money',
          details: {
            provider: 'M-Pesa',
            mobileNumber: '+************',
            accountName: 'Jane Buyer'
          }
        }
      };

      const response = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send(tradeData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trade.status).toBe('created');
      expect(response.body.data.trade.cryptocurrency.amount).toBe('100');
      tradeId = response.body.data.trade.tradeId;
    });

    test('Should complete full trade workflow', async () => {
      // 1. Create trade
      const tradeData = {
        offerId,
        amount: '100',
        paymentMethod: {
          method: 'mobile_money',
          details: {
            provider: 'M-Pesa',
            mobileNumber: '+************',
            accountName: 'Jane Buyer'
          }
        }
      };

      const createResponse = await request(app)
        .post('/api/trades')
        .set('Authorization', `Bearer ${buyerToken}`)
        .send(tradeData)
        .expect(201);

      tradeId = createResponse.body.data.trade.tradeId;

      // 2. Seller funds the trade
      await request(app)
        .post(`/api/trades/${tradeId}/fund`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .send({
          transactionHash: '0x123456789abcdef'
        })
        .expect(200);

      // 3. Buyer marks payment as sent
      await request(app)
        .post(`/api/trades/${tradeId}/payment-sent`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .send({
          paymentReference: 'MPESA123456',
          notes: 'Payment sent via M-Pesa'
        })
        .expect(200);

      // 4. Seller confirms payment and completes trade
      const completeResponse = await request(app)
        .post(`/api/trades/${tradeId}/complete`)
        .set('Authorization', `Bearer ${sellerToken}`)
        .expect(200);

      expect(completeResponse.body.success).toBe(true);
      expect(completeResponse.body.data.trade.status).toBe('completed');
    });
  });

  describe('4. Dispute Resolution', () => {
    beforeEach(async () => {
      await setupUsersAndLogin();
      await createTestOffer();
      await createTestTrade();
    });

    test('Should create and resolve dispute', async () => {
      // Create dispute
      const disputeData = {
        reason: 'payment_not_received',
        description: 'I sent the payment but seller claims not received',
        evidence: ['Payment screenshot attached']
      };

      const disputeResponse = await request(app)
        .post(`/api/trades/${tradeId}/dispute`)
        .set('Authorization', `Bearer ${buyerToken}`)
        .send(disputeData)
        .expect(201);

      expect(disputeResponse.body.success).toBe(true);
      expect(disputeResponse.body.data.dispute.status).toBe('pending');

      const disputeId = disputeResponse.body.data.dispute.disputeId;

      // Admin resolves dispute (would require admin token in real scenario)
      // For test purposes, we'll simulate admin resolution
      const resolveResponse = await request(app)
        .post(`/api/admin/disputes/${disputeId}/resolve`)
        .set('Authorization', `Bearer ${sellerToken}`) // Using seller token for test
        .send({
          resolution: 'favor_buyer',
          reason: 'Evidence shows payment was made',
          action: 'release_funds'
        })
        .expect(200);

      expect(resolveResponse.body.success).toBe(true);
    });
  });

  // Helper functions
  async function setupUsersAndLogin() {
    // Register and login seller
    await request(app)
      .post('/api/auth/register')
      .send({
        username: 'seller123',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Seller',
        phone: '+************',
        country: 'KE',
        city: 'Nairobi'
      });

    const sellerLogin = await request(app)
      .post('/api/auth/login')
      .send({
        identifier: '<EMAIL>',
        password: 'password123'
      });

    sellerToken = sellerLogin.body.data.token;

    // Register and login buyer
    await request(app)
      .post('/api/auth/register')
      .send({
        username: 'buyer123',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Buyer',
        phone: '+************',
        country: 'KE',
        city: 'Mombasa'
      });

    const buyerLogin = await request(app)
      .post('/api/auth/login')
      .send({
        identifier: '<EMAIL>',
        password: 'password123'
      });

    buyerToken = buyerLogin.body.data.token;
  }

  async function createTestOffer() {
    const offerData = {
      type: 'sell',
      cryptocurrency: {
        symbol: 'USDT',
        network: 'TRC20',
        minAmount: '10',
        maxAmount: '1000',
        availableAmount: '1000'
      },
      fiat: {
        currency: 'KES',
        priceType: 'fixed',
        fixedPrice: '150'
      },
      paymentMethods: [
        {
          method: 'mobile_money',
          details: {
            provider: 'M-Pesa',
            mobileNumber: '+************',
            accountName: 'John Seller'
          }
        }
      ],
      terms: {
        instructions: 'Please send payment to the provided M-Pesa number',
        timeLimit: 30
      },
      location: {
        country: 'KE',
        city: 'Nairobi'
      }
    };

    const response = await request(app)
      .post('/api/offers')
      .set('Authorization', `Bearer ${sellerToken}`)
      .send(offerData);

    offerId = response.body.data.offer.offerId;
  }

  async function createTestTrade() {
    const tradeData = {
      offerId,
      amount: '100',
      paymentMethod: {
        method: 'mobile_money',
        details: {
          provider: 'M-Pesa',
          mobileNumber: '+************',
          accountName: 'Jane Buyer'
        }
      }
    };

    const response = await request(app)
      .post('/api/trades')
      .set('Authorization', `Bearer ${buyerToken}`)
      .send(tradeData);

    tradeId = response.body.data.trade.tradeId;
  }
});
