/**
 * Response Caching Middleware
 * Implements intelligent caching for API responses
 */

const { setCache, getCache, deleteCache } = require('../config/redis');
const logger = require('../utils/logger');

class ResponseCacheManager {
  constructor() {
    this.cacheConfig = {
      // Cache durations in seconds
      durations: {
        offers: 120,        // 2 minutes - offers change frequently
        trades: 300,        // 5 minutes - trades change less frequently
        users: 600,         // 10 minutes - user data changes infrequently
        dashboard: 60,      // 1 minute - dashboard needs fresh data
        statistics: 1800,   // 30 minutes - statistics can be cached longer
        public: 3600        // 1 hour - public data rarely changes
      },
      
      // Cache invalidation patterns
      invalidationPatterns: {
        'trades': ['trades:*', 'dashboard:*', 'statistics:*'],
        'offers': ['offers:*', 'dashboard:*', 'statistics:*'],
        'users': ['users:*', 'dashboard:*']
      }
    };

    this.metrics = {
      hits: 0,
      misses: 0,
      invalidations: 0,
      errors: 0
    };
  }

  /**
   * Create cache middleware for specific route types
   */
  createCacheMiddleware(cacheType, customDuration = null) {
    return async (req, res, next) => {
      // Skip caching for non-GET requests
      if (req.method !== 'GET') {
        return next();
      }

      // Skip caching if user requests fresh data
      if (req.query.fresh === 'true' || req.headers['cache-control'] === 'no-cache') {
        return next();
      }

      const cacheKey = this.generateCacheKey(req, cacheType);
      const duration = customDuration || this.cacheConfig.durations[cacheType] || 300;

      try {
        // Try to get cached response
        const cachedResponse = await getCache(cacheKey);
        
        if (cachedResponse) {
          this.metrics.hits++;
          
          // Add cache headers
          res.set({
            'X-Cache': 'HIT',
            'X-Cache-Key': cacheKey,
            'Cache-Control': `public, max-age=${duration}`
          });

          return res.json(cachedResponse);
        }

        // Cache miss - continue to route handler
        this.metrics.misses++;
        
        // Override res.json to cache the response
        const originalJson = res.json;
        res.json = (data) => {
          // Only cache successful responses
          if (res.statusCode >= 200 && res.statusCode < 300) {
            this.cacheResponse(cacheKey, data, duration);
          }

          // Add cache headers
          res.set({
            'X-Cache': 'MISS',
            'X-Cache-Key': cacheKey,
            'Cache-Control': `public, max-age=${duration}`
          });

          return originalJson.call(res, data);
        };

        next();

      } catch (error) {
        this.metrics.errors++;
        logger.error('Cache middleware error:', error);
        
        // Continue without caching on error
        next();
      }
    };
  }

  /**
   * Generate cache key based on request
   */
  generateCacheKey(req, cacheType) {
    const baseKey = `${cacheType}:${req.path}`;
    
    // Include relevant query parameters
    const relevantParams = this.getRelevantParams(req.query, cacheType);
    const paramString = Object.keys(relevantParams).length > 0 
      ? `:${JSON.stringify(relevantParams)}`
      : '';

    // Include user ID for user-specific data
    const userString = req.user ? `:user:${req.user._id}` : '';

    return `${baseKey}${paramString}${userString}`;
  }

  /**
   * Get relevant query parameters for caching
   */
  getRelevantParams(query, cacheType) {
    const relevantParamsByType = {
      offers: ['type', 'cryptocurrency', 'fiatCurrency', 'country', 'paymentMethod', 'minAmount', 'maxAmount', 'sortBy', 'sortOrder', 'limit', 'offset'],
      trades: ['status', 'cryptocurrency', 'fiatCurrency', 'limit', 'offset', 'sortBy', 'sortOrder'],
      users: ['limit', 'offset', 'sortBy', 'sortOrder'],
      dashboard: [], // No query params for dashboard
      statistics: ['period', 'type'],
      public: ['limit', 'offset']
    };

    const relevantParams = relevantParamsByType[cacheType] || [];
    const filtered = {};

    relevantParams.forEach(param => {
      if (query[param] !== undefined) {
        filtered[param] = query[param];
      }
    });

    return filtered;
  }

  /**
   * Cache response data
   */
  async cacheResponse(cacheKey, data, duration) {
    try {
      await setCache(cacheKey, data, duration);
      logger.debug(`Response cached: ${cacheKey} (${duration}s)`);
    } catch (error) {
      logger.error('Failed to cache response:', error);
    }
  }

  /**
   * Invalidate cache based on data changes
   */
  async invalidateCache(dataType, specificKey = null) {
    try {
      this.metrics.invalidations++;

      if (specificKey) {
        await deleteCache(specificKey);
        logger.debug(`Cache invalidated: ${specificKey}`);
        return;
      }

      // Invalidate based on patterns
      const patterns = this.cacheConfig.invalidationPatterns[dataType] || [];
      
      for (const pattern of patterns) {
        // In a real Redis implementation, you'd use SCAN with pattern matching
        // For now, we'll log the invalidation
        logger.debug(`Cache invalidation pattern: ${pattern}`);
        
        // This is a simplified approach - in production, implement proper pattern matching
        if (pattern.includes('*')) {
          await this.invalidateByPattern(pattern);
        } else {
          await deleteCache(pattern);
        }
      }

    } catch (error) {
      logger.error('Cache invalidation error:', error);
    }
  }

  /**
   * Invalidate cache by pattern (simplified implementation)
   */
  async invalidateByPattern(pattern) {
    // In production, implement Redis SCAN with pattern matching
    logger.info(`Pattern-based cache invalidation: ${pattern}`);
  }

  /**
   * Create cache invalidation middleware for write operations
   */
  createInvalidationMiddleware(dataType) {
    return async (req, res, next) => {
      // Store original res.json
      const originalJson = res.json;
      
      res.json = async (data) => {
        // Only invalidate on successful write operations
        if (res.statusCode >= 200 && res.statusCode < 300) {
          await this.invalidateCache(dataType);
        }
        
        return originalJson.call(res, data);
      };

      next();
    };
  }

  /**
   * Conditional caching based on request characteristics
   */
  createConditionalCache(conditions) {
    return (req, res, next) => {
      // Check conditions
      for (const condition of conditions) {
        if (!condition.check(req)) {
          return next();
        }
      }

      // Apply caching
      return this.createCacheMiddleware(conditions[0].cacheType)(req, res, next);
    };
  }

  /**
   * Cache warming for frequently accessed data
   */
  async warmCache() {
    logger.info('Starting cache warming...');
    
    try {
      // Warm popular offers
      const popularOffers = await this.getPopularOffers();
      await setCache('offers:popular', popularOffers, this.cacheConfig.durations.offers);

      // Warm market statistics
      const marketStats = await this.getMarketStatistics();
      await setCache('statistics:market', marketStats, this.cacheConfig.durations.statistics);

      logger.info('Cache warming completed');
    } catch (error) {
      logger.error('Cache warming failed:', error);
    }
  }

  /**
   * Get cache metrics
   */
  getMetrics() {
    const total = this.metrics.hits + this.metrics.misses;
    const hitRate = total > 0 ? ((this.metrics.hits / total) * 100).toFixed(2) : 0;

    return {
      ...this.metrics,
      hitRate: `${hitRate}%`,
      total
    };
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      hits: 0,
      misses: 0,
      invalidations: 0,
      errors: 0
    };
  }

  // Placeholder methods for cache warming
  async getPopularOffers() {
    // Implement logic to get popular offers
    return [];
  }

  async getMarketStatistics() {
    // Implement logic to get market statistics
    return {};
  }
}

// Create singleton instance
const responseCacheManager = new ResponseCacheManager();

// Convenience functions for common cache types
const cacheOffers = (duration) => responseCacheManager.createCacheMiddleware('offers', duration);
const cacheTrades = (duration) => responseCacheManager.createCacheMiddleware('trades', duration);
const cacheUsers = (duration) => responseCacheManager.createCacheMiddleware('users', duration);
const cacheDashboard = (duration) => responseCacheManager.createCacheMiddleware('dashboard', duration);
const cacheStatistics = (duration) => responseCacheManager.createCacheMiddleware('statistics', duration);
const cachePublic = (duration) => responseCacheManager.createCacheMiddleware('public', duration);

// Invalidation middleware
const invalidateOffers = () => responseCacheManager.createInvalidationMiddleware('offers');
const invalidateTrades = () => responseCacheManager.createInvalidationMiddleware('trades');
const invalidateUsers = () => responseCacheManager.createInvalidationMiddleware('users');

module.exports = {
  ResponseCacheManager,
  responseCacheManager,
  cacheOffers,
  cacheTrades,
  cacheUsers,
  cacheDashboard,
  cacheStatistics,
  cachePublic,
  invalidateOffers,
  invalidateTrades,
  invalidateUsers
};
