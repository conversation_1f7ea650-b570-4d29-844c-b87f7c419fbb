/**
 * Query Optimization Service
 * Provides optimized database queries using aggregation pipelines
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');
const { setCache, getCache } = require('../config/redis');

class QueryOptimizationService {
  constructor() {
    this.queryCache = new Map();
    this.performanceMetrics = {
      totalQueries: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageQueryTime: 0,
      slowQueries: []
    };
  }

  /**
   * Get optimized trades with aggregation pipeline
   */
  async getTradesOptimized(filters = {}, options = {}) {
    const cacheKey = `trades:${JSON.stringify(filters)}:${JSON.stringify(options)}`;
    const startTime = Date.now();

    try {
      // Try cache first
      const cached = await getCache(cacheKey);
      if (cached) {
        this.performanceMetrics.cacheHits++;
        return cached;
      }

      const {
        userId,
        status,
        cryptocurrency,
        fiatCurrency,
        limit = 20,
        offset = 0,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filters;

      // Build match stage
      const matchStage = {};
      
      if (userId) {
        matchStage.$or = [
          { seller: new mongoose.Types.ObjectId(userId) },
          { buyer: new mongoose.Types.ObjectId(userId) }
        ];
      }

      if (status) {
        if (Array.isArray(status)) {
          matchStage.status = { $in: status };
        } else {
          matchStage.status = status;
        }
      }

      if (cryptocurrency) {
        matchStage['cryptocurrency.symbol'] = cryptocurrency;
      }

      if (fiatCurrency) {
        matchStage['fiat.currency'] = fiatCurrency;
      }

      // Build sort stage
      const sortStage = {};
      sortStage[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Optimized aggregation pipeline
      const pipeline = [
        { $match: matchStage },
        {
          $lookup: {
            from: 'users',
            localField: 'seller',
            foreignField: '_id',
            as: 'sellerInfo',
            pipeline: [
              {
                $project: {
                  username: 1,
                  'profile.firstName': 1,
                  'profile.lastName': 1,
                  'reputation.score': 1,
                  'reputation.totalTrades': 1,
                  'verification.level': 1
                }
              }
            ]
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'buyer',
            foreignField: '_id',
            as: 'buyerInfo',
            pipeline: [
              {
                $project: {
                  username: 1,
                  'profile.firstName': 1,
                  'profile.lastName': 1,
                  'reputation.score': 1,
                  'reputation.totalTrades': 1,
                  'verification.level': 1
                }
              }
            ]
          }
        },
        {
          $lookup: {
            from: 'offers',
            localField: 'offer',
            foreignField: '_id',
            as: 'offerInfo',
            pipeline: [
              {
                $project: {
                  offerId: 1,
                  type: 1,
                  'cryptocurrency.symbol': 1,
                  'fiat.currency': 1
                }
              }
            ]
          }
        },
        {
          $lookup: {
            from: 'chats',
            localField: 'chat',
            foreignField: '_id',
            as: 'chatInfo',
            pipeline: [
              {
                $project: {
                  lastMessage: 1,
                  unreadCount: 1,
                  lastActivity: 1
                }
              }
            ]
          }
        },
        { $unwind: { path: '$sellerInfo', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$buyerInfo', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$offerInfo', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$chatInfo', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            tradeId: 1,
            status: 1,
            cryptocurrency: 1,
            fiat: 1,
            payment: 1,
            commission: 1,
            timeline: { $slice: ['$timeline', -5] }, // Only last 5 timeline entries
            createdAt: 1,
            updatedAt: 1,
            expiresAt: 1,
            completedAt: 1,
            seller: '$sellerInfo',
            buyer: '$buyerInfo',
            offer: '$offerInfo',
            chat: '$chatInfo'
          }
        },
        { $sort: sortStage },
        { $skip: offset },
        { $limit: limit }
      ];

      // Execute aggregation
      const [trades, totalCount] = await Promise.all([
        mongoose.model('Trade').aggregate(pipeline),
        this.getTradesCount(matchStage)
      ]);

      const result = {
        trades,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount
        }
      };

      // Cache result for 5 minutes
      await setCache(cacheKey, result, 300);
      this.performanceMetrics.cacheMisses++;

      const queryTime = Date.now() - startTime;
      this.updatePerformanceMetrics(queryTime);

      return result;

    } catch (error) {
      logger.error('Error in optimized trades query:', error);
      throw error;
    }
  }

  /**
   * Get optimized offers with aggregation pipeline
   */
  async getOffersOptimized(filters = {}, options = {}) {
    const cacheKey = `offers:${JSON.stringify(filters)}:${JSON.stringify(options)}`;
    const startTime = Date.now();

    try {
      // Try cache first
      const cached = await getCache(cacheKey);
      if (cached) {
        this.performanceMetrics.cacheHits++;
        return cached;
      }

      const {
        type,
        cryptocurrency,
        fiatCurrency,
        country,
        paymentMethod,
        minAmount,
        maxAmount,
        limit = 20,
        offset = 0,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filters;

      // Build match stage
      const matchStage = {
        status: 'active',
        expiresAt: { $gt: new Date() },
        'cryptocurrency.availableAmount': { $gt: 0 }
      };

      if (type) matchStage.type = type;
      if (cryptocurrency) matchStage['cryptocurrency.symbol'] = cryptocurrency;
      if (fiatCurrency) matchStage['fiat.currency'] = fiatCurrency;
      if (country) matchStage['creator.profile.location.country'] = country;
      if (paymentMethod) {
        matchStage['paymentMethods.method'] = paymentMethod;
      }
      if (minAmount) {
        matchStage['terms.minAmount'] = { $lte: parseFloat(minAmount) };
      }
      if (maxAmount) {
        matchStage['terms.maxAmount'] = { $gte: parseFloat(maxAmount) };
      }

      // Build sort stage
      const sortStage = {};
      switch (sortBy) {
        case 'price':
          sortStage['fiat.effectivePrice'] = sortOrder === 'asc' ? 1 : -1;
          break;
        case 'reputation':
          sortStage['creator.reputation.score'] = sortOrder === 'asc' ? 1 : -1;
          break;
        case 'volume':
          sortStage['statistics.totalVolume'] = sortOrder === 'asc' ? 1 : -1;
          break;
        default:
          sortStage.createdAt = sortOrder === 'asc' ? 1 : -1;
      }

      // Optimized aggregation pipeline
      const pipeline = [
        {
          $lookup: {
            from: 'users',
            localField: 'creator',
            foreignField: '_id',
            as: 'creator',
            pipeline: [
              {
                $project: {
                  username: 1,
                  'profile.firstName': 1,
                  'profile.lastName': 1,
                  'profile.location': 1,
                  'reputation.score': 1,
                  'reputation.totalTrades': 1,
                  'verification.level': 1,
                  'verification.email.verified': 1,
                  'verification.phone.verified': 1
                }
              }
            ]
          }
        },
        { $unwind: '$creator' },
        { $match: matchStage },
        {
          $addFields: {
            // Calculate effective price for sorting
            'fiat.effectivePrice': {
              $cond: {
                if: { $eq: ['$fiat.priceType', 'fixed'] },
                then: '$fiat.fixedPrice',
                else: {
                  $multiply: [
                    '$fiat.marketPrice',
                    { $add: [1, { $divide: ['$fiat.marginPercentage', 100] }] }
                  ]
                }
              }
            }
          }
        },
        {
          $project: {
            offerId: 1,
            type: 1,
            cryptocurrency: 1,
            fiat: 1,
            paymentMethods: 1,
            terms: 1,
            statistics: 1,
            createdAt: 1,
            expiresAt: 1,
            creator: 1
          }
        },
        { $sort: sortStage },
        { $skip: offset },
        { $limit: limit }
      ];

      // Execute aggregation
      const [offers, totalCount] = await Promise.all([
        mongoose.model('Offer').aggregate(pipeline),
        this.getOffersCount(matchStage)
      ]);

      const result = {
        offers,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount
        }
      };

      // Cache result for 2 minutes (offers change more frequently)
      await setCache(cacheKey, result, 120);
      this.performanceMetrics.cacheMisses++;

      const queryTime = Date.now() - startTime;
      this.updatePerformanceMetrics(queryTime);

      return result;

    } catch (error) {
      logger.error('Error in optimized offers query:', error);
      throw error;
    }
  }

  /**
   * Get user dashboard data with single aggregation
   */
  async getUserDashboardData(userId) {
    const cacheKey = `dashboard:${userId}`;
    const startTime = Date.now();

    try {
      // Try cache first
      const cached = await getCache(cacheKey);
      if (cached) {
        this.performanceMetrics.cacheHits++;
        return cached;
      }

      const userObjectId = new mongoose.Types.ObjectId(userId);

      // Single aggregation to get all dashboard data
      const pipeline = [
        {
          $facet: {
            activeTrades: [
              {
                $match: {
                  $or: [{ seller: userObjectId }, { buyer: userObjectId }],
                  status: { $in: ['created', 'funded', 'payment_sent'] }
                }
              },
              { $count: 'count' }
            ],
            completedTrades: [
              {
                $match: {
                  $or: [{ seller: userObjectId }, { buyer: userObjectId }],
                  status: 'completed'
                }
              },
              { $count: 'count' }
            ],
            totalVolume: [
              {
                $match: {
                  $or: [{ seller: userObjectId }, { buyer: userObjectId }],
                  status: 'completed'
                }
              },
              {
                $group: {
                  _id: null,
                  totalVolume: { $sum: '$fiat.amount' }
                }
              }
            ],
            recentTrades: [
              {
                $match: {
                  $or: [{ seller: userObjectId }, { buyer: userObjectId }]
                }
              },
              { $sort: { createdAt: -1 } },
              { $limit: 5 },
              {
                $project: {
                  tradeId: 1,
                  status: 1,
                  'cryptocurrency.symbol': 1,
                  'cryptocurrency.amount': 1,
                  'fiat.amount': 1,
                  'fiat.currency': 1,
                  createdAt: 1
                }
              }
            ]
          }
        }
      ];

      const [result] = await mongoose.model('Trade').aggregate(pipeline);

      const dashboardData = {
        activeTrades: result.activeTrades[0]?.count || 0,
        completedTrades: result.completedTrades[0]?.count || 0,
        totalVolume: result.totalVolume[0]?.totalVolume || 0,
        recentTrades: result.recentTrades || []
      };

      // Cache for 1 minute
      await setCache(cacheKey, dashboardData, 60);
      this.performanceMetrics.cacheMisses++;

      const queryTime = Date.now() - startTime;
      this.updatePerformanceMetrics(queryTime);

      return dashboardData;

    } catch (error) {
      logger.error('Error in dashboard data query:', error);
      throw error;
    }
  }

  /**
   * Helper method to get trades count
   */
  async getTradesCount(matchStage) {
    const countPipeline = [
      { $match: matchStage },
      { $count: 'total' }
    ];
    
    const result = await mongoose.model('Trade').aggregate(countPipeline);
    return result[0]?.total || 0;
  }

  /**
   * Helper method to get offers count
   */
  async getOffersCount(matchStage) {
    const countPipeline = [
      {
        $lookup: {
          from: 'users',
          localField: 'creator',
          foreignField: '_id',
          as: 'creator'
        }
      },
      { $unwind: '$creator' },
      { $match: matchStage },
      { $count: 'total' }
    ];
    
    const result = await mongoose.model('Offer').aggregate(countPipeline);
    return result[0]?.total || 0;
  }

  /**
   * Update performance metrics
   */
  updatePerformanceMetrics(queryTime) {
    this.performanceMetrics.totalQueries++;
    
    // Update average query time
    const totalTime = this.performanceMetrics.averageQueryTime * (this.performanceMetrics.totalQueries - 1) + queryTime;
    this.performanceMetrics.averageQueryTime = totalTime / this.performanceMetrics.totalQueries;

    // Track slow queries (>500ms)
    if (queryTime > 500) {
      this.performanceMetrics.slowQueries.push({
        queryTime,
        timestamp: new Date()
      });

      // Keep only last 100 slow queries
      if (this.performanceMetrics.slowQueries.length > 100) {
        this.performanceMetrics.slowQueries.shift();
      }

      logger.warn(`Slow query detected: ${queryTime}ms`);
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    const cacheHitRate = this.performanceMetrics.totalQueries > 0 
      ? (this.performanceMetrics.cacheHits / this.performanceMetrics.totalQueries * 100).toFixed(2)
      : 0;

    return {
      ...this.performanceMetrics,
      cacheHitRate: `${cacheHitRate}%`
    };
  }

  /**
   * Clear cache for specific patterns
   */
  async invalidateCache(pattern) {
    // This would need Redis SCAN in production
    logger.info(`Cache invalidation requested for pattern: ${pattern}`);
  }
}

// Create singleton instance
const queryOptimization = new QueryOptimizationService();

module.exports = {
  QueryOptimizationService,
  queryOptimization
};
