{"timestamp": "2025-07-06T00:09:02.446Z", "projects": [{"name": "Backend", "path": "backend", "type": "node", "vulnerabilities": [], "outdatedPackages": [], "unusedDependencies": [], "securityIssues": [{"type": "missing_security_config", "message": "No audit script defined", "recommendation": "Add \"audit\": \"npm audit\" to scripts"}, {"type": "missing_security_config", "message": "No audit-fix script defined", "recommendation": "Add \"audit-fix\": \"npm audit fix\" to scripts"}, {"type": "missing_lock_file", "message": "Missing yarn.lock", "recommendation": "Commit yarn.lock to ensure consistent dependency versions"}], "recommendations": []}, {"name": "Admin Dashboard", "path": "admin-dashboard", "type": "react", "vulnerabilities": [], "outdatedPackages": [], "unusedDependencies": [], "securityIssues": [{"type": "missing_security_config", "message": "No audit script defined", "recommendation": "Add \"audit\": \"npm audit\" to scripts"}, {"type": "missing_security_config", "message": "No audit-fix script defined", "recommendation": "Add \"audit-fix\": \"npm audit fix\" to scripts"}, {"type": "problematic_package", "package": "lodash", "message": "Using problematic package: lodash", "recommendation": "Use lodash-es for better tree shaking"}, {"type": "missing_lock_file", "message": "Missing yarn.lock", "recommendation": "Commit yarn.lock to ensure consistent dependency versions"}], "recommendations": []}, {"name": "Mobile App", "path": "kryptopesa_mobile", "type": "flutter", "vulnerabilities": [], "outdatedPackages": [], "unusedDependencies": [], "securityIssues": [], "recommendations": ["No package.json found - skipping audit"]}, {"name": "Smart Contracts", "path": "smart-contracts", "type": "node", "vulnerabilities": [], "outdatedPackages": [], "unusedDependencies": [], "securityIssues": [{"type": "missing_security_config", "message": "No audit script defined", "recommendation": "Add \"audit\": \"npm audit\" to scripts"}, {"type": "missing_security_config", "message": "No audit-fix script defined", "recommendation": "Add \"audit-fix\": \"npm audit fix\" to scripts"}, {"type": "missing_lock_file", "message": "Missing package-lock.json", "recommendation": "Commit package-lock.json to ensure consistent dependency versions"}, {"type": "missing_lock_file", "message": "Missing yarn.lock", "recommendation": "Commit yarn.lock to ensure consistent dependency versions"}], "recommendations": []}, {"name": "Root Project", "path": ".", "type": "node", "vulnerabilities": [], "outdatedPackages": [], "unusedDependencies": [], "securityIssues": [{"type": "missing_security_config", "message": "No audit script defined", "recommendation": "Add \"audit\": \"npm audit\" to scripts"}, {"type": "missing_security_config", "message": "No audit-fix script defined", "recommendation": "Add \"audit-fix\": \"npm audit fix\" to scripts"}, {"type": "missing_lock_file", "message": "Missing yarn.lock", "recommendation": "Commit yarn.lock to ensure consistent dependency versions"}], "recommendations": []}], "summary": {"totalVulnerabilities": 0, "criticalVulnerabilities": 0, "highVulnerabilities": 0, "moderateVulnerabilities": 0, "lowVulnerabilities": 0, "outdatedPackages": 0, "unusedDependencies": 0}, "recommendations": []}