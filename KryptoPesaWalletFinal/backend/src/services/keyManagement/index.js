/**
 * Key Management Integration Service
 * Provides unified interface for enhanced key management with monitoring and security
 */

const secureKeyService = require('./secureKeyService');
const KeyMonitoringService = require('./keyMonitoringService');
const logger = require('../../utils/logger');

class KeyManagementService {
  constructor() {
    this.secureKeyService = secureKeyService;
    this.monitoringService = new KeyMonitoringService();
    this.isInitialized = false;
  }

  /**
   * Initialize the complete key management system
   */
  async initialize() {
    try {
      logger.info('🔐 Initializing enhanced key management system...');

      // Initialize secure key service
      await this.secureKeyService.initialize();

      // Initialize monitoring service
      await this.monitoringService.initialize();

      this.isInitialized = true;
      logger.info('✅ Enhanced key management system initialized successfully');

      return {
        success: true,
        fallbackMode: this.secureKeyService.fallbackMode,
        services: {
          secureKeys: true,
          monitoring: true
        }
      };
    } catch (error) {
      logger.error('❌ Failed to initialize key management system:', error);
      throw error;
    }
  }

  /**
   * Get secure wallet with monitoring
   */
  async getSecureWallet(network = 'polygon', context = {}) {
    this.ensureInitialized();

    try {
      const wallet = await this.secureKeyService.getSecureWallet(network);
      
      // Record access for monitoring
      this.monitoringService.recordKeyAccess(`${network}_wallet`, {
        serviceId: context.serviceId || 'unknown',
        ipAddress: context.ipAddress || 'internal',
        userAgent: context.userAgent || 'system',
        purpose: context.purpose || 'wallet_operation'
      });

      return wallet;
    } catch (error) {
      // Record failed access
      await this.monitoringService.recordSecurityAlert('wallet_access_failed', {
        network,
        error: error.message,
        context
      });
      throw error;
    }
  }

  /**
   * Store encrypted key with full security
   */
  async storeEncryptedKey(network, encryptedKey, metadata = {}) {
    this.ensureInitialized();

    try {
      await this.secureKeyService.storeEncryptedKey(network, encryptedKey);
      
      // Record storage event
      this.monitoringService.recordKeyAccess(`${network}_storage`, {
        action: 'store',
        metadata
      });

      logger.info(`✅ Encrypted key stored securely for ${network}`);
      return true;
    } catch (error) {
      await this.monitoringService.recordSecurityAlert('key_storage_failed', {
        network,
        error: error.message,
        metadata
      });
      throw error;
    }
  }

  /**
   * Encrypt and store private key
   */
  async encryptAndStorePrivateKey(network, privateKey, metadata = {}) {
    this.ensureInitialized();

    try {
      await this.secureKeyService.encryptPrivateKey(network, privateKey);
      
      // Record encryption event
      this.monitoringService.recordKeyAccess(`${network}_encryption`, {
        action: 'encrypt_and_store',
        metadata
      });

      logger.info(`✅ Private key encrypted and stored for ${network}`);
      return true;
    } catch (error) {
      await this.monitoringService.recordSecurityAlert('key_encryption_failed', {
        network,
        error: error.message,
        metadata
      });
      throw error;
    }
  }

  /**
   * Force key rotation for specific network
   */
  async rotateNetworkKey(network, reason = 'manual_rotation') {
    this.ensureInitialized();

    try {
      logger.info(`🔄 Initiating manual key rotation for ${network}`);
      
      // Find the key
      const SecureKey = require('../../models/SecureKey');
      const keyId = `blockchain_${network}_private_key`;
      const secureKey = await SecureKey.findOne({ keyId });

      if (!secureKey) {
        throw new Error(`No key found for network: ${network}`);
      }

      // Perform rotation
      await this.secureKeyService.rotateIndividualKey(secureKey);

      // Record manual rotation
      await this.monitoringService.recordSecurityAlert('manual_key_rotation', {
        network,
        reason,
        keyId: secureKey.keyId
      });

      logger.info(`✅ Manual key rotation completed for ${network}`);
      return true;
    } catch (error) {
      await this.monitoringService.recordSecurityAlert('manual_rotation_failed', {
        network,
        reason,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Mark key as compromised
   */
  async markKeyCompromised(network, reason, userId = 'system') {
    this.ensureInitialized();

    try {
      const SecureKey = require('../../models/SecureKey');
      const keyId = `blockchain_${network}_private_key`;
      const secureKey = await SecureKey.findOne({ keyId });

      if (!secureKey) {
        throw new Error(`No key found for network: ${network}`);
      }

      await secureKey.markCompromised(reason, userId);

      // Record compromise event
      await this.monitoringService.recordSecurityAlert('key_marked_compromised', {
        network,
        keyId: secureKey.keyId,
        reason,
        userId,
        severity: 'critical'
      });

      logger.error(`🚨 Key marked as compromised for ${network}: ${reason}`);
      return true;
    } catch (error) {
      logger.error(`❌ Failed to mark key as compromised for ${network}:`, error);
      throw error;
    }
  }

  /**
   * Get security status for all keys
   */
  async getSecurityStatus() {
    this.ensureInitialized();

    try {
      const [securityReport, metricsReport] = await Promise.all([
        this.secureKeyService.generateSecurityReport(),
        this.monitoringService.generateMetricsReport()
      ]);

      return {
        timestamp: new Date().toISOString(),
        system: {
          initialized: this.isInitialized,
          fallbackMode: this.secureKeyService.fallbackMode
        },
        security: securityReport,
        monitoring: metricsReport,
        recommendations: await this.generateSecurityRecommendations(securityReport, metricsReport)
      };
    } catch (error) {
      logger.error('❌ Failed to get security status:', error);
      throw error;
    }
  }

  /**
   * Generate security recommendations
   */
  async generateSecurityRecommendations(securityReport, metricsReport) {
    const recommendations = [];

    // Check for overdue rotations
    if (metricsReport.keyMetrics.keysNeedingRotation > 0) {
      recommendations.push({
        type: 'rotation_overdue',
        severity: 'high',
        message: `${metricsReport.keyMetrics.keysNeedingRotation} keys need rotation`,
        action: 'Run key rotation process immediately'
      });
    }

    // Check for compromised keys
    if (metricsReport.keyMetrics.compromisedKeys > 0) {
      recommendations.push({
        type: 'compromised_keys',
        severity: 'critical',
        message: `${metricsReport.keyMetrics.compromisedKeys} keys are compromised`,
        action: 'Investigate and replace compromised keys immediately'
      });
    }

    // Check for high alert frequency
    if (metricsReport.securityMetrics.alertsLast24Hours > 10) {
      recommendations.push({
        type: 'high_alert_frequency',
        severity: 'medium',
        message: `${metricsReport.securityMetrics.alertsLast24Hours} security alerts in last 24 hours`,
        action: 'Review security alerts and investigate patterns'
      });
    }

    // Check fallback mode
    if (this.secureKeyService.fallbackMode) {
      recommendations.push({
        type: 'fallback_mode_active',
        severity: 'medium',
        message: 'System running in fallback mode (AWS unavailable)',
        action: 'Restore AWS connectivity for full security features'
      });
    }

    return recommendations;
  }

  /**
   * Perform security audit
   */
  async performSecurityAudit() {
    this.ensureInitialized();

    try {
      logger.info('🔍 Starting comprehensive security audit...');

      const auditResults = {
        timestamp: new Date().toISOString(),
        checks: {}
      };

      // Check key integrity
      auditResults.checks.keyIntegrity = await this.auditKeyIntegrity();

      // Check access patterns
      auditResults.checks.accessPatterns = await this.auditAccessPatterns();

      // Check rotation compliance
      auditResults.checks.rotationCompliance = await this.auditRotationCompliance();

      // Check security configuration
      auditResults.checks.securityConfig = await this.auditSecurityConfiguration();

      // Generate overall score
      auditResults.overallScore = this.calculateSecurityScore(auditResults.checks);

      logger.info(`📋 Security audit completed. Score: ${auditResults.overallScore}/100`);
      return auditResults;
    } catch (error) {
      logger.error('❌ Security audit failed:', error);
      throw error;
    }
  }

  /**
   * Audit key integrity
   */
  async auditKeyIntegrity() {
    const SecureKey = require('../../models/SecureKey');
    const keys = await SecureKey.find({ 'security.isActive': true });
    
    let integrityIssues = 0;
    for (const key of keys) {
      // Verify checksum
      const currentChecksum = require('crypto')
        .createHash('sha256')
        .update(JSON.stringify(key.encryptedData))
        .digest('hex');
      
      if (currentChecksum !== key.security.checksumHash) {
        integrityIssues++;
      }
    }

    return {
      totalKeys: keys.length,
      integrityIssues,
      score: Math.max(0, 100 - (integrityIssues * 20))
    };
  }

  /**
   * Audit access patterns
   */
  async auditAccessPatterns() {
    const recentAlerts = this.monitoringService.getRecentAlerts(24);
    const suspiciousAlerts = recentAlerts.filter(alert => 
      alert.type.includes('suspicious') || alert.type.includes('failed')
    );

    return {
      totalAlerts: recentAlerts.length,
      suspiciousAlerts: suspiciousAlerts.length,
      score: Math.max(0, 100 - (suspiciousAlerts.length * 10))
    };
  }

  /**
   * Audit rotation compliance
   */
  async auditRotationCompliance() {
    const SecureKey = require('../../models/SecureKey');
    const overdueKeys = await SecureKey.findKeysNeedingRotation();
    const totalKeys = await SecureKey.countDocuments({ 'security.isActive': true });

    const complianceRate = totalKeys > 0 ? ((totalKeys - overdueKeys.length) / totalKeys) * 100 : 100;

    return {
      totalKeys,
      overdueKeys: overdueKeys.length,
      complianceRate,
      score: Math.round(complianceRate)
    };
  }

  /**
   * Audit security configuration
   */
  async auditSecurityConfiguration() {
    const issues = [];

    // Check environment variables
    if (!process.env.ENCRYPTION_KEY || process.env.ENCRYPTION_KEY.length < 32) {
      issues.push('Weak encryption key');
    }

    if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
      issues.push('Weak JWT secret');
    }

    // Check AWS configuration
    if (!process.env.AWS_KMS_KEY_ID) {
      issues.push('Missing AWS KMS key ID');
    }

    return {
      issues,
      score: Math.max(0, 100 - (issues.length * 25))
    };
  }

  /**
   * Calculate overall security score
   */
  calculateSecurityScore(checks) {
    const scores = Object.values(checks).map(check => check.score || 0);
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }

  /**
   * Ensure service is initialized
   */
  ensureInitialized() {
    if (!this.isInitialized) {
      throw new Error('Key management service not initialized. Call initialize() first.');
    }
  }

  /**
   * Cleanup all services
   */
  cleanup() {
    this.secureKeyService.cleanup();
    this.monitoringService.cleanup();
    this.isInitialized = false;
    logger.info('🧹 Key management system cleaned up');
  }
}

// Export singleton instance
const keyManagementService = new KeyManagementService();

module.exports = {
  KeyManagementService,
  keyManagementService,
  secureKeyService,
  KeyMonitoringService
};
