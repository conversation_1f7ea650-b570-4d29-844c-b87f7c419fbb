
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for models</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> models</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">37.32% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>237/635</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12.37% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>37/299</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.48% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>22/119</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.18% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>236/618</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="AuditLog.js"><a href="AuditLog.js.html">AuditLog.js</a></td>
	<td data-value="42.85" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 42%"></div><div class="cover-empty" style="width: 58%"></div></div>
	</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="42" class="abs low">18/42</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="47.36" class="pct low">47.36%</td>
	<td data-value="38" class="abs low">18/38</td>
	</tr>

<tr>
	<td class="file low" data-value="Chat.js"><a href="Chat.js.html">Chat.js</a></td>
	<td data-value="30.26" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 30%"></div><div class="cover-empty" style="width: 70%"></div></div>
	</td>
	<td data-value="30.26" class="pct low">30.26%</td>
	<td data-value="76" class="abs low">23/76</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="32.85" class="pct low">32.85%</td>
	<td data-value="70" class="abs low">23/70</td>
	</tr>

<tr>
	<td class="file low" data-value="Dispute.js"><a href="Dispute.js.html">Dispute.js</a></td>
	<td data-value="34.48" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 34%"></div><div class="cover-empty" style="width: 66%"></div></div>
	</td>
	<td data-value="34.48" class="pct low">34.48%</td>
	<td data-value="58" class="abs low">20/58</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="34.48" class="pct low">34.48%</td>
	<td data-value="58" class="abs low">20/58</td>
	</tr>

<tr>
	<td class="file low" data-value="Message.js"><a href="Message.js.html">Message.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="69" class="abs low">0/69</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="49" class="abs low">0/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="68" class="abs low">0/68</td>
	</tr>

<tr>
	<td class="file low" data-value="Offer.js"><a href="Offer.js.html">Offer.js</a></td>
	<td data-value="24.44" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 24%"></div><div class="cover-empty" style="width: 76%"></div></div>
	</td>
	<td data-value="24.44" class="pct low">24.44%</td>
	<td data-value="90" class="abs low">22/90</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="49" class="abs low">0/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="24.44" class="pct low">24.44%</td>
	<td data-value="90" class="abs low">22/90</td>
	</tr>

<tr>
	<td class="file low" data-value="SecureKey.js"><a href="SecureKey.js.html">SecureKey.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="64" class="abs low">0/64</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="61" class="abs low">0/61</td>
	</tr>

<tr>
	<td class="file medium" data-value="Trade.js"><a href="Trade.js.html">Trade.js</a></td>
	<td data-value="54.54" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="77" class="abs medium">42/77</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="44" class="abs low">11/44</td>
	<td data-value="46.66" class="pct low">46.66%</td>
	<td data-value="15" class="abs low">7/15</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="77" class="abs medium">42/77</td>
	</tr>

<tr>
	<td class="file high" data-value="User.js"><a href="User.js.html">User.js</a></td>
	<td data-value="89.79" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 89%"></div><div class="cover-empty" style="width: 11%"></div></div>
	</td>
	<td data-value="89.79" class="pct high">89.79%</td>
	<td data-value="98" class="abs high">88/98</td>
	<td data-value="56.81" class="pct medium">56.81%</td>
	<td data-value="44" class="abs medium">25/44</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="14" class="abs high">13/14</td>
	<td data-value="90.62" class="pct high">90.62%</td>
	<td data-value="96" class="abs high">87/96</td>
	</tr>

<tr>
	<td class="file low" data-value="Wallet.js"><a href="Wallet.js.html">Wallet.js</a></td>
	<td data-value="39.34" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 39%"></div><div class="cover-empty" style="width: 61%"></div></div>
	</td>
	<td data-value="39.34" class="pct low">39.34%</td>
	<td data-value="61" class="abs low">24/61</td>
	<td data-value="4.34" class="pct low">4.34%</td>
	<td data-value="23" class="abs low">1/23</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="14" class="abs low">2/14</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="60" class="abs low">24/60</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-08T14:32:16.455Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    