# Business Flow Monitoring Documentation

## Overview

The Business Flow Monitoring Service provides comprehensive monitoring and alerting for critical business processes in the KryptoPesa platform. It monitors trade flows, escrow operations, dispute resolution, wallet operations, authentication flows, and abnormal activity patterns.

## Features

### 1. Trade Flow Monitoring
- **Stuck Trade Detection**: Identifies trades pending for more than 24 hours
- **Trade Failure Rate Monitoring**: Alerts when trade failure rate exceeds 5%
- **Dispute Rate Monitoring**: Alerts when dispute rate exceeds 10%
- **Trade Completion Tracking**: Monitors trade lifecycle and completion rates

### 2. Escrow Flow Monitoring
- **Escrow Funding Timeout**: Detects escrows not funded within 2 hours
- **Escrow Failure Rate**: Alerts when escrow failure rate exceeds 2%
- **Stuck Escrow Detection**: Identifies escrows stuck in pending state
- **Escrow Release Monitoring**: Tracks escrow release operations

### 3. Dispute Flow Monitoring
- **Unresolved Dispute Alerts**: Alerts when more than 10 disputes are unresolved
- **Old Dispute Detection**: Identifies disputes unresolved for more than 3 days
- **Dispute Resolution Tracking**: Monitors dispute resolution times and patterns

### 4. Wallet Flow Monitoring
- **Wallet Creation Failures**: Monitors wallet creation errors
- **Transaction Failures**: Tracks wallet operation failures
- **Balance Update Monitoring**: Monitors wallet balance update operations

### 5. Authentication Flow Monitoring
- **Authentication Failure Rate**: Alerts when auth failure rate exceeds 15%
- **Blocked User Monitoring**: Tracks users blocked due to failed login attempts
- **Suspicious Login Activity**: Detects abnormal authentication patterns

### 6. Abnormal Activity Monitoring
- **Trading Volume Spikes**: Detects volume 5x higher than normal
- **Suspicious User Activity**: Identifies users with abnormally high trading activity
- **Rapid Trade Creation**: Alerts on more than 20 trades in 5 minutes

## Alert Thresholds

```javascript
alertThresholds: {
  tradeFailureRate: 0.05,        // 5%
  escrowFailureRate: 0.02,       // 2%
  disputeRate: 0.10,             // 10%
  stuckTradeThreshold: 24 * 60 * 60 * 1000,  // 24 hours
  stuckEscrowThreshold: 2 * 60 * 60 * 1000,  // 2 hours
  unresolvedDisputeThreshold: 10,
  authFailureRate: 0.15,         // 15%
  abnormalTradingVolumeThreshold: 5.0  // 5x normal volume
}
```

## Monitoring Intervals

- **Trade Flow Monitoring**: Every 5 minutes
- **Escrow Flow Monitoring**: Every 3 minutes
- **Dispute Flow Monitoring**: Every 10 minutes
- **Wallet Flow Monitoring**: Every 15 minutes
- **Authentication Flow Monitoring**: Every 5 minutes
- **Abnormal Activity Monitoring**: Every 10 minutes

## API Endpoints

### Get Business Flow Metrics
```
GET /api/monitoring/business-flows
```

**Response:**
```json
{
  "success": true,
  "data": {
    "trades": {
      "created": 45,
      "funded": 42,
      "completed": 38,
      "failed": 3,
      "disputed": 2,
      "stuck": 1,
      "avgCompletionTime": 0
    },
    "escrows": {
      "created": 42,
      "funded": 40,
      "released": 38,
      "failed": 2,
      "stuck": 0,
      "avgFundingTime": 0
    },
    "disputes": {
      "created": 2,
      "resolved": 1,
      "escalated": 0,
      "unresolved": 1,
      "avgResolutionTime": 0
    },
    "wallets": {
      "created": 15,
      "transactions": 120,
      "failed": 2,
      "balanceUpdates": 85
    },
    "authentication": {
      "logins": 180,
      "failures": 12,
      "suspiciousActivity": 0,
      "blockedAttempts": 2
    },
    "isInitialized": true,
    "timestamp": "2025-01-08T10:30:00.000Z"
  }
}
```

### Record Business Flow Event
```
POST /api/monitoring/business-flows/event
```

**Request Body:**
```json
{
  "flowType": "trade",
  "eventType": "created",
  "eventData": {
    "status": "created",
    "cryptocurrency": "BTC",
    "amount": 0.001,
    "tradeId": "trade_id_here"
  }
}
```

## Alert Types

### Critical Alerts
- `STUCK_TRADES_DETECTED`: Trades stuck for more than 24 hours
- `STUCK_ESCROWS_DETECTED`: Escrows stuck for more than 2 hours
- `HIGH_ESCROW_FAILURE_RATE`: Escrow failure rate exceeds threshold

### High Priority Alerts
- `HIGH_TRADE_FAILURE_RATE`: Trade failure rate exceeds threshold
- `OLD_UNRESOLVED_DISPUTES`: Disputes unresolved for more than 3 days
- `RAPID_TRADE_CREATION`: Rapid trade creation detected

### Medium Priority Alerts
- `HIGH_DISPUTE_RATE`: Trade dispute rate exceeds threshold
- `HIGH_UNRESOLVED_DISPUTES`: Too many unresolved disputes
- `HIGH_AUTH_FAILURE_RATE`: Authentication failure rate exceeds threshold
- `MULTIPLE_BLOCKED_USERS`: Multiple users blocked due to failed logins
- `ABNORMAL_TRADING_VOLUME`: Trading volume significantly higher than normal
- `SUSPICIOUS_USER_ACTIVITY`: Users with abnormally high trading activity
- `WALLET_OPERATION_FAILURES`: Wallet operation failures detected

## Integration

### Service Integration
The Business Flow Monitoring Service integrates with:
- **Trading Service**: Records trade creation, completion, cancellation, and disputes
- **Escrow Service**: Records escrow creation, funding, release, and failures
- **Wallet Service**: Records wallet operations and failures
- **Authentication Service**: Records login attempts and failures
- **Monitoring Integration Service**: Sends alerts through comprehensive monitoring

### Event Recording
Services can record business flow events using:
```javascript
businessFlowMonitoringService.recordBusinessFlowEvent(flowType, eventType, eventData);
```

**Example:**
```javascript
// Record trade creation
businessFlowMonitoringService.recordBusinessFlowEvent('trade', 'created', {
  status: 'created',
  cryptocurrency: 'BTC',
  amount: 0.001,
  tradeId: trade._id
});

// Record escrow failure
businessFlowMonitoringService.recordBusinessFlowEvent('escrow', 'failed', {
  status: 'failed',
  tradeId,
  error: error.message
});
```

## Initialization

The service is automatically initialized during server startup:
```javascript
const { businessFlowMonitoringService } = require('./services/businessFlowMonitoring');
await businessFlowMonitoringService.initialize();
```

## Shutdown

The service is gracefully shutdown during server shutdown:
```javascript
await businessFlowMonitoringService.shutdown();
```

## Metrics Collection

The service maintains internal metrics for:
- Trade statistics (created, completed, failed, disputed, stuck)
- Escrow statistics (created, funded, released, failed, stuck)
- Dispute statistics (created, resolved, escalated, unresolved)
- Wallet statistics (created, transactions, failed, balance updates)
- Authentication statistics (logins, failures, suspicious activity, blocked attempts)

## Alert Integration

All alerts are sent through the comprehensive monitoring system with:
- **Multi-channel delivery**: Email, Discord, Slack
- **Severity levels**: Critical, High, Medium, Low
- **Alert details**: Threshold values, current values, context data
- **Cooldown periods**: Prevents alert spam
- **Environment context**: Development/Production environment information

## Production Considerations

1. **Performance**: Monitoring intervals are optimized to balance detection speed with system load
2. **Scalability**: Service designed to handle high-volume trading platforms
3. **Reliability**: Comprehensive error handling and graceful degradation
4. **Security**: Sensitive data filtering in alerts and logs
5. **Maintainability**: Modular design with clear separation of concerns

## Troubleshooting

### Common Issues
1. **Service not initialized**: Check server startup logs for initialization errors
2. **Missing alerts**: Verify monitoring integration service is running
3. **High false positives**: Adjust alert thresholds based on platform usage patterns
4. **Performance impact**: Monitor system resources and adjust monitoring intervals if needed

### Debug Information
Enable debug logging to see detailed monitoring activity:
```javascript
logger.debug('Business flow monitoring completed', {
  totalTrades,
  failureRate,
  disputeRate,
  stuckTrades
});
```
