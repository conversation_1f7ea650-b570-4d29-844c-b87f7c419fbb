#!/bin/bash

# KryptoPesa Security Audit Script
# Comprehensive security testing for production readiness

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_WARNING=0

# Function to record test results
record_test() {
    if [ $1 -eq 0 ]; then
        print_success "$2"
        ((TESTS_PASSED++))
    elif [ $1 -eq 2 ]; then
        print_warning "$2"
        ((TESTS_WARNING++))
    else
        print_error "$2"
        ((TESTS_FAILED++))
    fi
}

# Check if server is running
check_server() {
    print_header "Checking Server Status"
    
    if curl -f http://localhost:3000/health &> /dev/null; then
        record_test 0 "Server is running"
    else
        record_test 1 "Server is not running - please start the backend server"
        exit 1
    fi
}

# Test rate limiting
test_rate_limiting() {
    print_header "Testing Rate Limiting"
    
    # Test auth endpoint rate limiting
    print_info "Testing auth endpoint rate limiting (5 requests/15min)..."
    local auth_failures=0
    for i in {1..6}; do
        response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:3000/api/auth/login \
            -H "Content-Type: application/json" \
            -d '{"identifier":"test","password":"test"}')
        
        if [ $i -eq 6 ] && [ "$response" = "429" ]; then
            record_test 0 "Auth rate limiting working (got 429 on 6th request)"
            break
        elif [ $i -eq 6 ]; then
            record_test 1 "Auth rate limiting not working (expected 429, got $response)"
        fi
        sleep 0.1
    done
    
    # Test general API rate limiting
    print_info "Testing general API rate limiting..."
    local general_success=false
    for i in {1..101}; do
        response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/offers)
        if [ "$response" = "429" ]; then
            record_test 0 "General rate limiting working (got 429 after $i requests)"
            general_success=true
            break
        fi
        sleep 0.01
    done
    
    if [ "$general_success" = false ]; then
        record_test 2 "General rate limiting may be too lenient"
    fi
}

# Test input validation
test_input_validation() {
    print_header "Testing Input Validation"
    
    # Test SQL injection protection
    print_info "Testing SQL injection protection..."
    response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:3000/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"identifier":"admin'\'' OR 1=1--","password":"test"}')
    
    if [ "$response" = "400" ] || [ "$response" = "401" ]; then
        record_test 0 "SQL injection protection working"
    else
        record_test 1 "SQL injection protection may be insufficient (got $response)"
    fi
    
    # Test XSS protection
    print_info "Testing XSS protection..."
    response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:3000/api/auth/register \
        -H "Content-Type: application/json" \
        -d '{"username":"<script>alert(1)</script>","email":"<EMAIL>","password":"password123"}')
    
    if [ "$response" = "400" ]; then
        record_test 0 "XSS protection working"
    else
        record_test 2 "XSS protection may need review (got $response)"
    fi
    
    # Test oversized payload protection
    print_info "Testing oversized payload protection..."
    large_payload=$(python3 -c "print('a' * 2000000)")  # 2MB payload
    response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:3000/api/auth/login \
        -H "Content-Type: application/json" \
        -d "{\"identifier\":\"$large_payload\",\"password\":\"test\"}")
    
    if [ "$response" = "413" ] || [ "$response" = "400" ]; then
        record_test 0 "Oversized payload protection working"
    else
        record_test 1 "Oversized payload protection insufficient (got $response)"
    fi
}

# Test CORS configuration
test_cors() {
    print_header "Testing CORS Configuration"
    
    # Test CORS with malicious origin
    cors_header=$(curl -s -H "Origin: http://malicious-site.com" -I http://localhost:3000/health | grep -i "access-control-allow-origin" || echo "")
    
    if [[ $cors_header == *"localhost"* ]] || [[ $cors_header == *"kryptopesa.com"* ]] || [[ -z "$cors_header" ]]; then
        record_test 0 "CORS properly configured"
    else
        record_test 1 "CORS may be misconfigured: $cors_header"
    fi
}

# Test security headers
test_security_headers() {
    print_header "Testing Security Headers"
    
    headers=$(curl -s -I http://localhost:3000/health)
    
    # Check for security headers
    if echo "$headers" | grep -qi "x-frame-options"; then
        record_test 0 "X-Frame-Options header present"
    else
        record_test 1 "X-Frame-Options header missing"
    fi
    
    if echo "$headers" | grep -qi "x-content-type-options"; then
        record_test 0 "X-Content-Type-Options header present"
    else
        record_test 1 "X-Content-Type-Options header missing"
    fi
    
    if echo "$headers" | grep -qi "x-xss-protection"; then
        record_test 0 "X-XSS-Protection header present"
    else
        record_test 2 "X-XSS-Protection header missing (optional)"
    fi
    
    if echo "$headers" | grep -qi "strict-transport-security"; then
        record_test 2 "HSTS header present (good for production)"
    else
        record_test 2 "HSTS header missing (add for production HTTPS)"
    fi
}

# Test authentication security
test_auth_security() {
    print_header "Testing Authentication Security"
    
    # Test JWT token validation
    print_info "Testing JWT token validation..."
    response=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer invalid_token" http://localhost:3000/api/auth/me)
    
    if [ "$response" = "401" ]; then
        record_test 0 "JWT token validation working"
    else
        record_test 1 "JWT token validation may be insufficient (got $response)"
    fi
    
    # Test password requirements
    print_info "Testing password requirements..."
    response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:3000/api/auth/register \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser","email":"<EMAIL>","password":"123","firstName":"Test","lastName":"User","country":"KE","phone":"+254700000000"}')
    
    if [ "$response" = "400" ]; then
        record_test 0 "Password requirements enforced"
    else
        record_test 1 "Password requirements may be insufficient (got $response)"
    fi
}

# Test file upload security
test_file_upload_security() {
    print_header "Testing File Upload Security"
    
    # Test file size limits
    print_info "Testing file size limits..."
    
    # Create a large file (6MB)
    dd if=/dev/zero of=/tmp/large_file.txt bs=1M count=6 2>/dev/null
    
    response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:3000/api/chat/test/messages \
        -F "file=@/tmp/large_file.txt")
    
    if [ "$response" = "413" ] || [ "$response" = "400" ] || [ "$response" = "401" ]; then
        record_test 0 "File size limits working"
    else
        record_test 2 "File size limits may need review (got $response)"
    fi
    
    # Cleanup
    rm -f /tmp/large_file.txt
}

# Test database security
test_database_security() {
    print_header "Testing Database Security"
    
    # Test NoSQL injection
    print_info "Testing NoSQL injection protection..."
    response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:3000/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"identifier":{"$ne":""},"password":{"$ne":""}}')
    
    if [ "$response" = "400" ] || [ "$response" = "401" ]; then
        record_test 0 "NoSQL injection protection working"
    else
        record_test 1 "NoSQL injection protection may be insufficient (got $response)"
    fi
}

# Test WebSocket security
test_websocket_security() {
    print_header "Testing WebSocket Security"
    
    print_info "Testing WebSocket authentication..."
    
    # This is a basic test - in a real scenario you'd use a WebSocket client
    if netstat -tlnp 2>/dev/null | grep -q ":3000.*LISTEN"; then
        record_test 0 "WebSocket server is running"
    else
        record_test 2 "WebSocket server status unclear"
    fi
}

# Main execution
main() {
    print_header "KryptoPesa Security Audit"
    print_info "Starting comprehensive security testing..."
    echo
    
    check_server
    test_rate_limiting
    test_input_validation
    test_cors
    test_security_headers
    test_auth_security
    test_file_upload_security
    test_database_security
    test_websocket_security
    
    # Print summary
    print_header "Security Audit Summary"
    echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
    echo -e "Warnings: ${YELLOW}$TESTS_WARNING${NC}"
    echo
    
    if [ $TESTS_FAILED -eq 0 ]; then
        print_success "Security audit completed successfully!"
        if [ $TESTS_WARNING -gt 0 ]; then
            print_warning "Please review warnings for production deployment"
        fi
        exit 0
    else
        print_error "Security audit found critical issues that must be fixed"
        exit 1
    fi
}

# Run main function
main "$@"
