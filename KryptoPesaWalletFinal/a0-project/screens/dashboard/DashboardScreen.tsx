import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useNotifications } from '../../context/NotificationContext';
import { Ionicons } from '@expo/vector-icons';
// Services will be imported dynamically to avoid early initialization

// East African focused mock data for development
const mockEastAfricanTrades = [
  {
    id: '1',
    type: 'buy',
    cryptoCurrency: 'USDT',
    fiatCurrency: 'KES',
    amount: 100,
    fiatAmount: 14500, // 1 USDT = 145 KES
    status: 'completed',
    date: '2023-07-05T14:30:00Z',
    paymentMethod: 'mpesa',
  },
  {
    id: '2',
    type: 'sell',
    cryptoCurrency: 'USDC',
    fiatCurrency: 'TZS',
    amount: 50,
    fiatAmount: 125000, // 1 USDC = 2500 TZS
    status: 'pending',
    date: '2023-07-04T09:15:00Z',
    paymentMethod: 'airtel_money',
  },
];

export default function DashboardScreen() {
  // Default dashboard data to prevent null errors
  const defaultDashboardData: UserDashboard = {
    user: {
      _id: 'loading',
      username: 'Loading...',
      preferences: {
        currency: 'USD',
        language: 'en',
        country: 'US'
      }
    },
    stats: {
      walletValue: 0,
      profitLoss: 0,
      profitLossPercentage: 0,
      activeTrades: 0,
      completedTrades: 0,
      pendingOffers: 0
    },
    totalBalance: 0,
    totalChange: 0,
    activeTrades: 0,
    completedTrades: 0,
    pendingOffers: 0,
    recentTrades: [],
    notifications: []
  };

  const [refreshing, setRefreshing] = useState(false);
  const [wallets, setWallets] = useState<WalletBalance[]>([]);
  const [dashboardData, setDashboardData] = useState<UserDashboard>(defaultDashboardData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState<any>(null);

  const navigation = useNavigation();
  const { user, logout } = useAuth();
  const { theme } = useTheme();
  const { unreadCount } = useNotifications();
  const styles = createStyles(theme);

  // Load dashboard data with comprehensive fallbacks
  const loadDashboardData = async () => {
    try {
      setError(null);
      // Dynamic import to avoid early initialization
      const { walletService, userService, EAST_AFRICAN_CONFIG } = await import('../../services');
      setConfig(EAST_AFRICAN_CONFIG);

      let walletsData, dashboardInfo;

      try {
        // Try to load real data from backend
        [walletsData, dashboardInfo] = await Promise.all([
          walletService.getWalletBalances(),
          userService.getDashboard(),
        ]);
      } catch (apiError) {
        console.log('Backend not available, using mock data for dashboard');

        // Provide comprehensive mock data when backend is unavailable
        walletsData = [
          {
            currency: 'USDT',
            balance: 1250.50,
            pendingBalance: 0,
            usdValue: 1250.50,
            change: 2.5,
            address: '******************************************',
            name: 'Tether USD',
            network: 'Polygon'
          },
          {
            currency: 'BTC',
            balance: 0.0234,
            pendingBalance: 0,
            usdValue: 1456.78,
            change: -1.2,
            address: '******************************************',
            name: 'Bitcoin',
            network: 'Bitcoin'
          },
          {
            currency: 'ETH',
            balance: 0.8901,
            pendingBalance: 0,
            usdValue: 2234.56,
            change: 3.8,
            address: '******************************************',
            name: 'Ethereum',
            network: 'Ethereum'
          }
        ];

        dashboardInfo = {
          user: {
            _id: 'user123',
            username: 'trader_ke',
            preferences: {
              currency: 'KES',
              language: 'en',
              country: 'KE'
            }
          },
          stats: {
            walletValue: 4941.84,
            profitLoss: 1.7,
            profitLossPercentage: 1.7,
            activeTrades: 3,
            completedTrades: 47,
            pendingOffers: 2
          },
          totalBalance: 4941.84,
          totalChange: 1.7,
          activeTrades: 3,
          completedTrades: 47,
          pendingOffers: 2,
          recentTrades: [
            {
              _id: 'trade1',
              type: 'buy',
              cryptocurrency: { type: 'USDT', amount: 500 },
              fiat: { currency: 'KES', amount: 67500 },
              status: 'completed',
              createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
              trader: { username: 'Alice_KE', rating: 4.8 }
            },
            {
              _id: 'trade2',
              type: 'sell',
              cryptocurrency: { type: 'BTC', amount: 0.01 },
              fiat: { currency: 'TZS', amount: 1250000 },
              status: 'in_progress',
              createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
              trader: { username: 'Bob_TZ', rating: 4.9 }
            }
          ],
          notifications: [
            {
              _id: 'notif1',
              type: 'trade_update',
              title: 'Trade Completed',
              message: 'Your USDT purchase has been completed',
              isRead: false,
              createdAt: new Date(Date.now() - 1000 * 60 * 15).toISOString()
            }
          ]
        };
      }

      setWallets(walletsData || []);
      setDashboardData(dashboardInfo || {});
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');

      // Even on error, provide minimal mock data
      setWallets([]);
      setDashboardData({});
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadDashboardData();
  }, []);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  }, []);
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image
            source={{ uri: 'https://api.a0.dev/assets/image?text=CryptoP2P&aspect=1:1&seed=123' }}
            style={styles.logo}
          />
          <Text style={styles.appName}>CryptoP2P</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.notificationButton}
            onPress={() => navigation.navigate('NotificationCenter')}
          >
            <Ionicons name="notifications-outline" size={24} color={theme.colors.text} />
            {unreadCount > 0 && (
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationBadgeText}>{unreadCount}</Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity style={styles.profileButton} onPress={() => navigation.navigate('Profile')}>
            <Ionicons name="person-circle-outline" size={32} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>Welcome, {user?.name || 'Trader'}</Text>
          <Text style={styles.subText}>Your crypto trading dashboard</Text>
        </View>
        
        <View style={styles.balanceCard}>
          <Text style={styles.balanceTitle}>
            Total Balance ({dashboardData.user?.preferences?.currency || 'USD'})
          </Text>
          <Text style={styles.balanceAmount}>
            {config?.CURRENCIES[dashboardData.user?.preferences?.currency || 'USD']?.symbol || '$'}
            {dashboardData.stats?.walletValue?.toLocaleString() || '0.00'}
          </Text>
          <Text style={[
            styles.balanceChange,
            { color: (dashboardData.stats?.profitLoss || 0) >= 0 ? '#10B981' : '#EF4444' }
          ]}>
            {(dashboardData.stats?.profitLoss || 0) >= 0 ? '+' : ''}
            {dashboardData.stats?.profitLossPercentage?.toFixed(2) || '0.00'}% today
          </Text>
        </View>
        
        <Text style={styles.sectionTitle}>Your Wallets</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.walletsContainer}>
          {wallets.map((wallet, index) => (
            <TouchableOpacity
              key={index}
              style={styles.walletCard}
              onPress={() => navigation.navigate('Wallet', { currency: wallet.currency })}
            >
              <View style={styles.walletHeader}>
                <View style={styles.currencyIconContainer}>
                  <Text style={styles.currencyIconText}>
                    {config?.CRYPTOCURRENCIES[wallet.currency]?.symbol || wallet.currency}
                  </Text>
                </View>
                <Text style={styles.currencyName}>
                  {config?.CRYPTOCURRENCIES[wallet.currency]?.name || wallet.currency}
                </Text>
              </View>
              <Text style={styles.walletBalance}>
                {wallet.balance.toFixed(6)} {wallet.currency}
              </Text>
              <Text style={styles.walletUsdValue}>
                {config?.CURRENCIES[dashboardData.user?.preferences?.currency || 'USD']?.symbol || '$'}
                {wallet.usdValue.toFixed(2)}
              </Text>
              <Text style={[
                styles.walletChange,
                wallet.change >= 0 ? styles.positiveChange : styles.negativeChange
              ]}>
                {wallet.change >= 0 ? '+' : ''}{wallet.change.toFixed(2)}%
              </Text>
              <Text style={styles.networkText}>
                {config?.CRYPTOCURRENCIES[wallet.currency]?.network || 'Network'}
              </Text>
            </TouchableOpacity>
          ))}
          <TouchableOpacity style={styles.addWalletCard} onPress={() => navigation.navigate('Wallet')}>
            <Ionicons name="add-circle" size={32} color={theme.colors.primary} />
            <Text style={styles.addWalletText}>Add Wallet</Text>
          </TouchableOpacity>
        </ScrollView>
        
        <View style={styles.actionsContainer}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Offers', { type: 'buy' })}
          >
            <Ionicons name="arrow-down-circle" size={24} color={theme.colors.success} />
            <Text style={styles.actionText}>Buy Crypto</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Offers', { type: 'sell' })}
          >
            <Ionicons name="arrow-up-circle" size={24} color={theme.colors.error} />
            <Text style={styles.actionText}>Sell Crypto</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Wallet')}
          >
            <Ionicons name="wallet" size={24} color={theme.colors.purple} />
            <Text style={styles.actionText}>Wallet</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('ActiveTrades')}
          >
            <Ionicons name="swap-horizontal" size={24} color={theme.colors.warning} />
            <Text style={styles.actionText}>My Trades</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Trades</Text>
          <TouchableOpacity onPress={() => navigation.navigate('ActiveTrades')}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        {(dashboardData?.recentTrades || mockEastAfricanTrades).length > 0 ? (
          (dashboardData?.recentTrades || mockEastAfricanTrades).map((trade, index) => (
            <TouchableOpacity
              key={index}
              style={styles.tradeCard}
              onPress={() => navigation.navigate('Trade', { tradeId: trade._id || trade.id })}
            >
              <View style={styles.tradeIconContainer}>
                <Ionicons
                  name={trade.type === 'buy' ? 'arrow-down-circle' : 'arrow-up-circle'}
                  size={24}
                  color={trade.type === 'buy' ? theme.colors.success : theme.colors.error}
                />
              </View>
              <View style={styles.tradeDetails}>
                <Text style={styles.tradeTitle}>
                  {trade.type === 'buy' ? 'Bought' : 'Sold'} {trade.cryptocurrency?.amount || trade.amount || 0} {trade.cryptocurrency?.type || trade.cryptoCurrency || 'USDT'}
                </Text>
                <Text style={styles.tradeSubtitle}>
                  {config?.CURRENCIES[trade.fiat?.currency || trade.fiatCurrency]?.symbol || trade.fiat?.currency || trade.fiatCurrency} {(trade.fiat?.amount || trade.fiatAmount || 0).toLocaleString()}
                </Text>
                {trade.paymentMethod && (
                  <Text style={styles.paymentMethodText}>
                    via {config?.PAYMENT_METHODS[trade.paymentMethod]?.name || trade.paymentMethod}
                  </Text>
                )}
              </View>
              <View style={styles.tradeStatus}>
                <Text style={[
                  styles.statusText,
                  trade.status === 'completed' ? styles.statusCompleted :
                  trade.status === 'pending' ? styles.statusPending : styles.statusOther
                ]}>
                  {trade.status.charAt(0).toUpperCase() + trade.status.slice(1)}
                </Text>
                <Text style={styles.tradeDate}>
                  {new Date(trade.createdAt || trade.date).toLocaleDateString()}
                </Text>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyStateContainer}>
            <Ionicons name="swap-horizontal" size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateText}>No recent trades</Text>
            <Text style={styles.emptyStateSubText}>Your trading activity will appear here</Text>
          </View>
        )}
        
        <TouchableOpacity 
          style={styles.createOfferButton}
          onPress={() => navigation.navigate('Offers', { action: 'create' })}
        >
          <Text style={styles.createOfferButtonText}>Create New Offer</Text>
        </TouchableOpacity>
      </ScrollView>
      
      <View style={styles.tabBar}>
        <TouchableOpacity style={[styles.tabItem, styles.activeTab]}>
          <Ionicons name="home" size={24} color={theme.colors.tabBarActive} />
          <Text style={styles.activeTabText}>Home</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('Offers')}
        >
          <Ionicons name="list" size={24} color={theme.colors.tabBarInactive} />
          <Text style={styles.tabText}>Offers</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('Wallet')}
        >
          <Ionicons name="wallet" size={24} color={theme.colors.tabBarInactive} />
          <Text style={styles.tabText}>Wallet</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('Chat')}
        >
          <Ionicons name="chatbubbles" size={24} color={theme.colors.tabBarInactive} />
          <Text style={styles.tabText}>Chat</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('NotificationCenter')}
        >
          <View style={styles.tabNotificationContainer}>
            <Ionicons name="notifications" size={24} color={theme.colors.tabBarInactive} />
            {unreadCount > 0 && (
              <View style={styles.tabNotificationBadge}>
                <Text style={styles.tabNotificationBadgeText}>{unreadCount}</Text>
              </View>
            )}
          </View>
          <Text style={styles.tabText}>Alerts</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const { width } = Dimensions.get('window');
const walletCardWidth = Math.min(width * 0.4, 160);

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 32,
    height: 32,
    borderRadius: 8,
    marginRight: 8,
  },
  appName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  notificationButton: {
    padding: 8,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: theme.colors.background,
  },
  notificationBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  profileButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  welcomeSection: {
    padding: 16,
    paddingBottom: 8,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  subText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  balanceCard: {
    backgroundColor: theme.colors.primary,
    borderRadius: 16,
    padding: 20,
    margin: 16,
    marginTop: 8,
    marginBottom: 24,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  balanceTitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  balanceChange: {
    fontSize: 14,
    color: theme.colors.success,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  walletsContainer: {
    paddingLeft: 16,
    marginBottom: 24,
  },
  walletCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 16,
    marginRight: 12,
    width: walletCardWidth,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  walletHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  currencyIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  currencyIconText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  networkText: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginTop: 4,
    opacity: 0.7,
  },
  paymentMethodText: {
    fontSize: 11,
    color: theme.colors.primary,
    marginTop: 2,
    fontStyle: 'italic',
  },
  currencyName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  walletBalance: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  walletUsdValue: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  walletChange: {
    fontSize: 14,
    fontWeight: '500',
  },
  positiveChange: {
    color: theme.colors.success,
  },
  negativeChange: {
    color: theme.colors.error,
  },
  addWalletCard: {
    backgroundColor: theme.colors.primaryLight,
    borderRadius: 16,
    padding: 16,
    marginRight: 16,
    width: walletCardWidth,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.primary + '33',
    borderStyle: 'dashed',
  },
  addWalletText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
    marginTop: 8,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  actionButton: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    minWidth: 90,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginTop: 8,
  },
  tradeCard: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 12,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tradeIconContainer: {
    marginRight: 12,
    justifyContent: 'center',
  },
  tradeDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  tradeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  tradeSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  tradeStatus: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  statusCompleted: {
    color: theme.colors.success,
  },
  statusPending: {
    color: theme.colors.warning,
  },
  statusOther: {
    color: theme.colors.textSecondary,
  },
  tradeDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    marginHorizontal: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  createOfferButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    padding: 16,
    margin: 16,
    alignItems: 'center',
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  createOfferButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: theme.colors.tabBarBackground,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingVertical: 8,
    paddingHorizontal: 8,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  activeTab: {
    borderRadius: 8,
    backgroundColor: theme.colors.primaryLight,
  },
  tabText: {
    fontSize: 12,
    color: theme.colors.tabBarInactive,
    marginTop: 4,
  },
  activeTabText: {
    fontSize: 12,
    color: theme.colors.tabBarActive,
    fontWeight: '500',
    marginTop: 4,
  },
  tabNotificationContainer: {
    position: 'relative',
  },
  tabNotificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: theme.colors.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.background,
  },
  tabNotificationBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
});