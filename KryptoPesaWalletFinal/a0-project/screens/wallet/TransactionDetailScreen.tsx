import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

// Mock transaction data - in a real app this would come from Convex
const mockTransaction = {
  id: 'tx_1234567890',
  type: 'received',
  amount: 0.00125,
  currency: 'BTC',
  fiatAmount: 56.25,
  fiatCurrency: 'USD',
  status: 'completed',
  timestamp: '2024-01-15T14:30:00Z',
  confirmations: 6,
  requiredConfirmations: 3,
  fee: 0.00001,
  fromAddress: '**********************************',
  toAddress: '**********************************',
  txHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
  blockHeight: 825000,
  blockHash: '0000000000000000000123456789abcdef123456789abcdef123456789abcdef',
  description: 'P2P Trade Payment',
  relatedTradeId: 'trade_123',
};

export default function TransactionDetailScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { transactionId } = route.params || {};
  
  // In a real app, we would fetch the transaction data using the transactionId
  // const transaction = useQuery(api.transactions.getTransaction, { transactionId });
  const transaction = mockTransaction;
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return theme.colors.success;
      case 'pending':
        return theme.colors.warning;
      case 'failed':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'pending':
        return 'time';
      case 'failed':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  const formatAddress = (address: string) => {
    if (address.length <= 16) return address;
    return `${address.slice(0, 8)}...${address.slice(-8)}`;
  };
  
  const handleShare = async () => {
    try {
      await Share.share({
        message: `Transaction Details\n\nAmount: ${transaction.amount} ${transaction.currency}\nStatus: ${transaction.status}\nTx Hash: ${transaction.txHash}\n\nView on blockchain explorer for more details.`,
        title: 'Transaction Details',
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };
  
  const handleCopyHash = () => {
    // In a real app, we would copy to clipboard
    Alert.alert('Copied', 'Transaction hash copied to clipboard');
  };
  
  const handleViewOnExplorer = () => {
    Alert.alert(
      'View on Explorer',
      'This would open the transaction in a blockchain explorer',
      [{ text: 'OK' }]
    );
  };
  
  const handleViewTrade = () => {
    if (transaction.relatedTradeId) {
      navigation.navigate('Trade', { tradeId: transaction.relatedTradeId });
    }
  };

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Transaction Details</Text>
        <TouchableOpacity onPress={handleShare}>
          <Ionicons name="share-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Status Section */}
        <View style={styles.statusSection}>
          <View style={[styles.statusIcon, { backgroundColor: getStatusColor(transaction.status) + '20' }]}>
            <Ionicons 
              name={getStatusIcon(transaction.status)} 
              size={32} 
              color={getStatusColor(transaction.status)} 
            />
          </View>
          <Text style={[styles.statusText, { color: getStatusColor(transaction.status) }]}>
            {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
          </Text>
          <Text style={styles.statusDescription}>
            {transaction.type === 'received' ? 'Received' : 'Sent'}
          </Text>
        </View>
        
        {/* Amount Section */}
        <View style={styles.section}>
          <View style={styles.amountContainer}>
            <Text style={styles.amountLabel}>Amount</Text>
            <Text style={[
              styles.amountValue,
              transaction.type === 'received' ? styles.receivedAmount : styles.sentAmount
            ]}>
              {transaction.type === 'received' ? '+' : '-'}{transaction.amount} {transaction.currency}
            </Text>
            <Text style={styles.fiatAmount}>
              ≈ ${transaction.fiatAmount.toFixed(2)} {transaction.fiatCurrency}
            </Text>
          </View>
        </View>
        
        {/* Transaction Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Transaction Details</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Transaction ID</Text>
            <TouchableOpacity style={styles.hashContainer} onPress={handleCopyHash}>
              <Text style={styles.hashText}>{formatAddress(transaction.txHash)}</Text>
              <Ionicons name="copy-outline" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Date & Time</Text>
            <Text style={styles.detailValue}>{formatDate(transaction.timestamp)}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Network Fee</Text>
            <Text style={styles.detailValue}>{transaction.fee} {transaction.currency}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Confirmations</Text>
            <View style={styles.confirmationContainer}>
              <Text style={[
                styles.confirmationText,
                transaction.confirmations >= transaction.requiredConfirmations 
                  ? styles.confirmedText 
                  : styles.pendingText
              ]}>
                {transaction.confirmations}/{transaction.requiredConfirmations}
              </Text>
              {transaction.confirmations >= transaction.requiredConfirmations && (
                <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
              )}
            </View>
          </View>
          
          {transaction.description && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Description</Text>
              <Text style={styles.detailValue}>{transaction.description}</Text>
            </View>
          )}
        </View>
        
        {/* Address Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Address Details</Text>
          
          <View style={styles.addressContainer}>
            <Text style={styles.addressLabel}>From</Text>
            <TouchableOpacity style={styles.addressRow} onPress={handleCopyHash}>
              <Text style={styles.addressText}>{formatAddress(transaction.fromAddress)}</Text>
              <Ionicons name="copy-outline" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.addressSeparator}>
            <Ionicons name="arrow-down" size={20} color={theme.colors.textSecondary} />
          </View>
          
          <View style={styles.addressContainer}>
            <Text style={styles.addressLabel}>To</Text>
            <TouchableOpacity style={styles.addressRow} onPress={handleCopyHash}>
              <Text style={styles.addressText}>{formatAddress(transaction.toAddress)}</Text>
              <Ionicons name="copy-outline" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Blockchain Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Blockchain Details</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Block Height</Text>
            <Text style={styles.detailValue}>{transaction.blockHeight.toLocaleString()}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Block Hash</Text>
            <TouchableOpacity style={styles.hashContainer} onPress={handleCopyHash}>
              <Text style={styles.hashText}>{formatAddress(transaction.blockHash)}</Text>
              <Ionicons name="copy-outline" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleViewOnExplorer}>
            <Ionicons name="globe-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.actionButtonText}>View on Explorer</Text>
          </TouchableOpacity>
          
          {transaction.relatedTradeId && (
            <TouchableOpacity style={styles.actionButton} onPress={handleViewTrade}>
              <Ionicons name="swap-horizontal-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.actionButtonText}>View Related Trade</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  statusSection: {
    alignItems: 'center',
    paddingVertical: 32,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    marginTop: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statusIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  section: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  amountContainer: {
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  amountValue: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  receivedAmount: {
    color: theme.colors.success,
  },
  sentAmount: {
    color: theme.colors.error,
  },
  fiatAmount: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'right',
    flex: 1,
  },
  hashContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
    justifyContent: 'flex-end',
  },
  hashText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  confirmationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    flex: 1,
    justifyContent: 'flex-end',
  },
  confirmationText: {
    fontSize: 14,
    fontWeight: '500',
  },
  confirmedText: {
    color: theme.colors.success,
  },
  pendingText: {
    color: theme.colors.warning,
  },
  addressContainer: {
    marginBottom: 16,
  },
  addressLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  addressText: {
    fontSize: 14,
    fontFamily: 'monospace',
    color: theme.colors.text,
    flex: 1,
  },
  addressSeparator: {
    alignItems: 'center',
    marginVertical: 8,
  },
  actionButtons: {
    gap: 12,
    marginTop: 16,
    marginBottom: 32,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: theme.colors.surface,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.primary,
  },
});
