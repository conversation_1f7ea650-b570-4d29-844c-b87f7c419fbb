const { ethers } = require('ethers');
const bitcoin = require('bitcoinjs-lib');
const crypto = require('crypto');

/**
 * Wallet utility functions for KryptoPesa
 * These functions handle wallet creation, import, and key management
 */

class WalletUtils {
  
  /**
   * Generate a new mnemonic phrase
   * @returns {string} 12-word mnemonic phrase
   */
  static generateMnemonic() {
    return ethers.utils.entropyToMnemonic(ethers.utils.randomBytes(16));
  }
  
  /**
   * Validate mnemonic phrase
   * @param {string} mnemonic - The mnemonic phrase to validate
   * @returns {boolean} True if valid
   */
  static validateMnemonic(mnemonic) {
    try {
      ethers.utils.mnemonicToSeed(mnemonic);
      return true;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Create Ethereum wallet from mnemonic
   * @param {string} mnemonic - The mnemonic phrase
   * @param {number} accountIndex - Account index (default: 0)
   * @returns {Object} Wallet object with address and private key
   */
  static createEthereumWallet(mnemonic, accountIndex = 0) {
    const path = `m/44'/60'/0'/0/${accountIndex}`;
    const wallet = ethers.Wallet.fromMnemonic(mnemonic, path);
    
    return {
      address: wallet.address,
      privateKey: wallet.privateKey,
      publicKey: wallet.publicKey,
      derivationPath: path
    };
  }
  
  /**
   * Create Bitcoin wallet from mnemonic
   * @param {string} mnemonic - The mnemonic phrase
   * @param {number} accountIndex - Account index (default: 0)
   * @param {string} network - Bitcoin network ('mainnet' or 'testnet')
   * @returns {Object} Wallet object with address and keys
   */
  static createBitcoinWallet(mnemonic, accountIndex = 0, network = 'mainnet') {
    const seed = ethers.utils.mnemonicToSeed(mnemonic);
    const bitcoinNetwork = network === 'mainnet' ? bitcoin.networks.bitcoin : bitcoin.networks.testnet;
    
    // Derive key using BIP44 path for Bitcoin
    const path = `m/44'/0'/0'/0/${accountIndex}`;
    const root = bitcoin.bip32.fromSeed(seed, bitcoinNetwork);
    const child = root.derivePath(path);
    
    // Generate P2WPKH (native segwit) address
    const { address } = bitcoin.payments.p2wpkh({
      pubkey: child.publicKey,
      network: bitcoinNetwork
    });
    
    return {
      address,
      privateKey: child.privateKey.toString('hex'),
      publicKey: child.publicKey.toString('hex'),
      derivationPath: path,
      network: network
    };
  }
  
  /**
   * Encrypt mnemonic phrase for storage
   * @param {string} mnemonic - The mnemonic phrase
   * @param {string} password - User password for encryption
   * @returns {Object} Encrypted data with IV and salt
   */
  static encryptMnemonic(mnemonic, password) {
    const salt = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);
    
    // Derive key from password using PBKDF2
    const key = crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');
    
    // Encrypt mnemonic
    const cipher = crypto.createCipherGCM('aes-256-gcm', key, iv);
    let encrypted = cipher.update(mnemonic, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      salt: salt.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }
  
  /**
   * Decrypt mnemonic phrase
   * @param {Object} encryptedData - Encrypted mnemonic data
   * @param {string} password - User password for decryption
   * @returns {string} Decrypted mnemonic phrase
   */
  static decryptMnemonic(encryptedData, password) {
    const { encrypted, iv, salt, authTag } = encryptedData;
    
    // Derive key from password
    const key = crypto.pbkdf2Sync(password, Buffer.from(salt, 'hex'), 100000, 32, 'sha256');
    
    // Decrypt mnemonic
    const decipher = crypto.createDecipherGCM('aes-256-gcm', key, Buffer.from(iv, 'hex'));
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
  
  /**
   * Create hash of mnemonic for verification (without storing the actual mnemonic)
   * @param {string} mnemonic - The mnemonic phrase
   * @returns {string} SHA256 hash of the mnemonic
   */
  static hashMnemonic(mnemonic) {
    return crypto.createHash('sha256').update(mnemonic).digest('hex');
  }
  
  /**
   * Sign Ethereum transaction
   * @param {string} privateKey - Private key for signing
   * @param {Object} transaction - Transaction object
   * @returns {string} Signed transaction
   */
  static async signEthereumTransaction(privateKey, transaction) {
    const wallet = new ethers.Wallet(privateKey);
    return await wallet.signTransaction(transaction);
  }
  
  /**
   * Sign message with Ethereum private key
   * @param {string} privateKey - Private key for signing
   * @param {string} message - Message to sign
   * @returns {string} Signature
   */
  static signMessage(privateKey, message) {
    const wallet = new ethers.Wallet(privateKey);
    return wallet.signMessage(message);
  }
  
  /**
   * Verify message signature
   * @param {string} message - Original message
   * @param {string} signature - Signature to verify
   * @param {string} address - Expected signer address
   * @returns {boolean} True if signature is valid
   */
  static verifySignature(message, signature, address) {
    try {
      const recoveredAddress = ethers.utils.verifyMessage(message, signature);
      return recoveredAddress.toLowerCase() === address.toLowerCase();
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Generate secure random password for wallet encryption
   * @param {number} length - Password length (default: 32)
   * @returns {string} Random password
   */
  static generateSecurePassword(length = 32) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }
  
  /**
   * Validate Ethereum address
   * @param {string} address - Address to validate
   * @returns {boolean} True if valid
   */
  static isValidEthereumAddress(address) {
    return ethers.utils.isAddress(address);
  }
  
  /**
   * Validate Bitcoin address
   * @param {string} address - Address to validate
   * @param {string} network - Network ('mainnet' or 'testnet')
   * @returns {boolean} True if valid
   */
  static isValidBitcoinAddress(address, network = 'mainnet') {
    try {
      const bitcoinNetwork = network === 'mainnet' ? bitcoin.networks.bitcoin : bitcoin.networks.testnet;
      bitcoin.address.toOutputScript(address, bitcoinNetwork);
      return true;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Format token amount for display
   * @param {string} amount - Amount in wei/satoshi
   * @param {number} decimals - Token decimals
   * @param {number} displayDecimals - Decimals to show
   * @returns {string} Formatted amount
   */
  static formatTokenAmount(amount, decimals = 18, displayDecimals = 4) {
    const formatted = ethers.utils.formatUnits(amount, decimals);
    const number = parseFloat(formatted);
    
    if (number === 0) return '0';
    if (number < 0.0001) return '< 0.0001';
    
    return number.toFixed(displayDecimals).replace(/\.?0+$/, '');
  }
  
  /**
   * Parse token amount from user input
   * @param {string} amount - User input amount
   * @param {number} decimals - Token decimals
   * @returns {string} Amount in wei/smallest unit
   */
  static parseTokenAmount(amount, decimals = 18) {
    return ethers.utils.parseUnits(amount.toString(), decimals).toString();
  }
}

module.exports = WalletUtils;
