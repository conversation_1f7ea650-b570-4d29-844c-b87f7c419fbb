const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const sharp = require('sharp');
const fileType = require('file-type');
const { execSync } = require('child_process');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');
const { getRedisClient } = require('../config/redis');

class FileSecurityService {
  constructor() {
    this.allowedMimeTypes = new Set([
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      'application/pdf'
    ]);

    this.allowedExtensions = new Set([
      '.jpg', '.jpeg', '.png', '.webp', '.gif', '.pdf'
    ]);

    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.maxImageDimensions = { width: 4096, height: 4096 };
    this.quarantineDir = path.join(__dirname, '../../quarantine');
    this.secureUploadDir = path.join(__dirname, '../../uploads/secure');
    
    this.redisClient = getRedisClient();
    this.initializeDirectories();
  }

  async initializeDirectories() {
    try {
      await fs.mkdir(this.quarantineDir, { recursive: true });
      await fs.mkdir(this.secureUploadDir, { recursive: true });
      await fs.mkdir(path.join(this.secureUploadDir, 'payment-proofs'), { recursive: true });
      await fs.mkdir(path.join(this.secureUploadDir, 'chat-attachments'), { recursive: true });
      await fs.mkdir(path.join(this.secureUploadDir, 'documents'), { recursive: true });
    } catch (error) {
      logger.error('Failed to initialize secure directories:', error);
    }
  }

  /**
   * Comprehensive file validation and security scanning
   */
  async validateAndSecureFile(filePath, originalName, userId, context = 'general') {
    try {
      logger.info(`Starting file security validation for ${originalName}`, {
        userId,
        context,
        filePath
      });

      // Step 1: Basic file validation
      const basicValidation = await this.performBasicValidation(filePath, originalName);
      if (!basicValidation.isValid) {
        await this.quarantineFile(filePath, basicValidation.reason, userId);
        throw new AppError(basicValidation.reason, 400);
      }

      // Step 2: File content analysis
      const contentAnalysis = await this.analyzeFileContent(filePath);
      if (!contentAnalysis.isValid) {
        await this.quarantineFile(filePath, contentAnalysis.reason, userId);
        throw new AppError(contentAnalysis.reason, 400);
      }

      // Step 3: Malware scanning
      const malwareScan = await this.scanForMalware(filePath);
      if (!malwareScan.isClean) {
        await this.quarantineFile(filePath, 'Malware detected', userId);
        throw new AppError('File contains malicious content', 400);
      }

      // Step 4: Image-specific validation
      if (contentAnalysis.fileType.mime.startsWith('image/')) {
        const imageValidation = await this.validateImage(filePath);
        if (!imageValidation.isValid) {
          await this.quarantineFile(filePath, imageValidation.reason, userId);
          throw new AppError(imageValidation.reason, 400);
        }
      }

      // Step 5: Generate secure filename and move to secure location
      const secureFile = await this.secureFileStorage(filePath, contentAnalysis.fileType, userId, context);

      // Step 6: Generate file metadata and integrity hash
      const metadata = await this.generateFileMetadata(secureFile.path, originalName, userId, context);

      // Step 7: Store file record in database/cache
      await this.storeFileRecord(secureFile.id, metadata);

      logger.info(`File security validation completed successfully`, {
        userId,
        fileId: secureFile.id,
        originalName,
        secureFilename: secureFile.filename
      });

      return {
        fileId: secureFile.id,
        filename: secureFile.filename,
        path: secureFile.path,
        url: secureFile.url,
        metadata,
        isSecure: true
      };

    } catch (error) {
      logger.error('File security validation failed:', error);
      
      // Clean up original file
      try {
        await fs.unlink(filePath);
      } catch (unlinkError) {
        logger.error('Failed to clean up file after validation failure:', unlinkError);
      }
      
      throw error;
    }
  }

  /**
   * Basic file validation (size, extension, MIME type)
   */
  async performBasicValidation(filePath, originalName) {
    try {
      const stats = await fs.stat(filePath);
      
      // Check file size
      if (stats.size > this.maxFileSize) {
        return {
          isValid: false,
          reason: `File size exceeds maximum limit of ${this.maxFileSize / (1024 * 1024)}MB`
        };
      }

      if (stats.size === 0) {
        return {
          isValid: false,
          reason: 'File is empty'
        };
      }

      // Check file extension
      const ext = path.extname(originalName).toLowerCase();
      if (!this.allowedExtensions.has(ext)) {
        return {
          isValid: false,
          reason: `File extension ${ext} is not allowed`
        };
      }

      // Check for dangerous filenames
      if (this.isDangerousFilename(originalName)) {
        return {
          isValid: false,
          reason: 'Filename contains dangerous characters or patterns'
        };
      }

      return { isValid: true };
    } catch (error) {
      logger.error('Basic file validation failed:', error);
      return {
        isValid: false,
        reason: 'File validation error'
      };
    }
  }

  /**
   * Analyze file content and verify file type
   */
  async analyzeFileContent(filePath) {
    try {
      const buffer = await fs.readFile(filePath);
      const detectedType = await fileType.fromBuffer(buffer);

      if (!detectedType) {
        return {
          isValid: false,
          reason: 'Unable to determine file type'
        };
      }

      // Verify MIME type is allowed
      if (!this.allowedMimeTypes.has(detectedType.mime)) {
        return {
          isValid: false,
          reason: `File type ${detectedType.mime} is not allowed`
        };
      }

      // Check for embedded executables or scripts
      if (this.containsExecutableContent(buffer)) {
        return {
          isValid: false,
          reason: 'File contains executable content'
        };
      }

      return {
        isValid: true,
        fileType: detectedType,
        size: buffer.length
      };
    } catch (error) {
      logger.error('File content analysis failed:', error);
      return {
        isValid: false,
        reason: 'File content analysis failed'
      };
    }
  }

  /**
   * Scan file for malware using ClamAV (if available)
   */
  async scanForMalware(filePath) {
    try {
      // Check if ClamAV is available
      try {
        execSync('which clamscan', { stdio: 'ignore' });
      } catch (error) {
        logger.warn('ClamAV not available, skipping malware scan');
        return { isClean: true, scanner: 'none' };
      }

      // Run ClamAV scan
      const result = execSync(`clamscan --no-summary "${filePath}"`, { 
        encoding: 'utf8',
        timeout: 30000 // 30 second timeout
      });

      const isClean = result.includes('OK') && !result.includes('FOUND');
      
      logger.info(`Malware scan completed`, {
        filePath,
        isClean,
        result: result.trim()
      });

      return {
        isClean,
        scanner: 'clamav',
        result: result.trim()
      };
    } catch (error) {
      logger.error('Malware scan failed:', error);
      
      // If scan fails, quarantine the file for safety
      return {
        isClean: false,
        scanner: 'clamav',
        error: error.message
      };
    }
  }

  /**
   * Validate image files for malicious content
   */
  async validateImage(filePath) {
    try {
      const image = sharp(filePath);
      const metadata = await image.metadata();

      // Check image dimensions
      if (metadata.width > this.maxImageDimensions.width || 
          metadata.height > this.maxImageDimensions.height) {
        return {
          isValid: false,
          reason: `Image dimensions exceed maximum allowed (${this.maxImageDimensions.width}x${this.maxImageDimensions.height})`
        };
      }

      // Check for suspicious metadata
      if (this.hasSuspiciousImageMetadata(metadata)) {
        return {
          isValid: false,
          reason: 'Image contains suspicious metadata'
        };
      }

      // Strip metadata and re-encode for security
      await image
        .jpeg({ quality: 90, mozjpeg: true })
        .toFile(filePath + '.clean');

      // Replace original with cleaned version
      await fs.rename(filePath + '.clean', filePath);

      return {
        isValid: true,
        metadata: {
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          channels: metadata.channels
        }
      };
    } catch (error) {
      logger.error('Image validation failed:', error);
      return {
        isValid: false,
        reason: 'Image validation failed'
      };
    }
  }

  /**
   * Move file to secure storage with encrypted filename
   */
  async secureFileStorage(filePath, fileType, userId, context) {
    try {
      const fileId = crypto.randomUUID();
      const timestamp = Date.now();
      const extension = fileType.ext;
      
      // Generate secure filename
      const secureFilename = `${context}_${timestamp}_${crypto.randomBytes(16).toString('hex')}.${extension}`;
      const securePath = path.join(this.secureUploadDir, context, secureFilename);

      // Ensure directory exists
      await fs.mkdir(path.dirname(securePath), { recursive: true });

      // Move file to secure location
      await fs.rename(filePath, securePath);

      // Set restrictive permissions
      await fs.chmod(securePath, 0o600);

      return {
        id: fileId,
        filename: secureFilename,
        path: securePath,
        url: `/api/files/${fileId}`,
        context
      };
    } catch (error) {
      logger.error('Secure file storage failed:', error);
      throw new AppError('File storage failed', 500);
    }
  }

  /**
   * Generate comprehensive file metadata
   */
  async generateFileMetadata(filePath, originalName, userId, context) {
    try {
      const stats = await fs.stat(filePath);
      const buffer = await fs.readFile(filePath);
      
      // Generate integrity hashes
      const sha256Hash = crypto.createHash('sha256').update(buffer).digest('hex');
      const md5Hash = crypto.createHash('md5').update(buffer).digest('hex');

      return {
        originalName: this.sanitizeFilename(originalName),
        size: stats.size,
        uploadedAt: new Date(),
        uploadedBy: userId,
        context,
        integrity: {
          sha256: sha256Hash,
          md5: md5Hash
        },
        permissions: {
          owner: userId,
          access: 'private'
        },
        security: {
          scanned: true,
          scanDate: new Date(),
          quarantined: false
        }
      };
    } catch (error) {
      logger.error('Metadata generation failed:', error);
      throw new AppError('Metadata generation failed', 500);
    }
  }

  /**
   * Store file record in Redis for fast access
   */
  async storeFileRecord(fileId, metadata) {
    try {
      if (this.redisClient && this.redisClient.isReady) {
        const fileRecord = {
          id: fileId,
          ...metadata,
          createdAt: new Date().toISOString()
        };

        // Store with 30-day expiration
        await this.redisClient.setEx(
          `file:${fileId}`,
          30 * 24 * 60 * 60, // 30 days
          JSON.stringify(fileRecord)
        );

        // Store user file index
        await this.redisClient.sAdd(`user_files:${metadata.uploadedBy}`, fileId);
      }
    } catch (error) {
      logger.error('Failed to store file record:', error);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Quarantine suspicious files
   */
  async quarantineFile(filePath, reason, userId) {
    try {
      const quarantineId = crypto.randomUUID();
      const quarantinePath = path.join(this.quarantineDir, `quarantine_${quarantineId}`);
      
      await fs.rename(filePath, quarantinePath);
      
      logger.warn('File quarantined', {
        quarantineId,
        reason,
        userId,
        originalPath: filePath,
        quarantinePath
      });

      // Store quarantine record
      if (this.redisClient && this.redisClient.isReady) {
        await this.redisClient.setEx(
          `quarantine:${quarantineId}`,
          7 * 24 * 60 * 60, // 7 days
          JSON.stringify({
            id: quarantineId,
            reason,
            userId,
            originalPath: filePath,
            quarantinedAt: new Date().toISOString()
          })
        );
      }
    } catch (error) {
      logger.error('File quarantine failed:', error);
    }
  }

  /**
   * Utility methods
   */
  isDangerousFilename(filename) {
    const dangerousPatterns = [
      /\.\./,  // Directory traversal
      /[<>:"|?*]/,  // Windows reserved characters
      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Windows reserved names
      /\.(exe|bat|cmd|scr|pif|com|vbs|js|jar|app|deb|rpm)$/i,  // Executable extensions
      /^\./,  // Hidden files
      /\s+$/,  // Trailing whitespace
    ];

    return dangerousPatterns.some(pattern => pattern.test(filename));
  }

  containsExecutableContent(buffer) {
    // Check for common executable signatures
    const executableSignatures = [
      Buffer.from([0x4D, 0x5A]), // PE executable (MZ)
      Buffer.from([0x7F, 0x45, 0x4C, 0x46]), // ELF executable
      Buffer.from([0xFE, 0xED, 0xFA, 0xCE]), // Mach-O executable
      Buffer.from([0xFE, 0xED, 0xFA, 0xCF]), // Mach-O 64-bit
      Buffer.from([0xCA, 0xFE, 0xBA, 0xBE]), // Java class file
    ];

    return executableSignatures.some(signature => 
      buffer.subarray(0, signature.length).equals(signature)
    );
  }

  hasSuspiciousImageMetadata(metadata) {
    // Check for suspicious EXIF data or metadata
    if (metadata.exif && metadata.exif.length > 65536) { // 64KB limit for EXIF
      return true;
    }

    // Check for suspicious dimensions
    if (metadata.width * metadata.height > 100000000) { // 100MP limit
      return true;
    }

    return false;
  }

  sanitizeFilename(filename) {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255);
  }
}

module.exports = new FileSecurityService();
