import { apiClient, API_ENDPOINTS } from './api';

// Settings interfaces
export interface UserSettings {
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
    trading: boolean;
    security: boolean;
  };
  privacy: {
    showOnlineStatus: boolean;
    showTradeHistory: boolean;
    allowDirectMessages: boolean;
  };
  trading: {
    autoAcceptOffers: boolean;
    maxTradeAmount: number;
    preferredPaymentMethods: string[];
    requireVerification: boolean;
  };
  display: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    currency: string;
    timezone: string;
  };
  security: {
    twoFactorEnabled: boolean;
    sessionTimeout: number;
    loginNotifications: boolean;
  };
}

// Settings Service Class
class SettingsService {
  /**
   * Get user settings
   */
  async getUserSettings(): Promise<UserSettings> {
    try {
      const response = await apiClient.get<UserSettings>(
        API_ENDPOINTS.USERS.SETTINGS || '/users/settings'
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get user settings');
      }
    } catch (error) {
      console.error('Get user settings error:', error);

      // Return mock settings data when API fails
      const mockSettings: UserSettings = {
        notifications: {
          email: true,
          push: true,
          sms: false,
          trading: true,
          security: true,
        },
        privacy: {
          showOnlineStatus: true,
          showTradeHistory: false,
          allowDirectMessages: true,
        },
        trading: {
          autoAcceptOffers: false,
          maxTradeAmount: 50000,
          preferredPaymentMethods: ['M-Pesa', 'Bank Transfer'],
          requireVerification: true,
        },
        display: {
          theme: 'light',
          language: 'en',
          currency: 'KES',
          timezone: 'Africa/Nairobi',
        },
        security: {
          twoFactorEnabled: false,
          sessionTimeout: 30,
          loginNotifications: true,
        },
      };

      console.log('Using mock settings data');
      return mockSettings;
    }
  }

  /**
   * Update user settings
   */
  async updateSettings(settings: Partial<UserSettings>): Promise<void> {
    try {
      const response = await apiClient.put(
        API_ENDPOINTS.USERS.SETTINGS || '/users/settings',
        settings
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to update settings');
      }
    } catch (error) {
      console.error('Update settings error:', error);
      
      // For mock mode, just log the update
      console.log('Mock settings update:', settings);
    }
  }

  /**
   * Reset settings to default
   */
  async resetSettings(): Promise<UserSettings> {
    try {
      const response = await apiClient.post<UserSettings>(
        API_ENDPOINTS.USERS.RESET_SETTINGS || '/users/settings/reset'
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to reset settings');
      }
    } catch (error) {
      console.error('Reset settings error:', error);
      
      // Return default settings
      return this.getUserSettings();
    }
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(notifications: Partial<UserSettings['notifications']>): Promise<void> {
    try {
      const currentSettings = await this.getUserSettings();
      const updatedSettings = {
        ...currentSettings,
        notifications: {
          ...currentSettings.notifications,
          ...notifications,
        },
      };

      await this.updateSettings(updatedSettings);
    } catch (error) {
      console.error('Update notification preferences error:', error);
      throw error;
    }
  }

  /**
   * Update privacy settings
   */
  async updatePrivacySettings(privacy: Partial<UserSettings['privacy']>): Promise<void> {
    try {
      const currentSettings = await this.getUserSettings();
      const updatedSettings = {
        ...currentSettings,
        privacy: {
          ...currentSettings.privacy,
          ...privacy,
        },
      };

      await this.updateSettings(updatedSettings);
    } catch (error) {
      console.error('Update privacy settings error:', error);
      throw error;
    }
  }

  /**
   * Update trading preferences
   */
  async updateTradingPreferences(trading: Partial<UserSettings['trading']>): Promise<void> {
    try {
      const currentSettings = await this.getUserSettings();
      const updatedSettings = {
        ...currentSettings,
        trading: {
          ...currentSettings.trading,
          ...trading,
        },
      };

      await this.updateSettings(updatedSettings);
    } catch (error) {
      console.error('Update trading preferences error:', error);
      throw error;
    }
  }

  /**
   * Update display preferences
   */
  async updateDisplayPreferences(display: Partial<UserSettings['display']>): Promise<void> {
    try {
      const currentSettings = await this.getUserSettings();
      const updatedSettings = {
        ...currentSettings,
        display: {
          ...currentSettings.display,
          ...display,
        },
      };

      await this.updateSettings(updatedSettings);
    } catch (error) {
      console.error('Update display preferences error:', error);
      throw error;
    }
  }

  /**
   * Update security settings
   */
  async updateSecuritySettings(security: Partial<UserSettings['security']>): Promise<void> {
    try {
      const currentSettings = await this.getUserSettings();
      const updatedSettings = {
        ...currentSettings,
        security: {
          ...currentSettings.security,
          ...security,
        },
      };

      await this.updateSettings(updatedSettings);
    } catch (error) {
      console.error('Update security settings error:', error);
      throw error;
    }
  }
}

// Create and export service instance
export const settingsService = new SettingsService();
