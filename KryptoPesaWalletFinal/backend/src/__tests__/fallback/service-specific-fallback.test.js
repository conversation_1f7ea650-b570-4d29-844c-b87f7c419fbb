/**
 * Service-Specific Fallback Testing
 * Tests fallback mechanisms for individual services
 */

// Set up test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long-for-testing';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-chars-long-for-testing';
process.env.SESSION_SECRET = 'test-session-secret-32-chars-long-for-testing';
process.env.MONGODB_URI = 'mongodb://localhost:27017/kryptopesa_service_fallback_test';
process.env.REDIS_URL = 'redis://localhost:6379';

const testEnvironment = require('../testEnvironment');
const { circuitBreakers } = require('../../utils/circuitBreaker');
const gracefulDegradationService = require('../../services/gracefulDegradation');

describe('Service-Specific Fallback Testing', () => {
  beforeAll(async () => {
    await testEnvironment.setup();
  }, 60000);

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset circuit breakers
    Object.values(circuitBreakers).forEach(cb => {
      if (cb.reset) cb.reset();
    });
    
    // Reset graceful degradation service
    if (gracefulDegradationService.serviceStatus) {
      gracefulDegradationService.serviceStatus = {
        redis: 'healthy',
        blockchain: 'healthy',
        database: 'healthy',
        external: 'healthy'
      };
      gracefulDegradationService.degradationLevel = 'none';
    }
  });

  afterAll(async () => {
    await testEnvironment.teardown();
  });

  describe('Redis Service Fallback', () => {
    test('should fallback to memory cache when Redis is unavailable', async () => {
      const redisConfig = require('../../config/redis');
      
      // Mock Redis failure
      const mockRedisClient = {
        isReady: false,
        isConnected: false,
        get: jest.fn().mockRejectedValue(new Error('Redis unavailable')),
        setEx: jest.fn().mockRejectedValue(new Error('Redis unavailable')),
        del: jest.fn().mockRejectedValue(new Error('Redis unavailable'))
      };

      // Test cache operations fallback to memory
      const cacheKey = 'test:fallback:key';
      const cacheValue = { data: 'test data', timestamp: Date.now() };

      // Should fallback to memory cache
      const setResult = await redisConfig.setCache(cacheKey, cacheValue, 3600);
      expect(setResult).toBe(true); // Should succeed with memory fallback

      const getValue = await redisConfig.getCache(cacheKey);
      expect(getValue).toEqual(cacheValue);
    });

    test('should handle rate limiting fallback when Redis fails', async () => {
      const rateLimitService = require('../../middleware/advancedRateLimit');
      
      // Mock Redis failure for rate limiting
      const mockRedisClient = {
        isReady: false,
        incr: jest.fn().mockRejectedValue(new Error('Redis rate limit failed')),
        expire: jest.fn().mockRejectedValue(new Error('Redis rate limit failed'))
      };

      // Rate limiting should fallback to allowing requests
      const req = { ip: '127.0.0.1', user: { _id: 'test-user-id' } };
      const res = { status: jest.fn().mockReturnThis(), json: jest.fn() };
      const next = jest.fn();

      // Should not block request when Redis fails
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalledWith(429);
    });

    test('should handle session storage fallback', async () => {
      const authService = require('../../services/authService');
      
      // Mock Redis session storage failure
      const mockRedisClient = {
        isReady: false,
        setEx: jest.fn().mockRejectedValue(new Error('Redis session storage failed')),
        get: jest.fn().mockRejectedValue(new Error('Redis session storage failed'))
      };

      // Should still be able to create tokens without Redis session storage
      const tokens = await authService.generateTokens('test-user-id');
      expect(tokens.accessToken).toBeDefined();
      expect(tokens.refreshToken).toBeDefined();
    });
  });

  describe('Database Service Fallback', () => {
    test('should handle database connection timeouts gracefully', async () => {
      const mongoose = require('mongoose');
      const User = require('../../models/User');
      
      // Mock database timeout
      const originalFind = User.find;
      User.find = jest.fn().mockImplementation(() => {
        return {
          exec: jest.fn().mockRejectedValue(new Error('Database timeout'))
        };
      });

      try {
        // Should handle timeout gracefully
        await circuitBreakers.database.execute(async () => {
          return await User.find({}).exec();
        });
      } catch (error) {
        expect(error.message).toContain('Database timeout');
      }

      // Restore original method
      User.find = originalFind;
    });

    test('should handle database write failures with retry', async () => {
      const User = require('../../models/User');
      
      // Mock database write failure
      const originalSave = User.prototype.save;
      let attemptCount = 0;
      
      User.prototype.save = jest.fn().mockImplementation(function() {
        attemptCount++;
        if (attemptCount < 3) {
          return Promise.reject(new Error('Database write failed'));
        }
        return Promise.resolve(this);
      });

      const user = new User({
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123'
      });

      // Should retry and eventually succeed
      const result = await circuitBreakers.database.execute(async () => {
        return await user.save();
      });

      expect(result).toBeDefined();
      expect(attemptCount).toBe(3);

      // Restore original method
      User.prototype.save = originalSave;
    });
  });

  describe('Blockchain Service Fallback', () => {
    test('should handle RPC endpoint failures with retry', async () => {
      const ethereumService = require('../../services/blockchain/ethereumService');
      
      // Mock RPC failure
      const mockProvider = {
        getBalance: jest.fn()
          .mockRejectedValueOnce(new Error('RPC endpoint 1 failed'))
          .mockRejectedValueOnce(new Error('RPC endpoint 2 failed'))
          .mockResolvedValueOnce('1000000000000000000') // 1 ETH
      };

      // Should retry with different endpoints and succeed
      const balance = await ethereumService.executeWithRetry(async () => {
        return await mockProvider.getBalance('******************************************');
      });

      expect(balance).toBe('1000000000000000000');
      expect(mockProvider.getBalance).toHaveBeenCalledTimes(3);
    });

    test('should handle smart contract interaction failures', async () => {
      const ethereumService = require('../../services/blockchain/ethereumService');
      
      // Mock smart contract failure
      const mockContract = {
        createTrade: jest.fn()
          .mockRejectedValueOnce(new Error('Gas estimation failed'))
          .mockRejectedValueOnce(new Error('Network congestion'))
          .mockResolvedValueOnce({ hash: '0xabc123', wait: jest.fn().mockResolvedValue({ status: 1 }) })
      };

      // Should retry and eventually succeed
      const result = await ethereumService.executeWithRetry(async () => {
        return await mockContract.createTrade(
          '******************************************',
          '******************************************',
          '1000000000000000000',
          '45000',
          'USD',
          'M-Pesa',
          '0x123'
        );
      });

      expect(result.hash).toBe('0xabc123');
      expect(mockContract.createTrade).toHaveBeenCalledTimes(3);
    });

    test('should queue transactions when blockchain is unavailable', async () => {
      const transactionQueue = require('../../services/transactionQueue');
      
      // Mock blockchain unavailability
      const mockBlockchainService = {
        isConnected: jest.fn().mockReturnValue(false),
        sendTransaction: jest.fn().mockRejectedValue(new Error('Blockchain unavailable'))
      };

      // Should queue transaction for later processing
      const queueResult = await transactionQueue.queueTransaction({
        to: '******************************************',
        amount: '0.001',
        currency: 'ETH',
        userId: 'test-user-id'
      });

      expect(queueResult.queued).toBe(true);
      expect(queueResult.queueId).toBeDefined();
      expect(queueResult.estimatedProcessingTime).toBeDefined();
    });
  });

  describe('External Service Fallback', () => {
    test('should handle notification service failures gracefully', async () => {
      const notificationService = require('../../services/notificationService');
      
      // Mock Firebase failure
      const mockFirebase = {
        messaging: jest.fn(() => ({
          send: jest.fn().mockRejectedValue(new Error('Firebase unavailable'))
        }))
      };

      // Should fallback to database storage for later retry
      const result = await notificationService.sendNotification({
        userId: 'test-user-id',
        title: 'Test Notification',
        body: 'Test message',
        type: 'trade_update'
      });

      expect(result.queued).toBe(true);
      expect(result.fallback).toBe('database');
    });

    test('should handle file upload service failures', async () => {
      const fileUploadService = require('../../services/fileUploadService');
      
      // Mock Cloudinary failure
      const mockCloudinary = {
        uploader: {
          upload: jest.fn().mockRejectedValue(new Error('Cloudinary unavailable'))
        }
      };

      // Should fallback to local storage
      const result = await fileUploadService.uploadFile({
        buffer: Buffer.from('test file content'),
        originalname: 'test.jpg',
        mimetype: 'image/jpeg'
      });

      expect(result.fallback).toBe('local');
      expect(result.url).toContain('localhost');
    });

    test('should handle price feed service failures', async () => {
      const priceService = require('../../services/priceService');
      
      // Mock external price API failure
      const mockPriceAPI = {
        getCurrentPrice: jest.fn().mockRejectedValue(new Error('Price API unavailable'))
      };

      // Should fallback to cached prices
      const price = await priceService.getPrice('BTC', 'USD');
      
      expect(price).toBeDefined();
      expect(price.source).toBe('cache');
      expect(price.stale).toBe(true);
    });
  });

  describe('WebSocket Service Fallback', () => {
    test('should handle WebSocket server failures', async () => {
      const socketService = require('../../services/socketService');
      
      // Mock WebSocket server failure
      const mockIO = {
        emit: jest.fn().mockImplementation(() => {
          throw new Error('WebSocket server unavailable');
        }),
        to: jest.fn().mockReturnThis()
      };

      // Should fallback to database notifications
      const result = await socketService.broadcastToUser('test-user-id', 'trade_update', {
        tradeId: 'test-trade-id',
        status: 'completed'
      });

      expect(result.fallback).toBe('database');
      expect(result.queued).toBe(true);
    });

    test('should handle Redis pub/sub failures in real-time features', async () => {
      const realtimeService = require('../../services/realtimeService');
      
      // Mock Redis pub/sub failure
      const mockRedisClient = {
        publish: jest.fn().mockRejectedValue(new Error('Redis pub/sub failed')),
        subscribe: jest.fn().mockRejectedValue(new Error('Redis pub/sub failed'))
      };

      // Should fallback to direct WebSocket communication
      const result = await realtimeService.publishUpdate('trade:123', {
        type: 'status_update',
        data: { status: 'payment_sent' }
      });

      expect(result.fallback).toBe('direct');
      expect(result.delivered).toBe(true);
    });
  });
});
