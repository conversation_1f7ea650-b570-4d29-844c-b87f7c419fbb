const Wallet = require('../models/Wallet');
const { ethers } = require('ethers');
const ethereumService = require('./blockchain/ethereumService');
const bitcoinService = require('./blockchain/bitcoinService');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');
const crypto = require('crypto');
const bip39 = require('bip39');
const { getRedisClient } = require('../config/redis');
const AuditLog = require('../models/AuditLog');

class WalletService {
  constructor() {
    this.redisClient = null;
    this.balanceUpdateThrottling = {
      // Minimum time between balance updates per user (in milliseconds)
      minInterval: parseInt(process.env.BALANCE_UPDATE_MIN_INTERVAL) || 30000, // 30 seconds
      // Maximum balance updates per user per hour
      maxUpdatesPerHour: parseInt(process.env.BALANCE_UPDATE_MAX_PER_HOUR) || 120,
      // Cache duration for balance data (in seconds)
      cacheDuration: parseInt(process.env.BALANCE_CACHE_DURATION) || 300 // 5 minutes
    };
    this.mnemonicSecurity = {
      // Maximum time mnemonic can be in memory (in milliseconds)
      maxMemoryTime: parseInt(process.env.MNEMONIC_MAX_MEMORY_TIME) || 60000, // 1 minute
      // Enable secure memory clearing
      secureMemoryClearing: process.env.MNEMONIC_SECURE_MEMORY === 'true',
      // Audit all mnemonic operations
      auditMnemonicAccess: process.env.MNEMONIC_AUDIT_ACCESS !== 'false'
    };
    this.initializeRedis();
  }

  /**
   * Initialize Redis connection for throttling and caching
   */
  async initializeRedis() {
    try {
      this.redisClient = await getRedisClient();
      logger.info('✅ WalletService Redis connection initialized');
    } catch (error) {
      logger.warn('⚠️ WalletService Redis connection failed, throttling will use fallback:', error.message);
    }
  }

  /**
   * Audit mnemonic access for security monitoring
   */
  async auditMnemonicAccess(userId, action, details = {}) {
    if (!this.mnemonicSecurity.auditMnemonicAccess) return;

    try {
      await AuditLog.create({
        userId,
        action: `mnemonic_${action}`,
        resource: 'wallet_mnemonic',
        resourceId: userId,
        method: 'INTERNAL',
        endpoint: 'wallet_service',
        statusCode: 200,
        duration: 0,
        ip: details.ip || 'internal',
        userAgent: details.userAgent || 'wallet_service',
        requestData: {
          action,
          securityLevel: 'high',
          ...details
        },
        timestamp: new Date()
      });

      logger.info(`🔐 Mnemonic access audited: ${action} for user ${userId}`);
    } catch (error) {
      logger.error('Failed to audit mnemonic access:', error);
    }
  }

  /**
   * Secure mnemonic memory management
   */
  secureMemoryClear(mnemonicBuffer) {
    if (!this.mnemonicSecurity.secureMemoryClearing) return;

    try {
      // Overwrite memory with random data multiple times
      for (let i = 0; i < 3; i++) {
        if (Buffer.isBuffer(mnemonicBuffer)) {
          crypto.randomFillSync(mnemonicBuffer);
        } else if (typeof mnemonicBuffer === 'string') {
          // For strings, we can't directly overwrite memory, but we can clear the reference
          mnemonicBuffer = null;
        }
      }
      logger.debug('🧹 Secure memory clearing completed');
    } catch (error) {
      logger.error('Secure memory clearing failed:', error);
    }
  }

  /**
   * Check balance update throttling
   */
  async checkBalanceUpdateThrottling(userId) {
    if (!this.redisClient || !this.redisClient.isReady) {
      // Fallback: allow update but log warning
      logger.warn('Redis unavailable for throttling, allowing balance update');
      return { allowed: true, reason: 'redis_unavailable' };
    }

    try {
      const now = Date.now();
      const hourKey = `balance_updates:${userId}:${Math.floor(now / 3600000)}`;
      const lastUpdateKey = `last_balance_update:${userId}`;

      // Check last update time
      const lastUpdate = await this.redisClient.get(lastUpdateKey);
      if (lastUpdate && (now - parseInt(lastUpdate)) < this.balanceUpdateThrottling.minInterval) {
        return {
          allowed: false,
          reason: 'too_frequent',
          nextAllowedTime: parseInt(lastUpdate) + this.balanceUpdateThrottling.minInterval,
          waitTime: (parseInt(lastUpdate) + this.balanceUpdateThrottling.minInterval) - now
        };
      }

      // Check hourly limit
      const hourlyCount = await this.redisClient.get(hourKey);
      if (hourlyCount && parseInt(hourlyCount) >= this.balanceUpdateThrottling.maxUpdatesPerHour) {
        return {
          allowed: false,
          reason: 'hourly_limit_exceeded',
          limit: this.balanceUpdateThrottling.maxUpdatesPerHour,
          resetTime: (Math.floor(now / 3600000) + 1) * 3600000
        };
      }

      return { allowed: true };
    } catch (error) {
      logger.error('Balance update throttling check failed:', error);
      return { allowed: true, reason: 'throttling_error' };
    }
  }

  /**
   * Record balance update for throttling
   */
  async recordBalanceUpdate(userId) {
    if (!this.redisClient || !this.redisClient.isReady) return;

    try {
      const now = Date.now();
      const hourKey = `balance_updates:${userId}:${Math.floor(now / 3600000)}`;
      const lastUpdateKey = `last_balance_update:${userId}`;

      // Update counters
      await this.redisClient.pipeline()
        .incr(hourKey)
        .expire(hourKey, 3600) // Expire after 1 hour
        .set(lastUpdateKey, now.toString())
        .expire(lastUpdateKey, this.balanceUpdateThrottling.minInterval / 1000)
        .exec();

      logger.debug(`📊 Balance update recorded for user ${userId}`);
    } catch (error) {
      logger.error('Failed to record balance update:', error);
    }
  }

  /**
   * Create a new wallet for user
   */
  async createWallet(userId, mnemonic = null, auditContext = {}) {
    let mnemonicBuffer = null;
    let secureCleanupTimer = null;

    try {
      // Audit wallet creation attempt
      await this.auditMnemonicAccess(userId, 'wallet_creation_attempt', {
        hasProvidedMnemonic: !!mnemonic,
        ...auditContext
      });

      // Check if user already has a wallet
      const existingWallet = await Wallet.findOne({ user: userId });
      if (existingWallet) {
        throw new AppError('User already has a wallet', 400);
      }

      // Generate mnemonic if not provided
      if (!mnemonic) {
        mnemonic = bip39.generateMnemonic(256); // 24-word mnemonic for better security
        await this.auditMnemonicAccess(userId, 'mnemonic_generation', auditContext);
      } else {
        // Validate provided mnemonic
        if (!bip39.validateMnemonic(mnemonic)) {
          await this.auditMnemonicAccess(userId, 'mnemonic_validation_failed', auditContext);
          throw new AppError('Invalid mnemonic phrase', 400);
        }
        await this.auditMnemonicAccess(userId, 'mnemonic_import', auditContext);
      }

      // Convert mnemonic to buffer for secure handling
      mnemonicBuffer = Buffer.from(mnemonic, 'utf8');

      // Set up secure cleanup timer
      secureCleanupTimer = setTimeout(() => {
        this.secureMemoryClear(mnemonicBuffer);
        mnemonic = null;
        logger.debug('🔐 Mnemonic automatically cleared from memory');
      }, this.mnemonicSecurity.maxMemoryTime);

      // Create Ethereum wallet
      const ethWallet = ethers.Wallet.fromMnemonic(mnemonic);

      // Create Bitcoin wallet using proper derivation
      const bitcoin = require('bitcoinjs-lib');
      const BIP32Factory = require('bip32').default;
      const ecc = require('tiny-secp256k1');
      const bip32 = BIP32Factory(ecc);

      const seed = bip39.mnemonicToSeedSync(mnemonic);
      const network = process.env.NODE_ENV === 'production' ? bitcoin.networks.bitcoin : bitcoin.networks.testnet;
      const root = bip32.fromSeed(seed, network);
      const derivationPath = process.env.NODE_ENV === 'production' ? "m/44'/0'/0'/0/0" : "m/44'/1'/0'/0/0";
      const child = root.derivePath(derivationPath);

      const { address } = bitcoin.payments.p2wpkh({
        pubkey: Buffer.from(child.publicKey),
        network
      });

      const btcWallet = {
        address,
        publicKey: child.publicKey.toString('hex'),
        derivationPath
      };

      // Hash mnemonic for verification (don't store the actual mnemonic)
      const mnemonicHash = crypto.createHash('sha256').update(mnemonic).digest('hex');
      
      // Create wallet document
      const wallet = new Wallet({
        user: userId,
        addresses: {
          ethereum: {
            address: ethWallet.address,
            publicKey: ethWallet.publicKey,
            derivationPath: ethWallet.derivationPath
          },
          bitcoin: {
            address: btcWallet.address,
            publicKey: btcWallet.publicKey,
            derivationPath: btcWallet.derivationPath
          }
        },
        security: {
          mnemonicHash
        },
        balances: [
          // Initialize with zero balances for common tokens
          { symbol: 'MATIC', network: 'polygon', balance: '0', decimals: 18 },
          { symbol: 'USDT', network: 'polygon', balance: '0', decimals: 6, contractAddress: process.env.USDT_CONTRACT_ADDRESS },
          { symbol: 'USDC', network: 'polygon', balance: '0', decimals: 6, contractAddress: process.env.USDC_CONTRACT_ADDRESS },
          { symbol: 'ETH', network: 'ethereum', balance: '0', decimals: 18 },
          { symbol: 'BTC', network: 'bitcoin', balance: '0', decimals: 8 }
        ]
      });
      
      await wallet.save();

      // Audit successful wallet creation
      await this.auditMnemonicAccess(userId, 'wallet_creation_success', {
        ethereumAddress: ethWallet.address,
        bitcoinAddress: btcWallet.address,
        ...auditContext
      });

      logger.info(`Wallet created for user ${userId}: ETH ${ethWallet.address}, BTC ${btcWallet.address}`);

      // Prepare secure response
      const response = {
        wallet,
        mnemonic, // Return mnemonic only once during creation - WARNING: Handle securely!
        addresses: {
          ethereum: ethWallet.address,
          bitcoin: btcWallet.address
        },
        securityWarning: 'CRITICAL: Store mnemonic securely and delete from memory immediately after use'
      };

      // Schedule immediate cleanup after response
      setTimeout(() => {
        this.secureMemoryClear(mnemonicBuffer);
        if (secureCleanupTimer) clearTimeout(secureCleanupTimer);
        mnemonic = null;
        logger.debug('🔐 Mnemonic cleared from memory after wallet creation');
      }, 1000); // 1 second delay to allow response processing

      return response;

    } catch (error) {
      // Audit failed wallet creation
      await this.auditMnemonicAccess(userId, 'wallet_creation_failed', {
        error: error.message,
        ...auditContext
      });

      // Ensure cleanup on error
      if (mnemonicBuffer) {
        this.secureMemoryClear(mnemonicBuffer);
      }
      if (secureCleanupTimer) {
        clearTimeout(secureCleanupTimer);
      }
      mnemonic = null;

      logger.error(`Error creating wallet: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Import existing wallet from mnemonic
   */
  async importWallet(userId, mnemonic, auditContext = {}) {
    try {
      // Audit import attempt
      await this.auditMnemonicAccess(userId, 'wallet_import_attempt', auditContext);

      // Validate mnemonic
      if (!ethers.utils.isValidMnemonic(mnemonic)) {
        await this.auditMnemonicAccess(userId, 'wallet_import_validation_failed', auditContext);
        throw new AppError('Invalid mnemonic phrase', 400);
      }

      // Check if user already has a wallet
      const existingWallet = await Wallet.findOne({ user: userId });
      if (existingWallet) {
        await this.auditMnemonicAccess(userId, 'wallet_import_duplicate_attempt', auditContext);
        throw new AppError('User already has a wallet', 400);
      }

      // Use enhanced createWallet with audit context
      const result = await this.createWallet(userId, mnemonic, {
        ...auditContext,
        operation: 'import'
      });

      await this.auditMnemonicAccess(userId, 'wallet_import_success', {
        ethereumAddress: result.addresses.ethereum,
        bitcoinAddress: result.addresses.bitcoin,
        ...auditContext
      });

      return result;

    } catch (error) {
      await this.auditMnemonicAccess(userId, 'wallet_import_failed', {
        error: error.message,
        ...auditContext
      });
      logger.error(`Error importing wallet: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get wallet for user
   */
  async getWallet(userId) {
    try {
      const wallet = await Wallet.findOne({ user: userId }).populate('user', 'username email');
      
      if (!wallet) {
        throw new AppError('Wallet not found', 404);
      }
      
      return wallet;
      
    } catch (error) {
      logger.error(`Error getting wallet: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Update wallet balances (using atomic operations with throttling)
   */
  async updateBalances(userId, forceUpdate = false) {
    try {
      // Check throttling unless force update is requested
      if (!forceUpdate) {
        const throttleCheck = await this.checkBalanceUpdateThrottling(userId);
        if (!throttleCheck.allowed) {
          logger.info(`Balance update throttled for user ${userId}: ${throttleCheck.reason}`);

          // Return cached data if available
          const cachedBalances = await this.getCachedBalances(userId);
          if (cachedBalances) {
            return cachedBalances;
          }

          // If no cached data and throttled, throw informative error
          const errorMessage = throttleCheck.reason === 'too_frequent'
            ? `Balance updates are limited. Please wait ${Math.ceil(throttleCheck.waitTime / 1000)} seconds.`
            : `Hourly balance update limit (${throttleCheck.limit}) exceeded. Try again later.`;

          throw new AppError(errorMessage, 429);
        }
      }

      const wallet = await this.getWallet(userId);

      // Update Ethereum/Polygon balances
      const ethAddress = wallet.addresses.ethereum.address;
      const balanceUpdates = [];

      // Get MATIC balance
      const maticBalance = await ethereumService.getNativeBalance(ethAddress, 'polygon');
      balanceUpdates.push({
        symbol: 'MATIC',
        network: 'polygon',
        newBalance: maticBalance.balance
      });

      // Get USDT balance
      if (process.env.USDT_CONTRACT_ADDRESS) {
        const usdtBalance = await ethereumService.getTokenBalance(
          process.env.USDT_CONTRACT_ADDRESS,
          ethAddress,
          'polygon'
        );
        balanceUpdates.push({
          symbol: 'USDT',
          network: 'polygon',
          newBalance: usdtBalance.balance,
          contractAddress: process.env.USDT_CONTRACT_ADDRESS
        });
      }

      // Get USDC balance
      if (process.env.USDC_CONTRACT_ADDRESS) {
        const usdcBalance = await ethereumService.getTokenBalance(
          process.env.USDC_CONTRACT_ADDRESS,
          ethAddress,
          'polygon'
        );
        balanceUpdates.push({
          symbol: 'USDC',
          network: 'polygon',
          newBalance: usdcBalance.balance,
          contractAddress: process.env.USDC_CONTRACT_ADDRESS
        });
      }

      // Get ETH balance
      const ethBalance = await ethereumService.getNativeBalance(ethAddress, 'ethereum');
      balanceUpdates.push({
        symbol: 'ETH',
        network: 'ethereum',
        newBalance: ethBalance.balance
      });

      // Get Bitcoin balance
      const btcAddress = wallet.addresses.bitcoin.address;
      if (btcAddress) {
        const btcBalance = await bitcoinService.getBalance(btcAddress);
        balanceUpdates.push({
          symbol: 'BTC',
          network: 'bitcoin',
          newBalance: btcBalance.balance
        });
      }

      // Use atomic operation to update all balances at once
      const { dataConsistencyService } = require('./dataConsistency');

      const transactionData = {
        type: 'balance_sync',
        timestamp: new Date(),
        balancesUpdated: balanceUpdates.length,
        source: 'balance_update_service'
      };

      await dataConsistencyService.executeAtomicBalanceUpdate(
        userId,
        balanceUpdates,
        transactionData
      );

      // Record the balance update for throttling
      await this.recordBalanceUpdate(userId);

      // Cache the updated wallet data
      const updatedWallet = await this.getWallet(userId);
      await this.cacheBalances(userId, updatedWallet);

      logger.info(`Balances updated atomically for wallet ${wallet.addresses.ethereum.address} (${balanceUpdates.length} balances)`);

      return updatedWallet;

    } catch (error) {
      logger.error(`Error updating balances: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cache wallet balances for throttling
   */
  async cacheBalances(userId, wallet) {
    if (!this.redisClient || !this.redisClient.isReady) return;

    try {
      const cacheKey = `wallet_balances:${userId}`;
      const cacheData = {
        balances: wallet.balances,
        addresses: wallet.addresses,
        lastUpdated: new Date().toISOString(),
        cached: true
      };

      await this.redisClient.setEx(
        cacheKey,
        this.balanceUpdateThrottling.cacheDuration,
        JSON.stringify(cacheData)
      );

      logger.debug(`💾 Cached balances for user ${userId}`);
    } catch (error) {
      logger.error('Failed to cache balances:', error);
    }
  }

  /**
   * Get cached wallet balances
   */
  async getCachedBalances(userId) {
    if (!this.redisClient || !this.redisClient.isReady) return null;

    try {
      const cacheKey = `wallet_balances:${userId}`;
      const cachedData = await this.redisClient.get(cacheKey);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        logger.debug(`📋 Retrieved cached balances for user ${userId}`);
        return {
          ...parsed,
          fromCache: true,
          cacheAge: Date.now() - new Date(parsed.lastUpdated).getTime()
        };
      }

      return null;
    } catch (error) {
      logger.error('Failed to get cached balances:', error);
      return null;
    }
  }
  
  /**
   * Get transaction history
   */
  async getTransactionHistory(userId, limit = 50, offset = 0) {
    try {
      const wallet = await this.getWallet(userId);
      
      const transactions = wallet.transactions
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(offset, offset + limit);
      
      return {
        transactions,
        total: wallet.transactions.length,
        hasMore: offset + limit < wallet.transactions.length
      };
      
    } catch (error) {
      logger.error(`Error getting transaction history: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Add transaction to wallet history (using atomic operations)
   */
  async addTransaction(userId, transactionData) {
    try {
      const wallet = await this.getWallet(userId);

      // Use atomic operation to add transaction
      const { dataConsistencyService } = require('./dataConsistency');

      // If balance update is included, use atomic balance update
      if (transactionData.balanceUpdate) {
        const balanceUpdates = [{
          symbol: transactionData.symbol,
          network: transactionData.network,
          newBalance: transactionData.balanceUpdate.newBalance
        }];

        await dataConsistencyService.executeAtomicBalanceUpdate(
          userId,
          balanceUpdates,
          transactionData
        );
      } else {
        // Just add transaction without balance update
        await wallet.addTransaction(transactionData);
      }

      logger.info(`Transaction added to wallet ${wallet.addresses.ethereum.address}: ${transactionData.hash}`);

      return await this.getWallet(userId);

    } catch (error) {
      logger.error(`Error adding transaction: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Update transaction status
   */
  async updateTransactionStatus(userId, txHash, status, confirmations = 0, blockNumber = null) {
    try {
      const wallet = await this.getWallet(userId);
      await wallet.updateTransactionStatus(txHash, status, confirmations, blockNumber);
      
      logger.info(`Transaction status updated: ${txHash} -> ${status}`);
      
      return wallet;
      
    } catch (error) {
      logger.error(`Error updating transaction status: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Verify mnemonic phrase with enhanced security
   */
  async verifyMnemonic(userId, mnemonic, auditContext = {}) {
    let mnemonicBuffer = null;

    try {
      // Audit mnemonic verification attempt
      await this.auditMnemonicAccess(userId, 'mnemonic_verification_attempt', auditContext);

      // Basic validation first
      if (!bip39.validateMnemonic(mnemonic)) {
        await this.auditMnemonicAccess(userId, 'mnemonic_verification_invalid_format', auditContext);
        return false;
      }

      const wallet = await this.getWallet(userId);

      // Convert to buffer for secure handling
      mnemonicBuffer = Buffer.from(mnemonic, 'utf8');

      // Hash the provided mnemonic
      const mnemonicHash = crypto.createHash('sha256').update(mnemonic).digest('hex');

      // Compare with stored hash
      const isValid = wallet.security.mnemonicHash === mnemonicHash;

      // Audit the result
      if (isValid) {
        await this.auditMnemonicAccess(userId, 'mnemonic_verification_success', auditContext);
      } else {
        await this.auditMnemonicAccess(userId, 'mnemonic_verification_failed', auditContext);
      }

      // Secure cleanup
      setTimeout(() => {
        this.secureMemoryClear(mnemonicBuffer);
        mnemonic = null;
      }, 100);

      return isValid;

    } catch (error) {
      await this.auditMnemonicAccess(userId, 'mnemonic_verification_error', {
        error: error.message,
        ...auditContext
      });

      // Ensure cleanup on error
      if (mnemonicBuffer) {
        this.secureMemoryClear(mnemonicBuffer);
      }
      mnemonic = null;

      logger.error(`Error verifying mnemonic: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Mark backup as completed
   */
  async markBackupCompleted(userId) {
    try {
      const wallet = await this.getWallet(userId);
      
      wallet.security.backupCompleted = true;
      wallet.security.backupDate = new Date();
      
      await wallet.save();
      
      logger.info(`Backup marked as completed for wallet ${wallet.addresses.ethereum.address}`);
      
      return wallet;
      
    } catch (error) {
      logger.error(`Error marking backup completed: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get wallet statistics
   */
  async getWalletStats(userId) {
    try {
      const wallet = await this.getWallet(userId);
      
      // Calculate total portfolio value (simplified)
      let totalValue = 0;
      for (const balance of wallet.balances) {
        // This would use real price feeds in production
        const amount = parseFloat(ethers.utils.formatUnits(balance.balance, balance.decimals));
        totalValue += amount; // Simplified calculation
      }
      
      const stats = {
        totalValue,
        totalTransactions: wallet.transactions.length,
        successfulTransactions: wallet.transactions.filter(tx => tx.status === 'confirmed').length,
        pendingTransactions: wallet.transactions.filter(tx => tx.status === 'pending').length,
        lastActivity: wallet.transactions.length > 0 ? wallet.transactions[0].timestamp : wallet.createdAt,
        backupCompleted: wallet.security.backupCompleted
      };
      
      return stats;
      
    } catch (error) {
      logger.error(`Error getting wallet stats: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate mnemonic format (static validation without user context)
   */
  validateMnemonicFormat(mnemonic) {
    try {
      return bip39.validateMnemonic(mnemonic);
    } catch (error) {
      logger.error(`Error validating mnemonic format: ${error.message}`);
      return false;
    }
  }

  /**
   * Enhanced mnemonic strength validation
   */
  validateMnemonicStrength(mnemonic) {
    try {
      if (!bip39.validateMnemonic(mnemonic)) {
        return { valid: false, reason: 'invalid_format' };
      }

      const words = mnemonic.trim().split(/\s+/);

      // Check word count (12, 15, 18, 21, or 24 words)
      const validWordCounts = [12, 15, 18, 21, 24];
      if (!validWordCounts.includes(words.length)) {
        return { valid: false, reason: 'invalid_word_count', wordCount: words.length };
      }

      // Check for duplicate words
      const uniqueWords = new Set(words);
      if (uniqueWords.size !== words.length) {
        return { valid: false, reason: 'duplicate_words' };
      }

      // Check entropy (24 words = 256 bits is strongest)
      const entropyBits = (words.length / 3) * 32;
      const strength = entropyBits >= 256 ? 'very_strong' :
                      entropyBits >= 192 ? 'strong' :
                      entropyBits >= 128 ? 'medium' : 'weak';

      return {
        valid: true,
        strength,
        entropyBits,
        wordCount: words.length,
        recommendation: entropyBits < 256 ? 'Consider using 24-word mnemonic for maximum security' : null
      };
    } catch (error) {
      logger.error(`Error validating mnemonic strength: ${error.message}`);
      return { valid: false, reason: 'validation_error' };
    }
  }

  /**
   * Get wallet security status
   */
  async getWalletSecurityStatus(userId) {
    try {
      const wallet = await this.getWallet(userId);

      // Check recent balance update activity
      const recentUpdates = await this.getRecentBalanceUpdates(userId);

      return {
        hasBackup: wallet.security.backupCompleted || false,
        backupDate: wallet.security.backupDate || null,
        mnemonicHashExists: !!wallet.security.mnemonicHash,
        lastBalanceUpdate: wallet.balances.reduce((latest, balance) => {
          const balanceTime = new Date(balance.lastUpdated || 0);
          return balanceTime > latest ? balanceTime : latest;
        }, new Date(0)),
        recentUpdateCount: recentUpdates.count,
        securityScore: this.calculateSecurityScore(wallet),
        recommendations: this.getSecurityRecommendations(wallet)
      };
    } catch (error) {
      logger.error(`Error getting wallet security status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calculate wallet security score (0-100)
   */
  calculateSecurityScore(wallet) {
    let score = 0;

    // Base score for having a wallet
    score += 20;

    // Mnemonic hash exists
    if (wallet.security.mnemonicHash) score += 25;

    // Backup completed
    if (wallet.security.backupCompleted) score += 25;

    // Recent backup (within 30 days)
    if (wallet.security.backupDate &&
        (Date.now() - new Date(wallet.security.backupDate).getTime()) < 30 * 24 * 60 * 60 * 1000) {
      score += 15;
    }

    // Multiple addresses configured
    if (wallet.addresses.ethereum && wallet.addresses.bitcoin) score += 10;

    // Recent activity (balances updated recently)
    const hasRecentActivity = wallet.balances.some(balance =>
      balance.lastUpdated &&
      (Date.now() - new Date(balance.lastUpdated).getTime()) < 7 * 24 * 60 * 60 * 1000
    );
    if (hasRecentActivity) score += 5;

    return Math.min(score, 100);
  }

  /**
   * Get security recommendations
   */
  getSecurityRecommendations(wallet) {
    const recommendations = [];

    if (!wallet.security.backupCompleted) {
      recommendations.push({
        priority: 'high',
        type: 'backup',
        message: 'Complete wallet backup to secure your funds'
      });
    }

    if (wallet.security.backupDate &&
        (Date.now() - new Date(wallet.security.backupDate).getTime()) > 90 * 24 * 60 * 60 * 1000) {
      recommendations.push({
        priority: 'medium',
        type: 'backup_refresh',
        message: 'Consider refreshing your backup verification (last backup over 90 days ago)'
      });
    }

    if (!wallet.security.mnemonicHash) {
      recommendations.push({
        priority: 'critical',
        type: 'mnemonic_security',
        message: 'Mnemonic security verification missing - contact support'
      });
    }

    return recommendations;
  }

  /**
   * Get recent balance update statistics
   */
  async getRecentBalanceUpdates(userId) {
    if (!this.redisClient || !this.redisClient.isReady) {
      return { count: 0, source: 'redis_unavailable' };
    }

    try {
      const now = Date.now();
      const hourKey = `balance_updates:${userId}:${Math.floor(now / 3600000)}`;
      const count = await this.redisClient.get(hourKey);

      return {
        count: parseInt(count) || 0,
        hour: Math.floor(now / 3600000),
        limit: this.balanceUpdateThrottling.maxUpdatesPerHour
      };
    } catch (error) {
      logger.error('Failed to get recent balance updates:', error);
      return { count: 0, source: 'error' };
    }
  }

  /**
   * Mark backup as completed
   */
  async markBackupCompleted(userId, auditContext = {}) {
    try {
      const wallet = await Wallet.findOne({ user: userId });
      if (!wallet) {
        throw new AppError('Wallet not found', 404);
      }

      wallet.security.backupCompleted = true;
      wallet.security.backupDate = new Date();
      await wallet.save();

      // Audit backup completion
      await this.auditMnemonicAccess(userId, 'backup_completed', auditContext);

      logger.info(`Backup marked as completed for user ${userId}`);
      return true;
    } catch (error) {
      logger.error(`Error marking backup as completed: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new WalletService();
