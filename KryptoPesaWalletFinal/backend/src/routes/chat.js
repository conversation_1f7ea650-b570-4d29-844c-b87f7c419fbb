const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { authenticate } = require('../middleware/authMiddleware');
const Chat = require('../models/Chat');
const Message = require('../models/Message');
const Trade = require('../models/Trade');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const chatScalabilityService = require('../services/chatScalabilityService');

const router = express.Router();

// All chat routes require authentication
router.use(authenticate);

// Get chat messages for a trade (optimized for scalability)
router.get('/:tradeId/messages', [
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 }),
  query('before').optional().isMongoId(),
  query('after').optional().isMongoId()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { tradeId } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const { before, after } = req.query;

    // Find trade and verify user is participant
    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    // Get chat metadata
    const chat = await Chat.findByTrade(trade._id);
    if (!chat) {
      throw new AppError('Chat not found', 404);
    }

    // Use optimized message retrieval from scalability service
    const options = {
      limit,
      offset,
      userId: req.user._id.toString(),
      before,
      after
    };

    const messages = await chatScalabilityService.getMessages(trade._id, options);
    // Get total message count for pagination
    const totalMessages = await Message.countDocuments({
      trade: trade._id,
      isDeleted: false
    });

    // Get unread count for current user
    const unreadCount = await chatScalabilityService.getUnreadCount(trade._id, req.user._id.toString());

    res.json({
      success: true,
      data: {
        chat: {
          _id: chat._id,
          trade: chat.trade,
          participants: chat.participants,
          status: chat.status,
          metadata: chat.metadata
        },
        messages,
        pagination: {
          total: totalMessages,
          limit,
          offset,
          hasMore: offset + limit < totalMessages
        },
        unreadCount
      }
    });

  } catch (error) {
    next(error);
  }
});

// Send message in trade chat (optimized)
router.post('/:tradeId/messages', [
  body('content').notEmpty().withMessage('Message content is required').isLength({ max: 1000 }),
  body('type').optional().isIn(['text', 'image', 'file', 'system']),
  body('attachmentData').optional().isObject()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { tradeId } = req.params;
    const { content, type = 'text', attachmentData } = req.body;

    // Find trade and verify user is participant
    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    // Get or create chat
    let chat = await Chat.findByTrade(trade._id);
    if (!chat) {
      chat = await Chat.createForTrade(trade._id, trade.seller, trade.buyer);
    }

    // Check if chat is locked
    if (chat.status === 'locked') {
      throw new AppError('Chat is locked', 403);
    }

    // Create message using scalability service
    const message = await chatScalabilityService.createMessage(
      trade._id,
      req.user._id,
      content,
      type,
      attachmentData
    );

    // Update chat metadata
    await chat.updateAfterMessage(message._id, content);
    await chat.incrementUnreadForOthers(req.user._id);

    // Broadcast message via scalability service
    await chatScalabilityService.broadcastMessage(trade._id, message, req.user._id.toString());

    logger.info(`Message sent in trade ${tradeId} by user ${req.user.username}`);

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        messageId: message._id,
        timestamp: message.timestamp,
        tradeId
      }
    });

  } catch (error) {
    next(error);
  }
});

// Mark messages as read (optimized)
router.post('/:tradeId/read', [
  body('messageIds').optional().isArray()
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const { messageIds = [] } = req.body;

    // Find trade and verify user is participant
    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    // Get chat
    const chat = await Chat.findByTrade(trade._id);
    if (!chat) {
      throw new AppError('Chat not found', 404);
    }

    // Mark messages as read using scalability service
    const readCount = await chatScalabilityService.markMessagesAsRead(
      trade._id,
      req.user._id.toString(),
      messageIds
    );

    // Reset unread count in chat
    await chat.resetUnreadCount(req.user._id);

    res.json({
      success: true,
      message: 'Messages marked as read',
      data: { readCount }
    });

  } catch (error) {
    next(error);
  }
});

// Get unread message count for user's trades (optimized)
router.get('/unread-count', async (req, res, next) => {
  try {
    // Get user's active chats
    const chats = await Chat.getUserChats(req.user._id, {
      status: 'active',
      limit: 100
    });

    let totalUnread = 0;
    const unreadByTrade = {};

    for (const chat of chats) {
      const unreadCount = chat.getUnreadCount(req.user._id);
      if (unreadCount > 0) {
        totalUnread += unreadCount;
        unreadByTrade[chat.trade.tradeId] = unreadCount;
      }
    }

    res.json({
      success: true,
      data: {
        totalUnread,
        unreadByTrade,
        activeChats: chats.length
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get user's chat list
router.get('/list', [
  query('limit').optional().isInt({ min: 1, max: 50 }),
  query('offset').optional().isInt({ min: 0 }),
  query('status').optional().isIn(['active', 'archived', 'locked'])
], async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit) || 20;
    const offset = parseInt(req.query.offset) || 0;
    const status = req.query.status || 'active';

    const chats = await Chat.getUserChats(req.user._id, {
      limit,
      offset,
      status
    });

    // Add unread counts and last message preview
    const enrichedChats = chats.map(chat => ({
      ...chat,
      unreadCount: chat.getUnreadCount(req.user._id),
      lastMessagePreview: chat.metadata.lastMessagePreview || null,
      lastActivity: chat.metadata.lastActivity
    }));

    res.json({
      success: true,
      data: {
        chats: enrichedChats,
        pagination: {
          limit,
          offset,
          hasMore: chats.length === limit
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get chat statistics
router.get('/stats', async (req, res, next) => {
  try {
    const chatStats = await Chat.getStats();
    const serviceMetrics = chatScalabilityService.getMetrics();

    res.json({
      success: true,
      data: {
        chatStats,
        serviceMetrics,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    next(error);
  }
});

// Archive chat
router.post('/:tradeId/archive', async (req, res, next) => {
  try {
    const { tradeId } = req.params;

    // Find trade and verify user is participant
    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    // Get chat
    const chat = await Chat.findByTrade(trade._id);
    if (!chat) {
      throw new AppError('Chat not found', 404);
    }

    // Archive the chat
    await chat.archive();

    res.json({
      success: true,
      message: 'Chat archived successfully'
    });

  } catch (error) {
    next(error);
  }
});

// Get chat health check
router.get('/health', async (req, res, next) => {
  try {
    const healthCheck = await chatScalabilityService.getMetrics();

    res.json({
      success: true,
      data: {
        healthy: true,
        metrics: healthCheck,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Chat service health check failed',
      error: error.message
    });
  }
});

module.exports = router;