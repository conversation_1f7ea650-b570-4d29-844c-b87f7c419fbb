import React, { createContext, useContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';

// KYC verification levels
export const KYC_LEVELS = {
  NONE: 0,
  BASIC: 1,
  INTERMEDIATE: 2,
  ADVANCED: 3,
};

// KYC status types
export type KYCStatus = 'not_started' | 'in_progress' | 'under_review' | 'approved' | 'rejected';

// Document status types
export type DocumentStatus = 'not_uploaded' | 'uploaded' | 'under_review' | 'approved' | 'rejected';

// Document types
export interface DocumentData {
  type: string;
  frontImage?: string | null;
  backImage?: string | null;
  image?: string | null;
  status: DocumentStatus;
  rejectionReason?: string;
}

// Personal information interface
export interface PersonalInfo {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  nationality: string;
  phoneNumber: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

// KYC data interface
export interface KYCData {
  currentLevel: number;
  status: KYCStatus;
  personalInfo: PersonalInfo;
  documents: {
    governmentId: DocumentData;
    selfie: DocumentData;
    proofOfAddress: DocumentData;
  };
  rejectionReasons: string[];
  submittedAt?: string;
  reviewedAt?: string;
  approvedAt?: string;
}

// KYC context interface
interface KYCContextType {
  kycData: KYCData;
  isLoading: boolean;
  updatePersonalInfo: (info: Partial<PersonalInfo>) => void;
  updateDocument: (documentType: string, data: Partial<DocumentData>) => void;
  submitKYCVerification: () => Promise<boolean>;
  checkKYCStatus: () => Promise<void>;
  resetKYCData: () => void;
  canTrade: (amount?: number) => boolean;
  getTradingLimit: () => string;
  getRequiredDocuments: () => string[];
}

// Default KYC data
const defaultKYCData: KYCData = {
  currentLevel: KYC_LEVELS.NONE,
  status: 'not_started',
  personalInfo: {
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    nationality: '',
    phoneNumber: '',
    address: {
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
    },
  },
  documents: {
    governmentId: {
      type: '',
      frontImage: null,
      backImage: null,
      status: 'not_uploaded',
    },
    selfie: {
      image: null,
      status: 'not_uploaded',
    },
    proofOfAddress: {
      type: '',
      image: null,
      status: 'not_uploaded',
    },
  },
  rejectionReasons: [],
};

// Create the context
const KYCContext = createContext<KYCContextType | undefined>(undefined);

// KYC Provider component
export const KYCProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [kycData, setKycData] = useState<KYCData>(defaultKYCData);
  const [isLoading, setIsLoading] = useState(false);

  // Update personal information
  const updatePersonalInfo = (info: Partial<PersonalInfo>) => {
    setKycData(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        ...info,
        address: {
          ...prev.personalInfo.address,
          ...(info.address || {}),
        },
      },
    }));
  };

  // Update document data
  const updateDocument = (documentType: string, data: Partial<DocumentData>) => {
    setKycData(prev => ({
      ...prev,
      documents: {
        ...prev.documents,
        [documentType]: {
          ...prev.documents[documentType],
          ...data,
        },
      },
    }));
  };

  // Submit KYC verification
  const submitKYCVerification = async (): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Validate required fields
      const { personalInfo, documents } = kycData;
      
      // Check personal info
      const requiredPersonalFields = ['firstName', 'lastName', 'dateOfBirth', 'nationality', 'phoneNumber'];
      for (const field of requiredPersonalFields) {
        if (!personalInfo[field]) {
          Alert.alert('Error', `Please fill in your ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
          return false;
        }
      }

      // Check address
      const requiredAddressFields = ['street', 'city', 'state', 'postalCode', 'country'];
      for (const field of requiredAddressFields) {
        if (!personalInfo.address[field]) {
          Alert.alert('Error', `Please fill in your ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
          return false;
        }
      }

      // Check required documents
      if (!documents.governmentId.frontImage || !documents.selfie.image) {
        Alert.alert('Error', 'Please upload all required documents');
        return false;
      }

      // In a real app, this would submit to backend
      // const result = await submitKYCMutation({ kycData });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update status
      setKycData(prev => ({
        ...prev,
        status: 'under_review',
        submittedAt: new Date().toISOString(),
      }));

      return true;
    } catch (error) {
      console.error('KYC submission error:', error);
      Alert.alert('Error', 'Failed to submit KYC verification. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Check KYC status
  const checkKYCStatus = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would fetch from backend
      // const status = await getKYCStatusQuery();
      
      // For demo purposes, simulate status check
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock status update (in real app this would come from backend)
      // setKycData(prev => ({ ...prev, ...status }));
    } catch (error) {
      console.error('KYC status check error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Reset KYC data
  const resetKYCData = () => {
    setKycData(defaultKYCData);
  };

  // Check if user can trade based on KYC level
  const canTrade = (amount?: number): boolean => {
    if (kycData.status !== 'approved') return false;
    
    if (!amount) return true;
    
    switch (kycData.currentLevel) {
      case KYC_LEVELS.BASIC:
        return amount <= 1000;
      case KYC_LEVELS.INTERMEDIATE:
        return amount <= 10000;
      case KYC_LEVELS.ADVANCED:
        return true;
      default:
        return false;
    }
  };

  // Get trading limit based on KYC level
  const getTradingLimit = (): string => {
    switch (kycData.currentLevel) {
      case KYC_LEVELS.BASIC:
        return '$1,000';
      case KYC_LEVELS.INTERMEDIATE:
        return '$10,000';
      case KYC_LEVELS.ADVANCED:
        return 'Unlimited';
      default:
        return '$0';
    }
  };

  // Get required documents for next level
  const getRequiredDocuments = (): string[] => {
    switch (kycData.currentLevel) {
      case KYC_LEVELS.NONE:
        return ['Personal Information', 'Government ID', 'Selfie Verification'];
      case KYC_LEVELS.BASIC:
        return ['Proof of Address'];
      case KYC_LEVELS.INTERMEDIATE:
        return ['Enhanced Due Diligence'];
      default:
        return [];
    }
  };

  // Initialize KYC data on mount
  useEffect(() => {
    checkKYCStatus();
  }, []);

  const value: KYCContextType = {
    kycData,
    isLoading,
    updatePersonalInfo,
    updateDocument,
    submitKYCVerification,
    checkKYCStatus,
    resetKYCData,
    canTrade,
    getTradingLimit,
    getRequiredDocuments,
  };

  return <KYCContext.Provider value={value}>{children}</KYCContext.Provider>;
};

// Hook to use KYC context
export const useKYC = (): KYCContextType => {
  const context = useContext(KYCContext);
  if (context === undefined) {
    throw new Error('useKYC must be used within a KYCProvider');
  }
  return context;
};
