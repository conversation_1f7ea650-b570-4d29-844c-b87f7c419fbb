const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { AppError } = require('../middleware/errorHandler');
const { auditLogger } = require('../middleware/auditLogger');
const authService = require('../services/authService');
const { authenticate, require2FA } = require('../middleware/authMiddleware');
const logger = require('../utils/logger');

const router = express.Router();

// Register user
router.post('/register',
  auditLogger('user_register', 'user'),
  [
  body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Please provide a valid phone number with country code'),
  body('password')
    .custom((password) => {
      const validation = authService.validatePassword(password);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    }),
  body('firstName')
    .isLength({ min: 1, max: 50 })
    .trim()
    .withMessage('First name is required'),
  body('lastName')
    .isLength({ min: 1, max: 50 })
    .trim()
    .withMessage('Last name is required'),
  body('country')
    .isIn(['KE', 'TZ', 'UG', 'RW'])
    .withMessage('Country must be one of: KE, TZ, UG, RW'),
  body('city')
    .isLength({ min: 1 })
    .trim()
    .withMessage('City is required')
], async (req, res, next) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      username,
      email,
      phone,
      password,
      firstName,
      lastName,
      country,
      city
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }, { phone }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists with this email, username, or phone number'
      });
    }

    // Create new user
    const user = new User({
      username,
      email,
      phone,
      password,
      profile: {
        firstName,
        lastName,
        location: {
          country,
          city
        }
      }
    });

    await user.save();

    // Generate tokens using enhanced auth service
    const tokens = await authService.generateTokens(user._id);

    // Remove password from response
    const userResponse = user.toObject();

    logger.info(`New user registered: ${username} (${email})`);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userResponse,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn
      }
    });

  } catch (error) {
    next(error);
  }
});

// Login user
router.post('/login',
  auditLogger('user_login', 'user'),
  [
  body('identifier')
    .notEmpty()
    .withMessage('Email, username, or phone is required'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { identifier, password } = req.body;

    // Find user by email, username, or phone
    const user = await User.findOne({
      $or: [
        { email: identifier.toLowerCase() },
        { username: identifier },
        { phone: identifier }
      ]
    }).select('+password');

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check account status
    if (user.status !== 'active') {
      return res.status(403).json({
        success: false,
        message: 'Account is not active. Please contact support if you believe this is an error.'
      });
    }

    // Check if account is locked
    if (user.isLocked) {
      return res.status(423).json({
        success: false,
        message: 'Account is temporarily locked due to too many failed login attempts'
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);

    if (!isPasswordValid) {
      await user.incLoginAttempts();
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Reset login attempts on successful login
    if (user.security.loginAttempts > 0) {
      await user.updateOne({
        $unset: { 'security.loginAttempts': 1, 'security.lockUntil': 1 }
      });
    }

    // Update last login
    user.security.lastLogin = new Date();
    user.lastActive = new Date();
    await user.save();

    // Generate tokens using enhanced auth service
    const tokens = await authService.generateTokens(user._id);

    // Remove sensitive data from response
    const userResponse = user.toObject();

    logger.info(`User logged in: ${user.username} (${user.email})`);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn,
        requiresTwoFactor: user.security.twoFactorEnabled
      }
    });

  } catch (error) {
    next(error);
  }
});

// Token refresh endpoint
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { refreshToken } = req.body;
    const tokens = await authService.refreshAccessToken(refreshToken);

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: tokens
    });
  } catch (error) {
    next(error);
  }
});

// Get current user
router.get('/me', authenticate, async (req, res) => {
  const userResponse = req.user.toObject();

  res.json({
    success: true,
    data: {
      user: userResponse
    }
  });
});

// Logout with session invalidation
router.post('/logout', authenticate, async (req, res, next) => {
  try {
    await authService.revokeRefreshToken(req.user._id, req.sessionId);

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    next(error);
  }
});

// Logout from all devices
router.post('/logout-all', authenticate, async (req, res, next) => {
  try {
    await authService.revokeAllUserSessions(req.user._id);

    res.json({
      success: true,
      message: 'Logged out from all devices successfully'
    });
  } catch (error) {
    next(error);
  }
});

// Setup 2FA
router.post('/2fa/setup', authenticate, async (req, res, next) => {
  try {
    if (req.user.security.twoFactorEnabled) {
      return res.status(400).json({
        success: false,
        message: 'Two-factor authentication is already enabled'
      });
    }

    const twoFactorData = await authService.generate2FASecret(req.user._id, req.user.email);

    res.json({
      success: true,
      message: '2FA setup initiated',
      data: twoFactorData
    });
  } catch (error) {
    next(error);
  }
});

// Verify and enable 2FA
router.post('/2fa/verify', authenticate, [
  body('secret')
    .notEmpty()
    .withMessage('2FA secret is required'),
  body('token')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('2FA token must be 6 digits')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { secret, token } = req.body;

    const isValid = authService.verify2FAToken(secret, token);
    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid 2FA token'
      });
    }

    // Enable 2FA for user
    req.user.security.twoFactorEnabled = true;
    req.user.security.twoFactorSecret = secret;
    await req.user.save();

    logger.info(`2FA enabled for user ${req.user.username}`);

    res.json({
      success: true,
      message: 'Two-factor authentication enabled successfully'
    });
  } catch (error) {
    next(error);
  }
});

// Disable 2FA
router.post('/2fa/disable', authenticate, require2FA, async (req, res, next) => {
  try {
    req.user.security.twoFactorEnabled = false;
    req.user.security.twoFactorSecret = undefined;
    await req.user.save();

    logger.info(`2FA disabled for user ${req.user.username}`);

    res.json({
      success: true,
      message: 'Two-factor authentication disabled successfully'
    });
  } catch (error) {
    next(error);
  }
});

// Get active sessions
router.get('/sessions', authenticate, async (req, res, next) => {
  try {
    const sessions = await authService.getUserSessions(req.user._id);

    res.json({
      success: true,
      data: {
        sessions
      }
    });
  } catch (error) {
    next(error);
  }
});

// Revoke specific session
router.delete('/sessions/:sessionId', authenticate, async (req, res, next) => {
  try {
    const { sessionId } = req.params;
    await authService.revokeRefreshToken(req.user._id, sessionId);

    res.json({
      success: true,
      message: 'Session revoked successfully'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = { router, authenticate };
