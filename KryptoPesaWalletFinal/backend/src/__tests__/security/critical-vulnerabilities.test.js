const jwt = require('jsonwebtoken');
const User = require('../../models/User');
const Trade = require('../../models/Trade');
require('../setup'); // This sets up global test utilities

describe('Critical Security Vulnerabilities Tests', () => {
  let testUser, authToken;

  beforeEach(async () => {
    testUser = await global.createTestUser();
    authToken = jwt.sign({ userId: testUser._id }, process.env.JWT_SECRET);
  });

  describe('Authentication & Authorization Vulnerabilities', () => {
    test('should reject requests with invalid JWT tokens', async () => {
      const invalidTokens = [
        'invalid.jwt.token',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
        '',
        null,
        undefined
      ];

      for (const token of invalidTokens) {
        try {
          if (token) {
            jwt.verify(token, process.env.JWT_SECRET);
            fail(`Should have thrown an error for invalid token: ${token}`);
          }
        } catch (error) {
          expect(error).toBeDefined();
        }
      }
    });

    test('should reject expired JWT tokens', async () => {
      const expiredToken = jwt.sign(
        { userId: testUser._id },
        process.env.JWT_SECRET,
        { expiresIn: '-1h' }
      );

      try {
        jwt.verify(expiredToken, process.env.JWT_SECRET);
        fail('Should have thrown an error for expired token');
      } catch (error) {
        expect(error.name).toBe('TokenExpiredError');
      }
    });

    test('should prevent access to other users\' resources', async () => {
      const otherUser = await global.createTestUser({
        username: 'otheruser',
        email: '<EMAIL>',
        phone: '+************' // Different phone to avoid duplicate key error
      });

      // Test that user IDs are different
      expect(otherUser._id.toString()).not.toBe(testUser._id.toString());
    });

    test('should validate user account status before allowing access', async () => {
      // Test that suspended users should be handled properly
      const suspendedUser = await User.findByIdAndUpdate(
        testUser._id,
        { status: 'suspended' },
        { new: true }
      );

      expect(suspendedUser.status).toBe('suspended');
    });
  });

  describe('Input Validation Vulnerabilities', () => {
    test('should prevent NoSQL injection in user queries', async () => {
      // Test direct model usage to avoid complex server setup
      const maliciousQuery = { $ne: null };

      try {
        const result = await User.findOne({ email: maliciousQuery });
        // Should not return any results with malicious query
        expect(result).toBeNull();
      } catch (error) {
        // Should throw an error for invalid query
        expect(error).toBeDefined();
      }
    });

    test('should validate input sanitization', async () => {
      const xssPayload = '<script>alert("xss")</script>';

      // Test that XSS payload is properly handled in user creation
      try {
        const user = new User({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123',
          phone: '+254700000000',
          profile: {
            firstName: xssPayload,
            lastName: 'Test',
            location: {
              country: 'KE',
              city: 'Nairobi'
            }
          }
        });

        await user.save();

        // Check if XSS payload was sanitized
        const savedUser = await User.findById(user._id);
        expect(savedUser.profile.firstName).not.toContain('<script>');
      } catch (error) {
        // Validation should catch malicious input
        expect(error).toBeDefined();
      }
    });
  });

  describe('Sensitive Data Exposure', () => {
    test('should never expose passwords in user objects', async () => {
      const user = await User.findById(testUser._id);
      const userObject = user.toObject();

      expect(userObject.password).toBeUndefined();
    });

    test('should never expose 2FA secrets in user objects', async () => {
      const user = await User.findById(testUser._id);
      const userObject = user.toObject();

      expect(userObject.security?.twoFactorSecret).toBeUndefined();
    });

    test('should not expose sensitive fields in JSON serialization', async () => {
      const user = await User.findById(testUser._id);
      const userJSON = JSON.stringify(user);

      expect(userJSON).not.toContain('password');
      expect(userJSON).not.toContain('twoFactorSecret');
    });
  });

  describe('Authorization and Access Control', () => {
    test('should prevent privilege escalation in user model', async () => {
      // Test that users cannot directly modify their role
      const user = await User.findById(testUser._id);
      const originalRole = user.role;

      // Attempt to change role directly
      user.role = 'admin';
      await user.save();

      // Verify role didn't change (should be protected)
      const updatedUser = await User.findById(testUser._id);
      expect(updatedUser.role).toBe(originalRole);
    });

    test('should validate JWT token structure', async () => {
      const invalidTokens = [
        'invalid.jwt.token',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
        '',
        null,
        undefined
      ];

      for (const token of invalidTokens) {
        try {
          if (token) {
            jwt.verify(token, process.env.JWT_SECRET);
            fail('Should have thrown an error for invalid token');
          }
        } catch (error) {
          expect(error).toBeDefined();
        }
      }
    });
  });

  describe('JWT Security Validation', () => {
    test('should use secure JWT configuration', () => {
      const jwtSecret = process.env.JWT_SECRET;

      // JWT secret should be strong
      expect(jwtSecret).toBeDefined();
      expect(jwtSecret.length).toBeGreaterThan(32);
    });

    test('should handle token expiration properly', async () => {
      const expiredToken = jwt.sign(
        { userId: testUser._id },
        process.env.JWT_SECRET,
        { expiresIn: '-1h' }
      );

      try {
        jwt.verify(expiredToken, process.env.JWT_SECRET);
        fail('Should have thrown an error for expired token');
      } catch (error) {
        expect(error.name).toBe('TokenExpiredError');
      }
    });
  });

  describe('Data Validation Security', () => {
    test('should validate user input lengths', async () => {
      const longString = 'x'.repeat(10000);

      try {
        const user = new User({
          username: longString,
          email: '<EMAIL>',
          password: 'password123',
          phone: '+254700000000',
          profile: {
            firstName: 'Test',
            lastName: 'User',
            location: {
              country: 'KE',
              city: 'Nairobi'
            }
          }
        });

        await user.save();
        fail('Should have failed validation for overly long username');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('should validate email format', async () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        '<EMAIL>'
      ];

      for (const email of invalidEmails) {
        try {
          const user = new User({
            username: 'testuser',
            email: email,
            password: 'password123',
            phone: '+254700000000',
            profile: {
              firstName: 'Test',
              lastName: 'User',
              location: {
                country: 'KE',
                city: 'Nairobi'
              }
            }
          });

          await user.save();
          fail(`Should have failed validation for invalid email: ${email}`);
        } catch (error) {
          expect(error).toBeDefined();
        }
      }
    });
  });
});
