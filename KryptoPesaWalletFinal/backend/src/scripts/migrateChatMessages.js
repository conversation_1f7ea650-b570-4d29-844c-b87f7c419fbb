const mongoose = require('mongoose');
const Chat = require('../models/Chat');
const Message = require('../models/Message');
const logger = require('../utils/logger');

/**
 * Migration script to move embedded messages from Chat documents to separate Message collection
 * This improves scalability by eliminating document size growth issues
 */
class ChatMessageMigration {
  constructor() {
    this.migratedCount = 0;
    this.errorCount = 0;
    this.processedChats = 0;
    this.startTime = Date.now();
  }

  /**
   * Run the migration
   */
  async run() {
    try {
      logger.info('Starting chat message migration...');
      
      // Connect to database if not already connected
      if (mongoose.connection.readyState !== 1) {
        await mongoose.connect(process.env.MONGODB_URI);
        logger.info('Connected to MongoDB for migration');
      }

      // Get all chats with embedded messages
      const chats = await Chat.find({ 
        'messages.0': { $exists: true } // Only chats with messages
      }).lean();

      logger.info(`Found ${chats.length} chats with embedded messages to migrate`);

      if (chats.length === 0) {
        logger.info('No chats with embedded messages found. Migration complete.');
        return;
      }

      // Process chats in batches to avoid memory issues
      const batchSize = 10;
      for (let i = 0; i < chats.length; i += batchSize) {
        const batch = chats.slice(i, i + batchSize);
        await this.processBatch(batch);
        
        // Log progress
        const progress = Math.round(((i + batch.length) / chats.length) * 100);
        logger.info(`Migration progress: ${progress}% (${i + batch.length}/${chats.length} chats)`);
      }

      // Final report
      const duration = (Date.now() - this.startTime) / 1000;
      logger.info(`Migration completed in ${duration}s:`);
      logger.info(`- Processed chats: ${this.processedChats}`);
      logger.info(`- Migrated messages: ${this.migratedCount}`);
      logger.info(`- Errors: ${this.errorCount}`);

      if (this.errorCount === 0) {
        logger.info('Migration completed successfully! You can now remove embedded messages from Chat schema.');
      } else {
        logger.warn(`Migration completed with ${this.errorCount} errors. Please review the logs.`);
      }

    } catch (error) {
      logger.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * Process a batch of chats
   */
  async processBatch(chats) {
    const promises = chats.map(chat => this.migrateChat(chat));
    await Promise.allSettled(promises);
  }

  /**
   * Migrate messages from a single chat
   */
  async migrateChat(chat) {
    try {
      if (!chat.messages || chat.messages.length === 0) {
        return;
      }

      logger.debug(`Migrating ${chat.messages.length} messages from chat ${chat._id}`);

      // Prepare messages for insertion
      const messagesToInsert = chat.messages.map(msg => ({
        trade: chat.trade,
        sender: msg.sender,
        message: msg.content,
        type: msg.type || 'text',
        timestamp: msg.createdAt || msg.timestamp || new Date(),
        isRead: this.determineReadStatus(msg, chat),
        readAt: this.getReadTimestamp(msg),
        attachmentUrl: this.getAttachmentUrl(msg),
        attachmentType: this.getAttachmentType(msg),
        attachmentSize: this.getAttachmentSize(msg),
        isDeleted: false,
        isArchived: false,
        metadata: {
          migratedFrom: 'embedded_chat_message',
          originalMessageId: msg._id,
          migrationDate: new Date()
        }
      }));

      // Insert messages in batch
      const insertedMessages = await Message.insertMany(messagesToInsert, { 
        ordered: false // Continue on errors
      });

      // Update chat metadata
      await this.updateChatAfterMigration(chat, insertedMessages);

      this.migratedCount += insertedMessages.length;
      this.processedChats++;

      logger.debug(`Successfully migrated ${insertedMessages.length} messages from chat ${chat._id}`);

    } catch (error) {
      this.errorCount++;
      logger.error(`Failed to migrate chat ${chat._id}:`, error);
    }
  }

  /**
   * Determine if message was read based on embedded readBy array
   */
  determineReadStatus(message, chat) {
    if (!message.readBy || message.readBy.length === 0) {
      return false;
    }

    // Check if all participants have read the message
    const participantIds = chat.participants.map(p => p.user.toString());
    const readByIds = message.readBy.map(r => r.user.toString());
    
    return participantIds.every(id => readByIds.includes(id));
  }

  /**
   * Get read timestamp from embedded readBy array
   */
  getReadTimestamp(message) {
    if (!message.readBy || message.readBy.length === 0) {
      return null;
    }

    // Return the latest read timestamp
    const readTimes = message.readBy.map(r => r.readAt).filter(Boolean);
    return readTimes.length > 0 ? new Date(Math.max(...readTimes.map(d => new Date(d)))) : null;
  }

  /**
   * Extract attachment URL from embedded message
   */
  getAttachmentUrl(message) {
    if (message.attachments && message.attachments.length > 0) {
      return message.attachments[0].url || null;
    }
    return null;
  }

  /**
   * Extract attachment type from embedded message
   */
  getAttachmentType(message) {
    if (message.attachments && message.attachments.length > 0) {
      return message.attachments[0].mimeType || message.type || null;
    }
    return null;
  }

  /**
   * Extract attachment size from embedded message
   */
  getAttachmentSize(message) {
    if (message.attachments && message.attachments.length > 0) {
      return message.attachments[0].size || null;
    }
    return null;
  }

  /**
   * Update chat document after successful migration
   */
  async updateChatAfterMigration(chat, insertedMessages) {
    try {
      const lastMessage = insertedMessages[insertedMessages.length - 1];
      
      const updateData = {
        'metadata.totalMessages': insertedMessages.length,
        'metadata.lastActivity': lastMessage.timestamp,
        'metadata.lastMessageId': lastMessage._id,
        'metadata.lastMessagePreview': lastMessage.message ? lastMessage.message.substring(0, 100) : '',
        'metadata.migrationCompleted': true,
        'metadata.migrationDate': new Date()
      };

      await Chat.updateOne({ _id: chat._id }, { $set: updateData });
      
      logger.debug(`Updated chat metadata for ${chat._id}`);
    } catch (error) {
      logger.error(`Failed to update chat metadata for ${chat._id}:`, error);
    }
  }

  /**
   * Validate migration results
   */
  async validateMigration() {
    try {
      logger.info('Validating migration results...');

      // Count embedded messages still remaining
      const chatsWithMessages = await Chat.countDocuments({
        'messages.0': { $exists: true }
      });

      // Count migrated messages
      const migratedMessages = await Message.countDocuments({
        'metadata.migratedFrom': 'embedded_chat_message'
      });

      logger.info(`Validation results:`);
      logger.info(`- Chats with embedded messages remaining: ${chatsWithMessages}`);
      logger.info(`- Messages in Message collection: ${migratedMessages}`);

      if (chatsWithMessages === 0) {
        logger.info('✅ Migration validation passed - no embedded messages remaining');
        return true;
      } else {
        logger.warn('⚠️  Migration validation failed - embedded messages still exist');
        return false;
      }

    } catch (error) {
      logger.error('Migration validation failed:', error);
      return false;
    }
  }

  /**
   * Rollback migration (for testing purposes)
   */
  async rollback() {
    try {
      logger.info('Rolling back migration...');

      // Delete migrated messages
      const result = await Message.deleteMany({
        'metadata.migratedFrom': 'embedded_chat_message'
      });

      logger.info(`Rollback completed - deleted ${result.deletedCount} migrated messages`);

    } catch (error) {
      logger.error('Rollback failed:', error);
      throw error;
    }
  }
}

// CLI execution
if (require.main === module) {
  const migration = new ChatMessageMigration();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'run':
      migration.run()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
      break;
      
    case 'validate':
      migration.validateMigration()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
      break;
      
    case 'rollback':
      migration.rollback()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
      break;
      
    default:
      console.log('Usage: node migrateChatMessages.js [run|validate|rollback]');
      process.exit(1);
  }
}

module.exports = ChatMessageMigration;
