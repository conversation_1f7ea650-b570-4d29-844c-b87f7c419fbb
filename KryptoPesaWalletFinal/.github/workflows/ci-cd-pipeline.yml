name: KryptoPesa CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: "18.x"
  MONGODB_VERSION: "6.0"
  REDIS_VERSION: "7.0"

jobs:
  # Security and Code Quality Checks
  security-scan:
    name: Security & Code Quality
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: |
            backend/package-lock.json
            mobile/package-lock.json
            admin-dashboard/package-lock.json

      - name: Install Backend Dependencies
        run: |
          cd backend
          npm ci

      - name: Security Audit - Backend
        run: |
          cd backend
          npm audit --audit-level=high

      - name: Lint Backend Code
        run: |
          cd backend
          npm run lint || true

      - name: Install Mobile Dependencies
        run: |
          cd mobile
          npm ci --legacy-peer-deps

      - name: Security Audit - Mobile
        run: |
          cd mobile
          npm audit --audit-level=high

      - name: Install Admin Dashboard Dependencies
        run: |
          cd admin-dashboard
          npm ci

      - name: Security Audit - Admin Dashboard
        run: |
          cd admin-dashboard
          npm audit --audit-level=high

      - name: CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Backend Testing
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    needs: security-scan

    services:
      mongodb:
        image: mongo:6.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.0
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: backend/package-lock.json

      - name: Install Dependencies
        run: |
          cd backend
          npm ci

      - name: Run Unit Tests
        run: |
          cd backend
          npm run test:unit
        env:
          NODE_ENV: test
          MONGODB_TEST_URI: mongodb://localhost:27017/kryptopesa_test
          REDIS_TEST_URL: redis://localhost:6379/1

      - name: Run Integration Tests
        run: |
          cd backend
          npm run test:integration
        env:
          NODE_ENV: test
          MONGODB_TEST_URI: mongodb://localhost:27017/kryptopesa_test
          REDIS_TEST_URL: redis://localhost:6379/1

      - name: Run E2E Tests
        run: |
          cd backend
          node scripts/run-e2e-tests.js
        env:
          NODE_ENV: test
          MONGODB_TEST_URI: mongodb://localhost:27017/kryptopesa_test
          REDIS_TEST_URL: redis://localhost:6379/1

      - name: Upload Coverage Reports
        uses: codecov/codecov-action@v3
        with:
          directory: backend/coverage
          flags: backend

  # Frontend Testing
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    needs: security-scan

    strategy:
      matrix:
        app: [mobile, admin-dashboard]

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: ${{ matrix.app }}/package-lock.json

      - name: Install Dependencies
        run: |
          cd ${{ matrix.app }}
          npm ci ${{ matrix.app == 'mobile' && '--legacy-peer-deps' || '' }}

      - name: Run Tests
        run: |
          cd ${{ matrix.app }}
          npm test -- --coverage --watchAll=false
        env:
          CI: true

      - name: Build Application
        run: |
          cd ${{ matrix.app }}
          npm run build
        env:
          CI: true

      - name: Upload Coverage Reports
        uses: codecov/codecov-action@v3
        with:
          directory: ${{ matrix.app }}/coverage
          flags: ${{ matrix.app }}

  # Performance Testing
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [backend-tests]

    services:
      mongodb:
        image: mongo:6.0
        ports:
          - 27017:27017
      redis:
        image: redis:7.0
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Dependencies
        run: |
          cd backend
          npm ci

      - name: Start Backend Server
        run: |
          cd backend
          npm start &
          sleep 10
        env:
          NODE_ENV: production
          MONGODB_URI: mongodb://localhost:27017/kryptopesa_perf
          REDIS_URL: redis://localhost:6379

      - name: Install Artillery
        run: npm install -g artillery

      - name: Run Load Tests
        run: |
          cd backend/tests/performance
          artillery run load-test.yml

      - name: Upload Performance Results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: backend/tests/performance/results/

  # Build and Deploy
  build-and-deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, performance-tests]
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      # Backend Build
      - name: Build Backend
        run: |
          cd backend
          npm ci
          npm run build

      # Mobile App Build
      - name: Setup Expo CLI
        run: npm install -g @expo/cli

      - name: Build Mobile App
        run: |
          cd mobile
          npm ci --legacy-peer-deps
          expo export --platform android

      # Admin Dashboard Build
      - name: Build Admin Dashboard
        run: |
          cd admin-dashboard
          npm ci
          npm run build

      # Docker Build
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.CONTAINER_REGISTRY }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build and Push Backend Image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: |
            ${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-backend:latest
            ${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-backend:${{ github.sha }}

      - name: Build and Push Admin Dashboard Image
        uses: docker/build-push-action@v5
        with:
          context: ./admin-dashboard
          push: true
          tags: |
            ${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-admin:latest
            ${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-admin:${{ github.sha }}

      # Security Scanning
      - name: Run Security Scan
        uses: securecodewarrior/github-action-add-sarif@v1
        with:
          sarif-file: "security-scan-results.sarif"

      - name: Container Security Scan
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: "${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-backend:${{ github.sha }}"
          format: "sarif"
          output: "trivy-results.sarif"

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: "trivy-results.sarif"

      # Deploy to Staging
      - name: Deploy to Staging
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "Deploying to staging environment..."
          kubectl config set-context --current --namespace=kryptopesa-staging
          kubectl set image deployment/backend-blue backend=${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-backend:${{ github.sha }}
          kubectl rollout status deployment/backend-blue --timeout=300s

      # Deploy to Production (Blue-Green)
      - name: Deploy to Production
        if: github.ref == 'refs/heads/main'
        run: |
          echo "Starting blue-green deployment to production..."

          # Set up kubectl for production
          kubectl config set-context --current --namespace=kryptopesa-production

          # Determine current active deployment (blue or green)
          CURRENT_ACTIVE=$(kubectl get service backend -o jsonpath='{.spec.selector.version}')
          echo "Current active deployment: $CURRENT_ACTIVE"

          # Determine target deployment
          if [ "$CURRENT_ACTIVE" = "blue" ]; then
            TARGET="green"
          else
            TARGET="blue"
          fi
          echo "Target deployment: $TARGET"

          # Update target deployment with new image
          kubectl set image deployment/backend-$TARGET backend=${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-backend:${{ github.sha }}
          kubectl set image deployment/admin-$TARGET admin=${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-admin:${{ github.sha }}

          # Scale up target deployment
          kubectl scale deployment backend-$TARGET --replicas=3
          kubectl scale deployment admin-$TARGET --replicas=2

          # Wait for rollout to complete
          kubectl rollout status deployment/backend-$TARGET --timeout=600s
          kubectl rollout status deployment/admin-$TARGET --timeout=600s

          # Run health checks on target deployment
          echo "Running health checks..."
          sleep 30

          # Get target pod IP for health check
          TARGET_POD=$(kubectl get pods -l app=backend,version=$TARGET -o jsonpath='{.items[0].status.podIP}')

          # Health check
          if curl -f http://$TARGET_POD:3000/health; then
            echo "Health check passed. Switching traffic to $TARGET deployment."

            # Switch service to target deployment
            kubectl patch service backend -p '{"spec":{"selector":{"version":"'$TARGET'"}}}'
            kubectl patch service admin -p '{"spec":{"selector":{"version":"'$TARGET'"}}}'

            # Wait for traffic switch
            sleep 30

            # Scale down old deployment
            kubectl scale deployment backend-$CURRENT_ACTIVE --replicas=0
            kubectl scale deployment admin-$CURRENT_ACTIVE --replicas=0

            echo "Blue-green deployment completed successfully!"
          else
            echo "Health check failed. Rolling back..."
            kubectl scale deployment backend-$TARGET --replicas=0
            kubectl scale deployment admin-$TARGET --replicas=0
            exit 1
          fi

      # Post-deployment verification
      - name: Post-deployment Verification
        if: github.ref == 'refs/heads/main'
        run: |
          echo "Running post-deployment verification..."

          # Wait for services to be ready
          sleep 60

          # Test API endpoints
          curl -f https://api.kryptopesa.com/health || exit 1
          curl -f https://admin.kryptopesa.com/health || exit 1

          # Run smoke tests
          cd backend
          npm run test:smoke

          echo "Post-deployment verification completed successfully!"

      # Notify deployment status
      - name: Notify Deployment Status
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: "#deployments"
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow

      # Health Check
      - name: Production Health Check
        if: github.ref == 'refs/heads/main'
        run: |
          sleep 30
          curl -f ${{ secrets.PRODUCTION_URL }}/health || exit 1

  # Notification
  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    if: always()

    steps:
      - name: Notify Success
        if: needs.build-and-deploy.result == 'success'
        run: |
          echo "✅ Deployment successful!"
          # Add Slack/Discord notification here

      - name: Notify Failure
        if: needs.build-and-deploy.result == 'failure'
        run: |
          echo "❌ Deployment failed!"
          # Add failure notification here
