#!/usr/bin/env node

/**
 * Load Production Environment Variables for Testing
 * This script loads the production environment configuration for validation
 */

const fs = require('fs');
const path = require('path');

function loadProductionEnv() {
  const envPath = path.join(__dirname, '..', '.env.production');
  
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      // Skip comments and empty lines
      if (line.trim() === '' || line.trim().startsWith('#')) {
        continue;
      }
      
      // Parse key=value pairs
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=');
        process.env[key.trim()] = value.trim();
      }
    }
    
    console.log('✅ Production environment variables loaded');
    console.log(`Loaded ${Object.keys(process.env).filter(key => !key.startsWith('_')).length} environment variables`);
    
  } catch (error) {
    console.error('❌ Failed to load production environment:', error.message);
    process.exit(1);
  }
}

// Load environment if this script is run directly
if (require.main === module) {
  loadProductionEnv();
}

module.exports = { loadProductionEnv };
