/**
 * Response filtering utility to reduce API payload sizes
 * Allows clients to specify which fields they want in the response
 */

/**
 * Filter object properties based on field selection
 * @param {Object} obj - Object to filter
 * @param {String|Array} fields - Comma-separated string or array of field names
 * @returns {Object} Filtered object
 */
const filterFields = (obj, fields) => {
  if (!fields || !obj) return obj;
  
  // Convert string to array
  const fieldArray = Array.isArray(fields) 
    ? fields 
    : fields.split(',').map(f => f.trim());
  
  if (fieldArray.length === 0) return obj;
  
  const filtered = {};
  
  fieldArray.forEach(field => {
    // Handle nested fields (e.g., 'user.profile.firstName')
    const fieldParts = field.split('.');
    let sourceValue = obj;
    let targetRef = filtered;
    
    // Navigate to the nested value
    for (let i = 0; i < fieldParts.length - 1; i++) {
      const part = fieldParts[i];
      if (sourceValue && sourceValue[part] !== undefined) {
        sourceValue = sourceValue[part];
        if (!targetRef[part]) targetRef[part] = {};
        targetRef = targetRef[part];
      } else {
        return; // Field doesn't exist, skip
      }
    }
    
    // Set the final value
    const finalField = fieldParts[fieldParts.length - 1];
    if (sourceValue && sourceValue[finalField] !== undefined) {
      targetRef[finalField] = sourceValue[finalField];
    }
  });
  
  return filtered;
};

/**
 * Filter array of objects
 * @param {Array} array - Array of objects to filter
 * @param {String|Array} fields - Fields to include
 * @returns {Array} Filtered array
 */
const filterArray = (array, fields) => {
  if (!Array.isArray(array)) return array;
  return array.map(item => filterFields(item, fields));
};

/**
 * Express middleware for response filtering
 * Usage: Add ?fields=field1,field2,nested.field to query string
 */
const responseFilterMiddleware = (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    const fields = req.query.fields;
    
    if (fields && data && data.success && data.data) {
      // Filter the data portion of the response
      if (Array.isArray(data.data)) {
        data.data = filterArray(data.data, fields);
      } else if (typeof data.data === 'object') {
        data.data = filterFields(data.data, fields);
      }
      
      // Handle nested data structures
      if (data.data.trades) {
        data.data.trades = filterArray(data.data.trades, fields);
      }
      if (data.data.users) {
        data.data.users = filterArray(data.data.users, fields);
      }
      if (data.data.offers) {
        data.data.offers = filterArray(data.data.offers, fields);
      }
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};

/**
 * Predefined field sets for common use cases
 */
const fieldSets = {
  // Minimal user info for lists
  userBasic: 'username,profile.firstName,profile.lastName,reputation.score',
  
  // Trade summary for lists
  tradeSummary: 'tradeId,status,cryptocurrency.symbol,cryptocurrency.amount,fiat.currency,fiat.amount,createdAt',
  
  // Offer summary for lists
  offerSummary: 'offerId,type,cryptocurrency.symbol,fiat.currency,rate,minAmount,maxAmount,status',
  
  // Chat message basics
  messageBasic: 'content,sender,timestamp,type',
  
  // Admin dashboard stats
  dashboardStats: 'statistics.users.total,statistics.trades.active,statistics.disputes.pending,statistics.trades.volume'
};

/**
 * Get predefined field set
 * @param {String} setName - Name of the field set
 * @returns {String} Comma-separated field list
 */
const getFieldSet = (setName) => {
  return fieldSets[setName] || '';
};

/**
 * Optimize response for mobile clients
 * Removes heavy fields that mobile apps typically don't need
 */
const mobileOptimize = (data) => {
  if (!data) return data;
  
  const removeFields = (obj) => {
    if (!obj || typeof obj !== 'object') return obj;
    
    // Remove heavy fields
    delete obj.timeline;
    delete obj.escrow;
    delete obj.verification;
    delete obj.security;
    delete obj.__v;
    
    // Recursively clean nested objects
    Object.keys(obj).forEach(key => {
      if (Array.isArray(obj[key])) {
        obj[key] = obj[key].map(removeFields);
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        obj[key] = removeFields(obj[key]);
      }
    });
    
    return obj;
  };
  
  return removeFields(JSON.parse(JSON.stringify(data)));
};

/**
 * Middleware to automatically optimize responses for mobile clients
 */
const mobileOptimizeMiddleware = (req, res, next) => {
  const userAgent = req.get('User-Agent') || '';
  const isMobile = /mobile|android|ios/i.test(userAgent) || req.query.mobile === 'true';
  
  if (isMobile) {
    const originalJson = res.json;
    
    res.json = function(data) {
      const optimizedData = mobileOptimize(data);
      return originalJson.call(this, optimizedData);
    };
  }
  
  next();
};

module.exports = {
  filterFields,
  filterArray,
  responseFilterMiddleware,
  mobileOptimizeMiddleware,
  fieldSets,
  getFieldSet,
  mobileOptimize
};
