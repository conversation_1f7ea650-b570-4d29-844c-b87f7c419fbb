
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.54% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>627/5430</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.32% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>160/2184</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">10.59% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>80/755</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.57% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>618/5341</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="authService.js"><a href="authService.js.html">authService.js</a></td>
	<td data-value="31.49" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 31%"></div><div class="cover-empty" style="width: 69%"></div></div>
	</td>
	<td data-value="31.49" class="pct low">31.49%</td>
	<td data-value="127" class="abs low">40/127</td>
	<td data-value="33.8" class="pct low">33.8%</td>
	<td data-value="71" class="abs low">24/71</td>
	<td data-value="36.36" class="pct low">36.36%</td>
	<td data-value="11" class="abs low">4/11</td>
	<td data-value="31.49" class="pct low">31.49%</td>
	<td data-value="127" class="abs low">40/127</td>
	</tr>

<tr>
	<td class="file low" data-value="backgroundJobService.js"><a href="backgroundJobService.js.html">backgroundJobService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="134" class="abs low">0/134</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="65" class="abs low">0/65</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="129" class="abs low">0/129</td>
	</tr>

<tr>
	<td class="file low" data-value="businessFlowMonitoring.js"><a href="businessFlowMonitoring.js.html">businessFlowMonitoring.js</a></td>
	<td data-value="5.6" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.6" class="pct low">5.6%</td>
	<td data-value="214" class="abs low">12/214</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="53" class="abs low">0/53</td>
	<td data-value="3.12" class="pct low">3.12%</td>
	<td data-value="32" class="abs low">1/32</td>
	<td data-value="5.6" class="pct low">5.6%</td>
	<td data-value="214" class="abs low">12/214</td>
	</tr>

<tr>
	<td class="file low" data-value="chatScalabilityService.js"><a href="chatScalabilityService.js.html">chatScalabilityService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="289" class="abs low">0/289</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="147" class="abs low">0/147</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="33" class="abs low">0/33</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="286" class="abs low">0/286</td>
	</tr>

<tr>
	<td class="file low" data-value="chatService.js"><a href="chatService.js.html">chatService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="173" class="abs low">0/173</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="49" class="abs low">0/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="31" class="abs low">0/31</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="171" class="abs low">0/171</td>
	</tr>

<tr>
	<td class="file medium" data-value="circuitBreakerMonitoring.js"><a href="circuitBreakerMonitoring.js.html">circuitBreakerMonitoring.js</a></td>
	<td data-value="53.9" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 53%"></div><div class="cover-empty" style="width: 47%"></div></div>
	</td>
	<td data-value="53.9" class="pct medium">53.9%</td>
	<td data-value="282" class="abs medium">152/282</td>
	<td data-value="42.14" class="pct low">42.14%</td>
	<td data-value="121" class="abs low">51/121</td>
	<td data-value="55.55" class="pct medium">55.55%</td>
	<td data-value="36" class="abs medium">20/36</td>
	<td data-value="53.62" class="pct medium">53.62%</td>
	<td data-value="276" class="abs medium">148/276</td>
	</tr>

<tr>
	<td class="file low" data-value="connectionPoolManager.js"><a href="connectionPoolManager.js.html">connectionPoolManager.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="197" class="abs low">0/197</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="76" class="abs low">0/76</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="196" class="abs low">0/196</td>
	</tr>

<tr>
	<td class="file low" data-value="dataConsistency.js"><a href="dataConsistency.js.html">dataConsistency.js</a></td>
	<td data-value="25.19" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 25%"></div><div class="cover-empty" style="width: 75%"></div></div>
	</td>
	<td data-value="25.19" class="pct low">25.19%</td>
	<td data-value="258" class="abs low">65/258</td>
	<td data-value="14.81" class="pct low">14.81%</td>
	<td data-value="54" class="abs low">8/54</td>
	<td data-value="41.37" class="pct low">41.37%</td>
	<td data-value="29" class="abs low">12/29</td>
	<td data-value="25.29" class="pct low">25.29%</td>
	<td data-value="257" class="abs low">65/257</td>
	</tr>

<tr>
	<td class="file low" data-value="deadLetterQueue.js"><a href="deadLetterQueue.js.html">deadLetterQueue.js</a></td>
	<td data-value="4.14" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 4%"></div><div class="cover-empty" style="width: 96%"></div></div>
	</td>
	<td data-value="4.14" class="pct low">4.14%</td>
	<td data-value="217" class="abs low">9/217</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="104" class="abs low">0/104</td>
	<td data-value="3.03" class="pct low">3.03%</td>
	<td data-value="33" class="abs low">1/33</td>
	<td data-value="4.2" class="pct low">4.2%</td>
	<td data-value="214" class="abs low">9/214</td>
	</tr>

<tr>
	<td class="file low" data-value="enhancedSocketService.js"><a href="enhancedSocketService.js.html">enhancedSocketService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="249" class="abs low">0/249</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="46" class="abs low">0/46</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="33" class="abs low">0/33</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="248" class="abs low">0/248</td>
	</tr>

<tr>
	<td class="file low" data-value="escrowService.js"><a href="escrowService.js.html">escrowService.js</a></td>
	<td data-value="7.28" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.28" class="pct low">7.28%</td>
	<td data-value="206" class="abs low">15/206</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="53" class="abs low">0/53</td>
	<td data-value="5.26" class="pct low">5.26%</td>
	<td data-value="19" class="abs low">1/19</td>
	<td data-value="7.31" class="pct low">7.31%</td>
	<td data-value="205" class="abs low">15/205</td>
	</tr>

<tr>
	<td class="file low" data-value="fileCleanupService.js"><a href="fileCleanupService.js.html">fileCleanupService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="191" class="abs low">0/191</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="79" class="abs low">0/79</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="189" class="abs low">0/189</td>
	</tr>

<tr>
	<td class="file low" data-value="fileSecurityService.js"><a href="fileSecurityService.js.html">fileSecurityService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="149" class="abs low">0/149</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="47" class="abs low">0/47</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="17" class="abs low">0/17</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="148" class="abs low">0/148</td>
	</tr>

<tr>
	<td class="file low" data-value="gracefulDegradation.js"><a href="gracefulDegradation.js.html">gracefulDegradation.js</a></td>
	<td data-value="6.69" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.69" class="pct low">6.69%</td>
	<td data-value="284" class="abs low">19/284</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="123" class="abs low">0/123</td>
	<td data-value="5" class="pct low">5%</td>
	<td data-value="40" class="abs low">2/40</td>
	<td data-value="6.73" class="pct low">6.73%</td>
	<td data-value="282" class="abs low">19/282</td>
	</tr>

<tr>
	<td class="file low" data-value="monitoringIntegrationService.js"><a href="monitoringIntegrationService.js.html">monitoringIntegrationService.js</a></td>
	<td data-value="6.02" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.02" class="pct low">6.02%</td>
	<td data-value="83" class="abs low">5/83</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="40" class="abs low">0/40</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="12" class="abs low">1/12</td>
	<td data-value="6.66" class="pct low">6.66%</td>
	<td data-value="75" class="abs low">5/75</td>
	</tr>

<tr>
	<td class="file low" data-value="notificationService.js"><a href="notificationService.js.html">notificationService.js</a></td>
	<td data-value="8.53" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8.53" class="pct low">8.53%</td>
	<td data-value="164" class="abs low">14/164</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="66" class="abs low">0/66</td>
	<td data-value="4.54" class="pct low">4.54%</td>
	<td data-value="22" class="abs low">1/22</td>
	<td data-value="8.58" class="pct low">8.58%</td>
	<td data-value="163" class="abs low">14/163</td>
	</tr>

<tr>
	<td class="file low" data-value="performanceMonitoringService.js"><a href="performanceMonitoringService.js.html">performanceMonitoringService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="196" class="abs low">0/196</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="88" class="abs low">0/88</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="37" class="abs low">0/37</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="194" class="abs low">0/194</td>
	</tr>

<tr>
	<td class="file low" data-value="performanceOptimization.js"><a href="performanceOptimization.js.html">performanceOptimization.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="107" class="abs low">0/107</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="62" class="abs low">0/62</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="103" class="abs low">0/103</td>
	</tr>

<tr>
	<td class="file low" data-value="priceService.js"><a href="priceService.js.html">priceService.js</a></td>
	<td data-value="13.1" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 13%"></div><div class="cover-empty" style="width: 87%"></div></div>
	</td>
	<td data-value="13.1" class="pct low">13.1%</td>
	<td data-value="145" class="abs low">19/145</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="69" class="abs low">0/69</td>
	<td data-value="6.66" class="pct low">6.66%</td>
	<td data-value="30" class="abs low">2/30</td>
	<td data-value="13.38" class="pct low">13.38%</td>
	<td data-value="142" class="abs low">19/142</td>
	</tr>

<tr>
	<td class="file low" data-value="queryOptimization.js"><a href="queryOptimization.js.html">queryOptimization.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="115" class="abs low">0/115</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="74" class="abs low">0/74</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="111" class="abs low">0/111</td>
	</tr>

<tr>
	<td class="file low" data-value="rateLimitingService.js"><a href="rateLimitingService.js.html">rateLimitingService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="152" class="abs low">0/152</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="65" class="abs low">0/65</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="143" class="abs low">0/143</td>
	</tr>

<tr>
	<td class="file low" data-value="realTimeScalabilityService.js"><a href="realTimeScalabilityService.js.html">realTimeScalabilityService.js</a></td>
	<td data-value="6.81" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.81" class="pct low">6.81%</td>
	<td data-value="220" class="abs low">15/220</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="50" class="abs low">0/50</td>
	<td data-value="2" class="pct low">2%</td>
	<td data-value="50" class="abs low">1/50</td>
	<td data-value="6.81" class="pct low">6.81%</td>
	<td data-value="220" class="abs low">15/220</td>
	</tr>

<tr>
	<td class="file low" data-value="serviceLayerPerformance.js"><a href="serviceLayerPerformance.js.html">serviceLayerPerformance.js</a></td>
	<td data-value="20.38" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 20%"></div><div class="cover-empty" style="width: 80%"></div></div>
	</td>
	<td data-value="20.38" class="pct low">20.38%</td>
	<td data-value="206" class="abs low">42/206</td>
	<td data-value="20.83" class="pct low">20.83%</td>
	<td data-value="72" class="abs low">15/72</td>
	<td data-value="18.91" class="pct low">18.91%</td>
	<td data-value="37" class="abs low">7/37</td>
	<td data-value="21.42" class="pct low">21.42%</td>
	<td data-value="196" class="abs low">42/196</td>
	</tr>

<tr>
	<td class="file low" data-value="socketRedisAdapter.js"><a href="socketRedisAdapter.js.html">socketRedisAdapter.js</a></td>
	<td data-value="7.75" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.75" class="pct low">7.75%</td>
	<td data-value="116" class="abs low">9/116</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="60" class="abs low">0/60</td>
	<td data-value="5.26" class="pct low">5.26%</td>
	<td data-value="19" class="abs low">1/19</td>
	<td data-value="7.75" class="pct low">7.75%</td>
	<td data-value="116" class="abs low">9/116</td>
	</tr>

<tr>
	<td class="file low" data-value="socketService.js"><a href="socketService.js.html">socketService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="63" class="abs low">0/63</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="63" class="abs low">0/63</td>
	</tr>

<tr>
	<td class="file low" data-value="tradingService.js"><a href="tradingService.js.html">tradingService.js</a></td>
	<td data-value="5.06" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.06" class="pct low">5.06%</td>
	<td data-value="237" class="abs low">12/237</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="127" class="abs low">0/127</td>
	<td data-value="5.26" class="pct low">5.26%</td>
	<td data-value="19" class="abs low">1/19</td>
	<td data-value="5.21" class="pct low">5.21%</td>
	<td data-value="230" class="abs low">12/230</td>
	</tr>

<tr>
	<td class="file low" data-value="transactionMonitoring.js"><a href="transactionMonitoring.js.html">transactionMonitoring.js</a></td>
	<td data-value="16.09" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.09" class="pct low">16.09%</td>
	<td data-value="205" class="abs low">33/205</td>
	<td data-value="2.7" class="pct low">2.7%</td>
	<td data-value="74" class="abs low">2/74</td>
	<td data-value="15.38" class="pct low">15.38%</td>
	<td data-value="26" class="abs low">4/26</td>
	<td data-value="16.17" class="pct low">16.17%</td>
	<td data-value="204" class="abs low">33/204</td>
	</tr>

<tr>
	<td class="file low" data-value="transactionService.js"><a href="transactionService.js.html">transactionService.js</a></td>
	<td data-value="8.4" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8.4" class="pct low">8.4%</td>
	<td data-value="119" class="abs low">10/119</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="74" class="abs low">0/74</td>
	<td data-value="6.66" class="pct low">6.66%</td>
	<td data-value="15" class="abs low">1/15</td>
	<td data-value="8.47" class="pct low">8.47%</td>
	<td data-value="118" class="abs low">10/118</td>
	</tr>

<tr>
	<td class="file low" data-value="walletService.js"><a href="walletService.js.html">walletService.js</a></td>
	<td data-value="46.98" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 46%"></div><div class="cover-empty" style="width: 54%"></div></div>
	</td>
	<td data-value="46.98" class="pct low">46.98%</td>
	<td data-value="332" class="abs low">156/332</td>
	<td data-value="38.21" class="pct low">38.21%</td>
	<td data-value="157" class="abs low">60/157</td>
	<td data-value="60.6" class="pct medium">60.6%</td>
	<td data-value="33" class="abs medium">20/33</td>
	<td data-value="47.04" class="pct low">47.04%</td>
	<td data-value="321" class="abs low">151/321</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-08T14:32:16.455Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    