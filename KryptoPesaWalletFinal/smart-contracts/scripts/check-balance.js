const { ethers } = require("hardhat");

async function main() {
  console.log("Checking wallet balance...");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Wallet Address:", deployer.address);
  
  const balance = await deployer.getBalance();
  console.log("Balance:", ethers.utils.formatEther(balance), "MATIC");
  console.log("Balance (wei):", balance.toString());
  
  if (balance.gt(0)) {
    console.log("✅ Wallet has sufficient balance for deployment");
  } else {
    console.log("❌ Wallet needs test MATIC from faucet");
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
