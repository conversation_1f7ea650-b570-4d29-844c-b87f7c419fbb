const redis = require('redis');
const logger = require('../utils/logger');

// Redis High Availability Manager
class RedisManager {
  constructor() {
    this.client = null;
    this.sentinelClient = null;
    this.memoryCache = new Map();
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 10;
    this.reconnectDelay = 1000;
    this.healthCheckInterval = null;
    this.cacheCleanupInterval = null;

    // Memory cache settings
    this.maxMemoryCacheSize = 1000;
    this.memoryCacheTTL = 300000; // 5 minutes
  }

  async initialize() {
    const isProduction = process.env.NODE_ENV === 'production';

    if (isProduction && process.env.REDIS_SENTINEL_HOSTS) {
      await this.initializeSentinel();
    } else {
      await this.initializeStandalone();
    }

    this.startHealthMonitoring();
    this.startCacheCleanup();
  }

  async initializeSentinel() {
    try {
      const sentinelHosts = process.env.REDIS_SENTINEL_HOSTS.split(',').map(host => {
        const [hostname, port] = host.trim().split(':');
        return { host: hostname, port: parseInt(port) || 26379 };
      });

      const sentinelConfig = {
        sentinels: sentinelHosts,
        name: process.env.REDIS_SENTINEL_MASTER_NAME || 'kryptopesa-master',
        retryDelayOnFailover: 100,
        enableOfflineQueue: false,
        maxRetriesPerRequest: 3,
        sentinelRetryDelayOnFailover: 100,
        sentinelMaxRetriesPerRequest: 3,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB) || 0
      };

      this.client = redis.createClient(sentinelConfig);
      await this.setupEventHandlers();
      await this.client.connect();

      logger.info('Redis Sentinel connection established', {
        sentinels: sentinelHosts.length,
        masterName: sentinelConfig.name
      });

    } catch (error) {
      logger.error('Redis Sentinel initialization failed:', error);
      await this.fallbackToMemoryCache();
    }
  }

  async initializeStandalone() {
    try {
      if (!process.env.REDIS_URL) {
        throw new Error('REDIS_URL environment variable is required');
      }
      const redisUrl = process.env.REDIS_URL;

      const config = {
        url: redisUrl,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB) || 0,
        retryDelayOnFailover: 100,
        enableOfflineQueue: false,
        maxRetriesPerRequest: 3,
        connectTimeout: 10000,
        lazyConnect: true,
        retry_strategy: (options) => {
          if (options.error && options.error.code === 'ECONNREFUSED') {
            logger.error('Redis server connection refused');
            return new Error('Redis server connection refused');
          }
          if (options.total_retry_time > 1000 * 60 * 60) {
            logger.error('Redis retry time exhausted');
            return new Error('Retry time exhausted');
          }
          if (options.attempt > this.maxConnectionAttempts) {
            logger.error('Redis max retry attempts reached');
            return undefined;
          }
          return Math.min(options.attempt * this.reconnectDelay, 30000);
        }
      };

      this.client = redis.createClient(config);
      await this.setupEventHandlers();
      await this.client.connect();

      logger.info('Redis standalone connection established');

    } catch (error) {
      logger.error('Redis standalone initialization failed:', error);
      await this.fallbackToMemoryCache();
    }
  }

  async setupEventHandlers() {
    this.client.on('error', async (err) => {
      logger.error('Redis Client Error:', err);
      this.isConnected = false;
      await this.handleConnectionError(err);
    });

    this.client.on('connect', () => {
      logger.info('Redis Client Connected');
      this.isConnected = true;
      this.connectionAttempts = 0;
    });

    this.client.on('ready', () => {
      logger.info('Redis Client Ready');
      this.isConnected = true;
    });

    this.client.on('end', () => {
      logger.warn('Redis Client Disconnected');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      logger.info('Redis Client Reconnecting...');
      this.connectionAttempts++;
    });
  }

  async handleConnectionError(error) {
    if (this.connectionAttempts >= this.maxConnectionAttempts) {
      logger.error('Max Redis connection attempts reached, falling back to memory cache');
      await this.fallbackToMemoryCache();
      return;
    }

    // Exponential backoff for reconnection
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.connectionAttempts), 30000);

    setTimeout(async () => {
      try {
        await this.client.connect();
      } catch (reconnectError) {
        logger.error('Redis reconnection failed:', reconnectError);
      }
    }, delay);
  }

  async fallbackToMemoryCache() {
    this.isConnected = false;
    logger.warn('Using memory cache fallback for Redis operations');

    // Ensure memory cache is initialized
    if (!this.memoryCache) {
      this.memoryCache = new Map();
    }
  }

  async setCache(key, value, expireInSeconds = 3600) {
    try {
      if (this.isConnected && this.client && this.client.isReady) {
        await this.client.setEx(key, expireInSeconds, JSON.stringify(value));
        return true;
      } else {
        // Use memory cache fallback
        this.setMemoryCache(key, value, expireInSeconds * 1000);
        return true;
      }
    } catch (error) {
      logger.error('Redis set error:', error);
      // Fallback to memory cache
      this.setMemoryCache(key, value, expireInSeconds * 1000);
      return false;
    }
  }

  async getCache(key) {
    try {
      if (this.isConnected && this.client && this.client.isReady) {
        const value = await this.client.get(key);
        return value ? JSON.parse(value) : null;
      } else {
        // Use memory cache fallback
        return this.getMemoryCache(key);
      }
    } catch (error) {
      logger.error('Redis get error:', error);
      // Fallback to memory cache
      return this.getMemoryCache(key);
    }
  }

  async deleteCache(key) {
    try {
      if (this.isConnected && this.client && this.client.isReady) {
        await this.client.del(key);
      }
      // Also delete from memory cache
      this.memoryCache.delete(key);
    } catch (error) {
      logger.error('Redis delete error:', error);
      // Still delete from memory cache
      this.memoryCache.delete(key);
    }
  }

  async flushCache() {
    try {
      if (this.isConnected && this.client && this.client.isReady) {
        await this.client.flushAll();
      }
      // Also clear memory cache
      this.memoryCache.clear();
    } catch (error) {
      logger.error('Redis flush error:', error);
      // Still clear memory cache
      this.memoryCache.clear();
    }
  }

  // Memory cache methods
  setMemoryCache(key, value, ttl) {
    // Implement LRU eviction if cache is full
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }

    this.memoryCache.set(key, {
      value,
      expires: Date.now() + ttl
    });
  }

  getMemoryCache(key) {
    const item = this.memoryCache.get(key);
    if (!item) return null;

    if (Date.now() > item.expires) {
      this.memoryCache.delete(key);
      return null;
    }

    return item.value;
  }

  // Health monitoring
  startHealthMonitoring() {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 30000); // Check every 30 seconds
  }

  async performHealthCheck() {
    try {
      if (this.client && this.client.isReady) {
        const start = Date.now();
        await this.client.ping();
        const responseTime = Date.now() - start;

        if (responseTime > 1000) {
          logger.warn(`Redis slow response: ${responseTime}ms`);
        }

        this.isConnected = true;
      } else {
        this.isConnected = false;
      }
    } catch (error) {
      logger.error('Redis health check failed:', error);
      this.isConnected = false;
    }
  }

  // Cache cleanup
  startCacheCleanup() {
    this.cacheCleanupInterval = setInterval(() => {
      this.cleanupMemoryCache();
    }, 60000); // Cleanup every minute
  }

  cleanupMemoryCache() {
    const now = Date.now();
    for (const [key, item] of this.memoryCache.entries()) {
      if (now > item.expires) {
        this.memoryCache.delete(key);
      }
    }
  }

  // Get connection status
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      connectionAttempts: this.connectionAttempts,
      memoryCacheSize: this.memoryCache.size,
      clientReady: this.client ? this.client.isReady : false
    };
  }

  // Graceful shutdown
  async shutdown() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval);
    }

    if (this.client) {
      try {
        await this.client.quit();
      } catch (error) {
        logger.error('Error during Redis shutdown:', error);
      }
    }

    this.memoryCache.clear();
    logger.info('Redis manager shutdown complete');
  }
}

// Create singleton instance
const redisManager = new RedisManager();

// Legacy compatibility functions
const connectRedis = async () => {
  await redisManager.initialize();
};

const getRedisClient = () => {
  return redisManager.client;
};

const setCache = async (key, value, expireInSeconds = 3600) => {
  return await redisManager.setCache(key, value, expireInSeconds);
};

const getCache = async (key) => {
  return await redisManager.getCache(key);
};

const deleteCache = async (key) => {
  return await redisManager.deleteCache(key);
};

const flushCache = async () => {
  return await redisManager.flushCache();
};

module.exports = {
  connectRedis,
  getRedisClient,
  setCache,
  getCache,
  deleteCache,
  flushCache,
  redisManager
};
