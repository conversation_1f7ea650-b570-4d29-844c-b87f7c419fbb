#!/usr/bin/env node

/**
 * Bundle analysis script to identify optimization opportunities
 * Analyzes dependencies and suggests optimizations for better performance
 */

const fs = require('fs');
const path = require('path');

// Projects to analyze
const projects = [
  {
    name: 'Mobile App',
    path: 'mobile/package.json',
    type: 'react-native'
  },
  {
    name: 'Admin Dashboard',
    path: 'admin-dashboard/package.json',
    type: 'react'
  },
  {
    name: 'Backend',
    path: 'backend/package.json',
    type: 'node'
  }
];

// Known heavy dependencies and their lighter alternatives
const heavyDependencies = {
  'moment': {
    alternative: 'date-fns',
    savings: '~200KB',
    reason: 'Moment.js is deprecated and much larger than date-fns'
  },
  'lodash': {
    alternative: 'lodash-es (with tree-shaking)',
    savings: '~400KB',
    reason: 'Use lodash-es with tree-shaking to import only needed functions'
  },
  'axios': {
    alternative: 'fetch API',
    savings: '~50KB',
    reason: 'Native fetch API is sufficient for most use cases'
  },
  'react-moment': {
    alternative: 'date-fns + custom hooks',
    savings: '~250KB',
    reason: 'Combines moment.js overhead with React wrapper'
  }
};

// Security vulnerabilities to check
const vulnerablePackages = [
  'moment',
  'lodash',
  'axios',
  'react-scripts',
  'webpack'
];

function analyzePackageJson(projectPath) {
  const fullPath = path.join(__dirname, '..', projectPath);
  
  if (!fs.existsSync(fullPath)) {
    return null;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  return {
    name: packageJson.name,
    dependencies,
    scripts: packageJson.scripts || {}
  };
}

function findHeavyDependencies(dependencies) {
  const found = [];
  
  Object.keys(dependencies).forEach(dep => {
    if (heavyDependencies[dep]) {
      found.push({
        name: dep,
        version: dependencies[dep],
        ...heavyDependencies[dep]
      });
    }
  });
  
  return found;
}

function findOutdatedDependencies(dependencies) {
  const outdated = [];
  
  Object.entries(dependencies).forEach(([name, version]) => {
    // Check for very old versions (simplified check)
    if (version.includes('^0.') || version.includes('~0.')) {
      outdated.push({ name, version, reason: 'Very old version (0.x)' });
    } else if (version.includes('^1.') || version.includes('~1.')) {
      outdated.push({ name, version, reason: 'Old version (1.x) - check for updates' });
    }
  });
  
  return outdated;
}

function findVulnerableDependencies(dependencies) {
  const vulnerable = [];
  
  vulnerablePackages.forEach(pkg => {
    if (dependencies[pkg]) {
      vulnerable.push({
        name: pkg,
        version: dependencies[pkg],
        reason: 'Known to have security vulnerabilities in older versions'
      });
    }
  });
  
  return vulnerable;
}

function generateOptimizationSuggestions(projectType, dependencies) {
  const suggestions = [];
  
  // React Native specific optimizations
  if (projectType === 'react-native') {
    suggestions.push({
      type: 'Bundle Size',
      suggestion: 'Enable Hermes engine for better performance',
      implementation: 'Set "enableHermes": true in android/app/build.gradle'
    });
    
    suggestions.push({
      type: 'Bundle Size',
      suggestion: 'Use react-native-bundle-visualizer to analyze bundle',
      implementation: 'npx react-native-bundle-visualizer'
    });
  }
  
  // React specific optimizations
  if (projectType === 'react') {
    suggestions.push({
      type: 'Bundle Size',
      suggestion: 'Use webpack-bundle-analyzer to analyze bundle',
      implementation: 'npm install --save-dev webpack-bundle-analyzer'
    });
    
    suggestions.push({
      type: 'Performance',
      suggestion: 'Implement code splitting with React.lazy',
      implementation: 'Split large components into separate chunks'
    });
  }
  
  // General optimizations
  suggestions.push({
    type: 'Dependencies',
    suggestion: 'Remove unused dependencies',
    implementation: 'Use depcheck to find unused dependencies: npx depcheck'
  });
  
  return suggestions;
}

function printAnalysis() {
  // console.log('🔍 KryptoPesa Bundle Analysis Report\n');
  // console.log('=' .repeat(60));
  
  projects.forEach(project => {
    const analysis = analyzePackageJson(project.path);
    
    if (!analysis) {
      // console.log(`\n❌ ${project.name}: package.json not found`);
      return;
    }
    
    // console.log(`\n📦 ${project.name} (${project.type})`);
    // console.log('-'.repeat(40));
    
    const dependencyCount = Object.keys(analysis.dependencies).length;
    // console.log(`Total dependencies: ${dependencyCount}`);
    
    // Heavy dependencies
    const heavy = findHeavyDependencies(analysis.dependencies);
    if (heavy.length > 0) {
      // console.log('\n⚠️  Heavy Dependencies:');
      heavy.forEach(dep => {
        // console.log(`  • ${dep.name} (${dep.version})`);
        // console.log(`    Alternative: ${dep.alternative}`);
        // console.log(`    Potential savings: ${dep.savings}`);
        // console.log(`    Reason: ${dep.reason}\n`);
      });
    }
    
    // Outdated dependencies
    const outdated = findOutdatedDependencies(analysis.dependencies);
    if (outdated.length > 0) {
      // console.log('\n📅 Potentially Outdated Dependencies:');
      outdated.forEach(dep => {
        // console.log(`  • ${dep.name} (${dep.version}) - ${dep.reason}`);
      });
    }
    
    // Vulnerable dependencies
    const vulnerable = findVulnerableDependencies(analysis.dependencies);
    if (vulnerable.length > 0) {
      // console.log('\n🔒 Security Concerns:');
      vulnerable.forEach(dep => {
        // console.log(`  • ${dep.name} (${dep.version}) - ${dep.reason}`);
      });
    }
    
    // Optimization suggestions
    const suggestions = generateOptimizationSuggestions(project.type, analysis.dependencies);
    if (suggestions.length > 0) {
      // console.log('\n💡 Optimization Suggestions:');
      suggestions.forEach(suggestion => {
        // console.log(`  • [${suggestion.type}] ${suggestion.suggestion}`);
        // console.log(`    Implementation: ${suggestion.implementation}\n`);
      });
    }
  });
  
  // console.log('\n🎯 Priority Actions:');
  // console.log('1. Replace moment.js with date-fns (if found)');
  // console.log('2. Enable tree-shaking for lodash');
  // console.log('3. Run security audit: npm audit');
  // console.log('4. Analyze bundle sizes with appropriate tools');
  // console.log('5. Remove unused dependencies with depcheck');
  
  // console.log('\n✅ Bundle optimization analysis complete!');
}

if (require.main === module) {
  printAnalysis();
}

module.exports = {
  analyzePackageJson,
  findHeavyDependencies,
  findOutdatedDependencies,
  findVulnerableDependencies,
  generateOptimizationSuggestions
};
