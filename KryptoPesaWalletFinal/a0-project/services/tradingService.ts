import { API_ENDPOINTS, ApiResponse, PaginatedResponse } from './api';

// Lazy import for apiClient to avoid early initialization
const getApiClient = () => {
  const { apiClient } = require('./api');
  return apiClient;
};

// East African Trading Types
export type EastAfricanCurrency = 'KES' | 'TZS' | 'UGX' | 'RWF' | 'USD';
export type CryptoCurrency = 'USDT' | 'USDC' | 'DAI' | 'BTC' | 'ETH' | 'MATIC';
export type PaymentMethod = 'mpesa' | 'airtel_money' | 'bank_transfer' | 'cash';
export type EastAfricanCountry = 'KE' | 'TZ' | 'UG' | 'RW';

export interface Offer {
  _id: string;
  type: 'buy' | 'sell';
  cryptocurrency: {
    type: CryptoCurrency;
    minAmount: number;
    maxAmount: number;
    availableAmount: number;
  };
  fiat: {
    currency: EastAfricanCurrency;
    priceType: 'fixed' | 'market' | 'margin';
    fixedPrice?: number;
    marginPercentage?: number;
    currentPrice: number;
  };
  paymentMethods: PaymentMethod[];
  location: {
    country: EastAfricanCountry;
    city?: string;
  };
  terms: {
    timeLimit: number; // minutes
    instructions?: string;
    minimumReputation?: number;
  };
  trader: {
    _id: string;
    username: string;
    reputation: number;
    totalTrades: number;
    completionRate: number;
    isOnline: boolean;
  };
  status: 'active' | 'paused' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface Trade {
  _id: string;
  offerId: string;
  buyer: {
    _id: string;
    username: string;
    walletAddress: string;
  };
  seller: {
    _id: string;
    username: string;
    walletAddress: string;
  };
  cryptocurrency: {
    type: CryptoCurrency;
    amount: number;
    networkFee: number;
  };
  fiat: {
    currency: EastAfricanCurrency;
    amount: number;
    exchangeRate: number;
  };
  paymentMethod: PaymentMethod;
  paymentDetails: {
    accountNumber?: string;
    accountName?: string;
    bankName?: string;
    phoneNumber?: string;
  };
  status: 'pending' | 'funded' | 'payment_sent' | 'payment_confirmed' | 'completed' | 'disputed' | 'cancelled';
  timeline: Array<{
    status: string;
    timestamp: string;
    note?: string;
  }>;
  escrow: {
    address: string;
    txHash?: string;
    isReleased: boolean;
  };
  dispute?: {
    _id: string;
    reason: string;
    status: string;
  };
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface OfferFilters {
  type?: 'buy' | 'sell';
  cryptocurrency?: CryptoCurrency;
  fiatCurrency?: EastAfricanCurrency;
  country?: EastAfricanCountry;
  paymentMethod?: PaymentMethod;
  minAmount?: number;
  maxAmount?: number;
  sortBy?: 'price' | 'reputation' | 'created' | 'volume';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface CreateOfferData {
  type: 'buy' | 'sell';
  cryptocurrency: {
    type: CryptoCurrency;
    minAmount: number;
    maxAmount: number;
    availableAmount: number;
  };
  fiat: {
    currency: EastAfricanCurrency;
    priceType: 'fixed' | 'market' | 'margin';
    fixedPrice?: number;
    marginPercentage?: number;
  };
  paymentMethods: PaymentMethod[];
  location: {
    country: EastAfricanCountry;
    city?: string;
  };
  terms: {
    timeLimit: number;
    instructions?: string;
    minimumReputation?: number;
  };
}

export interface CreateTradeData {
  offerId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDetails: {
    accountNumber?: string;
    accountName?: string;
    bankName?: string;
    phoneNumber?: string;
  };
}

// Trading Service Class
class TradingService {
  /**
   * Get all offers with filters
   */
  async getOffers(filters?: OfferFilters): Promise<PaginatedResponse<Offer>> {
    try {
      const response = await getApiClient().get<Offer[]>(
        API_ENDPOINTS.OFFERS.LIST,
        filters
      );

      return response as PaginatedResponse<Offer>;
    } catch (error) {
      console.error('Get offers error:', error);

      // Return mock offers data when API fails
      const mockOffers: Offer[] = [
        {
          _id: 'offer_1',
          type: 'sell',
          cryptocurrency: {
            type: 'USDT',
            minAmount: 100,
            maxAmount: 10000,
            availableAmount: 5000,
          },
          fiat: {
            currency: 'KES',
            priceType: 'margin',
            marginPercentage: -2.5,
            currentPrice: 132.50,
          },
          paymentMethods: ['mpesa', 'bank_transfer'],
          location: {
            country: 'KE',
            city: 'Nairobi',
          },
          terms: {
            timeLimit: 30,
            instructions: 'Fast payment required. M-Pesa preferred.',
            minimumReputation: 80,
          },
          trader: {
            _id: 'trader_1',
            username: 'KenyaCryptoTrader',
            reputation: 95,
            totalTrades: 247,
            completionRate: 98.5,
            isOnline: true,
          },
          status: 'active',
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          updatedAt: new Date(Date.now() - 1800000).toISOString(),
        },
        {
          _id: 'offer_2',
          type: 'buy',
          cryptocurrency: {
            type: 'BTC',
            minAmount: 0.001,
            maxAmount: 0.1,
            availableAmount: 0.05,
          },
          fiat: {
            currency: 'TZS',
            priceType: 'fixed',
            fixedPrice: *********,
            currentPrice: *********,
          },
          paymentMethods: ['airtel_money', 'bank_transfer'],
          location: {
            country: 'TZ',
            city: 'Dar es Salaam',
          },
          terms: {
            timeLimit: 45,
            instructions: 'Bank transfer only for amounts above 1M TZS.',
            minimumReputation: 70,
          },
          trader: {
            _id: 'trader_2',
            username: 'TanzaniaBTCBuyer',
            reputation: 88,
            totalTrades: 156,
            completionRate: 96.2,
            isOnline: false,
          },
          status: 'active',
          createdAt: new Date(Date.now() - 7200000).toISOString(),
          updatedAt: new Date(Date.now() - 3600000).toISOString(),
        },
        {
          _id: 'offer_3',
          type: 'sell',
          cryptocurrency: {
            type: 'ETH',
            minAmount: 0.01,
            maxAmount: 2,
            availableAmount: 0.8,
          },
          fiat: {
            currency: 'UGX',
            priceType: 'market',
            currentPrice: 9250000,
          },
          paymentMethods: ['bank_transfer', 'cash'],
          location: {
            country: 'UG',
            city: 'Kampala',
          },
          terms: {
            timeLimit: 60,
            instructions: 'Cash trades in Kampala CBD only. Bank transfers accepted.',
          },
          trader: {
            _id: 'trader_3',
            username: 'UgandaETHSeller',
            reputation: 92,
            totalTrades: 89,
            completionRate: 97.8,
            isOnline: true,
          },
          status: 'active',
          createdAt: new Date(Date.now() - ********).toISOString(),
          updatedAt: new Date(Date.now() - 5400000).toISOString(),
        },
      ];

      // Filter mock offers based on provided filters
      let filteredOffers = mockOffers;

      if (filters) {
        if (filters.cryptocurrency) {
          filteredOffers = filteredOffers.filter(offer =>
            offer.cryptocurrency.type === filters.cryptocurrency
          );
        }
        if (filters.fiatCurrency) {
          filteredOffers = filteredOffers.filter(offer =>
            offer.fiat.currency === filters.fiatCurrency
          );
        }
        if (filters.country) {
          filteredOffers = filteredOffers.filter(offer =>
            offer.location.country === filters.country
          );
        }
        if (filters.type) {
          filteredOffers = filteredOffers.filter(offer =>
            offer.type === filters.type
          );
        }
      }

      return {
        success: true,
        data: filteredOffers,
        pagination: {
          total: filteredOffers.length,
          page: 1,
          limit: filters?.limit || 20,
          totalPages: 1,
        },
        message: 'Mock offers data loaded',
      } as PaginatedResponse<Offer>;
    }
  }

  /**
   * Get specific offer by ID
   */
  async getOffer(offerId: string): Promise<Offer> {
    try {
      const response = await getApiClient().get<Offer>(
        API_ENDPOINTS.OFFERS.GET(offerId)
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get offer');
      }
    } catch (error) {
      console.error('Get offer error:', error);
      throw error;
    }
  }

  /**
   * Create new offer
   */
  async createOffer(offerData: CreateOfferData): Promise<Offer> {
    try {
      const response = await getApiClient().post<Offer>(
        API_ENDPOINTS.OFFERS.CREATE,
        offerData
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create offer');
      }
    } catch (error) {
      console.error('Create offer error:', error);

      // Return mock created offer when API fails
      const mockOffer: Offer = {
        _id: `offer_${Date.now()}`,
        type: offerData.type,
        cryptocurrency: offerData.cryptocurrency,
        fiat: {
          ...offerData.fiat,
          currentPrice: offerData.fiat.fixedPrice || 130.50, // Default price
        },
        paymentMethods: offerData.paymentMethods,
        location: offerData.location,
        terms: offerData.terms,
        trader: {
          _id: 'current_user',
          username: 'YourUsername',
          reputation: 85,
          totalTrades: 12,
          completionRate: 95.0,
          isOnline: true,
        },
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      console.log('Mock offer created:', mockOffer);
      return mockOffer;
    }
  }

  /**
   * Update existing offer
   */
  async updateOffer(offerId: string, updateData: Partial<CreateOfferData>): Promise<Offer> {
    try {
      const response = await getApiClient().put<Offer>(
        API_ENDPOINTS.OFFERS.UPDATE(offerId),
        updateData
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update offer');
      }
    } catch (error) {
      console.error('Update offer error:', error);
      throw error;
    }
  }

  /**
   * Delete offer
   */
  async deleteOffer(offerId: string): Promise<void> {
    try {
      const response = await getApiClient().delete(
        API_ENDPOINTS.OFFERS.DELETE(offerId)
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to delete offer');
      }
    } catch (error) {
      console.error('Delete offer error:', error);
      throw error;
    }
  }

  /**
   * Create new trade
   */
  async createTrade(tradeData: CreateTradeData): Promise<Trade> {
    try {
      const response = await getApiClient().post<Trade>(
        API_ENDPOINTS.TRADES.CREATE,
        tradeData
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create trade');
      }
    } catch (error) {
      console.error('Create trade error:', error);
      throw error;
    }
  }

  /**
   * Get specific trade by ID
   */
  async getTrade(tradeId: string): Promise<Trade> {
    try {
      const response = await getApiClient().get<Trade>(
        API_ENDPOINTS.TRADES.GET(tradeId)
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get trade');
      }
    } catch (error) {
      console.error('Get trade error:', error);
      throw error;
    }
  }

  /**
   * Get active trades for current user
   */
  async getActiveTrades(): Promise<Trade[]> {
    try {
      const response = await getApiClient().get<Trade[]>(
        API_ENDPOINTS.TRADES.ACTIVE
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get active trades');
      }
    } catch (error) {
      console.error('Get active trades error:', error);
      throw error;
    }
  }

  /**
   * Get trade history for current user
   */
  async getTradeHistory(limit?: number, offset?: number): Promise<PaginatedResponse<Trade>> {
    try {
      const response = await getApiClient().get<Trade[]>(
        API_ENDPOINTS.TRADES.HISTORY,
        { limit, offset }
      );

      return response as PaginatedResponse<Trade>;
    } catch (error) {
      console.error('Get trade history error:', error);
      throw error;
    }
  }

  /**
   * Update trade status
   */
  async updateTradeStatus(tradeId: string, status: Trade['status'], note?: string): Promise<Trade> {
    try {
      const response = await getApiClient().put<Trade>(
        API_ENDPOINTS.TRADES.UPDATE(tradeId),
        { status, note }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update trade status');
      }
    } catch (error) {
      console.error('Update trade status error:', error);
      throw error;
    }
  }

  /**
   * Cancel trade
   */
  async cancelTrade(tradeId: string, reason?: string): Promise<Trade> {
    try {
      const response = await getApiClient().put<Trade>(
        API_ENDPOINTS.TRADES.UPDATE(tradeId),
        { status: 'cancelled', note: reason }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to cancel trade');
      }
    } catch (error) {
      console.error('Cancel trade error:', error);
      throw error;
    }
  }

  /**
   * Get user trade statistics
   */
  async getUserTradeStats(): Promise<any> {
    try {
      const response = await getApiClient().get(
        API_ENDPOINTS.TRADES.STATS
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        // Return mock data if API fails
        return {
          totalTrades: 0,
          completedTrades: 0,
          activeTrades: 0,
          totalVolume: 0,
          averageRating: 0,
          activeOffers: 0,
          volumeByCurrency: {},
          lastUpdated: new Date().toISOString()
        };
      }
    } catch (error) {
      console.error('Get user trade stats error:', error);
      // Return mock data on error
      return {
        totalTrades: 0,
        completedTrades: 0,
        activeTrades: 0,
        totalVolume: 0,
        averageRating: 0,
        activeOffers: 0,
        volumeByCurrency: {},
        lastUpdated: new Date().toISOString()
      };
    }
  }
}

// Create and export service instance
export const tradingService = new TradingService();

// East African market helpers
export const eastAfricanHelpers = {
  /**
   * Get currency symbol for East African currencies
   */
  getCurrencySymbol(currency: EastAfricanCurrency): string {
    const symbols = {
      KES: 'KSh',
      TZS: 'TSh',
      UGX: 'USh',
      RWF: 'RWF',
      USD: '$',
    };
    return symbols[currency];
  },

  /**
   * Get country name from code
   */
  getCountryName(countryCode: EastAfricanCountry): string {
    const countries = {
      KE: 'Kenya',
      TZ: 'Tanzania',
      UG: 'Uganda',
      RW: 'Rwanda',
    };
    return countries[countryCode];
  },

  /**
   * Get payment method display name
   */
  getPaymentMethodName(method: PaymentMethod): string {
    const names = {
      mpesa: 'M-Pesa',
      airtel_money: 'Airtel Money',
      bank_transfer: 'Bank Transfer',
      cash: 'Cash',
    };
    return names[method];
  },

  /**
   * Format currency amount with proper symbols
   */
  formatCurrencyAmount(amount: number, currency: EastAfricanCurrency): string {
    const symbol = this.getCurrencySymbol(currency);
    return `${symbol} ${amount.toLocaleString()}`;
  },

  /**
   * Get available payment methods by country
   */
  getPaymentMethodsByCountry(country: EastAfricanCountry): PaymentMethod[] {
    const methodsByCountry = {
      KE: ['mpesa', 'airtel_money', 'bank_transfer', 'cash'] as PaymentMethod[],
      TZ: ['mpesa', 'airtel_money', 'bank_transfer', 'cash'] as PaymentMethod[],
      UG: ['airtel_money', 'bank_transfer', 'cash'] as PaymentMethod[],
      RW: ['airtel_money', 'bank_transfer', 'cash'] as PaymentMethod[],
    };
    return methodsByCountry[country];
  },

  /**
   * Validate payment details based on method
   */
  validatePaymentDetails(method: PaymentMethod, details: any): boolean {
    switch (method) {
      case 'mpesa':
      case 'airtel_money':
        return !!(details.phoneNumber && /^\+254\d{9}$|^\+255\d{9}$|^\+256\d{9}$|^\+250\d{9}$/.test(details.phoneNumber));
      case 'bank_transfer':
        return !!(details.accountNumber && details.accountName && details.bankName);
      case 'cash':
        return true; // No specific validation for cash
      default:
        return false;
    }
  },
};

export default tradingService;
