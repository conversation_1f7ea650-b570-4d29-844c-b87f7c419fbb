const mongoose = require('mongoose');

const walletSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  addresses: {
    ethereum: {
      address: {
        type: String,
        required: true,
        unique: true,
        match: /^0x[a-fA-F0-9]{40}$/
      },
      publicKey: String,
      derivationPath: {
        type: String,
        default: "m/44'/60'/0'/0/0"
      }
    },
    bitcoin: {
      address: {
        type: String,
        unique: true,
        sparse: true
      },
      publicKey: String,
      derivationPath: {
        type: String,
        default: "m/44'/0'/0'/0/0"
      }
    }
  },
  balances: [{
    symbol: {
      type: String,
      required: true,
      enum: ['ETH', 'MATIC', 'USDT', 'USDC', 'DAI', 'BTC']
    },
    contractAddress: String,
    network: {
      type: String,
      required: true,
      enum: ['ethereum', 'polygon', 'bitcoin']
    },
    balance: {
      type: String,
      default: '0'
    },
    decimals: {
      type: Number,
      default: 18
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  }],
  transactions: [{
    hash: {
      type: String,
      required: true
    },
    type: {
      type: String,
      enum: ['send', 'receive', 'escrow_fund', 'escrow_release'],
      required: true
    },
    symbol: String,
    amount: String,
    from: String,
    to: String,
    network: String,
    blockNumber: Number,
    gasUsed: Number,
    gasPrice: String,
    fee: String,
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'failed'],
      default: 'pending'
    },
    confirmations: {
      type: Number,
      default: 0
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    relatedTrade: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Trade'
    }
  }],
  security: {
    encryptedMnemonic: String, // Client-side encrypted
    mnemonicHash: String, // Server-side hash for verification
    backupCompleted: {
      type: Boolean,
      default: false
    },
    backupDate: Date,
    lastAccessDate: {
      type: Date,
      default: Date.now
    }
  },
  preferences: {
    defaultNetwork: {
      type: String,
      enum: ['ethereum', 'polygon'],
      default: 'polygon'
    },
    autoRefreshBalances: {
      type: Boolean,
      default: true
    },
    transactionNotifications: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      // Remove sensitive wallet data from JSON output
      delete ret.security?.encryptedMnemonic;
      delete ret.security?.mnemonicHash;
      delete ret.addresses?.ethereum?.publicKey;
      delete ret.addresses?.bitcoin?.publicKey;
      delete ret.__v;

      // Remove sensitive transaction data
      if (ret.transactions) {
        ret.transactions = ret.transactions.map(tx => {
          const safeTx = { ...tx };
          // Keep transaction data but remove any potential sensitive info
          return safeTx;
        });
      }

      return ret;
    }
  },
  toObject: {
    virtuals: true,
    transform: function(doc, ret) {
      // Remove sensitive wallet data from object output
      delete ret.security?.encryptedMnemonic;
      delete ret.security?.mnemonicHash;
      delete ret.addresses?.ethereum?.publicKey;
      delete ret.addresses?.bitcoin?.publicKey;
      delete ret.__v;

      // Remove sensitive transaction data
      if (ret.transactions) {
        ret.transactions = ret.transactions.map(tx => {
          const safeTx = { ...tx };
          // Keep transaction data but remove any potential sensitive info
          return safeTx;
        });
      }

      return ret;
    }
  }
});

// Indexes
walletSchema.index({ user: 1 });
walletSchema.index({ 'addresses.ethereum.address': 1 });
walletSchema.index({ 'addresses.bitcoin.address': 1 });
walletSchema.index({ 'transactions.hash': 1 });
walletSchema.index({ 'transactions.status': 1 });
walletSchema.index({ 'balances.symbol': 1, 'balances.network': 1 });

// Virtual for total portfolio value (in USD)
walletSchema.virtual('portfolioValue').get(function() {
  // This would be calculated with current market prices
  return this.balances.reduce((total, balance) => {
    // Placeholder calculation - would use real price feeds
    return total + (parseFloat(balance.balance) * 1); // Assuming $1 per token for demo
  }, 0);
});

// Method to get balance for specific token
walletSchema.methods.getBalance = function(symbol, network) {
  const balance = this.balances.find(b => 
    b.symbol === symbol && b.network === network
  );
  return balance ? balance.balance : '0';
};

// Method to update balance (with optional session support for transactions)
walletSchema.methods.updateBalance = function(symbol, network, newBalance, contractAddress = null, options = {}) {
  const balanceIndex = this.balances.findIndex(b =>
    b.symbol === symbol && b.network === network
  );

  if (balanceIndex >= 0) {
    this.balances[balanceIndex].balance = newBalance;
    this.balances[balanceIndex].lastUpdated = new Date();
  } else {
    this.balances.push({
      symbol,
      network,
      balance: newBalance,
      contractAddress,
      lastUpdated: new Date()
    });
  }

  // Support session for transactions
  return this.save(options);
};

// Method to add transaction (with optional session support for transactions)
walletSchema.methods.addTransaction = function(transactionData, options = {}) {
  this.transactions.unshift(transactionData); // Add to beginning

  // Keep only last 1000 transactions
  if (this.transactions.length > 1000) {
    this.transactions = this.transactions.slice(0, 1000);
  }

  // Support session for transactions
  return this.save(options);
};

// Method to update transaction status
walletSchema.methods.updateTransactionStatus = function(hash, status, confirmations = 0, blockNumber = null) {
  const transaction = this.transactions.find(tx => tx.hash === hash);
  
  if (transaction) {
    transaction.status = status;
    transaction.confirmations = confirmations;
    if (blockNumber) {
      transaction.blockNumber = blockNumber;
    }
    return this.save();
  }
  
  return Promise.resolve(this);
};

// Static method to find wallet by address
walletSchema.statics.findByAddress = function(address) {
  return this.findOne({
    $or: [
      { 'addresses.ethereum.address': address },
      { 'addresses.bitcoin.address': address }
    ]
  }).populate('user');
};

module.exports = mongoose.model('Wallet', walletSchema);
