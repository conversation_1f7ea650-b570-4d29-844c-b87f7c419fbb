/**
 * Key Management Monitoring Service
 * Provides comprehensive monitoring, alerting, and reporting for key management operations
 */

const logger = require('../../utils/logger');
const SecureKey = require('../../models/SecureKey');
const { circuitBreakers } = require('../../utils/circuitBreaker');

class KeyMonitoringService {
  constructor() {
    this.monitoringInterval = null;
    this.alertThresholds = {
      overdueRotationDays: 7,
      maxFailedAccesses: 5,
      suspiciousAccessPattern: 10 // accesses per minute
    };
    this.metrics = {
      keyAccesses: new Map(),
      rotationEvents: [],
      securityAlerts: [],
      performanceMetrics: {
        avgDecryptionTime: 0,
        avgEncryptionTime: 0,
        totalOperations: 0
      }
    };
  }

  /**
   * Initialize monitoring service
   */
  async initialize() {
    try {
      await this.startContinuousMonitoring();
      await this.initializeMetrics();
      
      logger.info('🔍 Key monitoring service initialized');
      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize key monitoring service:', error);
      throw error;
    }
  }

  /**
   * Start continuous monitoring
   */
  async startContinuousMonitoring() {
    // Monitor every 15 minutes
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performSecurityChecks();
        await this.analyzeAccessPatterns();
        await this.checkKeyHealth();
        await this.generateMetricsReport();
      } catch (error) {
        logger.error('❌ Monitoring cycle failed:', error);
      }
    }, 15 * 60 * 1000); // 15 minutes

    logger.info('🔄 Continuous key monitoring started');
  }

  /**
   * Perform comprehensive security checks
   */
  async performSecurityChecks() {
    try {
      // Check for compromised keys
      const compromisedKeys = await SecureKey.findCompromisedKeys();
      if (compromisedKeys.length > 0) {
        await this.handleCompromisedKeys(compromisedKeys);
      }

      // Check for overdue rotations
      const overdueKeys = await SecureKey.findKeysNeedingRotation();
      if (overdueKeys.length > 0) {
        await this.handleOverdueRotations(overdueKeys);
      }

      // Check for suspicious access patterns
      await this.detectSuspiciousActivity();

      // Verify key integrity
      await this.verifyKeyIntegrity();

    } catch (error) {
      logger.error('❌ Security checks failed:', error);
      await this.recordSecurityAlert('security_check_failed', {
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Analyze access patterns for anomalies
   */
  async analyzeAccessPatterns() {
    try {
      const now = Date.now();
      const oneHourAgo = now - (60 * 60 * 1000);

      // Analyze recent access patterns
      for (const [keyId, accesses] of this.metrics.keyAccesses) {
        const recentAccesses = accesses.filter(access => access.timestamp > oneHourAgo);
        
        if (recentAccesses.length > this.alertThresholds.suspiciousAccessPattern) {
          await this.recordSecurityAlert('suspicious_access_pattern', {
            keyId,
            accessCount: recentAccesses.length,
            timeWindow: '1_hour',
            threshold: this.alertThresholds.suspiciousAccessPattern
          });
        }
      }

      // Clean old access records (keep last 24 hours)
      const twentyFourHoursAgo = now - (24 * 60 * 60 * 1000);
      for (const [keyId, accesses] of this.metrics.keyAccesses) {
        this.metrics.keyAccesses.set(
          keyId, 
          accesses.filter(access => access.timestamp > twentyFourHoursAgo)
        );
      }

    } catch (error) {
      logger.error('❌ Access pattern analysis failed:', error);
    }
  }

  /**
   * Check overall key health
   */
  async checkKeyHealth() {
    try {
      const healthReport = await SecureKey.getSecuritySummary();
      
      for (const keyTypeReport of healthReport) {
        const { _id: keyType, total, active, compromised, needingRotation } = keyTypeReport;
        
        // Alert if more than 50% of keys need rotation
        if (needingRotation / total > 0.5) {
          await this.recordSecurityAlert('high_rotation_backlog', {
            keyType,
            total,
            needingRotation,
            percentage: Math.round((needingRotation / total) * 100)
          });
        }

        // Alert if any keys are compromised
        if (compromised > 0) {
          await this.recordSecurityAlert('compromised_keys_detected', {
            keyType,
            compromisedCount: compromised
          });
        }
      }

    } catch (error) {
      logger.error('❌ Key health check failed:', error);
    }
  }

  /**
   * Verify key integrity using checksums
   */
  async verifyKeyIntegrity() {
    try {
      const activeKeys = await SecureKey.find({
        'security.isActive': true,
        'security.isCompromised': false
      });

      for (const key of activeKeys) {
        // Verify checksum
        const currentChecksum = require('crypto')
          .createHash('sha256')
          .update(JSON.stringify(key.encryptedData))
          .digest('hex');

        if (currentChecksum !== key.security.checksumHash) {
          await this.recordSecurityAlert('key_integrity_violation', {
            keyId: key.keyId,
            expectedChecksum: key.security.checksumHash,
            actualChecksum: currentChecksum
          });

          // Mark key as potentially compromised
          await key.markCompromised('integrity_check_failed', 'monitoring_service');
        }
      }

    } catch (error) {
      logger.error('❌ Key integrity verification failed:', error);
    }
  }

  /**
   * Handle compromised keys
   */
  async handleCompromisedKeys(compromisedKeys) {
    for (const key of compromisedKeys) {
      await this.recordSecurityAlert('compromised_key_active', {
        keyId: key.keyId,
        keyType: key.keyType,
        network: key.network,
        compromisedAt: key.security.compromisedAt,
        reason: key.security.compromisedReason,
        severity: 'critical'
      });
    }
  }

  /**
   * Handle overdue rotations
   */
  async handleOverdueRotations(overdueKeys) {
    for (const key of overdueKeys) {
      const daysPastDue = Math.floor(
        (Date.now() - key.rotation.nextRotation.getTime()) / (24 * 60 * 60 * 1000)
      );

      if (daysPastDue >= this.alertThresholds.overdueRotationDays) {
        await this.recordSecurityAlert('rotation_critically_overdue', {
          keyId: key.keyId,
          keyType: key.keyType,
          network: key.network,
          daysPastDue,
          severity: daysPastDue > 30 ? 'critical' : 'high'
        });
      }
    }
  }

  /**
   * Detect suspicious activity
   */
  async detectSuspiciousActivity() {
    // Check for failed access attempts
    const recentAlerts = this.metrics.securityAlerts.filter(
      alert => alert.timestamp > Date.now() - (60 * 60 * 1000) // Last hour
    );

    const failedAccesses = recentAlerts.filter(
      alert => alert.type === 'key_access_failed'
    );

    if (failedAccesses.length >= this.alertThresholds.maxFailedAccesses) {
      await this.recordSecurityAlert('multiple_access_failures', {
        failureCount: failedAccesses.length,
        timeWindow: '1_hour',
        threshold: this.alertThresholds.maxFailedAccesses,
        severity: 'high'
      });
    }
  }

  /**
   * Record key access for monitoring
   */
  recordKeyAccess(keyId, details = {}) {
    if (!this.metrics.keyAccesses.has(keyId)) {
      this.metrics.keyAccesses.set(keyId, []);
    }

    this.metrics.keyAccesses.get(keyId).push({
      timestamp: Date.now(),
      ...details
    });
  }

  /**
   * Record security alert
   */
  async recordSecurityAlert(type, details) {
    const alert = {
      type,
      timestamp: Date.now(),
      details,
      severity: details.severity || 'medium'
    };

    this.metrics.securityAlerts.push(alert);

    // Keep only last 1000 alerts
    if (this.metrics.securityAlerts.length > 1000) {
      this.metrics.securityAlerts = this.metrics.securityAlerts.slice(-1000);
    }

    logger.warn(`🚨 SECURITY ALERT [${alert.severity.toUpperCase()}] ${type}:`, details);

    // In production, integrate with external alerting systems
    if (alert.severity === 'critical') {
      await this.sendCriticalAlert(alert);
    }
  }

  /**
   * Send critical alert to external systems
   */
  async sendCriticalAlert(alert) {
    // Placeholder for external alerting integration
    // In production, this would send to PagerDuty, Slack, email, etc.
    logger.error('🚨 CRITICAL SECURITY ALERT:', JSON.stringify(alert, null, 2));
  }

  /**
   * Generate comprehensive metrics report
   */
  async generateMetricsReport() {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        keyMetrics: await this.getKeyMetrics(),
        securityMetrics: this.getSecurityMetrics(),
        performanceMetrics: this.metrics.performanceMetrics,
        recentAlerts: this.getRecentAlerts()
      };

      logger.info('📊 Key Management Metrics Report:', JSON.stringify(report, null, 2));
      return report;
    } catch (error) {
      logger.error('❌ Failed to generate metrics report:', error);
    }
  }

  /**
   * Get key metrics
   */
  async getKeyMetrics() {
    const summary = await SecureKey.getSecuritySummary();
    return {
      totalKeys: summary.reduce((sum, item) => sum + item.total, 0),
      activeKeys: summary.reduce((sum, item) => sum + item.active, 0),
      compromisedKeys: summary.reduce((sum, item) => sum + item.compromised, 0),
      keysNeedingRotation: summary.reduce((sum, item) => sum + item.needingRotation, 0)
    };
  }

  /**
   * Get security metrics
   */
  getSecurityMetrics() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    return {
      alertsLastHour: this.metrics.securityAlerts.filter(a => a.timestamp > oneHourAgo).length,
      alertsLast24Hours: this.metrics.securityAlerts.filter(a => a.timestamp > oneDayAgo).length,
      totalKeyAccesses: Array.from(this.metrics.keyAccesses.values())
        .reduce((sum, accesses) => sum + accesses.length, 0)
    };
  }

  /**
   * Get recent alerts
   */
  getRecentAlerts(hours = 24) {
    const cutoff = Date.now() - (hours * 60 * 60 * 1000);
    return this.metrics.securityAlerts
      .filter(alert => alert.timestamp > cutoff)
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10); // Last 10 alerts
  }

  /**
   * Initialize metrics
   */
  async initializeMetrics() {
    this.metrics.keyAccesses.clear();
    this.metrics.rotationEvents = [];
    this.metrics.securityAlerts = [];
    this.metrics.performanceMetrics = {
      avgDecryptionTime: 0,
      avgEncryptionTime: 0,
      totalOperations: 0
    };
  }

  /**
   * Cleanup monitoring service
   */
  cleanup() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    this.initializeMetrics();
    logger.info('🧹 Key monitoring service cleaned up');
  }
}

module.exports = KeyMonitoringService;
