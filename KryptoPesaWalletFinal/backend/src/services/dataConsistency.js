/**
 * Data Consistency & Reliability Service
 * Ensures atomic operations and data integrity across all critical business flows
 * Implements MongoDB transactions for multi-document updates and provides
 * comprehensive data consistency guarantees for the KryptoPesa platform
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');
const { createAuditLog } = require('../middleware/auditLogger');

class DataConsistencyService {
  constructor() {
    this.activeTransactions = new Map();
    this.transactionTimeouts = new Map();
    this.consistencyMetrics = {
      totalTransactions: 0,
      successfulTransactions: 0,
      failedTransactions: 0,
      rollbackCount: 0,
      averageTransactionTime: 0,
      lastReset: new Date()
    };
    
    // Transaction timeout settings
    this.transactionTimeout = 30000; // 30 seconds
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
    
    this.initialize();
  }

  /**
   * Initialize data consistency service
   */
  async initialize() {
    try {
      logger.info('🔄 Initializing Data Consistency Service...');
      
      // Verify MongoDB replica set for transactions
      await this.verifyReplicaSet();
      
      // Setup periodic cleanup
      this.setupPeriodicCleanup();
      
      logger.info('✅ Data Consistency Service initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Data Consistency Service initialization failed:', error);
      return false;
    }
  }

  /**
   * Verify MongoDB replica set configuration for transactions
   */
  async verifyReplicaSet() {
    try {
      const admin = mongoose.connection.db.admin();
      const result = await admin.command({ replSetGetStatus: 1 });
      
      if (!result || !result.members) {
        logger.warn('⚠️ MongoDB replica set not detected - transactions may not work');
        return false;
      }
      
      logger.info('✅ MongoDB replica set verified for transactions');
      return true;
    } catch (error) {
      logger.warn('⚠️ Could not verify replica set status:', error.message);
      return false;
    }
  }

  /**
   * Execute atomic trade creation with all related operations
   */
  async executeAtomicTradeCreation(tradeData, offerData, userId) {
    const transactionId = this.generateTransactionId();
    const startTime = Date.now();
    
    try {
      logger.info(`🔄 Starting atomic trade creation transaction: ${transactionId}`);
      
      const result = await this.withTransaction(async (session) => {
        const Trade = require('../models/Trade');
        const Chat = require('../models/Chat');
        const Offer = require('../models/Offer');
        
        // 1. Create trade
        const trade = new Trade(tradeData);
        const savedTrade = await trade.save({ session });
        
        // 2. Create chat for the trade
        const chat = await Chat.createForTrade(
          savedTrade._id,
          savedTrade.seller,
          savedTrade.buyer,
          { session }
        );
        
        // 3. Update trade with chat reference
        savedTrade.chat = chat._id;
        await savedTrade.save({ session });
        
        // 4. Update offer available amount
        const offer = await Offer.findById(offerData.offerId).session(session);
        if (!offer) {
          throw new AppError('Offer not found', 404);
        }
        
        await offer.updateAvailableAmount(tradeData.cryptocurrency.amount, { session });
        
        // 5. Add timeline entry
        await savedTrade.addTimelineEntry('created', userId, 'Trade created from offer', { session });
        
        // 6. Create audit log
        await createAuditLog({
          action: 'trade_created',
          userId,
          details: {
            tradeId: savedTrade._id,
            offerId: offer._id,
            amount: tradeData.cryptocurrency.amount,
            transactionId
          }
        }, { session });
        
        return {
          trade: savedTrade,
          chat: chat,
          offer: offer
        };
      }, transactionId);
      
      const duration = Date.now() - startTime;
      this.updateMetrics('success', duration);
      
      logger.info(`✅ Atomic trade creation completed: ${transactionId} (${duration}ms)`);
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics('failure', duration);
      
      logger.error(`❌ Atomic trade creation failed: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Execute atomic escrow creation with trade update
   */
  async executeAtomicEscrowCreation(tradeId, escrowData, userId) {
    const transactionId = this.generateTransactionId();
    const startTime = Date.now();
    
    try {
      logger.info(`🔄 Starting atomic escrow creation transaction: ${transactionId}`);
      
      const result = await this.withTransaction(async (session) => {
        const Trade = require('../models/Trade');
        
        // 1. Get and lock trade
        const trade = await Trade.findById(tradeId).session(session);
        if (!trade) {
          throw new AppError('Trade not found', 404);
        }
        
        // 2. Validate trade state
        if (trade.status !== 'created') {
          throw new AppError(`Cannot create escrow for trade in status: ${trade.status}`, 400);
        }
        
        // 3. Update trade with escrow details
        trade.escrow = {
          ...escrowData,
          status: 'active',
          createdAt: new Date()
        };
        
        trade.status = 'funded';
        await trade.save({ session });
        
        // 4. Add timeline entry
        await trade.addTimelineEntry('escrow_created', userId, 'Escrow created and funded', { session });
        
        // 5. Create audit log
        await createAuditLog({
          action: 'escrow_created',
          userId,
          details: {
            tradeId: trade._id,
            escrowId: escrowData.escrowId,
            contractAddress: escrowData.contractAddress,
            transactionId
          }
        }, { session });
        
        return trade;
      }, transactionId);
      
      const duration = Date.now() - startTime;
      this.updateMetrics('success', duration);
      
      logger.info(`✅ Atomic escrow creation completed: ${transactionId} (${duration}ms)`);
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics('failure', duration);
      
      logger.error(`❌ Atomic escrow creation failed: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Execute atomic dispute creation with trade update
   */
  async executeAtomicDisputeCreation(tradeId, disputeData, userId) {
    const transactionId = this.generateTransactionId();
    const startTime = Date.now();
    
    try {
      logger.info(`🔄 Starting atomic dispute creation transaction: ${transactionId}`);
      
      const result = await this.withTransaction(async (session) => {
        const Trade = require('../models/Trade');
        const Dispute = require('../models/Dispute');
        
        // 1. Get and lock trade
        const trade = await Trade.findById(tradeId).session(session);
        if (!trade) {
          throw new AppError('Trade not found', 404);
        }
        
        // 2. Validate trade state
        if (!['funded', 'payment_sent'].includes(trade.status)) {
          throw new AppError(`Cannot create dispute for trade in status: ${trade.status}`, 400);
        }
        
        if (trade.dispute) {
          throw new AppError('Dispute already exists for this trade', 400);
        }
        
        // 3. Create dispute
        const dispute = new Dispute({
          ...disputeData,
          trade: trade._id
        });
        
        const savedDispute = await dispute.save({ session });
        
        // 4. Update trade with dispute reference
        trade.dispute = savedDispute._id;
        trade.status = 'disputed';
        await trade.save({ session });
        
        // 5. Add timeline entries
        await trade.addTimelineEntry('disputed', userId, `Dispute created: ${disputeData.description}`, { session });
        await savedDispute.addTimelineEntry('created', userId, 'Dispute created', { session });
        
        // 6. Create audit log
        await createAuditLog({
          action: 'dispute_created',
          userId,
          details: {
            tradeId: trade._id,
            disputeId: savedDispute._id,
            category: disputeData.category,
            transactionId
          }
        }, { session });
        
        return {
          trade: trade,
          dispute: savedDispute
        };
      }, transactionId);
      
      const duration = Date.now() - startTime;
      this.updateMetrics('success', duration);
      
      logger.info(`✅ Atomic dispute creation completed: ${transactionId} (${duration}ms)`);
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics('failure', duration);
      
      logger.error(`❌ Atomic dispute creation failed: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Execute atomic escrow release with trade completion
   */
  async executeAtomicEscrowRelease(tradeId, releaseData, userId) {
    const transactionId = this.generateTransactionId();
    const startTime = Date.now();

    try {
      logger.info(`🔄 Starting atomic escrow release transaction: ${transactionId}`);

      const result = await this.withTransaction(async (session) => {
        const Trade = require('../models/Trade');
        const Wallet = require('../models/Wallet');

        // 1. Get and lock trade
        const trade = await Trade.findById(tradeId).populate('buyer seller').session(session);
        if (!trade) {
          throw new AppError('Trade not found', 404);
        }

        // 2. Validate trade state
        if (!trade.escrow || trade.escrow.status !== 'active') {
          throw new AppError('No active escrow found for this trade', 400);
        }

        // 3. Update trade with release details
        trade.escrow.status = 'released';
        trade.escrow.releasedAt = new Date();
        trade.escrow.releasedBy = userId;
        trade.escrow.releaseTxHash = releaseData.txHash;
        trade.status = 'completed';
        trade.completedAt = new Date();

        await trade.save({ session });

        // 4. Update seller wallet balance (if tracking on-platform)
        if (releaseData.updateBalance) {
          const sellerWallet = await Wallet.findOne({ user: trade.seller._id }).session(session);
          if (sellerWallet) {
            await sellerWallet.updateBalance(
              trade.cryptocurrency.symbol,
              trade.cryptocurrency.network,
              releaseData.newBalance
            );
          }
        }

        // 5. Add timeline entry
        await trade.addTimelineEntry('completed', userId, 'Escrow released and trade completed', { session });

        // 6. Create audit log
        await createAuditLog({
          action: 'escrow_released',
          userId,
          details: {
            tradeId: trade._id,
            releaseTxHash: releaseData.txHash,
            amount: trade.cryptocurrency.amount,
            transactionId
          }
        }, { session });

        return trade;
      }, transactionId);

      const duration = Date.now() - startTime;
      this.updateMetrics('success', duration);

      logger.info(`✅ Atomic escrow release completed: ${transactionId} (${duration}ms)`);
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics('failure', duration);

      logger.error(`❌ Atomic escrow release failed: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Execute atomic wallet balance update with transaction logging
   */
  async executeAtomicBalanceUpdate(userId, balanceUpdates, transactionData = null) {
    const transactionId = this.generateTransactionId();
    const startTime = Date.now();

    try {
      logger.info(`🔄 Starting atomic balance update transaction: ${transactionId}`);

      const result = await this.withTransaction(async (session) => {
        const Wallet = require('../models/Wallet');

        // 1. Get and lock wallet
        const wallet = await Wallet.findOne({ user: userId }).session(session);
        if (!wallet) {
          throw new AppError('Wallet not found', 404);
        }

        // 2. Apply balance updates
        const updatedBalances = [];
        for (const update of balanceUpdates) {
          const oldBalance = wallet.getBalance(update.symbol, update.network);
          await wallet.updateBalance(update.symbol, update.network, update.newBalance, update.contractAddress);

          updatedBalances.push({
            symbol: update.symbol,
            network: update.network,
            oldBalance,
            newBalance: update.newBalance,
            change: (parseFloat(update.newBalance) - parseFloat(oldBalance)).toString()
          });
        }

        // 3. Add transaction if provided
        if (transactionData) {
          await wallet.addTransaction(transactionData);
        }

        // 4. Create audit log
        await createAuditLog({
          action: 'balance_updated',
          userId,
          details: {
            balanceUpdates: updatedBalances,
            transactionHash: transactionData?.hash,
            transactionId
          }
        }, { session });

        return {
          wallet,
          updatedBalances
        };
      }, transactionId);

      const duration = Date.now() - startTime;
      this.updateMetrics('success', duration);

      logger.info(`✅ Atomic balance update completed: ${transactionId} (${duration}ms)`);
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics('failure', duration);

      logger.error(`❌ Atomic balance update failed: ${transactionId}`, error);
      throw error;
    }
  }

  /**
   * Core transaction wrapper with retry logic and timeout handling
   */
  async withTransaction(operation, transactionId = null) {
    if (!transactionId) {
      transactionId = this.generateTransactionId();
    }

    const session = await mongoose.startSession();
    this.activeTransactions.set(transactionId, session);

    // Set transaction timeout
    const timeoutHandle = setTimeout(() => {
      this.handleTransactionTimeout(transactionId);
    }, this.transactionTimeout);

    this.transactionTimeouts.set(transactionId, timeoutHandle);

    try {
      let result;
      let retryCount = 0;

      while (retryCount <= this.maxRetries) {
        try {
          result = await session.withTransaction(async () => {
            return await operation(session);
          }, {
            readPreference: 'primary',
            readConcern: { level: 'local' },
            writeConcern: { w: 'majority' }
          });

          break; // Success, exit retry loop

        } catch (error) {
          retryCount++;

          if (retryCount > this.maxRetries) {
            throw error;
          }

          // Check if error is retryable
          if (this.isRetryableError(error)) {
            logger.warn(`🔄 Retrying transaction ${transactionId} (attempt ${retryCount}/${this.maxRetries}): ${error.message}`);
            await this.delay(this.retryDelay * retryCount); // Exponential backoff
          } else {
            throw error;
          }
        }
      }

      this.consistencyMetrics.totalTransactions++;
      return result;

    } finally {
      // Cleanup
      clearTimeout(timeoutHandle);
      this.transactionTimeouts.delete(transactionId);
      this.activeTransactions.delete(transactionId);

      try {
        await session.endSession();
      } catch (error) {
        logger.warn(`Warning: Error ending session for transaction ${transactionId}:`, error.message);
      }
    }
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error) {
    const retryableErrors = [
      'WriteConflict',
      'LockTimeout',
      'TransientTransactionError',
      'UnknownTransactionCommitResult'
    ];

    return retryableErrors.some(errorType =>
      error.message.includes(errorType) ||
      error.code === 112 || // WriteConflict
      error.code === 50 ||  // MaxTimeMSExpired
      error.code === 11000  // DuplicateKey (in some cases)
    );
  }

  /**
   * Handle transaction timeout
   */
  async handleTransactionTimeout(transactionId) {
    try {
      const session = this.activeTransactions.get(transactionId);
      if (session) {
        logger.warn(`⏰ Transaction timeout: ${transactionId}`);
        await session.abortTransaction();
        this.consistencyMetrics.rollbackCount++;
      }
    } catch (error) {
      logger.error(`Error handling transaction timeout for ${transactionId}:`, error);
    }
  }

  /**
   * Setup periodic cleanup of stale transactions
   */
  setupPeriodicCleanup() {
    setInterval(() => {
      this.cleanupStaleTransactions();
    }, 60000); // Every minute
  }

  /**
   * Cleanup stale transactions
   */
  async cleanupStaleTransactions() {
    const now = Date.now();
    const staleTransactions = [];

    for (const [transactionId, session] of this.activeTransactions.entries()) {
      const timeout = this.transactionTimeouts.get(transactionId);
      if (!timeout) {
        staleTransactions.push(transactionId);
      }
    }

    for (const transactionId of staleTransactions) {
      try {
        const session = this.activeTransactions.get(transactionId);
        if (session) {
          await session.abortTransaction();
          await session.endSession();
        }

        this.activeTransactions.delete(transactionId);
        this.transactionTimeouts.delete(transactionId);

        logger.warn(`🧹 Cleaned up stale transaction: ${transactionId}`);
      } catch (error) {
        logger.error(`Error cleaning up stale transaction ${transactionId}:`, error);
      }
    }
  }

  /**
   * Update consistency metrics
   */
  updateMetrics(result, duration) {
    if (result === 'success') {
      this.consistencyMetrics.successfulTransactions++;
    } else {
      this.consistencyMetrics.failedTransactions++;
    }

    // Update average transaction time
    const totalTransactions = this.consistencyMetrics.successfulTransactions + this.consistencyMetrics.failedTransactions;
    const currentAverage = this.consistencyMetrics.averageTransactionTime;
    this.consistencyMetrics.averageTransactionTime =
      ((currentAverage * (totalTransactions - 1)) + duration) / totalTransactions;
  }

  /**
   * Get consistency metrics
   */
  getConsistencyMetrics() {
    return {
      ...this.consistencyMetrics,
      activeTransactions: this.activeTransactions.size,
      successRate: this.consistencyMetrics.totalTransactions > 0
        ? (this.consistencyMetrics.successfulTransactions / this.consistencyMetrics.totalTransactions) * 100
        : 0,
      rollbackRate: this.consistencyMetrics.totalTransactions > 0
        ? (this.consistencyMetrics.rollbackCount / this.consistencyMetrics.totalTransactions) * 100
        : 0
    };
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.consistencyMetrics = {
      totalTransactions: 0,
      successfulTransactions: 0,
      failedTransactions: 0,
      rollbackCount: 0,
      averageTransactionTime: 0,
      lastReset: new Date()
    };

    logger.info('📊 Data consistency metrics reset');
  }

  /**
   * Delay utility for retry logic
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    try {
      logger.info('🔄 Shutting down Data Consistency Service...');

      // Abort all active transactions
      for (const [transactionId, session] of this.activeTransactions.entries()) {
        try {
          await session.abortTransaction();
          await session.endSession();
          logger.info(`🔄 Aborted transaction: ${transactionId}`);
        } catch (error) {
          logger.warn(`Warning: Error aborting transaction ${transactionId}:`, error.message);
        }
      }

      // Clear all timeouts
      for (const timeout of this.transactionTimeouts.values()) {
        clearTimeout(timeout);
      }

      this.activeTransactions.clear();
      this.transactionTimeouts.clear();

      logger.info('✅ Data Consistency Service shutdown completed');
    } catch (error) {
      logger.error('❌ Error during Data Consistency Service shutdown:', error);
    }
  }

  /**
   * Generate unique transaction ID
   */
  generateTransactionId() {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
const dataConsistencyService = new DataConsistencyService();
module.exports = { dataConsistencyService, DataConsistencyService };
