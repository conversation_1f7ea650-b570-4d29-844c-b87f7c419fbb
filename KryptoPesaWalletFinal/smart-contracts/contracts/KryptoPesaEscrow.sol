// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title KryptoPesaEscrow
 * @dev P2P cryptocurrency trading escrow contract for KryptoPesa platform
 */
contract KryptoPesaEscrow is ReentrancyGuard, Ownable, Pausable {
    
    enum TradeStatus {
        CREATED,
        FUNDED,
        PAYMENT_SENT,
        COMPLETED,
        DISPUTED,
        CANCELLED,
        REFUNDED
    }
    
    struct Trade {
        uint256 tradeId;
        address seller;
        address buyer;
        address tokenAddress;
        uint256 amount;
        uint256 fiatAmount;
        string fiatCurrency;
        uint256 commissionRate; // in basis points (100 = 1%)
        TradeStatus status;
        uint256 createdAt;
        uint256 expiresAt;
        uint256 completedAt;
        bool sellerConfirmed;
        bool buyerConfirmed;
        string paymentMethod;
        bytes32 paymentHash; // Hash of payment details
    }
    
    struct Dispute {
        uint256 tradeId;
        address initiator;
        string reason;
        uint256 createdAt;
        bool resolved;
        address winner;
    }
    
    // State variables
    mapping(uint256 => Trade) public trades;
    mapping(uint256 => Dispute) public disputes;
    mapping(address => bool) public authorizedTokens;
    mapping(address => uint256) public userReputation;
    mapping(address => uint256) public completedTrades;
    
    uint256 public nextTradeId = 1;
    uint256 public defaultCommissionRate = 50; // 0.5% in basis points
    uint256 public constant MAX_COMMISSION_RATE = 1000; // 10%
    uint256 public constant TRADE_TIMEOUT = 24 hours;
    uint256 public constant DISPUTE_TIMEOUT = 7 days;
    
    address public feeCollector;
    address public disputeResolver;
    
    // Events
    event TradeCreated(uint256 indexed tradeId, address indexed seller, address indexed buyer, uint256 amount);
    event TradeFunded(uint256 indexed tradeId, uint256 amount);
    event PaymentSent(uint256 indexed tradeId, address indexed buyer);
    event TradeCompleted(uint256 indexed tradeId, uint256 sellerAmount, uint256 commission);
    event TradeCancelled(uint256 indexed tradeId, string reason);
    event DisputeCreated(uint256 indexed tradeId, address indexed initiator, string reason);
    event DisputeResolved(uint256 indexed tradeId, address indexed winner);
    event TokenAuthorized(address indexed token, bool authorized);
    event CommissionRateUpdated(uint256 newRate);
    
    modifier onlyTradeParticipant(uint256 _tradeId) {
        require(
            msg.sender == trades[_tradeId].seller || msg.sender == trades[_tradeId].buyer,
            "Not a trade participant"
        );
        _;
    }
    
    modifier onlyDisputeResolver() {
        require(msg.sender == disputeResolver || msg.sender == owner(), "Not authorized to resolve disputes");
        _;
    }
    
    modifier validTradeId(uint256 _tradeId) {
        require(_tradeId > 0 && _tradeId < nextTradeId, "Invalid trade ID");
        _;
    }
    
    constructor(address _feeCollector, address _disputeResolver) Ownable(msg.sender) {
        feeCollector = _feeCollector;
        disputeResolver = _disputeResolver;
        
        // Authorize common stablecoins on Polygon
        authorizedTokens[******************************************] = true; // USDT
        authorizedTokens[******************************************] = true; // USDC
        authorizedTokens[******************************************] = true; // DAI
    }
    
    /**
     * @dev Create a new P2P trade
     */
    function createTrade(
        address _buyer,
        address _tokenAddress,
        uint256 _amount,
        uint256 _fiatAmount,
        string memory _fiatCurrency,
        string memory _paymentMethod,
        bytes32 _paymentHash
    ) external whenNotPaused returns (uint256) {
        require(_buyer != address(0) && _buyer != msg.sender, "Invalid buyer address");
        require(authorizedTokens[_tokenAddress], "Token not authorized");
        require(_amount > 0, "Amount must be greater than 0");
        require(bytes(_fiatCurrency).length > 0, "Fiat currency required");
        
        uint256 tradeId = nextTradeId++;
        
        trades[tradeId] = Trade({
            tradeId: tradeId,
            seller: msg.sender,
            buyer: _buyer,
            tokenAddress: _tokenAddress,
            amount: _amount,
            fiatAmount: _fiatAmount,
            fiatCurrency: _fiatCurrency,
            commissionRate: defaultCommissionRate,
            status: TradeStatus.CREATED,
            createdAt: block.timestamp,
            expiresAt: block.timestamp + TRADE_TIMEOUT,
            completedAt: 0,
            sellerConfirmed: false,
            buyerConfirmed: false,
            paymentMethod: _paymentMethod,
            paymentHash: _paymentHash
        });
        
        emit TradeCreated(tradeId, msg.sender, _buyer, _amount);
        return tradeId;
    }
    
    /**
     * @dev Seller funds the escrow
     */
    function fundTrade(uint256 _tradeId) external validTradeId(_tradeId) whenNotPaused nonReentrant {
        Trade storage trade = trades[_tradeId];
        require(msg.sender == trade.seller, "Only seller can fund");
        require(trade.status == TradeStatus.CREATED, "Trade not in created status");
        require(block.timestamp <= trade.expiresAt, "Trade expired");

        IERC20 token = IERC20(trade.tokenAddress);
        require(token.transferFrom(msg.sender, address(this), trade.amount), "Transfer failed");

        trade.status = TradeStatus.FUNDED;

        emit TradeFunded(_tradeId, trade.amount);
    }

    /**
     * @dev Buyer confirms fiat payment sent
     */
    function confirmPaymentSent(uint256 _tradeId) external validTradeId(_tradeId) whenNotPaused {
        Trade storage trade = trades[_tradeId];
        require(msg.sender == trade.buyer, "Only buyer can confirm payment");
        require(trade.status == TradeStatus.FUNDED, "Trade not funded");
        require(block.timestamp <= trade.expiresAt, "Trade expired");

        trade.status = TradeStatus.PAYMENT_SENT;
        trade.buyerConfirmed = true;

        emit PaymentSent(_tradeId, msg.sender);
    }

    /**
     * @dev Seller confirms fiat payment received and releases escrow
     */
    function confirmPaymentReceived(uint256 _tradeId) external validTradeId(_tradeId) whenNotPaused nonReentrant {
        Trade storage trade = trades[_tradeId];
        require(msg.sender == trade.seller, "Only seller can confirm");
        require(trade.status == TradeStatus.PAYMENT_SENT, "Payment not sent");

        trade.sellerConfirmed = true;
        _completeTrade(_tradeId);
    }

    /**
     * @dev Complete trade and distribute funds
     */
    function _completeTrade(uint256 _tradeId) internal {
        Trade storage trade = trades[_tradeId];
        require(trade.status == TradeStatus.PAYMENT_SENT, "Invalid status for completion");

        // Update status BEFORE external calls to prevent reentrancy
        trade.status = TradeStatus.COMPLETED;
        trade.completedAt = block.timestamp;

        uint256 commission = (trade.amount * trade.commissionRate) / 10000;
        uint256 sellerAmount = trade.amount - commission;

        IERC20 token = IERC20(trade.tokenAddress);

        // Transfer to buyer (they get the crypto)
        require(token.transfer(trade.buyer, sellerAmount), "Transfer to buyer failed");

        // Transfer commission to fee collector
        if (commission > 0) {
            require(token.transfer(feeCollector, commission), "Commission transfer failed");
        }

        trade.status = TradeStatus.COMPLETED;

        // Update reputation
        userReputation[trade.seller] += 1;
        userReputation[trade.buyer] += 1;
        completedTrades[trade.seller] += 1;
        completedTrades[trade.buyer] += 1;

        emit TradeCompleted(_tradeId, sellerAmount, commission);
    }

    /**
     * @dev Cancel trade (only if not funded or expired)
     */
    function cancelTrade(uint256 _tradeId, string memory _reason) external validTradeId(_tradeId) onlyTradeParticipant(_tradeId) {
        Trade storage trade = trades[_tradeId];
        require(
            trade.status == TradeStatus.CREATED ||
            (trade.status == TradeStatus.FUNDED && block.timestamp > trade.expiresAt),
            "Cannot cancel trade"
        );

        if (trade.status == TradeStatus.FUNDED) {
            // Refund seller
            IERC20 token = IERC20(trade.tokenAddress);
            require(token.transfer(trade.seller, trade.amount), "Refund failed");
            trade.status = TradeStatus.REFUNDED;
        } else {
            trade.status = TradeStatus.CANCELLED;
        }

        emit TradeCancelled(_tradeId, _reason);
    }

    /**
     * @dev Create dispute
     */
    function createDispute(uint256 _tradeId, string memory _reason) external validTradeId(_tradeId) onlyTradeParticipant(_tradeId) {
        Trade storage trade = trades[_tradeId];
        require(trade.status == TradeStatus.PAYMENT_SENT, "Invalid status for dispute");
        require(!disputes[_tradeId].resolved, "Dispute already exists");

        trade.status = TradeStatus.DISPUTED;

        disputes[_tradeId] = Dispute({
            tradeId: _tradeId,
            initiator: msg.sender,
            reason: _reason,
            createdAt: block.timestamp,
            resolved: false,
            winner: address(0)
        });

        emit DisputeCreated(_tradeId, msg.sender, _reason);
    }

    /**
     * @dev Resolve dispute (admin only)
     */
    function resolveDispute(uint256 _tradeId, address _winner) external validTradeId(_tradeId) onlyDisputeResolver nonReentrant {
        Trade storage trade = trades[_tradeId];
        Dispute storage dispute = disputes[_tradeId];

        require(trade.status == TradeStatus.DISPUTED, "Trade not in dispute");
        require(!dispute.resolved, "Dispute already resolved");
        require(_winner == trade.seller || _winner == trade.buyer, "Invalid winner");

        dispute.resolved = true;
        dispute.winner = _winner;

        IERC20 token = IERC20(trade.tokenAddress);

        if (_winner == trade.buyer) {
            // Buyer wins - gets the crypto
            uint256 commission = (trade.amount * trade.commissionRate) / 10000;
            uint256 buyerAmount = trade.amount - commission;

            require(token.transfer(trade.buyer, buyerAmount), "Transfer to buyer failed");
            if (commission > 0) {
                require(token.transfer(feeCollector, commission), "Commission transfer failed");
            }

            // Penalize seller reputation
            if (userReputation[trade.seller] > 0) {
                userReputation[trade.seller] -= 1;
            }
        } else {
            // Seller wins - gets refund
            require(token.transfer(trade.seller, trade.amount), "Refund to seller failed");

            // Penalize buyer reputation
            if (userReputation[trade.buyer] > 0) {
                userReputation[trade.buyer] -= 1;
            }
        }

        trade.status = TradeStatus.COMPLETED;

        emit DisputeResolved(_tradeId, _winner);
    }

    /**
     * @dev Emergency refund (admin only)
     */
    function emergencyRefund(uint256 _tradeId) external validTradeId(_tradeId) onlyOwner nonReentrant {
        Trade storage trade = trades[_tradeId];
        require(trade.status == TradeStatus.FUNDED || trade.status == TradeStatus.DISPUTED, "Invalid status");

        IERC20 token = IERC20(trade.tokenAddress);
        require(token.transfer(trade.seller, trade.amount), "Emergency refund failed");

        trade.status = TradeStatus.REFUNDED;
    }

    // Admin functions
    function authorizeToken(address _token, bool _authorized) external onlyOwner {
        authorizedTokens[_token] = _authorized;
        emit TokenAuthorized(_token, _authorized);
    }

    function setCommissionRate(uint256 _rate) external onlyOwner {
        require(_rate <= MAX_COMMISSION_RATE, "Commission rate too high");
        defaultCommissionRate = _rate;
        emit CommissionRateUpdated(_rate);
    }

    function setFeeCollector(address _feeCollector) external onlyOwner {
        require(_feeCollector != address(0), "Invalid fee collector");
        feeCollector = _feeCollector;
    }

    function setDisputeResolver(address _disputeResolver) external onlyOwner {
        require(_disputeResolver != address(0), "Invalid dispute resolver");
        disputeResolver = _disputeResolver;
    }

    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // View functions
    function getTrade(uint256 _tradeId) external view returns (Trade memory) {
        return trades[_tradeId];
    }

    function getDispute(uint256 _tradeId) external view returns (Dispute memory) {
        return disputes[_tradeId];
    }

    function getUserReputation(address _user) external view returns (uint256 reputation, uint256 completedTradesCount) {
        return (userReputation[_user], completedTrades[_user]);
    }

    function isTokenAuthorized(address _token) external view returns (bool) {
        return authorizedTokens[_token];
    }
}
