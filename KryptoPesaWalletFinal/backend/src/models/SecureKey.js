/**
 * Secure Key Storage Model
 * Provides encrypted database storage for cryptographic keys with audit trail
 */

const mongoose = require('mongoose');
const crypto = require('crypto');
const securityConfig = require('../config/security');

const secureKeySchema = new mongoose.Schema({
  keyId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  keyType: {
    type: String,
    required: true,
    enum: ['blockchain_private_key', 'jwt_secret', 'encryption_key', 'api_key', 'webhook_secret'],
    index: true
  },
  network: {
    type: String,
    enum: ['ethereum', 'polygon', 'bitcoin', 'global'],
    default: 'global',
    index: true
  },
  encryptedData: {
    encrypted: {
      type: String,
      required: true
    },
    iv: {
      type: String,
      required: true
    },
    salt: {
      type: String,
      required: true
    },
    tag: {
      type: String,
      required: true
    },
    algorithm: {
      type: String,
      default: 'aes-256-gcm'
    }
  },
  metadata: {
    description: String,
    purpose: String,
    environment: {
      type: String,
      enum: ['development', 'staging', 'production'],
      default: 'production'
    },
    version: {
      type: Number,
      default: 1
    },
    derivationPath: String, // For blockchain keys
    publicKey: String, // For asymmetric keys
    keyStrength: Number, // Key length in bits
    createdBy: {
      type: String,
      default: 'system'
    }
  },
  rotation: {
    lastRotated: {
      type: Date,
      default: Date.now
    },
    nextRotation: {
      type: Date,
      required: true
    },
    rotationInterval: {
      type: Number,
      default: 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds
    },
    rotationCount: {
      type: Number,
      default: 0
    },
    autoRotate: {
      type: Boolean,
      default: true
    }
  },
  access: {
    lastAccessed: {
      type: Date,
      default: Date.now
    },
    accessCount: {
      type: Number,
      default: 0
    },
    allowedServices: [{
      type: String,
      enum: ['escrow', 'wallet', 'auth', 'transaction', 'admin']
    }],
    accessRestrictions: {
      ipWhitelist: [String],
      timeRestrictions: {
        startHour: Number,
        endHour: Number,
        timezone: String
      }
    }
  },
  security: {
    isActive: {
      type: Boolean,
      default: true
    },
    isCompromised: {
      type: Boolean,
      default: false
    },
    compromisedAt: Date,
    compromisedReason: String,
    backupExists: {
      type: Boolean,
      default: false
    },
    backupLocation: String,
    checksumHash: String, // For integrity verification
    encryptionKeyId: String // Reference to master encryption key
  },
  auditTrail: [{
    action: {
      type: String,
      enum: ['created', 'accessed', 'rotated', 'updated', 'compromised', 'deactivated', 'backed_up'],
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    userId: String,
    serviceId: String,
    ipAddress: String,
    userAgent: String,
    details: mongoose.Schema.Types.Mixed,
    success: {
      type: Boolean,
      default: true
    },
    errorMessage: String
  }]
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      // Never expose encrypted data in JSON
      delete ret.encryptedData;
      delete ret.__v;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Indexes for performance and security
secureKeySchema.index({ keyId: 1, keyType: 1 });
secureKeySchema.index({ network: 1, keyType: 1 });
secureKeySchema.index({ 'rotation.nextRotation': 1, 'rotation.autoRotate': 1 });
secureKeySchema.index({ 'security.isActive': 1, 'security.isCompromised': 1 });
secureKeySchema.index({ 'auditTrail.timestamp': -1 });
secureKeySchema.index({ 'auditTrail.action': 1, 'auditTrail.timestamp': -1 });

// Virtual for rotation status
secureKeySchema.virtual('rotationStatus').get(function() {
  const now = new Date();
  const nextRotation = this.rotation.nextRotation;
  
  if (nextRotation <= now) {
    return 'overdue';
  } else if (nextRotation <= new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)) {
    return 'due_soon';
  } else {
    return 'current';
  }
});

// Virtual for security status
secureKeySchema.virtual('securityStatus').get(function() {
  if (this.security.isCompromised) return 'compromised';
  if (!this.security.isActive) return 'inactive';
  if (this.rotationStatus === 'overdue') return 'rotation_overdue';
  return 'secure';
});

// Pre-save middleware to set next rotation date
secureKeySchema.pre('save', function(next) {
  if (this.isNew || this.isModified('rotation.rotationInterval')) {
    this.rotation.nextRotation = new Date(
      this.rotation.lastRotated.getTime() + this.rotation.rotationInterval
    );
  }
  
  // Generate checksum for integrity verification
  if (this.isModified('encryptedData')) {
    const dataString = JSON.stringify(this.encryptedData);
    this.security.checksumHash = crypto
      .createHash('sha256')
      .update(dataString)
      .digest('hex');
  }
  
  next();
});

// Instance methods
secureKeySchema.methods.addAuditEntry = function(action, details = {}) {
  this.auditTrail.push({
    action,
    timestamp: new Date(),
    details,
    success: true
  });
  
  // Keep only last 100 audit entries
  if (this.auditTrail.length > 100) {
    this.auditTrail = this.auditTrail.slice(-100);
  }
  
  return this.save();
};

secureKeySchema.methods.markAccessed = function(serviceId, ipAddress) {
  this.access.lastAccessed = new Date();
  this.access.accessCount += 1;
  
  return this.addAuditEntry('accessed', {
    serviceId,
    ipAddress,
    accessCount: this.access.accessCount
  });
};

secureKeySchema.methods.markCompromised = function(reason, userId) {
  this.security.isCompromised = true;
  this.security.compromisedAt = new Date();
  this.security.compromisedReason = reason;
  this.security.isActive = false;
  
  return this.addAuditEntry('compromised', {
    reason,
    userId,
    immediate_action_required: true
  });
};

secureKeySchema.methods.rotateKey = function(newEncryptedData, userId) {
  this.encryptedData = newEncryptedData;
  this.rotation.lastRotated = new Date();
  this.rotation.rotationCount += 1;
  this.metadata.version += 1;
  
  return this.addAuditEntry('rotated', {
    userId,
    rotationCount: this.rotation.rotationCount,
    version: this.metadata.version
  });
};

// Static methods
secureKeySchema.statics.findKeysNeedingRotation = function() {
  return this.find({
    'rotation.autoRotate': true,
    'rotation.nextRotation': { $lte: new Date() },
    'security.isActive': true,
    'security.isCompromised': false
  });
};

secureKeySchema.statics.findCompromisedKeys = function() {
  return this.find({
    'security.isCompromised': true
  });
};

secureKeySchema.statics.getSecuritySummary = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$keyType',
        total: { $sum: 1 },
        active: { $sum: { $cond: ['$security.isActive', 1, 0] } },
        compromised: { $sum: { $cond: ['$security.isCompromised', 1, 0] } },
        needingRotation: { 
          $sum: { 
            $cond: [
              { $lte: ['$rotation.nextRotation', new Date()] }, 
              1, 
              0 
            ] 
          } 
        }
      }
    }
  ]);
};

module.exports = mongoose.model('SecureKey', secureKeySchema);
