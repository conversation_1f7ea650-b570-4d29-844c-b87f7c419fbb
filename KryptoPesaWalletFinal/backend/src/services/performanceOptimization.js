/**
 * Performance Optimization Service
 * Handles database connection pooling, query optimization, and performance monitoring
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

class PerformanceOptimizationService {
  constructor() {
    this.connectionPool = {
      maxPoolSize: parseInt(process.env.MONGO_MAX_POOL_SIZE) || 100, // Increased for 50K+ users
      minPoolSize: parseInt(process.env.MONGO_MIN_POOL_SIZE) || 10,
      maxIdleTimeMS: parseInt(process.env.MONGO_MAX_IDLE_TIME) || 30000,
      serverSelectionTimeoutMS: parseInt(process.env.MONGO_SERVER_SELECTION_TIMEOUT) || 5000,
      socketTimeoutMS: parseInt(process.env.MONGO_SOCKET_TIMEOUT) || 45000,
      maxConnecting: parseInt(process.env.MONGO_MAX_CONNECTING) || 10,
      heartbeatFrequencyMS: parseInt(process.env.MONGO_HEARTBEAT_FREQUENCY) || 10000
    };

    this.queryMetrics = {
      totalQueries: 0,
      slowQueries: 0,
      averageQueryTime: 0,
      cacheHitRate: 0,
      connectionPoolStats: {},
      throughputPerSecond: 0,
      concurrentConnections: 0,
      memoryUsage: 0
    };

    this.redisClient = getRedisClient();
    this.queryCache = new Map(); // In-memory cache for frequently accessed data
    this.rateLimitCache = new Map(); // Rate limiting cache
    this.initializeOptimizations();
  }

  /**
   * Initialize performance optimizations
   */
  initializeOptimizations() {
    // Configure mongoose for production performance
    mongoose.set('bufferCommands', false);

    // Enable query result caching
    mongoose.set('debug', process.env.NODE_ENV === 'development');

    // Monitor slow queries
    this.setupQueryMonitoring();

    // Setup connection pool monitoring
    this.setupConnectionPoolMonitoring();
  }

  /**
   * Setup query monitoring for performance tracking
   */
  setupQueryMonitoring() {
    const originalExec = mongoose.Query.prototype.exec;
    const self = this;

    mongoose.Query.prototype.exec = function() {
      const startTime = Date.now();
      const query = this;
      
      return originalExec.call(this).then(result => {
        const queryTime = Date.now() - startTime;
        self.trackQueryPerformance(query, queryTime);
        return result;
      }).catch(error => {
        const queryTime = Date.now() - startTime;
        self.trackQueryPerformance(query, queryTime, error);
        throw error;
      });
    };
  }

  /**
   * Track query performance metrics
   */
  trackQueryPerformance(query, queryTime, error = null) {
    this.queryMetrics.totalQueries++;
    
    // Update average query time
    const totalTime = this.queryMetrics.averageQueryTime * (this.queryMetrics.totalQueries - 1) + queryTime;
    this.queryMetrics.averageQueryTime = totalTime / this.queryMetrics.totalQueries;

    // Track slow queries (>500ms)
    if (queryTime > 500) {
      this.queryMetrics.slowQueries++;
      
      logger.warn('Slow query detected', {
        queryTime: `${queryTime}ms`,
        collection: query.model?.collection?.name,
        operation: query.op,
        conditions: JSON.stringify(query.getQuery()),
        error: error?.message
      });
    }

    // Log extremely slow queries (>2s)
    if (queryTime > 2000) {
      logger.error('Extremely slow query detected', {
        queryTime: `${queryTime}ms`,
        collection: query.model?.collection?.name,
        operation: query.op,
        conditions: JSON.stringify(query.getQuery()),
        options: JSON.stringify(query.getOptions()),
        error: error?.message
      });
    }
  }

  /**
   * Setup connection pool monitoring
   */
  setupConnectionPoolMonitoring() {
    if (mongoose.connection.readyState === 1) {
      this.monitorConnectionPool();
    } else {
      mongoose.connection.once('connected', () => {
        this.monitorConnectionPool();
      });
    }
  }

  /**
   * Monitor MongoDB connection pool
   */
  monitorConnectionPool() {
    setInterval(() => {
      try {
        const db = mongoose.connection.db;
        if (db && db.serverConfig) {
          const poolStats = {
            totalConnections: db.serverConfig.s?.pool?.totalConnectionCount || 0,
            availableConnections: db.serverConfig.s?.pool?.availableConnectionCount || 0,
            checkedOutConnections: db.serverConfig.s?.pool?.checkedOutConnectionCount || 0,
            timestamp: new Date()
          };

          this.queryMetrics.connectionPoolStats = poolStats;

          // Log warnings for pool exhaustion
          if (poolStats.availableConnections === 0 && poolStats.totalConnections > 0) {
            logger.warn('MongoDB connection pool exhausted', poolStats);
          }
        }
      } catch (error) {
        logger.error('Error monitoring connection pool:', error);
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Optimize database indexes
   */
  async optimizeIndexes() {
    try {
      const collections = await mongoose.connection.db.listCollections().toArray();
      
      for (const collection of collections) {
        const collectionName = collection.name;
        const indexes = await mongoose.connection.db.collection(collectionName).indexes();
        
        // Analyze index usage
        const indexStats = await mongoose.connection.db.collection(collectionName).aggregate([
          { $indexStats: {} }
        ]).toArray();

        // Log unused indexes
        for (const index of indexStats) {
          if (index.accesses.ops === 0 && index.name !== '_id_') {
            logger.warn(`Unused index detected: ${collectionName}.${index.name}`);
          }
        }
      }
    } catch (error) {
      logger.error('Error optimizing indexes:', error);
    }
  }

  /**
   * Implement query result caching
   */
  async cacheQuery(key, queryFunction, ttl = 300) {
    try {
      // Try to get from cache first
      if (this.redisClient && this.redisClient.isReady) {
        const cached = await this.redisClient.get(key);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      // Execute query
      const result = await queryFunction();

      // Cache the result
      if (this.redisClient && this.redisClient.isReady && result) {
        await this.redisClient.setex(key, ttl, JSON.stringify(result));
      }

      return result;
    } catch (error) {
      logger.error('Error in query caching:', error);
      // Fallback to direct query execution
      return await queryFunction();
    }
  }

  /**
   * Batch database operations for better performance
   */
  async batchOperation(operations, batchSize = 100) {
    const results = [];
    
    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      
      try {
        const batchResults = await Promise.all(batch);
        results.push(...batchResults);
      } catch (error) {
        logger.error(`Error in batch operation (batch ${Math.floor(i / batchSize) + 1}):`, error);
        throw error;
      }
    }
    
    return results;
  }

  /**
   * Optimize aggregation pipelines
   */
  optimizeAggregationPipeline(pipeline) {
    const optimized = [...pipeline];
    
    // Move $match stages to the beginning
    const matchStages = optimized.filter(stage => stage.$match);
    const otherStages = optimized.filter(stage => !stage.$match);
    
    // Add $limit early if not present and dealing with large datasets
    const hasLimit = otherStages.some(stage => stage.$limit);
    if (!hasLimit && otherStages.length > 3) {
      otherStages.splice(1, 0, { $limit: 10000 }); // Reasonable default limit
    }
    
    return [...matchStages, ...otherStages];
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    const slowQueryRate = this.queryMetrics.totalQueries > 0 
      ? (this.queryMetrics.slowQueries / this.queryMetrics.totalQueries * 100).toFixed(2)
      : 0;

    return {
      ...this.queryMetrics,
      slowQueryRate: `${slowQueryRate}%`,
      averageQueryTime: `${this.queryMetrics.averageQueryTime.toFixed(2)}ms`,
      recommendations: this.getPerformanceRecommendations()
    };
  }

  /**
   * Get performance recommendations
   */
  getPerformanceRecommendations() {
    const recommendations = [];
    
    if (this.queryMetrics.slowQueries > this.queryMetrics.totalQueries * 0.1) {
      recommendations.push('High number of slow queries detected. Consider adding indexes or optimizing queries.');
    }
    
    if (this.queryMetrics.averageQueryTime > 200) {
      recommendations.push('Average query time is high. Consider implementing query caching.');
    }
    
    const poolStats = this.queryMetrics.connectionPoolStats;
    if (poolStats.availableConnections < poolStats.totalConnections * 0.2) {
      recommendations.push('Connection pool utilization is high. Consider increasing pool size.');
    }
    
    return recommendations;
  }

  /**
   * Cleanup and optimize database
   */
  async performMaintenance() {
    try {
      logger.info('Starting database maintenance...');
      
      // Optimize indexes
      await this.optimizeIndexes();
      
      // Clear old performance metrics
      if (this.queryMetrics.totalQueries > 10000) {
        this.queryMetrics = {
          totalQueries: 0,
          slowQueries: 0,
          averageQueryTime: 0,
          cacheHitRate: 0,
          connectionPoolStats: this.queryMetrics.connectionPoolStats
        };
      }
      
      logger.info('Database maintenance completed');
    } catch (error) {
      logger.error('Error during database maintenance:', error);
    }
  }

  /**
   * Advanced multi-layer caching for 50K+ users
   */
  async getWithMultiLayerCache(key, queryFunction, options = {}) {
    const {
      ttl = 300, // 5 minutes default
      useMemoryCache = true,
      useRedisCache = true,
      compressionThreshold = 1024 // Compress data larger than 1KB
    } = options;

    try {
      // Layer 1: In-memory cache (fastest)
      if (useMemoryCache && this.queryCache.has(key)) {
        const cached = this.queryCache.get(key);
        if (cached.expires > Date.now()) {
          this.queryMetrics.cacheHitRate++;
          return cached.data;
        }
        this.queryCache.delete(key);
      }

      // Layer 2: Redis cache (fast)
      if (useRedisCache) {
        const redisData = await this.redisClient.get(key);
        if (redisData) {
          const parsed = JSON.parse(redisData);

          // Store in memory cache for next time
          if (useMemoryCache) {
            this.queryCache.set(key, {
              data: parsed,
              expires: Date.now() + (ttl * 1000)
            });
          }

          this.queryMetrics.cacheHitRate++;
          return parsed;
        }
      }

      // Layer 3: Database query (slowest)
      const result = await queryFunction();

      // Store in caches
      if (useRedisCache) {
        const serialized = JSON.stringify(result);
        await this.redisClient.setex(key, ttl, serialized);
      }

      if (useMemoryCache) {
        this.queryCache.set(key, {
          data: result,
          expires: Date.now() + (ttl * 1000)
        });
      }

      return result;
    } catch (error) {
      logger.error('Error in multi-layer cache:', error);
      return await queryFunction();
    }
  }

  /**
   * Auto-scaling metrics for infrastructure
   */
  getScalingMetrics() {
    const memUsage = process.memoryUsage();

    return {
      memoryUtilization: (memUsage.heapUsed / memUsage.heapTotal) * 100,
      connectionPoolUtilization: (this.queryMetrics.concurrentConnections / this.connectionPool.maxPoolSize) * 100,
      queryThroughput: this.queryMetrics.throughputPerSecond,
      averageResponseTime: this.queryMetrics.averageQueryTime,
      errorRate: (this.queryMetrics.slowQueries / this.queryMetrics.totalQueries) * 100,
      cacheHitRate: this.queryMetrics.cacheHitRate,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Cleanup memory caches periodically
   */
  startCacheCleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, value] of this.queryCache.entries()) {
        if (value.expires < now) {
          this.queryCache.delete(key);
        }
      }

      // Clean rate limit cache
      for (const [key, value] of this.rateLimitCache.entries()) {
        if (value.expires < now) {
          this.rateLimitCache.delete(key);
        }
      }
    }, 60000); // Clean every minute
  }
}

// Create singleton instance
const performanceOptimization = new PerformanceOptimizationService();

// Schedule maintenance every hour
setInterval(() => {
  performanceOptimization.performMaintenance();
}, 60 * 60 * 1000);

module.exports = {
  PerformanceOptimizationService,
  performanceOptimization
};
