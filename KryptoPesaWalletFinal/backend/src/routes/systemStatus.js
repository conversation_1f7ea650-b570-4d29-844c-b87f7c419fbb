/**
 * System Status Routes
 * API endpoints for graceful degradation communication and system status
 */

const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const { gracefulDegradationService } = require('../services/gracefulDegradation');
const { realTimeScalabilityService } = require('../services/realTimeScalabilityService');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Get current system status (public endpoint)
 */
router.get('/status', async (req, res, next) => {
  try {
    const systemStatus = gracefulDegradationService.getSystemStatus();
    
    // Remove sensitive information for public endpoint
    const publicStatus = {
      degradationLevel: systemStatus.degradationLevel,
      statusMessage: systemStatus.statusMessage,
      availableFeatures: systemStatus.availableFeatures,
      lastHealthCheck: systemStatus.lastHealthCheck,
      uptime: process.uptime()
    };

    res.json({
      success: true,
      data: publicStatus,
      timestamp: new Date()
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get detailed system status (admin only)
 */
router.get('/status/detailed', authenticate, authorize('admin', 'moderator'), async (req, res, next) => {
  try {
    const systemStatus = gracefulDegradationService.getSystemStatus();
    const degradationStats = gracefulDegradationService.getDegradationStatistics();

    res.json({
      success: true,
      data: {
        ...systemStatus,
        statistics: degradationStats,
        communicationConfig: gracefulDegradationService.getCommunicationConfig()
      },
      timestamp: new Date()
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get system status history (admin only)
 */
router.get('/status/history', [
  authenticate,
  authorize('admin', 'moderator'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('hours').optional().isInt({ min: 1, max: 168 }).withMessage('Hours must be between 1 and 168')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { limit = 50, hours = 24 } = req.query;
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    const systemStatus = gracefulDegradationService.getSystemStatus();
    const history = systemStatus.degradationHistory
      .filter(event => event.timestamp >= cutoffTime)
      .slice(-limit);

    res.json({
      success: true,
      data: {
        history,
        totalEvents: history.length,
        timeRange: `${hours} hours`,
        currentStatus: {
          degradationLevel: systemStatus.degradationLevel,
          statusMessage: systemStatus.statusMessage
        }
      },
      timestamp: new Date()
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get active alerts (admin only)
 */
router.get('/alerts', authenticate, authorize('admin', 'moderator'), async (req, res, next) => {
  try {
    const systemStatus = gracefulDegradationService.getSystemStatus();
    
    res.json({
      success: true,
      data: {
        alerts: systemStatus.activeAlerts,
        count: systemStatus.activeAlerts.length
      },
      timestamp: new Date()
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Acknowledge alert (admin only)
 */
router.post('/alerts/:alertId/acknowledge', [
  authenticate,
  authorize('admin', 'moderator'),
  body('notes').optional().isLength({ max: 500 }).withMessage('Notes cannot exceed 500 characters')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { alertId } = req.params;
    const { notes } = req.body;
    const adminId = req.user._id;

    const acknowledged = gracefulDegradationService.acknowledgeAlert(alertId, adminId);
    
    if (!acknowledged) {
      return res.status(404).json({
        success: false,
        message: 'Alert not found'
      });
    }

    // Log the acknowledgment
    logger.info(`Alert ${alertId} acknowledged by admin ${req.user.username}`, {
      alertId,
      adminId,
      notes
    });

    res.json({
      success: true,
      message: 'Alert acknowledged successfully',
      data: {
        alertId,
        acknowledgedBy: adminId,
        acknowledgedAt: new Date(),
        notes
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Update communication configuration (admin only)
 */
router.put('/communication/config', [
  authenticate,
  authorize('admin'),
  body('broadcastStatusChanges').optional().isBoolean().withMessage('broadcastStatusChanges must be boolean'),
  body('notifyAdmins').optional().isBoolean().withMessage('notifyAdmins must be boolean'),
  body('notifyUsers').optional().isBoolean().withMessage('notifyUsers must be boolean'),
  body('alertThresholds.partial').optional().isInt({ min: 1000 }).withMessage('Partial threshold must be at least 1000ms'),
  body('alertThresholds.severe').optional().isInt({ min: 1000 }).withMessage('Severe threshold must be at least 1000ms')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const config = req.body;
    gracefulDegradationService.updateCommunicationConfig(config);

    logger.info(`Communication config updated by admin ${req.user.username}`, {
      adminId: req.user._id,
      config
    });

    res.json({
      success: true,
      message: 'Communication configuration updated',
      data: gracefulDegradationService.getCommunicationConfig()
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Force degradation level (admin only - for testing)
 */
router.post('/force-degradation', [
  authenticate,
  authorize('admin'),
  body('level').isIn(['none', 'partial', 'severe']).withMessage('Invalid degradation level'),
  body('reason').isLength({ min: 1, max: 200 }).withMessage('Reason must be 1-200 characters')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { level, reason } = req.body;
    
    gracefulDegradationService.forceDegradationLevel(level);

    logger.warn(`Degradation level manually set by admin ${req.user.username}`, {
      adminId: req.user._id,
      level,
      reason
    });

    res.json({
      success: true,
      message: `Degradation level set to ${level}`,
      data: {
        level,
        reason,
        setBy: req.user._id,
        timestamp: new Date()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Broadcast system message (admin only)
 */
router.post('/broadcast', [
  authenticate,
  authorize('admin'),
  body('message').isLength({ min: 1, max: 500 }).withMessage('Message must be 1-500 characters'),
  body('type').optional().isIn(['info', 'warning', 'maintenance', 'emergency']).withMessage('Invalid message type'),
  body('priority').optional().isIn(['low', 'normal', 'high', 'urgent']).withMessage('Invalid priority'),
  body('targetUsers').optional().isArray().withMessage('targetUsers must be an array')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { message, type = 'info', priority = 'normal', targetUsers } = req.body;
    
    const broadcastMessage = {
      type: 'admin_broadcast',
      message,
      messageType: type,
      priority,
      timestamp: new Date(),
      sentBy: {
        id: req.user._id,
        username: req.user.username
      }
    };

    // Broadcast message
    if (targetUsers && targetUsers.length > 0) {
      // Send to specific users
      for (const userId of targetUsers) {
        await realTimeScalabilityService.sendToUser(userId, 'system_message', broadcastMessage);
      }
    } else {
      // Broadcast to all users
      await realTimeScalabilityService.broadcast('system_message', broadcastMessage);
    }

    logger.info(`System message broadcasted by admin ${req.user.username}`, {
      adminId: req.user._id,
      message,
      type,
      priority,
      targetUsers: targetUsers ? targetUsers.length : 'all'
    });

    res.json({
      success: true,
      message: 'System message broadcasted successfully',
      data: {
        broadcastMessage,
        targetCount: targetUsers ? targetUsers.length : 'all'
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get degradation statistics (admin only)
 */
router.get('/statistics', authenticate, authorize('admin', 'moderator'), async (req, res, next) => {
  try {
    const statistics = gracefulDegradationService.getDegradationStatistics();
    
    res.json({
      success: true,
      data: statistics,
      timestamp: new Date()
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
