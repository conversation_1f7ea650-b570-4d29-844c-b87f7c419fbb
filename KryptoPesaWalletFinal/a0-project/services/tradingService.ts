import { apiClient, API_ENDPOINTS, ApiResponse, PaginatedResponse } from './api';

// East African Trading Types
export type EastAfricanCurrency = 'KES' | 'TZS' | 'UGX' | 'RWF' | 'USD';
export type CryptoCurrency = 'USDT' | 'USDC' | 'DAI' | 'BTC' | 'ETH' | 'MATIC';
export type PaymentMethod = 'mpesa' | 'airtel_money' | 'bank_transfer' | 'cash';
export type EastAfricanCountry = 'KE' | 'TZ' | 'UG' | 'RW';

export interface Offer {
  _id: string;
  type: 'buy' | 'sell';
  cryptocurrency: {
    type: CryptoCurrency;
    minAmount: number;
    maxAmount: number;
    availableAmount: number;
  };
  fiat: {
    currency: EastAfricanCurrency;
    priceType: 'fixed' | 'market' | 'margin';
    fixedPrice?: number;
    marginPercentage?: number;
    currentPrice: number;
  };
  paymentMethods: PaymentMethod[];
  location: {
    country: EastAfricanCountry;
    city?: string;
  };
  terms: {
    timeLimit: number; // minutes
    instructions?: string;
    minimumReputation?: number;
  };
  trader: {
    _id: string;
    username: string;
    reputation: number;
    totalTrades: number;
    completionRate: number;
    isOnline: boolean;
  };
  status: 'active' | 'paused' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface Trade {
  _id: string;
  offerId: string;
  buyer: {
    _id: string;
    username: string;
    walletAddress: string;
  };
  seller: {
    _id: string;
    username: string;
    walletAddress: string;
  };
  cryptocurrency: {
    type: CryptoCurrency;
    amount: number;
    networkFee: number;
  };
  fiat: {
    currency: EastAfricanCurrency;
    amount: number;
    exchangeRate: number;
  };
  paymentMethod: PaymentMethod;
  paymentDetails: {
    accountNumber?: string;
    accountName?: string;
    bankName?: string;
    phoneNumber?: string;
  };
  status: 'pending' | 'funded' | 'payment_sent' | 'payment_confirmed' | 'completed' | 'disputed' | 'cancelled';
  timeline: Array<{
    status: string;
    timestamp: string;
    note?: string;
  }>;
  escrow: {
    address: string;
    txHash?: string;
    isReleased: boolean;
  };
  dispute?: {
    _id: string;
    reason: string;
    status: string;
  };
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface OfferFilters {
  type?: 'buy' | 'sell';
  cryptocurrency?: CryptoCurrency;
  fiatCurrency?: EastAfricanCurrency;
  country?: EastAfricanCountry;
  paymentMethod?: PaymentMethod;
  minAmount?: number;
  maxAmount?: number;
  sortBy?: 'price' | 'reputation' | 'created' | 'volume';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface CreateOfferData {
  type: 'buy' | 'sell';
  cryptocurrency: {
    type: CryptoCurrency;
    minAmount: number;
    maxAmount: number;
    availableAmount: number;
  };
  fiat: {
    currency: EastAfricanCurrency;
    priceType: 'fixed' | 'market' | 'margin';
    fixedPrice?: number;
    marginPercentage?: number;
  };
  paymentMethods: PaymentMethod[];
  location: {
    country: EastAfricanCountry;
    city?: string;
  };
  terms: {
    timeLimit: number;
    instructions?: string;
    minimumReputation?: number;
  };
}

export interface CreateTradeData {
  offerId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDetails: {
    accountNumber?: string;
    accountName?: string;
    bankName?: string;
    phoneNumber?: string;
  };
}

// Trading Service Class
class TradingService {
  /**
   * Get all offers with filters
   */
  async getOffers(filters?: OfferFilters): Promise<PaginatedResponse<Offer>> {
    try {
      const response = await apiClient.get<Offer[]>(
        API_ENDPOINTS.OFFERS.LIST,
        filters
      );

      return response as PaginatedResponse<Offer>;
    } catch (error) {
      console.error('Get offers error:', error);
      throw error;
    }
  }

  /**
   * Get specific offer by ID
   */
  async getOffer(offerId: string): Promise<Offer> {
    try {
      const response = await apiClient.get<Offer>(
        API_ENDPOINTS.OFFERS.GET(offerId)
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get offer');
      }
    } catch (error) {
      console.error('Get offer error:', error);
      throw error;
    }
  }

  /**
   * Create new offer
   */
  async createOffer(offerData: CreateOfferData): Promise<Offer> {
    try {
      const response = await apiClient.post<Offer>(
        API_ENDPOINTS.OFFERS.CREATE,
        offerData
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create offer');
      }
    } catch (error) {
      console.error('Create offer error:', error);
      throw error;
    }
  }

  /**
   * Update existing offer
   */
  async updateOffer(offerId: string, updateData: Partial<CreateOfferData>): Promise<Offer> {
    try {
      const response = await apiClient.put<Offer>(
        API_ENDPOINTS.OFFERS.UPDATE(offerId),
        updateData
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update offer');
      }
    } catch (error) {
      console.error('Update offer error:', error);
      throw error;
    }
  }

  /**
   * Delete offer
   */
  async deleteOffer(offerId: string): Promise<void> {
    try {
      const response = await apiClient.delete(
        API_ENDPOINTS.OFFERS.DELETE(offerId)
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to delete offer');
      }
    } catch (error) {
      console.error('Delete offer error:', error);
      throw error;
    }
  }

  /**
   * Create new trade
   */
  async createTrade(tradeData: CreateTradeData): Promise<Trade> {
    try {
      const response = await apiClient.post<Trade>(
        API_ENDPOINTS.TRADES.CREATE,
        tradeData
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create trade');
      }
    } catch (error) {
      console.error('Create trade error:', error);
      throw error;
    }
  }

  /**
   * Get specific trade by ID
   */
  async getTrade(tradeId: string): Promise<Trade> {
    try {
      const response = await apiClient.get<Trade>(
        API_ENDPOINTS.TRADES.GET(tradeId)
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get trade');
      }
    } catch (error) {
      console.error('Get trade error:', error);
      throw error;
    }
  }

  /**
   * Get active trades for current user
   */
  async getActiveTrades(): Promise<Trade[]> {
    try {
      const response = await apiClient.get<Trade[]>(
        API_ENDPOINTS.TRADES.ACTIVE
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get active trades');
      }
    } catch (error) {
      console.error('Get active trades error:', error);
      throw error;
    }
  }

  /**
   * Get trade history for current user
   */
  async getTradeHistory(limit?: number, offset?: number): Promise<PaginatedResponse<Trade>> {
    try {
      const response = await apiClient.get<Trade[]>(
        API_ENDPOINTS.TRADES.HISTORY,
        { limit, offset }
      );

      return response as PaginatedResponse<Trade>;
    } catch (error) {
      console.error('Get trade history error:', error);
      throw error;
    }
  }

  /**
   * Update trade status
   */
  async updateTradeStatus(tradeId: string, status: Trade['status'], note?: string): Promise<Trade> {
    try {
      const response = await apiClient.put<Trade>(
        API_ENDPOINTS.TRADES.UPDATE(tradeId),
        { status, note }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update trade status');
      }
    } catch (error) {
      console.error('Update trade status error:', error);
      throw error;
    }
  }

  /**
   * Cancel trade
   */
  async cancelTrade(tradeId: string, reason?: string): Promise<Trade> {
    try {
      const response = await apiClient.put<Trade>(
        API_ENDPOINTS.TRADES.UPDATE(tradeId),
        { status: 'cancelled', note: reason }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to cancel trade');
      }
    } catch (error) {
      console.error('Cancel trade error:', error);
      throw error;
    }
  }
}

// Create and export service instance
export const tradingService = new TradingService();

// East African market helpers
export const eastAfricanHelpers = {
  /**
   * Get currency symbol for East African currencies
   */
  getCurrencySymbol(currency: EastAfricanCurrency): string {
    const symbols = {
      KES: 'KSh',
      TZS: 'TSh',
      UGX: 'USh',
      RWF: 'RWF',
      USD: '$',
    };
    return symbols[currency];
  },

  /**
   * Get country name from code
   */
  getCountryName(countryCode: EastAfricanCountry): string {
    const countries = {
      KE: 'Kenya',
      TZ: 'Tanzania',
      UG: 'Uganda',
      RW: 'Rwanda',
    };
    return countries[countryCode];
  },

  /**
   * Get payment method display name
   */
  getPaymentMethodName(method: PaymentMethod): string {
    const names = {
      mpesa: 'M-Pesa',
      airtel_money: 'Airtel Money',
      bank_transfer: 'Bank Transfer',
      cash: 'Cash',
    };
    return names[method];
  },

  /**
   * Format currency amount with proper symbols
   */
  formatCurrencyAmount(amount: number, currency: EastAfricanCurrency): string {
    const symbol = this.getCurrencySymbol(currency);
    return `${symbol} ${amount.toLocaleString()}`;
  },

  /**
   * Get available payment methods by country
   */
  getPaymentMethodsByCountry(country: EastAfricanCountry): PaymentMethod[] {
    const methodsByCountry = {
      KE: ['mpesa', 'airtel_money', 'bank_transfer', 'cash'] as PaymentMethod[],
      TZ: ['mpesa', 'airtel_money', 'bank_transfer', 'cash'] as PaymentMethod[],
      UG: ['airtel_money', 'bank_transfer', 'cash'] as PaymentMethod[],
      RW: ['airtel_money', 'bank_transfer', 'cash'] as PaymentMethod[],
    };
    return methodsByCountry[country];
  },

  /**
   * Validate payment details based on method
   */
  validatePaymentDetails(method: PaymentMethod, details: any): boolean {
    switch (method) {
      case 'mpesa':
      case 'airtel_money':
        return !!(details.phoneNumber && /^\+254\d{9}$|^\+255\d{9}$|^\+256\d{9}$|^\+250\d{9}$/.test(details.phoneNumber));
      case 'bank_transfer':
        return !!(details.accountNumber && details.accountName && details.bankName);
      case 'cash':
        return true; // No specific validation for cash
      default:
        return false;
    }
  },
};

export default tradingService;
