<!DOCTYPE html>
<html>
<head>
    <title>KryptoPesa API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>KryptoPesa API Connection Test</h1>
    
    <div>
        <h3>Test Different Endpoints:</h3>
        <button onclick="testEndpoint('http://localhost:8000/api/')">Test localhost:8000</button>
        <button onclick="testEndpoint('http://***************:8000/api/')">Test ***************:8000</button>
        <button onclick="testEndpoint('http://********:8000/api/')">Test ********:8000</button>
    </div>
    
    <div>
        <h3>Test Registration:</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="testpassword123">
        <input type="text" id="firstName" placeholder="First Name" value="Test">
        <input type="text" id="lastName" placeholder="Last Name" value="User">
        <button onclick="testRegistration()">Test Registration</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
        }

        async function testEndpoint(url) {
            try {
                addResult(`Testing ${url}...`, true);
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.text();
                addResult(`✅ ${url} responded: ${data}`, true);
            } catch (error) {
                addResult(`❌ ${url} failed: ${error.message}`, false);
            }
        }

        async function testRegistration() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            
            const endpoints = [
                'http://localhost:8000/api/auth/register',
                'http://***************:8000/api/auth/register',
                'http://********:8000/api/auth/register'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    addResult(`Testing registration at ${endpoint}...`, true);
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            email,
                            password,
                            firstName,
                            lastName
                        })
                    });
                    
                    const data = await response.json();
                    addResult(`✅ ${endpoint} responded: ${JSON.stringify(data)}`, true);
                    break; // If one works, stop testing others
                } catch (error) {
                    addResult(`❌ ${endpoint} failed: ${error.message}`, false);
                }
            }
        }
    </script>
</body>
</html>
