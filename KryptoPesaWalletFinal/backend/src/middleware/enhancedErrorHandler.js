/**
 * Enhanced Error Handler with User-Friendly Messages
 * Provides graceful error handling with multilingual support
 */

const logger = require('../utils/logger');
const { AppError } = require('./errorHandler');

class EnhancedErrorHandler {
  constructor() {
    this.errorMetrics = {
      totalErrors: 0,
      errorCounts: {},
      lastError: null,
      startTime: Date.now()
    };
  }

  /**
   * Enhanced error response with user-friendly messages
   */
  createUserFriendlyResponse(error, req) {
    const userLanguage = req.headers['accept-language']?.startsWith('sw') ? 'sw' : 'en';
    
    const friendlyMessages = {
      en: {
        VALIDATION_ERROR: 'Please check your input and try again.',
        AUTHENTICATION_ERROR: 'Please log in to continue.',
        AUTHORIZATION_ERROR: 'You do not have permission to perform this action.',
        NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
        SERVER_ERROR: 'Something went wrong on our end. Please try again later.',
        RATE_LIMIT_ERROR: 'Too many requests. Please wait a moment and try again.',
        WALLET_ERROR: 'Wallet operation failed. Please check your wallet connection.',
        TRADE_ERROR: 'Trading operation failed. Please try again.',
        KYC_ERROR: 'Verification process failed. Please check your documents.',
        INSUFFICIENT_FUNDS: 'Insufficient funds for this transaction.',
        INVALID_ADDRESS: 'Invalid cryptocurrency address.',
        TRANSACTION_FAILED: 'Transaction failed. Please try again.',
        MAINTENANCE_MODE: 'System is under maintenance. Please try again later.',
        FILE_UPLOAD_ERROR: 'File upload failed. Please check file size and format.',
        DATABASE_ERROR: 'Database operation failed. Please try again.',
        EXTERNAL_SERVICE_ERROR: 'External service unavailable. Please try again later.'
      },
      sw: {
        VALIDATION_ERROR: 'Tafadhali kagua maelezo yako na ujaribu tena.',
        AUTHENTICATION_ERROR: 'Tafadhali ingia ili kuendelea.',
        AUTHORIZATION_ERROR: 'Huna ruhusa ya kufanya kitendo hiki.',
        NETWORK_ERROR: 'Hitilafu ya muunganisho wa mtandao. Tafadhali kagua muunganisho wako wa intaneti.',
        SERVER_ERROR: 'Kuna tatizo upande wetu. Tafadhali jaribu tena baadaye.',
        RATE_LIMIT_ERROR: 'Maombi mengi sana. Tafadhali subiri kidogo na ujaribu tena.',
        WALLET_ERROR: 'Uendeshaji wa pochi umeshindwa. Tafadhali kagua muunganisho wa pochi yako.',
        TRADE_ERROR: 'Uendeshaji wa biashara umeshindwa. Tafadhali jaribu tena.',
        KYC_ERROR: 'Mchakato wa uthibitishaji umeshindwa. Tafadhali kagua hati zako.',
        INSUFFICIENT_FUNDS: 'Fedha hazitoshi kwa muamala huu.',
        INVALID_ADDRESS: 'Anwani ya sarafu ya kidijitali si sahihi.',
        TRANSACTION_FAILED: 'Muamala umeshindwa. Tafadhali jaribu tena.',
        MAINTENANCE_MODE: 'Mfumo upo chini ya matengenezo. Tafadhali jaribu tena baadaye.',
        FILE_UPLOAD_ERROR: 'Upakiaji wa faili umeshindwa. Tafadhali kagua ukubwa na muundo wa faili.',
        DATABASE_ERROR: 'Uendeshaji wa hifadhidata umeshindwa. Tafadhali jaribu tena.',
        EXTERNAL_SERVICE_ERROR: 'Huduma ya nje haipatikani. Tafadhali jaribu tena baadaye.'
      }
    };

    const errorType = this.categorizeError(error);
    const message = friendlyMessages[userLanguage][errorType] || friendlyMessages[userLanguage].SERVER_ERROR;

    return {
      success: false,
      error: {
        type: errorType,
        message,
        code: error.code || error.errorCode || 'UNKNOWN_ERROR',
        timestamp: new Date().toISOString(),
        requestId: req.id || `req_${Date.now()}`,
        ...(process.env.NODE_ENV === 'development' && {
          details: error.message,
          stack: error.stack
        })
      },
      // Add helpful suggestions based on error type
      suggestions: this.getErrorSuggestions(errorType, userLanguage)
    };
  }

  /**
   * Get helpful suggestions based on error type
   */
  getErrorSuggestions(errorType, language) {
    const suggestions = {
      en: {
        NETWORK_ERROR: [
          'Check your internet connection',
          'Try switching between WiFi and mobile data',
          'Restart the app if the problem persists'
        ],
        WALLET_ERROR: [
          'Ensure your wallet is properly connected',
          'Check if you have sufficient gas fees',
          'Verify your wallet address is correct'
        ],
        TRADE_ERROR: [
          'Check if the offer is still available',
          'Verify you have sufficient balance',
          'Contact support if the issue continues'
        ],
        VALIDATION_ERROR: [
          'Double-check all required fields',
          'Ensure data formats are correct',
          'Remove any special characters if not allowed'
        ]
      },
      sw: {
        NETWORK_ERROR: [
          'Kagua muunganisho wako wa intaneti',
          'Jaribu kubadilisha kati ya WiFi na data ya simu',
          'Zima na uwashe programu ikiwa tatizo linaendelea'
        ],
        WALLET_ERROR: [
          'Hakikisha pochi yako imeunganishwa vizuri',
          'Kagua kama una ada za kutosha za gesi',
          'Thibitisha anwani ya pochi yako ni sahihi'
        ],
        TRADE_ERROR: [
          'Kagua kama ofa bado inapatikana',
          'Thibitisha una salio la kutosha',
          'Wasiliana na msaada ikiwa tatizo linaendelea'
        ],
        VALIDATION_ERROR: [
          'Kagua tena sehemu zote zinazohitajika',
          'Hakikisha miundo ya data ni sahihi',
          'Ondoa alama maalum zisizoruhusiwa'
        ]
      }
    };

    return suggestions[language][errorType] || suggestions[language].NETWORK_ERROR || [];
  }

  /**
   * Categorize error for user-friendly messaging
   */
  categorizeError(error) {
    // Check error codes first
    if (error.errorCode) {
      if (error.errorCode.includes('VALIDATION')) return 'VALIDATION_ERROR';
      if (error.errorCode.includes('AUTH')) return 'AUTHENTICATION_ERROR';
      if (error.errorCode.includes('FORBIDDEN')) return 'AUTHORIZATION_ERROR';
      if (error.errorCode.includes('WALLET')) return 'WALLET_ERROR';
      if (error.errorCode.includes('TRADE')) return 'TRADE_ERROR';
      if (error.errorCode.includes('KYC')) return 'KYC_ERROR';
    }

    // Check error names
    if (error.name === 'ValidationError' || error.code === 'VALIDATION_ERROR') {
      return 'VALIDATION_ERROR';
    }
    
    if (error.name === 'UnauthorizedError' || error.code === 'UNAUTHORIZED') {
      return 'AUTHENTICATION_ERROR';
    }
    
    if (error.name === 'ForbiddenError' || error.code === 'FORBIDDEN') {
      return 'AUTHORIZATION_ERROR';
    }
    
    // Check error messages for specific patterns
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('network') || error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return 'NETWORK_ERROR';
    }
    
    if (message.includes('rate limit') || error.code === 'RATE_LIMIT_EXCEEDED') {
      return 'RATE_LIMIT_ERROR';
    }
    
    if (message.includes('wallet') || message.includes('signature') || message.includes('private key')) {
      return 'WALLET_ERROR';
    }
    
    if (message.includes('trade') || message.includes('offer') || message.includes('escrow')) {
      return 'TRADE_ERROR';
    }
    
    if (message.includes('kyc') || message.includes('verification') || message.includes('document')) {
      return 'KYC_ERROR';
    }
    
    if (message.includes('insufficient funds') || message.includes('balance')) {
      return 'INSUFFICIENT_FUNDS';
    }
    
    if (message.includes('invalid address') || message.includes('address format')) {
      return 'INVALID_ADDRESS';
    }
    
    if (message.includes('transaction failed') || message.includes('tx failed')) {
      return 'TRANSACTION_FAILED';
    }
    
    if (message.includes('maintenance') || message.includes('service unavailable')) {
      return 'MAINTENANCE_MODE';
    }
    
    if (message.includes('file') || message.includes('upload') || message.includes('multipart')) {
      return 'FILE_UPLOAD_ERROR';
    }
    
    if (message.includes('database') || message.includes('mongodb') || message.includes('connection')) {
      return 'DATABASE_ERROR';
    }
    
    if (message.includes('external') || message.includes('api') || message.includes('service')) {
      return 'EXTERNAL_SERVICE_ERROR';
    }
    
    return 'SERVER_ERROR';
  }

  /**
   * Enhanced error middleware with graceful degradation
   */
  getEnhancedErrorMiddleware() {
    return (error, req, res, next) => {
      try {
        // Log the error
        this.logError(error, req);

        // Track error metrics
        this.trackErrorMetrics(error);

        // Create user-friendly response
        const response = this.createUserFriendlyResponse(error, req);

        // Determine status code
        const statusCode = this.getStatusCode(error);

        // Add retry information for certain errors
        if (this.isRetryableError(error)) {
          response.retryable = true;
          response.retryAfter = this.getRetryDelay(error);
        }

        // Send response
        res.status(statusCode).json(response);

      } catch (handlerError) {
        // Fallback error handling
        logger.error('Error in enhanced error handler:', handlerError);
        
        res.status(500).json({
          success: false,
          error: {
            type: 'SERVER_ERROR',
            message: 'An unexpected error occurred',
            timestamp: new Date().toISOString(),
            requestId: req.id || `req_${Date.now()}`
          }
        });
      }
    };
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error) {
    const retryableTypes = [
      'NETWORK_ERROR',
      'DATABASE_ERROR',
      'EXTERNAL_SERVICE_ERROR',
      'RATE_LIMIT_ERROR'
    ];
    
    const errorType = this.categorizeError(error);
    return retryableTypes.includes(errorType);
  }

  /**
   * Get retry delay based on error type
   */
  getRetryDelay(error) {
    const errorType = this.categorizeError(error);
    
    switch (errorType) {
      case 'RATE_LIMIT_ERROR': return 60; // 1 minute
      case 'NETWORK_ERROR': return 5; // 5 seconds
      case 'DATABASE_ERROR': return 10; // 10 seconds
      case 'EXTERNAL_SERVICE_ERROR': return 30; // 30 seconds
      default: return 5;
    }
  }

  /**
   * Get appropriate HTTP status code for error
   */
  getStatusCode(error) {
    if (error.statusCode) return error.statusCode;
    if (error.status) return error.status;
    
    const errorType = this.categorizeError(error);
    
    switch (errorType) {
      case 'VALIDATION_ERROR': return 400;
      case 'AUTHENTICATION_ERROR': return 401;
      case 'AUTHORIZATION_ERROR': return 403;
      case 'RATE_LIMIT_ERROR': return 429;
      case 'MAINTENANCE_MODE': return 503;
      default: return 500;
    }
  }

  /**
   * Log error with context
   */
  logError(error, req) {
    const errorContext = {
      message: error.message,
      stack: error.stack,
      type: this.categorizeError(error),
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      timestamp: new Date().toISOString()
    };

    if (error.statusCode >= 500) {
      logger.error('Server Error:', errorContext);
    } else {
      logger.warn('Client Error:', errorContext);
    }
  }

  /**
   * Track error metrics for monitoring
   */
  trackErrorMetrics(error) {
    const errorType = this.categorizeError(error);
    
    if (!this.errorMetrics.errorCounts[errorType]) {
      this.errorMetrics.errorCounts[errorType] = 0;
    }
    
    this.errorMetrics.errorCounts[errorType]++;
    this.errorMetrics.totalErrors++;
    this.errorMetrics.lastError = {
      type: errorType,
      message: error.message,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get error metrics for monitoring dashboard
   */
  getErrorMetrics() {
    return {
      ...this.errorMetrics,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      errorRate: this.calculateErrorRate()
    };
  }

  /**
   * Calculate error rate
   */
  calculateErrorRate() {
    const uptimeHours = process.uptime() / 3600;
    return uptimeHours > 0 ? this.errorMetrics.totalErrors / uptimeHours : 0;
  }

  /**
   * Reset error metrics
   */
  resetMetrics() {
    this.errorMetrics = {
      totalErrors: 0,
      errorCounts: {},
      lastError: null,
      startTime: Date.now()
    };
  }
}

module.exports = new EnhancedErrorHandler();
