const validator = require('validator');
const xss = require('xss');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');

/**
 * Advanced input sanitization and validation middleware
 */

// XSS protection configuration
const xssOptions = {
  whiteList: {}, // No HTML tags allowed
  stripIgnoreTag: true,
  stripIgnoreTagBody: ['script'],
};

// Dangerous patterns that should be blocked
const DANGEROUS_PATTERNS = [
  // SQL injection patterns
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
  // NoSQL injection patterns
  /(\$where|\$ne|\$gt|\$lt|\$gte|\$lte|\$in|\$nin|\$regex|\$exists)/i,
  // JavaScript injection patterns
  /(javascript:|data:|vbscript:|onload|onerror|onclick)/i,
  // Command injection patterns
  /(;|\||&|`|\$\(|\${)/,
  // Path traversal patterns
  /(\.\.\/|\.\.\\|%2e%2e%2f|%2e%2e%5c)/i,
];

// Cryptocurrency address validation patterns
const CRYPTO_ADDRESS_PATTERNS = {
  ethereum: /^0x[a-fA-F0-9]{40}$/,
  bitcoin: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/,
  polygon: /^0x[a-fA-F0-9]{40}$/,
};

/**
 * Sanitize string input
 */
const sanitizeString = (input, maxLength = 1000) => {
  if (typeof input !== 'string') return input;
  
  // Remove XSS
  let sanitized = xss(input, xssOptions);
  
  // Trim whitespace
  sanitized = sanitized.trim();
  
  // Limit length
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }
  
  return sanitized;
};

/**
 * Check for dangerous patterns
 */
const containsDangerousPattern = (input) => {
  if (typeof input !== 'string') return false;
  
  return DANGEROUS_PATTERNS.some(pattern => pattern.test(input));
};

/**
 * Validate cryptocurrency address
 */
const validateCryptoAddress = (address, network) => {
  if (!address || typeof address !== 'string') return false;
  
  const pattern = CRYPTO_ADDRESS_PATTERNS[network.toLowerCase()];
  return pattern ? pattern.test(address) : false;
};

/**
 * Validate email with additional security checks
 */
const validateEmail = (email) => {
  if (!email || typeof email !== 'string') return false;
  
  // Basic email validation
  if (!validator.isEmail(email)) return false;
  
  // Check for dangerous patterns
  if (containsDangerousPattern(email)) return false;
  
  // Check length
  if (email.length > 254) return false;
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /\+.*\+/, // Multiple plus signs
    /\.{2,}/, // Multiple consecutive dots
    /@.*@/, // Multiple @ symbols
  ];
  
  return !suspiciousPatterns.some(pattern => pattern.test(email));
};

/**
 * Validate phone number
 */
const validatePhoneNumber = (phone) => {
  if (!phone || typeof phone !== 'string') return false;
  
  // Must start with + and contain only digits
  if (!/^\+[1-9]\d{1,14}$/.test(phone)) return false;
  
  // Check for dangerous patterns
  if (containsDangerousPattern(phone)) return false;
  
  return true;
};

/**
 * Validate monetary amount
 */
const validateAmount = (amount, min = 0, max = Number.MAX_SAFE_INTEGER) => {
  const num = parseFloat(amount);
  
  if (isNaN(num)) return false;
  if (num < min || num > max) return false;
  if (!isFinite(num)) return false;
  
  // Check for reasonable decimal places (max 18 for crypto)
  const decimalPlaces = (amount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 18) return false;
  
  return true;
};

/**
 * Recursive object sanitization
 */
const sanitizeObject = (obj, depth = 0) => {
  if (depth > 10) return obj; // Prevent deep recursion
  
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, depth + 1));
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized = {};
    
    for (const [key, value] of Object.entries(obj)) {
      // Sanitize key
      const sanitizedKey = sanitizeString(key, 100);
      
      // Skip dangerous keys
      if (containsDangerousPattern(sanitizedKey)) {
        logger.warn('Dangerous key detected and removed:', key);
        continue;
      }
      
      // Sanitize value
      if (typeof value === 'string') {
        sanitized[sanitizedKey] = sanitizeString(value);
        
        // Check for dangerous patterns in values
        if (containsDangerousPattern(sanitized[sanitizedKey])) {
          logger.warn('Dangerous pattern detected in value:', value);
          throw new AppError('Invalid input detected', 400);
        }
      } else {
        sanitized[sanitizedKey] = sanitizeObject(value, depth + 1);
      }
    }
    
    return sanitized;
  }
  
  return obj;
};

/**
 * Main security validation middleware
 */
const securityValidation = (req, res, next) => {
  try {
    // Log suspicious requests
    const userAgent = req.get('User-Agent') || '';
    const suspiciousUserAgents = [
      'sqlmap',
      'nikto',
      'nmap',
      'masscan',
      'curl', // Be careful with this in production
    ];
    
    if (suspiciousUserAgents.some(agent => userAgent.toLowerCase().includes(agent))) {
      logger.warn('Suspicious user agent detected:', {
        ip: req.ip,
        userAgent,
        endpoint: req.originalUrl,
      });
    }
    
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }
    
    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }
    
    // Sanitize URL parameters
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeObject(req.params);
    }
    
    // Check for oversized requests
    const contentLength = parseInt(req.get('Content-Length')) || 0;
    if (contentLength > 50 * 1024 * 1024) { // 50MB absolute limit
      logger.warn('Oversized request detected:', {
        ip: req.ip,
        contentLength,
        endpoint: req.originalUrl,
      });
      throw new AppError('Request too large', 413);
    }
    
    next();
  } catch (error) {
    if (error instanceof AppError) {
      next(error);
    } else {
      logger.error('Security validation error:', error);
      next(new AppError('Invalid request format', 400));
    }
  }
};

/**
 * Specific validation for trade data
 */
const validateTradeData = (req, res, next) => {
  const { body } = req;
  
  try {
    // Validate cryptocurrency amounts
    if (body.amount && !validateAmount(body.amount, 0.000001, 1000000)) {
      throw new AppError('Invalid cryptocurrency amount', 400);
    }
    
    // Validate fiat amounts
    if (body.fiatAmount && !validateAmount(body.fiatAmount, 1, 10000000)) {
      throw new AppError('Invalid fiat amount', 400);
    }
    
    // Validate cryptocurrency addresses
    if (body.address && body.network) {
      if (!validateCryptoAddress(body.address, body.network)) {
        throw new AppError('Invalid cryptocurrency address', 400);
      }
    }
    
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Specific validation for user data
 */
const validateUserData = (req, res, next) => {
  const { body } = req;
  
  try {
    // Validate email
    if (body.email && !validateEmail(body.email)) {
      throw new AppError('Invalid email address', 400);
    }
    
    // Validate phone
    if (body.phone && !validatePhoneNumber(body.phone)) {
      throw new AppError('Invalid phone number', 400);
    }
    
    // Validate password strength
    if (body.password) {
      if (body.password.length < 8) {
        throw new AppError('Password must be at least 8 characters', 400);
      }
      
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(body.password)) {
        throw new AppError('Password must contain uppercase, lowercase, and number', 400);
      }
      
      if (containsDangerousPattern(body.password)) {
        throw new AppError('Password contains invalid characters', 400);
      }
    }
    
    next();
  } catch (error) {
    next(error);
  }
};

module.exports = {
  securityValidation,
  validateTradeData,
  validateUserData,
  sanitizeString,
  validateCryptoAddress,
  validateEmail,
  validatePhoneNumber,
  validateAmount,
  containsDangerousPattern,
};
