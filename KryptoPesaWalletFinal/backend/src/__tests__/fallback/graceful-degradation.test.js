/**
 * Graceful Degradation Testing
 * Tests system behavior under various degradation scenarios
 */

// Set up test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long-for-testing';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-chars-long-for-testing';
process.env.SESSION_SECRET = 'test-session-secret-32-chars-long-for-testing';

const gracefulDegradationService = require('../../services/gracefulDegradation');

describe('Graceful Degradation Testing', () => {
  beforeEach(() => {
    // Reset graceful degradation service
    gracefulDegradationService.serviceStatus = {
      redis: 'healthy',
      blockchain: 'healthy',
      database: 'healthy',
      external: 'healthy'
    };
    gracefulDegradationService.degradationLevel = 'none';
    gracefulDegradationService.degradationHistory = [];
    gracefulDegradationService.activeAlerts.clear();
  });

  describe('Service Status Management', () => {
    test('should update service status correctly', () => {
      gracefulDegradationService.updateServiceStatus('redis', 'unhealthy');
      
      expect(gracefulDegradationService.serviceStatus.redis).toBe('unhealthy');
      expect(gracefulDegradationService.serviceStatus.database).toBe('healthy');
    });

    test('should calculate degradation level based on service status', () => {
      // Single service failure should trigger partial degradation
      gracefulDegradationService.updateServiceStatus('redis', 'unhealthy');
      gracefulDegradationService.calculateDegradationLevel();
      
      expect(gracefulDegradationService.degradationLevel).toBe('partial');
      
      // Multiple service failures should trigger severe degradation
      gracefulDegradationService.updateServiceStatus('blockchain', 'unhealthy');
      gracefulDegradationService.calculateDegradationLevel();
      
      expect(gracefulDegradationService.degradationLevel).toBe('severe');
    });

    test('should track degradation history', () => {
      gracefulDegradationService.updateServiceStatus('redis', 'unhealthy');
      
      expect(gracefulDegradationService.degradationHistory).toHaveLength(1);
      expect(gracefulDegradationService.degradationHistory[0]).toMatchObject({
        from: 'none',
        to: 'partial',
        service: 'redis',
        timestamp: expect.any(Number)
      });
    });
  });

  describe('Feature Availability Management', () => {
    test('should return correct available features for healthy system', () => {
      const features = gracefulDegradationService.getAvailableFeatures();
      
      expect(features).toEqual({
        trading: true,
        walletOperations: true,
        realTimeUpdates: true,
        notifications: true,
        analytics: true,
        reporting: true
      });
    });

    test('should disable appropriate features during partial degradation', () => {
      gracefulDegradationService.updateServiceStatus('redis', 'unhealthy');
      
      const features = gracefulDegradationService.getAvailableFeatures();
      
      expect(features.trading).toBe(true);
      expect(features.walletOperations).toBe(true);
      expect(features.realTimeUpdates).toBe(false); // Redis dependent
      expect(features.analytics).toBe(false); // Non-critical
      expect(features.notifications).toBe(true);
      expect(features.reporting).toBe(true);
    });

    test('should disable more features during severe degradation', () => {
      gracefulDegradationService.updateServiceStatus('redis', 'unhealthy');
      gracefulDegradationService.updateServiceStatus('blockchain', 'unhealthy');
      
      const features = gracefulDegradationService.getAvailableFeatures();
      
      expect(features.trading).toBe(true); // Core feature remains
      expect(features.walletOperations).toBe(false); // Blockchain dependent
      expect(features.realTimeUpdates).toBe(false);
      expect(features.notifications).toBe(false);
      expect(features.analytics).toBe(false);
      expect(features.reporting).toBe(false);
    });

    test('should identify affected features correctly', () => {
      const affectedFeatures = gracefulDegradationService.getAffectedFeatures('none', 'partial');
      
      expect(affectedFeatures.disabled).toContain('analytics');
      expect(affectedFeatures.limited).toContain('realTimeUpdates');
      expect(affectedFeatures.enabled).toHaveLength(0);
    });
  });

  describe('Fallback Data Management', () => {
    test('should store and retrieve fallback data', async () => {
      const key = 'test:data';
      const data = { value: 'test', timestamp: Date.now() };
      const ttl = 3600000; // 1 hour

      // Mock fetch function
      const fetchFunction = jest.fn().mockResolvedValue(data);

      // Should fetch and store data
      const result = await gracefulDegradationService.getWithFallback(key, fetchFunction, ttl);
      
      expect(result).toEqual(data);
      expect(fetchFunction).toHaveBeenCalled();
      
      // Should return cached data on subsequent calls
      const cachedResult = await gracefulDegradationService.getWithFallback(key, fetchFunction, ttl);
      
      expect(cachedResult).toEqual(data);
      expect(fetchFunction).toHaveBeenCalledTimes(1); // Should not fetch again
    });

    test('should return stale data when fetch fails', async () => {
      const key = 'test:stale:data';
      const staleData = { value: 'stale', timestamp: Date.now() - 7200000 }; // 2 hours old
      const ttl = 3600000; // 1 hour

      // Store stale data
      gracefulDegradationService.fallbackData.set(key, staleData);

      // Mock failing fetch function
      const failingFetchFunction = jest.fn().mockRejectedValue(new Error('Fetch failed'));

      // Should return stale data when fetch fails
      const result = await gracefulDegradationService.getWithFallback(key, failingFetchFunction, ttl);
      
      expect(result).toEqual(staleData.data);
      expect(failingFetchFunction).toHaveBeenCalled();
    });

    test('should throw error when no fallback data available', async () => {
      const key = 'test:no:fallback';
      const failingFetchFunction = jest.fn().mockRejectedValue(new Error('Fetch failed'));

      // Should throw error when no fallback data available
      await expect(
        gracefulDegradationService.getWithFallback(key, failingFetchFunction, 3600000)
      ).rejects.toThrow('Fetch failed');
    });
  });

  describe('Communication and Alerting', () => {
    test('should generate admin notification messages', () => {
      const message = gracefulDegradationService.generateAdminNotificationMessage('partial', {
        disabled: ['analytics'],
        limited: ['realTimeUpdates'],
        enabled: []
      });

      expect(message).toContain('System Degradation Alert');
      expect(message).toContain('partial degradation');
      expect(message).toContain('analytics');
      expect(message).toContain('realTimeUpdates');
    });

    test('should generate user notification messages', () => {
      const message = gracefulDegradationService.generateUserNotificationMessage('partial', {
        disabled: ['analytics'],
        limited: ['realTimeUpdates'],
        enabled: []
      });

      expect(message).toContain('Service Notice');
      expect(message).toContain('limited functionality');
      expect(message).toContain('real-time updates');
    });

    test('should track active alerts', () => {
      gracefulDegradationService.updateActiveAlerts('partial', {
        disabled: ['analytics'],
        limited: ['realTimeUpdates'],
        enabled: []
      });

      expect(gracefulDegradationService.activeAlerts.has('degradation:partial')).toBe(true);
      
      const alert = gracefulDegradationService.activeAlerts.get('degradation:partial');
      expect(alert.level).toBe('partial');
      expect(alert.affectedFeatures.disabled).toContain('analytics');
    });

    test('should handle degradation level changes with communication', () => {
      const mockBroadcast = jest.fn();
      const mockNotifyAdmins = jest.fn();
      const mockNotifyUsers = jest.fn();

      // Mock communication methods
      gracefulDegradationService.broadcastStatusChange = mockBroadcast;
      gracefulDegradationService.notifyAdmins = mockNotifyAdmins;
      gracefulDegradationService.notifyUsers = mockNotifyUsers;

      gracefulDegradationService.handleDegradationLevelChange('none', 'partial');

      expect(mockBroadcast).toHaveBeenCalled();
      expect(mockNotifyAdmins).toHaveBeenCalled();
      expect(mockNotifyUsers).toHaveBeenCalled();
    });
  });

  describe('Degradation Statistics', () => {
    test('should calculate degradation statistics correctly', () => {
      // Add some degradation history
      gracefulDegradationService.degradationHistory = [
        { from: 'none', to: 'partial', timestamp: Date.now() - 3600000, service: 'redis' },
        { from: 'partial', to: 'severe', timestamp: Date.now() - 1800000, service: 'blockchain' },
        { from: 'severe', to: 'partial', timestamp: Date.now() - 900000, service: 'blockchain' },
        { from: 'partial', to: 'none', timestamp: Date.now() - 300000, service: 'redis' }
      ];

      const stats = gracefulDegradationService.getDegradationStatistics();

      expect(stats.totalEvents).toBe(4);
      expect(stats.currentLevel).toBe('none');
      expect(stats.timeInDegradation).toBeGreaterThan(0);
      expect(stats.mostAffectedService).toBeDefined();
      expect(stats.averageDegradationDuration).toBeGreaterThan(0);
    });

    test('should handle empty degradation history', () => {
      const stats = gracefulDegradationService.getDegradationStatistics();

      expect(stats.totalEvents).toBe(0);
      expect(stats.currentLevel).toBe('none');
      expect(stats.timeInDegradation).toBe(0);
      expect(stats.mostAffectedService).toBe('none');
      expect(stats.averageDegradationDuration).toBe(0);
    });
  });

  describe('Force Degradation for Testing', () => {
    test('should allow forcing degradation for testing', () => {
      gracefulDegradationService.forceDegradation('severe', 'Testing severe degradation scenario');

      expect(gracefulDegradationService.degradationLevel).toBe('severe');
      expect(gracefulDegradationService.degradationHistory).toHaveLength(1);
      expect(gracefulDegradationService.degradationHistory[0].reason).toBe('Testing severe degradation scenario');
    });

    test('should restore normal operation after forced degradation', () => {
      gracefulDegradationService.forceDegradation('partial', 'Testing');
      expect(gracefulDegradationService.degradationLevel).toBe('partial');

      gracefulDegradationService.forceDegradation('none', 'Restoring normal operation');
      expect(gracefulDegradationService.degradationLevel).toBe('none');
    });
  });

  describe('Configuration Management', () => {
    test('should update communication configuration', () => {
      const newConfig = {
        broadcastStatusChanges: false,
        notifyAdmins: true,
        notifyUsers: false,
        alertThresholds: {
          partial: 60000,
          severe: 30000
        }
      };

      gracefulDegradationService.updateCommunicationConfig(newConfig);

      expect(gracefulDegradationService.communicationConfig.broadcastStatusChanges).toBe(false);
      expect(gracefulDegradationService.communicationConfig.alertThresholds.partial).toBe(60000);
    });

    test('should validate configuration updates', () => {
      const invalidConfig = {
        alertThresholds: {
          partial: -1000, // Invalid negative threshold
          severe: 'invalid' // Invalid type
        }
      };

      expect(() => {
        gracefulDegradationService.updateCommunicationConfig(invalidConfig);
      }).toThrow();
    });
  });
});
