import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

// Mock offer data - in a real app this would come from Convex
const mockOffer = {
  id: '1',
  type: 'sell',
  cryptoCurrency: 'BTC',
  fiatCurrency: 'USD',
  price: 45250.00,
  minAmount: 100,
  maxAmount: 5000,
  paymentMethods: ['Bank Transfer', 'PayPal', 'Wise'],
  terms: 'Please make payment within 30 minutes. Provide transaction ID after payment. No third-party payments accepted.',
  timeLimit: 30,
  user: {
    id: 'user123',
    name: '<PERSON>',
    rating: 4.8,
    completedTrades: 127,
    responseTime: '5 min',
    lastSeen: '2 hours ago',
    verificationLevel: 'verified',
  },
  createdAt: '2024-01-15T10:30:00Z',
  isOnline: true,
};

export default function OfferDetailScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { offerId } = route.params || {};
  
  // In a real app, we would fetch the offer data using the offerId
  // const offer = useQuery(api.offers.getOffer, { offerId });
  const offer = mockOffer;
  
  const [tradeAmount, setTradeAmount] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [paymentMethodModalVisible, setPaymentMethodModalVisible] = useState(false);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  
  const calculateCryptoAmount = () => {
    if (!tradeAmount || !offer.price) return '0';
    return (parseFloat(tradeAmount) / offer.price).toFixed(8);
  };
  
  const validateTrade = () => {
    const amount = parseFloat(tradeAmount);
    
    if (!tradeAmount || isNaN(amount) || amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return false;
    }
    
    if (amount < offer.minAmount) {
      Alert.alert('Error', `Minimum amount is ${offer.minAmount} ${offer.fiatCurrency}`);
      return false;
    }
    
    if (amount > offer.maxAmount) {
      Alert.alert('Error', `Maximum amount is ${offer.maxAmount} ${offer.fiatCurrency}`);
      return false;
    }
    
    if (!selectedPaymentMethod) {
      Alert.alert('Error', 'Please select a payment method');
      return false;
    }
    
    return true;
  };
  
  const handleStartTrade = () => {
    if (!validateTrade()) return;
    setConfirmModalVisible(true);
  };
  
  const confirmTrade = () => {
    // In a real app, we would call a Convex mutation
    // const result = await createTradeMutation({ offerId, amount: tradeAmount, paymentMethod: selectedPaymentMethod });
    
    setConfirmModalVisible(false);
    
    Alert.alert(
      'Trade Started',
      'Your trade request has been sent successfully!',
      [
        {
          text: 'OK',
          onPress: () => {
            // Navigate to the trade screen
            navigation.navigate('Trade', { tradeId: 'new-trade-id' });
          },
        },
      ]
    );
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Offer Details</Text>
        <TouchableOpacity>
          <Ionicons name="share-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Offer Type Badge */}
        <View style={styles.offerTypeBadge}>
          <Text style={[
            styles.offerTypeText,
            offer.type === 'buy' ? styles.buyText : styles.sellText
          ]}>
            {offer.type === 'buy' ? 'BUY' : 'SELL'} {offer.cryptoCurrency}
          </Text>
        </View>
        
        {/* Price Section */}
        <View style={styles.section}>
          <View style={styles.priceContainer}>
            <Text style={styles.priceLabel}>Price</Text>
            <Text style={styles.priceValue}>
              {offer.price.toLocaleString()} {offer.fiatCurrency}
            </Text>
            <Text style={styles.priceSubtext}>per {offer.cryptoCurrency}</Text>
          </View>
        </View>
        
        {/* Amount Limits */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Amount Limits</Text>
          <View style={styles.limitsContainer}>
            <View style={styles.limitItem}>
              <Text style={styles.limitLabel}>Minimum</Text>
              <Text style={styles.limitValue}>
                {offer.minAmount} {offer.fiatCurrency}
              </Text>
            </View>
            <View style={styles.limitSeparator} />
            <View style={styles.limitItem}>
              <Text style={styles.limitLabel}>Maximum</Text>
              <Text style={styles.limitValue}>
                {offer.maxAmount} {offer.fiatCurrency}
              </Text>
            </View>
          </View>
        </View>
        
        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Accepted Payment Methods</Text>
          <View style={styles.paymentMethodsContainer}>
            {offer.paymentMethods.map((method, index) => (
              <View key={index} style={styles.paymentMethodTag}>
                <Ionicons name="card-outline" size={16} color={theme.colors.primary} />
                <Text style={styles.paymentMethodText}>{method}</Text>
              </View>
            ))}
          </View>
        </View>
        
        {/* Trader Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trader Information</Text>
          <View style={styles.traderContainer}>
            <View style={styles.traderHeader}>
              <View style={styles.traderAvatar}>
                <Text style={styles.traderAvatarText}>
                  {offer.user.name.charAt(0)}
                </Text>
              </View>
              <View style={styles.traderInfo}>
                <View style={styles.traderNameContainer}>
                  <Text style={styles.traderName}>{offer.user.name}</Text>
                  {offer.user.verificationLevel === 'verified' && (
                    <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
                  )}
                  {offer.isOnline && (
                    <View style={styles.onlineIndicator} />
                  )}
                </View>
                <View style={styles.traderStats}>
                  <View style={styles.statItem}>
                    <Ionicons name="star" size={14} color={theme.colors.warning} />
                    <Text style={styles.statText}>{offer.user.rating}</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Ionicons name="swap-horizontal" size={14} color={theme.colors.textSecondary} />
                    <Text style={styles.statText}>{offer.user.completedTrades} trades</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Ionicons name="time" size={14} color={theme.colors.textSecondary} />
                    <Text style={styles.statText}>~{offer.user.responseTime}</Text>
                  </View>
                </View>
              </View>
            </View>
            <Text style={styles.lastSeen}>Last seen: {offer.user.lastSeen}</Text>
          </View>
        </View>
        
        {/* Trading Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trading Terms</Text>
          <Text style={styles.termsText}>{offer.terms}</Text>
          <View style={styles.timeLimitContainer}>
            <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={styles.timeLimitText}>
              Payment time limit: {offer.timeLimit} minutes
            </Text>
          </View>
        </View>
        
        {/* Trade Form */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Start Trade</Text>
          
          <View style={styles.tradeForm}>
            <Text style={styles.inputLabel}>
              Amount ({offer.fiatCurrency})
            </Text>
            <TextInput
              style={styles.amountInput}
              placeholder={`${offer.minAmount} - ${offer.maxAmount}`}
              value={tradeAmount}
              onChangeText={setTradeAmount}
              keyboardType="numeric"
            />
            
            {tradeAmount && (
              <View style={styles.conversionContainer}>
                <Text style={styles.conversionText}>
                  You will receive: {calculateCryptoAmount()} {offer.cryptoCurrency}
                </Text>
              </View>
            )}
            
            <Text style={styles.inputLabel}>Payment Method</Text>
            <TouchableOpacity
              style={styles.paymentMethodSelector}
              onPress={() => setPaymentMethodModalVisible(true)}
            >
              <Text style={[
                styles.paymentMethodSelectorText,
                !selectedPaymentMethod && styles.placeholderText
              ]}>
                {selectedPaymentMethod || 'Select payment method'}
              </Text>
              <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.startTradeButton,
                (!tradeAmount || !selectedPaymentMethod) && styles.disabledButton
              ]}
              onPress={handleStartTrade}
              disabled={!tradeAmount || !selectedPaymentMethod}
            >
              <Text style={styles.startTradeButtonText}>
                {offer.type === 'buy' ? 'Sell' : 'Buy'} {offer.cryptoCurrency}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.offerMeta}>
          <Text style={styles.offerMetaText}>
            Offer created: {formatDate(offer.createdAt)}
          </Text>
        </View>
      </ScrollView>

      {/* Payment Method Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={paymentMethodModalVisible}
        onRequestClose={() => setPaymentMethodModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Payment Method</Text>
              <TouchableOpacity onPress={() => setPaymentMethodModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {offer.paymentMethods.map((method, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.modalOption,
                    selectedPaymentMethod === method && styles.modalOptionSelected
                  ]}
                  onPress={() => {
                    setSelectedPaymentMethod(method);
                    setPaymentMethodModalVisible(false);
                  }}
                >
                  <Text style={[
                    styles.modalOptionText,
                    selectedPaymentMethod === method && styles.modalOptionTextSelected
                  ]}>
                    {method}
                  </Text>
                  {selectedPaymentMethod === method && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Confirmation Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={confirmModalVisible}
        onRequestClose={() => setConfirmModalVisible(false)}
      >
        <View style={styles.confirmModalOverlay}>
          <View style={styles.confirmModalContent}>
            <View style={styles.confirmModalHeader}>
              <Ionicons name="information-circle" size={48} color={theme.colors.primary} />
              <Text style={styles.confirmModalTitle}>Confirm Trade</Text>
            </View>

            <View style={styles.confirmModalBody}>
              <View style={styles.confirmRow}>
                <Text style={styles.confirmLabel}>You will {offer.type}:</Text>
                <Text style={styles.confirmValue}>
                  {calculateCryptoAmount()} {offer.cryptoCurrency}
                </Text>
              </View>

              <View style={styles.confirmRow}>
                <Text style={styles.confirmLabel}>For:</Text>
                <Text style={styles.confirmValue}>
                  {tradeAmount} {offer.fiatCurrency}
                </Text>
              </View>

              <View style={styles.confirmRow}>
                <Text style={styles.confirmLabel}>Payment method:</Text>
                <Text style={styles.confirmValue}>{selectedPaymentMethod}</Text>
              </View>

              <View style={styles.confirmRow}>
                <Text style={styles.confirmLabel}>Trading with:</Text>
                <Text style={styles.confirmValue}>{offer.user.name}</Text>
              </View>
            </View>

            <View style={styles.confirmModalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setConfirmModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.confirmButton}
                onPress={confirmTrade}
              >
                <Text style={styles.confirmButtonText}>Confirm Trade</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  offerTypeBadge: {
    alignSelf: 'center',
    backgroundColor: theme.colors.primaryLight + '40',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 16,
    borderWidth: 1,
    borderColor: theme.colors.primaryLight,
  },
  offerTypeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  buyText: {
    color: theme.colors.success,
  },
  sellText: {
    color: theme.colors.error,
  },
  section: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  priceContainer: {
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.text,
  },
  priceSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  limitsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  limitItem: {
    flex: 1,
    alignItems: 'center',
  },
  limitSeparator: {
    width: 1,
    height: 40,
    backgroundColor: theme.colors.border,
    marginHorizontal: 16,
  },
  limitLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  limitValue: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  paymentMethodsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  paymentMethodTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primaryLight + '40',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.primaryLight,
    gap: 6,
  },
  paymentMethodText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  traderContainer: {
    gap: 12,
  },
  traderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  traderAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  traderAvatarText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  traderInfo: {
    flex: 1,
  },
  traderNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 4,
  },
  traderName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  onlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.success,
  },
  traderStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  lastSeen: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  termsText: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: 12,
  },
  timeLimitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: theme.colors.warning + '40',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  timeLimitText: {
    fontSize: 12,
    color: theme.colors.warning,
    fontWeight: '500',
  },
  tradeForm: {
    gap: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  amountInput: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    fontSize: 16,
    color: theme.colors.text,
  },
  conversionContainer: {
    backgroundColor: theme.colors.info + '20',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.info + '60',
  },
  conversionText: {
    fontSize: 14,
    color: theme.colors.info,
    fontWeight: '500',
    textAlign: 'center',
  },
  paymentMethodSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  paymentMethodSelectorText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  placeholderText: {
    color: theme.colors.textSecondary,
  },
  startTradeButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 8,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  disabledButton: {
    backgroundColor: theme.colors.border,
    shadowOpacity: 0,
    elevation: 0,
  },
  startTradeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  offerMeta: {
    alignItems: 'center',
    paddingVertical: 16,
    marginTop: 8,
  },
  offerMetaText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalScrollView: {
    maxHeight: 300,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalOptionSelected: {
    backgroundColor: theme.colors.primaryLight + '40',
  },
  modalOptionText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  modalOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  confirmModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  confirmModalContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  confirmModalHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  confirmModalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 12,
  },
  confirmModalBody: {
    gap: 12,
    marginBottom: 24,
  },
  confirmRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  confirmLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  confirmValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  confirmModalFooter: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: theme.colors.backgroundSecondary,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
  },
  confirmButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});
