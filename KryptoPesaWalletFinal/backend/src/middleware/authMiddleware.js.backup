const User = require('../models/User');
const authService = require('../services/authService');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');
const { authenticateWallet } = require('./walletAuth');

/**
 * Enhanced authentication middleware with session management
 * Supports both Bearer token and wallet authentication
 */
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const walletAddress = req.headers['x-wallet-address'];

    // Try Bearer token authentication first
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);

      try {
        // Verify token using enhanced auth service
        const decoded = await authService.verifyAccessToken(token);

        // Get user from database
        const user = await User.findById(decoded.userId);
        if (!user) {
          throw new AppError('User not found', 401, 'USER_NOT_FOUND');
        }

        // Check account status
        if (user.status !== 'active') {
          throw new AppError('Account is not active', 403, 'ACCOUNT_INACTIVE');
        }

        // Check if account is locked
        if (user.isLocked) {
          throw new AppError('Account is temporarily locked', 423, 'ACCOUNT_LOCKED');
        }

        // Update last activity
        user.lastActive = new Date();
        await user.save();

        // Attach user and session info to request
        req.user = user;
        req.sessionId = decoded.sessionId;
        req.tokenPayload = decoded;

        return next();
      } catch (tokenError) {
        logger.debug('Bearer token authentication failed, trying wallet auth:', tokenError.message);
        // Fall through to wallet authentication
      }
    }

    // Try wallet authentication if Bearer token failed or not provided
    if (walletAddress) {
      try {
        await authenticateWallet(req, res, () => {
          // Wallet authentication successful
          logger.debug('Wallet authentication successful for trading endpoint');
          next();
        });
        return; // authenticateWallet will call next() or send error response
      } catch (walletError) {
        logger.debug('Wallet authentication failed:', walletError.message);
        // Fall through to error response
      }
    }

    // No valid authentication method found
    return res.status(401).json({
      success: false,
      message: 'Access token is required',
      code: 'MISSING_TOKEN'
    });

  } catch (error) {
    logger.error('Authentication failed:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        code: error.code || 'AUTH_ERROR'
      });
    }

    return res.status(401).json({
      success: false,
      message: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Optional authentication middleware (doesn't fail if no token)
 */
const optionalAuthenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    const decoded = await authService.verifyAccessToken(token);
    
    const user = await User.findById(decoded.userId);
    if (user && user.status === 'active' && !user.isLocked) {
      req.user = user;
      req.sessionId = decoded.sessionId;
      req.tokenPayload = decoded;
      
      // Update last activity
      user.lastActive = new Date();
      await user.save();
    }
    
    next();
  } catch (error) {
    // Log error but don't fail the request
    logger.warn('Optional authentication failed:', error);
    next();
  }
};

/**
 * Role-based authorization middleware
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!roles.includes(req.user.role)) {
      logger.warn(`Unauthorized access attempt by user ${req.user._id} with role ${req.user.role}`, {
        userId: req.user._id,
        userRole: req.user.role,
        requiredRoles: roles,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        message: 'Insufficient privileges',
        code: 'INSUFFICIENT_PRIVILEGES'
      });
    }

    next();
  };
};

/**
 * 2FA verification middleware
 */
const require2FA = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Check if 2FA is enabled for user
    if (!req.user.security.twoFactorEnabled) {
      return res.status(403).json({
        success: false,
        message: 'Two-factor authentication is required for this operation',
        code: '2FA_REQUIRED'
      });
    }

    // Check for 2FA token in headers
    const twoFactorToken = req.headers['x-2fa-token'];
    if (!twoFactorToken) {
      return res.status(403).json({
        success: false,
        message: 'Two-factor authentication token required',
        code: '2FA_TOKEN_REQUIRED'
      });
    }

    // Verify 2FA token
    const isValid = authService.verify2FAToken(req.user.security.twoFactorSecret, twoFactorToken);
    if (!isValid) {
      logger.warn(`Invalid 2FA attempt by user ${req.user._id}`, {
        userId: req.user._id,
        endpoint: req.originalUrl,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        message: 'Invalid two-factor authentication token',
        code: 'INVALID_2FA_TOKEN'
      });
    }

    next();
  } catch (error) {
    logger.error('2FA verification failed:', error);
    return res.status(500).json({
      success: false,
      message: 'Two-factor authentication verification failed',
      code: '2FA_ERROR'
    });
  }
};

/**
 * Resource ownership verification middleware
 */
const verifyOwnership = (resourceIdParam = 'id', resourceModel = null) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        return res.status(400).json({
          success: false,
          message: 'Resource ID is required',
          code: 'MISSING_RESOURCE_ID'
        });
      }

      // Admin users can access any resource
      if (req.user.role === 'admin') {
        return next();
      }

      // If resource model is provided, check ownership
      if (resourceModel) {
        const resource = await resourceModel.findById(resourceId);
        if (!resource) {
          return res.status(404).json({
            success: false,
            message: 'Resource not found',
            code: 'RESOURCE_NOT_FOUND'
          });
        }

        // Check if user owns the resource
        const ownerId = resource.userId || resource.user || resource.owner;
        if (ownerId && ownerId.toString() !== req.user._id.toString()) {
          logger.warn(`Unauthorized resource access attempt by user ${req.user._id}`, {
            userId: req.user._id,
            resourceId,
            resourceType: resourceModel.modelName,
            endpoint: req.originalUrl,
            ip: req.ip
          });
          
          return res.status(403).json({
            success: false,
            message: 'Access denied to this resource',
            code: 'RESOURCE_ACCESS_DENIED'
          });
        }

        req.resource = resource;
      }

      next();
    } catch (error) {
      logger.error('Ownership verification failed:', error);
      return res.status(500).json({
        success: false,
        message: 'Resource verification failed',
        code: 'VERIFICATION_ERROR'
      });
    }
  };
};

/**
 * Session validation middleware
 */
const validateSession = async (req, res, next) => {
  try {
    if (!req.user || !req.sessionId) {
      return next();
    }

    const redisClient = getRedisClient();
    if (!redisClient || !redisClient.isReady) {
      return next();
    }

    const sessionKey = `session:${req.user._id}:${req.sessionId}`;
    const sessionData = await redisClient.get(sessionKey);
    
    if (!sessionData) {
      return res.status(401).json({
        success: false,
        message: 'Session expired or invalid',
        code: 'SESSION_INVALID'
      });
    }

    // Update session activity
    const session = JSON.parse(sessionData);
    session.lastActivity = new Date().toISOString();
    await redisClient.setEx(sessionKey, 7 * 24 * 60 * 60, JSON.stringify(session));

    req.session = session;
    next();
  } catch (error) {
    logger.error('Session validation failed:', error);
    next(); // Continue without session validation in case of Redis issues
  }
};

/**
 * Rate limiting by user ID
 */
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return next();
      }

      const redisClient = getRedisClient();
      if (!redisClient || !redisClient.isReady) {
        return next();
      }

      const key = `user_rate_limit:${req.user._id}`;
      const current = await redisClient.get(key);
      
      if (current && parseInt(current) >= maxRequests) {
        return res.status(429).json({
          success: false,
          message: 'Rate limit exceeded for user',
          code: 'USER_RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil(windowMs / 1000)
        });
      }

      // Increment counter
      const multi = redisClient.multi();
      multi.incr(key);
      multi.expire(key, Math.ceil(windowMs / 1000));
      await multi.exec();

      next();
    } catch (error) {
      logger.error('User rate limiting failed:', error);
      next(); // Continue without rate limiting in case of Redis issues
    }
  };
};

module.exports = {
  authenticate,
  optionalAuthenticate,
  authorize,
  require2FA,
  verifyOwnership,
  validateSession,
  userRateLimit
};
