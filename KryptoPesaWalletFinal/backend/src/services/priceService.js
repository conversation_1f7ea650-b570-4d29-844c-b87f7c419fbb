const axios = require('axios');
const logger = require('../utils/logger');
const { circuitBreakers } = require('../utils/circuitBreaker');

class PriceService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 60000; // 1 minute cache
    this.apiKeys = {
      coingecko: process.env.COINGECKO_API_KEY,
      coinmarketcap: process.env.COINMARKETCAP_API_KEY
    };

    // Performance optimization integration
    this.performanceService = null;
    this.initializePerformanceService();
    
    this.supportedCurrencies = ['USD', 'KES', 'TZS', 'UGX', 'RWF'];
    this.supportedCryptos = ['bitcoin', 'ethereum', 'matic-network', 'tether', 'usd-coin', 'dai'];
    
    this.cryptoMapping = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum', 
      'MATIC': 'matic-network',
      'USDT': 'tether',
      'USDC': 'usd-coin',
      'DAI': 'dai'
    };

    this.fiatMapping = {
      'USD': 'usd',
      'KES': 'kes',
      'TZS': 'tzs', 
      'UGX': 'ugx',
      'RWF': 'rwf'
    };

    this.maxRetries = 3;
    this.baseDelay = 1000;
  }

  /**
   * Initialize performance service integration
   */
  async initializePerformanceService() {
    try {
      // Lazy load to avoid circular dependencies
      const { serviceLayerPerformance } = require('./serviceLayerPerformance');
      this.performanceService = serviceLayerPerformance;
      logger.info('Price service performance optimization initialized');
    } catch (error) {
      logger.warn('Performance service not available for price service:', error.message);
    }
  }

  /**
   * Execute price operation with retry logic and circuit breaker
   */
  async executeWithRetry(operation, maxRetries = this.maxRetries) {
    return await circuitBreakers.external.execute(async () => {
      let lastError;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await operation();
        } catch (error) {
          lastError = error;

          if (this.isNonRetryableError(error)) {
            throw error;
          }

          if (attempt < maxRetries) {
            const delay = this.calculateBackoffDelay(attempt);
            logger.warn(`Price API operation failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, {
              error: error.message,
              attempt,
              maxRetries
            });
            await this.sleep(delay);
          }
        }
      }

      throw lastError;
    });
  }

  isNonRetryableError(error) {
    const nonRetryableStatuses = [400, 401, 403, 404];
    return error.response && nonRetryableStatuses.includes(error.response.status);
  }

  calculateBackoffDelay(attempt) {
    return this.baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get cache key for price data
   */
  getCacheKey(cryptos, currencies) {
    const cryptoStr = Array.isArray(cryptos) ? cryptos.sort().join(',') : cryptos;
    const currencyStr = Array.isArray(currencies) ? currencies.sort().join(',') : currencies;
    return `${cryptoStr}-${currencyStr}`;
  }

  /**
   * Check if cached data is still valid
   */
  isCacheValid(cacheEntry) {
    return cacheEntry && (Date.now() - cacheEntry.timestamp) < this.cacheTimeout;
  }

  /**
   * Get prices from CoinGecko API
   */
  async getPricesFromCoinGecko(cryptoIds, currencies) {
    return await this.executeWithRetry(async () => {
      const cryptoStr = Array.isArray(cryptoIds) ? cryptoIds.join(',') : cryptoIds;
      const currencyStr = Array.isArray(currencies) ? currencies.join(',') : currencies;
      
      const url = 'https://api.coingecko.com/api/v3/simple/price';
      const params = {
        ids: cryptoStr,
        vs_currencies: currencyStr,
        include_24hr_change: true,
        include_market_cap: true,
        include_24hr_vol: true
      };

      if (this.apiKeys.coingecko) {
        params.x_cg_demo_api_key = this.apiKeys.coingecko;
      }

      const response = await axios.get(url, { params, timeout: 10000 });
      return response.data;
    });
  }

  /**
   * Get prices from CoinMarketCap API (fallback)
   */
  async getPricesFromCoinMarketCap(symbols, currencies) {
    return await this.executeWithRetry(async () => {
      const symbolStr = Array.isArray(symbols) ? symbols.join(',') : symbols;
      
      const url = 'https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest';
      const headers = {
        'X-CMC_PRO_API_KEY': this.apiKeys.coinmarketcap,
        'Accept': 'application/json'
      };

      const response = await axios.get(url, {
        headers,
        params: { symbol: symbolStr },
        timeout: 10000
      });

      // Transform CMC response to match CoinGecko format
      const transformed = {};
      Object.values(response.data.data).forEach(crypto => {
        const symbol = crypto.symbol.toLowerCase();
        transformed[symbol] = {};
        
        currencies.forEach(currency => {
          const quote = crypto.quote[currency.toUpperCase()];
          if (quote) {
            transformed[symbol][currency.toLowerCase()] = quote.price;
            transformed[symbol][`${currency.toLowerCase()}_24h_change`] = quote.percent_change_24h;
            transformed[symbol][`${currency.toLowerCase()}_market_cap`] = quote.market_cap;
            transformed[symbol][`${currency.toLowerCase()}_24h_vol`] = quote.volume_24h;
          }
        });
      });

      return transformed;
    });
  }

  /**
   * Get current prices for cryptocurrencies with performance optimization
   */
  async getPrices(cryptos, currencies = ['USD']) {
    try {
      // Normalize inputs
      const cryptoArray = Array.isArray(cryptos) ? cryptos : [cryptos];
      const currencyArray = Array.isArray(currencies) ? currencies : [currencies];

      // Use performance service for optimized caching if available
      if (this.performanceService) {
        return await this.performanceService.getOptimizedPrices(cryptoArray, currencyArray);
      }

      // Fallback to local cache
      const cacheKey = this.getCacheKey(cryptoArray, currencyArray);
      const cached = this.cache.get(cacheKey);

      if (this.isCacheValid(cached)) {
        logger.debug('Returning cached price data');
        return cached.data;
      }

      // Map crypto symbols to CoinGecko IDs
      const cryptoIds = cryptoArray.map(crypto => 
        this.cryptoMapping[crypto.toUpperCase()] || crypto.toLowerCase()
      );

      // Map fiat currencies
      const mappedCurrencies = currencyArray.map(currency => 
        this.fiatMapping[currency.toUpperCase()] || currency.toLowerCase()
      );

      let priceData;

      try {
        // Try CoinGecko first
        priceData = await this.getPricesFromCoinGecko(cryptoIds, mappedCurrencies);
        logger.debug('Successfully fetched prices from CoinGecko');
      } catch (error) {
        logger.warn('CoinGecko API failed, trying CoinMarketCap fallback:', error.message);
        
        if (this.apiKeys.coinmarketcap) {
          try {
            priceData = await this.getPricesFromCoinMarketCap(cryptoArray, currencyArray);
            logger.debug('Successfully fetched prices from CoinMarketCap');
          } catch (cmcError) {
            logger.error('Both price APIs failed:', cmcError.message);
            throw new Error('Unable to fetch price data from any provider');
          }
        } else {
          throw error;
        }
      }

      // Cache the result
      this.cache.set(cacheKey, {
        data: priceData,
        timestamp: Date.now()
      });

      // Clean old cache entries
      this.cleanCache();

      return priceData;
    } catch (error) {
      logger.error(`Error fetching prices: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get price for a single cryptocurrency
   */
  async getPrice(crypto, currency = 'USD') {
    const prices = await this.getPrices([crypto], [currency]);
    const cryptoId = this.cryptoMapping[crypto.toUpperCase()] || crypto.toLowerCase();
    const currencyKey = this.fiatMapping[currency.toUpperCase()] || currency.toLowerCase();
    
    return prices[cryptoId]?.[currencyKey] || null;
  }

  /**
   * Convert amount from one currency to another
   */
  async convertCurrency(amount, fromCrypto, toCurrency = 'USD') {
    try {
      const price = await this.getPrice(fromCrypto, toCurrency);
      if (!price) {
        throw new Error(`Price not available for ${fromCrypto} to ${toCurrency}`);
      }
      
      return {
        amount: parseFloat(amount),
        fromCrypto,
        toCurrency,
        price,
        convertedAmount: parseFloat(amount) * price,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error(`Error converting currency: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get portfolio value in specified currency
   */
  async getPortfolioValue(balances, currency = 'USD') {
    try {
      const cryptos = balances.map(balance => balance.symbol);
      const prices = await this.getPrices(cryptos, [currency]);
      
      let totalValue = 0;
      const breakdown = [];

      for (const balance of balances) {
        const cryptoId = this.cryptoMapping[balance.symbol.toUpperCase()] || balance.symbol.toLowerCase();
        const currencyKey = this.fiatMapping[currency.toUpperCase()] || currency.toLowerCase();
        const price = prices[cryptoId]?.[currencyKey] || 0;
        
        const balanceAmount = parseFloat(balance.balance) / Math.pow(10, balance.decimals);
        const value = balanceAmount * price;
        
        totalValue += value;
        breakdown.push({
          symbol: balance.symbol,
          balance: balanceAmount,
          price,
          value,
          percentage: 0 // Will be calculated after total
        });
      }

      // Calculate percentages
      breakdown.forEach(item => {
        item.percentage = totalValue > 0 ? (item.value / totalValue) * 100 : 0;
      });

      return {
        totalValue,
        currency,
        breakdown,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error(`Error calculating portfolio value: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get historical prices (simplified - would need more complex implementation)
   */
  async getHistoricalPrices(crypto, currency = 'USD', days = 7) {
    return await this.executeWithRetry(async () => {
      const cryptoId = this.cryptoMapping[crypto.toUpperCase()] || crypto.toLowerCase();
      const currencyKey = this.fiatMapping[currency.toUpperCase()] || currency.toLowerCase();
      
      const url = `https://api.coingecko.com/api/v3/coins/${cryptoId}/market_chart`;
      const params = {
        vs_currency: currencyKey,
        days: days,
        interval: days <= 1 ? 'hourly' : 'daily'
      };

      if (this.apiKeys.coingecko) {
        params.x_cg_demo_api_key = this.apiKeys.coingecko;
      }

      const response = await axios.get(url, { params, timeout: 10000 });
      
      return {
        prices: response.data.prices.map(([timestamp, price]) => ({
          timestamp: new Date(timestamp).toISOString(),
          price
        })),
        market_caps: response.data.market_caps,
        total_volumes: response.data.total_volumes
      };
    });
  }

  /**
   * Clean expired cache entries
   */
  cleanCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Clear all cached data
   */
  clearCache() {
    this.cache.clear();
    logger.info('Price cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      timeout: this.cacheTimeout,
      entries: Array.from(this.cache.keys())
    };
  }
}

module.exports = new PriceService();
