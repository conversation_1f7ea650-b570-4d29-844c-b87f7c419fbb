/**
 * Comprehensive Sensitive Data Exposure Prevention Tests
 * Tests all aspects of sensitive data filtering across the application
 */

const mongoose = require('mongoose');
const User = require('../../models/User');
const Wallet = require('../../models/Wallet');
const Trade = require('../../models/Trade');
const {
  filterSensitiveData,
  filterForLogging,
  filterForAPI,
  filterErrorForLogging,
  isSensitiveField,
  maskSensitiveValue
} = require('../../utils/sensitiveDataFilter');

describe('Sensitive Data Exposure Prevention Tests', () => {
  let testUser, testWallet, testTrade;

  beforeEach(async () => {
    // Create test user with sensitive data
    testUser = await global.createTestUser({
      email: '<EMAIL>',
      password: 'SuperSecretPassword123!',
      security: {
        twoFactorSecret: 'JBSWY3DPEHPK3PXP',
        passwordResetToken: 'reset-token-12345',
        passwordResetExpires: new Date(Date.now() + 3600000)
      },
      verification: {
        email: {
          token: 'email-verification-token',
          verified: false
        },
        phone: {
          token: 'phone-verification-token',
          verified: false
        },
        identity: {
          documentNumber: 'ID123456789',
          verified: false
        }
      }
    });

    // Create test wallet with sensitive data
    testWallet = await Wallet.create({
      user: testUser._id,
      addresses: {
        ethereum: {
          address: '******************************************',
          publicKey: 'public-key-data-should-be-hidden',
          derivationPath: "m/44'/60'/0'/0/0"
        }
      },
      security: {
        encryptedMnemonic: 'encrypted-mnemonic-data',
        mnemonicHash: 'mnemonic-hash-data',
        backupCompleted: true
      }
    });

    // Create test trade with sensitive payment data
    testTrade = await Trade.create({
      tradeId: 'TRADE-001',
      blockchainTradeId: 1,
      seller: testUser._id,
      buyer: testUser._id,
      offer: new mongoose.Types.ObjectId(),
      cryptocurrency: {
        symbol: 'USDT',
        contractAddress: '******************************************',
        network: 'ethereum',
        amount: '100.00',
        decimals: 6
      },
      fiat: {
        currency: 'USD',
        amount: 100,
        exchangeRate: 1
      },
      payment: {
        method: 'bank_transfer',
        details: {
          bankName: 'Test Bank',
          accountNumber: '****************',
          accountName: 'Test Account',
          mobileNumber: '+************'
        }
      },
      status: 'created',
      chat: new mongoose.Types.ObjectId(),
      commission: {
        rate: 0.005,
        amount: '0.5',
        currency: 'USDT'
      },
      escrow: {
        contractAddress: '******************************************'
      },
      metadata: {
        ipAddress: '***********00',
        userAgent: 'Mozilla/5.0 Test Browser'
      }
    });
  });

  describe('Model Data Filtering', () => {
    test('User model should filter sensitive data in toJSON', async () => {
      const user = await User.findById(testUser._id);
      const userJSON = user.toJSON();

      // Should not expose sensitive fields
      expect(userJSON.password).toBeUndefined();
      expect(userJSON.security?.twoFactorSecret).toBeUndefined();
      expect(userJSON.security?.passwordResetToken).toBeUndefined();
      expect(userJSON.security?.passwordResetExpires).toBeUndefined();
      expect(userJSON.verification?.email?.token).toBeUndefined();
      expect(userJSON.verification?.phone?.token).toBeUndefined();
      expect(userJSON.verification?.identity?.documentNumber).toBeUndefined();
      expect(userJSON.__v).toBeUndefined();
    });

    test('User model should filter sensitive data in toObject', async () => {
      const user = await User.findById(testUser._id);
      const userObject = user.toObject();

      // Should not expose sensitive fields
      expect(userObject.password).toBeUndefined();
      expect(userObject.security?.twoFactorSecret).toBeUndefined();
      expect(userObject.security?.passwordResetToken).toBeUndefined();
      expect(userObject.security?.passwordResetExpires).toBeUndefined();
      expect(userObject.verification?.email?.token).toBeUndefined();
      expect(userObject.verification?.phone?.token).toBeUndefined();
      expect(userObject.verification?.identity?.documentNumber).toBeUndefined();
      expect(userObject.__v).toBeUndefined();
    });

    test('Wallet model should filter sensitive data', async () => {
      const wallet = await Wallet.findById(testWallet._id);
      const walletJSON = wallet.toJSON();

      // Should not expose sensitive wallet data
      expect(walletJSON.security?.encryptedMnemonic).toBeUndefined();
      expect(walletJSON.security?.mnemonicHash).toBeUndefined();
      expect(walletJSON.addresses?.ethereum?.publicKey).toBeUndefined();
      expect(walletJSON.__v).toBeUndefined();
    });

    test('Trade model should mask sensitive payment data', async () => {
      const trade = await Trade.findById(testTrade._id);
      const tradeJSON = trade.toJSON();

      // Should mask sensitive payment details
      expect(tradeJSON.payment.details.accountNumber).toMatch(/\*+\d{4}$/);
      expect(tradeJSON.payment.details.mobileNumber).toMatch(/\*+\d{4}$/);
      expect(tradeJSON.metadata?.ipAddress).toBeUndefined();
      expect(tradeJSON.__v).toBeUndefined();
    });
  });

  describe('Sensitive Data Filter Utility', () => {
    test('should identify sensitive fields correctly', () => {
      expect(isSensitiveField('password')).toBe(true);
      expect(isSensitiveField('twoFactorSecret')).toBe(true);
      expect(isSensitiveField('privateKey')).toBe(true);
      expect(isSensitiveField('mnemonic')).toBe(true);
      expect(isSensitiveField('token')).toBe(true);
      expect(isSensitiveField('accountNumber')).toBe(true);
      expect(isSensitiveField('ipAddress')).toBe(true);
      
      expect(isSensitiveField('username')).toBe(false);
      expect(isSensitiveField('email')).toBe(false);
      expect(isSensitiveField('status')).toBe(false);
    });

    test('should mask sensitive values correctly', () => {
      expect(maskSensitiveValue('password123')).toBe('*******d123');
      expect(maskSensitiveValue('short')).toBe('*hort');
      expect(maskSensitiveValue('****************')).toBe('************3456');
      expect(maskSensitiveValue('')).toBe('');
      expect(maskSensitiveValue(null)).toBe(null);
    });

    test('should filter sensitive data from objects', () => {
      const testData = {
        username: 'testuser',
        password: 'secret123',
        email: '<EMAIL>',
        security: {
          twoFactorSecret: 'JBSWY3DPEHPK3PXP',
          lastLogin: new Date()
        },
        nested: {
          privateKey: 'private-key-data',
          publicData: 'safe-data'
        }
      };

      const filtered = filterSensitiveData(testData);

      expect(filtered.username).toBe('testuser');
      expect(filtered.email).toBe('<EMAIL>');
      expect(filtered.password).toBe('*****t123');
      expect(filtered.security.twoFactorSecret).toBe('[REDACTED]');
      expect(filtered.security.lastLogin).toEqual(testData.security.lastLogin);
      expect(filtered.nested.privateKey).toBe('[REDACTED]');
      expect(filtered.nested.publicData).toBe('safe-data');
    });

    test('should filter data for logging', () => {
      const logData = {
        message: 'User login attempt',
        user: {
          email: '<EMAIL>',
          password: 'secret123'
        },
        request: {
          ip: '***********',
          userAgent: 'Mozilla/5.0'
        }
      };

      const filtered = filterForLogging(logData);

      expect(filtered.message).toBe('User login attempt');
      expect(filtered.user.email).toBe('<EMAIL>');
      expect(filtered.user.password).toBe('[REDACTED]');
      expect(filtered.request.userAgent).toBe('[REDACTED]');
    });

    test('should filter data for API responses', () => {
      const apiData = {
        success: true,
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            password: 'secret123',
            token: 'jwt-token-data'
          }
        }
      };

      const filtered = filterForAPI(apiData);

      expect(filtered.success).toBe(true);
      expect(filtered.data.user.id).toBe('123');
      expect(filtered.data.user.email).toBe('<EMAIL>');
      expect(filtered.data.user.password).toBe('*****t123');
      expect(filtered.data.user.token).toBe('**********data');
    });

    test('should filter error objects for logging', () => {
      const error = new Error('Database connection failed');
      error.code = 'DB_ERROR';
      error.statusCode = 500;
      error.sensitiveData = '******************************************';
      error.stack = 'Error: Database connection failed\n    at test.js:1:1';

      const filtered = filterErrorForLogging(error);

      expect(filtered.name).toBe('Error');
      expect(filtered.message).toBe('Database connection failed');
      expect(filtered.code).toBe('DB_ERROR');
      expect(filtered.statusCode).toBe(500);
      expect(filtered.sensitiveData).toBe('[REDACTED]');
      expect(filtered.timestamp).toBeDefined();
    });
  });

  describe('Array and Nested Object Filtering', () => {
    test('should filter arrays of objects', () => {
      const arrayData = [
        { id: 1, password: 'secret1', name: 'User 1' },
        { id: 2, password: 'secret2', name: 'User 2' }
      ];

      const filtered = filterSensitiveData(arrayData);

      expect(Array.isArray(filtered)).toBe(true);
      expect(filtered[0].id).toBe(1);
      expect(filtered[0].name).toBe('User 1');
      expect(filtered[0].password).toBe('***ret1');
      expect(filtered[1].password).toBe('***ret2');
    });

    test('should handle deeply nested objects', () => {
      const deepData = {
        level1: {
          level2: {
            level3: {
              password: 'deep-secret',
              safeData: 'safe'
            }
          }
        }
      };

      const filtered = filterSensitiveData(deepData);

      expect(filtered.level1.level2.level3.safeData).toBe('safe');
      expect(filtered.level1.level2.level3.password).toBe('*******cret');
    });
  });
});
