const mongoose = require('mongoose');

const auditLogSchema = new mongoose.Schema({
  // User who performed the action
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false // Some actions might be system-generated
  },

  // Action performed
  action: {
    type: String,
    required: true,
    enum: [
      // Authentication actions
      'user_login',
      'user_logout',
      'user_register',
      'password_change',
      'password_reset',
      'email_change',
      'email_verify',
      
      // Wallet actions
      'wallet_create',
      'wallet_import',
      'wallet_backup',
      'wallet_restore',
      
      // Trading actions
      'offer_create',
      'offer_update',
      'offer_delete',
      'offer_pause',
      'offer_activate',
      'trade_create',
      'trade_fund',
      'trade_payment_sent',
      'trade_payment_received',
      'trade_complete',
      'trade_cancel',
      
      // Dispute actions
      'dispute_create',
      'dispute_update',
      'dispute_resolve',
      'dispute_escalate',
      
      // KYC actions
      'kyc_submit',
      'kyc_update',
      'kyc_approve',
      'kyc_reject',
      
      // Admin actions
      'admin_login',
      'admin_user_suspend',
      'admin_user_activate',
      'admin_trade_intervene',
      'admin_dispute_resolve',
      'admin_system_config',
      
      // System actions
      'system_backup',
      'system_maintenance',
      'system_alert',
      'system_error'
    ]
  },

  // Resource type being acted upon
  resource: {
    type: String,
    required: true,
    enum: [
      'user',
      'wallet',
      'offer',
      'trade',
      'dispute',
      'kyc',
      'admin',
      'system'
    ]
  },

  // ID of the specific resource
  resourceId: {
    type: String,
    required: false
  },

  // HTTP method used
  method: {
    type: String,
    enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    required: false
  },

  // API endpoint
  endpoint: {
    type: String,
    required: false
  },

  // HTTP status code
  statusCode: {
    type: Number,
    required: false
  },

  // Request duration in milliseconds
  duration: {
    type: Number,
    required: false
  },

  // Client IP address
  ip: {
    type: String,
    required: false
  },

  // User agent string
  userAgent: {
    type: String,
    required: false
  },

  // Request data (sanitized)
  requestData: {
    params: mongoose.Schema.Types.Mixed,
    query: mongoose.Schema.Types.Mixed,
    body: mongoose.Schema.Types.Mixed
  },

  // Response data (sanitized)
  responseData: mongoose.Schema.Types.Mixed,

  // Additional metadata
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },

  // Severity level
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },

  // Success/failure indicator
  success: {
    type: Boolean,
    default: true
  },

  // Error details if action failed
  error: {
    message: String,
    stack: String,
    code: String
  },

  // Timestamp
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  }
}, {
  timestamps: true,
  collection: 'audit_logs'
});

// Indexes for efficient querying
auditLogSchema.index({ userId: 1, timestamp: -1 });
auditLogSchema.index({ action: 1, timestamp: -1 });
auditLogSchema.index({ resource: 1, timestamp: -1 });
auditLogSchema.index({ resourceId: 1, timestamp: -1 });
auditLogSchema.index({ timestamp: -1 });
auditLogSchema.index({ severity: 1, timestamp: -1 });
auditLogSchema.index({ success: 1, timestamp: -1 });

// Compound indexes for common queries
auditLogSchema.index({ userId: 1, action: 1, timestamp: -1 });
auditLogSchema.index({ resource: 1, resourceId: 1, timestamp: -1 });

// TTL index to automatically delete old logs (optional - keep for 1 year)
auditLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });

// Instance methods
auditLogSchema.methods.toSafeObject = function() {
  const obj = this.toObject();
  
  // Remove sensitive data from the safe object
  if (obj.requestData && obj.requestData.body) {
    const sensitiveFields = ['password', 'mnemonic', 'privateKey', 'token'];
    sensitiveFields.forEach(field => {
      if (obj.requestData.body[field]) {
        obj.requestData.body[field] = '[REDACTED]';
      }
    });
  }

  return obj;
};

// Static methods
auditLogSchema.statics.findByUser = function(userId, options = {}) {
  const { limit = 50, skip = 0, action, resource } = options;
  
  const query = { userId };
  if (action) query.action = action;
  if (resource) query.resource = resource;

  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(limit)
    .skip(skip)
    .lean();
};

auditLogSchema.statics.findByResource = function(resource, resourceId, options = {}) {
  const { limit = 50, skip = 0 } = options;
  
  return this.find({ resource, resourceId })
    .populate('userId', 'username email')
    .sort({ timestamp: -1 })
    .limit(limit)
    .skip(skip)
    .lean();
};

auditLogSchema.statics.getActionStats = function(startDate, endDate) {
  const matchStage = {};
  if (startDate || endDate) {
    matchStage.timestamp = {};
    if (startDate) matchStage.timestamp.$gte = new Date(startDate);
    if (endDate) matchStage.timestamp.$lte = new Date(endDate);
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$action',
        count: { $sum: 1 },
        successCount: {
          $sum: { $cond: [{ $eq: ['$success', true] }, 1, 0] }
        },
        failureCount: {
          $sum: { $cond: [{ $eq: ['$success', false] }, 1, 0] }
        },
        avgDuration: { $avg: '$duration' }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

const AuditLog = mongoose.model('AuditLog', auditLogSchema);

module.exports = AuditLog;
