{"_format": "hh-sol-artifact-1", "contractName": "MockERC20", "sourceName": "contracts/MockERC20.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}