/**
 * Background Job Service
 * Handles expensive operations asynchronously to improve API response times
 */

const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

class BackgroundJobService {
  constructor() {
    this.jobs = new Map();
    this.workers = new Map();
    this.maxConcurrentJobs = parseInt(process.env.MAX_CONCURRENT_JOBS) || 10;
    this.currentJobs = 0;
    this.redisClient = getRedisClient();
    
    this.jobTypes = {
      WALLET_BALANCE_UPDATE: 'wallet_balance_update',
      PRICE_UPDATE: 'price_update',
      NOTIFICATION_SEND: 'notification_send',
      FILE_PROCESSING: 'file_processing',
      ANALYTICS_CALCULATION: 'analytics_calculation',
      BLOCKCHAIN_SYNC: 'blockchain_sync',
      EMAIL_SEND: 'email_send',
      REPUTATION_UPDATE: 'reputation_update'
    };

    this.initializeWorkers();
  }

  /**
   * Initialize background workers
   */
  initializeWorkers() {
    // Wallet balance update worker
    this.registerWorker(this.jobTypes.WALLET_BALANCE_UPDATE, async (data) => {
      const { walletService } = require('./walletService');
      await walletService.updateBalances(data.userId);
    });

    // Price update worker
    this.registerWorker(this.jobTypes.PRICE_UPDATE, async (data) => {
      const { priceService } = require('./priceService');
      await priceService.updatePrices(data.symbols);
    });

    // Notification send worker
    this.registerWorker(this.jobTypes.NOTIFICATION_SEND, async (data) => {
      const { notificationService } = require('./notificationService');
      await notificationService.sendNotification(data.userId, data.notification);
    });

    // File processing worker
    this.registerWorker(this.jobTypes.FILE_PROCESSING, async (data) => {
      const { fileSecurityService } = require('./fileSecurityService');
      await fileSecurityService.processFile(data.fileId, data.options);
    });

    // Analytics calculation worker
    this.registerWorker(this.jobTypes.ANALYTICS_CALCULATION, async (data) => {
      await this.calculateAnalytics(data.type, data.parameters);
    });

    // Blockchain sync worker
    this.registerWorker(this.jobTypes.BLOCKCHAIN_SYNC, async (data) => {
      const { blockchainService } = require('./blockchainService');
      await blockchainService.syncTransactions(data.address, data.network);
    });

    // Email send worker
    this.registerWorker(this.jobTypes.EMAIL_SEND, async (data) => {
      const { emailService } = require('./emailService');
      await emailService.sendEmail(data.to, data.subject, data.template, data.data);
    });

    // Reputation update worker
    this.registerWorker(this.jobTypes.REPUTATION_UPDATE, async (data) => {
      await this.updateUserReputation(data.userId, data.tradeId);
    });
  }

  /**
   * Register a worker for a specific job type
   */
  registerWorker(jobType, workerFunction) {
    this.workers.set(jobType, workerFunction);
  }

  /**
   * Add a job to the queue
   */
  async addJob(jobType, data, options = {}) {
    const jobId = this.generateJobId();
    const job = {
      id: jobId,
      type: jobType,
      data,
      status: 'pending',
      createdAt: new Date(),
      attempts: 0,
      maxAttempts: options.maxAttempts || 3,
      delay: options.delay || 0,
      priority: options.priority || 5, // 1-10, 1 being highest priority
      retryDelay: options.retryDelay || 5000
    };

    // Store job in memory and Redis
    this.jobs.set(jobId, job);
    
    if (this.redisClient && this.redisClient.isReady) {
      await this.redisClient.lpush(`jobs:${jobType}`, JSON.stringify(job));
    }

    // Process immediately if delay is 0 and we have capacity
    if (job.delay === 0 && this.currentJobs < this.maxConcurrentJobs) {
      setImmediate(() => this.processJob(jobId));
    } else if (job.delay > 0) {
      // Schedule delayed job
      setTimeout(() => this.processJob(jobId), job.delay);
    }

    return jobId;
  }

  /**
   * Process a job
   */
  async processJob(jobId) {
    const job = this.jobs.get(jobId);
    if (!job || job.status !== 'pending') {
      return;
    }

    if (this.currentJobs >= this.maxConcurrentJobs) {
      // Queue is full, try again later
      setTimeout(() => this.processJob(jobId), 1000);
      return;
    }

    this.currentJobs++;
    job.status = 'processing';
    job.startedAt = new Date();

    try {
      const worker = this.workers.get(job.type);
      if (!worker) {
        throw new Error(`No worker registered for job type: ${job.type}`);
      }

      await worker(job.data);
      
      job.status = 'completed';
      job.completedAt = new Date();
      
      logger.info(`Job completed successfully`, {
        jobId: job.id,
        type: job.type,
        duration: job.completedAt - job.startedAt
      });

    } catch (error) {
      job.attempts++;
      job.lastError = error.message;
      job.lastAttemptAt = new Date();

      if (job.attempts >= job.maxAttempts) {
        job.status = 'failed';
        logger.error(`Job failed after ${job.attempts} attempts`, {
          jobId: job.id,
          type: job.type,
          error: error.message,
          data: job.data
        });
      } else {
        job.status = 'pending';
        logger.warn(`Job failed, retrying (${job.attempts}/${job.maxAttempts})`, {
          jobId: job.id,
          type: job.type,
          error: error.message
        });
        
        // Retry with exponential backoff
        const retryDelay = job.retryDelay * Math.pow(2, job.attempts - 1);
        setTimeout(() => this.processJob(jobId), retryDelay);
      }
    } finally {
      this.currentJobs--;
    }

    // Clean up completed/failed jobs after 1 hour
    if (job.status === 'completed' || job.status === 'failed') {
      setTimeout(() => {
        this.jobs.delete(jobId);
      }, 60 * 60 * 1000);
    }
  }

  /**
   * Get job status
   */
  getJobStatus(jobId) {
    const job = this.jobs.get(jobId);
    if (!job) {
      return null;
    }

    return {
      id: job.id,
      type: job.type,
      status: job.status,
      createdAt: job.createdAt,
      startedAt: job.startedAt,
      completedAt: job.completedAt,
      attempts: job.attempts,
      maxAttempts: job.maxAttempts,
      lastError: job.lastError
    };
  }

  /**
   * Get queue statistics
   */
  getQueueStats() {
    const jobs = Array.from(this.jobs.values());
    
    return {
      total: jobs.length,
      pending: jobs.filter(job => job.status === 'pending').length,
      processing: jobs.filter(job => job.status === 'processing').length,
      completed: jobs.filter(job => job.status === 'completed').length,
      failed: jobs.filter(job => job.status === 'failed').length,
      currentJobs: this.currentJobs,
      maxConcurrentJobs: this.maxConcurrentJobs
    };
  }

  /**
   * Process pending jobs from Redis queue
   */
  async processPendingJobs() {
    if (!this.redisClient || !this.redisClient.isReady) {
      return;
    }

    for (const jobType of Object.values(this.jobTypes)) {
      try {
        const jobData = await this.redisClient.rpop(`jobs:${jobType}`);
        if (jobData) {
          const job = JSON.parse(jobData);
          this.jobs.set(job.id, job);
          this.processJob(job.id);
        }
      } catch (error) {
        logger.error(`Error processing pending jobs for type ${jobType}:`, error);
      }
    }
  }

  /**
   * Calculate analytics in background
   */
  async calculateAnalytics(type, parameters) {
    const Trade = require('../models/Trade');
    const User = require('../models/User');
    
    switch (type) {
      case 'user_stats':
        const userStats = await Trade.aggregate([
          { $match: { $or: [{ seller: parameters.userId }, { buyer: parameters.userId }] } },
          {
            $group: {
              _id: null,
              totalTrades: { $sum: 1 },
              totalVolume: { $sum: '$fiat.amount' },
              averageTradeSize: { $avg: '$fiat.amount' }
            }
          }
        ]);
        
        await User.findByIdAndUpdate(parameters.userId, {
          'statistics.totalTrades': userStats[0]?.totalTrades || 0,
          'statistics.totalVolume': userStats[0]?.totalVolume || 0,
          'statistics.averageTradeSize': userStats[0]?.averageTradeSize || 0
        });
        break;

      case 'platform_stats':
        // Calculate platform-wide statistics
        const platformStats = await Trade.aggregate([
          {
            $group: {
              _id: null,
              totalTrades: { $sum: 1 },
              totalVolume: { $sum: '$fiat.amount' },
              activeUsers: { $addToSet: '$seller' }
            }
          }
        ]);
        
        // Store in Redis for quick access
        if (this.redisClient && this.redisClient.isReady) {
          await this.redisClient.setex('platform:stats', 3600, JSON.stringify(platformStats[0]));
        }
        break;
    }
  }

  /**
   * Update user reputation based on trade completion
   */
  async updateUserReputation(userId, tradeId) {
    const Trade = require('../models/Trade');
    const User = require('../models/User');
    
    const trade = await Trade.findById(tradeId);
    if (!trade || trade.status !== 'completed') {
      return;
    }

    // Calculate reputation score based on trade completion time, amount, etc.
    const completionTime = trade.completedAt - trade.createdAt;
    const expectedTime = 24 * 60 * 60 * 1000; // 24 hours
    
    let reputationChange = 1; // Base reputation for completing trade
    
    if (completionTime < expectedTime) {
      reputationChange += 0.5; // Bonus for quick completion
    }
    
    if (trade.fiat.amount > 1000) {
      reputationChange += 0.5; // Bonus for large trades
    }

    await User.findByIdAndUpdate(userId, {
      $inc: { 'reputation.score': reputationChange, 'reputation.totalTrades': 1 }
    });
  }

  /**
   * Generate unique job ID
   */
  generateJobId() {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Start processing pending jobs periodically
   */
  startJobProcessor() {
    // Process pending jobs every 5 seconds
    setInterval(() => {
      this.processPendingJobs();
    }, 5000);

    logger.info('Background job processor started');
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('Shutting down background job service...');
    
    // Wait for current jobs to complete (max 30 seconds)
    const maxWaitTime = 30000;
    const startTime = Date.now();
    
    while (this.currentJobs > 0 && (Date.now() - startTime) < maxWaitTime) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    logger.info(`Background job service shutdown complete. ${this.currentJobs} jobs may have been interrupted.`);
  }
}

// Create singleton instance
const backgroundJobService = new BackgroundJobService();

// Start job processor
backgroundJobService.startJobProcessor();

module.exports = {
  BackgroundJobService,
  backgroundJobService
};
