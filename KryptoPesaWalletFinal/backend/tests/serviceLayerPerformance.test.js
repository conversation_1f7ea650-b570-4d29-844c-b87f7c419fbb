/**
 * Service Layer Performance Tests
 * Comprehensive tests for service layer performance optimizations
 */

const { expect } = require('chai');
const sinon = require('sinon');
const mongoose = require('mongoose');
const { ServiceLayerPerformanceService } = require('../src/services/serviceLayerPerformance');

describe('Service Layer Performance Optimization', () => {
  let performanceService;
  let redisStub;
  let mongooseStub;

  beforeEach(() => {
    performanceService = new ServiceLayerPerformanceService();
    
    // Stub Redis client
    redisStub = {
      isReady: true,
      setex: sinon.stub().resolves('OK'),
      get: sinon.stub().resolves(null),
      del: sinon.stub().resolves(1)
    };
    
    performanceService.redisClient = redisStub;
    
    // Stub mongoose
    mongooseStub = {
      model: sinon.stub().returns({
        aggregate: sinon.stub().resolves([]),
        bulkWrite: sinon.stub().resolves({ modifiedCount: 10 }),
        collection: {
          createIndex: sinon.stub().resolves()
        }
      })
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Distributed Caching', () => {
    it('should set cache in both Redis and memory', async () => {
      const key = 'test:key';
      const value = { data: 'test' };
      const ttl = 300;

      const result = await performanceService.setCache(key, value, ttl);

      expect(result).to.be.true;
      expect(redisStub.setex).to.have.been.calledOnce;
      expect(performanceService.distributedCache.has(key)).to.be.true;
    });

    it('should get cache from Redis first, then memory fallback', async () => {
      const key = 'test:key';
      const value = { data: 'test' };
      
      // Set up Redis to return cached data
      redisStub.get.resolves(JSON.stringify({
        data: value,
        timestamp: Date.now(),
        ttl: 300000
      }));

      const result = await performanceService.getCache(key);

      expect(result).to.deep.equal(value);
      expect(performanceService.performanceMetrics.cacheHits).to.equal(1);
    });

    it('should fallback to memory cache when Redis fails', async () => {
      const key = 'test:key';
      const value = { data: 'test' };
      
      // Set memory cache
      performanceService.distributedCache.set(key, {
        data: value,
        timestamp: Date.now(),
        ttl: 300000
      });
      
      // Make Redis fail
      redisStub.get.rejects(new Error('Redis connection failed'));

      const result = await performanceService.getCache(key);

      expect(result).to.deep.equal(value);
      expect(performanceService.performanceMetrics.cacheHits).to.equal(1);
    });

    it('should return null for cache miss', async () => {
      const key = 'nonexistent:key';
      
      redisStub.get.resolves(null);

      const result = await performanceService.getCache(key);

      expect(result).to.be.null;
      expect(performanceService.performanceMetrics.cacheMisses).to.equal(1);
    });
  });

  describe('Price Optimization', () => {
    it('should cache price data with shorter TTL', async () => {
      const cryptos = ['BTC', 'ETH'];
      const currencies = ['USD'];
      
      // Mock price service
      const priceServiceStub = {
        getPrices: sinon.stub().resolves({
          bitcoin: { usd: 50000 },
          ethereum: { usd: 3000 }
        })
      };
      
      // Replace require to return our stub
      const originalRequire = require;
      require = sinon.stub().returns(priceServiceStub);
      
      const result = await performanceService.getOptimizedPrices(cryptos, currencies);

      expect(result).to.have.property('bitcoin');
      expect(result).to.have.property('ethereum');
      expect(performanceService.performanceMetrics.priceApiCalls).to.equal(1);
      
      // Restore require
      require = originalRequire;
    });

    it('should return cached price data on subsequent calls', async () => {
      const cryptos = ['BTC'];
      const currencies = ['USD'];
      const cachedData = { bitcoin: { usd: 50000 } };
      
      // Set cache
      await performanceService.setCache(
        `prices:${JSON.stringify(cryptos)}:${JSON.stringify(currencies)}`,
        cachedData,
        60
      );

      const result = await performanceService.getOptimizedPrices(cryptos, currencies);

      expect(result).to.deep.equal(cachedData);
      expect(performanceService.performanceMetrics.cacheHits).to.equal(1);
    });
  });

  describe('Bulk Operations', () => {
    it('should queue bulk operations', async () => {
      const operation = 'balance_update';
      const data = { userId: 'user123', currency: 'BTC', balance: '1.5' };

      await performanceService.addToBulkQueue(operation, data);

      const queueKey = `bulk:${operation}`;
      expect(performanceService.bulkOperationQueue.has(queueKey)).to.be.true;
      expect(performanceService.bulkOperationQueue.get(queueKey)).to.have.length(1);
    });

    it('should process bulk queue when batch size reached', async () => {
      const operation = 'balance_update';
      
      // Add items to reach batch size
      for (let i = 0; i < performanceService.thresholds.bulkBatchSize; i++) {
        await performanceService.addToBulkQueue(operation, {
          userId: `user${i}`,
          currency: 'BTC',
          balance: '1.0'
        });
      }

      // Queue should be processed automatically
      const queueKey = `bulk:${operation}`;
      expect(performanceService.bulkOperationQueue.get(queueKey)).to.have.length(0);
    });

    it('should process bulk balance updates', async () => {
      const batchData = [
        { data: { userId: 'user1', currency: 'BTC', balance: '1.0' } },
        { data: { userId: 'user2', currency: 'ETH', balance: '10.0' } }
      ];

      // Mock mongoose model
      const mockModel = {
        bulkWrite: sinon.stub().resolves({ modifiedCount: 2 })
      };
      sinon.stub(mongoose, 'model').returns(mockModel);

      await performanceService.processBulkBalanceUpdates(batchData);

      expect(mockModel.bulkWrite).to.have.been.calledOnce;
      expect(mockModel.bulkWrite.firstCall.args[0]).to.have.length(2);
    });
  });

  describe('Query Optimization', () => {
    it('should execute optimized aggregation with caching', async () => {
      const collection = 'Trade';
      const pipeline = [{ $match: { status: 'active' } }];
      const cacheKey = 'trades:active';
      const mockResult = [{ _id: 'trade1', status: 'active' }];

      // Mock mongoose model
      const mockModel = {
        aggregate: sinon.stub().resolves(mockResult)
      };
      sinon.stub(mongoose, 'model').returns(mockModel);

      const result = await performanceService.getOptimizedAggregation(
        collection,
        pipeline,
        cacheKey
      );

      expect(result).to.deep.equal(mockResult);
      expect(mockModel.aggregate).to.have.been.calledOnce;
      expect(performanceService.performanceMetrics.queryOptimizations).to.equal(1);
    });

    it('should return cached aggregation result', async () => {
      const collection = 'Trade';
      const pipeline = [{ $match: { status: 'active' } }];
      const cacheKey = 'trades:active';
      const cachedResult = [{ _id: 'trade1', status: 'active' }];

      // Set cache
      await performanceService.setCache(cacheKey, cachedResult);

      const result = await performanceService.getOptimizedAggregation(
        collection,
        pipeline,
        cacheKey
      );

      expect(result).to.deep.equal(cachedResult);
      expect(performanceService.performanceMetrics.cacheHits).to.equal(1);
    });

    it('should get optimal index for query', () => {
      const collection = 'Trade';
      const pipeline = [{ $match: { status: 'active', buyer: 'user123' } }];

      const index = performanceService.getOptimalIndex(collection, pipeline);

      expect(index).to.have.property('status');
      expect(index).to.have.property('buyer');
    });
  });

  describe('Performance Metrics', () => {
    it('should track response time metrics', () => {
      const responseTime = 250;

      performanceService.updateResponseTime(responseTime);

      expect(performanceService.performanceMetrics.totalOperations).to.equal(1);
      expect(performanceService.performanceMetrics.averageResponseTime).to.equal(responseTime);
    });

    it('should track slow operations', () => {
      const slowResponseTime = 600; // Above threshold

      performanceService.updateResponseTime(slowResponseTime);

      expect(performanceService.performanceMetrics.slowOperations).to.equal(1);
    });

    it('should collect comprehensive performance metrics', () => {
      // Add some test data
      performanceService.performanceMetrics.totalOperations = 100;
      performanceService.performanceMetrics.cacheHits = 80;
      performanceService.performanceMetrics.priceApiErrors = 2;

      const metrics = performanceService.getPerformanceMetrics();

      expect(metrics).to.have.property('cacheHitRate');
      expect(metrics).to.have.property('errorRate');
      expect(metrics).to.have.property('slowOperationRate');
      expect(metrics.cacheHitRate).to.equal('80.00');
    });

    it('should reset metrics', () => {
      // Set some metrics
      performanceService.performanceMetrics.totalOperations = 100;
      performanceService.performanceMetrics.cacheHits = 80;

      performanceService.resetMetrics();

      expect(performanceService.performanceMetrics.totalOperations).to.equal(0);
      expect(performanceService.performanceMetrics.cacheHits).to.equal(0);
    });
  });

  describe('Memory Management', () => {
    it('should cleanup expired cache entries', () => {
      const now = Date.now();
      
      // Add expired entry
      performanceService.distributedCache.set('expired:key', {
        data: 'test',
        timestamp: now - 400000, // 400 seconds ago
        ttl: 300000 // 300 seconds TTL
      });
      
      // Add valid entry
      performanceService.distributedCache.set('valid:key', {
        data: 'test',
        timestamp: now,
        ttl: 300000
      });

      performanceService.cleanupMemoryCache();

      expect(performanceService.distributedCache.has('expired:key')).to.be.false;
      expect(performanceService.distributedCache.has('valid:key')).to.be.true;
    });

    it('should limit cache size', () => {
      const originalMaxSize = performanceService.thresholds.maxCacheSize;
      performanceService.thresholds.maxCacheSize = 5;

      // Add more entries than max size
      for (let i = 0; i < 10; i++) {
        performanceService.distributedCache.set(`key${i}`, {
          data: `data${i}`,
          timestamp: Date.now(),
          ttl: 300000
        });
      }

      performanceService.cleanupMemoryCache();

      expect(performanceService.distributedCache.size).to.be.at.most(5);
      
      // Restore original max size
      performanceService.thresholds.maxCacheSize = originalMaxSize;
    });
  });

  describe('Index Optimization', () => {
    it('should create indexes for all collections', async () => {
      const mockModel = {
        collection: {
          createIndex: sinon.stub().resolves()
        }
      };
      sinon.stub(mongoose, 'model').returns(mockModel);

      await performanceService.optimizeIndexes();

      expect(mockModel.collection.createIndex).to.have.been.called;
    });

    it('should handle index creation errors gracefully', async () => {
      const mockModel = {
        collection: {
          createIndex: sinon.stub().rejects(new Error('Index creation failed'))
        }
      };
      sinon.stub(mongoose, 'model').returns(mockModel);

      // Should not throw
      await performanceService.optimizeIndexes();

      expect(mockModel.collection.createIndex).to.have.been.called;
    });
  });

  describe('Graceful Shutdown', () => {
    it('should process remaining bulk operations on shutdown', async () => {
      // Add some bulk operations
      await performanceService.addToBulkQueue('balance_update', {
        userId: 'user1',
        currency: 'BTC',
        balance: '1.0'
      });

      const processBulkQueueSpy = sinon.spy(performanceService, 'processBulkQueue');

      await performanceService.shutdown();

      expect(processBulkQueueSpy).to.have.been.called;
      expect(performanceService.distributedCache.size).to.equal(0);
      expect(performanceService.bulkOperationQueue.size).to.equal(0);
    });
  });
});
