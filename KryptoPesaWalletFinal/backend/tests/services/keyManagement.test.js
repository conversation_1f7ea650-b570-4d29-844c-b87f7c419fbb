/**
 * Key Management Security Tests
 * Comprehensive tests for enhanced key management security features
 */

const { expect } = require('chai');
const sinon = require('sinon');
const mongoose = require('mongoose');
const SecureKeyService = require('../../src/services/keyManagement/secureKeyService');
const KeyMonitoringService = require('../../src/services/keyManagement/keyMonitoringService');
const SecureKey = require('../../src/models/SecureKey');
const { SecurityConfig } = require('../../src/config/security');

describe('Key Management Security', () => {
  let secureKeyService;
  let keyMonitoringService;
  let securityConfig;
  let sandbox;

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
    secureKeyService = new SecureKeyService();
    keyMonitoringService = new KeyMonitoringService();
    securityConfig = new SecurityConfig();

    // Mock AWS services
    sandbox.stub(secureKeyService, 'testAWSConnectivity').resolves(true);
    sandbox.stub(secureKeyService, 'getFromAWSSecretsManager').resolves(null);
    sandbox.stub(secureKeyService, 'storeInAWSSecretsManager').resolves();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('SecureKey Model', () => {
    it('should create secure key with proper encryption', async () => {
      const testData = {
        keyId: 'test_blockchain_key',
        keyType: 'blockchain_private_key',
        network: 'ethereum',
        encryptedData: securityConfig.encrypt('test-private-key'),
        metadata: {
          description: 'Test ethereum private key',
          purpose: 'testing'
        }
      };

      const secureKey = new SecureKey(testData);
      await secureKey.save();

      expect(secureKey.keyId).to.equal('test_blockchain_key');
      expect(secureKey.keyType).to.equal('blockchain_private_key');
      expect(secureKey.network).to.equal('ethereum');
      expect(secureKey.security.isActive).to.be.true;
      expect(secureKey.security.isCompromised).to.be.false;
      expect(secureKey.security.checksumHash).to.be.a('string');
    });

    it('should calculate rotation status correctly', async () => {
      const secureKey = new SecureKey({
        keyId: 'test_key',
        keyType: 'blockchain_private_key',
        network: 'polygon',
        encryptedData: securityConfig.encrypt('test-key'),
        rotation: {
          nextRotation: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
        }
      });

      expect(secureKey.rotationStatus).to.equal('overdue');
    });

    it('should add audit entries correctly', async () => {
      const secureKey = new SecureKey({
        keyId: 'test_audit_key',
        keyType: 'blockchain_private_key',
        network: 'ethereum',
        encryptedData: securityConfig.encrypt('test-key')
      });

      await secureKey.addAuditEntry('accessed', {
        serviceId: 'test_service',
        ipAddress: '127.0.0.1'
      });

      expect(secureKey.auditTrail).to.have.length(1);
      expect(secureKey.auditTrail[0].action).to.equal('accessed');
      expect(secureKey.auditTrail[0].details.serviceId).to.equal('test_service');
    });

    it('should mark key as compromised', async () => {
      const secureKey = new SecureKey({
        keyId: 'test_compromised_key',
        keyType: 'blockchain_private_key',
        network: 'ethereum',
        encryptedData: securityConfig.encrypt('test-key')
      });

      await secureKey.markCompromised('test_breach', 'admin_user');

      expect(secureKey.security.isCompromised).to.be.true;
      expect(secureKey.security.isActive).to.be.false;
      expect(secureKey.security.compromisedReason).to.equal('test_breach');
      expect(secureKey.auditTrail).to.have.length(1);
      expect(secureKey.auditTrail[0].action).to.equal('compromised');
    });
  });

  describe('SecureKeyService', () => {
    it('should initialize with fallback mode when AWS unavailable', async () => {
      sandbox.restore();
      sandbox.stub(secureKeyService, 'testAWSConnectivity').rejects(new Error('NetworkingError'));
      sandbox.stub(secureKeyService, 'loadEncryptedKeysFromDatabase').resolves();

      const result = await secureKeyService.initialize();

      expect(result).to.be.true;
      expect(secureKeyService.fallbackMode).to.be.true;
    });

    it('should store keys in both AWS and database', async () => {
      const storeInDatabaseStub = sandbox.stub(secureKeyService, 'storeInSecureDatabase').resolves();
      
      await secureKeyService.storeEncryptedKey('ethereum', 'encrypted-key-data');

      expect(storeInDatabaseStub.calledOnce).to.be.true;
    });

    it('should retrieve keys with fallback priority', async () => {
      sandbox.stub(secureKeyService, 'getFromSecureDatabase').resolves('database-key');
      sandbox.stub(secureKeyService, 'recordKeyAccess').resolves();

      const result = await secureKeyService.getStoredEncryptedKey('ethereum');

      expect(result).to.equal('database-key');
    });

    it('should handle key rotation with monitoring', async () => {
      const mockKey = {
        keyId: 'test_key',
        keyType: 'blockchain_private_key',
        network: 'ethereum',
        rotateKey: sandbox.stub().resolves(),
        addAuditEntry: sandbox.stub().resolves()
      };

      sandbox.stub(SecureKey, 'findKeysNeedingRotation').resolves([mockKey]);
      sandbox.stub(secureKeyService, 'getFromSecureDatabase').resolves('test-private-key');
      sandbox.stub(secureKeyService, 'generateRotationReport').resolves();

      await secureKeyService.rotateKeys();

      expect(mockKey.rotateKey.calledOnce).to.be.true;
    });

    it('should generate security reports', async () => {
      const mockSummary = [
        { _id: 'blockchain_private_key', total: 3, active: 3, compromised: 0, needingRotation: 1 }
      ];

      sandbox.stub(SecureKey, 'getSecuritySummary').resolves(mockSummary);
      sandbox.stub(SecureKey, 'findCompromisedKeys').resolves([]);

      const report = await secureKeyService.generateSecurityReport();

      expect(report).to.be.an('object');
      expect(report.summary).to.deep.equal(mockSummary);
      expect(report.compromisedKeys).to.equal(0);
    });

    it('should handle rotation failures with alerts', async () => {
      const error = new Error('Rotation failed');
      sandbox.stub(secureKeyService, 'sendSecurityAlert').resolves();

      await secureKeyService.handleRotationFailure(error);

      expect(secureKeyService.sendSecurityAlert.calledOnce).to.be.true;
      expect(secureKeyService.sendSecurityAlert.firstCall.args[0]).to.equal('key_rotation_failed');
    });
  });

  describe('KeyMonitoringService', () => {
    it('should initialize monitoring service', async () => {
      sandbox.stub(keyMonitoringService, 'startContinuousMonitoring').resolves();
      sandbox.stub(keyMonitoringService, 'initializeMetrics').resolves();

      const result = await keyMonitoringService.initialize();

      expect(result).to.be.true;
    });

    it('should detect suspicious access patterns', async () => {
      // Simulate high access frequency
      for (let i = 0; i < 15; i++) {
        keyMonitoringService.recordKeyAccess('test_key', {
          timestamp: Date.now() - (i * 1000) // Last 15 seconds
        });
      }

      sandbox.stub(keyMonitoringService, 'recordSecurityAlert').resolves();

      await keyMonitoringService.analyzeAccessPatterns();

      expect(keyMonitoringService.recordSecurityAlert.calledOnce).to.be.true;
      expect(keyMonitoringService.recordSecurityAlert.firstCall.args[0]).to.equal('suspicious_access_pattern');
    });

    it('should verify key integrity', async () => {
      const mockKey = {
        keyId: 'test_key',
        encryptedData: { encrypted: 'test', iv: 'test', salt: 'test', tag: 'test' },
        security: { checksumHash: 'wrong_checksum' },
        markCompromised: sandbox.stub().resolves()
      };

      sandbox.stub(SecureKey, 'find').resolves([mockKey]);
      sandbox.stub(keyMonitoringService, 'recordSecurityAlert').resolves();

      await keyMonitoringService.verifyKeyIntegrity();

      expect(mockKey.markCompromised.calledOnce).to.be.true;
      expect(keyMonitoringService.recordSecurityAlert.calledOnce).to.be.true;
    });

    it('should handle compromised keys', async () => {
      const compromisedKey = {
        keyId: 'compromised_key',
        keyType: 'blockchain_private_key',
        network: 'ethereum',
        security: {
          compromisedAt: new Date(),
          compromisedReason: 'test_breach'
        }
      };

      sandbox.stub(keyMonitoringService, 'recordSecurityAlert').resolves();

      await keyMonitoringService.handleCompromisedKeys([compromisedKey]);

      expect(keyMonitoringService.recordSecurityAlert.calledOnce).to.be.true;
      expect(keyMonitoringService.recordSecurityAlert.firstCall.args[0]).to.equal('compromised_key_active');
    });

    it('should generate comprehensive metrics report', async () => {
      sandbox.stub(keyMonitoringService, 'getKeyMetrics').resolves({
        totalKeys: 5,
        activeKeys: 4,
        compromisedKeys: 1,
        keysNeedingRotation: 2
      });

      const report = await keyMonitoringService.generateMetricsReport();

      expect(report).to.be.an('object');
      expect(report.keyMetrics).to.be.an('object');
      expect(report.securityMetrics).to.be.an('object');
      expect(report.performanceMetrics).to.be.an('object');
    });

    it('should record and manage security alerts', async () => {
      await keyMonitoringService.recordSecurityAlert('test_alert', {
        severity: 'high',
        details: 'test alert details'
      });

      expect(keyMonitoringService.metrics.securityAlerts).to.have.length(1);
      expect(keyMonitoringService.metrics.securityAlerts[0].type).to.equal('test_alert');
      expect(keyMonitoringService.metrics.securityAlerts[0].details.severity).to.equal('high');
    });
  });

  describe('Security Configuration', () => {
    it('should encrypt and decrypt data correctly', () => {
      const testData = 'sensitive-private-key-data';
      
      const encrypted = securityConfig.encrypt(testData);
      const decrypted = securityConfig.decrypt(encrypted);

      expect(encrypted).to.be.an('object');
      expect(encrypted.encrypted).to.be.a('string');
      expect(encrypted.iv).to.be.a('string');
      expect(encrypted.salt).to.be.a('string');
      expect(encrypted.tag).to.be.a('string');
      expect(decrypted).to.equal(testData);
    });

    it('should fail decryption with wrong key', () => {
      const testData = 'sensitive-data';
      const encrypted = securityConfig.encrypt(testData, 'correct-key');

      expect(() => {
        securityConfig.decrypt(encrypted, 'wrong-key');
      }).to.throw('Decryption failed');
    });

    it('should fail decryption with tampered data', () => {
      const testData = 'sensitive-data';
      const encrypted = securityConfig.encrypt(testData);
      
      // Tamper with encrypted data
      encrypted.encrypted = 'tampered-data';

      expect(() => {
        securityConfig.decrypt(encrypted);
      }).to.throw('Decryption failed');
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete key lifecycle with monitoring', async () => {
      // Initialize services
      sandbox.stub(secureKeyService, 'testAWSConnectivity').resolves(true);
      sandbox.stub(secureKeyService, 'loadEncryptedKeys').resolves();
      sandbox.stub(secureKeyService, 'setupKeyRotation').returns();
      sandbox.stub(secureKeyService, 'setupRotationMonitoring').returns();
      sandbox.stub(secureKeyService, 'initializeMetrics').returns();

      await secureKeyService.initialize();

      // Store a key
      sandbox.stub(secureKeyService, 'storeInSecureDatabase').resolves();
      await secureKeyService.storeEncryptedKey('ethereum', 'test-encrypted-key');

      // Monitor key access
      keyMonitoringService.recordKeyAccess('ethereum_key', {
        serviceId: 'escrow_service',
        ipAddress: '127.0.0.1'
      });

      // Verify monitoring recorded the access
      expect(keyMonitoringService.metrics.keyAccesses.has('ethereum_key')).to.be.true;
    });
  });
});
