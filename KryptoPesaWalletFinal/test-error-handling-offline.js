/**
 * Comprehensive Test for Error Handling and Offline Support
 * Tests all error handling mechanisms and offline functionality
 */

const fs = require('fs');
const path = require('path');

async function testErrorHandlingAndOfflineSupport() {
  console.log('🧪 Testing Error Handling and Offline Support...\n');

  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };

  function addTest(name, passed, details = '') {
    results.tests.push({ name, passed, details });
    if (passed) {
      results.passed++;
      console.log(`✅ ${name}`);
    } else {
      results.failed++;
      console.log(`❌ ${name}: ${details}`);
    }
  }

  try {
    // Test 1: Error Boundary Component
    console.log('1️⃣ Testing Error Boundary Component...');
    const errorBoundaryPath = path.join(__dirname, 'a0-project/components/ErrorBoundary.tsx');
    const errorBoundaryExists = fs.existsSync(errorBoundaryPath);
    addTest('Error Boundary exists', errorBoundaryExists);

    if (errorBoundaryExists) {
      const errorBoundaryContent = fs.readFileSync(errorBoundaryPath, 'utf8');
      const hasErrorReporting = errorBoundaryContent.includes('ErrorReportingService');
      const hasSwahiliSupport = errorBoundaryContent.includes('useTranslation');
      const hasRetryMechanism = errorBoundaryContent.includes('handleRetry');
      
      addTest('Error Boundary has error reporting', hasErrorReporting);
      addTest('Error Boundary has Swahili support', hasSwahiliSupport);
      addTest('Error Boundary has retry mechanism', hasRetryMechanism);
    }

    // Test 2: Error Reporting Service
    console.log('\n2️⃣ Testing Error Reporting Service...');
    const errorReportingPath = path.join(__dirname, 'a0-project/services/errorReporting.ts');
    const errorReportingExists = fs.existsSync(errorReportingPath);
    addTest('Error Reporting Service exists', errorReportingExists);

    if (errorReportingExists) {
      const errorReportingContent = fs.readFileSync(errorReportingPath, 'utf8');
      const hasDeviceInfo = errorReportingContent.includes('DeviceInfo');
      const hasQueueManagement = errorReportingContent.includes('reportQueue');
      const hasSeverityLevels = errorReportingContent.includes('severity');
      const hasOfflineSupport = errorReportingContent.includes('AsyncStorage');
      
      addTest('Error Reporting has device info', hasDeviceInfo);
      addTest('Error Reporting has queue management', hasQueueManagement);
      addTest('Error Reporting has severity levels', hasSeverityLevels);
      addTest('Error Reporting has offline support', hasOfflineSupport);
    }

    // Test 3: API Service Error Handling
    console.log('\n3️⃣ Testing API Service Error Handling...');
    const apiServicePath = path.join(__dirname, 'a0-project/services/api.ts');
    const apiServiceExists = fs.existsSync(apiServicePath);
    addTest('API Service exists', apiServiceExists);

    if (apiServiceExists) {
      const apiServiceContent = fs.readFileSync(apiServicePath, 'utf8');
      const hasRetryLogic = apiServiceContent.includes('retryCount');
      const hasErrorReporting = apiServiceContent.includes('ErrorReportingService');
      const hasLocalizedErrors = apiServiceContent.includes('getLocalizedError');
      const hasRetryableCheck = apiServiceContent.includes('isRetryableError');
      
      addTest('API Service has retry logic', hasRetryLogic);
      addTest('API Service has error reporting', hasErrorReporting);
      addTest('API Service has localized errors', hasLocalizedErrors);
      addTest('API Service has retryable error check', hasRetryableCheck);
    }

    // Test 4: Offline Manager
    console.log('\n4️⃣ Testing Offline Manager...');
    const offlineManagerPath = path.join(__dirname, 'a0-project/services/offlineManager.ts');
    const offlineManagerExists = fs.existsSync(offlineManagerPath);
    addTest('Offline Manager exists', offlineManagerExists);

    if (offlineManagerExists) {
      const offlineManagerContent = fs.readFileSync(offlineManagerPath, 'utf8');
      const hasNetworkDetection = offlineManagerContent.includes('NetInfo');
      const hasQueueManagement = offlineManagerContent.includes('offlineQueue');
      const hasDataCaching = offlineManagerContent.includes('offlineData');
      const hasSyncMechanism = offlineManagerContent.includes('syncOfflineActions');
      const hasErrorReporting = offlineManagerContent.includes('ErrorReportingService');
      
      addTest('Offline Manager has network detection', hasNetworkDetection);
      addTest('Offline Manager has queue management', hasQueueManagement);
      addTest('Offline Manager has data caching', hasDataCaching);
      addTest('Offline Manager has sync mechanism', hasSyncMechanism);
      addTest('Offline Manager has error reporting', hasErrorReporting);
    }

    // Test 5: Network Status Component
    console.log('\n5️⃣ Testing Network Status Component...');
    const networkStatusPath = path.join(__dirname, 'a0-project/components/NetworkStatus.tsx');
    const networkStatusExists = fs.existsSync(networkStatusPath);
    addTest('Network Status Component exists', networkStatusExists);

    if (networkStatusExists) {
      const networkStatusContent = fs.readFileSync(networkStatusPath, 'utf8');
      const hasOfflineBanner = networkStatusContent.includes('offlineBanner');
      const hasQueueStatus = networkStatusContent.includes('queueStatus');
      const hasSwahiliSupport = networkStatusContent.includes('useTranslation');
      const hasAnimations = networkStatusContent.includes('Animated');
      
      addTest('Network Status has offline banner', hasOfflineBanner);
      addTest('Network Status has queue status', hasQueueStatus);
      addTest('Network Status has Swahili support', hasSwahiliSupport);
      addTest('Network Status has animations', hasAnimations);
    }

    // Test 6: Swahili Error Messages
    console.log('\n6️⃣ Testing Swahili Error Messages...');
    const swahiliLocalePath = path.join(__dirname, 'a0-project/i18n/locales/sw.json');
    const swahiliLocaleExists = fs.existsSync(swahiliLocalePath);
    addTest('Swahili locale file exists', swahiliLocaleExists);

    if (swahiliLocaleExists) {
      const swahiliContent = JSON.parse(fs.readFileSync(swahiliLocalePath, 'utf8'));
      const hasErrorSection = swahiliContent.errors !== undefined;
      const hasNetworkErrors = swahiliContent.errors?.networkError !== undefined;
      const hasTimeoutErrors = swahiliContent.errors?.timeoutError !== undefined;
      const hasOfflineErrors = swahiliContent.errors?.offlineError !== undefined;
      const hasValidationErrors = swahiliContent.validation !== undefined;
      
      addTest('Swahili has error section', hasErrorSection);
      addTest('Swahili has network errors', hasNetworkErrors);
      addTest('Swahili has timeout errors', hasTimeoutErrors);
      addTest('Swahili has offline errors', hasOfflineErrors);
      addTest('Swahili has validation errors', hasValidationErrors);
    }

    // Test 7: English Error Messages
    console.log('\n7️⃣ Testing English Error Messages...');
    const englishLocalePath = path.join(__dirname, 'a0-project/i18n/locales/en.json');
    const englishLocaleExists = fs.existsSync(englishLocalePath);
    addTest('English locale file exists', englishLocaleExists);

    if (englishLocaleExists) {
      const englishContent = JSON.parse(fs.readFileSync(englishLocalePath, 'utf8'));
      const hasErrorSection = englishContent.errors !== undefined;
      const hasNetworkErrors = englishContent.errors?.networkError !== undefined;
      const hasTimeoutErrors = englishContent.errors?.timeoutError !== undefined;
      const hasOfflineErrors = englishContent.errors?.offlineError !== undefined;
      const hasValidationErrors = englishContent.validation !== undefined;
      
      addTest('English has error section', hasErrorSection);
      addTest('English has network errors', hasNetworkErrors);
      addTest('English has timeout errors', hasTimeoutErrors);
      addTest('English has offline errors', hasOfflineErrors);
      addTest('English has validation errors', hasValidationErrors);
    }

    // Test 8: Backend Error Handling
    console.log('\n8️⃣ Testing Backend Error Handling...');
    const backendErrorHandlerPath = path.join(__dirname, 'backend/src/middleware/errorHandler.js');
    const backendErrorHandlerExists = fs.existsSync(backendErrorHandlerPath);
    addTest('Backend Error Handler exists', backendErrorHandlerExists);

    if (backendErrorHandlerExists) {
      const backendErrorContent = fs.readFileSync(backendErrorHandlerPath, 'utf8');
      const hasAppError = backendErrorContent.includes('AppError');
      const hasProductionHandling = backendErrorContent.includes('sendErrorProd');
      const hasDevelopmentHandling = backendErrorContent.includes('sendErrorDev');
      const hasLogging = backendErrorContent.includes('logger');
      
      addTest('Backend has AppError class', hasAppError);
      addTest('Backend has production error handling', hasProductionHandling);
      addTest('Backend has development error handling', hasDevelopmentHandling);
      addTest('Backend has error logging', hasLogging);
    }

    // Test 9: Enhanced Error Handler
    console.log('\n9️⃣ Testing Enhanced Error Handler...');
    const enhancedErrorHandlerPath = path.join(__dirname, 'backend/src/middleware/enhancedErrorHandler.js');
    const enhancedErrorHandlerExists = fs.existsSync(enhancedErrorHandlerPath);
    addTest('Enhanced Error Handler exists', enhancedErrorHandlerExists);

    if (enhancedErrorHandlerExists) {
      const enhancedErrorContent = fs.readFileSync(enhancedErrorHandlerPath, 'utf8');
      const hasMultiLanguage = enhancedErrorContent.includes('friendlyMessages');
      const hasErrorCategorization = enhancedErrorContent.includes('categorizeError');
      const hasRetryLogic = enhancedErrorContent.includes('isRetryableError');
      const hasMetrics = enhancedErrorContent.includes('trackErrorMetrics');
      
      addTest('Enhanced handler has multi-language support', hasMultiLanguage);
      addTest('Enhanced handler has error categorization', hasErrorCategorization);
      addTest('Enhanced handler has retry logic', hasRetryLogic);
      addTest('Enhanced handler has metrics tracking', hasMetrics);
    }

    // Test 10: East African Formatting Utilities
    console.log('\n🔟 Testing East African Formatting Utilities...');
    const eastAfricanFormattingPath = path.join(__dirname, 'a0-project/utils/eastAfricanFormatting.ts');
    const eastAfricanFormattingExists = fs.existsSync(eastAfricanFormattingPath);
    addTest('East African Formatting exists', eastAfricanFormattingExists);

    if (eastAfricanFormattingExists) {
      const eastAfricanContent = fs.readFileSync(eastAfricanFormattingPath, 'utf8');
      const hasLocalizedErrors = eastAfricanContent.includes('getLocalizedErrorMessage');
      const hasSwahiliSupport = eastAfricanContent.includes("'sw'");
      const hasErrorMessages = eastAfricanContent.includes('errorMessages');
      
      addTest('East African utils has localized errors', hasLocalizedErrors);
      addTest('East African utils has Swahili support', hasSwahiliSupport);
      addTest('East African utils has error messages', hasErrorMessages);
    }

    console.log('\n🎉 Error Handling and Offline Support Test Complete!');
    console.log('\n📊 Test Results:');
    console.log(`   ✅ Passed: ${results.passed}`);
    console.log(`   ❌ Failed: ${results.failed}`);
    console.log(`   📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);

    if (results.failed === 0) {
      console.log('\n🚀 All error handling and offline support features are implemented!');
      console.log('\n📋 Summary of Features:');
      console.log('   ✅ Error Boundary with React error catching');
      console.log('   ✅ Error Reporting Service with queue management');
      console.log('   ✅ API Service with retry logic and localized errors');
      console.log('   ✅ Offline Manager with sync and caching');
      console.log('   ✅ Network Status Component with visual feedback');
      console.log('   ✅ Comprehensive Swahili error translations');
      console.log('   ✅ Backend error handling with categorization');
      console.log('   ✅ Enhanced error middleware with metrics');
      console.log('   ✅ East African localization utilities');
      console.log('   ✅ Production-ready error reporting');
    } else {
      console.log('\n⚠️  Some features need attention. Check failed tests above.');
    }

    return results.failed === 0;

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
testErrorHandlingAndOfflineSupport()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test error:', error);
    process.exit(1);
  });
