{"name": "kryptopesa-admin-dashboard", "version": "1.0.0", "description": "Admin Dashboard for KryptoPesa P2P Trading Platform", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.5", "@mui/x-charts": "^6.0.0-alpha.18", "@mui/x-data-grid": "^6.10.1", "@mui/x-date-pickers": "^6.10.1", "axios": "^1.4.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "notistack": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.2", "react-query": "^3.39.3", "react-router-dom": "^6.14.2", "react-scripts": "^5.0.1", "recharts": "^2.7.2", "socket.io-client": "^4.7.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "eslint": "^8.45.0", "prettier": "^3.0.0"}, "scripts": {"start": "react-scripts start", "build": "npm run build:production", "build:production": "NODE_ENV=production webpack --config webpack.config.production.js --progress", "build:analyze": "ANALYZE=true npm run build:production", "build:development": "react-scripts build", "test": "react-scripts test --coverage --watchAll=false", "test:watch": "react-scripts test", "test:ci": "CI=true npm test", "eject": "react-scripts eject", "optimize:images": "imagemin src/assets/images/* --out-dir=build/static/media --plugin=imagemin-mozjpeg --plugin=imagemin-pngquant", "precommit": "npm run test:ci && npm run lint", "lint": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "lint:check": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}", "serve": "serve -s build -l 3000", "docker:build": "docker build -t kryptopesa-admin .", "docker:run": "docker run -p 80:80 kryptopesa-admin"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3000"}