const express = require('express');
const router = express.Router();
const { body, query, validationResult } = require('express-validator');
const { authenticateWallet } = require('../middleware/walletAuth');
const User = require('../models/User');
const logger = require('../utils/logger');

/**
 * GET /api/notifications
 * Get user notifications with filtering and pagination
 */
router.get('/', [
  authenticateWallet,
  query('filter').optional().isIn(['all', 'unread', 'trade', 'system']),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 })
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { filter = 'all', limit = 20, offset = 0 } = req.query;
    const userId = req.user._id;

    // Mock notifications for now - in production this would come from database
    const mockNotifications = [
      {
        id: '1',
        type: 'trade_update',
        title: 'Trade Update',
        message: 'Your trade request has been accepted',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        isRead: false,
        priority: 'normal',
        data: { tradeId: 'trade_123' }
      },
      {
        id: '2',
        type: 'payment',
        title: 'Payment Received',
        message: 'Payment confirmed for trade #456',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        isRead: true,
        priority: 'normal',
        data: { tradeId: 'trade_456' }
      },
      {
        id: '3',
        type: 'system',
        title: 'System Maintenance',
        message: 'Scheduled maintenance tonight at 2 AM',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
        isRead: false,
        priority: 'low',
        data: {}
      },
      {
        id: '4',
        type: 'security',
        title: 'Security Alert',
        message: 'New device login detected',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        isRead: true,
        priority: 'urgent',
        data: {}
      }
    ];

    // Apply filters
    let filteredNotifications = mockNotifications;
    
    if (filter === 'unread') {
      filteredNotifications = mockNotifications.filter(n => !n.isRead);
    } else if (filter === 'trade') {
      filteredNotifications = mockNotifications.filter(n => n.type.includes('trade') || n.type === 'payment');
    } else if (filter === 'system') {
      filteredNotifications = mockNotifications.filter(n => n.type === 'system' || n.type === 'security');
    }

    // Apply pagination
    const paginatedNotifications = filteredNotifications.slice(offset, offset + limit);

    res.json({
      success: true,
      data: {
        notifications: paginatedNotifications,
        pagination: {
          total: filteredNotifications.length,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: offset + limit < filteredNotifications.length
        }
      }
    });

  } catch (error) {
    logger.error('Failed to get notifications:', error);
    next(error);
  }
});

/**
 * PUT /api/notifications/:id/read
 * Mark a notification as read
 */
router.put('/:id/read', [
  authenticateWallet
], async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    // In production, this would update the notification in the database
    logger.info(`Marking notification ${id} as read for user ${userId}`);

    res.json({
      success: true,
      message: 'Notification marked as read'
    });

  } catch (error) {
    logger.error('Failed to mark notification as read:', error);
    next(error);
  }
});

/**
 * PUT /api/notifications/mark-all-read
 * Mark all notifications as read
 */
router.put('/mark-all-read', [
  authenticateWallet
], async (req, res, next) => {
  try {
    const userId = req.user._id;

    // In production, this would update all notifications in the database
    logger.info(`Marking all notifications as read for user ${userId}`);

    res.json({
      success: true,
      message: 'All notifications marked as read'
    });

  } catch (error) {
    logger.error('Failed to mark all notifications as read:', error);
    next(error);
  }
});

/**
 * DELETE /api/notifications/:id
 * Delete a notification
 */
router.delete('/:id', [
  authenticateWallet
], async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    // In production, this would delete the notification from the database
    logger.info(`Deleting notification ${id} for user ${userId}`);

    res.json({
      success: true,
      message: 'Notification deleted'
    });

  } catch (error) {
    logger.error('Failed to delete notification:', error);
    next(error);
  }
});

/**
 * GET /api/notifications/unread-count
 * Get unread notification count
 */
router.get('/unread-count', [
  authenticateWallet
], async (req, res, next) => {
  try {
    const userId = req.user._id;

    // Mock unread count - in production this would come from database
    const unreadCount = 2;

    res.json({
      success: true,
      data: {
        unreadCount
      }
    });

  } catch (error) {
    logger.error('Failed to get unread count:', error);
    next(error);
  }
});

module.exports = router;
