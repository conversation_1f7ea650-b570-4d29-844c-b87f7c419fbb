version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6
    container_name: kryptopesa-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: kryptopesa
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./backend/scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - kryptopesa-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: kryptopesa-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - kryptopesa-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kryptopesa-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      MONGODB_URI: *********************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      POLYGON_RPC_URL: ${POLYGON_RPC_URL}
      POLYGON_PRIVATE_KEY: ${POLYGON_PRIVATE_KEY}
      ESCROW_CONTRACT_ADDRESS: ${ESCROW_CONTRACT_ADDRESS}
      USDT_CONTRACT_ADDRESS: ${USDT_CONTRACT_ADDRESS}
      USDC_CONTRACT_ADDRESS: ${USDC_CONTRACT_ADDRESS}
    ports:
      - "3000:3000"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend/logs:/app/logs
    networks:
      - kryptopesa-network

  # Admin Dashboard
  admin-dashboard:
    build:
      context: ./admin-dashboard
      dockerfile: Dockerfile
    container_name: kryptopesa-admin
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:3000/api
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - kryptopesa-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: kryptopesa-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deployment/nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - admin-dashboard
    networks:
      - kryptopesa-network

  # Blockchain Node (Optional - for development)
  hardhat-node:
    build:
      context: ./smart-contracts
      dockerfile: Dockerfile.node
    container_name: kryptopesa-blockchain
    restart: unless-stopped
    ports:
      - "8545:8545"
    networks:
      - kryptopesa-network
    profiles:
      - development

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  kryptopesa-network:
    driver: bridge
