const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { body, param, query, validationResult } = require('express-validator');
const WalletUser = require('../models/WalletUser');
const AuditLog = require('../models/AuditLog');
const logger = require('../utils/logger');
const { authenticateWallet } = require('../middleware/walletAuth');
const { AppError } = require('../middleware/errorHandler');
const notificationService = require('../services/notificationService');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/kyc');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `${req.user.walletAddress}-${file.fieldname}-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and PDF files are allowed.'));
    }
  }
});

// Validation middleware
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

/**
 * GET /api/kyc/status
 * Get current KYC status and information
 */
router.get('/status', authenticateWallet, async (req, res) => {
  try {
    const user = await WalletUser.findOne({ walletAddress: req.user.walletAddress });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate current usage (simplified - in production, this would query actual trades)
    const currentUsage = {
      dailyUsed: 0,
      monthlyUsed: 0
    };

    const response = {
      verification: {
        level: user.verification.level,
        status: user.verification.status,
        documents: user.verification.documents.map(doc => ({
          type: doc.type,
          status: doc.status,
          uploadedAt: doc.uploadedAt?.toISOString(),
          reviewedAt: doc.reviewedAt?.toISOString(),
          rejectionReason: doc.rejectionReason
        })),
        tradingLimits: {
          daily: user.verification.tradingLimits.daily,
          monthly: user.verification.tradingLimits.monthly,
          current: currentUsage
        }
      },
      nextSteps: getNextSteps(user.verification),
      requirements: getRequirements(user.verification.level)
    };

    res.json({
      success: true,
      data: response
    });

  } catch (error) {
    logger.error('Failed to get KYC status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get KYC status'
    });
  }
});

/**
 * GET /api/kyc/requirements
 * Get KYC requirements for all levels
 */
router.get('/requirements', async (req, res) => {
  try {
    const requirements = [
      {
        level: 1,
        documents: [
          {
            type: 'national_id',
            name: 'National ID',
            description: 'Government-issued national identification card',
            required: true,
            examples: ['National ID Card', 'Voter ID', 'Citizen ID']
          },
          {
            type: 'selfie',
            name: 'Selfie Verification',
            description: 'Clear selfie photo holding your ID document',
            required: true,
            examples: ['Selfie with ID', 'Live photo verification']
          }
        ],
        benefits: ['Basic trading access', 'Secure account verification'],
        tradingLimits: {
          daily: 1000,
          monthly: 10000
        }
      },
      {
        level: 2,
        documents: [
          {
            type: 'proof_of_address',
            name: 'Proof of Address',
            description: 'Recent utility bill or bank statement',
            required: true,
            examples: ['Utility Bill', 'Bank Statement', 'Rental Agreement']
          }
        ],
        benefits: ['Increased trading limits', 'Priority support'],
        tradingLimits: {
          daily: 10000,
          monthly: 100000
        }
      },
      {
        level: 3,
        documents: [
          {
            type: 'enhanced_verification',
            name: 'Enhanced Verification',
            description: 'Additional verification for high-value trading',
            required: true,
            examples: ['Video Call Verification', 'Additional Documentation']
          }
        ],
        benefits: ['Unlimited trading', 'VIP support', 'Advanced features'],
        tradingLimits: {
          daily: -1, // Unlimited
          monthly: -1 // Unlimited
        }
      }
    ];

    res.json({
      success: true,
      data: { requirements }
    });

  } catch (error) {
    logger.error('Failed to get KYC requirements:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get KYC requirements'
    });
  }
});

/**
 * POST /api/kyc/documents
 * Upload KYC document
 */
router.post('/documents',
  authenticateWallet,
  upload.single('file'),
  [
    body('type').isIn(['national_id', 'passport', 'driving_license', 'proof_of_address']).withMessage('Invalid document type'),
    body('description').optional().isString().isLength({ max: 500 }).withMessage('Description too long'),
    validateRequest
  ],
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
      }

      const { type, description } = req.body;
      const user = await WalletUser.findOne({ walletAddress: req.user.walletAddress });
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Remove existing document of same type
      user.verification.documents = user.verification.documents.filter(doc => doc.type !== type);

      // Add new document
      const newDocument = {
        type,
        status: 'pending',
        uploadedAt: new Date(),
        filePath: req.file.path,
        fileName: req.file.filename,
        description
      };

      user.verification.documents.push(newDocument);
      await user.save();

      // Log the action
      await AuditLog.create({
        userId: user._id,
        action: 'kyc_document_upload',
        resource: 'kyc',
        resourceId: user._id,
        details: {
          documentType: type,
          fileName: req.file.filename
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });

      res.json({
        success: true,
        data: {
          document: {
            id: newDocument._id,
            type: newDocument.type,
            status: newDocument.status,
            uploadedAt: newDocument.uploadedAt.toISOString()
          }
        }
      });

    } catch (error) {
      logger.error('Failed to upload KYC document:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to upload document'
      });
    }
  }
);

/**
 * POST /api/kyc/submit
 * Submit KYC application for review
 */
router.post('/submit',
  authenticateWallet,
  async (req, res) => {
    try {
      const user = await WalletUser.findOne({ walletAddress: req.user.walletAddress });
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check if user has required documents for their current level + 1
      const nextLevel = user.verification.level + 1;
      const requiredDocs = getRequiredDocumentsForLevel(nextLevel);
      
      const uploadedDocs = user.verification.documents.map(doc => doc.type);
      const missingDocs = requiredDocs.filter(doc => !uploadedDocs.includes(doc));

      if (missingDocs.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Missing required documents',
          data: { missingDocuments: missingDocs }
        });
      }

      // Update status to pending
      user.verification.status = 'pending';
      user.verification.submittedAt = new Date();
      await user.save();

      // Log the action
      await AuditLog.create({
        userId: user._id,
        action: 'kyc_submit',
        resource: 'kyc',
        resourceId: user._id,
        details: {
          level: nextLevel,
          documentsCount: user.verification.documents.length
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Send notification
      await notificationService.sendSystemNotification(user._id, 'kyc_submitted', {
        level: nextLevel
      });

      res.json({
        success: true,
        data: {
          application: {
            id: user._id,
            status: 'pending',
            submittedAt: user.verification.submittedAt.toISOString(),
            estimatedReviewTime: '2-3 business days'
          }
        }
      });

    } catch (error) {
      logger.error('Failed to submit KYC application:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to submit KYC application'
      });
    }
  }
);

// Helper functions
function getNextSteps(verification) {
  const steps = [];
  
  if (verification.level === 0) {
    steps.push('Upload government-issued ID');
    steps.push('Take selfie verification photo');
  } else if (verification.level === 1) {
    steps.push('Upload proof of address document');
  } else if (verification.level === 2) {
    steps.push('Complete enhanced verification');
  }
  
  if (verification.status === 'rejected') {
    steps.unshift('Review rejection feedback and resubmit documents');
  }
  
  return steps;
}

function getRequirements(level) {
  const requirements = [];
  
  if (level === 0) {
    requirements.push(
      { type: 'national_id', description: 'Government-issued ID', required: true, completed: false },
      { type: 'selfie', description: 'Selfie with ID', required: true, completed: false }
    );
  } else if (level === 1) {
    requirements.push(
      { type: 'proof_of_address', description: 'Proof of address', required: true, completed: false }
    );
  }
  
  return requirements;
}

function getRequiredDocumentsForLevel(level) {
  switch (level) {
    case 1:
      return ['national_id'];
    case 2:
      return ['proof_of_address'];
    case 3:
      return ['enhanced_verification'];
    default:
      return [];
  }
}

module.exports = router;
