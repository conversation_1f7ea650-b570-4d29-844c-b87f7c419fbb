import React, { useState } from 'react';
import {
const logger = require('./utils/logger');

  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';

const DisputesPage = () => {
  const [selectedDispute, setSelectedDispute] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [resolution, setResolution] = useState('');
  const [reasoning, setReasoning] = useState('');

  // Mock data - would come from API
  const disputes = [
    {
      id: 'DSP-001',
      tradeId: 'TRD-001',
      initiator: 'john_doe',
      respondent: 'jane_smith',
      category: 'payment_not_received',
      status: 'open',
      priority: 'high',
      createdAt: '2023-07-15T10:30:00Z',
      amount: '500 USDT',
    },
    {
      id: 'DSP-002',
      tradeId: 'TRD-002',
      initiator: 'alice_trader',
      respondent: 'bob_crypto',
      category: 'wrong_amount',
      status: 'under_review',
      priority: 'medium',
      createdAt: '2023-07-14T15:45:00Z',
      amount: '1000 USDT',
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'error';
      case 'under_review': return 'warning';
      case 'resolved': return 'success';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  const handleResolveDispute = (dispute) => {
    setSelectedDispute(dispute);
    setDialogOpen(true);
  };

  const handleSubmitResolution = () => {
    // API call to resolve dispute
    // logger.info('Resolving dispute:', {
      disputeId: selectedDispute.id,
      resolution,
      reasoning,
    });
    
    setDialogOpen(false);
    setSelectedDispute(null);
    setResolution('');
    setReasoning('');
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dispute Management
      </Typography>

      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Dispute ID</TableCell>
                <TableCell>Trade ID</TableCell>
                <TableCell>Initiator</TableCell>
                <TableCell>Respondent</TableCell>
                <TableCell>Category</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {disputes.map((dispute) => (
                <TableRow key={dispute.id}>
                  <TableCell>{dispute.id}</TableCell>
                  <TableCell>{dispute.tradeId}</TableCell>
                  <TableCell>{dispute.initiator}</TableCell>
                  <TableCell>{dispute.respondent}</TableCell>
                  <TableCell>{dispute.category.replace('_', ' ')}</TableCell>
                  <TableCell>{dispute.amount}</TableCell>
                  <TableCell>
                    <Chip 
                      label={dispute.status} 
                      color={getStatusColor(dispute.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={dispute.priority} 
                      color={getPriorityColor(dispute.priority)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(dispute.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => handleResolveDispute(dispute)}
                      disabled={dispute.status === 'resolved'}
                    >
                      Resolve
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Resolution Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Resolve Dispute {selectedDispute?.id}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Resolution Decision</InputLabel>
              <Select
                value={resolution}
                onChange={(e) => setResolution(e.target.value)}
                label="Resolution Decision"
              >
                <MenuItem value="favor_initiator">Favor Initiator</MenuItem>
                <MenuItem value="favor_respondent">Favor Respondent</MenuItem>
                <MenuItem value="partial_refund">Partial Refund</MenuItem>
                <MenuItem value="no_action">No Action</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Reasoning"
              value={reasoning}
              onChange={(e) => setReasoning(e.target.value)}
              margin="normal"
              placeholder="Explain the reasoning for this resolution..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleSubmitResolution}
            variant="contained"
            disabled={!resolution || !reasoning}
          >
            Submit Resolution
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DisputesPage;
