# KryptoPesa Production Deployment Guide

## 🎉 100% Complete - Ready for Production!

This guide covers the complete deployment process for the KryptoPesa P2P crypto trading platform.

## Prerequisites

### System Requirements
- **Server**: Ubuntu 20.04+ or CentOS 8+ (minimum 4GB RAM, 2 CPU cores)
- **Node.js**: Version 18+ 
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Domain**: SSL certificate for HTTPS

### Required Services
- **MongoDB Atlas** or self-hosted MongoDB cluster
- **Redis Cloud** or self-hosted Redis instance
- **Firebase Project** for push notifications
- **Ethereum/Polygon RPC** endpoint (Alchemy, Infura, or QuickNode)

## Phase 1: Infrastructure Setup

### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Clone repository
git clone https://github.com/your-org/kryptopesa.git
cd kryptopesa
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

**Required Environment Variables:**
```env
# Application
NODE_ENV=production
PORT=3000
FRONTEND_URL=https://app.kryptopesa.com
ADMIN_URL=https://admin.kryptopesa.com

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/kryptopesa
REDIS_URL=redis://username:password@redis-host:6379

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-256-bits
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Blockchain
ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/your-api-key
POLYGON_RPC_URL=https://polygon-mainnet.alchemyapi.io/v2/your-api-key
PRIVATE_KEY=your-deployment-wallet-private-key
ESCROW_CONTRACT_ADDRESS=0x...

# Firebase
FIREBASE_PROJECT_ID=your-firebase-project
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Security
CORS_ORIGIN=https://app.kryptopesa.com,https://admin.kryptopesa.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring
LOG_LEVEL=info
SENTRY_DSN=https://<EMAIL>/project-id
```

## Phase 2: Smart Contract Deployment

### 1. Deploy to Mainnet
```bash
cd smart-contracts

# Install dependencies
npm install

# Deploy to Polygon mainnet
npx hardhat run scripts/deploy.js --network polygon

# Verify contract
npx hardhat verify --network polygon DEPLOYED_CONTRACT_ADDRESS

# Update .env with contract address
echo "ESCROW_CONTRACT_ADDRESS=0x..." >> ../.env
```

### 2. Contract Verification
```bash
# Run contract tests on mainnet fork
npx hardhat test --network hardhat

# Verify deployment
npx hardhat run scripts/verify-deployment.js --network polygon
```

## Phase 3: Backend Deployment

### 1. Production Build
```bash
cd backend

# Install production dependencies
npm ci --only=production

# Run database migrations
npm run migrate

# Start with PM2
npm install -g pm2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### 2. Database Setup
```bash
# Create database indexes
node scripts/create-indexes.js

# Seed initial data
node scripts/seed-production.js

# Verify database connection
node scripts/health-check.js
```

## Phase 4: Mobile App Deployment

### 1. Android Build
```bash
cd mobile

# Install dependencies
npm install

# Create production build
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle

# Build APK
cd android
./gradlew assembleRelease

# Build AAB for Play Store
./gradlew bundleRelease
```

### 2. iOS Build
```bash
cd mobile

# Install dependencies
npm install
cd ios && pod install && cd ..

# Create production build
npx react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle

# Build for App Store (requires Xcode)
# Open ios/KryptoPesa.xcworkspace in Xcode
# Archive and upload to App Store Connect
```

## Phase 5: Admin Dashboard Deployment

### 1. Production Build
```bash
cd admin-dashboard

# Install dependencies
npm ci --only=production

# Create production build
npm run build

# Serve with nginx or deploy to CDN
```

### 2. Nginx Configuration
```nginx
server {
    listen 443 ssl;
    server_name admin.kryptopesa.com;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    root /var/www/kryptopesa-admin/build;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Phase 6: Monitoring & Security

### 1. SSL/TLS Setup
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificates
sudo certbot --nginx -d api.kryptopesa.com -d admin.kryptopesa.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. Monitoring Setup
```bash
# Install monitoring tools
npm install -g pm2-logrotate
pm2 install pm2-server-monit

# Configure log rotation
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30

# Set up health checks
pm2 install pm2-auto-pull
```

### 3. Security Hardening
```bash
# Firewall configuration
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Fail2ban for SSH protection
sudo apt install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Regular security updates
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

## Phase 7: Testing & Validation

### 1. End-to-End Testing
```bash
# Run integration tests
npm run test:integration

# Run system tests
./scripts/test-system-integration.sh

# Load testing
npm install -g artillery
artillery run tests/load-test.yml
```

### 2. Production Validation
```bash
# Health check endpoints
curl https://api.kryptopesa.com/health
curl https://admin.kryptopesa.com/health

# WebSocket connection test
node scripts/test-websocket.js

# Database connectivity
node scripts/test-database.js
```

## Phase 8: Go-Live Checklist

### Pre-Launch
- [ ] All environment variables configured
- [ ] SSL certificates installed and valid
- [ ] Smart contracts deployed and verified
- [ ] Database migrations completed
- [ ] Monitoring and logging active
- [ ] Backup systems configured
- [ ] Security hardening applied
- [ ] Load testing completed
- [ ] Mobile apps submitted to stores

### Launch Day
- [ ] Final system health check
- [ ] Monitor error rates and performance
- [ ] Customer support team ready
- [ ] Marketing campaigns activated
- [ ] Social media announcements
- [ ] Press release distributed

### Post-Launch
- [ ] Monitor user feedback
- [ ] Track key metrics (trades, users, volume)
- [ ] Regular security audits
- [ ] Performance optimization
- [ ] Feature updates based on usage

## Maintenance & Updates

### Regular Tasks
- **Daily**: Monitor logs, check system health
- **Weekly**: Review performance metrics, update dependencies
- **Monthly**: Security audit, backup verification
- **Quarterly**: Smart contract audit, penetration testing

### Update Procedure
1. Test updates in staging environment
2. Schedule maintenance window
3. Deploy backend updates with zero downtime
4. Update mobile apps through app stores
5. Monitor for issues and rollback if necessary

## Support & Troubleshooting

### Common Issues
- **High CPU usage**: Scale horizontally with load balancer
- **Database connection issues**: Check MongoDB Atlas connection limits
- **WebSocket disconnections**: Verify Redis configuration
- **Mobile app crashes**: Check crash reporting in Firebase

### Emergency Contacts
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **Support Team**: <EMAIL>

## Success Metrics

### Key Performance Indicators
- **Uptime**: 99.9% target
- **Response Time**: <200ms API responses
- **User Growth**: Track daily/monthly active users
- **Trade Volume**: Monitor cryptocurrency trading volume
- **Customer Satisfaction**: NPS score >8

---

**🎉 Congratulations! KryptoPesa is now production-ready and fully deployed!**

For additional support, refer to the technical documentation in the `/docs` directory or contact the development team.
