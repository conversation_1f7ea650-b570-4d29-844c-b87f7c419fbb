"use strict";(self.webpackChunkkryptopesa_admin_dashboard=self.webpackChunkkryptopesa_admin_dashboard||[]).push([[220],{4220:(e,i,s)=>{s.r(i),s.d(i,{default:()=>O});var r=s(2555),n=s(5043),l=s(6446),a=s(5865),t=s(4194),d=s(3336),o=s(8903),c=s(5795),x=s(1787),u=s(3193),A=s(9190),h=s(648),j=s(2143),v=s(1906),m=s(9650),y=s(1806),f=s(4882),p=s(8076),b=s(39),g=s(3460),S=s(1045),C=s(3845),w=s(8483),k=s(7392),z=s(1574),U=s(35),D=s(6600),I=s(5316),P=s(2110),V=s(6494),W=s(9347),_=s(794),B=s(9484),L=s(4190),R=s(9893),E=s(8262),N=s(9974),T=s(9320),F=s(3632),q=s(751),H=s(8606),J=s(9722),M=s(579);const O=()=>{var e,i,s,O,G,K,Q,X,Y,Z,$,ee,ie,se,re,ne,le,ae,te,de;const[oe,ce]=(0,n.useState)([]),[xe,ue]=(0,n.useState)(!0),[Ae,he]=(0,n.useState)(null),[je,ve]=(0,n.useState)(0),[me,ye]=(0,n.useState)(25),[fe,pe]=(0,n.useState)(0),[be,ge]=(0,n.useState)(""),[Se,Ce]=(0,n.useState)(""),[we,ke]=(0,n.useState)(""),[ze,Ue]=(0,n.useState)(""),[De,Ie]=(0,n.useState)(null),[Pe,Ve]=(0,n.useState)(!1),[We,_e]=(0,n.useState)(!1),[Be,Le]=(0,n.useState)(""),[Re,Ee]=(0,n.useState)(""),[Ne,Te]=(0,n.useState)({open:!1,message:"",severity:"success"}),Fe=async()=>{try{ue(!0);const e={page:je+1,limit:me,search:be||void 0,status:Se||void 0,role:we||void 0,verified:ze||void 0,sortBy:"createdAt",sortOrder:"desc"},i=await J.A.get("/admin/users",{params:e});i.data.success&&(ce(i.data.data.users),pe(i.data.data.pagination.totalUsers))}catch(Ae){console.error("Error fetching users:",Ae),he("Failed to fetch users")}finally{ue(!1)}};(0,n.useEffect)(()=>{Fe()},[je,me,be,Se,we,ze]);const qe=(e,i)=>{switch(e){case"status":Ce(i);break;case"role":ke(i);break;case"verified":Ue(i)}ve(0)},He=async(e,i)=>{Ie(e),Le(i),_e(!0)},Je=e=>{switch(e){case"active":return"success";case"suspended":return"warning";case"banned":return"error";default:return"default"}},Me=e=>{switch(e){case"admin":return"error";case"moderator":return"warning";case"user":return"primary";default:return"default"}};return xe&&0===oe.length?(0,M.jsx)(l.A,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",children:(0,M.jsx)(a.A,{children:"Loading users..."})}):(0,M.jsxs)(l.A,{children:[(0,M.jsx)(a.A,{variant:"h4",gutterBottom:!0,children:"User Management"}),Ae&&(0,M.jsx)(t.A,{severity:"error",sx:{mb:2},children:Ae}),(0,M.jsx)(d.A,{sx:{p:2,mb:2},children:(0,M.jsxs)(o.Ay,{container:!0,spacing:2,alignItems:"center",children:[(0,M.jsx)(o.Ay,{item:!0,xs:12,md:4,children:(0,M.jsx)(c.A,{fullWidth:!0,placeholder:"Search users...",value:be,onChange:e=>{ge(e.target.value),ve(0)},InputProps:{startAdornment:(0,M.jsx)(x.A,{position:"start",children:(0,M.jsx)(B.A,{})})}})}),(0,M.jsx)(o.Ay,{item:!0,xs:12,md:2,children:(0,M.jsxs)(u.A,{fullWidth:!0,children:[(0,M.jsx)(A.A,{children:"Status"}),(0,M.jsxs)(h.A,{value:Se,label:"Status",onChange:e=>qe("status",e.target.value),children:[(0,M.jsx)(j.A,{value:"",children:"All"}),(0,M.jsx)(j.A,{value:"active",children:"Active"}),(0,M.jsx)(j.A,{value:"suspended",children:"Suspended"}),(0,M.jsx)(j.A,{value:"banned",children:"Banned"}),(0,M.jsx)(j.A,{value:"pending",children:"Pending"})]})]})}),(0,M.jsx)(o.Ay,{item:!0,xs:12,md:2,children:(0,M.jsxs)(u.A,{fullWidth:!0,children:[(0,M.jsx)(A.A,{children:"Role"}),(0,M.jsxs)(h.A,{value:we,label:"Role",onChange:e=>qe("role",e.target.value),children:[(0,M.jsx)(j.A,{value:"",children:"All"}),(0,M.jsx)(j.A,{value:"user",children:"User"}),(0,M.jsx)(j.A,{value:"moderator",children:"Moderator"}),(0,M.jsx)(j.A,{value:"admin",children:"Admin"})]})]})}),(0,M.jsx)(o.Ay,{item:!0,xs:12,md:2,children:(0,M.jsxs)(u.A,{fullWidth:!0,children:[(0,M.jsx)(A.A,{children:"Verified"}),(0,M.jsxs)(h.A,{value:ze,label:"Verified",onChange:e=>qe("verified",e.target.value),children:[(0,M.jsx)(j.A,{value:"",children:"All"}),(0,M.jsx)(j.A,{value:"true",children:"Verified"}),(0,M.jsx)(j.A,{value:"false",children:"Unverified"})]})]})}),(0,M.jsx)(o.Ay,{item:!0,xs:12,md:2,children:(0,M.jsx)(v.A,{fullWidth:!0,variant:"outlined",startIcon:(0,M.jsx)(L.A,{}),onClick:()=>{ge(""),Ce(""),ke(""),Ue(""),ve(0)},children:"Clear Filters"})})]})}),(0,M.jsxs)(m.A,{component:d.A,children:[(0,M.jsxs)(y.A,{children:[(0,M.jsx)(f.A,{children:(0,M.jsxs)(p.A,{children:[(0,M.jsx)(b.A,{children:"User"}),(0,M.jsx)(b.A,{children:"Contact"}),(0,M.jsx)(b.A,{children:"Status"}),(0,M.jsx)(b.A,{children:"Role"}),(0,M.jsx)(b.A,{children:"Reputation"}),(0,M.jsx)(b.A,{children:"Verified"}),(0,M.jsx)(b.A,{children:"Joined"}),(0,M.jsx)(b.A,{children:"Actions"})]})}),(0,M.jsx)(g.A,{children:oe.map(e=>{var i,s,r,n,t,d,o,c,x,u,A,h,j,v,m;return(0,M.jsxs)(p.A,{hover:!0,children:[(0,M.jsx)(b.A,{children:(0,M.jsxs)(l.A,{display:"flex",alignItems:"center",gap:2,children:[(0,M.jsx)(S.A,{children:null!==(i=e.profile)&&void 0!==i&&i.avatar?(0,M.jsx)("img",{src:e.profile.avatar,alt:e.username}):(0,M.jsx)(R.A,{})}),(0,M.jsxs)(l.A,{children:[(0,M.jsxs)(a.A,{variant:"subtitle2",children:[null===(s=e.profile)||void 0===s?void 0:s.firstName," ",null===(r=e.profile)||void 0===r?void 0:r.lastName]}),(0,M.jsxs)(a.A,{variant:"body2",color:"textSecondary",children:["@",e.username]})]})]})}),(0,M.jsx)(b.A,{children:(0,M.jsxs)(l.A,{children:[(0,M.jsxs)(l.A,{display:"flex",alignItems:"center",gap:1,mb:.5,children:[(0,M.jsx)(E.A,{fontSize:"small",color:"action"}),(0,M.jsx)(a.A,{variant:"body2",children:e.email})]}),(0,M.jsxs)(l.A,{display:"flex",alignItems:"center",gap:1,children:[(0,M.jsx)(N.A,{fontSize:"small",color:"action"}),(0,M.jsx)(a.A,{variant:"body2",children:e.phone})]})]})}),(0,M.jsx)(b.A,{children:(0,M.jsx)(C.A,{label:e.status,color:Je(e.status),size:"small"})}),(0,M.jsx)(b.A,{children:(0,M.jsx)(C.A,{label:e.role,color:Me(e.role),size:"small"})}),(0,M.jsxs)(b.A,{children:[(0,M.jsxs)(l.A,{display:"flex",alignItems:"center",gap:1,children:[(0,M.jsx)(T.A,{fontSize:"small",color:"warning"}),(0,M.jsxs)(a.A,{variant:"body2",children:[(null===(n=e.reputation)||void 0===n?void 0:n.score)||0,"/100"]})]}),(0,M.jsxs)(a.A,{variant:"caption",color:"textSecondary",children:[(null===(t=e.reputation)||void 0===t?void 0:t.totalTrades)||0," trades"]})]}),(0,M.jsx)(b.A,{children:(0,M.jsxs)(l.A,{children:[(0,M.jsx)(C.A,{label:null!==(d=e.verification)&&void 0!==d&&null!==(o=d.identity)&&void 0!==o&&o.verified?"ID Verified":"Unverified",color:null!==(c=e.verification)&&void 0!==c&&null!==(x=c.identity)&&void 0!==x&&x.verified?"success":"default",size:"small",sx:{mb:.5}}),(null===(u=e.verification)||void 0===u||null===(A=u.email)||void 0===A?void 0:A.verified)&&(0,M.jsx)(C.A,{label:"Email",color:"info",size:"small",sx:{mr:.5}}),(null===(h=e.verification)||void 0===h||null===(j=h.phone)||void 0===j?void 0:j.verified)&&(0,M.jsx)(C.A,{label:"Phone",color:"info",size:"small"})]})}),(0,M.jsxs)(b.A,{children:[(0,M.jsx)(a.A,{variant:"body2",children:new Date(e.createdAt).toLocaleDateString()}),(0,M.jsxs)(a.A,{variant:"caption",color:"textSecondary",children:["Last active: ",new Date(e.lastActive).toLocaleDateString()]})]}),(0,M.jsx)(b.A,{children:(0,M.jsxs)(l.A,{display:"flex",gap:1,children:[(0,M.jsx)(w.A,{title:"View Details",children:(0,M.jsx)(k.A,{size:"small",onClick:()=>{Ie(e),Ve(!0)},children:(0,M.jsx)(F.A,{})})}),"active"===e.status&&(0,M.jsx)(w.A,{title:"Suspend User",children:(0,M.jsx)(k.A,{size:"small",color:"warning",onClick:()=>He(e,"suspend"),children:(0,M.jsx)(q.A,{})})}),"banned"!==e.status&&(0,M.jsx)(w.A,{title:"Ban User",children:(0,M.jsx)(k.A,{size:"small",color:"error",onClick:()=>He(e,"ban"),children:(0,M.jsx)(q.A,{})})}),["suspended","banned"].includes(e.status)&&(0,M.jsx)(w.A,{title:"Activate User",children:(0,M.jsx)(k.A,{size:"small",color:"success",onClick:()=>He(e,"activate"),children:(0,M.jsx)(H.A,{})})}),!(null!==(v=e.verification)&&void 0!==v&&null!==(m=v.identity)&&void 0!==m&&m.verified)&&(0,M.jsx)(w.A,{title:"Verify Identity",children:(0,M.jsx)(k.A,{size:"small",color:"primary",onClick:()=>He(e,"verify"),children:(0,M.jsx)(H.A,{})})})]})})]},e._id)})})]}),(0,M.jsx)(z.A,{rowsPerPageOptions:[10,25,50,100],component:"div",count:fe,rowsPerPage:me,page:je,onPageChange:(e,i)=>{ve(i)},onRowsPerPageChange:e=>{ye(parseInt(e.target.value,10)),ve(0)}})]}),(0,M.jsxs)(U.A,{open:Pe,onClose:()=>Ve(!1),maxWidth:"md",fullWidth:!0,children:[(0,M.jsxs)(D.A,{children:["User Details: ",null===De||void 0===De?void 0:De.username]}),(0,M.jsx)(I.A,{children:De&&(0,M.jsxs)(o.Ay,{container:!0,spacing:3,children:[(0,M.jsx)(o.Ay,{item:!0,xs:12,md:6,children:(0,M.jsx)(P.A,{children:(0,M.jsxs)(V.A,{children:[(0,M.jsx)(a.A,{variant:"h6",gutterBottom:!0,children:"Profile Information"}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Name"}),(0,M.jsxs)(a.A,{variant:"body1",children:[null===(e=De.profile)||void 0===e?void 0:e.firstName," ",null===(i=De.profile)||void 0===i?void 0:i.lastName]})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Username"}),(0,M.jsxs)(a.A,{variant:"body1",children:["@",De.username]})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Email"}),(0,M.jsx)(a.A,{variant:"body1",children:De.email})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Phone"}),(0,M.jsx)(a.A,{variant:"body1",children:De.phone})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Location"}),(0,M.jsxs)(a.A,{variant:"body1",children:[null===(s=De.profile)||void 0===s||null===(O=s.location)||void 0===O?void 0:O.city,", ",null===(G=De.profile)||void 0===G||null===(K=G.location)||void 0===K?void 0:K.country]})]})]})})}),(0,M.jsx)(o.Ay,{item:!0,xs:12,md:6,children:(0,M.jsx)(P.A,{children:(0,M.jsxs)(V.A,{children:[(0,M.jsx)(a.A,{variant:"h6",gutterBottom:!0,children:"Account Status"}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Status"}),(0,M.jsx)(C.A,{label:De.status,color:Je(De.status),size:"small"})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Role"}),(0,M.jsx)(C.A,{label:De.role,color:Me(De.role),size:"small"})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Reputation Score"}),(0,M.jsxs)(a.A,{variant:"body1",children:[(null===(Q=De.reputation)||void 0===Q?void 0:Q.score)||0,"/100"]})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Total Trades"}),(0,M.jsx)(a.A,{variant:"body1",children:(null===(X=De.reputation)||void 0===X?void 0:X.totalTrades)||0})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Joined Date"}),(0,M.jsx)(a.A,{variant:"body1",children:new Date(De.createdAt).toLocaleDateString()})]}),(0,M.jsxs)(l.A,{mb:2,children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Last Active"}),(0,M.jsx)(a.A,{variant:"body1",children:new Date(De.lastActive).toLocaleDateString()})]})]})})}),(0,M.jsx)(o.Ay,{item:!0,xs:12,children:(0,M.jsx)(P.A,{children:(0,M.jsxs)(V.A,{children:[(0,M.jsx)(a.A,{variant:"h6",gutterBottom:!0,children:"Verification Status"}),(0,M.jsxs)(o.Ay,{container:!0,spacing:2,children:[(0,M.jsx)(o.Ay,{item:!0,xs:4,children:(0,M.jsxs)(l.A,{textAlign:"center",children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Email"}),(0,M.jsx)(C.A,{label:null!==(Y=De.verification)&&void 0!==Y&&null!==(Z=Y.email)&&void 0!==Z&&Z.verified?"Verified":"Unverified",color:null!==($=De.verification)&&void 0!==$&&null!==(ee=$.email)&&void 0!==ee&&ee.verified?"success":"default",size:"small"})]})}),(0,M.jsx)(o.Ay,{item:!0,xs:4,children:(0,M.jsxs)(l.A,{textAlign:"center",children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Phone"}),(0,M.jsx)(C.A,{label:null!==(ie=De.verification)&&void 0!==ie&&null!==(se=ie.phone)&&void 0!==se&&se.verified?"Verified":"Unverified",color:null!==(re=De.verification)&&void 0!==re&&null!==(ne=re.phone)&&void 0!==ne&&ne.verified?"success":"default",size:"small"})]})}),(0,M.jsx)(o.Ay,{item:!0,xs:4,children:(0,M.jsxs)(l.A,{textAlign:"center",children:[(0,M.jsx)(a.A,{variant:"body2",color:"textSecondary",children:"Identity"}),(0,M.jsx)(C.A,{label:null!==(le=De.verification)&&void 0!==le&&null!==(ae=le.identity)&&void 0!==ae&&ae.verified?"Verified":"Unverified",color:null!==(te=De.verification)&&void 0!==te&&null!==(de=te.identity)&&void 0!==de&&de.verified?"success":"default",size:"small"})]})})]})]})})})]})}),(0,M.jsx)(W.A,{children:(0,M.jsx)(v.A,{onClick:()=>Ve(!1),children:"Close"})})]}),(0,M.jsxs)(U.A,{open:We,onClose:()=>_e(!1),maxWidth:"sm",fullWidth:!0,children:[(0,M.jsxs)(D.A,{children:["Confirm ",Be," User"]}),(0,M.jsxs)(I.A,{children:[(0,M.jsxs)(a.A,{gutterBottom:!0,children:["Are you sure you want to ",Be,' user "',null===De||void 0===De?void 0:De.username,'"?']}),(0,M.jsx)(c.A,{fullWidth:!0,multiline:!0,rows:3,label:"Reason (required)",value:Re,onChange:e=>Ee(e.target.value),margin:"normal",required:!0})]}),(0,M.jsxs)(W.A,{children:[(0,M.jsx)(v.A,{onClick:()=>_e(!1),children:"Cancel"}),(0,M.jsxs)(v.A,{onClick:async()=>{try{let e="",i={};switch(Be){case"suspend":e="/admin/users/".concat(De._id,"/status"),i={status:"suspended",reason:Re};break;case"ban":e="/admin/users/".concat(De._id,"/status"),i={status:"banned",reason:Re};break;case"activate":e="/admin/users/".concat(De._id,"/status"),i={status:"active",reason:Re};break;case"verify":e="/admin/users/".concat(De._id,"/verify"),i={documentType:"national_id",approved:!0,notes:Re}}(await J.A.put(e,i)).data.success&&(Te({open:!0,message:"User ".concat(Be," successful"),severity:"success"}),Fe())}catch(Ae){var e,i;console.error("Error executing user action:",Ae),Te({open:!0,message:(null===(e=Ae.response)||void 0===e||null===(i=e.data)||void 0===i?void 0:i.message)||"Failed to ".concat(Be," user"),severity:"error"})}finally{_e(!1),Ee("")}},color:"primary",variant:"contained",disabled:!Re.trim(),children:["Confirm ",Be]})]})]}),(0,M.jsx)(_.A,{open:Ne.open,autoHideDuration:6e3,onClose:()=>Te((0,r.A)((0,r.A)({},Ne),{},{open:!1})),children:(0,M.jsx)(t.A,{onClose:()=>Te((0,r.A)((0,r.A)({},Ne),{},{open:!1})),severity:Ne.severity,sx:{width:"100%"},children:Ne.message})})]})}}}]);
//# sourceMappingURL=220.74b7f83a.chunk.js.map