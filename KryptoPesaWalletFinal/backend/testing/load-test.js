/**
 * Comprehensive Load Testing Suite for KryptoPesa API
 * Tests system performance under 10,000+ concurrent users
 */

import http from 'k6/http';
import ws from 'k6/ws';
import { check, group, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { randomString, randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Custom metrics
const errorRate = new Rate('error_rate');
const responseTime = new Trend('response_time');
const throughput = new Counter('throughput');
const wsConnections = new Counter('websocket_connections');

// Test configuration
export const options = {
  scenarios: {
    // Ramp-up test: Gradually increase load
    ramp_up: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 100 },   // Ramp up to 100 users
        { duration: '5m', target: 500 },   // Ramp up to 500 users
        { duration: '10m', target: 1000 }, // Ramp up to 1000 users
        { duration: '15m', target: 2000 }, // Ramp up to 2000 users
        { duration: '20m', target: 5000 }, // Ramp up to 5000 users
        { duration: '30m', target: 10000 }, // Peak load: 10,000 users
        { duration: '10m', target: 10000 }, // Sustain peak load
        { duration: '5m', target: 0 },     // Ramp down
      ],
    },
    
    // Spike test: Sudden load increase
    spike_test: {
      executor: 'ramping-vus',
      startTime: '60m',
      stages: [
        { duration: '1m', target: 1000 },  // Normal load
        { duration: '30s', target: 15000 }, // Sudden spike
        { duration: '2m', target: 15000 },  // Sustain spike
        { duration: '1m', target: 1000 },   // Return to normal
      ],
    },
    
    // Stress test: Beyond normal capacity
    stress_test: {
      executor: 'ramping-vus',
      startTime: '70m',
      stages: [
        { duration: '5m', target: 5000 },
        { duration: '10m', target: 15000 },
        { duration: '10m', target: 20000 },
        { duration: '5m', target: 0 },
      ],
    },
    
    // WebSocket test: Real-time connections
    websocket_test: {
      executor: 'constant-vus',
      vus: 1000,
      duration: '30m',
      exec: 'websocketTest',
    },
  },
  
  thresholds: {
    // Performance requirements
    http_req_duration: ['p(95)<1000'], // 95% of requests under 1s
    http_req_failed: ['rate<0.01'],    // Error rate under 1%
    error_rate: ['rate<0.01'],
    response_time: ['p(99)<2000'],     // 99% under 2s
    
    // Specific endpoint thresholds
    'http_req_duration{endpoint:auth}': ['p(95)<500'],
    'http_req_duration{endpoint:trading}': ['p(95)<800'],
    'http_req_duration{endpoint:wallet}': ['p(95)<600'],
  },
};

// Test data
const BASE_URL = __ENV.BASE_URL || 'https://api.kryptopesa.com';
const WS_URL = __ENV.WS_URL || 'wss://api.kryptopesa.com/ws';

// User credentials for testing
const testUsers = [];
for (let i = 0; i < 1000; i++) {
  testUsers.push({
    email: `testuser${i}@example.com`,
    password: 'TestPassword123!',
    phone: `+254700${String(i).padStart(6, '0')}`,
  });
}

// Cryptocurrencies for testing
const cryptocurrencies = ['BTC', 'ETH', 'USDT', 'USDC', 'MATIC'];
const offerTypes = ['buy', 'sell'];
const paymentMethods = ['M-Pesa', 'Airtel Money', 'Bank Transfer'];

export function setup() {
  console.log('Setting up load test environment...');
  
  // Health check
  const healthResponse = http.get(`${BASE_URL}/health`);
  check(healthResponse, {
    'Health check passed': (r) => r.status === 200,
  });
  
  return { baseUrl: BASE_URL, wsUrl: WS_URL };
}

export default function (data) {
  const userIndex = __VU % testUsers.length;
  const user = testUsers[userIndex];
  
  group('Authentication Flow', () => {
    authenticateUser(user);
  });
  
  group('Trading Operations', () => {
    tradingOperations();
  });
  
  group('Wallet Operations', () => {
    walletOperations();
  });
  
  group('Chat Operations', () => {
    chatOperations();
  });
  
  sleep(randomIntBetween(1, 3));
}

function authenticateUser(user) {
  const loginPayload = {
    email: user.email,
    password: user.password,
  };
  
  const loginResponse = http.post(
    `${BASE_URL}/api/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: { 'Content-Type': 'application/json' },
      tags: { endpoint: 'auth' },
    }
  );
  
  const loginSuccess = check(loginResponse, {
    'Login successful': (r) => r.status === 200,
    'Login response time OK': (r) => r.timings.duration < 1000,
    'Has auth token': (r) => r.json('token') !== undefined,
  });
  
  errorRate.add(!loginSuccess);
  responseTime.add(loginResponse.timings.duration);
  throughput.add(1);
  
  if (loginSuccess) {
    const token = loginResponse.json('token');
    
    // Store token for subsequent requests
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }
  
  return null;
}

function tradingOperations() {
  const headers = authenticateUser(testUsers[__VU % testUsers.length]);
  if (!headers) return;
  
  // Get trading offers
  const offersResponse = http.get(
    `${BASE_URL}/api/trading/offers?cryptocurrency=${cryptocurrencies[__VU % cryptocurrencies.length]}&limit=20`,
    { headers, tags: { endpoint: 'trading' } }
  );
  
  check(offersResponse, {
    'Offers loaded': (r) => r.status === 200,
    'Offers response time OK': (r) => r.timings.duration < 1000,
    'Has offers data': (r) => Array.isArray(r.json('data')),
  });
  
  // Create a new offer (10% of users)
  if (Math.random() < 0.1) {
    const offerPayload = {
      offerType: offerTypes[Math.floor(Math.random() * offerTypes.length)],
      cryptocurrency: cryptocurrencies[Math.floor(Math.random() * cryptocurrencies.length)],
      amount: randomIntBetween(100, 10000),
      pricePerUnit: randomIntBetween(30000, 50000),
      paymentMethods: [paymentMethods[Math.floor(Math.random() * paymentMethods.length)]],
      description: `Test offer ${randomString(10)}`,
      minAmount: randomIntBetween(50, 500),
      maxAmount: randomIntBetween(1000, 5000),
    };
    
    const createOfferResponse = http.post(
      `${BASE_URL}/api/trading/offers`,
      JSON.stringify(offerPayload),
      { headers, tags: { endpoint: 'trading' } }
    );
    
    check(createOfferResponse, {
      'Offer created': (r) => r.status === 201,
      'Create offer response time OK': (r) => r.timings.duration < 2000,
    });
  }
  
  // Get user's active trades
  const tradesResponse = http.get(
    `${BASE_URL}/api/trading/trades?status=active`,
    { headers, tags: { endpoint: 'trading' } }
  );
  
  check(tradesResponse, {
    'Trades loaded': (r) => r.status === 200,
    'Trades response time OK': (r) => r.timings.duration < 800,
  });
  
  responseTime.add(offersResponse.timings.duration);
  responseTime.add(tradesResponse.timings.duration);
  throughput.add(2);
}

function walletOperations() {
  const headers = authenticateUser(testUsers[__VU % testUsers.length]);
  if (!headers) return;
  
  // Get wallet balances
  const balancesResponse = http.get(
    `${BASE_URL}/api/wallet/balances`,
    { headers, tags: { endpoint: 'wallet' } }
  );
  
  check(balancesResponse, {
    'Balances loaded': (r) => r.status === 200,
    'Balances response time OK': (r) => r.timings.duration < 600,
    'Has balance data': (r) => r.json('data') !== undefined,
  });
  
  // Get transaction history
  const historyResponse = http.get(
    `${BASE_URL}/api/wallet/transactions?limit=20&offset=0`,
    { headers, tags: { endpoint: 'wallet' } }
  );
  
  check(historyResponse, {
    'Transaction history loaded': (r) => r.status === 200,
    'History response time OK': (r) => r.timings.duration < 800,
  });
  
  // Generate wallet address (5% of users)
  if (Math.random() < 0.05) {
    const generateAddressResponse = http.post(
      `${BASE_URL}/api/wallet/addresses`,
      JSON.stringify({
        cryptocurrency: cryptocurrencies[Math.floor(Math.random() * cryptocurrencies.length)]
      }),
      { headers, tags: { endpoint: 'wallet' } }
    );
    
    check(generateAddressResponse, {
      'Address generated': (r) => r.status === 201,
      'Generate address response time OK': (r) => r.timings.duration < 1000,
    });
  }
  
  responseTime.add(balancesResponse.timings.duration);
  responseTime.add(historyResponse.timings.duration);
  throughput.add(2);
}

function chatOperations() {
  const headers = authenticateUser(testUsers[__VU % testUsers.length]);
  if (!headers) return;
  
  // Get chat conversations
  const conversationsResponse = http.get(
    `${BASE_URL}/api/chat/conversations`,
    { headers, tags: { endpoint: 'chat' } }
  );
  
  check(conversationsResponse, {
    'Conversations loaded': (r) => r.status === 200,
    'Conversations response time OK': (r) => r.timings.duration < 500,
  });
  
  const conversations = conversationsResponse.json('data') || [];
  
  // Send message to random conversation (20% of users)
  if (conversations.length > 0 && Math.random() < 0.2) {
    const conversation = conversations[Math.floor(Math.random() * conversations.length)];
    
    const messagePayload = {
      content: `Test message ${randomString(20)}`,
      type: 'text',
    };
    
    const sendMessageResponse = http.post(
      `${BASE_URL}/api/chat/conversations/${conversation.id}/messages`,
      JSON.stringify(messagePayload),
      { headers, tags: { endpoint: 'chat' } }
    );
    
    check(sendMessageResponse, {
      'Message sent': (r) => r.status === 201,
      'Send message response time OK': (r) => r.timings.duration < 800,
    });
  }
  
  responseTime.add(conversationsResponse.timings.duration);
  throughput.add(1);
}

// WebSocket test function
export function websocketTest() {
  const url = `${WS_URL}?token=test_token_${__VU}`;
  
  const response = ws.connect(url, {}, function (socket) {
    wsConnections.add(1);
    
    socket.on('open', () => {
      console.log(`WebSocket connection opened for VU ${__VU}`);
      
      // Send periodic messages
      socket.setInterval(() => {
        socket.send(JSON.stringify({
          type: 'ping',
          timestamp: Date.now(),
        }));
      }, 30000); // Every 30 seconds
    });
    
    socket.on('message', (data) => {
      const message = JSON.parse(data);
      check(message, {
        'WebSocket message received': (msg) => msg !== null,
        'Message has type': (msg) => msg.type !== undefined,
      });
    });
    
    socket.on('close', () => {
      console.log(`WebSocket connection closed for VU ${__VU}`);
    });
    
    socket.on('error', (e) => {
      console.error(`WebSocket error for VU ${__VU}:`, e);
      errorRate.add(1);
    });
    
    // Keep connection alive for test duration
    sleep(1800); // 30 minutes
  });
  
  check(response, {
    'WebSocket connection established': (r) => r && r.status === 101,
  });
}

export function teardown(data) {
  console.log('Load test completed');
  
  // Generate summary report
  const summary = {
    timestamp: new Date().toISOString(),
    baseUrl: data.baseUrl,
    testDuration: '90m',
    maxConcurrentUsers: 20000,
    scenarios: ['ramp_up', 'spike_test', 'stress_test', 'websocket_test'],
  };
  
  console.log('Test Summary:', JSON.stringify(summary, null, 2));
}
