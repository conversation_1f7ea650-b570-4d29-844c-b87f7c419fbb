const logger = require('./utils/logger');

/**
 * PWA Service for Admin Dashboard
 * Manages service worker, push notifications, and app installation
 */

class PWAService {
  constructor() {
    this.registration = null;
    this.isSupported = 'serviceWorker' in navigator;
    this.isInstallable = false;
    this.installPrompt = null;
    this.pushSubscription = null;
    this.notificationPermission = 'default';
    
    this.listeners = {
      install: [],
      update: [],
      offline: [],
      online: []
    };
  }

  // Initialize PWA service
  async initialize() {
    if (!this.isSupported) {
      // logger.warn('Service Workers not supported');
      return false;
    }

    try {
      // Register service worker
      await this.registerServiceWorker();
      
      // Setup install prompt handling
      this.setupInstallPrompt();
      
      // Setup network status monitoring
      this.setupNetworkMonitoring();
      
      // Setup push notifications
      await this.setupPushNotifications();
      
      // logger.info('PWA Service initialized successfully');
      return true;
    } catch (error) {
      // logger.error('Failed to initialize PWA Service:', error);
      return false;
    }
  }

  // Register service worker
  async registerServiceWorker() {
    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/admin/'
      });

      // logger.info('Service Worker registered:', this.registration);

      // Handle service worker updates
      this.registration.addEventListener('updatefound', () => {
        const newWorker = this.registration.installing;
        
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed') {
            if (navigator.serviceWorker.controller) {
              // New content is available
              this.notifyListeners('update', {
                registration: this.registration,
                newWorker
              });
            } else {
              // Content is cached for first time
              // logger.info('Content is cached for offline use');
            }
          }
        });
      });

      // Handle service worker messages
      navigator.serviceWorker.addEventListener('message', (event) => {
        this.handleServiceWorkerMessage(event);
      });

      return this.registration;
    } catch (error) {
      // logger.error('Service Worker registration failed:', error);
      throw error;
    }
  }

  // Setup app installation prompt
  setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (event) => {
      // Prevent the mini-infobar from appearing
      event.preventDefault();
      
      // Store the event for later use
      this.installPrompt = event;
      this.isInstallable = true;
      
      // logger.info('App is installable');
      
      // Notify listeners
      this.notifyListeners('install', {
        canInstall: true,
        prompt: event
      });
    });

    // Handle app installation
    window.addEventListener('appinstalled', () => {
      // logger.info('App was installed');
      this.installPrompt = null;
      this.isInstallable = false;
      
      // Track installation
      this.trackEvent('app_installed');
    });
  }

  // Setup network monitoring
  setupNetworkMonitoring() {
    const updateOnlineStatus = () => {
      if (navigator.onLine) {
        this.notifyListeners('online');
        // logger.info('App is online');
      } else {
        this.notifyListeners('offline');
        // logger.info('App is offline');
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    
    // Initial status
    updateOnlineStatus();
  }

  // Setup push notifications
  async setupPushNotifications() {
    if (!('Notification' in window) || !('PushManager' in window)) {
      // logger.warn('Push notifications not supported');
      return false;
    }

    this.notificationPermission = Notification.permission;
    
    if (this.notificationPermission === 'granted') {
      await this.subscribeToPush();
    }

    return true;
  }

  // Request notification permission
  async requestNotificationPermission() {
    if (!('Notification' in window)) {
      throw new Error('Notifications not supported');
    }

    const permission = await Notification.requestPermission();
    this.notificationPermission = permission;

    if (permission === 'granted') {
      await this.subscribeToPush();
      return true;
    }

    return false;
  }

  // Subscribe to push notifications
  async subscribeToPush() {
    if (!this.registration) {
      throw new Error('Service Worker not registered');
    }

    try {
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(
          process.env.REACT_APP_VAPID_PUBLIC_KEY || ''
        )
      });

      this.pushSubscription = subscription;
      
      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);
      
      // logger.info('Push subscription successful');
      return subscription;
    } catch (error) {
      // logger.error('Push subscription failed:', error);
      throw error;
    }
  }

  // Send subscription to server
  async sendSubscriptionToServer(subscription) {
    try {
      const response = await fetch('/api/admin/push-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify({
          subscription,
          userId: localStorage.getItem('admin_user_id')
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to save subscription: ${response.status}`);
      }

      // logger.info('Push subscription saved to server');
    } catch (error) {
      // logger.error('Failed to save push subscription:', error);
    }
  }

  // Show app install prompt
  async showInstallPrompt() {
    if (!this.installPrompt) {
      throw new Error('Install prompt not available');
    }

    try {
      // Show the install prompt
      this.installPrompt.prompt();
      
      // Wait for user response
      const { outcome } = await this.installPrompt.userChoice;
      
      // logger.info(`Install prompt outcome: ${outcome}`);
      
      // Track the outcome
      this.trackEvent('install_prompt_result', { outcome });
      
      // Clear the prompt
      this.installPrompt = null;
      this.isInstallable = false;
      
      return outcome === 'accepted';
    } catch (error) {
      // logger.error('Install prompt failed:', error);
      throw error;
    }
  }

  // Update service worker
  async updateServiceWorker() {
    if (!this.registration) {
      throw new Error('Service Worker not registered');
    }

    try {
      await this.registration.update();
      
      // Skip waiting for new service worker
      if (this.registration.waiting) {
        this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      }
      
      // logger.info('Service Worker updated');
    } catch (error) {
      // logger.error('Service Worker update failed:', error);
      throw error;
    }
  }

  // Cache specific URLs
  async cacheUrls(urls) {
    if (!this.registration || !this.registration.active) {
      throw new Error('Service Worker not active');
    }

    this.registration.active.postMessage({
      type: 'CACHE_URLS',
      payload: { urls }
    });
  }

  // Clear cache
  async clearCache(cacheName = null) {
    if (!this.registration || !this.registration.active) {
      throw new Error('Service Worker not active');
    }

    this.registration.active.postMessage({
      type: 'CLEAR_CACHE',
      payload: { cacheName }
    });
  }

  // Handle service worker messages
  handleServiceWorkerMessage(event) {
    const { type, payload } = event.data;
    
    switch (type) {
      case 'CACHE_UPDATED':
        // logger.info('Cache updated:', payload);
        break;
        
      case 'OFFLINE_FALLBACK':
        // logger.info('Offline fallback triggered:', payload);
        break;
        
      default:
        // logger.info('Unknown service worker message:', type, payload);
    }
  }

  // Add event listener
  addEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    }
  }

  // Remove event listener
  removeEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }

  // Notify listeners
  notifyListeners(event, data = null) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          // logger.error('Listener error:', error);
        }
      });
    }
  }

  // Utility: Convert VAPID key
  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Track events (analytics)
  trackEvent(event, data = {}) {
    // Send to analytics service
    // logger.info('PWA Event:', event, data);
    
    // Could integrate with Google Analytics, etc.
    if (window.gtag) {
      window.gtag('event', event, data);
    }
  }

  // Get PWA status
  getStatus() {
    return {
      isSupported: this.isSupported,
      isRegistered: !!this.registration,
      isInstallable: this.isInstallable,
      notificationPermission: this.notificationPermission,
      hasSubscription: !!this.pushSubscription,
      isOnline: navigator.onLine
    };
  }

  // Check if app is running as PWA
  isRunningAsPWA() {
    return window.matchMedia('(display-mode: standalone)').matches ||
           window.navigator.standalone === true;
  }
}

// Create singleton instance
const pwaService = new PWAService();

// React hook for PWA functionality
export const usePWA = () => {
  const [status, setStatus] = React.useState(pwaService.getStatus());
  
  React.useEffect(() => {
    const updateStatus = () => setStatus(pwaService.getStatus());
    
    pwaService.addEventListener('install', updateStatus);
    pwaService.addEventListener('update', updateStatus);
    pwaService.addEventListener('online', updateStatus);
    pwaService.addEventListener('offline', updateStatus);
    
    return () => {
      pwaService.removeEventListener('install', updateStatus);
      pwaService.removeEventListener('update', updateStatus);
      pwaService.removeEventListener('online', updateStatus);
      pwaService.removeEventListener('offline', updateStatus);
    };
  }, []);

  return {
    ...status,
    install: () => pwaService.showInstallPrompt(),
    requestNotifications: () => pwaService.requestNotificationPermission(),
    update: () => pwaService.updateServiceWorker(),
    isRunningAsPWA: pwaService.isRunningAsPWA()
  };
};

export default pwaService;
