import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
const logger = require('./utils/logger');

  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
} from '@mui/material';
import {
  People,
  SwapHoriz,
  Gavel,
  TrendingUp,
  TrendingDown,
  LocalOffer,
} from '@mui/icons-material';
import { adminAPI, adminWebSocket } from '../services/api';

const StatCard = React.memo(({ title, value, icon, color = 'primary', growth }) => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="div">
            {value}
          </Typography>
          {growth !== undefined && (
            <Box display="flex" alignItems="center" mt={1}>
              <Box display="flex" alignItems="center">
                {growth > 0 && <TrendingUp color="success" fontSize="small" />}
                {growth < 0 && <TrendingDown color="error" fontSize="small" />}
                <Typography
                  variant="body2"
                  sx={{
                    ml: 0.5,
                    color: growth > 0 ? 'success.main' : growth < 0 ? 'error.main' : 'text.secondary'
                  }}
                >
                  {growth > 0 ? '+' : ''}{growth}%
                </Typography>
              </Box>
              <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                vs last week
              </Typography>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            backgroundColor: `${color}.light`,
            borderRadius: '50%',
            p: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
));

const DashboardPage = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch dashboard statistics with useCallback to prevent unnecessary re-renders
  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      const response = await adminAPI.dashboard.getStats();

      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // logger.error('Error fetching dashboard stats:', error);
      }
      setError('Failed to fetch dashboard statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();

    // Set up WebSocket for real-time updates
    const handleStatsUpdate = (data) => {
      setStats(prevStats => ({
        ...prevStats,
        ...data
      }));
    };

    adminWebSocket.on('dashboard_stats_update', handleStatsUpdate);
    adminWebSocket.on('trade_update', fetchStats);
    adminWebSocket.on('user_update', fetchStats);

    // Refresh stats every 30 seconds as fallback
    const interval = setInterval(fetchStats, 30000);

    return () => {
      clearInterval(interval);
      adminWebSocket.off('dashboard_stats_update', handleStatsUpdate);
      adminWebSocket.off('trade_update', fetchStats);
      adminWebSocket.off('user_update', fetchStats);
    };
  }, [fetchStats]);

  // Memoized formatting functions to prevent recalculation on every render
  const formatNumber = useCallback((num) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num?.toLocaleString() || '0';
  }, []);

  const formatCurrency = useCallback((amount) => {
    return `$${formatNumber(amount)}`;
  }, [formatNumber]);

  // Memoized stats calculations to prevent unnecessary recalculations
  const formattedStats = useMemo(() => {
    if (!stats) return null;

    return {
      totalUsers: formatNumber(stats?.statistics?.users?.total),
      activeTrades: formatNumber(stats?.statistics?.trades?.active),
      pendingDisputes: formatNumber(stats?.statistics?.disputes?.pending),
      totalVolume: formatCurrency(stats?.statistics?.trades?.volume),
      totalOffers: formatNumber(stats?.statistics?.offers?.total),
      completedTrades: formatNumber(stats?.statistics?.trades?.completed),
      weeklyNewUsers: formatNumber(stats?.statistics?.users?.weeklyNew),
      activeOffers: formatNumber(stats?.statistics?.offers?.active),
    };
  }, [stats, formatNumber, formatCurrency]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Dashboard Overview
        </Typography>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard Overview
      </Typography>
      
      <Grid container spacing={3}>
        {/* Stats Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Users"
            value={formattedStats?.totalUsers}
            growth={stats?.statistics?.users?.growth}
            icon={<People />}
            color="primary"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Trades"
            value={formattedStats?.activeTrades}
            growth={stats?.statistics?.trades?.growth}
            icon={<SwapHoriz />}
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending Disputes"
            value={formattedStats?.pendingDisputes}
            icon={<Gavel />}
            color="warning"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Volume"
            value={formattedStats?.totalVolume}
            growth={stats?.statistics?.trades?.volumeGrowth}
            icon={<TrendingUp />}
            color="info"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Offers"
            value={formatNumber(stats?.statistics?.offers?.total)}
            icon={<LocalOffer />}
            color="secondary"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Completed Trades"
            value={formatNumber(stats?.statistics?.trades?.completed)}
            icon={<SwapHoriz />}
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="New Users (Week)"
            value={formatNumber(stats?.statistics?.users?.weeklyNew)}
            icon={<People />}
            color="primary"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Offers"
            value={formatNumber(stats?.statistics?.offers?.active)}
            icon={<LocalOffer />}
            color="secondary"
          />
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activity
            </Typography>
            <Typography color="textSecondary">
              Recent activity data will be displayed here...
            </Typography>
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Typography color="textSecondary">
              Quick action buttons will be displayed here...
            </Typography>
          </Paper>
        </Grid>

        {/* Charts */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Trading Volume Chart
            </Typography>
            <Typography color="textSecondary">
              Trading volume chart will be displayed here...
            </Typography>
          </Paper>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Trades
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Trade ID</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Time</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {stats?.recentActivity?.trades?.slice(0, 5).map((trade) => (
                    <TableRow key={trade._id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {trade.tradeId?.substring(0, 8)}...
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {trade.cryptocurrency?.amount} {trade.cryptocurrency?.symbol}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={trade.status}
                          size="small"
                          color={
                            trade.status === 'completed' ? 'success' :
                            trade.status === 'disputed' ? 'warning' :
                            trade.status === 'cancelled' ? 'error' : 'default'
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(trade.createdAt).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Disputes
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Dispute ID</TableCell>
                    <TableCell>Trade</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Time</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {stats?.recentActivity?.disputes?.slice(0, 5).map((dispute) => (
                    <TableRow key={dispute._id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {dispute.disputeId?.substring(0, 8)}...
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {dispute.trade?.tradeId?.substring(0, 8)}...
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={dispute.status}
                          size="small"
                          color={
                            dispute.status === 'resolved' ? 'success' :
                            dispute.status === 'pending' ? 'warning' : 'default'
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(dispute.createdAt).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
